<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<parent>
		<groupId>com.burgeon.r3</groupId>
		<artifactId>r3-st</artifactId>
		<version>3.0.0-SNAPSHOT</version>
		<relativePath>../../../pom.xml</relativePath>
	</parent>

	<modelVersion>4.0.0</modelVersion>
	<artifactId>r3-st-bll</artifactId>
	<version>3.0.0-SNAPSHOT</version>
	<packaging>jar</packaging>

	<dependencies>
		<!-- raincloud -->
		<dependency>
			<groupId>org.syman</groupId>
			<artifactId>raincloud-es</artifactId>
		</dependency>

		<!-- r3-project -->
		<dependency>
			<groupId>com.burgeon.r3</groupId>
			<artifactId>r3-st-api</artifactId>
			<scope>compile</scope>
		</dependency>
		<dependency>
			<groupId>com.burgeon.r3</groupId>
			<artifactId>r3-oc-basic-bll</artifactId>
		</dependency>
		<dependency>
			<groupId>com.burgeon.r3</groupId>
			<artifactId>r3-oc-basic-srv</artifactId>
		</dependency>
		<dependency>
			<groupId>com.burgeon.r3</groupId>
			<artifactId>r3-ad-util</artifactId>
		</dependency>
		<dependency>
			<groupId>com.burgeon.r3</groupId>
			<artifactId>r3-ps-ext-api</artifactId>
		</dependency>
		<dependency>
			<groupId>com.burgeon.r3</groupId>
			<artifactId>r3-ip-api</artifactId>
		</dependency>
		<dependency>
			<groupId>com.burgeon.r3</groupId>
			<artifactId>r3-sg-interface-api</artifactId>
		</dependency>
		<dependency>
			<groupId>com.burgeon.r3</groupId>
			<artifactId>r3-sg-stocksync-api</artifactId>
		</dependency>
		<dependency>
			<groupId>com.burgeon.r3</groupId>
			<artifactId>r3-oc-oms-model</artifactId>
		</dependency>
		<dependency>
			<groupId>com.burgeon.r3</groupId>
			<artifactId>oms-vip-frontinterface-api</artifactId>
		</dependency>
		<dependency>
			<groupId>com.burgeon.r3</groupId>
			<artifactId>r3-sg-basic-api</artifactId>
		</dependency>

		<!-- r3-service -->
		<dependency>
			<groupId>com.burgeon.r3</groupId>
			<artifactId>r3-storage-file</artifactId>
		</dependency>
		<dependency>
			<groupId>com.burgeon.r3</groupId>
			<artifactId>r3-common-util</artifactId>
		</dependency>
		<dependency>
			<groupId>com.burgeon.r3</groupId>
			<artifactId>R3-Starter-Dubbo</artifactId>
		</dependency>

		<!-- utils -->
		<dependency>
			<groupId>commons-lang</groupId>
			<artifactId>commons-lang</artifactId>
		</dependency>
	</dependencies>

</project>