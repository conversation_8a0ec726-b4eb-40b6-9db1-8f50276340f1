package com.jackrain.nea.st.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.burgeon.r3.sg.channel.model.request.product.SgChannelProductQueryForSTRequest;
import com.burgeon.r3.sg.channel.model.result.product.SgChannelProductQueryForSTResult;
import com.burgeon.r3.sg.inf.api.oms.product.SgChannelProductQueryCmd;

import com.burgeon.r3.sg.stocksync.api.SgBSyncChannelStockCmd;
import com.burgeon.r3.sg.stocksync.model.table.SgBSyncChannelStock;
import com.google.common.collect.Lists;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.st.common.StConstant;
import com.jackrain.nea.st.mapper.StCLockStockStrategyItemMapper;
import com.jackrain.nea.st.model.table.StCLockStockStrategyItemDO;
import com.jackrain.nea.st.utils.ValueHolderUtils;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.List;

/**
 * <AUTHOR> ShiLong
 * @Date: 2020/7/8 9:22 下午
 * @Desc: 店铺锁库同步平台库存
 */
@Component
@Slf4j
public class ShopLockStockSyncStockService extends CommandAdapter {

    @Autowired
    private StCLockStockStrategyItemMapper lockStockItemMapper;
    @Reference(group = "sg", version = "1.0")
    private SgBSyncChannelStockCmd sgBSyncChannelStockCmd;
    @Reference(group = "sg", version = "1.0")
    private SgChannelProductQueryCmd sgChannelProductQueryCmd;

    /**
     * 用户点击审核，插入中间表，锁库结束时间过后才可以同步
     */
    @Override
    public ValueHolder execute(QuerySession session) {
        DefaultWebEvent event = session.getEvent();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(
                        event.getParameterValue("param"), "yyyy-MM-dd HH:mm:ss", SerializerFeature.WriteMapNullValue),
                Feature.OrderedField);
        log.info(LogUtil.format("店铺锁库同步平台库存入参:{}"), param);
        Long id = param.getLong("objid");
        if (id == null || id < 0) {
            throw new NDSException("请选择需要同步的库存策略");
        }
        this.syncPlatformStock(id, session.getUser());
        return ValueHolderUtils.getFailValueHolder("暂无需要同步的库存策略");
    }

    /**
     * 用户点击审核，插入中间表，锁库结束时间过后才可以同步
     */
    public ValueHolder syncPlatformStock(Long objid, User user) {
        //查询明细表信息（需要修改前比例是否为Null，以及不为null的情况）
        List<StCLockStockStrategyItemDO> itemList = lockStockItemMapper.selectList(new QueryWrapper<StCLockStockStrategyItemDO>()
                .lambda()
                .eq(StCLockStockStrategyItemDO::getStCLockStockStrategyId, objid)
                .eq(StCLockStockStrategyItemDO::getIsactive, StConstant.ISACTIVE_Y));
        if (!CollectionUtils.isEmpty(itemList)) {
            //调用同步接口,调用成功后需更新修改前比例
            try {
                //插入中间表,需先查询sku相关信息
                List<SgBSyncChannelStock> channelStockList = this.assembleParam(itemList);
                if (!CollectionUtils.isEmpty(channelStockList)) {
                    ValueHolderV14<List<SgBSyncChannelStock>> valueHolderV14 = sgBSyncChannelStockCmd.insert(channelStockList, user);
                    if (valueHolderV14.isOK()) {
                        return ValueHolderUtils.getSuccessValueHolder("同步库存成功");
                    }
                }
                log.debug(LogUtil.format("【店铺锁库同步平台库存失败】data:{}"), JSONObject.toJSON(channelStockList));
                return ValueHolderUtils.getFailValueHolder("同步库存失败");
            } catch (Exception e) {
                e.printStackTrace();
                log.error(LogUtil.format("【店铺锁库同步平台库存失败】data:{}"), JSONObject.toJSON(itemList));
            }
        }
        return ValueHolderUtils.getFailValueHolder("暂无需要同步的锁库库存策略");
    }


    private List<SgBSyncChannelStock> assembleParam(List<StCLockStockStrategyItemDO> itemList) {
        List<SgBSyncChannelStock> channelStockList = Lists.newArrayList();
        for (StCLockStockStrategyItemDO strategyItemDO : itemList) {
            SgBSyncChannelStock sgBSyncChannelStock = new SgBSyncChannelStock();
            sgBSyncChannelStock.setCpCShopId(strategyItemDO.getCpCShopId());
            sgBSyncChannelStock.setCpCShopTitle(strategyItemDO.getCpCShopTitle());//设置店铺名称
            sgBSyncChannelStock.setBeginTime(strategyItemDO.getLockBtime());
            sgBSyncChannelStock.setEndTime(strategyItemDO.getLockEtime());
            sgBSyncChannelStock.setStrategyId(strategyItemDO.getStCLockStockStrategyId());
            sgBSyncChannelStock.setStrategyItemId(strategyItemDO.getId());
            sgBSyncChannelStock.setType(StConstant.LOCK_STOCK_TYPE);
            sgBSyncChannelStock.setStatus(StConstant.NO_SYNC_STATUS);
            channelStockList.add(sgBSyncChannelStock);
        }
        return channelStockList;
    }

    private List<SgBSyncChannelStock> assembleParam(Long objid, List<StCLockStockStrategyItemDO> itemList) {
        List<SgBSyncChannelStock> channelStockList = Lists.newArrayList();
        for (StCLockStockStrategyItemDO item : itemList) {
            SgChannelProductQueryForSTRequest queryForSTResult = new SgChannelProductQueryForSTRequest();
            List<Long> shopIds = Lists.newArrayList();
            shopIds.add(item.getCpCShopId());
            queryForSTResult.setCpCShopIdList(shopIds);
            if (item.getStCLockStockStrategyId().equals(objid)) {
                //根据明细表店铺id组合查询sku信息
                List<SgChannelProductQueryForSTResult> queryResult = sgChannelProductQueryCmd.queryChannelProductForST(queryForSTResult);
                for (SgChannelProductQueryForSTResult result : queryResult) {
                    SgBSyncChannelStock sgBSyncChannelStock = new SgBSyncChannelStock();
                    sgBSyncChannelStock.setCpCShopId(item.getCpCShopId());
                    sgBSyncChannelStock.setCpCShopTitle(item.getCpCShopTitle());//设置店铺名称
                    sgBSyncChannelStock.setBeginTime(item.getLockBtime());
                    sgBSyncChannelStock.setEndTime(item.getLockEtime());
                    if (!StringUtils.isEmpty(result.getNumiid())) {
                        sgBSyncChannelStock.setNumberId(result.getNumiid());
                    }
                    if (result.getPsCProId() != null) {
                        sgBSyncChannelStock.setPsCProId(result.getPsCProId());
                    }
                    if (result.getPsCSkuId() != null) {
                        sgBSyncChannelStock.setPsCSkuId(result.getPsCSkuId());
                        sgBSyncChannelStock.setPsCSkuEcode(result.getPsCSkuEcode());//设置条码code
                    }
                    if (!StringUtils.isEmpty(result.getSkuId())) {
                        sgBSyncChannelStock.setSkuId(result.getSkuId());
                    }
                    sgBSyncChannelStock.setStrategyId(objid);
                    sgBSyncChannelStock.setType(StConstant.LOCK_STOCK_TYPE);
                    sgBSyncChannelStock.setStatus(StConstant.NO_SYNC_STATUS);
                    channelStockList.add(sgBSyncChannelStock);
                }
            }
        }
        return channelStockList;
    }
}
