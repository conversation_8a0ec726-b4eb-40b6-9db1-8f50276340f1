package com.jackrain.nea.st.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.st.common.StConstant;
import com.jackrain.nea.st.mapper.StCVipcomProjectMapper;
import com.jackrain.nea.st.model.table.StCVipcomProjectDO;
import com.jackrain.nea.st.utils.StBeanUtils;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.HashMap;

/**
 * <AUTHOR>
 * @Date 2019/3/8 10:02
 */
@Component
@Slf4j
public class VipcomProjectVoidService extends CommandAdapter {
    @Autowired
    private StCVipcomProjectMapper stCVipcomProjectMapper;

    @Override
    public ValueHolder execute(QuerySession session) throws NDSException {
        DefaultWebEvent event = session.getEvent();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(
                event.getParameterValue("param"), "yyyy-MM-dd HH:mm:ss", SerializerFeature.WriteMapNullValue),
                Feature.OrderedField);
        int errRecord = 0;
        HashMap<Long, Object> errMap = new HashMap<>();
        //生成数组
        JSONArray voidArray = StBeanUtils.makeVoidJsonArray(param);
        //列表批量
        if (!CollectionUtils.isEmpty(voidArray)) {
            for (int i = 0; i < voidArray.size(); i++) {
                Long id = Long.valueOf(voidArray.get(i).toString());   // 遍历 jsonarray 数组，把每一个对象转成 json 对象
                try {
                    voidVipcomProject(id, session);
                } catch (Exception ex) {
                    errRecord++;
                    errMap.put(id, ex.getMessage());
                }
            }
        }
        //返回日志
        return StBeanUtils.getExcuteValueHolder(voidArray.size(), errMap);
    }

    /**
     * 档期日程计划 批量作废
     *
     * @param id
     * @param session
     * @return com.jackrain.nea.util.ValueHolder
     * @Author: 陈秀楼
     * @Date 2019/3/8
     */
    private void voidVipcomProject(long id, QuerySession session) {
        //验证
        checkVipcomProject(id);
        //作废
        StCVipcomProjectDO project = new StCVipcomProjectDO();
        project.setId(id);
        project.setIsactive(StConstant.ISACTIVE_N);//作废状态
        project.setDelname(session.getUser().getName());//作废人用户名
        project.setDelid(Long.valueOf(session.getUser().getId()));//作废人
        project.setDelename(session.getUser().getName());//作废人姓名
        project.setDelTime(new Date());//作废时间
        StBeanUtils.makeModifierField(project, session.getUser());
        int update = stCVipcomProjectMapper.updateById(project);
        if (update < 0) {
            throw new NDSException("作废失败！");
        }
    }

    /**
     * 检查日程计划
     *
     * @param id
     * @return java.lang.String
     * @Author: 陈秀楼
     * @Date 2019/3/8
     */
    private void checkVipcomProject(long id) {
        StCVipcomProjectDO project = stCVipcomProjectMapper.selectById(id);
        if (project == null) {
            throw new NDSException("当前记录已不存在！");
        } else {
            if (project.getIsactive().equals(StConstant.ISACTIVE_N)) {
                throw new NDSException("当前记录已作废，不允许重复作废！");
            }
        }
    }


}
