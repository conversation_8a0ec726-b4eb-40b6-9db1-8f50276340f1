package com.jackrain.nea.st.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.st.common.StConstant;
import com.jackrain.nea.st.mapper.StCRouterWarehouseMapper;
import com.jackrain.nea.st.model.table.StCRouterWarehouseDO;
import com.jackrain.nea.st.utils.StBeanUtils;
import com.jackrain.nea.st.utils.ValueHolderUtils;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.sql.Timestamp;
import java.util.Date;

/**
 * @Descroption 路由操作码策略
 * <AUTHOR>
 * @Date 2020/5/11 15:45
 */
@Component
@Slf4j
@Transactional
public class RouterWarehouseSaveService extends CommandAdapter {
    @Autowired
    private StCRouterWarehouseMapper mapper;

    public ValueHolder execute(QuerySession session) {
        //1.获取传入参数
        DefaultWebEvent event = session.getEvent();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(
                event.getParameterValue("param"), "yyyy-MM-dd HH:mm:ss", SerializerFeature.WriteMapNullValue),
                Feature.OrderedField);

        Long id = param.getLong("objid");
        JSONObject fixColumn = param.getJSONObject("fixcolumn");
        //json转换成对象
        String routerWarehouse = fixColumn.getString(StConstant.TAB_ST_C_ROUTER_WAREHOUSE);
        //新增或变更
        if (routerWarehouse != null && id != null) {
            StCRouterWarehouseDO stCRouterWarehouse = JSON.parseObject(routerWarehouse, StCRouterWarehouseDO.class);
            if (id != -1) {
                return updateRouterWarehouse(session, id, stCRouterWarehouse);
            } else {
                return addRouterWarehouse(session, stCRouterWarehouse);
            }
        }
        throw new NDSException("传入数据异常！");
    }

    /**
     * @Descroption 路由操作码策略
     * <AUTHOR>
     * @Date 2020/5/11 15:45
     * @param session
     * @param stCRouterWarehouse
     * @return com.jackrain.nea.util.ValueHolder
     */
    private ValueHolder addRouterWarehouse(QuerySession session, StCRouterWarehouseDO stCRouterWarehouse) {
        //1.主表id及创建修改时间赋值
        stCRouterWarehouse.setId(ModelUtil.getSequence(StConstant.TAB_ST_C_ROUTER_WAREHOUSE));

        if (stCRouterWarehouse.getIsAllLogistics() != null && stCRouterWarehouse.getIsAllLogistics() == 1) {
            stCRouterWarehouse.setCpCLogisticsId(null);
        }
        if (stCRouterWarehouse.getIsAllLogistics() != null && stCRouterWarehouse.getIsAllLogistics() == 0 && stCRouterWarehouse.getCpCPhyWarehouseId() == null) {
            throw new NDSException("请选择物流公司!");
        }
        //2.主表数据业务更新校验
        checkRouterWarehouse(stCRouterWarehouse);

        //主表创建信息更新
        Timestamp timestamp = new Timestamp(System.currentTimeMillis());
        stCRouterWarehouse.setAdClientId((long) session.getUser().getClientId());//所属公司
        stCRouterWarehouse.setAdOrgId((long) session.getUser().getOrgId());//所属组织
        stCRouterWarehouse.setOwnerid(Long.valueOf(session.getUser().getId()));//创建人id
        stCRouterWarehouse.setOwnername(session.getUser().getName());//创建人名称
        stCRouterWarehouse.setOwnerename(session.getUser().getEname());
        stCRouterWarehouse.setCreationdate(timestamp);//创建时间
        stCRouterWarehouse.setModifierid(Long.valueOf(session.getUser().getId()));//修改人id
        stCRouterWarehouse.setModifiername(session.getUser().getName());//修改人名称
        stCRouterWarehouse.setModifierename(session.getUser().getEname());//修改人名称
        stCRouterWarehouse.setModifieddate(new Date());//修改时间
        //主表最后信息修改
        StBeanUtils.makeModifierField(stCRouterWarehouse, session.getUser());
        //3.主表数据保存
        int insertResult = mapper.insert(stCRouterWarehouse);
        if (insertResult <= 0) {
            return ValueHolderUtils.getFailValueHolder("保存失败");
        }

        return ValueHolderUtils.getSuccessValueHolder(stCRouterWarehouse.getId(), StConstant.TAB_ST_C_ROUTER_WAREHOUSE);
    }

    /**
     * @Descroption 路由操作码策略
     * <AUTHOR>
     * @Date 2020/5/11 15:45
     * @param session
     * @param id
     * @param stCRouterWarehouse
     * @return com.jackrain.nea.util.ValueHolder
     */
    private ValueHolder updateRouterWarehouse(QuerySession session, Long id, StCRouterWarehouseDO stCRouterWarehouse)  {
        //1.更新前校验
        StCRouterWarehouseDO existsRouterWarehouse = mapper.selectById(id);

        if (existsRouterWarehouse == null) {
            return ValueHolderUtils.getFailValueHolder("当前记录已不存在！");
        }

        //主表数据业务更新校验
        if(stCRouterWarehouse != null){
            //2.主表最后修改信息字段变更
            stCRouterWarehouse.setId(id);

            if (stCRouterWarehouse.getSysType() == null) {
                stCRouterWarehouse.setSysType(existsRouterWarehouse.getSysType());
            }
            if (stCRouterWarehouse.getCpCPhyWarehouseId() == null) {
                stCRouterWarehouse.setCpCPhyWarehouseId(existsRouterWarehouse.getCpCPhyWarehouseId());
            }
            if (stCRouterWarehouse.getIsAllLogistics() == null){
                stCRouterWarehouse.setIsAllLogistics(existsRouterWarehouse.getIsAllLogistics());
            }
            // 所有物流公司
            if (stCRouterWarehouse.getIsAllLogistics() == 1) {
                stCRouterWarehouse.setCpCLogisticsId(null);
                if (mapper.updateLogisticsIdById(id) <= 0) {
                    return ValueHolderUtils.getFailValueHolder("路由操作码策略更新失败");
                }
            }
            if (stCRouterWarehouse.getIsAllLogistics() == 0 && stCRouterWarehouse.getCpCPhyWarehouseId() == null) {
                throw new NDSException("请选择物流公司!");
            }

            checkRouterWarehouse(stCRouterWarehouse);
            StBeanUtils.makeModifierField(stCRouterWarehouse, session.getUser());
            //3.更新主表信息
            if (mapper.updateById(stCRouterWarehouse) <= 0) {
                return ValueHolderUtils.getFailValueHolder("路由操作码策略更新失败");
            }
        }
        return ValueHolderUtils.getSuccessValueHolder(id, StConstant.TAB_ST_C_ROUTER_WAREHOUSE);
    }

    /**
     *
     * @param stCRouterWarehouse
     */
    private void checkRouterWarehouse(StCRouterWarehouseDO stCRouterWarehouse) {
        // 判断方案名称是否重复
        if(mapper.selectCountByEnameAndId(stCRouterWarehouse.getId(), stCRouterWarehouse.getEname()) != 0){
            throw new NDSException("方案名称"+stCRouterWarehouse.getEname()+"已存在!");
        }

        // 判断全选方案是否存在
        if(mapper.selectCountBySysTypeAndWarehouseId(stCRouterWarehouse.getId(), stCRouterWarehouse.getSysType(), stCRouterWarehouse.getCpCPhyWarehouseId()) != 0){
            throw new NDSException("已存在相同实体仓和快递系统的物流公司全选方案!");
        }

        if (stCRouterWarehouse.getIsAllLogistics() != null && stCRouterWarehouse.getIsAllLogistics() == 1) {
            // 判断全选方案是否存在
            if(mapper.selectCountAllBySysTypeAndWarehouseId(stCRouterWarehouse.getId(), stCRouterWarehouse.getSysType(), stCRouterWarehouse.getCpCPhyWarehouseId()) != 0){
                throw new NDSException("已存在相同实体仓和快递系统的物流公司方案，请先作废该方案!");
            }
        }

        if (stCRouterWarehouse.getCpCLogisticsId() != null) {
            if(mapper.selectCountBySysTypeAndWarehouseIdAndLogisticsId(stCRouterWarehouse.getId(),
                    stCRouterWarehouse.getSysType(),
                    stCRouterWarehouse.getCpCPhyWarehouseId(),
                    stCRouterWarehouse.getCpCLogisticsId()) != 0){
                throw new NDSException("已存在相同实体仓、快递系统、物流公司的方案!");
            }
        }
    }
}
