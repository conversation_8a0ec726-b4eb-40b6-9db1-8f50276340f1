package com.jackrain.nea.st.services;

import com.jackrain.nea.st.mapper.StCOrderUrgentStrategyMapper;
import com.jackrain.nea.st.model.table.StCOrderUrgentStrategyDO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @author: 易邵峰
 * @since: 2020-08-29
 * create at : 2020-08-29 22:58
 */
@Component
public class OrderUrgentStrategyService {

    @Autowired
    private StCOrderUrgentStrategyMapper orderUrgentStrategyMapper;

    public StCOrderUrgentStrategyDO selectOrderUrgentStrategy(Long shopId, Integer vipLevel) {

        return orderUrgentStrategyMapper.selectOrderUrgencyStrategy(shopId, vipLevel);
    }

}
