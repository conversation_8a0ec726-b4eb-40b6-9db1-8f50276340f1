package com.jackrain.nea.st.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.st.common.StConstant;
import com.jackrain.nea.st.mapper.StCSellOwngoodsCustomerMapper;
import com.jackrain.nea.st.mapper.StCSellOwngoodsItemMapper;
import com.jackrain.nea.st.mapper.StCSellOwngoodsMapper;
import com.jackrain.nea.st.model.table.StCSellOwngoodsCustomerDO;
import com.jackrain.nea.st.model.table.StCSellOwngoodsDO;
import com.jackrain.nea.st.model.table.StCSellOwngoodsItemDO;
import com.jackrain.nea.st.utils.StBeanUtils;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.HashMap;

/**
 * <AUTHOR>
 * @Description 经销商自有商品审核业务类
 * @Date 2019/3/10
 **/
@Component
@Slf4j
@Transactional
public class SellOwnGoodsAuditService extends CommandAdapter {

    @Autowired
    private StCSellOwngoodsMapper stCSellOwngoodsMapper;
    @Autowired
    private StCSellOwngoodsItemMapper stCSellOwngoodsItemMapper;
    @Autowired
    private StCSellOwngoodsCustomerMapper stCSellOwngoodsCustomerMapper;

    /**
     * @return com.jackrain.nea.util.ValueHolder
     * <AUTHOR>
     * @Description 经销商自有商品审核方法
     * @Date 2019/3/12
     * @Param [session]
     **/
    public ValueHolder auditSellOwnGoods(QuerySession session) {
        DefaultWebEvent event = session.getEvent();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(
                event.getParameterValue("param"), "yyyy-MM-dd HH:mm:ss", SerializerFeature.WriteMapNullValue),
                Feature.OrderedField);
        log.debug(LogUtil.format("请求JSON：") + param.toString());

        ValueHolder resultValueHolder = new ValueHolder();
        HashMap<Long, Object> errMap = new HashMap<Long, Object>();
        //生成审核Json数组
        JSONArray auditArray = StBeanUtils.makeAuditJsonArray(param);
        for (int i = 0; i < auditArray.size(); i++) {
            Long id = Long.valueOf(auditArray.get(i).toString());
            try {
                //审核验证
                checkAudit(id);
                StCSellOwngoodsDO stCSellOwngoodsDO = new StCSellOwngoodsDO();
                stCSellOwngoodsDO.setId(id);
                stCSellOwngoodsDO.setBillStatus(StConstant.CON_BILL_STATUS_02);
                makeCheckerField(stCSellOwngoodsDO, session.getUser());
                StBeanUtils.makeModifierField(stCSellOwngoodsDO, session.getUser());
                //更新单据状态
                int count = stCSellOwngoodsMapper.updateById(stCSellOwngoodsDO);
                if (count < 0) {
                    throw new Exception();
                }
            } catch (Exception e) {
                errMap.put(id, e.getMessage());
            }
        }
        resultValueHolder = StBeanUtils.getExcuteValueHolder(auditArray.size(),errMap);
        return resultValueHolder;
    }

    /**
     * @return com.alibaba.fastjson.JSONObject
     * <AUTHOR>
     * @Description 审核验证
     * @Date 2019/3/10
     * @Param [id]
     **/
    private JSONObject checkAudit(Long id) throws Exception {
        //记录不存在
        StCSellOwngoodsDO stCSellOwngoodsDO = stCSellOwngoodsMapper.selectById(id);
        if (stCSellOwngoodsDO == null) {
            throw new Exception("当前记录已不存在！");
        } else {
            if (StConstant.CON_BILL_STATUS_02.equals(stCSellOwngoodsDO.getBillStatus())) {
                throw new NDSException("当前记录已审核，不允许重复审核！");
            }else if(StConstant.CON_BILL_STATUS_03.equals(stCSellOwngoodsDO.getBillStatus())){
                throw new NDSException("当前记录已作废，不允许审核！");
            }else if(StConstant.CON_BILL_STATUS_04.equals(stCSellOwngoodsDO.getBillStatus())){
                throw new NDSException("当前记录已结案，不允许审核！");
            }
        }

        //明细为空
        Integer itemSize = stCSellOwngoodsItemMapper.selectCount(new QueryWrapper<StCSellOwngoodsItemDO>()
                .eq("ST_C_SELL_OWNGOODS_ID", id));
        Integer customerSize = stCSellOwngoodsCustomerMapper.selectCount(new QueryWrapper<StCSellOwngoodsCustomerDO>()
                .eq("ST_C_SELL_OWNGOODS_ID", id));
        if (itemSize == 0 && customerSize == 0) {
            throw new Exception("当前记录无明细，不允许审核！");
        }
        return null;
    }

    /**
     * @return void
     * <AUTHOR>
     * @Description 审核人bean组装
     * @Date 2019/3/11
     * @Param [stCSellOwngoodsDO, user]
     **/
    public static void makeCheckerField(StCSellOwngoodsDO stCSellOwngoodsDO, User user) {
        stCSellOwngoodsDO.setCheckid(Long.valueOf(user.getId()));//审核人id
        stCSellOwngoodsDO.setCheckname(user.getName());//审核人名称
        stCSellOwngoodsDO.setCheckename(user.getEname());//审核人账号
        stCSellOwngoodsDO.setChecktime(new Date());//审核时间

    }
}
