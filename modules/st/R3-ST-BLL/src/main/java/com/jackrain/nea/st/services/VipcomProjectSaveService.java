package com.jackrain.nea.st.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.cpext.model.table.CpCPhyWarehouse;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.st.common.StConstant;
import com.jackrain.nea.st.mapper.StCVipcomProjectMapper;
import com.jackrain.nea.st.model.table.StCVipcomProjectDO;
import com.jackrain.nea.st.model.table.StCVipcomProjectItemDO;
import com.jackrain.nea.st.rpc.RpcCpService;
import com.jackrain.nea.st.utils.JsonUtils;
import com.jackrain.nea.st.utils.StBeanUtils;
import com.jackrain.nea.st.utils.ValueHolderUtils;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2021/05/26 14:49
 */
@Component
@Slf4j
@Transactional
public class VipcomProjectSaveService extends CommandAdapter {
    @Autowired
    private StCVipcomProjectMapper vipcomProjectMapper;
    @Autowired
    private RpcCpService cpService;

    @Override
    public ValueHolder execute(QuerySession session) {
        DefaultWebEvent event = session.getEvent();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(
                event.getParameterValue("param"), "yyyy-MM-dd HH:mm:ss", SerializerFeature.WriteMapNullValue),
                Feature.OrderedField);
        log.debug(LogUtil.format("Start VipcomProjectSaveService.execute. ReceiveParams: {}"), param.toJSONString());

        Long id = param.getLong("ID");
        //JSONObject fixColumn = param.getJSONObject("fixcolumn");
        JSONObject jsonObject = param.getJSONObject("VIPCOM_PROJECT");
        StCVipcomProjectDO stCVipcomProjectDO = JsonUtils.jsonParseClass(jsonObject, StCVipcomProjectDO.class);

        if (jsonObject != null && id != null) {
            if (id != -1) {
                if (stCVipcomProjectDO == null) {
                    return ValueHolderUtils.getSuccessValueHolder(id, StConstant.TAB_ST_C_VIPCOM_PROJECT);
                }
                return updateVipcomProject(session, id, stCVipcomProjectDO);
            } else {
                return insertVipcomProject(session, stCVipcomProjectDO);
            }
        }
        throw new NDSException("当前记录已不存在！");
    }

    /**
     * 档期日程规划更新
     * @param session
     * @param id
     * @return ValueHolder
     * @Author: 胡林洋
     * @Date 2021/05/26
     */
    public ValueHolder updateVipcomProject(QuerySession session, Long id, StCVipcomProjectDO project) {
        //档期日程规划主表保存
        if (project != null) {
            if (project.getId() == null) {
                project.setId(id);
            }
            StCVipcomProjectDO stCVipcomProjectDO = vipcomProjectMapper.selectById(id);
            if (project.getCpCShopId() == null){
                project.setCpCShopId(stCVipcomProjectDO.getCpCShopId());
            }
            checkVipcomProjectByFilter(id, project, "update");
            //update基础字段补全
            StBeanUtils.makeModifierField(project, session.getUser());
            if (vipcomProjectMapper.updateById(project) < 0) {
                return ValueHolderUtils.getFailValueHolder("更新失败！");
            }
        }
        return ValueHolderUtils.getSuccessValueHolder(id, StConstant.TAB_ST_C_VIPCOM_PROJECT);
    }

    /**
     * 档期日程规划基本信息新增
     * @param session
     * @param project
     * @return ValueHolder
     * @Author: 胡林洋
     * @Date 2021/05/26
     */
    private ValueHolder insertVipcomProject(QuerySession session, StCVipcomProjectDO project) {
        long id = 0;
        if (project != null) {
            checkVipcomProjectByFilter(-1L, project, "insert");
            //生成主键
            id = ModelUtil.getSequence(StConstant.TAB_ST_C_VIPCOM_PROJECT);
            project.setId(id);
            StBeanUtils.makeCreateField(project, session.getUser());
            int insertResult = vipcomProjectMapper.insert(project);
            if (insertResult < 0) {
                return ValueHolderUtils.getFailValueHolder("保存失败！");
            }
        }
        return ValueHolderUtils.getSuccessValueHolder(id, StConstant.TAB_ST_C_VIPCOM_PROJECT);
    }

    /**
     * 数据校验和过滤
     * @param id
     * @param project
     * @param action
     */
    private void checkVipcomProjectByFilter(Long id, StCVipcomProjectDO project, String action) {
        Date beginTime = project.getBeginTime();
        Date endTime = project.getEndTime();
        String ename = project.getEname();
        Long cpCShopId = project.getCpCShopId();
        String rank = project.getRank();

        //同个单据类型  下载时间不重复
        switch (action.toLowerCase()) {
            case "insert":
                HashMap<String, Object> map = new HashMap<>();
                map.put("ename", ename);
                if (!CollectionUtils.isEmpty(vipcomProjectMapper.selectByMap(map))) {
                    throw new NDSException("当前日程计划名称已存在！");
                }
                break;
            case "update":
                StCVipcomProjectDO existsproject = vipcomProjectMapper.selectById(id);
                if (existsproject == null) {
                    throw new NDSException("当前记录已不存在！");
                }
                if(ObjectUtils.isEmpty(cpCShopId)){
                    cpCShopId = existsproject.getCpCShopId();
                }
                if(!ObjectUtils.isEmpty(beginTime) && ObjectUtils.isEmpty(endTime)){
                    endTime = existsproject.getEndTime();
                }
                if(ObjectUtils.isEmpty(beginTime) && !ObjectUtils.isEmpty(endTime)){
                    beginTime = existsproject.getBeginTime();
                }
                break;
            default:
                break;
        }

        //时间判断
        if (beginTime != null && endTime != null) {
            if (endTime.compareTo(beginTime) < 0) {
                throw new NDSException("结束日期不能小于开始日期！");
            }
        }

        //判断相同店仓优先级唯一
        List<StCVipcomProjectDO> stCVipcomProjectDOS = vipcomProjectMapper.selectList(new QueryWrapper<StCVipcomProjectDO>().lambda().eq(StCVipcomProjectDO::getCpCShopId, cpCShopId));
        if (!CollectionUtils.isEmpty(stCVipcomProjectDOS)){
            List<StCVipcomProjectDO> collect = stCVipcomProjectDOS.stream().filter(v -> v.getRank().equals(rank)).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(collect)){
                throw new NDSException("相同店铺下优先级不能重复！");
            }
        }
    }

    /**
     * @param projectItemDO 仓库编码，以id1,id2,形式
     * @return
     * @Description 获取仓库编码
     * <AUTHOR>
     * @date 2019-04-24 2019-04-24
     */
    public void getWarehouseCodesAndName(StCVipcomProjectItemDO projectItemDO) {
        if (StringUtils.isEmpty(projectItemDO.getCpCPhyWarehouseId())) {
            return;
        }
        String[] wareIdArray = projectItemDO.getCpCPhyWarehouseId().split(",");
        List<String> codelist = new ArrayList<>();
        List<String> namelist = new ArrayList<>();
        for (String phyId : wareIdArray
        ) {
            if (StringUtils.isNotEmpty(phyId)) {
                CpCPhyWarehouse phyWarehouse = cpService.getCpCPhyWahouseDoById(Long.valueOf(phyId));
                if (phyWarehouse != null) {
                    codelist.add(phyWarehouse.getEcode());
                    namelist.add(phyWarehouse.getEname());
                }
            }
        }
        String phyWareCodes = StringUtils.join(codelist, ",");
        String phyWareNames = StringUtils.join(namelist, ",");
        projectItemDO.setCpCPhyWarehouseEcode(phyWareCodes);
        projectItemDO.setCpCPhyWarehouseEname(phyWareNames);
    }

}
