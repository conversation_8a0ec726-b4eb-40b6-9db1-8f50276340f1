package com.jackrain.nea.st.services;

import com.google.common.base.Throwables;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.st.mapper.StCVipcomProjectItemMapper;
import com.jackrain.nea.st.mapper.StCVipcomProjectMapper;
import com.jackrain.nea.st.model.request.VipcomDistributionFilterRequest;
import com.jackrain.nea.st.model.table.StCVipcomProjectDO;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.time.FastDateFormat;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.ParseException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date ：2020/12/14 3:59 下午
 * description ：
 * @ Modified By：
 */
@Service
@Slf4j
public class VipcomDistributionFilterService {
    @Autowired
    private StCVipcomProjectMapper vipcomProjectMapper;
    @Autowired
    private StCVipcomProjectItemMapper vipcomProjectItemMapper;

    private static final FastDateFormat FAST_DATE_FORMAT = FastDateFormat.getInstance("yyyy-MM-dd HH:mm:ss");

    private static final FastDateFormat TIME_FORMAT = FastDateFormat.getInstance("HH:mm:ss");


    public ValueHolderV14<List<Long>> filterDistribution(List<VipcomDistributionFilterRequest> requests,
                                                         User operateUser) {
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("VipcomDistributionFilterService.filterDistribution.begin"));
        }

        ValueHolderV14<List<Long>> vh = new ValueHolderV14<>();
        if (operateUser == null) {
            vh.setCode(ResultCode.FAIL);
            vh.setMessage(Resources.getMessage("请求参数为空，请检查参数后重试！"));
            return vh;
        }
        if (CollectionUtils.isEmpty(requests)) {
            vh.setCode(ResultCode.FAIL);
            vh.setMessage(Resources.getMessage("请求参数为空，请检查参数后重试！", operateUser.getLocale()));
            return vh;
        }

        List<Long> distributionIds = new ArrayList<>();
        Map<Long, List<VipcomDistributionFilterRequest>> map =
                requests.stream().collect(Collectors.groupingBy(VipcomDistributionFilterRequest::getShopId));
        for (Map.Entry<Long, List<VipcomDistributionFilterRequest>> longListEntry : map.entrySet()) {
            Long shopId = longListEntry.getKey();
            List<VipcomDistributionFilterRequest> distributionFilterRequests = longListEntry.getValue();
            try {
                if (log.isDebugEnabled()) {
                    log.debug(LogUtil.format("过滤唯品会配货单，店铺id：", shopId));
                }
                for (VipcomDistributionFilterRequest distributionFilterRequest : distributionFilterRequests) {
                    String time = distributionFilterRequest.getTime();
                    String subTime = time.substring(11, 19);
                    StringBuilder stringBuilder = new StringBuilder(subTime);
                    StringBuilder replace = stringBuilder.replace(4, 8, "9:59");
                    subTime = replace.toString();
                    String nowTime = TIME_FORMAT.format(System.currentTimeMillis());
                    stringBuilder = new StringBuilder(nowTime);
                    replace = stringBuilder.replace(4, 8, "9:59");
                    nowTime = replace.toString();


                    List<StCVipcomProjectDO> vipcomProjectDOList = vipcomProjectMapper.selectProjectByShopId(shopId,
                            FAST_DATE_FORMAT.parse(time));
                    if (CollectionUtils.isNotEmpty(vipcomProjectDOList) && vipcomProjectDOList.size() == 1) {
                        StCVipcomProjectDO vipcomProjectDO = vipcomProjectDOList.get(0);
                        Date maxDownloadTime = vipcomProjectItemMapper.selectVipcomProjectItemList(vipcomProjectDO.getId(),
                                nowTime);
                        if (maxDownloadTime != null
                                && maxDownloadTime.getTime() <= TIME_FORMAT.parse(subTime).getTime()) {
                            if (log.isDebugEnabled()) {
                                log.debug(LogUtil.format("过滤唯品会配货单：最大下载时间：{}，店铺id=",
                                        shopId), FAST_DATE_FORMAT.format(maxDownloadTime));
                            }
                            distributionIds.add(distributionFilterRequest.getDistributionId());
                        }
                    }
                }
            } catch (ParseException e) {
                log.error(LogUtil.format("过滤唯品会配货单异常：{},店铺id=", shopId), Throwables.getStackTraceAsString(e));
            }
        }

        vh.setCode(ResultCode.SUCCESS);
        vh.setMessage("过滤唯品会配货单成功");
        vh.setData(distributionIds);
        return vh;
    }
}
