package com.jackrain.nea.st.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.redis.util.RedisOpsUtil;
import com.jackrain.nea.st.common.StConstant;
import com.jackrain.nea.st.mapper.StCOrderLabelLogMapper;
import com.jackrain.nea.st.mapper.StCOrderLabelMapper;
import com.jackrain.nea.st.mapper.StCOrderLabelShopItemMapper;
import com.jackrain.nea.st.model.table.StCOrderLabelDO;
import com.jackrain.nea.st.model.table.StCOrderLabelLogDO;
import com.jackrain.nea.st.model.table.StCOrderLabelShopItemDO;
import com.jackrain.nea.st.utils.RedisConstant;
import com.jackrain.nea.st.utils.StBeanUtils;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @ClassName : StCOrderLabelFinishService
 * @Description : 订单打标结案
 * <AUTHOR>  YCH
 * @Date: 2021-11-23 16:07
 */
@Component
@Slf4j
public class StCOrderLabelFinishService extends CommandAdapter {

    @Autowired
    private StCOrderLabelMapper stCOrderLabelMapper;
    @Autowired
    private StCOrderLabelLogMapper stCOrderLabelLogMapper;
    @Autowired
    private StCOrderLabelShopItemMapper shopItemMapper;
    @Autowired
    private RedisOpsUtil<String, String> redisUtil;

    @Override
    public ValueHolder execute(QuerySession querySession) throws NDSException {
        //1.获取传入参数
        DefaultWebEvent event = querySession.getEvent();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(
                        event.getParameterValue("param"), "yyyy-MM-dd HH:mm:ss"),
                Feature.OrderedField);
        if (param == null) {
            throw new NDSException("参数为空！");
        }
        log.debug(LogUtil.format("Start StCOrderLabelFinishService.execute. ReceiveParams: {}"), param.toJSONString());
        //2.定义错误记录数
        HashMap<Long, Object> errMap = new HashMap();
        JSONArray voidArray = StBeanUtils.makeUnAuditJsonArray(param);
        for (int i = 0; i < voidArray.size(); i++) {
            Long id = voidArray.getLong(i);
            try {
                //4.遍历结案方法
                voidAction(id, querySession);

                List<StCOrderLabelShopItemDO> shopItemDOList = shopItemMapper.selectStCOrderLabelShopItemList(id);
                if (org.apache.commons.collections.CollectionUtils.isNotEmpty(shopItemDOList)) {
                    List<Long> shopIds = shopItemDOList.stream().distinct().map(StCOrderLabelShopItemDO::getCpCShopId).collect(Collectors.toList());
                    for (Long shopId : shopIds) {
                        redisUtil.strRedisTemplate.delete(RedisConstant.ST_ORDER_LABEL + shopId);
                    }
                }

                saveLog(id, querySession);
            } catch (Exception e) {
                errMap.put(id, e.getMessage());
            }
        }
        //5.返回ValueHolder对象
        return StBeanUtils.getExcuteValueHolder(voidArray.size(), errMap);
    }


    public void voidAction(Long id, QuerySession querySession) {
        StCOrderLabelDO stCOrderLabelDO = stCOrderLabelMapper.selectById(id);
        //主表校验
        checkAction(stCOrderLabelDO);
        stCOrderLabelDO.setStatus(StConstant.HOLD_ORDER_STATUS_04);
        //更新状态
        StBeanUtils.makeModifierField(stCOrderLabelDO, querySession.getUser());

        int updateNum = stCOrderLabelMapper.updateById(stCOrderLabelDO);
        if (updateNum < 0) {
            throw new NDSException("结案失败！");
        }
    }

    private void saveLog(Long id, QuerySession querySession) {
        StCOrderLabelLogDO stCOrderLabelLogDO = new StCOrderLabelLogDO();
        stCOrderLabelLogDO.setId(ModelUtil.getSequence(StConstant.TAB_ST_C_ORDER_LABEL_LOG));
        stCOrderLabelLogDO.setStCOrderLabelId(id);
        stCOrderLabelLogDO.setModcontent("结案");
        stCOrderLabelLogDO.setBmod("已审核");
        stCOrderLabelLogDO.setAmod("已结案");
        StBeanUtils.makeCreateField(stCOrderLabelLogDO, querySession.getUser());
        stCOrderLabelLogMapper.insert(stCOrderLabelLogDO);
    }

    private void checkAction(StCOrderLabelDO stCOrderLabelDO) {
        if (stCOrderLabelDO == null) {
            throw new NDSException("当前记录已不存在！");
        } else {
            if (StConstant.CON_BILL_STATUS_03.equals(stCOrderLabelDO.getStatus())) {
                throw new NDSException("当前记录已作废，不允许结案！");
            }
            if (StConstant.CON_BILL_STATUS_04.equals(stCOrderLabelDO.getStatus())) {
                throw new NDSException("当前记录已结案，不允许重复结案！");
            }
            if (!StConstant.CON_BILL_STATUS_02.equals(stCOrderLabelDO.getStatus())) {
                throw new NDSException("当前记录非审核状态，不允许结案！");
            }
        }
    }
}
