package com.jackrain.nea.st.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.jackrain.nea.st.common.StConstant;
import com.jackrain.nea.st.mapper.StCSplitReasonItemMapper;
import com.jackrain.nea.st.mapper.StCSplitReasonMapper;
import com.jackrain.nea.st.model.table.StCPriceDO;
import com.jackrain.nea.st.model.table.StCSplitReasonConfigDO;
import com.jackrain.nea.st.model.table.StCSplitReasonDO;
import com.jackrain.nea.st.model.table.StCSplitReasonItemDO;
import com.jackrain.nea.st.utils.RedisCacheUtil;
import com.jackrain.nea.st.utils.RedisConstant;
import com.jackrain.nea.st.utils.StBeanUtils;
import com.jackrain.nea.st.utils.ValueHolderUtils;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * @program: r3-st
 * @description: 自定义拆单删除业务类
 * @author: liuwj
 * @create: 2021-05-31 15:34
 **/
@Component
@Slf4j
@Transactional
public class StCSplitReasonDeleteService extends CommandAdapter {

    @Autowired
    StCSplitReasonMapper stCSplitReasonMapper;

    @Autowired
    StCSplitReasonItemMapper stCSplitReasonItemMapper;

    @Override
    public ValueHolder execute(QuerySession querySession) {
        DefaultWebEvent event = querySession.getEvent();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(
                event.getParameterValue("param"), "yyyy-MM-dd HH:mm:ss"),
                Feature.OrderedField);
        boolean delMainFlag = param.getBoolean("isdelmtable");//是否删除主表
        Long id = param.getLong("objid");
        JSONArray errorArray = new JSONArray();
        if (id != null && id > 0) {
            ValueHolder valueHolder = new ValueHolder();
            StCSplitReasonDO stCSplitReasonDO = stCSplitReasonMapper.selectById(id);
            if (stCSplitReasonDO == null) {
                return ValueHolderUtils.getFailValueHolder("当前记录已不存在！");
            }
            if (delMainFlag) {
                deletetReason(stCSplitReasonDO, errorArray);
            } else {
                //单独删除明细
                JSONObject tabitem = param.getJSONObject("tabitem");
                JSONArray itemArray = tabitem.getJSONArray(StConstant.TAB_ST_C_SPLIT_REASON_ITEM);
                if (itemArray != null && itemArray.size() > 0) {
                    deleteSplitReasonItem(itemArray, querySession, errorArray);
                }
                updateSplitReason(querySession, id);
                return StBeanUtils.getExcuteValueHolder(errorArray);
            }
        }
        return StBeanUtils.getExcuteValueHolder(errorArray);
    }

    /**
     * <AUTHOR>
     * @Date 14:35 2021/6/1
     * @Description 删除主表
     */
    private void deletetReason(StCSplitReasonDO stCSplitReasonDO, JSONArray errorArray) {
        int count1 = stCSplitReasonItemMapper.delete(new QueryWrapper<StCSplitReasonItemDO>().lambda().eq(StCSplitReasonItemDO::getSplitReasonId, stCSplitReasonDO.getId()));
        if (count1 <= 0) {
            errorArray.add("删除明细表失败");
        }
        int count = stCSplitReasonMapper.deleteById(stCSplitReasonDO);
        if (count <= 0) {
            errorArray.add("删除主表失败");
        }
    }

    /**
     * <AUTHOR>
     * @Date 17:35 2021/5/31
     * @Description 更新主表新
     */
    private void updateSplitReason(QuerySession session, Long id) {
        StCSplitReasonDO stCSplitReasonDO = new StCSplitReasonDO();
        stCSplitReasonDO.setId(id);
        StBeanUtils.makeModifierField(stCSplitReasonDO, session.getUser());//修改信息
        stCSplitReasonDO.setModifierename(session.getUser().getEname());//修改人账号
        if ((stCSplitReasonMapper.updateById(stCSplitReasonDO)) <= 0) {
            log.error(LogUtil.format("PriceDelService.updatePriceDate.Error", "删除明细，主表修改字段信息更新出错id:", id));
        }
    }

    /**
     * <AUTHOR>
     * @Date 17:33 2021/5/31
     * @Description 删除明细
     */
    private void deleteSplitReasonItem(JSONArray itemArray, QuerySession querySession, JSONArray errorArray) {
        if (itemArray.size() > 0) {
            for (int i = 0; i < itemArray.size(); i++) {
                Long itemid = itemArray.getLong(i);
                if ((stCSplitReasonItemMapper.deleteById(itemid)) <= 0) {
                    errorArray.add(StBeanUtils.getJsonObjectInfo(itemid, "自定义拆单-明细记录已不存在！"));
                }
            }
        }
    }
}
