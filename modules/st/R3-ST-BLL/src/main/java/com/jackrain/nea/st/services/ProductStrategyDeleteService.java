package com.jackrain.nea.st.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.google.common.base.Throwables;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.st.common.StConstant;
import com.jackrain.nea.st.mapper.StCProductStrategyItemMapper;
import com.jackrain.nea.st.mapper.StCProductStrategyMapper;
import com.jackrain.nea.st.model.table.StCProductStrategyDO;
import com.jackrain.nea.st.model.table.StCProductStrategyItemDO;
import com.jackrain.nea.st.utils.DatasToEsUtils;
import com.jackrain.nea.st.utils.StBeanUtils;
import com.jackrain.nea.st.utils.ValueHolderUtils;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @Description 店铺商品特殊设置删除业务类
 * @Date 2019/3/12
 **/
@Component
@Slf4j
@Transactional
public class ProductStrategyDeleteService extends CommandAdapter {

    @Autowired
    private StCProductStrategyMapper stCProductStrategyMapper;
    @Autowired
    private StCProductStrategyItemMapper stCProductStrategyItemMapper;

    /**
     * @return com.jackrain.nea.util.ValueHolder
     * <AUTHOR>
     * @Description 店铺商品特殊设置删除方法
     * @Date 2019/3/12
     * @Param [session]
     **/
    public ValueHolder deleteProductStrategy(QuerySession session) {
        DefaultWebEvent event = session.getEvent();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(
                event.getParameterValue("param"), "yyyy-MM-dd HH:mm:ss", SerializerFeature.WriteMapNullValue),
                Feature.OrderedField);
        log.debug(LogUtil.format("请求JSON：") + param.toString());

        ValueHolder valueHolder = new ValueHolder();

        Boolean isdelmtable = param.getBoolean("isdelmtable");
        //无主表删除，主表均为作废方案不会无理删除数据
        Long objid = param.getLong("objid");
        if (!isdelmtable) {
            List ids = new ArrayList();
            JSONObject tableItem = (JSONObject) param.get("tabitem");
            JSONArray jsonArray = tableItem.getJSONArray(StConstant.TAB_ST_C_PRODUCT_STRATEGY_ITEM);
            if (jsonArray != null) {
                ids = jsonArray;
            } else {
                log.debug(LogUtil.format("从表转换JsonArray为空！"));
                return ValueHolderUtils.getFailValueHolder("记录删除失败！");
            }
            int count = 0;
            List<StCProductStrategyItemDO> stCProductStrategyItemDOList = stCProductStrategyItemMapper.selectBatchIds(ids);
            count = stCProductStrategyItemMapper.deleteBatchIds(ids);
            if (count > 0) {
                //组装更新修改人bean
                StCProductStrategyDO stCProductStrategyDO = new StCProductStrategyDO();
                stCProductStrategyDO.setId(objid);
                StBeanUtils.makeModifierField(stCProductStrategyDO, session.getUser());
                int mainCount = stCProductStrategyMapper.updateById(stCProductStrategyDO);
                if (mainCount < 0) {
                    log.debug(LogUtil.format("主表更新操作人失败，删除失败！"));
                    valueHolder = ValueHolderUtils.getFailValueHolder("明细删除失败！");
                } else {
                    deleteProductEsData(stCProductStrategyDO,stCProductStrategyItemDOList);//删除ES上的数据
                    return ValueHolderUtils.getDeleteSuccessValueHolder();
                }
            } else {
                log.debug(LogUtil.format("删除的记录不存在！"));
                valueHolder = ValueHolderUtils.getFailValueHolder("明细删除失败！");
            }
        }
        return valueHolder;
    }
    /*
     *@DATE :2019 - 05 - 20
     *删除es数据
     */
    private void deleteProductEsData(StCProductStrategyDO stCProductStrategyDO,List<StCProductStrategyItemDO> stCProductStrategyItemDOList ){
        try{
            //查询明细，并删除es
            for(StCProductStrategyItemDO stCProductStrategyItemDO: stCProductStrategyItemDOList){
                DatasToEsUtils.deleteProductEsData(stCProductStrategyDO,stCProductStrategyItemDO,StConstant.TAB_ST_C_PRODUCT_STRATEGY_ITEM);//明细表删除
            }
        }catch(Exception ex){
            if(ex.toString().contains("404 Not Found")){
                log.error(LogUtil.format("删除ES数据失败：{}"), Throwables.getStackTraceAsString(ex));
            }else{
                throw new NDSException("删除ES数据失败！"+ex.toString());
            }
        }
    }

}
