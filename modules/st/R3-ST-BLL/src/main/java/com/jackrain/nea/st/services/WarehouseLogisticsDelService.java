package com.jackrain.nea.st.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.google.common.collect.Lists;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.psext.api.utils.JsonUtils;
import com.jackrain.nea.st.common.StConstant;
import com.jackrain.nea.st.mapper.StCWarehouseLogisticsItemMapper;
import com.jackrain.nea.st.mapper.StCWarehouseLogisticsMapper;
import com.jackrain.nea.st.mapper.StCWarehouseLogisticsRankMapper;
import com.jackrain.nea.st.model.result.LogisticsRankResult;
import com.jackrain.nea.st.model.table.StCWarehouseLogisticsDO;
import com.jackrain.nea.st.model.table.StCWarehouseLogisticsItemDO;
import com.jackrain.nea.st.model.table.StCWarehouseLogisticsRankDO;
import com.jackrain.nea.st.utils.StBeanUtils;
import com.jackrain.nea.st.utils.ValueHolderUtils;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 删除业务逻辑
 *
 * <AUTHOR> 黄超
 * @since : 2019-08-15
 * create at : 2019-08-15 10:00
 */

@Component
@Slf4j
@Transactional
public class WarehouseLogisticsDelService extends CommandAdapter {
    @Autowired
    private StCWarehouseLogisticsMapper mapper;

    @Autowired
    private StCWarehouseLogisticsItemMapper itemMapper;

    @Autowired
    private StCWarehouseLogisticsRankMapper rankMapper;

    /**
     * 主子表删除
     *
     * @param querySession
     * @return
     * @throws NDSException
     */
    @Override
    public ValueHolder execute(QuerySession querySession) throws NDSException {
        ValueHolder holder = new ValueHolder();
        DefaultWebEvent event = querySession.getEvent();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(
                event.getParameterValue("param"), "yyyy-MM-dd HH:mm:ss"), Feature.OrderedField);

        if (param == null || param.size() == 0) {
            holder = ValueHolderUtils.getFailValueHolder("参数为空!");
            return holder;
        }
        String isDel = param.getString("isdelmtable");
        Long objid = param.getLong("objid");

        //判断主表是否存在
        StCWarehouseLogisticsDO stCWarehouseLogisticsDO = mapper.selectById(objid);
        if (stCWarehouseLogisticsDO == null) {
            holder = ValueHolderUtils.getFailValueHolder("当前记录已不存在！");
            return holder;
        }
        JSONObject tabitem = param.getJSONObject("tabitem");
        //判断是删除主表还是明细表单独删除
        if (StConstant.TRUE_STR.equals(isDel)) {
            mapper.deleteById(stCWarehouseLogisticsDO);
            holder = ValueHolderUtils.getDeleteSuccessValueHolder();
        } else {
            JSONArray itemArray = tabitem.getJSONArray("ST_C_WAREHOUSE_LOGISTICS_ITEM");
            //单独删除明细
            holder = delItem(itemArray, objid, querySession, stCWarehouseLogisticsDO);
        }
        return holder;
    }

    /**
     * 删除明细表
     *
     * @param itemArray 子表数据
     * @param id    主表id
     * @return 返回状态
     */
    public ValueHolder delItem(JSONArray itemArray, Long id, QuerySession querySession,
                               StCWarehouseLogisticsDO stCWarehouseLogisticsDO) {
        ValueHolder holder = new ValueHolder();
        int iSuc = 0;
        //明细表记录不存在，则提示：当前记录已不存在！
        List<Long> logisticsIdList = Lists.newArrayList();
        for (int i = 0; i < itemArray.size(); i++) {
            Long itemId = itemArray.getLong(i);
            StCWarehouseLogisticsItemDO stCWarehouseLogisticsItemDO = itemMapper.selectById(itemId);
            if (stCWarehouseLogisticsItemDO != null) {
                logisticsIdList.add(stCWarehouseLogisticsItemDO.getCpCLogisticsId());
                if (itemMapper.deleteById(itemId) > 0) {
                    iSuc += 1;
                }
            }
        }
        //更新物流公司优先级明细表
        List<StCWarehouseLogisticsRankDO> rankList = rankMapper.selectList(new QueryWrapper<StCWarehouseLogisticsRankDO>()
                .lambda().eq(StCWarehouseLogisticsRankDO::getStCWarehouseLogisticsId, id));
        for (StCWarehouseLogisticsRankDO rank : rankList) {
            //获取物流公司优先级集合
            if (!StringUtils.isBlank(rank.getLogisticsRank())) {
                List<LogisticsRankResult> logisticsRankList = JsonUtils.jsonToList(LogisticsRankResult.class, rank.getLogisticsRank());
                if (CollectionUtils.isNotEmpty(logisticsRankList)) {
                    logisticsRankList = logisticsRankList.stream().filter(logistics ->
                            !logisticsIdList.contains(logistics.getLogisticsId())).collect(Collectors.toList());
                    rank.setLogisticsRank(JSONArray.toJSONString(logisticsRankList));
                    rankMapper.updateById(rank);
                }
            }
        }
        if (iSuc > 0) {
            StBeanUtils.makeModifierField(stCWarehouseLogisticsDO, querySession.getUser());
            mapper.updateById(stCWarehouseLogisticsDO);
            holder = ValueHolderUtils.getDeleteSuccessValueHolder();
        } else {
            holder = ValueHolderUtils.getFailValueHolder("删除记录不存在！");
        }
        return holder;
    }
}
