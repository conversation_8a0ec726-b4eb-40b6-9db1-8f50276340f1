package com.jackrain.nea.st.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.google.common.base.Throwables;
import com.google.common.collect.Lists;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.st.common.StConstant;
import com.jackrain.nea.st.mapper.StCLockSkuStrategyItemMapper;
import com.jackrain.nea.st.mapper.StCLockSkuStrategyMapper;
import com.jackrain.nea.st.model.table.StCLockSkuStrategyDO;
import com.jackrain.nea.st.model.table.StCLockSkuStrategyItemDO;
import com.jackrain.nea.st.utils.DatasToEsUtils;
import com.jackrain.nea.st.utils.ListUtils;
import com.jackrain.nea.st.utils.StBeanUtils;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

@Component
@Slf4j
@Transactional
public class LockSkuStrategyFinishService extends CommandAdapter {
    @Autowired
    private StCLockSkuStrategyMapper stCLockSkuStrategyMapper;

    @Autowired
    private StCLockSkuStrategyItemMapper stCLockSkuStrategyItemMapper;

//    @Reference(group = "sg", version = "1.0")
//    private SgLockSkuStrategyBasicCmd skuStrategyBasicCmd;
//
//    @Autowired
//    private ShopUniversalStockService shopUniversalStockService;

    @Override
    public ValueHolder execute(QuerySession querySession) throws NDSException {
        ValueHolder valueHolder = new ValueHolder();
        DefaultWebEvent event = querySession.getEvent();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(
                event.getParameterValue("param"), "yyyy-MM-dd HH:mm:ss"),
                Feature.OrderedField);

        if (param != null) {
            JSONArray itemArray = StBeanUtils.makeFinishJsonArray(param);
            valueHolder = finishLockSkuStrategy(itemArray, valueHolder, querySession);
        } else {
            throw new NDSException("参数为空！");
        }
        return valueHolder;
    }

    private JSONObject checkStatus(StCLockSkuStrategyDO stCLockSkuStrategyDO, Long objId, JSONObject errJo) {
        int iStatus = stCLockSkuStrategyDO.getEstatus();
        if (iStatus != StConstant.CON_BILL_STATUS_02) {
            errJo.put("objid", objId);
            errJo.put("message", "单据处于审核状态才能进行结案！");
            return errJo;
        }

        List<StCLockSkuStrategyItemDO> stCLockSkuStrategyItemDOList = stCLockSkuStrategyItemMapper.listByItemId(objId);
        if (stCLockSkuStrategyItemDOList == null || stCLockSkuStrategyItemDOList.isEmpty()) {
            errJo.put("objid", objId);
            errJo.put("message", "当前代销运费方案明细没有记录，不允许结案！");
            return errJo;
        }
        return null;
    }

    private ValueHolder finishLockSkuStrategy(JSONArray itemArray, ValueHolder valueHolder, QuerySession querySession) {
        //结案记录前，先判断是否存在
        int size = itemArray.size();
        if (size > 0) {
            List<StCLockSkuStrategyDO> cLockSkuStrategyList = stCLockSkuStrategyMapper.
                    selectBatchIds(JSONObject.parseArray(itemArray.toJSONString(), Long.class));
            List<Long> ids = Lists.newArrayListWithExpectedSize(size);
            JSONArray errorArray = new JSONArray();
            for (StCLockSkuStrategyDO cLockSkuStrategyDO : cLockSkuStrategyList) {
                JSONObject errJo = new JSONObject();
                Long objId = cLockSkuStrategyDO.getId();
                StCLockSkuStrategyDO stCLockSkuStrategyDO = stCLockSkuStrategyMapper.selectById(objId);

                if (stCLockSkuStrategyDO != null) {
                    JSONObject errorJson = checkStatus(stCLockSkuStrategyDO, objId, errJo);
                    if (errorJson != null) {
                        errorArray.add(errorJson);
                    } else {
                        //BILL_STATUS    1:未审核 2:已审核 3:已作废 4:已结案
                        stCLockSkuStrategyDO.setEstatus(StConstant.CON_BILL_STATUS_04);
                        stCLockSkuStrategyDO.setFinishid(Long.valueOf(querySession.getUser().getId()));//结案人
                        stCLockSkuStrategyDO.setFinishtime(new Date());//结案时间
                        stCLockSkuStrategyDO.setFinishname(querySession.getUser().getName());//结案人姓名
                        stCLockSkuStrategyDO.setFinishename(querySession.getUser().getEname());//结案人账号
                        int iResult = stCLockSkuStrategyMapper.updateById(stCLockSkuStrategyDO);
                        if (iResult <= 0) {
                            errJo.put("objid", objId);
                            errJo.put("message", "结案失败！");
                            errorArray.add(errJo);
                        } else {
//                            ValueHolder holder = shopUniversalStockService.deleteSgBSyncChannelStock(StConstant.LOCK_SKU_STRATEGY_TYPE.longValue(), objId);
//                            log.debug(LogUtil.format("锁库条码结案 删除同步库存中间表数据 策略id："+objId+" 结果："+JSONObject.toJSONString(holder));
                            ids.add(objId);
                            //推送ES数据
                            try {
                                //做更新的需要先查询更新后数据库的实体在推ES
                                stCLockSkuStrategyDO = stCLockSkuStrategyMapper.selectById(objId);
                                StCLockSkuStrategyItemDO item = new StCLockSkuStrategyItemDO();
                                item.setStatus(StConstant.CON_BILL_STATUS_04);
                                QueryWrapper<StCLockSkuStrategyItemDO> wrapper = new QueryWrapper<>();
                                wrapper.eq("st_c_lock_sku_strategy_id", objId);
                                stCLockSkuStrategyItemMapper.update(item, wrapper);
                                List<StCLockSkuStrategyItemDO> itemList = ListUtils.batchQueryByCondition(wrapper, stCLockSkuStrategyItemMapper, 1000);
                                DatasToEsUtils.insertLoclSkuEsData(stCLockSkuStrategyDO, null, StConstant.TAB_ST_C_LOCK_SKU_STRATEGY);
                                if (CollectionUtils.isNotEmpty(itemList)) {
                                    DatasToEsUtils.insertLoclSkuEsData(stCLockSkuStrategyDO, itemList, StConstant.TAB_ST_C_LOCK_SKU_STRATEGY_ITEM);
                                }
                            } catch (Exception ex) {
                                log.debug(LogUtil.format("店铺锁库条码特殊设置策略主表结案推数据到ES失败：{}"), Throwables.getStackTraceAsString(ex));
                            }
                        }
                    }
                }
            }
            if (!ids.isEmpty()) {
                //  完结修改状态
//                skuStrategyBasicCmd.status(ids, StConstant.CON_BILL_STATUS_04);
            }
            valueHolder = StBeanUtils.getProcessValueHolder(itemArray, errorArray, "结案");
            return valueHolder;
        }
        return valueHolder;
    }

    /**
     * <AUTHOR>
     * @Description 批量结案锁库条码
     * @Date 14:11 2020/12/18
     * @param ids
     * @param user
     * @return com.jackrain.nea.sys.domain.ValueHolderV14
     **/
    @Transactional(rollbackFor = Exception.class)
    public ValueHolderV14 batchFinishSkuStrategy(List<Long> ids, User user) {
        ValueHolderV14 valueHolder = new ValueHolderV14();
        if (CollectionUtils.isEmpty(ids)) {
            valueHolder.setCode(ResultCode.FAIL);
            valueHolder.setMessage("传入结案参数列表为空");
            return valueHolder;
        }
        try{
            /**1. 批量修改主表数据*/
            StCLockSkuStrategyDO main = new StCLockSkuStrategyDO();
            main.setEstatus(StConstant.CON_BILL_STATUS_04);
            main.setFinishid(Long.valueOf(user.getId()));
            main.setFinishename(user.getEname());
            main.setFinishname(user.getName());
            main.setFinishtime(new Date());
            StBeanUtils.makeModifierField(main,user);
            QueryWrapper<StCLockSkuStrategyDO> wrapper = new QueryWrapper<>();
            wrapper.in("id", ids);
            stCLockSkuStrategyMapper.update(main, wrapper);

            /**2. 批量修改明细数据*/
            StCLockSkuStrategyItemDO item = new StCLockSkuStrategyItemDO();
            item.setStatus(StConstant.CON_BILL_STATUS_04);
            StBeanUtils.makeModifierField(item,user);
            QueryWrapper<StCLockSkuStrategyItemDO> itemWrapper = new QueryWrapper<>();
            itemWrapper.in("st_c_lock_sku_strategy_id", ids);
            stCLockSkuStrategyItemMapper.update(item, itemWrapper);

            /**3. 批量更新es*/
            List<StCLockSkuStrategyDO> lockSkuStrategyDOS = stCLockSkuStrategyMapper.selectList(wrapper);
            for (StCLockSkuStrategyDO lockSkuStrategyDO : lockSkuStrategyDOS) {
                DatasToEsUtils.insertLoclSkuEsData(lockSkuStrategyDO, null, StConstant.TAB_ST_C_LOCK_SKU_STRATEGY);

                QueryWrapper<StCLockSkuStrategyItemDO> esWrapper = new QueryWrapper<>();
                esWrapper.eq("st_c_lock_sku_strategy_id", lockSkuStrategyDO.getId());
                List<StCLockSkuStrategyItemDO> itemList = ListUtils.batchQueryByCondition(esWrapper, stCLockSkuStrategyItemMapper, 1000);
                if (CollectionUtils.isNotEmpty(itemList)) {
                    DatasToEsUtils.insertLoclSkuEsData(lockSkuStrategyDO, itemList, StConstant.TAB_ST_C_LOCK_SKU_STRATEGY_ITEM);
                }
            }
            valueHolder.setCode(ResultCode.SUCCESS);
        } catch (Exception e) {
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("【执行批量锁库条码结案异常】{}"), Throwables.getStackTraceAsString(e));
            }
            valueHolder.setCode(ResultCode.FAIL);
            valueHolder.setMessage(e.getMessage());
        }
        return valueHolder;
    }
}

