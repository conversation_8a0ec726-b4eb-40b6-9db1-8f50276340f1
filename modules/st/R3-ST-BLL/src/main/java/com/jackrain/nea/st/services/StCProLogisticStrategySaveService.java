package com.jackrain.nea.st.services;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.collect.Lists;
import com.jackrain.nea.cpext.api.CpLogisticsQueryCmd;
import com.jackrain.nea.cpext.api.CpPhyWarehouseSelectCmd;
import com.jackrain.nea.cpext.model.table.CpCPhyWarehouse;
import com.jackrain.nea.cpext.model.table.CpLogistics;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.psext.model.table.PsCPro;
import com.jackrain.nea.st.annotation.StOperationLog;
import com.jackrain.nea.st.common.StConstant;
import com.jackrain.nea.st.mapper.StCProLogisticStrategyMapper;
import com.jackrain.nea.st.model.request.StCProLogisticStrategyRequest;
import com.jackrain.nea.st.model.table.StCProLogisticStrategy;
import com.jackrain.nea.st.rpc.RpcPsService;
import com.jackrain.nea.st.utils.JsonUtils;
import com.jackrain.nea.st.utils.StBeanUtils;
import com.jackrain.nea.st.utils.ValueHolderUtils;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * Created with IntelliJ IDEA.
 *
 * @Author: jg.zhan
 * @Date: 2022/6/21 14:47
 * @Description: 商品物流设置保存
 */
@Component
@Slf4j
public class StCProLogisticStrategySaveService extends CommandAdapter {

    @Autowired
    private StCProLogisticStrategyMapper stCProLogisticStrategyMapper;

    @Autowired
    private RpcPsService rpcPsService;

    @Reference(group = "cp-ext", version = "1.0")
    private CpLogisticsQueryCmd cpLogisticsQueryCmd;

    @Reference(group = "cp-ext", version = "1.0")
    private CpPhyWarehouseSelectCmd cpPhyWarehouseSelectCmd;

    @Override
    @StOperationLog(mainTableName = "ST_C_PRO_LOGISTIC_STRATEGY")
    public ValueHolder execute(QuerySession session) {
        DefaultWebEvent event = session.getEvent();
        List<String> nullKeyList = new ArrayList<>();
        JSONObject param = JsonUtils.filterNull((JSONObject) event.getParameterValue("param"), nullKeyList);
        log.info(LogUtil.format("StCProLogisticStrategySaveService.execute param:{},nullKeyList:{}",
                "StCProLogisticStrategySaveService.execute"), param.toJSONString(), JSONObject.toJSONString(nullKeyList));
        if (param != null) {
            Long id = param.getLong("objid");
            JSONObject fixColumn = param.getJSONObject("fixcolumn");
            StCProLogisticStrategyRequest stCProLogisticStrategyRequest = JsonUtils.jsonParseClass(fixColumn, StCProLogisticStrategyRequest.class);
            if (fixColumn != null && id != null) {
                if (id != -1) {
                    return updateStCProLogisticStrategy(session, id, stCProLogisticStrategyRequest, nullKeyList);
                } else {
                    return insertStCProLogisticStrategy(session, stCProLogisticStrategyRequest, nullKeyList);
                }
            }
        }
        throw new NDSException("当前记录已不存在！");
    }

    private void checkInsertParam(StCProLogisticStrategyRequest request) {
        if (Objects.isNull(request)) {
            throw new NDSException("入参不能为空！");
        }
        StCProLogisticStrategy stCProLogisticStrategy = request.getStCProLogisticStrategy();
        //检查维护规则
        boolean flag = checkData(stCProLogisticStrategy);
        if (!flag) {
            throw new NDSException("规则不正确，请检查!");
        }
        LambdaQueryWrapper<StCProLogisticStrategy> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(StCProLogisticStrategy::getCpCLogisticsId, stCProLogisticStrategy.getCpCLogisticsId());
        if (Objects.nonNull(stCProLogisticStrategy.getCpCProvinceId())) {
            wrapper.eq(StCProLogisticStrategy::getCpCProvinceId, stCProLogisticStrategy.getCpCProvinceId());
        } else {
            wrapper.isNull(StCProLogisticStrategy::getCpCProvinceId);
        }
        if (Objects.nonNull(stCProLogisticStrategy.getCpCCityId())) {
            wrapper.eq(StCProLogisticStrategy::getCpCCityId, stCProLogisticStrategy.getCpCCityId());
        } else {
            wrapper.isNull(StCProLogisticStrategy::getCpCCityId);
        }
        if (Objects.nonNull(stCProLogisticStrategy.getCpCAreaId())) {
            wrapper.eq(StCProLogisticStrategy::getCpCAreaId, stCProLogisticStrategy.getCpCAreaId());
        } else {
            wrapper.isNull(StCProLogisticStrategy::getCpCAreaId);
        }
        if (StringUtils.isNotEmpty(stCProLogisticStrategy.getNumIid())) {
            wrapper.eq(StCProLogisticStrategy::getNumIid, stCProLogisticStrategy.getNumIid());
        } else {
            wrapper.isNull(StCProLogisticStrategy::getNumIid);
        }
        if (Objects.nonNull(stCProLogisticStrategy.getPsCProdimId())) {
            wrapper.eq(StCProLogisticStrategy::getPsCProdimId, stCProLogisticStrategy.getPsCProdimId());
        } else {
            wrapper.isNull(StCProLogisticStrategy::getPsCProdimId);
        }
        if (Objects.nonNull(stCProLogisticStrategy.getPsCProId())) {
            wrapper.eq(StCProLogisticStrategy::getPsCProId, stCProLogisticStrategy.getPsCProId());
        } else {
            wrapper.isNull(StCProLogisticStrategy::getPsCProId);
        }
        if (Objects.nonNull(stCProLogisticStrategy.getCpCPhyWarehouseId())) {
            wrapper.eq(StCProLogisticStrategy::getCpCPhyWarehouseId, stCProLogisticStrategy.getCpCPhyWarehouseId());
        } else {
            wrapper.isNull(StCProLogisticStrategy::getCpCPhyWarehouseId);
        }
        int count = stCProLogisticStrategyMapper.selectCount(wrapper);
        if (count > 0) {
            throw new NDSException("记录已存在，请重新录入！");
        }
    }

    /**
     * 检查数据规则
     *
     * @param stCProLogisticStrategy
     * @return
     */
    private boolean checkData(StCProLogisticStrategy stCProLogisticStrategy) {
        if (Objects.isNull(stCProLogisticStrategy.getCpCLogisticsId())) {
            throw new NDSException("物流公司不能为空！");
        }
        //平台商品ID
        if (StringUtils.isNotEmpty(stCProLogisticStrategy.getNumIid()) &&
                Objects.isNull(stCProLogisticStrategy.getCpCProvinceId()) &&
                Objects.isNull(stCProLogisticStrategy.getCpCCityId()) &&
                Objects.isNull(stCProLogisticStrategy.getCpCAreaId()) &&
                Objects.isNull(stCProLogisticStrategy.getPsCProdimId()) &&
                Objects.isNull(stCProLogisticStrategy.getPsCProId()) &&
                Objects.isNull(stCProLogisticStrategy.getCpCPhyWarehouseId())
        ) {
            return true;
        }
        //商品+省市区+仓
        if (StringUtils.isEmpty(stCProLogisticStrategy.getNumIid()) &&
                Objects.nonNull(stCProLogisticStrategy.getCpCProvinceId()) &&
                Objects.nonNull(stCProLogisticStrategy.getCpCCityId()) &&
                Objects.nonNull(stCProLogisticStrategy.getCpCAreaId()) &&
                Objects.isNull(stCProLogisticStrategy.getPsCProdimId()) &&
                Objects.nonNull(stCProLogisticStrategy.getPsCProId()) &&
                Objects.nonNull(stCProLogisticStrategy.getCpCPhyWarehouseId())
        ) {
            return true;
        }
        //四级+省市区+仓
        if (StringUtils.isEmpty(stCProLogisticStrategy.getNumIid()) &&
                Objects.nonNull(stCProLogisticStrategy.getCpCProvinceId()) &&
                Objects.nonNull(stCProLogisticStrategy.getCpCCityId()) &&
                Objects.nonNull(stCProLogisticStrategy.getCpCAreaId()) &&
                Objects.nonNull(stCProLogisticStrategy.getPsCProdimId()) &&
                Objects.isNull(stCProLogisticStrategy.getPsCProId()) &&
                Objects.nonNull(stCProLogisticStrategy.getCpCPhyWarehouseId())
        ) {
            return true;
        }
        //商品+省市区
        if (StringUtils.isEmpty(stCProLogisticStrategy.getNumIid()) &&
                Objects.nonNull(stCProLogisticStrategy.getCpCProvinceId()) &&
                Objects.nonNull(stCProLogisticStrategy.getCpCCityId()) &&
                Objects.nonNull(stCProLogisticStrategy.getCpCAreaId()) &&
                Objects.isNull(stCProLogisticStrategy.getPsCProdimId()) &&
                Objects.nonNull(stCProLogisticStrategy.getPsCProId()) &&
                Objects.isNull(stCProLogisticStrategy.getCpCPhyWarehouseId())
        ) {
            return true;
        }
        //四级+省市区
        if (StringUtils.isEmpty(stCProLogisticStrategy.getNumIid()) &&
                Objects.nonNull(stCProLogisticStrategy.getCpCProvinceId()) &&
                Objects.nonNull(stCProLogisticStrategy.getCpCCityId()) &&
                Objects.nonNull(stCProLogisticStrategy.getCpCAreaId()) &&
                Objects.nonNull(stCProLogisticStrategy.getPsCProdimId()) &&
                Objects.isNull(stCProLogisticStrategy.getPsCProId()) &&
                Objects.isNull(stCProLogisticStrategy.getCpCPhyWarehouseId())
        ) {
            return true;
        }
        //省+仓+商品
        if (StringUtils.isEmpty(stCProLogisticStrategy.getNumIid()) &&
                Objects.nonNull(stCProLogisticStrategy.getCpCProvinceId()) &&
                Objects.isNull(stCProLogisticStrategy.getCpCCityId()) &&
                Objects.isNull(stCProLogisticStrategy.getCpCAreaId()) &&
                Objects.isNull(stCProLogisticStrategy.getPsCProdimId()) &&
                Objects.nonNull(stCProLogisticStrategy.getPsCProId()) &&
                Objects.nonNull(stCProLogisticStrategy.getCpCPhyWarehouseId())
        ) {
            return true;
        }
        //省+仓+品项（四级）
        if (StringUtils.isEmpty(stCProLogisticStrategy.getNumIid()) &&
                Objects.nonNull(stCProLogisticStrategy.getCpCProvinceId()) &&
                Objects.isNull(stCProLogisticStrategy.getCpCCityId()) &&
                Objects.isNull(stCProLogisticStrategy.getCpCAreaId()) &&
                Objects.nonNull(stCProLogisticStrategy.getPsCProdimId()) &&
                Objects.isNull(stCProLogisticStrategy.getPsCProId()) &&
                Objects.nonNull(stCProLogisticStrategy.getCpCPhyWarehouseId())
        ) {
            return true;
        }
        //省+商品
        if (StringUtils.isEmpty(stCProLogisticStrategy.getNumIid()) &&
                Objects.nonNull(stCProLogisticStrategy.getCpCProvinceId()) &&
                Objects.isNull(stCProLogisticStrategy.getCpCCityId()) &&
                Objects.isNull(stCProLogisticStrategy.getCpCAreaId()) &&
                Objects.isNull(stCProLogisticStrategy.getPsCProdimId()) &&
                Objects.nonNull(stCProLogisticStrategy.getPsCProId()) &&
                Objects.isNull(stCProLogisticStrategy.getCpCPhyWarehouseId())
        ) {
            return true;
        }
        //仓+商品
        if (StringUtils.isEmpty(stCProLogisticStrategy.getNumIid()) &&
                Objects.isNull(stCProLogisticStrategy.getCpCProvinceId()) &&
                Objects.isNull(stCProLogisticStrategy.getCpCCityId()) &&
                Objects.isNull(stCProLogisticStrategy.getCpCAreaId()) &&
                Objects.isNull(stCProLogisticStrategy.getPsCProdimId()) &&
                Objects.nonNull(stCProLogisticStrategy.getPsCProId()) &&
                Objects.nonNull(stCProLogisticStrategy.getCpCPhyWarehouseId())
        ) {
            return true;
        }
        //省+品项（四级）
        if (StringUtils.isEmpty(stCProLogisticStrategy.getNumIid()) &&
                Objects.nonNull(stCProLogisticStrategy.getCpCProvinceId()) &&
                Objects.isNull(stCProLogisticStrategy.getCpCCityId()) &&
                Objects.isNull(stCProLogisticStrategy.getCpCAreaId()) &&
                Objects.nonNull(stCProLogisticStrategy.getPsCProdimId()) &&
                Objects.isNull(stCProLogisticStrategy.getPsCProId()) &&
                Objects.isNull(stCProLogisticStrategy.getCpCPhyWarehouseId())
        ) {
            return true;
        }
        //仓+品项（四级）
        if (StringUtils.isEmpty(stCProLogisticStrategy.getNumIid()) &&
                Objects.isNull(stCProLogisticStrategy.getCpCProvinceId()) &&
                Objects.isNull(stCProLogisticStrategy.getCpCCityId()) &&
                Objects.isNull(stCProLogisticStrategy.getCpCAreaId()) &&
                Objects.nonNull(stCProLogisticStrategy.getPsCProdimId()) &&
                Objects.isNull(stCProLogisticStrategy.getPsCProId()) &&
                Objects.nonNull(stCProLogisticStrategy.getCpCPhyWarehouseId())
        ) {
            return true;
        }
        //仓+省
        if (StringUtils.isEmpty(stCProLogisticStrategy.getNumIid()) &&
                Objects.nonNull(stCProLogisticStrategy.getCpCProvinceId()) &&
                Objects.isNull(stCProLogisticStrategy.getCpCCityId()) &&
                Objects.isNull(stCProLogisticStrategy.getCpCAreaId()) &&
                Objects.isNull(stCProLogisticStrategy.getPsCProdimId()) &&
                Objects.isNull(stCProLogisticStrategy.getPsCProId()) &&
                Objects.nonNull(stCProLogisticStrategy.getCpCPhyWarehouseId())
        ) {
            return true;
        }
        //商品
        if (StringUtils.isEmpty(stCProLogisticStrategy.getNumIid()) &&
                Objects.isNull(stCProLogisticStrategy.getCpCProvinceId()) &&
                Objects.isNull(stCProLogisticStrategy.getCpCCityId()) &&
                Objects.isNull(stCProLogisticStrategy.getCpCAreaId()) &&
                Objects.isNull(stCProLogisticStrategy.getPsCProdimId()) &&
                Objects.nonNull(stCProLogisticStrategy.getPsCProId()) &&
                Objects.isNull(stCProLogisticStrategy.getCpCPhyWarehouseId())
        ) {
            return true;
        }
        //品项（四级）
        if (StringUtils.isEmpty(stCProLogisticStrategy.getNumIid()) &&
                Objects.isNull(stCProLogisticStrategy.getCpCProvinceId()) &&
                Objects.isNull(stCProLogisticStrategy.getCpCCityId()) &&
                Objects.isNull(stCProLogisticStrategy.getCpCAreaId()) &&
                Objects.nonNull(stCProLogisticStrategy.getPsCProdimId()) &&
                Objects.isNull(stCProLogisticStrategy.getPsCProId()) &&
                Objects.isNull(stCProLogisticStrategy.getCpCPhyWarehouseId())
        ) {
            return true;
        }
        //省
        if (StringUtils.isEmpty(stCProLogisticStrategy.getNumIid()) &&
                Objects.nonNull(stCProLogisticStrategy.getCpCProvinceId()) &&
                Objects.isNull(stCProLogisticStrategy.getCpCCityId()) &&
                Objects.isNull(stCProLogisticStrategy.getCpCAreaId()) &&
                Objects.isNull(stCProLogisticStrategy.getPsCProdimId()) &&
                Objects.isNull(stCProLogisticStrategy.getPsCProId()) &&
                Objects.isNull(stCProLogisticStrategy.getCpCPhyWarehouseId())
        ) {
            return true;
        }
        //仓
        if (StringUtils.isEmpty(stCProLogisticStrategy.getNumIid()) &&
                Objects.isNull(stCProLogisticStrategy.getCpCProvinceId()) &&
                Objects.isNull(stCProLogisticStrategy.getCpCCityId()) &&
                Objects.isNull(stCProLogisticStrategy.getCpCAreaId()) &&
                Objects.isNull(stCProLogisticStrategy.getPsCProdimId()) &&
                Objects.isNull(stCProLogisticStrategy.getPsCProId()) &&
                Objects.nonNull(stCProLogisticStrategy.getCpCPhyWarehouseId())
        ) {
            return true;
        }
        return false;
    }

    private void checkUpdateParam(StCProLogisticStrategy request, Long id, List<String> nullKeyList) {
        if (Objects.isNull(request)) {
            throw new NDSException("入参不能为空！");
        }
        StCProLogisticStrategy exitStCProLogisticStrategy = stCProLogisticStrategyMapper.selectById(id);
        if (Objects.isNull(exitStCProLogisticStrategy)) {
            throw new NDSException("当前记录不存在！");
        }
        buildParam(request, nullKeyList, exitStCProLogisticStrategy);
        boolean flag = checkData(request);
        if (!flag) {
            throw new NDSException("规则不正确，请检查!");
        }
        if (request.getCpCLogisticsId() != null) {
            LambdaQueryWrapper<StCProLogisticStrategy> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(StCProLogisticStrategy::getCpCLogisticsId, request.getCpCLogisticsId());
            wrapper.ne(StCProLogisticStrategy::getId, id);
            if (Objects.nonNull(exitStCProLogisticStrategy.getCpCProvinceId())) {
                wrapper.eq(StCProLogisticStrategy::getCpCProvinceId, exitStCProLogisticStrategy.getCpCProvinceId());
            } else {
                wrapper.isNull(StCProLogisticStrategy::getCpCProvinceId);
            }
            if (Objects.nonNull(exitStCProLogisticStrategy.getCpCCityId())) {
                wrapper.eq(StCProLogisticStrategy::getCpCCityId, exitStCProLogisticStrategy.getCpCCityId());
            } else {
                wrapper.isNull(StCProLogisticStrategy::getCpCCityId);
            }
            if (Objects.nonNull(exitStCProLogisticStrategy.getCpCAreaId())) {
                wrapper.eq(StCProLogisticStrategy::getCpCAreaId, exitStCProLogisticStrategy.getCpCAreaId());
            } else {
                wrapper.isNull(StCProLogisticStrategy::getCpCAreaId);
            }
            if (StringUtils.isNotEmpty(exitStCProLogisticStrategy.getNumIid())) {
                wrapper.eq(StCProLogisticStrategy::getNumIid, exitStCProLogisticStrategy.getNumIid());
            } else {
                wrapper.isNull(StCProLogisticStrategy::getNumIid);
            }
            if (Objects.nonNull(exitStCProLogisticStrategy.getPsCProdimId())) {
                wrapper.eq(StCProLogisticStrategy::getPsCProdimId, exitStCProLogisticStrategy.getPsCProdimId());
            } else {
                wrapper.isNull(StCProLogisticStrategy::getPsCProdimId);
            }
            if (Objects.nonNull(exitStCProLogisticStrategy.getPsCProId())) {
                wrapper.eq(StCProLogisticStrategy::getPsCProId, exitStCProLogisticStrategy.getPsCProId());
            } else {
                wrapper.isNull(StCProLogisticStrategy::getPsCProId);
            }
            if (Objects.nonNull(exitStCProLogisticStrategy.getCpCPhyWarehouseId())) {
                wrapper.eq(StCProLogisticStrategy::getCpCPhyWarehouseId, exitStCProLogisticStrategy.getCpCPhyWarehouseId());
            } else {
                wrapper.isNull(StCProLogisticStrategy::getCpCPhyWarehouseId);
            }
            int count = stCProLogisticStrategyMapper.selectCount(wrapper);
            if (count > 0) {
                throw new NDSException("记录已存在，请重新录入！");
            }
        }
    }

    /**
     * 更新时补充未变更字段内容
     *
     * @param request
     * @param nullKeyList
     * @param exitStCProLogisticStrategy
     */
    private void buildParam(StCProLogisticStrategy request, List<String> nullKeyList, StCProLogisticStrategy exitStCProLogisticStrategy) {
        if (Objects.isNull(request.getCpCProvinceId()) &&
                Objects.nonNull(exitStCProLogisticStrategy.getCpCProvinceId()) && !nullKeyList.contains("CP_C_PROVINCE_ID")) {
            request.setCpCProvinceId(exitStCProLogisticStrategy.getCpCProvinceId());
        }
        if (Objects.isNull(request.getCpCCityId()) &&
                Objects.nonNull(exitStCProLogisticStrategy.getCpCCityId()) && !nullKeyList.contains("CP_C_CITY_ID")) {
            request.setCpCCityId(exitStCProLogisticStrategy.getCpCCityId());
        }
        if (Objects.isNull(request.getCpCAreaId()) &&
                Objects.nonNull(exitStCProLogisticStrategy.getCpCAreaId()) && !nullKeyList.contains("CP_C_AREA_ID")) {
            request.setCpCAreaId(exitStCProLogisticStrategy.getCpCAreaId());
        }
        if (StringUtils.isNotEmpty(request.getNumIid()) &&
                StringUtils.isNotEmpty(exitStCProLogisticStrategy.getNumIid()) && !nullKeyList.contains("NUM_IID")) {
            request.setNumIid(exitStCProLogisticStrategy.getNumIid());
        }
        if (Objects.isNull(request.getPsCProdimId()) &&
                Objects.nonNull(exitStCProLogisticStrategy.getPsCProdimId()) && !nullKeyList.contains("PS_C_PRODIM_ID")) {
            request.setPsCProdimId(exitStCProLogisticStrategy.getPsCProdimId());
        }
        if (Objects.isNull(request.getPsCProId()) &&
                Objects.nonNull(exitStCProLogisticStrategy.getPsCProId()) && !nullKeyList.contains("PS_C_PRO_ID")) {
            request.setPsCProId(exitStCProLogisticStrategy.getPsCProId());
            request.setPsCProEcode(exitStCProLogisticStrategy.getPsCProEcode());
            request.setPsCProEname(exitStCProLogisticStrategy.getPsCProEname());
        }
        if (Objects.isNull(request.getCpCPhyWarehouseId()) &&
                Objects.nonNull(exitStCProLogisticStrategy.getCpCPhyWarehouseId()) && !nullKeyList.contains("CP_C_PHY_WAREHOUSE_ID")) {
            request.setCpCPhyWarehouseId(exitStCProLogisticStrategy.getCpCPhyWarehouseId());
            request.setCpCPhyWarehouseEcode(exitStCProLogisticStrategy.getCpCPhyWarehouseEcode());
            request.setCpCPhyWarehouseEname(exitStCProLogisticStrategy.getCpCPhyWarehouseEname());
        }
        if (Objects.isNull(request.getCpCLogisticsId()) && !nullKeyList.contains("CP_C_LOGISTICS_ID")) {
            request.setCpCLogisticsId(exitStCProLogisticStrategy.getCpCLogisticsId());
            request.setCpCLogisticsEcode(exitStCProLogisticStrategy.getCpCLogisticsEcode());
            request.setCpCLogisticsEname(exitStCProLogisticStrategy.getCpCLogisticsEname());
        }
    }

    /**
     * 更新商品物流设置
     *
     * @param session
     * @param id
     * @param request
     * @param nullKeyList
     * @return
     */
    private ValueHolder updateStCProLogisticStrategy(QuerySession session, Long id, StCProLogisticStrategyRequest request, List<String> nullKeyList) {
        StCProLogisticStrategy stCProLogisticStrategy = request.getStCProLogisticStrategy();

        if (stCProLogisticStrategy != null) {
            //校验数据
            checkUpdateParam(stCProLogisticStrategy, id, nullKeyList);
            stCProLogisticStrategy.setId(id);
            //构建关联表数据
            bulidOtherData(stCProLogisticStrategy);
            StBeanUtils.makeModifierField(stCProLogisticStrategy, session.getUser());
            if (stCProLogisticStrategyMapper.updateById(stCProLogisticStrategy) < 0) {
                return ValueHolderUtils.getFailValueHolder("更新失败！");
            }
        } else {
            return ValueHolderUtils.getFailValueHolder("请求参数不能为空");
        }
        return ValueHolderUtils.getSuccessValueHolder(id, StConstant.ST_C_PRO_LOGISTIC_STRATEGY, "");
    }

    /**
     * 新增商品物流设置
     *
     * @param session
     * @param request
     * @param nullKeyList
     * @return
     */
    private ValueHolder insertStCProLogisticStrategy(QuerySession session, StCProLogisticStrategyRequest request, List<String> nullKeyList) {

        long id = 0;
        StCProLogisticStrategy stCProLogisticStrategy = request.getStCProLogisticStrategy();

        if (stCProLogisticStrategy != null) {
            //校验数据
            checkInsertParam(request);
            id = ModelUtil.getSequence(StConstant.ST_C_PRO_LOGISTIC_STRATEGY);
            stCProLogisticStrategy.setId(id);
            //构建关联表数据
            bulidOtherData(stCProLogisticStrategy);
            StBeanUtils.makeCreateField(stCProLogisticStrategy, session.getUser());
            if (stCProLogisticStrategyMapper.insert(stCProLogisticStrategy) < 0) {
                return ValueHolderUtils.getFailValueHolder("新增失败！");
            }
        }
        return ValueHolderUtils.getSuccessValueHolder(id, StConstant.ST_C_PRO_LOGISTIC_STRATEGY, "");
    }

    /**
     * 构建其他数据
     *
     * @param stCProLogisticStrategy
     */
    private void bulidOtherData(StCProLogisticStrategy stCProLogisticStrategy) {
        Long psCProId = stCProLogisticStrategy.getPsCProId();
        Long cpCLogisticsId = stCProLogisticStrategy.getCpCLogisticsId();
        Long cpCPhyWarehouseId = stCProLogisticStrategy.getCpCPhyWarehouseId();
        //保存商品信息
        if (Objects.nonNull(psCProId)) {
            PsCPro psCPro = rpcPsService.queryProByID(psCProId);
            if (Objects.isNull(psCPro)) {
                throw new NDSException("商品不存在！");
            }
            stCProLogisticStrategy.setPsCProEcode(psCPro.getEcode());
            stCProLogisticStrategy.setPsCProEname(psCPro.getEname());
        }
        //保存物流公司档案
        if (Objects.nonNull(cpCLogisticsId)) {
            ValueHolderV14<Map<Long, CpLogistics>> mapValueHolderV14 = cpLogisticsQueryCmd.queryLogisticsByIds(Lists.newArrayList(cpCLogisticsId));
            if (!mapValueHolderV14.isOK()) {
                throw new NDSException("物流公司档案不存在！");
            }
            mapValueHolderV14.getData().forEach((k, v) -> {
                stCProLogisticStrategy.setCpCLogisticsId(cpCLogisticsId);
                stCProLogisticStrategy.setLogisticType(v.getType());
                stCProLogisticStrategy.setCpCLogisticsEcode(v.getEcode());
                stCProLogisticStrategy.setCpCLogisticsEname(v.getEname());
            });
        }

        //保存实体仓档案
        if(Objects.nonNull(cpCPhyWarehouseId)){
            CpCPhyWarehouse cpCPhyWarehouse = cpPhyWarehouseSelectCmd.queryWarehouseById(cpCPhyWarehouseId);
            if(Objects.isNull(cpCPhyWarehouse)){
                throw new NDSException("实体仓档案不存在！");
            }
            stCProLogisticStrategy.setCpCPhyWarehouseEcode(cpCPhyWarehouse.getEcode());
            stCProLogisticStrategy.setCpCPhyWarehouseEname(cpCPhyWarehouse.getEname());
        }

    }

}
