package com.jackrain.nea.st.services;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.redis.util.RedisOpsUtil;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.st.mapper.BusinessSystemParametersLogMapper;
import com.jackrain.nea.st.mapper.BusinessSystemParametersSaveMapper;
import com.jackrain.nea.st.model.table.BusinessSystemParametersLogDO;
import com.jackrain.nea.st.model.table.CpCBusinessSystemParametersDO;
import com.jackrain.nea.st.rpc.RpcPsService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;

/**
 * @description: 业务系统参数
 */
@Component
@Slf4j
public class BusinessSystemParametersService {

    @Autowired
    BusinessSystemParametersLogMapper businessSystemParametersLogMapper;

    @Autowired
    private BusinessSystemParametersSaveMapper businessSystemParametersSaveMapper;

    @Autowired
    private RedisOpsUtil<String, String> redisUtil;

    @Autowired
    private RpcPsService rpcPsService;

    public ValueHolderV14<Boolean> resetBusinessSystemParametersByParamCode(String paramCode, String newParamValue) {
        ValueHolderV14<Boolean> v14 = new ValueHolderV14<>(ResultCode.FAIL, "重置业务系统参数失败");
        if (StringUtils.isBlank(paramCode)) {
            v14.setMessage("重置业务系统参数-参数key不能为空");
            return v14;
        }
        if (StringUtils.isBlank(newParamValue)) {
            v14.setMessage("重置业务系统参数-参数值不能为空");
            return v14;
        }
        QueryWrapper<CpCBusinessSystemParametersDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(CpCBusinessSystemParametersDO::getParamCode, paramCode);
        CpCBusinessSystemParametersDO cpCBusinessSystemParametersDO
                = businessSystemParametersSaveMapper.selectList(queryWrapper).stream().findFirst().orElse(null);
        if (cpCBusinessSystemParametersDO != null) {
            Long id = cpCBusinessSystemParametersDO.getId();
            CpCBusinessSystemParametersDO update = new CpCBusinessSystemParametersDO();
            update.setId(id);
            update.setParamValue(newParamValue);
            update.setModifieddate(new Date());
            businessSystemParametersSaveMapper.updateById(update);
            v14.setCode(ResultCode.SUCCESS);
            v14.setMessage("重置业务系统参数成功");
            insertBusinessSystemParametersLog(id, cpCBusinessSystemParametersDO.getParamValue(), newParamValue,
                    SystemUserResource.getRootUser());
            //更新redis
            redisUtil.strRedisTemplate.opsForValue().set("business_system:" + cpCBusinessSystemParametersDO.getParamCode(),
                    newParamValue);
            log.info(LogUtil.format("更新redis：{}"), paramCode);
        } else {
            v14.setMessage("重置业务系统参数-参数key不存在");
            return v14;
        }
        return v14;
    }

    private void insertBusinessSystemParametersLog(Long id, String beforeValue, String afterValue, User user) {
        BusinessSystemParametersLogDO insertEntry = new BusinessSystemParametersLogDO();
        insertEntry.setBeforeModification(beforeValue);
        insertEntry.setAfterModification(afterValue);
        insertEntry.setModcontent("PARAM_VALUE");
        insertEntry.setModifieddate(new Date());
        insertEntry.setModifierid(Long.valueOf(user.getId()));
        insertEntry.setModifiername(user.getEname());
        insertEntry.setOutId(id);
        insertEntry.setIsactive("Y");
        insertEntry.setAdClientId(37L);
        insertEntry.setAdOrgId(27L);
        businessSystemParametersLogMapper.insert(insertEntry);
    }


}

