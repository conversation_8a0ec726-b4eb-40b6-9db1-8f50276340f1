package com.jackrain.nea.st.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.base.Throwables;
import com.google.common.collect.Lists;
import com.jackrain.nea.st.common.StConstant;
import com.jackrain.nea.st.mapper.StCMsgMapper;
import com.jackrain.nea.st.model.common.StCMsgConstants;
import com.jackrain.nea.st.model.table.StcMsgDO;
import com.jackrain.nea.st.utils.JsonUtils;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import com.jackrain.nea.web.query.QueryUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import java.util.List;
import java.util.stream.Collectors;

/**
 * @Descroption
 * <AUTHOR>
 * @Date 2020/8/28
 */
@Component
@Slf4j
public class StCMsgStrategyQueryService {
    @Autowired
    private StCMsgMapper stCMsgMapper;

    public ValueHolder queryList(QuerySession querySession) {
        ValueHolder vh = new ValueHolder();
        DefaultWebEvent event = querySession.getEvent();
        JSONObject param = JSON.parseObject(event.getParameterValue("param").toString());
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("msgqueryList=") + param.toJSONString());
        }
        JSONObject fixedcolumns = param.getJSONObject("fixedcolumns");
        int range = param.getInteger("range") == null ? QueryUtils.getdefalutrange() : param.getInteger("range");
        Integer startIndex = param.getInteger("startindex") == null ? 0 : param.getInteger("startindex");
        String msgName = fixedcolumns.getString("MSG_NAME");
        JSONArray cShopIds = fixedcolumns.getJSONArray("CP_C_SHOP_ID");
        String shopId = cShopIds == null || cShopIds.size() == 0 ? null : String.join(",", JSONObject.parseArray(cShopIds.toJSONString(), String.class));
        JSONArray adviceTypes = fixedcolumns.getJSONArray("ADVICE_TYPE");
        JSONArray taskNodes = fixedcolumns.getJSONArray("TASK_NODE");
        JSONArray isSends = fixedcolumns.getJSONArray("IS_SEND");
        JSONArray isactives = fixedcolumns.getJSONArray("ISACTIVE");

        try {
            List<String> taskNodeParse = splitFramePrefixByStr(taskNodes);
            List<String> adviceTypesParse = splitFramePrefixByStr(adviceTypes);
            List<String> isSend = splitFramePrefixByStr(isSends);
            List<String> isactive = splitFramePrefixByStr(isactives);
            int pageNum = 1;
            if (startIndex>=range){
                pageNum = (startIndex/range)+1;
            }
            PageHelper.startPage(pageNum, range,"id DESC");
            List<StcMsgDO> stcMsgDOS = stCMsgMapper.selectPageList(shopId, msgName,isSend,isactive, adviceTypesParse, taskNodeParse);
            PageInfo<StcMsgDO> pageInfo = new PageInfo<>(stcMsgDOS);

            /*
            可视化处理
             */
            List<StcMsgDO> collect = stcMsgDOS.stream().map(StCMsgStrategyQueryService::exChangeOutPutField).collect(Collectors.toList());
            JSONArray jsonArray = (JSONArray) JSONArray.toJSON(collect);
            List<JSONObject> jsonObjectList = JSONObject.parseArray(
                    JSONObject.toJSONString(jsonArray, SerializerFeature.WriteMapNullValue), JSONObject.class);
            JSONArray getFrameDataFormat = JsonUtils.getFrameDataFormat(jsonObjectList);

            JSONObject resultData = new JSONObject();
            resultData.put("start", startIndex);
            resultData.put("rowCount", range);
            resultData.put("row", getFrameDataFormat);
            resultData.put("totalRowCount", pageInfo.getTotal());
            vh.put("data", resultData);
            vh.put("code", 0);
            vh.put("message", "success");
            return vh;

        } catch (Exception e) {
            log.error(LogUtil.format("短信策略查询异常：{}"), Throwables.getStackTraceAsString(e));
            JSONObject resultData = new JSONObject();
            resultData.put("start", startIndex);
            resultData.put("row", "");
            resultData.put("totalRowCount", 0);
            vh.put("data", resultData);
            vh.put("code", 0);
            vh.put("message", "系统异常");
            vh.put("error", e.getMessage());
            return vh;
        }
    }

    public ValueHolderV14<String> querySmsContent(Long id) {
        ValueHolderV14<String> result = new ValueHolderV14<>();
        StcMsgDO stcMsgDO = stCMsgMapper.selectById(id);
        if (null == stcMsgDO) {
            result.setCode(-1);
            result.setMessage("没有数据");
            return result;
        }
        result.setData(stcMsgDO.getTemplateContent());
        return result;
    }

    public ValueHolderV14<StcMsgDO> queryMsgInfo(Long shopId, String taskNode, String advicType) {
        ValueHolderV14<StcMsgDO> vh = new ValueHolderV14<>();
        if (taskNode == null) {
            vh.setCode(-1);

            vh.setMessage("任务节点不能为空！");
            log.debug(LogUtil.format("任务节点不能为空！"));
            return vh;
        }
        if (advicType == null) {
            vh.setCode(-1);
            vh.setMessage("通知类型不能为空！");
            log.debug(LogUtil.format("通知类型不能为空！"));
            return vh;
        }
        if (!StConstant.MSG_TASK_NODE_04.equals(taskNode) && shopId == null) {
            vh.setCode(-1);
            vh.setMessage("非无名件完成入库必须选择店铺！");
            log.debug(LogUtil.format("非无名件完成入库必须选择店铺！"));
            return vh;
        }

        List<StcMsgDO> stcMsgDOS = stCMsgMapper.selectByShopId(shopId, null, advicType, taskNode);
        if (stcMsgDOS.size() == 0) {
            vh.setCode(-2);
            vh.setMessage("没有获取到短信策略！");
            log.debug(LogUtil.format("没有对应的短信策略：shopId/adviceType/taskNode=", shopId, advicType, taskNode));
            return vh;
        }
        if (stcMsgDOS.size() > 1) {
            vh.setCode(-3);
            vh.setMessage("获取到多条短信策略配置,无法识别！");
            log.debug(LogUtil.format("获取到多条短信策略配置,无法识别：shopId/adviceType/taskNode=", shopId, advicType, taskNode));
            return vh;
        }
        StcMsgDO stcMsgDO = stcMsgDOS.get(0);
        String templateContent = stcMsgDO.getTemplateContent();
        if (StringUtils.isEmpty(templateContent)) {
            vh.setCode(-2);
            vh.setMessage("记录中未配置短信信息！");
            log.debug(LogUtil.format("记录中未配置短信信息：shopId/adviceType/taskNode=", shopId, advicType, taskNode));
            return vh;
        }
        vh.setData(stcMsgDO);
        return vh;
    }

    /**
     * 转换可视化
     *
     * @param stcMsgDO 短信策略
     */
    private static StcMsgDO exChangeOutPutField(StcMsgDO stcMsgDO) {
        Assert.notNull(stcMsgDO, "exChangeOutPutField-短信策略-实体类为空");
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("stcMsgDO转换可视化:") + JSONObject.toJSONString(stcMsgDO));
        }
        String adviceType = stcMsgDO.getAdviceType();
        String taskNode = stcMsgDO.getTaskNode();
        String isSend = stcMsgDO.getIsSend();
        String isactive = stcMsgDO.getIsactive();
        stcMsgDO.setAdviceType(StCMsgConstants.ADVICEMAP.get(adviceType));
        stcMsgDO.setTaskNode(StCMsgConstants.TASKMAP.get(taskNode));
        stcMsgDO.setIsSend(StCMsgConstants.SENDMAP.get(isSend));
        stcMsgDO.setIsactive(StCMsgConstants.SENDMAP.get(isactive));
        return stcMsgDO;
    }

    /**
     * 去除框架下拉栏数据中的前缀"="
     */
    private static List<String> splitFramePrefixByStr(JSONArray jsonArray) {
        List<String> list = Lists.newArrayList();
        if (jsonArray != null && jsonArray.size() != 0) {
            List<String> strings = JSONObject.parseArray(jsonArray.toJSONString(), String.class);
            list = strings.stream().map(item->item.substring(1)).collect(Collectors.toList());
        }
        return list;
    }
}
