package com.jackrain.nea.st.services;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.st.common.StConstant;
import com.jackrain.nea.st.mapper.StCSplitReasonItemMapper;
import com.jackrain.nea.st.mapper.StCSplitReasonMapper;
import com.jackrain.nea.st.model.request.StCSplitReasonRequest;
import com.jackrain.nea.st.model.table.StCSplitReasonDO;
import com.jackrain.nea.st.model.table.StCSplitReasonItemDO;
import com.jackrain.nea.st.utils.JsonUtils;
import com.jackrain.nea.st.utils.StBeanUtils;
import com.jackrain.nea.st.utils.ValueHolderUtils;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

/**
 * @program: r3-st
 * @description: 自定义拆单保存业务类
 * @author: liuwj
 * @create: 2021-05-31 15:33
 **/
@Component
@Slf4j
@Transactional
public class StCSplitReasonSaveService  extends CommandAdapter {
    @Autowired
    StCSplitReasonMapper stCSplitReasonMapper;

    @Autowired
    StCSplitReasonItemMapper stCSplitReasonItemMapper;

    /**
     * <AUTHOR>
     * @Date 15:37 2021/5/31
     * @Description 保存业务
     */
    @Override
    public ValueHolder execute(QuerySession session) {
        DefaultWebEvent event = session.getEvent();
        List<String> nullKeyList = new ArrayList<>();
        JSONObject param = JsonUtils.filterNull((JSONObject) event.getParameterValue("param"), nullKeyList);
        if (param != null) {
            Long id = param.getLong("objid");
            JSONObject fixColumn = param.getJSONObject("fixcolumn");
            StCSplitReasonRequest stCSplitReasonRequest = JsonUtils.jsonParseClass(fixColumn, StCSplitReasonRequest.class);
            if (fixColumn != null && id != null) {
                if (id != -1) {
                    return updateSplitReason(session, id, stCSplitReasonRequest);
                } else {
                    return insertSplitReason(session, stCSplitReasonRequest);
                }
            }
        }
        throw new NDSException("当前记录已不存在！");
    }
    /**
     * <AUTHOR>
     * @Date 15:38 2021/5/31
     * @Description 自定义拆单新增
     */
    private ValueHolder insertSplitReason(QuerySession session, StCSplitReasonRequest stCSplitReasonRequest) {
        StCSplitReasonDO stCSplitReasonDO = stCSplitReasonRequest.getStCSplitReasonDO();
        if (stCSplitReasonDO == null){
            return ValueHolderUtils.getFailValueHolder("当前记录已不存在！");
        }
        if(log.isDebugEnabled()){
            log.info(getClass().getName()+"自定义拆单新增 param ：{}",stCSplitReasonRequest);
        }
        Long splitReasonId = ModelUtil.getSequence(StConstant.TAB_ST_C_SPLIT_REASON);
        stCSplitReasonDO.setId(splitReasonId);
        ValueHolder holder = checkSplitReason(stCSplitReasonDO);
        if (holder != null) {
            return holder;
        }
        if (stCSplitReasonDO != null ){
            StBeanUtils.makeCreateField(stCSplitReasonDO, session.getUser());
            if (stCSplitReasonMapper.insert(stCSplitReasonDO)<=0){
                throw new NDSException("新增失败！");
            }
        }
        //新增明细
        List<StCSplitReasonItemDO> stCSplitReasonItemDOList = stCSplitReasonRequest.getStCSplitReasonItemDOList();
        if (CollectionUtils.isNotEmpty(stCSplitReasonItemDOList)){
            log.info(LogUtil.format("主表新增完毕开始保存明细"));
            saveSplitReasonItemDO(session,splitReasonId,stCSplitReasonItemDOList);
        }
        return ValueHolderUtils.getSuccessValueHolder(splitReasonId, StConstant.TAB_ST_C_SPLIT_REASON, "");
    }

    /**
     * <AUTHOR>
     * @Date 16:11 2021/5/31
     * @Description 保存明细
     */
    private void saveSplitReasonItemDO(QuerySession session, Long splitReasonId, List<StCSplitReasonItemDO> stCSplitReasonItemDOList) {
        if(log.isDebugEnabled()){
            log.info(getClass().getName()+"自定义拆单明细保存 param ：{}",stCSplitReasonItemDOList);
        }
        for (StCSplitReasonItemDO stCSplitReasonItemDO : stCSplitReasonItemDOList) {
            checkSplitReasonItem(stCSplitReasonItemDO);
            if (stCSplitReasonItemDO.getId() != null && stCSplitReasonItemDO.getId() > 0) {
                //明细修改
                StBeanUtils.makeModifierField(stCSplitReasonItemDO, session.getUser());//修改信息
                if ((stCSplitReasonItemMapper.updateById(stCSplitReasonItemDO)) <= 0) {
                    throw new NDSException("自定义拆单-明细修改失败！");
                }
            } else {
                //明细创建
                stCSplitReasonItemDO.setSplitReasonId(splitReasonId);
                stCSplitReasonItemDO.setId(ModelUtil.getSequence(StConstant.TAB_ST_C_SPLIT_REASON_ITEM));
                StBeanUtils.makeCreateField(stCSplitReasonItemDO, session.getUser());//创建信息
                stCSplitReasonItemDO.setOwnerename(session.getUser().getEname());//创建人账号
                if ((stCSplitReasonItemMapper.insert(stCSplitReasonItemDO)) <= 0) {
                    throw new NDSException("自定义拆单-明细保存失败！");
                }
            }
        }
    }
    /**
     * <AUTHOR>
     * @Date 16:12 2021/5/31
     * @Description 自定义拆单明细校验
     */
    private void checkSplitReasonItem(StCSplitReasonItemDO stCSplitReasonItemDO) {
        if(log.isDebugEnabled()){
            log.info(getClass().getName()+"自定义拆单明细校验 param ：{}",stCSplitReasonItemDO);
        }
        if (stCSplitReasonItemDO == null ){
            throw new NDSException("当前记录已不存在！");
        }
        if (stCSplitReasonItemDO.getSystemSplitReason() != null){
            if (stCSplitReasonItemMapper.selectCount(new QueryWrapper<StCSplitReasonItemDO>().lambda()
                    .eq(StCSplitReasonItemDO ::getSystemSplitReason,stCSplitReasonItemDO.getSystemSplitReason()))>0){
                throw new NDSException("该系统拆单原因已存在或被其他自定义拆单原因引用！");
            }
        }
    }

    /**
     * <AUTHOR>
     * @Date 15:57 2021/5/31
     * @Description 自定义拆单校验
     */
    private ValueHolder checkSplitReason(StCSplitReasonDO stCSplitReasonDO) {
        if (stCSplitReasonDO == null ){
            return  null;
        }
        if(log.isDebugEnabled()){
            log.info(getClass().getName()+"自定义拆单校验 param ：{}",stCSplitReasonDO);
        }
        if (stCSplitReasonDO.getEcode() !=null){
            if (stCSplitReasonMapper.selectCount(new QueryWrapper<StCSplitReasonDO>()
                    .lambda().eq(StCSplitReasonDO :: getEcode,stCSplitReasonDO.getEcode())
                    .eq(StCSplitReasonDO :: getIsactive,"Y"))>0){
                return ValueHolderUtils.getFailValueHolder("编码已存在，不允许重复新增！");
            }
        }
        if (stCSplitReasonDO.getSplitReasonConfigId() !=null){
            if (stCSplitReasonMapper.selectCount(new QueryWrapper<StCSplitReasonDO>()
                    .lambda().eq(StCSplitReasonDO :: getSplitReasonConfigId,stCSplitReasonDO.getSplitReasonConfigId())
                    .eq(StCSplitReasonDO :: getIsactive,"Y"))>0){
                return ValueHolderUtils.getFailValueHolder("该自定义拆单原因已存在，不允许新增！");
            }
        }
        return  null;
    }

    /**
     * <AUTHOR>
     * @Date 15:38 2021/5/31
     * @Description 自定义拆单修改
     */
    private ValueHolder updateSplitReason(QuerySession session, Long id, StCSplitReasonRequest stCSplitReasonRequest) {
        StCSplitReasonDO stCSplitReasonDO = stCSplitReasonRequest.getStCSplitReasonDO();
        if (stCSplitReasonMapper.selectById(id) == null){
            return ValueHolderUtils.getFailValueHolder("当前记录已不存在！");
        }
        if(log.isDebugEnabled()){
            log.info(getClass().getName()+"自定义拆单配置修改 param ：{}",stCSplitReasonRequest);
        }
        ValueHolder holder = checkSplitReason(stCSplitReasonDO);
        if (holder != null) {
            return holder;
        }
        if (stCSplitReasonDO != null ){
            stCSplitReasonDO.setId(id);
            StBeanUtils.makeModifierField(stCSplitReasonDO, session.getUser());
            if (stCSplitReasonMapper.updateById(stCSplitReasonDO)<=0){
                throw new NDSException("保存失败！");
            }
        }
        //新增明细
        List<StCSplitReasonItemDO> stCSplitReasonItemDOList = stCSplitReasonRequest.getStCSplitReasonItemDOList();
        if (CollectionUtils.isNotEmpty(stCSplitReasonItemDOList)){
            log.info(LogUtil.format("主表新增完毕开始保存明细"));
            saveSplitReasonItemDO(session,id,stCSplitReasonItemDOList);
        }
        return ValueHolderUtils.getSuccessValueHolder(id, StConstant.TAB_ST_C_SPLIT_REASON, "");
    }
}
