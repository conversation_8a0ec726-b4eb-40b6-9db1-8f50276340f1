package com.jackrain.nea.st.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.st.common.StConstant;
import com.jackrain.nea.st.mapper.StCPostfeeMapper;
import com.jackrain.nea.st.model.table.StCPostfeeDO;
import com.jackrain.nea.st.utils.StBeanUtils;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;

/**
 * 运费方案-反审逻辑
 *
 * <AUTHOR>
 * @Date 2019/3/8 09:51
 */
@Component
@Slf4j
@Transactional
public class PostfeeCancelService extends CommandAdapter {
    @Autowired
    private StCPostfeeMapper stCPostfeeMapper;

    @Override
    public ValueHolder execute(QuerySession querySession) throws NDSException {
        DefaultWebEvent event = querySession.getEvent();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(
                event.getParameterValue("param"), "yyyy-MM-dd HH:mm:ss"),
                Feature.OrderedField);
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("PostfeeCancelService.param=") + param.toJSONString());
        }
        JSONArray itemArray = StBeanUtils.makeAuditJsonArray(param);
        JSONArray errorArray = new JSONArray();
        HashMap<Long, Object> errMap = new HashMap();
        if (itemArray.size() > 0) {
            for (int i = 0; i < itemArray.size(); i++) {
                Long itemid = itemArray.getLong(i);
                try {
                    savePostfeeByID(querySession, itemid, errorArray);
                } catch (Exception e) {
                    errMap.put(itemid, e.getMessage());
                }
            }
        }
        return StBeanUtils.getExcuteValueHolder(itemArray.size(), errMap);
    }

    private void savePostfeeByID(QuerySession session, Long id, JSONArray errorArray) {
        StCPostfeeDO stCPostfeeDO = stCPostfeeMapper.selectById(id);
        JSONArray tempArray = new JSONArray();
        checkPostfeeStatus(stCPostfeeDO, id, tempArray);
        if (tempArray.size() > 0) {
            errorArray.addAll(tempArray);
            return;
        }
        JSONObject jsonObject = new JSONObject();
        StBeanUtils.makeModifierField(jsonObject, session.getUser());//修改信息
        jsonObject.put("ID", id);
        jsonObject.put("BILL_STATUS", StConstant.CON_BILL_STATUS_01);//更改状态为反审
        StBeanUtils.makeCancelField(jsonObject);//反审-审核置空信息

        if ((stCPostfeeMapper.updateAtrributes(jsonObject)) <= 0) {
            throw new NDSException("方案:" + stCPostfeeDO.getEname() + ",反审失败！");
        }
    }

    private void checkPostfeeStatus(StCPostfeeDO stCPostfeeDO, Long id, JSONArray errorArray) {
        if (stCPostfeeDO == null) {
            throw new NDSException("当前记录已不存在！");
        }
        //不是已审核，不允许反审核
        if (stCPostfeeDO.getBillStatus() == null
                || !StConstant.CON_BILL_STATUS_02.equals(stCPostfeeDO.getBillStatus())) {
            throw new NDSException("当前记录不是已审核，不允许反审！");
        }
    }
}
