package com.jackrain.nea.st.services;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.st.common.StConstant;
import com.jackrain.nea.st.mapper.*;
import com.jackrain.nea.st.model.request.StCPreSaleRequest;
import com.jackrain.nea.st.model.table.*;
import com.jackrain.nea.st.rpc.RpcCpService;
import com.jackrain.nea.st.rpc.RpcPsService;
import com.jackrain.nea.st.utils.JsonUtils;
import com.jackrain.nea.st.utils.StBeanUtils;
import com.jackrain.nea.st.utils.ValueHolderUtils;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Description 预售解析策略方案保存
 * <AUTHOR>
 * @Date 2020/06/11 22:00:00
 */
@Component
@Slf4j
@Transactional
public class StCPreSaleSaveService extends CommandAdapter {
    @Autowired
    private StCPreSaleMapper stCPreSaleMapper;
    @Autowired
    private StCPreSaleItemMapper stCPreSaleItemMapper;
    @Autowired
    private RpcPsService rpcPsService;
    @Autowired
    private RpcCpService rpcCpService;

    @Override
    public ValueHolder execute(QuerySession session) {
        DefaultWebEvent event = session.getEvent();
        List<String> nullKeyList = new ArrayList<>();
        JSONObject param = JsonUtils.filterNull((JSONObject) event.getParameterValue("param"), nullKeyList);
        if (param != null) {
            Long id = param.getLong("objid");
            JSONObject fixColumn = param.getJSONObject("fixcolumn");
            StCPreSaleRequest stCPreSaleRequest = JsonUtils.jsonParseClass(fixColumn, StCPreSaleRequest.class);
            if (fixColumn != null && id != null) {
                if (id != -1) {
                    return updateStCPreSale(session, id, stCPreSaleRequest, nullKeyList);
                } else {
                    return insertStCPreSale(session, stCPreSaleRequest, nullKeyList);
                }
            }
        }
        throw new NDSException("当前记录已不存在！");
    }

    /**
     * 预售解析策略 插入
     *
     * @param session
     * @param stCPreSaleRequest
     * @return com.jackrain.nea.util.ValueHolder
     * @Author: 江家雷
     * @Date 2020/06/12
     */
    private ValueHolder updateStCPreSale(QuerySession session, Long id, StCPreSaleRequest stCPreSaleRequest, List<String> nullKeyList) {

        StCPreSaleDO stCPreSale = stCPreSaleRequest.getStCPreSale();
        if (stCPreSale != null) {
            ValueHolder holder = checkStCPreSaleByFilter(id, stCPreSale, "update");
            if (holder != null) {
                return holder;
            }
            //1.预售解析策略主表处理
            stCPreSale.setId(id);
            StBeanUtils.makeModifierField(stCPreSale, session.getUser());
            if (stCPreSaleMapper.updateById(stCPreSale) < 0) {
                return ValueHolderUtils.getFailValueHolder("更新失败！");
            }
        } else {
            stCPreSale = stCPreSaleMapper.selectById(id);
        }
        List<StCPreSaleItemDO> stCPreSaleItemCustomizeList = stCPreSaleRequest.getStCPreSaleItemCustomizeList();
        List<StCPreSaleItemDO> stCPreSaleItemList = stCPreSaleRequest.getStCPreSaleItemList();
        // 自定义全款预售
        if(!CollectionUtils.isEmpty(stCPreSaleItemCustomizeList)){
            List<StCPreSaleItemDO> addItemList = stCPreSaleItemCustomizeList.stream().filter(x -> x.getId() == -1).collect(Collectors.toList());
            List<StCPreSaleItemDO> updateProList = stCPreSaleItemCustomizeList.stream().filter(x -> x.getId() > 0).collect(Collectors.toList());
            if(!CollectionUtils.isEmpty(addItemList)){
                insertStCPreSaleItem(session, StConstant.PRE_SALE_02, stCPreSale, addItemList);
            }
            if(!CollectionUtils.isEmpty(updateProList)){
                updateStCPreSaleItem(session, StConstant.PRE_SALE_02, stCPreSale, updateProList);
            }
        }

        // 店铺全款预售
        if(!CollectionUtils.isEmpty(stCPreSaleItemList)){
            List<StCPreSaleItemDO> addItemList = stCPreSaleItemList.stream().filter(x -> x.getId() == -1).collect(Collectors.toList());
            List<StCPreSaleItemDO> updateProList = stCPreSaleItemList.stream().filter(x -> x.getId() > 0).collect(Collectors.toList());
            if(!CollectionUtils.isEmpty(addItemList)){
                insertStCPreSaleItem(session, StConstant.PRE_SALE_03, stCPreSale, addItemList);
            }
            if(!CollectionUtils.isEmpty(updateProList)){
                updateStCPreSaleItem(session, StConstant.PRE_SALE_03, stCPreSale, updateProList);
            }
        }
        return ValueHolderUtils.getSuccessValueHolder(id, StConstant.TAB_ST_C_PRE_SALE, "");
    }


    /**
     * 预售解析策略 插入
     *
     * @param session
     * @param stCPreSaleRequest
     * @return com.jackrain.nea.util.ValueHolder
     * @Author: 江家雷
     * @Date 2020/06/12
     */
    private ValueHolder insertStCPreSale(QuerySession session, StCPreSaleRequest stCPreSaleRequest, List<String> nullKeyList) {

        long id = 0;
        //1.预售解析策略主表
        StCPreSaleDO stCPreSale = stCPreSaleRequest.getStCPreSale();
        List<StCPreSaleItemDO> stCPreSaleItemCustomizeList = stCPreSaleRequest.getStCPreSaleItemCustomizeList();
        List<StCPreSaleItemDO> stCPreSaleItemList = stCPreSaleRequest.getStCPreSaleItemList();
        if (stCPreSale != null) {
            //1.1 判断名称是否已存在
            ValueHolder check = checkStCPreSaleByFilter(-1L, stCPreSale, "insert");
            if (check != null) {
                return check;
            }
            //1.2 插入
            StringBuilder preSaleWay = new StringBuilder();
            if(!CollectionUtils.isEmpty(stCPreSaleItemCustomizeList)){
                preSaleWay.append(StConstant.PRE_SALE_CUSTOMIZE);
            }
            if(!CollectionUtils.isEmpty(stCPreSaleItemCustomizeList)){
                if (preSaleWay.length() != 0){
                    preSaleWay.append(",");
                }
                preSaleWay.append(StConstant.PRE_SALE_SHOP);
            }

            id = ModelUtil.getSequence(StConstant.TAB_ST_C_PRE_SALE);
            stCPreSale.setId(id);
            stCPreSale.setPreSaleWay(preSaleWay.toString());
            stCPreSale.setPreSaleStatus(StConstant.PRE_SALE_STATUS_01);
            StBeanUtils.makeCreateField(stCPreSale, session.getUser());
            int insertResult = stCPreSaleMapper.insert(stCPreSale);
            if (insertResult < 0) {
                return ValueHolderUtils.getFailValueHolder("保存失败！");
            }

            // 自定义全款预售
            if(!CollectionUtils.isEmpty(stCPreSaleItemCustomizeList)){
                insertStCPreSaleItem(session,StConstant.PRE_SALE_02, stCPreSale, stCPreSaleItemCustomizeList);
            }

            // 店铺全款预售
            if(!CollectionUtils.isEmpty(stCPreSaleItemList)){
                insertStCPreSaleItem(session,StConstant.PRE_SALE_03, stCPreSale, stCPreSaleItemList);
            }
        }

        return ValueHolderUtils.getSuccessValueHolder(id, StConstant.TAB_ST_C_PRE_SALE, "");
    }

    /**
     *  预售解析策略方案明细 插入
     *
     * @param stCPreSale
     * @param session
     * @param stCPreSaleItemList
     * @return com.jackrain.nea.util.ValueHolder
     * @Author: 江家雷
     * @Date 2020/06/12
     */
    private void insertStCPreSaleItem(QuerySession session, Integer preSaleWay, StCPreSaleDO stCPreSale, List<StCPreSaleItemDO> stCPreSaleItemList ) {
        //新增
        for (StCPreSaleItemDO item : stCPreSaleItemList) {
            item.setId(ModelUtil.getSequence(StConstant.TAB_ST_C_PRE_SALE_ITEM));//主键
            item.setStCPreSaleId(stCPreSale.getId());//外键
            item.setStCPreSaleEname(stCPreSale.getEname());
            item.setPreSaleWay(preSaleWay);
            StBeanUtils.makeCreateField(item, session.getUser());
            int insert = stCPreSaleItemMapper.insert(item);
            if (insert < 0) {
                throw new NDSException("预售解析策略方案明细插入失败！");
            }
        }
    }

    /**
     *  预售解析策略方案明细 更新
     *
     * @param stCPreSale
     * @param session
     * @param stCPreSaleItemList
     * @return com.jackrain.nea.util.ValueHolder
     * @Author: 江家雷
     * @Date 2020/06/12
     */
    private void updateStCPreSaleItem(QuerySession session, Integer preSaleWay, StCPreSaleDO stCPreSale, List<StCPreSaleItemDO> stCPreSaleItemList ) {
        //更新
        for (StCPreSaleItemDO item : stCPreSaleItemList) {
            item.setStCPreSaleId(stCPreSale.getId());//外键
            item.setStCPreSaleEname(stCPreSale.getEname());
            item.setPreSaleWay(preSaleWay);
            StBeanUtils.makeModifierField(item, session.getUser());
            int update = stCPreSaleItemMapper.updateById(item);
            if (update < 0) {
                throw new NDSException("预售解析策略方案明细更新失败！");
            }
        }
    }

    /**
     * @param Id
     * @param preSale
     * @param action
     * @return com.jackrain.nea.util.ValueHolder
     * @Author: 江家雷
     * @Date 2020/06/12
     */
    private ValueHolder checkStCPreSaleByFilter(Long Id, StCPreSaleDO preSale, String action) {
        Date beginTime = preSale.getBeginTime();
        Date endTime = preSale.getEndTime();
        String ename = preSale.getEname();
        //时间判断
        if (beginTime != null && endTime != null) {
            if (endTime.compareTo(beginTime) < 0) {
                return ValueHolderUtils.getFailValueHolder("结束日期不能小于开始日期！");
            }
        }
        switch (action.toLowerCase()) {
            case "insert":
                HashMap<String, Object> map = new HashMap<>();
                map.put("ename", ename);
                if (!stCPreSaleMapper.selectByMap(map).isEmpty()) {
                    return ValueHolderUtils.getFailValueHolder("预售解析策略名称已存在！");
                }
                break;
            case "update":
                StCPreSaleDO StCPreSaleOld = stCPreSaleMapper.selectById(Id);
                if (StCPreSaleOld == null) {
                    return ValueHolderUtils.getFailValueHolder("当前记录已不存在！");
                }
                break;
        }

        return null;
    }

}
