package com.jackrain.nea.st.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.burgeon.r3.sg.basic.api.SgLockStockBasicCmd;
import com.google.common.base.Throwables;
import com.google.common.collect.Lists;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.st.common.StConstant;
import com.jackrain.nea.st.mapper.StCLockStockStrategyItemMapper;
import com.jackrain.nea.st.mapper.StCLockStockStrategyMapper;
import com.jackrain.nea.st.model.table.StCLockStockStrategyDO;
import com.jackrain.nea.st.model.table.StCLockStockStrategyItemDO;
import com.jackrain.nea.st.utils.DatasToEsUtils;
import com.jackrain.nea.st.utils.ListUtils;
import com.jackrain.nea.st.utils.StBeanUtils;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.HashMap;
import java.util.List;

/**
 * @Descroption 店铺策略锁库设置反审核
 * @Author: 汪聿森
 * @Date: 2019/3/26 19:40
 */
@Component
@Slf4j
public class LockStockCancleAuditService extends CommandAdapter {
    @Autowired
    private StCLockStockStrategyMapper mapper;

    @Autowired
    private StCLockStockStrategyItemMapper stCLockStockStrategyItemMapper;

    @Reference(group = "sg", version = "1.0")
    private SgLockStockBasicCmd sgLockStockBasicCmd;

//    @Autowired
//    private ShopUniversalStockService shopUniversalStockService;

    @Override
    public ValueHolder execute(QuerySession querySession) throws NDSException {
        //1.获取传入参数
        DefaultWebEvent event = querySession.getEvent();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(event.getParameterValue("param"),
                "yyyy-MM-dd HH:mm:ss", SerializerFeature.WriteMapNullValue), Feature.OrderedField);
        if (param == null) {
            throw new NDSException("参数为空！");
        }
        log.debug(LogUtil.format("Start LockStockCancleAuditService.execute. ReceiveParams: {}"), param.toJSONString());
        //2.定义错误记录数
        HashMap<Long, Object> errMap = new HashMap();
        //3.生成审核Json数组
        JSONArray cancleAuditArray = new JSONArray();
        cancleAuditArray = StBeanUtils.makeUnAuditJsonArray(param);
        if (cancleAuditArray.size() > 0) {
            List<StCLockStockStrategyDO> stockStrategyList = mapper.
                    selectBatchIds(JSONObject.parseArray(cancleAuditArray.toJSONString(), Long.class));
            List<Long> ids = Lists.newArrayListWithExpectedSize(cancleAuditArray.size());
            for (StCLockStockStrategyDO strategyDO : stockStrategyList) {
                try {
                    //4.遍历反审核方法
                    cancleAudit(strategyDO, querySession, ids);
                } catch (Exception e) {
                    errMap.put(strategyDO.getId(), e.getMessage());
                }
            }
            // 同步至PG
            sgLockStockBasicCmd.status(ids, StConstant.CON_BILL_STATUS_01);

        }
        //5.返回ValueHolder对象
        return StBeanUtils.getExcuteValueHolder(cancleAuditArray.size(), errMap);
    }

    public void cancleAudit(StCLockStockStrategyDO strategyDO, QuerySession querySession, List<Long> ids) {

        Long id = strategyDO.getId();
        //主表校验
        checkExpress(strategyDO);
        //更新单据状态
        StBeanUtils.makeModifierField(strategyDO, querySession.getUser());
        strategyDO.setModifierename(querySession.getUser().getEname());
        strategyDO.setEstatus(StConstant.CON_BILL_STATUS_01);
        clearAuditCommonField(strategyDO);
        //修改人id 修改人名称 修改时间
        strategyDO.setReverseId(Long.valueOf(querySession.getUser().getId()));
        strategyDO.setReverseName(querySession.getUser().getName());
        strategyDO.setReverseEname(querySession.getUser().getEname());
        strategyDO.setReverseTime(new Date());
        int updateNum = mapper.updateById(strategyDO);
        if (updateNum < 0) {
            throw new NDSException("锁库方案:" + strategyDO.getPlanName() + "反审核失败！");
        } else {
            //更新到同步库存中间
            //shopLockSkuSyncStockService.updateSgBSyncChannelStock(id,null,StConstant.SKUSTOCK_STATUS_03);
//            ValueHolder v14 = shopUniversalStockService.deleteSgBSyncChannelStock(StConstant.LOCK_STOCK_TYPE.longValue(), id);
//            log.debug(LogUtil.format("锁库反审核 删除同步库存中间表数据 策略id：" + id + " 结果：" + JSONObject.toJSONString(v14));
            ids.add(id);
            //推送ES数据
            try {
                //做更新的需要先查询更新后数据库的实体在推ES
                strategyDO = mapper.selectById(id);
                StCLockStockStrategyItemDO item = new StCLockStockStrategyItemDO();
                item.setStatus(StConstant.CON_BILL_STATUS_01);
                QueryWrapper<StCLockStockStrategyItemDO> wrapper = new QueryWrapper<>();
                wrapper.eq("st_c_lock_stock_strategy_id", id);
                stCLockStockStrategyItemMapper.update(item, wrapper);
                List<StCLockStockStrategyItemDO> itemList = ListUtils.batchQueryByCondition(wrapper, stCLockStockStrategyItemMapper, 1000);
                DatasToEsUtils.insertLoclStockEsData(strategyDO,null,StConstant.TAB_ST_C_LOCK_STOCK_STRATEGY);
                if (CollectionUtils.isNotEmpty(itemList)) {
                    DatasToEsUtils.insertLoclStockEsData(strategyDO, itemList, StConstant.TAB_ST_C_LOCK_STOCK_STRATEGY_ITEM);
                }
            } catch (Exception ex) {
                log.debug(LogUtil.format("店铺锁库策略反审核推数据到ES失败：{}"), Throwables.getStackTraceAsString(ex));
            }
        }

    }

    private void checkExpress(StCLockStockStrategyDO stCLockStockStrategyDO) {
        if (stCLockStockStrategyDO == null) {
            throw new NDSException("当前记录已不存在！");
        } else {
            if (StConstant.CON_BILL_STATUS_01.equals(stCLockStockStrategyDO.getEstatus())) {
                throw new NDSException("当前记录未审核，不允许做反审核！");
            } else if (StConstant.CON_BILL_STATUS_04.equals(stCLockStockStrategyDO.getEstatus())) {
                throw new NDSException("当前记录已结案，不允许做反审核！");
            } else if (StConstant.CON_BILL_STATUS_03.equals(stCLockStockStrategyDO.getEstatus())) {
                throw new NDSException("当前记录已作废，不允许做反审核！");
            }
        }
    }

    private void clearAuditCommonField(StCLockStockStrategyDO stCLockStockStrategyDO) {
        stCLockStockStrategyDO.setCheckid(null);
        stCLockStockStrategyDO.setCheckename(null);
        stCLockStockStrategyDO.setCheckname(null);
        stCLockStockStrategyDO.setChecktime(null);
    }
}
