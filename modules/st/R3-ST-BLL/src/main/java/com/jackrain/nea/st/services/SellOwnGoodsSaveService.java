package com.jackrain.nea.st.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.ext.sequence.observer.SequenceExec;
import com.jackrain.nea.ext.sequence.util.SequenceGenUtil;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.ps.api.result.PsSkuResult;
import com.jackrain.nea.ps.api.table.PsCProdimItem;
import com.jackrain.nea.ps.api.table.PsCSkuExt;
import com.jackrain.nea.psext.model.table.PsCPro;
import com.jackrain.nea.psext.request.SkuQueryListRequest;
import com.jackrain.nea.st.common.StConstant;
import com.jackrain.nea.st.mapper.StCSellOwngoodsCustomerMapper;
import com.jackrain.nea.st.mapper.StCSellOwngoodsItemMapper;
import com.jackrain.nea.st.mapper.StCSellOwngoodsMapper;
import com.jackrain.nea.st.model.request.SellOwnGoodsRequest;
import com.jackrain.nea.st.model.table.StCSellOwngoodsCustomerDO;
import com.jackrain.nea.st.model.table.StCSellOwngoodsDO;
import com.jackrain.nea.st.model.table.StCSellOwngoodsItemDO;
import com.jackrain.nea.st.rpc.RpcPsService;
import com.jackrain.nea.st.utils.JsonUtils;
import com.jackrain.nea.st.utils.StBeanUtils;
import com.jackrain.nea.st.utils.ValueHolderUtils;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description 经销商自有商品保存业务服务
 * @Date 2019/3/8
 **/
@Component
@Slf4j
@Transactional
public class SellOwnGoodsSaveService extends CommandAdapter {

    @Autowired
    private StCSellOwngoodsMapper stCSellOwngoodsMapper;
    @Autowired
    private StCSellOwngoodsItemMapper stCSellOwngoodsItemMapper;
    @Autowired
    private StCSellOwngoodsCustomerMapper stCSellOwngoodsCustomerMapper;
    @Autowired
    private RpcPsService rpcPsService;

    /**
     * @return com.jackrain.nea.util.ValueHolder
     * <AUTHOR>
     * @Description 经销商自有商品保存方法
     * @Date 2019/3/8
     * @Param QuerySession session
     **/
    public ValueHolder saveSellOwnGoods(QuerySession session) {
        DefaultWebEvent event = session.getEvent();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(
                event.getParameterValue("param"), "yyyy-MM-dd HH:mm:ss", SerializerFeature.WriteMapNullValue),
                Feature.OrderedField);
        log.debug(LogUtil.format("请求JSON：") + param.toString());
        //1.拉取请求参数，解析
        Long id = param.getLong("objid");//获取objid参数
        JSONObject fixColumn = param.getJSONObject("fixcolumn");//获取表数据

        //2.转换为请求bean，判断转是否成功校验参数
        SellOwnGoodsRequest sellOwnGoodsRequest = JsonUtils.jsonParseClass(fixColumn, SellOwnGoodsRequest.class);
        if (sellOwnGoodsRequest == null) {
            log.debug(LogUtil.format("获取转换请求对象失败！"));
            return ValueHolderUtils.getFailValueHolder("保存失败！");
        }

        if (fixColumn != null && id != null) {
            if (id != -1) {
                return updateSellOwnGoods(session, sellOwnGoodsRequest, id);
            } else {
                return addSellOwnGoods(session, sellOwnGoodsRequest);
            }
        }
        throw new NDSException("当前记录已不存在！");
    }

    /**
     * @return com.jackrain.nea.util.ValueHolder
     * <AUTHOR>
     * @Description 保存方法
     * @Date 2019/3/13
     * @Param [session, sellOwnGoodsRequest, id]
     **/
    private ValueHolder updateSellOwnGoods(QuerySession session, SellOwnGoodsRequest sellOwnGoodsRequest, Long id) {
        StCSellOwngoodsDO stCSellOwngoodsDO = sellOwnGoodsRequest.getStCSellOwngoodsDO();
        List<StCSellOwngoodsItemDO> stCSellOwngoodsItemDOList = sellOwnGoodsRequest.getStCSellOwngoodsItemDOList();

        //判断主表是否存在
        StCSellOwngoodsDO stCSellOwngoodsDO1 = stCSellOwngoodsMapper.selectById(id);
        if (stCSellOwngoodsDO1 ==  null){
            return ValueHolderUtils.getFailValueHolder("当前记录已不存在！");
        } else {
            if (StConstant.CON_BILL_STATUS_02.equals(stCSellOwngoodsDO1.getBillStatus())) {
                throw new NDSException("当前记录已审核，不允许编辑！");
            }else if(StConstant.CON_BILL_STATUS_03.equals(stCSellOwngoodsDO1.getBillStatus())){
                throw new NDSException("当前记录已作废，不允许编辑！");
            }else if(StConstant.CON_BILL_STATUS_04.equals(stCSellOwngoodsDO1.getBillStatus())){
                throw new NDSException("当前记录已结案，不允许编辑！");
            }
        }

        //1.主表修改
        if (stCSellOwngoodsDO != null) {
            //验证生效日期是否大于结束日期
            if (stCSellOwngoodsDO.getBeginTime() != null && stCSellOwngoodsDO.getEndTime() != null){
                if(!timeCheck(stCSellOwngoodsDO)){
                    return ValueHolderUtils.getFailValueHolder("生效日期必须小于结束日期！");
                }
            }
            stCSellOwngoodsDO.setId(id);
            //基本字段值设置
            StBeanUtils.makeModifierField(stCSellOwngoodsDO, session.getUser());
            stCSellOwngoodsDO.setModifierename(session.getUser().getEname());

            //转换经销商多选字段为jsonstring
            stCSellOwngoodsDO.setCpCCustomerId(stCSellOwngoodsDO.getCpCCustomerId());

            int count = stCSellOwngoodsMapper.updateById(stCSellOwngoodsDO);
            if (count <= 0) {
                log.debug(LogUtil.format("经销商自有商品主表修改保存失败！"));
                return ValueHolderUtils.getFailValueHolder("保存失败！");
            }
        }
        //2.商品明细修改
        if (!CollectionUtils.isEmpty(stCSellOwngoodsItemDOList)) {
            //已有数据查询
            List<StCSellOwngoodsItemDO> stCSellOwngoodsItemOldDOList =  stCSellOwngoodsItemMapper.selectItemByMainId(id);
            Map<Long, StCSellOwngoodsItemDO> itemMap = new HashMap<>();
            if (!CollectionUtils.isEmpty(stCSellOwngoodsItemOldDOList)) {
                itemMap = stCSellOwngoodsItemOldDOList.stream().collect(Collectors.toMap(StCSellOwngoodsItemDO::getPsCSkuId, Function.identity()));
            }
            //经销商自有商品子表保存
            for (StCSellOwngoodsItemDO stCSellOwngoodsItemDO : stCSellOwngoodsItemDOList) {
                ValueHolder holder = saveSellOwngoodsItem(id, stCSellOwngoodsItemDO, itemMap, session);
                if (holder != null) {
                    return holder;
                } else {
                    StCSellOwngoodsDO stCSellOwngoodsNewDO = new StCSellOwngoodsDO();
                    stCSellOwngoodsNewDO.setId(id);
                    StBeanUtils.makeModifierField(stCSellOwngoodsNewDO, session.getUser());
                    stCSellOwngoodsMapper.updateById(stCSellOwngoodsNewDO);
                }
            }
        }
        //3.经销商明细修改
        if (stCSellOwngoodsDO != null && stCSellOwngoodsDO.getCpCCustomerId() != null && !"".equals(stCSellOwngoodsDO.getCpCCustomerId())){
            String ids = stCSellOwngoodsDO.getCpCCustomerId();
            String[] lIds = ids.split(",");
            //插入经销商明细
            insertCustomers(session,id,lIds);
        }
        return ValueHolderUtils.getSuccessValueHolder(id, StConstant.TAB_ST_C_SELL_OWNGOODS);
    }

    /**
     * @return com.jackrain.nea.util.ValueHolder
     * <AUTHOR>
     * @Description 新增方法
     * @Date 2019/3/13
     * @Param [session, sellOwnGoodsRequest]
     **/
    private ValueHolder addSellOwnGoods(QuerySession session, SellOwnGoodsRequest sellOwnGoodsRequest) {
        StCSellOwngoodsDO stCSellOwngoodsDO = sellOwnGoodsRequest.getStCSellOwngoodsDO();
        List<StCSellOwngoodsItemDO> stCSellOwngoodsItemDOList = sellOwnGoodsRequest.getStCSellOwngoodsItemDOList();

        long id = -1;

        //1.判断主表
        if (stCSellOwngoodsDO != null) {
            //验证生效日期是否大于结束日期
            if (stCSellOwngoodsDO.getBeginTime() != null && stCSellOwngoodsDO.getEndTime() != null){
                if(!timeCheck(stCSellOwngoodsDO)){
                    return ValueHolderUtils.getFailValueHolder("生效日期必须小于结束日期！");
                }
            }
            //ID序列
            stCSellOwngoodsDO.setId(ModelUtil.getSequence(StConstant.TAB_ST_C_SELL_OWNGOODS));
            id = stCSellOwngoodsDO.getId();
            //基本字段值设置
            StBeanUtils.makeCreateField(stCSellOwngoodsDO, session.getUser());
            stCSellOwngoodsDO.setModifierename(session.getUser().getEname());
            stCSellOwngoodsDO.setOwnerename(session.getUser().getEname());

            //单据状态，默认值未审核--1
            stCSellOwngoodsDO.setBillStatus(StConstant.CON_BILL_STATUS_01);

            //转换经销商多选字段为jsonstring
            stCSellOwngoodsDO.setCpCCustomerId(stCSellOwngoodsDO.getCpCCustomerId());

            int insertResult = stCSellOwngoodsMapper.insert(stCSellOwngoodsDO);
            if (insertResult <= 0) {
                log.debug(LogUtil.format("经销商自有商品主表保存失败！"));
                return ValueHolderUtils.getFailValueHolder("保存失败！");
            }
            //单据编号
            JSONObject sequence = new JSONObject();
            sequence.put("ST_C_SELL_OWNGOODS", "PARAN");
            SequenceExec exec = SequenceGenUtil.preGenerateSequence()
                    .add("SEQ_ST_C_SELL_OWNGOODS", sequence, id, stCSellOwngoodsMapper, "updateSequence");
            exec.exec();

            //2.自有商品明细保存
            if (!CollectionUtils.isEmpty(stCSellOwngoodsItemDOList)) {
                //已有数据查询
                List<StCSellOwngoodsItemDO> stCSellOwngoodsItemOldDOList =  stCSellOwngoodsItemMapper.selectItemByMainId(id);
                Map<Long, StCSellOwngoodsItemDO> itemMap = new HashMap<>();
                if (!CollectionUtils.isEmpty(stCSellOwngoodsItemOldDOList)) {
                    itemMap = stCSellOwngoodsItemOldDOList.stream().collect(Collectors.toMap(StCSellOwngoodsItemDO::getPsCSkuId, Function.identity()));
                }
                for (StCSellOwngoodsItemDO stCSellOwngoodsItemDO : stCSellOwngoodsItemDOList) {
                    ValueHolder holder = saveSellOwngoodsItem(id, stCSellOwngoodsItemDO, itemMap, session);
                    if (holder != null) {
                        return holder;
                    }
                }
            }
            //3.经销商明细
            if (stCSellOwngoodsDO != null && stCSellOwngoodsDO.getCpCCustomerId() != null && !"".equals(stCSellOwngoodsDO.getCpCCustomerId())){
                String ids = stCSellOwngoodsDO.getCpCCustomerId();
                String[] lIds = ids.split(",");
                //插入经销商明细
                insertCustomers(session,id,lIds);
            }
        }
        return ValueHolderUtils.getSuccessValueHolder(id, StConstant.TAB_ST_C_SELL_OWNGOODS);
    }

    /**
     * <AUTHOR>
     * @Description 生效日期必须大于结束日期
     * @Date  2019/3/20
     * @Param [stCSellOwngoodsDO]
     * @return java.lang.Boolean
    **/
    private Boolean timeCheck(StCSellOwngoodsDO stCSellOwngoodsDO) {
        Boolean flag = false;
        //生效日期必须小于结束日期
        if(stCSellOwngoodsDO.getBeginTime().before(stCSellOwngoodsDO.getEndTime())){
            flag = true;
        }
        return flag;
    }

    /**
     * 插入经销商明细表
     * @param session
     * @param id
     * @param ids
     */
    private void insertCustomers(QuerySession session, Long id, String[] ids){
        //先删除再新增更新
        HashMap<String, Object> map = new HashMap<>();
        map.put("st_c_sell_owngoods_id", id);
        int delete = stCSellOwngoodsCustomerMapper.deleteByMap(map);
        if (delete < 0) {
            log.debug(LogUtil.format("经销商明细删除失败！", id));
            throw new NDSException("经销商明细插入失败！");
        }
        //新增
        for (String customerId : ids) {
            StCSellOwngoodsCustomerDO item = new StCSellOwngoodsCustomerDO();
            item.setId(ModelUtil.getSequence(StConstant.TAB_ST_C_SELL_OWNGOODS_CUSTOMER));//主键
            item.setStCSellOwngoodsId(id);//主表外键
            item.setCpCCustomerId(Long.valueOf(customerId));//经销商外键
            StBeanUtils.makeCreateField(item, session.getUser());
            item.setModifierename(session.getUser().getEname());
            item.setOwnerename(session.getUser().getEname());
            int insert = stCSellOwngoodsCustomerMapper.insert(item);
            if (insert < 0) {
                log.debug(LogUtil.format("经销商明细插入失败！", customerId));
                throw new NDSException("经销商明细插入失败！");
            }
        }
    }

    /**
     * 保存经销商自有商品子表信息
     * @param id
     * @param stCSellOwngoodsItemOldDO
     * @param session
     * @return com.jackrain.nea.util.ValueHolder
     * @Author: 黄超
     * @Date 2019/5/14
     */
    private ValueHolder saveSellOwngoodsItem(Long id, StCSellOwngoodsItemDO stCSellOwngoodsItemOldDO,
                                             Map<Long, StCSellOwngoodsItemDO> itemMap, QuerySession session) {

        if (stCSellOwngoodsItemOldDO == null) {
            log.debug(LogUtil.format("经销商自有商品明细表数据获取为空！"));
            return ValueHolderUtils.getFailValueHolder("保存失败！");
        }
        //商品编码和条码至少输入一个
        if (stCSellOwngoodsItemOldDO.getPsCProId() == null && stCSellOwngoodsItemOldDO.getPsCSkuId() == null) {
            return ValueHolderUtils.getFailValueHolder("商品编码和条码至少输入一个！");
        }

        if (stCSellOwngoodsItemOldDO.getPsCSkuId() != null) {
            SkuQueryListRequest psCSku = rpcPsService.querySkuByIds(stCSellOwngoodsItemOldDO.getPsCSkuId());
            if (psCSku != null && !itemMap.containsKey(psCSku.getId())) {
                StCSellOwngoodsItemDO stCSellOwngoodsItemDO = new StCSellOwngoodsItemDO();
                //序号
                stCSellOwngoodsItemDO.setId(ModelUtil.getSequence(StConstant.TAB_ST_C_SELL_OWNGOODS_ITEM));
                //主表关联id
                stCSellOwngoodsItemDO.setStCSellOwngoodsId(id);

                stCSellOwngoodsItemDO.setPsCSkuId(psCSku.getId()); //条码id
                stCSellOwngoodsItemDO.setPsCSkuEcode(psCSku.getEcode()); //条码
                stCSellOwngoodsItemDO.setPsCSkuGbcode(psCSku.getGbcode()); //国标码
                stCSellOwngoodsItemDO.setPsCProId(psCSku.getPsCProId()); //商品id
                stCSellOwngoodsItemDO.setPsCProEcode(psCSku.getPsCProEcode()); //商品编码
                stCSellOwngoodsItemDO.setPsCProEname(psCSku.getPsCProEname()); //商品名称
                stCSellOwngoodsItemDO.setPsCClrId(psCSku.getPsCSpec1objId()); //颜色id
                stCSellOwngoodsItemDO.setPsCClrEcode(psCSku.getColorEcode()); //颜色编码
                stCSellOwngoodsItemDO.setPsCClrEname(psCSku.getColorName()); //颜色名称
                stCSellOwngoodsItemDO.setPsCSizeId(psCSku.getPsCSpec2objId()); //尺寸id
                stCSellOwngoodsItemDO.setPsCSizeEcode(psCSku.getSizeEcode()); //尺寸编码
                stCSellOwngoodsItemDO.setPsCSizeEname(psCSku.getSizeName()); //尺寸名称

                PsCPro psCPro = rpcPsService.queryProByIds(stCSellOwngoodsItemDO.getPsCProId());
                if (psCPro != null) {
                    stCSellOwngoodsItemDO.setPriceList(psCPro.getPricelist()); //吊牌价
//                    if (psCPro.getBasicunit() != null) {  psCPro中没有该字段
//                        PsCProdimItem psCProdimItem = rpcPsService.queryPsCProDimItem(psCPro.getBasicunit());
//                        if (psCProdimItem != null) {
//                            stCSellOwngoodsItemDO.setUnit(psCProdimItem.getEname()); //单位
//                        }
//                    }
                }
                //基本字段值设置
                StBeanUtils.makeCreateField(stCSellOwngoodsItemDO, session.getUser());
                stCSellOwngoodsItemDO.setModifierename(session.getUser().getEname());
                stCSellOwngoodsItemDO.setOwnerename(session.getUser().getEname());
                int insertResult1 = stCSellOwngoodsItemMapper.insert(stCSellOwngoodsItemDO);
                if (insertResult1 <= 0) {
                    log.debug(LogUtil.format("经销商自有商品明细表插入失败！"));
                    return ValueHolderUtils.getFailValueHolder("保存失败！");
                }
                itemMap.put(stCSellOwngoodsItemDO.getPsCSkuId(), stCSellOwngoodsItemDO);
            }
        } else {
            List<PsCSkuExt> psCSkuExts = new ArrayList<>();
            PsSkuResult psSkuResult = rpcPsService.getSkuResultByProId(stCSellOwngoodsItemOldDO.getPsCProId());
            if (!CollectionUtils.isEmpty(psSkuResult.getSkuList()) && stCSellOwngoodsItemOldDO.getPsCSkuId() != null) {
                psCSkuExts = psSkuResult.getSkuList().stream().filter(sku -> sku.getId().equals(stCSellOwngoodsItemOldDO.getPsCSkuId())).collect(Collectors.toList());
            } else {
                psCSkuExts = psSkuResult.getSkuList();
            }
            if (CollectionUtils.isEmpty(psCSkuExts)) {
                return ValueHolderUtils.getFailValueHolder("无法匹配条码数据！");
            }
            for (PsCSkuExt psCSkuExt : psCSkuExts) {
                if(itemMap.containsKey(psCSkuExt.getId())) {
                    continue;
                }
                StCSellOwngoodsItemDO stCSellOwngoodsItemDO = new StCSellOwngoodsItemDO();
                //序号
                stCSellOwngoodsItemDO.setId(ModelUtil.getSequence(StConstant.TAB_ST_C_SELL_OWNGOODS_ITEM));
                //主表关联id
                stCSellOwngoodsItemDO.setStCSellOwngoodsId(id);

                stCSellOwngoodsItemDO.setPsCSkuId(psCSkuExt.getId()); //条码id
                stCSellOwngoodsItemDO.setPsCSkuEcode(psCSkuExt.getEcode()); //条码
                stCSellOwngoodsItemDO.setPsCSkuGbcode(psCSkuExt.getGbcode()); //国标码
                stCSellOwngoodsItemDO.setPsCProId(psCSkuExt.getPsCProId()); //商品id
                stCSellOwngoodsItemDO.setPsCProEcode(psCSkuExt.getPsCProEcode()); //商品编码
                stCSellOwngoodsItemDO.setPsCProEname(psCSkuExt.getPsCProEname()); //商品名称
                stCSellOwngoodsItemDO.setPsCClrId(psCSkuExt.getPsCSpec1objId()); //颜色id
                stCSellOwngoodsItemDO.setPsCClrEcode(psCSkuExt.getColorEcode()); //颜色编码
                stCSellOwngoodsItemDO.setPsCClrEname(psCSkuExt.getColorName()); //颜色名称
                stCSellOwngoodsItemDO.setPsCSizeId(psCSkuExt.getPsCSpec2objId()); //尺寸id
                stCSellOwngoodsItemDO.setPsCSizeEcode(psCSkuExt.getSizeEcode()); //尺寸编码
                stCSellOwngoodsItemDO.setPsCSizeEname(psCSkuExt.getSizeName()); //尺寸名称

                PsCPro psCPro = rpcPsService.queryProByIds(stCSellOwngoodsItemDO.getPsCProId());
                if (psCPro != null) {
                    stCSellOwngoodsItemDO.setPriceList(psCPro.getPricelist()); //吊牌价
//                    if (psCPro.getBasicunit() != null) {   psCPro中没有该字段
//                        PsCProdimItem psCProdimItem = rpcPsService.queryPsCProDimItem(psCPro.getBasicunit());
//                        if (psCProdimItem != null) {
//                            stCSellOwngoodsItemDO.setUnit(psCProdimItem.getEname()); //单位
//                        }
//                    }
                }
                //基本字段值设置
                StBeanUtils.makeCreateField(stCSellOwngoodsItemDO, session.getUser());
                stCSellOwngoodsItemDO.setModifierename(session.getUser().getEname());
                stCSellOwngoodsItemDO.setOwnerename(session.getUser().getEname());
                int insertResult1 = stCSellOwngoodsItemMapper.insert(stCSellOwngoodsItemDO);
                if (insertResult1 <= 0) {
                    log.debug(LogUtil.format("经销商自有商品明细表插入失败！"));
                    return ValueHolderUtils.getFailValueHolder("保存失败！");
                }
                itemMap.put(stCSellOwngoodsItemDO.getPsCSkuId(), stCSellOwngoodsItemDO);
            }
        }
        return null;
    }
}
