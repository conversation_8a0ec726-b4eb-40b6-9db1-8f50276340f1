package com.jackrain.nea.st.services;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.google.common.collect.Lists;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.psext.api.utils.JsonUtils;
import com.jackrain.nea.st.common.StConstant;
import com.jackrain.nea.st.mapper.StCExpressAreaItemMapper;
import com.jackrain.nea.st.mapper.StCExpressAreaMapper;
import com.jackrain.nea.st.model.result.ExpressAreaItemResult;
import com.jackrain.nea.st.model.result.ExpressAreaItemTableResult;
import com.jackrain.nea.st.model.result.ExpressAreaTreeResult;
import com.jackrain.nea.st.model.result.RegionTreeResult;
import com.jackrain.nea.st.model.table.StCExpressAreaDO;
import com.jackrain.nea.st.model.table.StCExpressAreaItemDO;
import com.jackrain.nea.st.utils.RegionTreeUtils;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @author: huang.zaizai
 * @since: 2019/08/07
 * create at : 2019/7/24 09:00
 */
@Slf4j
@Component
public class ExpressAreaQueryService {
    @Autowired
    private RegionTreeQueryService regionTreeQueryService;
    @Autowired
    private StCExpressAreaMapper mpper;
    @Autowired
    private StCExpressAreaItemMapper itemMapper;

    /**
     * 查询省市区树
     * @param obj
     * @return ValueHolderV14
     */
    public ValueHolderV14 queryExpressAreaTree(JSONObject obj) {
        ValueHolderV14<ExpressAreaTreeResult> vh = new ValueHolderV14<>();
        vh.setCode(ResultCode.SUCCESS);
        if (null == obj || !obj.containsKey("objid")) {
            vh.setCode(ResultCode.FAIL);
            vh.setMessage("参数为空");
            return vh;
        }
        ExpressAreaTreeResult result = new ExpressAreaTreeResult();
        Long id = obj.getLong("objid");
        if (id > 0) {
            StCExpressAreaDO expressArea = mpper.selectById(id);
            result.setExpressArea(expressArea);
        }
        //查询地址
        obj.put("regiontype", "PROV,CITY,DIST");
        ValueHolderV14<List<RegionTreeResult>> treeVh = regionTreeQueryService.queryRegionTree(obj);
        if (treeVh.isOK()) {
            Map<Long, StCExpressAreaItemDO> itemMap = new HashMap<>();
            if (id > 0) {
                List<StCExpressAreaItemDO> itemList = itemMapper.selectList(new QueryWrapper<StCExpressAreaItemDO>()
                        .lambda().eq(StCExpressAreaItemDO::getStCExpressAreaId, id));
                for (StCExpressAreaItemDO item : itemList) {
                    if (item.getCpCRegionAreaId() != null) {
                        itemMap.put(item.getCpCRegionAreaId(), item);
                    } else {
                        itemMap.put(item.getCpCRegionCityId(), item);
                    }
                }
            }
            //构造树
            List<RegionTreeResult> resultList = treeVh.getData();
            if (itemMap.size() > 0) {
                changeTreeChecked(resultList, itemMap);
            }
            result.setExpressAreaTree(resultList);
        } else {
            vh.setCode(ResultCode.FAIL);
            vh.setMessage("省市区获取失败！");
            return vh;
        }
        vh.setData(result);
        return vh;
    }

    /**
     * 构建树
     * @param resultList
     * @param itemMap
     * @return Boolean
     */
    private Boolean changeTreeChecked(List<RegionTreeResult> resultList,
                                    Map<Long, StCExpressAreaItemDO> itemMap) {
        Boolean checkedFlg = true;
        for (RegionTreeResult result : resultList) {
            if (itemMap.containsKey(result.getId())) {
                StCExpressAreaItemDO item = itemMap.get(result.getId());
                result.setChecked(StConstant.ISACTIVE_Y.equals(item.getIsArrive()));
                if (CollectionUtils.isNotEmpty(result.getChildren())) {
                    changeTreeChecked(result.getChildren(), itemMap);
                }
            } else {
                if (CollectionUtils.isNotEmpty(result.getChildren())) {
                    result.setChecked(changeTreeChecked(result.getChildren(), itemMap));
                }
            }
            if (!result.getChecked()) {
                checkedFlg = false;
            }
        }
        return checkedFlg;
    }

    /**
     * 查询物流区域明细表
     * @param obj
     * @return ValueHolderV14
     */
    public ValueHolderV14 queryExpressAreaItemTable(JSONObject obj) {
        ValueHolderV14<ExpressAreaItemTableResult> vh = new ValueHolderV14<>();
        vh.setCode(ResultCode.SUCCESS);
        if (null == obj || !obj.containsKey("objid")) {
            vh.setCode(ResultCode.FAIL);
            vh.setMessage("参数为空");
            return vh;
        }
        if (!obj.containsKey("treeNode")) {
            return vh;
        }
        //查询地址
        obj.put("regiontype", "PROV,CITY,DIST");
        ValueHolderV14<List<RegionTreeResult>> treeVh = regionTreeQueryService.queryRegionTree(obj);
        if (treeVh.isOK()) {
            //查询明细
            Long id = obj.getLong("objid");
            String treeNode = JSONArray.toJSONString(obj.getJSONArray("treeNode"));
            List<RegionTreeResult> treeList = treeVh.getData();
            List<RegionTreeResult> treeNodeList = JsonUtils.jsonToList(RegionTreeResult.class, treeNode);

            // 构造物流区域明细
            List<StCExpressAreaItemDO> expressAreaItemList = buildExpressAreaItemList(id, treeList, treeNodeList);
            if (CollectionUtils.isNotEmpty(expressAreaItemList)) {
                ExpressAreaItemTableResult tableResult = new ExpressAreaItemTableResult();
                tableResult.setTableSize(expressAreaItemList.size());
                List<ExpressAreaItemResult> itemResultList = Lists.newArrayList();
                for (StCExpressAreaItemDO item : expressAreaItemList) {
                    ExpressAreaItemResult itemResult = new ExpressAreaItemResult();
                    BeanUtils.copyProperties(item, itemResult);
                    itemResultList.add(itemResult);
                }
                tableResult.setItemResultList(itemResultList);
                vh.setData(tableResult);
            }
        } else {
            vh.setCode(ResultCode.FAIL);
            vh.setMessage("物流区域明细获取失败！");
            return vh;
        }
        return vh;
    }

    /**
     * 构造物流区域明细
     * @param id
     * @param treeList
     * @param treeNodeList
     * @return List<StCExpressAreaItemDO>
     */
    private List<StCExpressAreaItemDO> buildExpressAreaItemList(Long id, List<RegionTreeResult> treeList,
                                                                List<RegionTreeResult> treeNodeList) {
        List<StCExpressAreaItemDO> expressAreaItemList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(treeNodeList)) {
            treeNodeList = treeNodeList.stream().sorted(Comparator.comparing(RegionTreeResult::getId))
                    .collect(Collectors.toList());
            Map<Long, StCExpressAreaItemDO> itemMap = getItemMap(id, treeNodeList);

            for (RegionTreeResult node : treeNodeList) {
                if (itemMap.containsKey(node.getId())) {
                    StCExpressAreaItemDO item = itemMap.get(node.getId());
                    expressAreaItemList.add(item);
                } else {
                    RegionTreeResult treeResult = RegionTreeUtils.getTreeResult(treeList, node.getId());
                    if (treeResult != null) {
                        //设置值
                        StCExpressAreaItemDO item = new StCExpressAreaItemDO();
                        item.setId(-1L);
                        item.setIsArrive(StConstant.ISACTIVE_N);
                        if ("DIST".equals(treeResult.getRegiontype())) {
                            //区
                            item.setCpCRegionAreaId(treeResult.getId());
                            item.setCpCRegionAreaEcode(treeResult.getEcode());
                            item.setCpCRegionAreaEname(treeResult.getTitle());
                            treeResult = RegionTreeUtils.getTreeResult(treeList, treeResult.getUpId());
                            if (treeResult != null) {
                                //市
                                item.setCpCRegionCityId(treeResult.getId());
                                item.setCpCRegionCityEcode(treeResult.getEcode());
                                item.setCpCRegionCityEname(treeResult.getTitle());
                                treeResult = RegionTreeUtils.getTreeResult(treeList, treeResult.getUpId());
                                if (treeResult != null) {
                                    //省
                                    item.setCpCRegionProvinceId(treeResult.getId());
                                    item.setCpCRegionProvinceEcode(treeResult.getEcode());
                                    item.setCpCRegionProvinceEname(treeResult.getTitle());
                                }
                            }
                        } else {
                            item.setCpCRegionCityId(treeResult.getId());
                            item.setCpCRegionCityEcode(treeResult.getEcode());
                            item.setCpCRegionCityEname(treeResult.getTitle());
                            treeResult = RegionTreeUtils.getTreeResult(treeList, treeResult.getUpId());
                            if (treeResult != null) {
                                //省
                                item.setCpCRegionProvinceId(treeResult.getId());
                                item.setCpCRegionProvinceEcode(treeResult.getEcode());
                                item.setCpCRegionProvinceEname(treeResult.getTitle());
                            }
                        }
                        expressAreaItemList.add(item);
                    }
                }
            }
        }
        return expressAreaItemList;
    }

    /**
     * 获取明细MAP
     * @param id
     * @param treeNodeList
     * @return Map<Long, StCExpressAreaItemDO>
     */
    private Map<Long, StCExpressAreaItemDO> getItemMap(Long id, List<RegionTreeResult> treeNodeList) {
        Map<Long, StCExpressAreaItemDO> itemMap = new HashMap<>();
        if (id > 0) {
            Map<String, List<Long>> treeNodeMap = new HashMap<>();
            for (RegionTreeResult regionTree : treeNodeList) {
                List<Long> idList = new ArrayList<>();
                if (treeNodeMap.containsKey(regionTree.getRegiontype())) {
                    idList = treeNodeMap.get(regionTree.getRegiontype());
                }
                idList.add(regionTree.getId());
                treeNodeMap.put(regionTree.getRegiontype(), idList);
            }

            List<StCExpressAreaItemDO> itemAllList = itemMapper.selectList(new QueryWrapper<StCExpressAreaItemDO>()
                    .lambda().eq(StCExpressAreaItemDO::getStCExpressAreaId, id));
            for (String key : treeNodeMap.keySet()) {
                List<StCExpressAreaItemDO> itemList = Lists.newArrayList();
                if (key.equals("DIST")) {
                    itemList = itemAllList.stream().filter(item -> treeNodeMap.get(key).contains(item.getCpCRegionAreaId()))
                            .collect(Collectors.toList());
                } else {
                    itemList = itemAllList.stream().filter(item -> treeNodeMap.get(key).contains(item.getCpCRegionCityId()))
                            .collect(Collectors.toList());
                }
                if (CollectionUtils.isNotEmpty(itemList)) {
                    for (StCExpressAreaItemDO item : itemList) {
                        if (item.getCpCRegionAreaId() != null) {
                            itemMap.put(item.getCpCRegionAreaId(), item);
                        } else {
                            itemMap.put(item.getCpCRegionCityId(), item);
                        }
                    }
                }
            }
        }
        return itemMap;
    }

    /**
     * 模糊地区查询物流区域明细表
     * @param obj
     * @return ValueHolderV14
     */
    public ValueHolderV14 queryExpressAreaItemLikeTable(JSONObject obj) {
        ValueHolderV14<ExpressAreaItemTableResult> vh = new ValueHolderV14<>();
        vh.setCode(ResultCode.SUCCESS);
        if (null == obj || !obj.containsKey("objid")) {
            vh.setCode(ResultCode.FAIL);
            vh.setMessage("参数为空");
            return vh;
        }
        if (!obj.containsKey("treeLikeKey")) {
            return vh;
        }
        //查询地址
        obj.put("regiontype", "PROV,CITY,DIST");
        ValueHolderV14<List<RegionTreeResult>> treeVh = regionTreeQueryService.queryRegionTree(obj);
        ExpressAreaItemTableResult likeResult = new ExpressAreaItemTableResult();
        likeResult.setItemResultList(Lists.newArrayList());
        if (treeVh.isOK()) {
            //查询明细
            Long id = obj.getLong("objid");
            List<RegionTreeResult> treeList = treeVh.getData();

            List<RegionTreeResult> treeNodeList = org.assertj.core.util.Lists.newArrayList();
            RegionTreeUtils.changeTreeLikeChecked(treeList, obj.getString("treeLikeKey"), false, treeNodeList);
            likeResult.setExpressAreaTree(treeList);
            // 构造物流区域明细
            List<StCExpressAreaItemDO> expressAreaItemList = buildExpressAreaItemList(id, treeList, treeNodeList);
            if (CollectionUtils.isNotEmpty(expressAreaItemList)) {
                likeResult.setTableSize(expressAreaItemList.size());
                List<ExpressAreaItemResult> itemResultList = Lists.newArrayList();
                for (StCExpressAreaItemDO item : expressAreaItemList) {
                    ExpressAreaItemResult itemResult = new ExpressAreaItemResult();
                    BeanUtils.copyProperties(item, itemResult);
                    itemResultList.add(itemResult);
                }
                likeResult.setItemResultList(itemResultList);
            }
        } else {
            vh.setCode(ResultCode.FAIL);
            vh.setMessage("物流区域明细获取失败！");
            return vh;
        }
        vh.setData(likeResult);
        return vh;
    }
}
