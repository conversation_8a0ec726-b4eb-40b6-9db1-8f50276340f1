package com.jackrain.nea.st.services;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.jackrain.nea.st.mapper.StCSplitReasonConfigMapper;
import com.jackrain.nea.st.mapper.StCSplitReasonItemMapper;
import com.jackrain.nea.st.mapper.StCSplitReasonMapper;
import com.jackrain.nea.st.model.request.StCSplitReasonRequest;
import com.jackrain.nea.st.model.table.StCSplitReasonDO;
import com.jackrain.nea.st.model.table.StCSplitReasonItemDO;
import com.jackrain.nea.sys.CommandAdapter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

/**
 * @program: r3-st
 * @description: 查询自定义拆单业务
 * @author: liuwj
 * @create: 2021-06-02 11:13
 **/
@Component
@Slf4j
@Transactional
public class StCSplitReasonQueryService extends CommandAdapter {
    @Autowired
    StCSplitReasonConfigMapper stCSplitReasonConfigMapper;

    @Autowired
    StCSplitReasonMapper stCSplitReasonMapper;

    @Autowired
    StCSplitReasonItemMapper stCSplitReasonItemMapper;

    /**
     * <AUTHOR>
     * @Date 11:17 2021/6/2
     * @Description 查询系统拆单原因匹配的拆单原因
     */
    public List<StCSplitReasonRequest> queryStCSplitReasonBySplitReason(String systemSplitReason) {
        log.info(getClass().getName()+"查询自定义策略入参{}",systemSplitReason);
        List<StCSplitReasonRequest> stCSplitReasonRequestList = stCSplitReasonMapper.queryStCSplitReasonList(systemSplitReason);
        if (CollectionUtils.isEmpty(stCSplitReasonRequestList)){
            return null;
        }
        return stCSplitReasonRequestList;
    }

    /**
     * <AUTHOR>
     * @Date 14:25 2021/6/2
     * @Description 查询全部拆单原因
     */
    public List<StCSplitReasonRequest> queryStCSplitReasonAll() {
        List<StCSplitReasonRequest> stCSplitReasonRequestList = stCSplitReasonMapper.queryStCSplitReasonAll();
        if (CollectionUtils.isEmpty(stCSplitReasonRequestList)){
            return null;
        }
        return stCSplitReasonRequestList;
    }
}
