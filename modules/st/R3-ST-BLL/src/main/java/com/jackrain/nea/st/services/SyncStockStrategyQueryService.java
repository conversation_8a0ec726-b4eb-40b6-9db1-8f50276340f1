package com.jackrain.nea.st.services;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.jackrain.nea.cp.api.CpCOrgChannelQueryCmd;
import com.jackrain.nea.cp.entity.CpCOrgChannelEntity;
import com.jackrain.nea.cp.entity.CpCOrgChannelItemEntity;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.st.mapper.StCSyncStockStrategyChannelMapper;
import com.jackrain.nea.st.mapper.StCSyncStockStrategyMapper;
import com.jackrain.nea.st.model.request.StStockPriorityRequest;
import com.jackrain.nea.st.model.request.SyncStockStrategyQueryRequest;
import com.jackrain.nea.st.model.result.CpCOrgChannelResult;
import com.jackrain.nea.st.model.result.SyncStockStrategyQueryResult;
import com.jackrain.nea.st.model.table.StCSyncStockStrategyChannelDO;
import com.jackrain.nea.st.model.table.StCSyncStockStrategyDO;
import com.jackrain.nea.st.model.vo.StCSyncStockStrategyVo;
import com.jackrain.nea.utility.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * @description: 店铺同步库存策略服务接口
 * @author: 郑小龙
 * @date: 2019-04-03 16:46
 */
@Component
@Slf4j
public class SyncStockStrategyQueryService {
    @Autowired
    private StCSyncStockStrategyMapper stCSyncStockStrategyMapper;
    @Autowired
    private StCSyncStockStrategyChannelMapper stCSyncStockStrategyChannelMapper;
    @Reference(group = "cp", version = "1.0")
    private CpCOrgChannelQueryCmd cpCOrgChannelQueryCmd;

    /**
     * 通用查询库存同步策略接口
     *
     * @param request 查询对象
     * @return 返回满足条件的策略集合
     */
    public List<StCSyncStockStrategyDO> querySyncStockStrategy(SyncStockStrategyQueryRequest request) {
        LambdaQueryWrapper<StCSyncStockStrategyDO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(StringUtils.isNotBlank(request.getIsactive()),
                StCSyncStockStrategyDO::getIsactive, request.getIsactive());
        wrapper.in(CollectionUtils.isNotEmpty(request.getIds()),
                StCSyncStockStrategyDO::getId, request.getIds());
        wrapper.in(CollectionUtils.isNotEmpty(request.getShopIds()),
                StCSyncStockStrategyDO::getCpCShopId, request.getShopIds());
        wrapper.in(CollectionUtils.isNotEmpty(request.getShopCodes()),
                StCSyncStockStrategyDO::getCpCShopEcode, request.getShopCodes());
        return stCSyncStockStrategyMapper.selectList(wrapper);
    }

    /**
     * 店铺库存同步策略查询
     *
     * @param shopId
     * @return
     * @throws NDSException
     */
    public List<StCSyncStockStrategyVo> querySyncStockStrategyItemByID(Long shopId) {
        return stCSyncStockStrategyMapper.selectStrategyByShopId(shopId);
    }

    /**
     * 店铺库存同步策略查询-占单
     *
     * @param shopId
     * @return
     * @throws NDSException
     */
    public List<StStockPriorityRequest> selectStockPriorityByShopId(Long shopId) {
        return stCSyncStockStrategyMapper.selectStockPriorityByShopId(shopId);
    }

    /**
     * 孙继东加 2019.5.5 19:55
     * 校验是否存在根据逻辑仓库id集合
     *
     * @param storeIds 逻辑仓id集合
     * @return
     */
    public Boolean checkByStoreIds(List<Long> storeIds) {
        return cpCOrgChannelQueryCmd.checkByStoreIds(storeIds);

    }

    /**
     * 2019-05-20 15:06 by 孙继东
     * 根据店铺id查询店仓id集合
     *
     * @param shopId
     * @return
     */
    public List<Long> queryStoreIdsByShopId(Long shopId) {
        return stCSyncStockStrategyMapper.queryStoreIdsByShopId(shopId);
    }

    /**
     * 查找店铺同步策略下面的逻辑供货仓
     *
     * @param cpCShopId 店铺Id
     * @param storeList 取交集的逻辑仓
     * @return List<Long>
     * 搬迁：tqh；2019-05-22
     */
    public List<Long> queryShopStoreList(Long cpCShopId, List<Long> storeList) {
        log.debug(LogUtil.format("SyncStockStrategyQueryService.queryShopStoreList入参：取交集的逻辑仓{},店铺id=",cpCShopId),storeList);
        List<Long> shopStoreList = stCSyncStockStrategyMapper.queryShopStoreList(cpCShopId, storeList);
        log.debug(LogUtil.format("SyncStockStrategyQueryService.queryShopStoreList出参：" + shopStoreList));
        return shopStoreList;
    }

    /**
     * 查找店铺同步库存明细
     *
     * @param cpCShopId 店铺Id
     * @return List<StCSyncStockStrategyItemDO>
     */
    public List<CpCOrgChannelItemEntity> querySyncStockStrategy(Long cpCShopId) {
        log.debug(LogUtil.format("SyncStockStrategyQueryService.querySyncStockStrategy入参：店铺id=", cpCShopId));
        List<CpCOrgChannelItemEntity> itemList = stCSyncStockStrategyChannelMapper.selectItemByShopId(cpCShopId);
        log.debug(LogUtil.format("SyncStockStrategyQueryService.querySyncStockStrategy出参：") + itemList);
        return itemList;
    }

    /**
     * 查找店铺同步库存明细
     *
     * @param cpCShopId 店铺Id
     * @return List<StCSyncStockStrategyItemDO>
     */
    public List<CpCOrgChannelResult> querySyncStockStrategyStore(Long cpCShopId) {
        log.debug(LogUtil.format("SyncStockStrategyQueryService.querySyncStockStrategy入参：店铺id=", cpCShopId));
        List<CpCOrgChannelResult> itemList = stCSyncStockStrategyChannelMapper.selectSyncStockStrategyByShopId(cpCShopId);
        log.debug(LogUtil.format("SyncStockStrategyQueryService.querySyncStockStrategy出参：") + itemList);
        return itemList;
    }

    /**
     * 查找店铺同步库存逻辑供货仓的优先级
     *
     * @param cpCShopId 店铺Id
     * @param storeList 取交集的逻辑仓
     * @return List<Long>
     * 搬迁：tqh；2019-05-22
     */
    public List<StStockPriorityRequest> queryStStockPriority(Long cpCShopId, List<Long> storeList) {
        log.debug(LogUtil.format("SyncStockStrategyQueryService.queryStStockPriority入参：取交集的逻辑仓:{},店铺id="
                , cpCShopId), storeList);
        List<StStockPriorityRequest> queryStStockList = stCSyncStockStrategyMapper.queryStStockPriority(cpCShopId, storeList);
        log.debug(LogUtil.format("SyncStockStrategyQueryService.queryShopStoreList出参：") + queryStStockList);
        return queryStStockList;
    }

    /**
     * 查找店铺同步策略下面的逻辑供货仓[没有交集的情况]
     *
     * @param cpCShopId 店铺Iden
     * @return List<Long>
     * 搬迁：tqh；2019-05-22
     */
    public List<Long> queryShopStoreNextList(Long cpCShopId) {
        log.debug(LogUtil.format("SyncStockStrategyQueryService.queryShopStoreNextList入参：店铺id=", cpCShopId));
        List<Long> shopStoreList = stCSyncStockStrategyMapper.queryShopStoreNextList(cpCShopId);
        log.debug(LogUtil.format("SyncStockStrategyQueryService.queryShopStoreNextList出参：") + shopStoreList);
        return shopStoreList;
    }

    /**
     * 查询所有店铺同步库存策略
     *
     * @return List<SyncStockStrategyQueryResult>
     */
    public List<SyncStockStrategyQueryResult> queryAllSyncStockStrategy() {
        List<SyncStockStrategyQueryResult> syncStockStrategyQueryResultList = new ArrayList<>();

        List<StCSyncStockStrategyDO> stCSyncStockStrategyList = stCSyncStockStrategyMapper.selectAllSyncStockStrategy();

        if (CollectionUtils.isNotEmpty(stCSyncStockStrategyList)) {
            for (StCSyncStockStrategyDO stCSyncStockStrategyDO : stCSyncStockStrategyList) {
                SyncStockStrategyQueryResult result = new SyncStockStrategyQueryResult();
                result.setSyncStockStrategy(stCSyncStockStrategyDO);
                Long id = stCSyncStockStrategyDO.getId();
                List<StCSyncStockStrategyChannelDO> stCSyncStockStrategyChannelList = stCSyncStockStrategyChannelMapper.selectAllSyncStockStrategyChannel(id);
                List<CpCOrgChannelEntity> cpCOrgChannelEntityList = stCSyncStockStrategyChannelMapper.selectAllChannel(id);
                List<CpCOrgChannelItemEntity> cpCOrgChannelItemEntityList = stCSyncStockStrategyChannelMapper.selectAllChannelItem(id);
                if (CollectionUtils.isNotEmpty(stCSyncStockStrategyChannelList)) {
                    result.setSyncStockStrategyChannelDOList(stCSyncStockStrategyChannelList);
                }
                if (CollectionUtils.isNotEmpty(cpCOrgChannelEntityList)) {
                    result.setCpCOrgChannelEntityList(cpCOrgChannelEntityList);
                }
                if (CollectionUtils.isNotEmpty(cpCOrgChannelItemEntityList)) {
                    result.setCpCOrgChannelItemEntityList(cpCOrgChannelItemEntityList);
                }
                syncStockStrategyQueryResultList.add(result);
            }
        }

        log.debug(LogUtil.format("SyncStockStrategyQueryService.queryAllSyncStockStrategy返回：")
                + JSONObject.toJSONString(syncStockStrategyQueryResultList));
        System.out.println(syncStockStrategyQueryResultList.size());
        System.out.println("SyncStockStrategyQueryService.queryAllSyncStockStrategy返回:" + JSONObject.toJSONString(syncStockStrategyQueryResultList));

        return syncStockStrategyQueryResultList;
    }

}
