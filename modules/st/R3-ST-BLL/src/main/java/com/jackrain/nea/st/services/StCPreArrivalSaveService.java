package com.jackrain.nea.st.services;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.cpext.model.table.CpCPhyWarehouse;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.psext.model.table.PsCSku;
import com.jackrain.nea.redis.util.RedisOpsUtil;
import com.jackrain.nea.st.common.StConstant;
import com.jackrain.nea.st.mapper.StCPreArrivalMapper;
import com.jackrain.nea.st.mapper.StCPreArrivalItemMapper;
import com.jackrain.nea.st.model.request.StCPreArrivalRequest;
import com.jackrain.nea.st.model.table.*;
import com.jackrain.nea.st.rpc.RpcCpService;
import com.jackrain.nea.st.rpc.RpcPsService;
import com.jackrain.nea.st.utils.JsonUtils;
import com.jackrain.nea.st.utils.StBeanUtils;
import com.jackrain.nea.st.utils.ValueHolderUtils;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description 预到货策略保存
 * @Date 2020/06/10
 * @Copyright 2019-2020
 */
@Component
@Slf4j
@Transactional
public class StCPreArrivalSaveService extends CommandAdapter {

    @Autowired
    private StCPreArrivalMapper stCPreArrivalMapper;

    @Autowired
    private StCPreArrivalItemMapper stCPreArrivalItemMapper;

    @Autowired
    private RpcCpService rpcCpService;

    @Autowired
    private RpcPsService rpcPsService;

    @Autowired
    private RedisOpsUtil<String, Object> redisUtil;
    /**
     * @return com.jackrain.nea.util.ValueHolder
     * <AUTHOR>
     * @Description 预到货策略保存
     * @Date  2020/06/10
     * @Param [session]
     **/
    public ValueHolder execute(QuerySession session) throws NDSException{
        DefaultWebEvent event = session.getEvent();
        List<String> nullKeyList = new ArrayList<>();
        JSONObject param = JsonUtils.filterNull((JSONObject) event.getParameterValue("param"), nullKeyList);
        if(log.isDebugEnabled()){
            log.debug(LogUtil.format("updateStCPreArrival execute param:{}"), param);
        }
        if (param != null) {
            Long id = param.getLong("objid");
            JSONObject fixColumn = param.getJSONObject("fixcolumn");
            StCPreArrivalRequest stCPreArrivalRequest = JsonUtils.jsonParseClass(fixColumn, StCPreArrivalRequest.class);
            if (fixColumn != null && id != null) {
                if (id != -1) {
                    return updateStCPreArrival(session, id, stCPreArrivalRequest);
                } else {
                    return insertStCPreArrival(session, stCPreArrivalRequest);
                }
            }
        }
        throw new NDSException("当前记录已不存在！");
    }

    /**
     * 预到货方案 插入
     *
     * @param session
     * @param stCPreArrivalRequest
     * @return com.jackrain.nea.util.ValueHolder
     * @Date  2020/06/10
     * @Param [session]
     **/
    private ValueHolder insertStCPreArrival(QuerySession session, StCPreArrivalRequest stCPreArrivalRequest) {

        Long stCPreArrivalId = ModelUtil.getSequence(StConstant.TAB_ST_C_PRE_ARRIVAL);
        //1.预到货策略方案主表
        StCPreArrivalDO  stCPreArrival = stCPreArrivalRequest.getStCPreArrival();
        if (stCPreArrival != null) {
            //1.1 判断名称是否已存在
            ValueHolder check = checkStCPreArrivalByFilter( stCPreArrival, "insert", -1L);
            if (check != null) {
                return check;
            }
            //1.2 插入
            stCPreArrival.setPreArrivalStatus(StConstant.PRE_ARRIVAL_STATUS_01);
            stCPreArrival.setId(stCPreArrivalId);
            StBeanUtils.makeCreateField(stCPreArrival, session.getUser());
            long id  = stCPreArrivalMapper.insert(stCPreArrival);
            if (id <= 0) {
                return ValueHolderUtils.getFailValueHolder("保存失败！");
            }
            //2.商品明细
            if (stCPreArrivalRequest.getStCPreArrivalItemList() != null) {
                insertStCPreArrivalItem(session, stCPreArrivalId, stCPreArrivalRequest.getStCPreArrivalItemList());
            }
        }
        return ValueHolderUtils.getSuccessValueHolder(stCPreArrivalId, StConstant.TAB_ST_C_PRE_ARRIVAL, "");
    }

    private ValueHolder updateStCPreArrival(QuerySession session, Long id, StCPreArrivalRequest stCPreArrivalRequest) {
        StCPreArrivalDO stCPreArrival = stCPreArrivalRequest.getStCPreArrival();
        if (stCPreArrival != null) {
            if(log.isDebugEnabled()){
                log.debug(LogUtil.format("updateStCPreArrival params:{}"), JSONObject.toJSONString(stCPreArrival));
            }
            ValueHolder holder = checkStCPreArrivalByFilter(stCPreArrival, "update", id);
            if (holder != null) {
                return holder;
            }
            //1.预到货方案主表处理
            StBeanUtils.makeModifierField(stCPreArrival, session.getUser());
            if (stCPreArrivalMapper.updateById(stCPreArrival) < 0) {
                return ValueHolderUtils.getFailValueHolder("更新失败！");
            }

        }

        //新增子表信息
        List<StCPreArrivalItemDO> preList = stCPreArrivalRequest.getStCPreArrivalItemList() ;
        if (!CollectionUtils.isEmpty(preList)) {
            List<StCPreArrivalItemDO> addPreList = preList.stream().filter(x -> x.getId() == -1).collect(Collectors.toList());
            //修改子表信息
            List<StCPreArrivalItemDO> updatePreList = preList.stream().filter(x -> x.getId() > 0).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(updatePreList)) {
                updateStCPreArrivalItem(session, id, updatePreList);
            }
            if (!CollectionUtils.isEmpty(addPreList)) {
                insertStCPreArrivalItem(session, id, addPreList);
            }
        }
        return ValueHolderUtils.getSuccessValueHolder(id, StConstant.TAB_ST_C_PRE_ARRIVAL, "");
    }

    private void insertStCPreArrivalItem(QuerySession session, Long id, List<StCPreArrivalItemDO> addPreList) {
        StCPreArrivalDO stCPreArrivalDO = stCPreArrivalMapper.selectById(id);
        if(stCPreArrivalDO == null || CollectionUtils.isEmpty(addPreList)){
            return;
        }
        //新增
        for (StCPreArrivalItemDO item : addPreList) {
            item.setStCPreArrivalId(id);//外键
            StBeanUtils.makeCreateField(item, session.getUser());
            // TODO 此处可增加商品的其他信息
            // 商品编码、商品名称、商品规格、产品线等等
            PsCSku sku = rpcPsService.getSkuById(item.getPsCSkuId());
            if(sku != null){
                item.setId(ModelUtil.getSequence(StConstant.TAB_ST_C_PRE_ARRIVAL_ITEM));
                item.setPsCSkuEcode(sku.getEcode());
                item.setPsCProEcode(sku.getPsCProEcode());
                item.setPsCProEname(sku.getPsCProEname());
            }
            int insert = stCPreArrivalItemMapper.insert(item);
            if (insert < 0) {
                throw new NDSException("预到货策略明细插入失败！");
            }
            // 暂时不存 redis 有性能瓶颈再这么处理
            /*StringBuilder preKey = deleteRedisData(id, item.getPsCSkuEcode());
            Date now = new Date();
            long time = stCPreArrivalDO.getEndTime().getTime()-now.getTime();
            if(time > 0){
                redisUtil.objRedisTemplate.opsForValue().set(preKey.append(item.getPsCSkuEcode()).toString(), item,time, TimeUnit.MILLISECONDS);
            }*/

        }
    }

    private StringBuilder deleteRedisData(Long stCPreArrivalId,String skuCode) {
        // Redis 删除指定前缀的所有key
        StringBuilder preKey = new StringBuilder();
        preKey.append(StConstant.REDIS_ST_PRE_ARRIVAL).append(stCPreArrivalId).append(":");
        preKey.append(skuCode).toString();
        redisUtil.objRedisTemplate.delete(preKey.toString());
        return preKey;
    }

    private void updateStCPreArrivalItem(QuerySession session, Long id, List<StCPreArrivalItemDO> updatePreList) {

        StCPreArrivalDO stCPreArrivalDO = stCPreArrivalMapper.selectById(id);
        if(stCPreArrivalDO == null || CollectionUtils.isEmpty(updatePreList)){
            return;
        }
        // 更新
        for (StCPreArrivalItemDO item : updatePreList) {
            StBeanUtils.makeCreateField(item, session.getUser());
            // TODO 此处可增加商品的其他信息
            // 商品编码、商品名称、商品规格、产品线等等
            PsCSku sku = rpcPsService.getSkuById(item.getPsCSkuId());
            if(sku != null){
                item.setPsCSkuEcode(sku.getEcode());
                // 商品编码、商品名称、商品规格、产品线等等
                item.setPsCProEcode(sku.getPsCProEcode());
                item.setPsCProEname(sku.getPsCProEname());
            }
            int update = stCPreArrivalItemMapper.updateById(item);
            if (update < 0) {
                throw new NDSException("预到货策略明细更新失败！");
            }
            // 暂时不存 redis 有性能瓶颈再这么处理
            // 删除Redis缓存
            /*StringBuilder preKey = deleteRedisData(id, item.getPsCSkuEcode());
            Date now = new Date();
            long time = stCPreArrivalDO.getEndTime().getTime()-now.getTime();
            if(time > 0){
                redisUtil.objRedisTemplate.opsForValue().set(preKey.append(item.getPsCSkuEcode()).toString(), item,time, TimeUnit.MILLISECONDS);
            }*/
        }
    }


    private ValueHolder checkStCPreArrivalByFilter(StCPreArrivalDO stCPreArrivalDO, String action, Long id) {
        Date beginTime = stCPreArrivalDO.getBeginTime();
        Date endTime = stCPreArrivalDO.getEndTime();
        String ename = stCPreArrivalDO.getEname();
        //时间判断
        if (beginTime != null && endTime != null) {
            if (endTime.compareTo(beginTime) < 0) {
                return ValueHolderUtils.getFailValueHolder("结束日期不能小于开始日期！");
            }
        }
        switch (action.toLowerCase()) {
            case "insert":
                HashMap<String, Object> map = new HashMap<>();
                map.put("ename", ename);
                if (!stCPreArrivalMapper.selectByMap(map).isEmpty()) {
                    return ValueHolderUtils.getFailValueHolder("预到货方案名称已存在！");
                }
                break;
            case "update":
                StCPreArrivalDO stCPreArrivalOld = stCPreArrivalMapper.selectById(id);
                if (stCPreArrivalOld == null) {
                    return ValueHolderUtils.getFailValueHolder("当前记录已不存在！");
                }
                break;
        }
        return null;
    }
}
