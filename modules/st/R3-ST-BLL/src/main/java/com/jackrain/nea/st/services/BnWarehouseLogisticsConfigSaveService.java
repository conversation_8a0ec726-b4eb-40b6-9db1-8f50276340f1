package com.jackrain.nea.st.services;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.st.common.StConstant;
import com.jackrain.nea.st.mapper.StCBnColumnListMapper;
import com.jackrain.nea.st.mapper.StCBnWarehouseLogisticsConfigMapper;
import com.jackrain.nea.st.model.table.StCBnColumnListDO;
import com.jackrain.nea.st.model.table.StCBnWarehouseLogisticsConfigDO;
import com.jackrain.nea.st.utils.JsonUtils;
import com.jackrain.nea.st.utils.StBeanUtils;
import com.jackrain.nea.st.utils.ValueHolderUtils;
import com.jackrain.nea.sys.constants.ResultCode;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @ClassName BnWarehouseLogisticsConfigSaveService
 * @Description 班牛 仓库-物流保存
 * <AUTHOR>
 * @Date 2024/11/12 18:20
 * @Version 1.0
 */
@Component
@Slf4j
public class BnWarehouseLogisticsConfigSaveService {

    @Autowired
    private StCBnWarehouseLogisticsConfigMapper stCBnWarehouseLogisticsConfigMapper;
    @Autowired
    private StCBnColumnListMapper stCBnColumnListMapper;

    public ValueHolder save(QuerySession querySession) throws NDSException {
        ValueHolder valueHolder = new ValueHolder();
        DefaultWebEvent event = querySession.getEvent();
        User user = querySession.getUser();
        List<String> nullKeyList = new ArrayList<>();
        JSONObject param = JsonUtils.filterNull((JSONObject) event.getParameterValue("param"), nullKeyList);
        JSONObject fixColumn = param.getJSONObject("fixcolumn");
        String bnTable = fixColumn.getString(StConstant.ST_C_BN_WAREHOUSE_LOGISTICS_CONFIG);
        StCBnWarehouseLogisticsConfigDO bnWarehouseLogisticsConfigDO = JsonUtils.parseJSON(StCBnWarehouseLogisticsConfigDO.class, bnTable);
        // warehouseCode logisticsName 这两个不能为空。其他不关注
        if (StringUtils.isBlank(bnWarehouseLogisticsConfigDO.getLogisticsCode()) || StringUtils.isBlank(bnWarehouseLogisticsConfigDO.getWarehouseCode()) ||
                StringUtils.isBlank(bnWarehouseLogisticsConfigDO.getLogisticsName()) || StringUtils.isBlank(bnWarehouseLogisticsConfigDO.getWarehouseName()) ||
                StringUtils.isBlank(bnWarehouseLogisticsConfigDO.getBnLogistics())) {
            valueHolder.put("code", ResultCode.FAIL);
            valueHolder.put("message", "必填字段缺失");
            return valueHolder;
        }
        List<StCBnWarehouseLogisticsConfigDO> warehouseLogisticsConfigDOList =
                stCBnWarehouseLogisticsConfigMapper.queryByWarehouseCodeAndLogisticsCode(bnWarehouseLogisticsConfigDO.getWarehouseCode(), bnWarehouseLogisticsConfigDO.getLogisticsCode());
        if (CollectionUtils.isNotEmpty(warehouseLogisticsConfigDOList)) {
            valueHolder.put("code", ResultCode.FAIL);
            valueHolder.put("message", "该仓库物流渠道已存在，请勿重复添加");
            return valueHolder;
        }
        // 此处写死projectId 20940L 由产品提供的。后面如果班牛重新创建新的工作表 则需要更改id
        List<StCBnColumnListDO> columnList = stCBnColumnListMapper.selectByProjectId(20940L);
        if (CollectionUtils.isEmpty(columnList)) {
            valueHolder.put("code", ResultCode.FAIL);
            valueHolder.put("message", "物流问题工单组件未查询到数据");
            return valueHolder;
        }
        // 根据StCBnColumnListDO中的name字段 来对columnList进行分组
        Map<String, List<StCBnColumnListDO>> columnNameMap = columnList.stream().collect(Collectors.groupingBy(StCBnColumnListDO::getName));
        List<StCBnColumnListDO> kdList = columnNameMap.get("快递");
        if (CollectionUtils.isEmpty(kdList)) {
            valueHolder.put("code", ResultCode.FAIL);
            valueHolder.put("message", "快递字段未查询到数据");
            return valueHolder;
        }
        StCBnColumnListDO kdColumn = kdList.get(0);
        String kdOptions = kdColumn.getOptions();
        if (StringUtils.isEmpty(kdOptions)) {
            valueHolder.put("code", ResultCode.FAIL);
            valueHolder.put("message", "快递字段options为空");
            return valueHolder;
        }
        // kdOptions 转换成JSONArray
        List<Options> optionsList = JSONUtil.toList(kdOptions, Options.class);
        Map<String, List<Options>> kdOptionsMap = optionsList.stream().collect(Collectors.groupingBy(Options::getTitle));
        String bnLogistics = bnWarehouseLogisticsConfigDO.getBnLogistics();
        List<Options> bnLogisticsOptions = kdOptionsMap.get(bnLogistics);
        if (CollectionUtils.isEmpty(bnLogisticsOptions)) {
            valueHolder.put("code", ResultCode.FAIL);
            valueHolder.put("message", "快递字段options未查询到数据");
            return valueHolder;
        }
        Options bnLogisticsOption = bnLogisticsOptions.get(0);
        bnWarehouseLogisticsConfigDO.setId(ModelUtil.getSequence(StConstant.ST_C_BN_WAREHOUSE_LOGISTICS_CONFIG));
        bnWarehouseLogisticsConfigDO.setBnLogisticsId(bnLogisticsOption.getId());
        StBeanUtils.makeCreateField(bnWarehouseLogisticsConfigDO, user);
        stCBnWarehouseLogisticsConfigMapper.insert(bnWarehouseLogisticsConfigDO);
        valueHolder.put("code", ResultCode.SUCCESS);
        valueHolder.put("message", "保存成功！");
        return ValueHolderUtils.getSuccessValueHolderByMessage(bnWarehouseLogisticsConfigDO.getId(), StConstant.ST_C_BN_WAREHOUSE_LOGISTICS_CONFIG, "保存成功！");
    }

    @Data
    static class Options {
        private Long id;
        private String title;
    }

}
