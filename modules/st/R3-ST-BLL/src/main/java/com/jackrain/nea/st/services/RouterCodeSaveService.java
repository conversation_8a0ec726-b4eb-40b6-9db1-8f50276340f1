package com.jackrain.nea.st.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.st.common.StConstant;
import com.jackrain.nea.st.mapper.StCRouterCodeMapper;
import com.jackrain.nea.st.mapper.StCRouterMapper;
import com.jackrain.nea.st.model.request.RouterRequest;
import com.jackrain.nea.st.model.table.StCRouterCodeDO;
import com.jackrain.nea.st.model.table.StCRouterDO;
import com.jackrain.nea.st.utils.JsonUtils;
import com.jackrain.nea.st.utils.StBeanUtils;
import com.jackrain.nea.st.utils.ValueHolderUtils;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * @Descroption 路由操作码策略
 * <AUTHOR>
 * @Date 2020/5/11 15:45
 */
@Component
@Slf4j
@Transactional
public class RouterCodeSaveService extends CommandAdapter {

    @Autowired
    private StCRouterMapper mapper;

    @Autowired
    private StCRouterCodeMapper stCRouterCodeMapper;

    public ValueHolder execute(QuerySession session) {
        //1.获取传入参数
        DefaultWebEvent event = session.getEvent();

        List<String> nullKeyList = new ArrayList<>();
        JSONObject param = JsonUtils.filterNull((JSONObject) event.getParameterValue("param"), nullKeyList);
        log.debug(LogUtil.format("快递路由策略保存Json:") + param.toJSONString());

        Long id = param.getLong("objid");
        JSONObject fixColumn = param.getJSONObject("fixcolumn");
        //json转换成对象
        String routerCode = fixColumn.getString(StConstant.TAB_ST_C_ROUTER_CODE);
        //新增或变更
        if (routerCode != null && id != null) {
            RouterRequest routerCmdRequest = JsonUtils.jsonParseClass(fixColumn, RouterRequest.class);
            if (id != -1) {
                return updateRouterCode(session, id, routerCmdRequest);
            } else {
                return addRouterCode(session, routerCmdRequest);
            }
        }
        throw new NDSException("传入数据异常！");
    }

    /**
     * @param session
     * @param routerCmdRequest
     * @return com.jackrain.nea.util.ValueHolder
     * @Descroption 路由操作码策略
     * <AUTHOR>
     * @Date 2020/5/11 15:45
     */
    private ValueHolder addRouterCode(QuerySession session, RouterRequest routerCmdRequest) {
        StCRouterDO stCRouter = routerCmdRequest.getStCRouterDO();

        //1.主表id及创建修改时间赋值
        stCRouter.setId(ModelUtil.getSequence(StConstant.TAB_ST_C_ROUTER));

        //2.主表数据业务更新校验
        checkRouter(stCRouter);

        //主表创建信息更新
        Timestamp timestamp = new Timestamp(System.currentTimeMillis());
        stCRouter.setAdClientId((long) session.getUser().getClientId());//所属公司
        stCRouter.setAdOrgId((long) session.getUser().getOrgId());//所属组织
        stCRouter.setOwnerid(Long.valueOf(session.getUser().getId()));//创建人id
        stCRouter.setOwnername(session.getUser().getName());//创建人名称
        stCRouter.setOwnerename(session.getUser().getEname());
        stCRouter.setCreationdate(timestamp);//创建时间
        stCRouter.setModifierid(Long.valueOf(session.getUser().getId()));//修改人id
        stCRouter.setModifiername(session.getUser().getName());//修改人名称
        stCRouter.setModifierename(session.getUser().getEname());//修改人名称
        stCRouter.setModifieddate(new Date());//修改时间
        //主表最后信息修改
        StBeanUtils.makeModifierField(stCRouter, session.getUser());
        //3.主表数据保存
        int insertResult = mapper.insert(stCRouter);
        if (insertResult <= 0) {
            return ValueHolderUtils.getFailValueHolder("保存失败");
        }

        /**
         * 子表新增
         */
        List<StCRouterCodeDO> stCRouterCodeDOList = routerCmdRequest.getStCRouterCodeDOList();
        if (stCRouterCodeDOList != null && stCRouterCodeDOList.size() > 0) {
            StCRouterCodeDO stCRouterCodeDO = stCRouterCodeDOList.get(0);

            stCRouterCodeDO.setId(ModelUtil.getSequence(StConstant.TAB_ST_C_ROUTER_CODE));
            stCRouterCodeDO.setStCRouterId(stCRouter.getId());
            stCRouterCodeDO.setSysType(stCRouter.getSysType());

            StBeanUtils.makeCreateField(stCRouterCodeDO, session.getUser());
            int insertItemResult = stCRouterCodeMapper.insert(stCRouterCodeDO);
            if (insertItemResult < 0) {
                return ValueHolderUtils.getFailValueHolder("快递路由策略明细保存失败");
            }
        }
        return ValueHolderUtils.getSuccessValueHolder(stCRouter.getId(), StConstant.TAB_ST_C_ROUTER);
    }

    /**
     * @param session
     * @param id
     * @param routerCmdRequest
     * @return com.jackrain.nea.util.ValueHolder
     * @Descroption 路由操作码策略
     * <AUTHOR>
     * @Date 2020/5/11 15:45
     */
    private ValueHolder updateRouterCode(QuerySession session, Long id, RouterRequest routerCmdRequest) {
        //1.更新前校验
        StCRouterDO existsRouter = mapper.selectById(id);

        if (existsRouter == null) {
            return ValueHolderUtils.getFailValueHolder("当前记录已不存在！");
        }

        StCRouterDO stCRouter = routerCmdRequest.getStCRouterDO();
        String sysType = null;
        //主表数据业务更新校验
        if (stCRouter != null) {
            //2.主表最后修改信息字段变更
            stCRouter.setId(id);
            checkRouter(stCRouter);
            StBeanUtils.makeModifierField(stCRouter, session.getUser());
            //3.更新主表信息
            if (mapper.updateById(stCRouter) <= 0) {
                return ValueHolderUtils.getFailValueHolder("路由操作码策略更新失败");
            }
            sysType   = stCRouter.getSysType();
        }

        /**
         * 子表更新
         */
        List<StCRouterCodeDO> stCRouterCodeDOList = routerCmdRequest.getStCRouterCodeDOList();
        if (stCRouterCodeDOList != null && !stCRouterCodeDOList.isEmpty()) {
            for (StCRouterCodeDO stCRouterCodeDO : stCRouterCodeDOList) {
                stCRouterCodeDO.setSysType(sysType);
                if (stCRouterCodeDO.getId() > 0) {
                    //路由操作码策略明细修改
                    stCRouterCodeDO.setStCRouterId(id);
                    StCRouterCodeDO existsRouterCode = stCRouterCodeMapper.selectById(stCRouterCodeDO.getId());
                    if (existsRouterCode == null) {
                        return ValueHolderUtils.getFailValueHolder("当前记录已不存在！");
                    }

                    if (stCRouterCodeDO.getCode() == null) {
                        stCRouterCodeDO.setCode(existsRouterCode.getCode());
                    }

                    checkRouterCode(stCRouterCodeDO);

                    StBeanUtils.makeModifierField(stCRouterCodeDO, session.getUser());
                    if (stCRouterCodeMapper.updateById(stCRouterCodeDO) < 0) {
                        return ValueHolderUtils.getFailValueHolder("店铺策略明细修改失败");
                    }
                } else {
                    //路由操作码策略明细新增保存
                    stCRouterCodeDO.setStCRouterId(id);
                    stCRouterCodeDO.setId(ModelUtil.getSequence(StConstant.TAB_ST_C_ROUTER_CODE));
                    if (stCRouterCodeDO.getCode() == null) {
                        throw new NDSException("操作码不允许留空！");
                    }

                    checkRouterCode(stCRouterCodeDO);

                    StBeanUtils.makeCreateField(stCRouterCodeDO, session.getUser());//创建信息
                    if (stCRouterCodeMapper.insert(stCRouterCodeDO) < 0) {
                        return ValueHolderUtils.getFailValueHolder("路由操作码策略明细保存失败");
                    }
                }
            }
            // 更新操作时间
            StCRouterDO stCRouterNewDO = new StCRouterDO();
            stCRouterNewDO.setId(id);
            StBeanUtils.makeModifierField(stCRouterNewDO, session.getUser());
            mapper.updateById(stCRouterNewDO);
        }
        return ValueHolderUtils.getSuccessValueHolder(id, StConstant.TAB_ST_C_ROUTER);
    }

    /**
     * @param stCRouterCode
     */
    private void checkRouterCode(StCRouterCodeDO stCRouterCode) {
        if (stCRouterCodeMapper.selectCountByEnameAndId(stCRouterCode.getId(), stCRouterCode.getEname()) != 0) {
            throw new NDSException("方案名称" + stCRouterCode.getEname() + "已存在!");
        }

        if (stCRouterCodeMapper.selectCountByCodeAndSysType(stCRouterCode.getId(), stCRouterCode.getCode(), stCRouterCode.getSysType()) != 0) {
            throw new NDSException("路由系统操作码已存在，不允许重复设置！");
        }
    }

    /**
     * @param stCRouter
     */
    private void checkRouter(StCRouterDO stCRouter) {
        if (mapper.selectCountByEnameAndId(stCRouter.getId(), stCRouter.getEname()) != 0) {
            throw new NDSException("方案名称" + stCRouter.getEname() + "已存在!");
        }

        if (mapper.selectCountBySysTypeAndId(stCRouter.getId(), stCRouter.getSysType()) != 0) {
            throw new NDSException("路由系统已存在，不允许重复设置！");
        }
    }
}
