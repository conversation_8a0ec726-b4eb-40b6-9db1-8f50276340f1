//package com.jackrain.nea.st.services;
//
//import com.alibaba.fastjson.JSON;
//import com.alibaba.fastjson.JSONArray;
//import com.alibaba.fastjson.JSONObject;
//import com.alibaba.fastjson.TypeReference;
//import com.alibaba.fastjson.parser.Feature;
//import com.google.common.collect.Lists;
//import com.jackrain.nea.config.PropertiesConf;
//import com.jackrain.nea.constants.ResultCode;
//import com.jackrain.nea.cpext.model.table.CpShop;
//import com.jackrain.nea.exception.NDSException;
//import com.jackrain.nea.ext.sequence.util.SequenceGenUtil;
//import com.jackrain.nea.model.util.ModelUtil;
//import com.jackrain.nea.ps.api.result.PsSkuResult;
//import com.jackrain.nea.ps.api.table.PsCSkuExt;
//import com.jackrain.nea.psext.model.table.PsCPro;
//import com.jackrain.nea.psext.model.table.PsCSku;
//import com.jackrain.nea.sg.basic.api.SgLockSkuStrategyBasicCmd;
//import com.jackrain.nea.sg.oms.model.request.SgChannelProductQueryForSTRequest;
//import com.jackrain.nea.sg.oms.model.result.SgChannelProductQueryForSTResult;
//import com.jackrain.nea.st.common.StConstant;
//import com.jackrain.nea.st.mapper.StCLockSkuStrategyItemMapper;
//import com.jackrain.nea.st.mapper.StCLockSkuStrategyMapper;
//import com.jackrain.nea.st.model.table.StCLockSkuStrategyDO;
//import com.jackrain.nea.st.model.table.StCLockSkuStrategyItemDO;
//import com.jackrain.nea.st.rpc.RpcCpService;
//import com.jackrain.nea.st.rpc.RpcIpService;
//import com.jackrain.nea.st.rpc.RpcPsService;
//import com.jackrain.nea.st.rpc.RpcSgService;
//import com.jackrain.nea.st.utils.*;
//import com.jackrain.nea.sys.CommandAdapter;
//import com.jackrain.nea.util.ApplicationContextHandle;
//import com.jackrain.nea.util.ValueHolder;
//import com.jackrain.nea.web.query.DefaultWebEvent;
//import com.jackrain.nea.web.query.QuerySession;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.commons.collections.CollectionUtils;
//import org.apache.commons.lang3.StringUtils;
//import org.apache.dubbo.config.annotation.Reference;
//import org.springframework.beans.BeanUtils;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.stereotype.Component;
//import org.springframework.transaction.annotation.Transactional;
//
//import java.util.*;
//import java.util.stream.Collectors;
//
///**
// * Bll层-店铺锁库条码特殊设置新增保存业务逻辑
// *
// * <AUTHOR> 陈俊明
// * @since : 2019-03-12
// * create at : 2019-03-12 17:41
// */
//@Component
//@Slf4j
//@Transactional
//public class LockSkuStrategySaveService extends CommandAdapter {
//    @Autowired
//    private StCLockSkuStrategyMapper stCLockSkuStrategyMapper;
//
//    @Autowired
//    private StCLockSkuStrategyItemMapper stCLockSkuStrategyItemMapper;
//
//    @Autowired
//    private StCLockSkuStrategyItemService skuStrategyItemService;
//
//    @Autowired
//    private RpcPsService rpcPsService;
//
//    @Autowired
//    private RpcIpService rpcIpService;
//
//    @Autowired
//    private RpcSgService rpcSgService;
//
//    @Autowired
//    private RpcCpService rpcCpService;
//
//    private String strTableMain = "ST_C_LOCK_SKU_STRATEGY";
//    private String strTableList = "ST_C_LOCK_SKU_STRATEGY_ITEM";
//
//    @Reference(group = "sg", version = "1.0")
//    private SgLockSkuStrategyBasicCmd skuStrategyBasicCmd;
//
//    @Autowired
//    private PropertiesConf propertiesConf;
//
//    @Autowired
//    private GeneralStrategyCheckService generalStrategyCheckService;
//
//    /**
//     * 新增和修改
//     *
//     * @param querySession 参数封装
//     * @return 返回状态
//     * @throws NDSException 异常信息
//     */
//    @Override
//    public ValueHolder execute(QuerySession querySession) throws NDSException {
//        ValueHolder valueHolder = new ValueHolder();
//        DefaultWebEvent event = querySession.getEvent();
//        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(
//                event.getParameterValue("param"), "yyyy-MM-dd HH:mm:ss"),
//                Feature.OrderedField);
//        if (param != null) {
//            Long id = param.getLong("objid");
//            JSONObject fixColumn = param.getJSONObject("fixcolumn");
//            if (fixColumn != null) {
//                JSONObject lockSkuStrategyMap = fixColumn.getJSONObject(strTableMain);
//                JSONArray lockSkuStrategyItemMap = fixColumn.getJSONArray(strTableList);
//                if (id != null && id < 0) {
//                    valueHolder = saveLockSkuStrategy(lockSkuStrategyMap, lockSkuStrategyItemMap, valueHolder, querySession, id);
//                } else {
//                    valueHolder = updateLockSkuStrategy(lockSkuStrategyMap, lockSkuStrategyItemMap, valueHolder, querySession, id);
//                }
//            }
//        } else {
//            throw new NDSException("参数为空！");
//        }
//
//        return valueHolder;
//    }
//
//    /**
//     * 新增操作
//     *
//     * @param lockSkuStrategyMap     主表数据
//     * @param lockSkuStrategyItemMap 子表数据
//     * @param holder                 响应数据
//     * @param querySession           封装数据
//     * @param objid                  id
//     * @return 返回状态
//     */
//    private ValueHolder saveLockSkuStrategy(JSONObject lockSkuStrategyMap,
//                                            JSONArray lockSkuStrategyItemMap,
//                                            ValueHolder holder,
//                                            QuerySession querySession, Long objid) {
//        StCLockSkuStrategyDO stCLockSkuStrategyDO = JsonUtils.jsonParseClass(lockSkuStrategyMap, StCLockSkuStrategyDO.class);
//
//        //状态数据检查
//        if (!checkStatus(stCLockSkuStrategyDO, objid, holder)) {
//            return holder;
//        }
//
//        if (stCLockSkuStrategyDO != null) {
//            stCLockSkuStrategyDO.setId(ModelUtil.getSequence(strTableMain));
//            // 方案ID
//            JSONObject sequence = new JSONObject();
//            String seqProductStrategy = SequenceGenUtil.generateSquence("SEQ_LOCK_SKU_STRATEGY", sequence, querySession.getUser().getLocale(), false);
//            stCLockSkuStrategyDO.setEname(seqProductStrategy);
//            stCLockSkuStrategyDO.setEstatus(StConstant.CON_BILL_STATUS_01);
//            StBeanUtils.makeCreateField(stCLockSkuStrategyDO, querySession.getUser());
//            // 修改店铺信息
//            this.syncCpCShopInfo(stCLockSkuStrategyDO);
//            if (stCLockSkuStrategyMapper.insert(stCLockSkuStrategyDO) < 0) {
//                holder = ValueHolderUtils.getFailValueHolder("保存失败！");
//                return holder;
//            } else {
//                //推送ES数据
//                try {
//                    DatasToEsUtils.insertLoclSkuEsData(stCLockSkuStrategyDO, null, StConstant.TAB_ST_C_LOCK_SKU_STRATEGY);
//                } catch (Exception ex) {
//                    log.debug(LogUtil.format("店铺锁库条码特殊设置策略主表推数据到ES失败：" + ex.toString());
//                }
//            }
//        } else {
//            holder = ValueHolderUtils.getFailValueHolder("保存失败！");
//            return holder;
//        }
//        JSONObject sync2PgDataSourceObj = new JSONObject();
//        sync2PgDataSourceObj.put("master", stCLockSkuStrategyDO);
//        JSONArray saveItems = new JSONArray();
//        LockSkuStrategySaveService bean = ApplicationContextHandle.getBean(LockSkuStrategySaveService.class);
//        if (!bean.saveLockSkuStrategyItem(holder, querySession, stCLockSkuStrategyDO, lockSkuStrategyItemMap, saveItems)) {
//            return holder;
//        }
//        // 新增的items,
//        sync2PgDataSourceObj.put("items", saveItems);
//        skuStrategyBasicCmd.insert(sync2PgDataSourceObj);
//        holder = ValueHolderUtils.getSuccessValueHolder(stCLockSkuStrategyDO.getId(), strTableMain);
//        return holder;
//    }
//
//    private boolean checkStatus(StCLockSkuStrategyDO stCLockSkuStrategyDO, Long objid, ValueHolder valueHolder) {
//        if (objid > 0) {
//            StCLockSkuStrategyDO stCLockSkuStrategyDO1 = stCLockSkuStrategyMapper.selectById(objid);
//            if (stCLockSkuStrategyDO1 != null) {
//                int iStatus = stCLockSkuStrategyDO1.getEstatus();
//                if (iStatus != 1) {
//                    valueHolder.put("code", -1);
//                    valueHolder.put("message", "单据处于未审核状态才能进行编辑！");
//                    return false;
//                }
//            } else {
//                valueHolder.put("code", -1);
//                valueHolder.put("message", "数据已不存在");
//                return false;
//            }
//
//            if (stCLockSkuStrategyDO != null) {
//                if (stCLockSkuStrategyDO.getLockEtime() == null) {
//                    stCLockSkuStrategyDO.setLockEtime(stCLockSkuStrategyDO1.getLockEtime());
//                }
//                if (stCLockSkuStrategyDO.getLockBtime() == null) {
//                    stCLockSkuStrategyDO.setLockBtime(stCLockSkuStrategyDO1.getLockBtime());
//                }
//            }
//        }
//
//        /**
//         * 保存前进行的判断
//         * 代销运费策略方案的 生效日期( 结束日期不能大于起始日期)
//         */
//        if (stCLockSkuStrategyDO != null) {
////            if(stCLockSkuStrategyDO.getCpCShopId() != null
////                  && stCLockSkuStrategyMapper.selectStCLockSkuStrategyByCpCShopId(stCLockSkuStrategyDO.getCpCShopId()) != 0){
////                valueHolder.put("code", -1);
////                valueHolder.put("message", "店铺名称已存在!");
////                return false;
////            }
//            if (stCLockSkuStrategyDO.getLockBtime() != null && stCLockSkuStrategyDO.getLockEtime() != null) {
//                if (stCLockSkuStrategyDO.getLockEtime().before(stCLockSkuStrategyDO.getLockBtime())) {
//                    valueHolder.put("code", -1);
//                    valueHolder.put("message", "方案的结束日期不能小于生效日期！");
//                    return false;
//                }
//
//                if (stCLockSkuStrategyDO.getLockEtime().before(new Date())) {
//                    valueHolder.put("code", -1);
//                    valueHolder.put("message", "方案的结束日期不能小于当前日期！");
//                    return false;
//                }
//            }
//        }
//
//        return true;
//    }
//
//    /**
//     * 更新操作
//     *
//     * @param lockSkuStrategyMap     主表数据
//     * @param lockSkuStrategyItemMap 子表数据
//     * @param holder                 响应数据
//     * @param querySession           封装数据
//     * @param objid                  主表id
//     * @return 返回状态
//     */
//    private ValueHolder updateLockSkuStrategy(JSONObject lockSkuStrategyMap,
//                                              JSONArray lockSkuStrategyItemMap,
//                                              ValueHolder holder,
//                                              QuerySession querySession,
//                                              Long objid) {
//
//        JSONObject sync2PgDataSourceObj = new JSONObject();
//        //主表更新，objid就是主表ID
//        if (lockSkuStrategyMap != null && !lockSkuStrategyMap.isEmpty()) {
//            StCLockSkuStrategyDO stCConsignFeeDO = JSON.parseObject(lockSkuStrategyMap.toJSONString(),
//                    new TypeReference<StCLockSkuStrategyDO>() {
//                    });
//            //状态数据检查
//            if (!checkStatus(stCConsignFeeDO, objid, holder)) {
//                return holder;
//            }
//            stCConsignFeeDO.setId(objid);
//            StBeanUtils.makeModifierField(stCConsignFeeDO, querySession.getUser());
//            // 修改店铺信息
//            this.syncCpCShopInfo(stCConsignFeeDO);
//            if (stCLockSkuStrategyMapper.updateById(stCConsignFeeDO) < 0) {
//                holder = ValueHolderUtils.getFailValueHolder("保存失败！");
//                return holder;
//            } else {
//                sync2PgDataSourceObj.put("master", stCConsignFeeDO);
//                //推送ES数据
//                try {
//                    //做更新的需要先查询更新后数据库的实体在推ES
//                    stCConsignFeeDO = stCLockSkuStrategyMapper.selectById(objid);
//                    DatasToEsUtils.insertLoclSkuEsData(stCConsignFeeDO, null, StConstant.TAB_ST_C_LOCK_SKU_STRATEGY);
//                } catch (Exception ex) {
//                    log.debug(LogUtil.format("店铺锁库条码特殊设置策略主表推数据到ES失败：" + ex.toString());
//                }
//            }
//        }
//
//        JSONArray saveItems = new JSONArray();
//        //判断子表数据是否存在
//        if (lockSkuStrategyItemMap != null && !lockSkuStrategyItemMap.isEmpty()) {
//            //状态数据检查
//            if (!checkStatus(null, objid, holder)) {
//                return holder;
//            }
//            StCLockSkuStrategyDO stCLockSkuStrategyDO = stCLockSkuStrategyMapper.selectById(objid);
//            LockSkuStrategySaveService bean = ApplicationContextHandle.getBean(LockSkuStrategySaveService.class);
//            if (!bean.saveLockSkuStrategyItem(holder, querySession, stCLockSkuStrategyDO, lockSkuStrategyItemMap, saveItems)) {
//                return holder;
//            }
//        }
//        // 新增明细，不存在修改明细，删除的已通过明细的删除，已同步至pg库
//        sync2PgDataSourceObj.put("saveItems", saveItems);
//        // todo 同步锁库条数条码策略至PG库
//        skuStrategyBasicCmd.update(sync2PgDataSourceObj);
//        holder = ValueHolderUtils.getSuccessValueHolder(objid, strTableMain);
//        return holder;
//    }
//
//    /**
//     * @Description 校验商品ID和商商品名称不能重复(包含新增和编辑)
//     **/
//    private ValueHolder checkOnlyIdAndTitleAllFunc(ValueHolder holder, Long objid, List<StCLockSkuStrategyItemDO> stCLockSkuStrategyItemDOList) {
//        Map judgeMap = new HashMap<>(); //存储前台传回的商品ID和商品名称组合
//        for (StCLockSkuStrategyItemDO stCLockSkuStrategyItemDO : stCLockSkuStrategyItemDOList) {
//            Long id = stCLockSkuStrategyItemDO.getId();
//            String ptProId = stCLockSkuStrategyItemDO.getPtProId();//商品Id
//            String ptProTitle = stCLockSkuStrategyItemDO.getPtProTitle();//商品名称
//            String proIdAndTitle = "";//组合商品id和名称
//            if (StringUtils.isNotEmpty(ptProId) && !"".equals(ptProTitle)) {
//                proIdAndTitle = ptProId + "-" + ptProId;
//            }
//            //需要判断前台传回的明细集合组合 商品ID和商品名称 是否存在重复的校验
//            if (judgeMap.get(proIdAndTitle) != null) {
//                throw new NDSException("商品明细的商品ID和商品名称组合已经存在，不能重复保存！");
//            }
//            judgeMap.put(proIdAndTitle, proIdAndTitle);
//            if (id < 0) {
//                //明细新增需要以书库未准判断是否存在同个组合校验唯一性：
//                //商品明细的商品ID和他的商品名称要控制唯一不能重复
//                if (checkOnlyIdAndTitleFunc(objid, ptProId, ptProTitle)) {
//                    throw new NDSException("商品明细的商品ID和商品名称组合已经存在，不能重复保存！");
//                }
//            }
//        }
//
//        return holder;
//    }
//
//    /**
//     * @Description 校验商品ID和商商品名称不能重复
//     **/
//    private boolean checkOnlyIdAndTitleFunc(Long id, String ptProId, String ptProTitle) {
//        List<StCLockSkuStrategyItemDO> stCLockSkuStrategyItemDO = stCLockSkuStrategyItemMapper.selectDatasByIdAndTitle(id, ptProId, ptProTitle);
//        if (stCLockSkuStrategyItemDO != null && stCLockSkuStrategyItemDO.size() > 0) {
//            return true;
//        }
//        return false;
//    }
//
//
//    /**
//     * @Description 商品编码ID获取商品信息
//     **/
//    private void setProSku(StCLockSkuStrategyItemDO stCLockSkuStrategyItemDO) {
//
//        if (stCLockSkuStrategyItemDO.getPsCProId() != null) {
//            PsCPro psCPro = rpcPsService.queryProByID(stCLockSkuStrategyItemDO.getPsCProId());
//            if (psCPro != null) {
//                stCLockSkuStrategyItemDO.setPsCProEcode(psCPro.getEcode());
//                stCLockSkuStrategyItemDO.setPsCProEname(psCPro.getEname());
//            }
//        }
//
//        if (stCLockSkuStrategyItemDO.getPsCSkuId() != null) {
//            PsCSku psCSku = rpcPsService.getSkuById(stCLockSkuStrategyItemDO.getPsCSkuId());
//            if (psCSku != null) {
//                stCLockSkuStrategyItemDO.setPsCSkuEcode(psCSku.getEcode());
//            }
//        }
//    }
//
//    /**
//     * 子表新增
//     */
//    public boolean saveLockSkuStrategyItem(ValueHolder holder, QuerySession querySession,
//                                           StCLockSkuStrategyDO stCLockSkuStrategyDO, JSONArray lockSkuStrategyItemMap, JSONArray saveItems) {
//        // 旧数据取得，防止重复
//        List<StCLockSkuStrategyItemDO> stCLockSkuStrategyItemDOOldList = stCLockSkuStrategyItemMapper.listByItemId(stCLockSkuStrategyDO.getId());
//        Map<String, StCLockSkuStrategyItemDO> lockSkuStrategyItemDOOldMap = new HashMap<>();
//
//        // 数据去重
//        Map<String, StCLockSkuStrategyItemDO> uniqueMap = new HashMap<>();
//
//        for (StCLockSkuStrategyItemDO stCLockSkuStrategyItemDO : stCLockSkuStrategyItemDOOldList) {
//            String key = stCLockSkuStrategyItemDO.getPtProId() + " " + stCLockSkuStrategyItemDO.getPtSkuId()
//                    + " " + stCLockSkuStrategyItemDO.getPsCProEcode() + " " + stCLockSkuStrategyItemDO.getPsCSkuEcode();
//            lockSkuStrategyItemDOOldMap.put(key, stCLockSkuStrategyItemDO);
//        }
//        if (lockSkuStrategyItemMap != null) {
//            List<StCLockSkuStrategyItemDO> stCLockSkuStrategyItemDOList = JSON.parseObject(lockSkuStrategyItemMap.toJSONString(),
//                    new TypeReference<ArrayList<StCLockSkuStrategyItemDO>>() {
//                    });
//            if (stCLockSkuStrategyItemDOList != null) {
//                //zyj-20191223 start
//                if (stCLockSkuStrategyDO.getCpCShopId() == null) {
//                    holder.put("code", ResultCode.FAIL);
//                    holder.put("message", "店铺为空！");
//                    return false;
//                }
//                //获取渠道商品
//                List<SgChannelProductQueryForSTResult> resultList = generalStrategyCheckService.checkParam(
//                        StConstant.TAB_ST_C_LOCK_SKU_STRATEGY, stCLockSkuStrategyDO.getCpCShopId(), stCLockSkuStrategyItemDOList);
//                if (CollectionUtils.isEmpty(resultList)) {
//                    holder.put("code", ResultCode.FAIL);
//                    holder.put("message", "渠道商品查询不到！");
//                    return false;
//                }
//                if (log.isDebugEnabled()) {
//                    log.debug(LogUtil.format(" 获取渠道商品出参：" + JSONObject.toJSONString(resultList));
//                }
//                String pageNum = propertiesConf.getProperty("r3.st.queryChannelProduct.pageNum");
//                if (StringUtils.isEmpty(pageNum)) {
//                    pageNum = "2000";
//                }
//                LockSkuStrategySaveService bean =
//                        ApplicationContextHandle.getBean(LockSkuStrategySaveService.class);
//                List<StCLockSkuStrategyItemDO> itemDoInsertList = new ArrayList<>();
//                ListPageUtil pager = new ListPageUtil(resultList, Integer.valueOf(pageNum));
//                for (int i = 1; i <= pager.getPageCount(); i++) {
//                    List<SgChannelProductQueryForSTResult> list = pager.getPagedList(i);
//                    Long[] idList = ModelUtil.getSequence(strTableList, list.size());
//                    for (int j = 0; j < list.size(); j++) {
//                        SgChannelProductQueryForSTResult sgChannelProductQueryForSTResult = list.get(j);
//                        StCLockSkuStrategyItemDO stCLockSkuStrategyItemDO = new StCLockSkuStrategyItemDO();
//                        stCLockSkuStrategyItemDO.setId(idList[j]);
//                        stCLockSkuStrategyItemDO.setStCLockSkuStrategyId(stCLockSkuStrategyDO.getId());
//                        //平台商品id
//                        stCLockSkuStrategyItemDO.setPtProId(sgChannelProductQueryForSTResult.getNumiid());
//                        //平台skuid
//                        stCLockSkuStrategyItemDO.setPtSkuId(sgChannelProductQueryForSTResult.getSkuId());
//                        //系统条码
//                        stCLockSkuStrategyItemDO.setPsCSkuEcode(sgChannelProductQueryForSTResult.getPsCSkuEcode());
//                        //商品信息
//                        stCLockSkuStrategyItemDO.setPsCSkuId(sgChannelProductQueryForSTResult.getPsCSkuId());
//                        stCLockSkuStrategyItemDO.setPsCProId(sgChannelProductQueryForSTResult.getPsCProId());
//                        stCLockSkuStrategyItemDO.setPsCProEcode(sgChannelProductQueryForSTResult.getPsCProEcode());
//                        stCLockSkuStrategyItemDO.setPsCProEname(sgChannelProductQueryForSTResult.getPsCProEname());
//                        //颜色尺寸信息
//                        stCLockSkuStrategyItemDO.setGbcode(sgChannelProductQueryForSTResult.getGbcode()); //国标码
//                        stCLockSkuStrategyItemDO.setPsCClrId(sgChannelProductQueryForSTResult.getPsCSpec1Id()); //颜色id
//                        stCLockSkuStrategyItemDO.setPsCClrEcode(sgChannelProductQueryForSTResult.getPsCSpec1Ecode()); //颜色编码
//                        stCLockSkuStrategyItemDO.setPsCClrEname(sgChannelProductQueryForSTResult.getPsCSpec1Ename()); //颜色名称
//                        stCLockSkuStrategyItemDO.setPsCSizeId(sgChannelProductQueryForSTResult.getPsCSpec2Id()); //尺寸id
//                        stCLockSkuStrategyItemDO.setPsCSizeEcode(sgChannelProductQueryForSTResult.getPsCSpec2Ecode()); //尺寸编码
//                        stCLockSkuStrategyItemDO.setPsCSizeEname(sgChannelProductQueryForSTResult.getPsCSpec2Ecode()); //尺寸名称
//                        //主表信息冗余
//                        stCLockSkuStrategyItemDO.setPlanName(stCLockSkuStrategyDO.getEname());
//                        stCLockSkuStrategyItemDO.setStatus(stCLockSkuStrategyDO.getEstatus());
//                        stCLockSkuStrategyItemDO.setBeginTime(stCLockSkuStrategyDO.getLockBtime());
//                        stCLockSkuStrategyItemDO.setEndTime(stCLockSkuStrategyDO.getLockEtime());
//                        stCLockSkuStrategyItemDO.setCpCShopId(stCLockSkuStrategyDO.getCpCShopId());
//                        stCLockSkuStrategyItemDO.setCpCShopEcode(stCLockSkuStrategyDO.getCpCShopEcode());
//                        stCLockSkuStrategyItemDO.setCpCShopTitle(stCLockSkuStrategyDO.getCpCShopTitle());
//                        stCLockSkuStrategyItemDO.setMainCreationdate(stCLockSkuStrategyDO.getCreationdate());
//                        String key = stCLockSkuStrategyItemDO.getPtProId() + " " + stCLockSkuStrategyItemDO.getPtSkuId()
//                                + " " + stCLockSkuStrategyItemDO.getPsCProEcode() + " " + stCLockSkuStrategyItemDO.getPsCSkuEcode();
//                        //数据去重
//                        if (uniqueMap.containsKey(key)) {
//                            continue;
//                        } else {
//                            uniqueMap.put(key, stCLockSkuStrategyItemDO);
//                        }
//                        if (!lockSkuStrategyItemDOOldMap.containsKey(key)) {
//                            StBeanUtils.makeCreateField(stCLockSkuStrategyItemDO, querySession.getUser());
//                            itemDoInsertList.add(stCLockSkuStrategyItemDO);
//                        }
//                    }
//                }
//                if (CollectionUtils.isNotEmpty(itemDoInsertList)) {
//                    bean.batchSave(stCLockSkuStrategyDO, itemDoInsertList, saveItems);
//                }
//            } else {
//                holder.put("code", -1);
//                holder.put("message", "明细JSON转换失败，保存失败！");
//                return false;
//            }
//        }
//        return true;
//    }
//
//    @Transactional(rollbackFor = Exception.class)
//    public void batchSave(StCLockSkuStrategyDO skuStrategyDO,
//                          List<StCLockSkuStrategyItemDO> itemDoInsertList,
//                          JSONArray saveItems) {
//        try {
//            if (CollectionUtils.isNotEmpty(itemDoInsertList)) {
//                skuStrategyItemService.saveBatch(itemDoInsertList, 500);
//                saveItems.addAll(itemDoInsertList);
//            }
//        } catch (Exception e) {
//            throw new NDSException("批量插入异常", e);
//        }
//
//        if (CollectionUtils.isNotEmpty(itemDoInsertList)) {
//            try {
//                DatasToEsUtils.insertLoclSkuEsData(skuStrategyDO, itemDoInsertList, StConstant.TAB_ST_C_LOCK_SKU_STRATEGY_ITEM);
//                if (log.isDebugEnabled()) {
//                    log.debug(LogUtil.format("店铺锁库条码特殊设置明细表推数据到ES成功》》》》》》》》");
//                }
//            } catch (Exception ex) {
//                log.error(LogUtil.format("店铺锁库条码特殊设置明细表推数据到ES失败：" + ex.toString());
//            }
//        }
//    }
//
//    /**
//     * 校验条码 和平台条码id 是否唯一
//     *
//     * @return
//     */
//    private boolean checkProSku(Long objid, Long prsku, String ptSku) {
//        List<StCLockSkuStrategyItemDO> stCLockSkuStrategyItemDOS = stCLockSkuStrategyItemMapper.selectByPsSkuIdAndPtSkuId(objid, ptSku, prsku);
//        if (!CollectionUtils.isEmpty(stCLockSkuStrategyItemDOS)) {
//            return true;
//        }
//        return false;
//    }
//
//    /**
//     * @Description 商品编码查找对应的条码档案
//     **/
//    private List<StCLockSkuStrategyItemDO> getLockSkuStrategyItemList(List<StCLockSkuStrategyItemDO> stCLockSkuStrategyItemDOList
//            , Map<Long, Integer> proSizeMap) {
//        List<StCLockSkuStrategyItemDO> lockSkuStrategyItemList = Lists.newArrayList();
//        for (StCLockSkuStrategyItemDO stCLockSkuStrategyItemDO : stCLockSkuStrategyItemDOList) {
//            if (stCLockSkuStrategyItemDO.getId() == -1 &&
//                    stCLockSkuStrategyItemDO.getPsCProId() != null && stCLockSkuStrategyItemDO.getPsCSkuId() == null) {
//                List<PsCSkuExt> psCSkuExts = new ArrayList<>();
//                PsSkuResult psSkuResult = rpcPsService.getSkuResultByProId(stCLockSkuStrategyItemDO.getPsCProId());
//                if (!CollectionUtils.isEmpty(psSkuResult.getSkuList())) {
//                    psCSkuExts = psSkuResult.getSkuList();
//                }
//                for (PsCSkuExt psCSkuExt : psCSkuExts) {
//                    StCLockSkuStrategyItemDO lockSkuStrategyItem = new StCLockSkuStrategyItemDO();
//                    BeanUtils.copyProperties(stCLockSkuStrategyItemDO, lockSkuStrategyItem);
//                    lockSkuStrategyItem.setPsCSkuId(psCSkuExt.getId());
//                    lockSkuStrategyItemList.add(lockSkuStrategyItem);
//                }
//                proSizeMap.put(stCLockSkuStrategyItemDO.getPsCProId(), psCSkuExts.size());
//            } else {
//                lockSkuStrategyItemList.add(stCLockSkuStrategyItemDO);
//            }
//        }
//        return lockSkuStrategyItemList;
//    }
//
//    /**
//     * 根据店铺锁库条码特殊设置明细条件查询渠道商品
//     *
//     * @param stCLockSkuStrategyItemDOList
//     * @param stCLockSkuStrategyDO
//     * @return
//     */
//    private List<SgChannelProductQueryForSTResult> getChannelProduct(List<StCLockSkuStrategyItemDO> stCLockSkuStrategyItemDOList,
//                                                                     StCLockSkuStrategyDO stCLockSkuStrategyDO) {
//        List<SgChannelProductQueryForSTResult> newSgChannelProductQueryForSTResultList = new ArrayList<>();
//        //平台商品id
//        List<String> ptProIdList = stCLockSkuStrategyItemDOList.stream().filter(x -> x.getPtProId() != null).map(x -> String.valueOf(x.getPtProId())).collect(Collectors.toList());
//        //平台skuid
//        List<String> ptSkuIdList = stCLockSkuStrategyItemDOList.stream().filter(x -> x.getPtSkuId() != null).map(x -> String.valueOf(x.getPtSkuId())).collect(Collectors.toList());
//        //商品编码
//        List<Long> psCProIdList = stCLockSkuStrategyItemDOList.stream().filter(x -> x.getPsCProId() != null).map(x -> x.getPsCProId()).collect(Collectors.toList());
//        //系统条码
//        List<Long> psCSkuIdList = stCLockSkuStrategyItemDOList.stream().filter(x -> x.getPsCSkuId() != null).map(x -> x.getPsCSkuId()).collect(Collectors.toList());
//        SgChannelProductQueryForSTRequest sgChannelProductQueryForSTRequest = new SgChannelProductQueryForSTRequest();
//        sgChannelProductQueryForSTRequest.setCpCShopId(stCLockSkuStrategyDO.getCpCShopId() != null ? stCLockSkuStrategyDO.getCpCShopId() : null);
//        sgChannelProductQueryForSTRequest.setPtProIdList(CollectionUtils.isNotEmpty(ptProIdList) ? ptProIdList : null);
//        sgChannelProductQueryForSTRequest.setPtSkuIdList(CollectionUtils.isNotEmpty(ptSkuIdList) ? ptSkuIdList : null);
//        sgChannelProductQueryForSTRequest.setPsCProIdList(CollectionUtils.isNotEmpty(psCProIdList) ? psCProIdList : null);
//        sgChannelProductQueryForSTRequest.setPsCSkuIdList(CollectionUtils.isNotEmpty(psCSkuIdList) ? psCSkuIdList : null);
//        if (log.isDebugEnabled()) {
//            log.debug(LogUtil.format(" 【店铺锁库特殊设置策略】查询渠道商品入参:" + JSONObject.toJSONString(sgChannelProductQueryForSTRequest));
//        }
//        String pageNum = propertiesConf.getProperty("r3.st.queryChannelProduct.pageNum");
//        if (StringUtils.isEmpty(pageNum)) {
//            pageNum = "2000";
//        }
//        Integer count = rpcSgService.queryChannelProductCount(sgChannelProductQueryForSTRequest);
//        if (count.intValue() == 0) {
//            return new ArrayList<>();
//        }
//        sgChannelProductQueryForSTRequest.setPageNum(Integer.valueOf(pageNum));
//        ListPageUtil pageResultList = new ListPageUtil(count, Integer.valueOf(pageNum));
//        for (int i = 1; i <= pageResultList.getPageCount(); i++) {
//            List<SgChannelProductQueryForSTResult> tempList = rpcSgService.queryChannelProduct(sgChannelProductQueryForSTRequest);
//
//            if (tempList != null && !tempList.isEmpty()) {
//                newSgChannelProductQueryForSTResultList.addAll(tempList);
//                Optional<SgChannelProductQueryForSTResult> resultOp = tempList.stream().max(Comparator.comparingLong(SgChannelProductQueryForSTResult::getId));
//                if (resultOp.isPresent()) {
//                    SgChannelProductQueryForSTResult sgChannelProductQueryForSTResult = resultOp.get();
//                    sgChannelProductQueryForSTRequest.setId(sgChannelProductQueryForSTResult.getId());
//                }
//            }
//
//            if (i == pageResultList.getPageCount()) {
//                break;
//            }
//
//        }
//        return newSgChannelProductQueryForSTResultList;
//    }
//
//    /**
//     * 根据店铺锁库条码特殊设置明细条件查询渠道商品
//     *
//     * @param stCLockSkuStrategyItemDOList
//     * @param stCLockSkuStrategyDO
//     * @return
//     */
//    private List<SgChannelProductQueryForSTResult> getSingleChannelProduct(List<StCLockSkuStrategyItemDO> stCLockSkuStrategyItemDOList,
//                                                                           StCLockSkuStrategyDO stCLockSkuStrategyDO) {
//        List<SgChannelProductQueryForSTResult> sgChannelProductQueryForSTResultList = new ArrayList<>();
//        SgChannelProductQueryForSTRequest sgChannelProductQueryForSTRequest = new SgChannelProductQueryForSTRequest();
//        sgChannelProductQueryForSTRequest.setCpCShopId(stCLockSkuStrategyDO.getCpCShopId());
//        if (log.isDebugEnabled()) {
//            log.debug(LogUtil.format(" excel:" + JSONObject.toJSONString(stCLockSkuStrategyItemDOList));
//        }
//        for (StCLockSkuStrategyItemDO stCLockSkuStrategyItemDO : stCLockSkuStrategyItemDOList) {
//            sgChannelProductQueryForSTRequest.setPtSkuId(stCLockSkuStrategyItemDO.getPtSkuId() != null ? String.valueOf(stCLockSkuStrategyItemDO.getPtSkuId()) : null);
//            sgChannelProductQueryForSTRequest.setPtProId(stCLockSkuStrategyItemDO.getPtProId() != null ? String.valueOf(stCLockSkuStrategyItemDO.getPtProId()) : null);
//            sgChannelProductQueryForSTRequest.setPsCSkuId(stCLockSkuStrategyItemDO.getPsCSkuId() != null ? stCLockSkuStrategyItemDO.getPsCSkuId() : null);
//            sgChannelProductQueryForSTRequest.setPsCProId(stCLockSkuStrategyItemDO.getPsCProId() != null ? stCLockSkuStrategyItemDO.getPsCProId() : null);
//            if (log.isDebugEnabled()) {
//                log.debug(LogUtil.format(" 【店铺锁库特殊设置策略】查询渠道商品入参:" + JSONObject.toJSONString(sgChannelProductQueryForSTRequest));
//            }
//            sgChannelProductQueryForSTResultList.addAll(rpcSgService.queryChannelProduct(sgChannelProductQueryForSTRequest));
//        }
//        return sgChannelProductQueryForSTResultList;
//    }
//
//
//    /**
//     * 修改锁库条码策略上面的 店铺信息
//     *
//     * @param stCLockSkuStrategyDO
//     */
//    void syncCpCShopInfo(StCLockSkuStrategyDO stCLockSkuStrategyDO) {
//        // 假如店铺id 不是空，而且店铺名称是空的
//        if (stCLockSkuStrategyDO.getCpCShopId() != null) {
//            CpShop cpShop = rpcCpService.selectCpCShopById(stCLockSkuStrategyDO.getCpCShopId());
//            if (cpShop != null) {
//                stCLockSkuStrategyDO.setCpCShopEcode(cpShop.getEcode());
//                stCLockSkuStrategyDO.setCpCShopTitle(cpShop.getCpCShopTitle());
//            }
//        }
//    }
//}