package com.jackrain.nea.st.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.st.annotation.StOperationLog;
import com.jackrain.nea.st.mapper.StCFullcarCostItemMapper;
import com.jackrain.nea.st.mapper.StCFullcarCostMapper;
import com.jackrain.nea.st.model.enums.CloseStatusEnum;
import com.jackrain.nea.st.model.enums.SubmitStatusEnum;
import com.jackrain.nea.st.model.table.StCFullcarCost;
import com.jackrain.nea.st.model.table.StCFullcarCostItem;
import com.jackrain.nea.st.model.common.StRedisConstant;
import com.jackrain.nea.st.utils.RedisCacheUtil;
import com.jackrain.nea.st.utils.StBeanUtils;
import com.jackrain.nea.st.utils.ValueHolderUtils;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/6/11 13:19
 */
@Component
@Slf4j
public class StCFullcarCostSubmitService extends CommandAdapter {

    @Autowired
    private StCFullcarCostMapper stCFullcarCostMapper;

    @Autowired
    private StCFullcarCostItemMapper stCFullcarCostItemMapper;

    @StOperationLog(operationType = "AUDIT", mainTableName = "ST_C_FULLCAR_COST", itemsTableName = "ST_C_FULLCAR_COST_ITEM")
    @Override
    public ValueHolder execute(QuerySession querySession) throws NDSException {
        DefaultWebEvent event = querySession.getEvent();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(
                event.getParameterValue("param"), "yyyy-MM-dd HH:mm:ss"), Feature.OrderedField);
        if (param == null) {
            throw new NDSException("参数为空！");
        }
        JSONArray voidArray = StBeanUtils.makeUnAuditJsonArray(param);
        if (voidArray.size() == 1) {
            voidAction(voidArray.getLong(0), querySession);
        } else {
            int success = 0;
            int fail = 0;
            for (int i = 0; i < voidArray.size(); i++) {
                Long id = voidArray.getLong(i);
                try {
                    voidAction(id, querySession);
                    success++;
                } catch (Exception e) {
                    fail++;
                }
            }
            if (success != voidArray.size()) {
                return ValueHolderUtils.getFailValueHolder("零担费用设置提交成功" + success + "条,失败" + fail + "条");
            }
        }

        return ValueHolderUtils.getSuccessValueHolder("提交成功");
    }

    private void voidAction(Long id, QuerySession querySession) {
        User user = querySession.getUser();
        StCFullcarCost fullcarCost = stCFullcarCostMapper.selectById(id);
        //判断主表是否存在
        if (fullcarCost == null) {
            throw new NDSException("当前记录不存在！");
        }
        if (!SubmitStatusEnum.NO_SUBMIT.getKey().equals(fullcarCost.getStatus())) {
            throw new NDSException("当前单据状态，不允许提交！");
        }
        //判断是否有明细
        Integer count = stCFullcarCostItemMapper.selectCount(new LambdaQueryWrapper<StCFullcarCostItem>()
                .eq(StCFullcarCostItem::getFullcarCostId, fullcarCost.getId()));
        if (count <= 0) {
            throw new NDSException("请维护明细！");
        }
        //判断是否冲突
        List<StCFullcarCost> selectList = stCFullcarCostMapper.selectList(new LambdaQueryWrapper<StCFullcarCost>()
                .eq(StCFullcarCost::getCpCPhyWarehouseId, fullcarCost.getCpCPhyWarehouseId())
                .eq(StCFullcarCost::getCpCLogisticsId, fullcarCost.getCpCLogisticsId())
                .eq(StCFullcarCost::getStatus, SubmitStatusEnum.SUBMIT.getKey())
                .eq(StCFullcarCost::getCloseStatus, CloseStatusEnum.NO_CLOSE.getKey()));
        if (CollectionUtils.isNotEmpty(selectList)) {
            for (StCFullcarCost cost : selectList) {
                Boolean flag = checkTime(fullcarCost.getStartDate(), fullcarCost.getEndDate(), cost.getStartDate(), cost.getEndDate());
                if (!flag) {
                    throw new NDSException("不允许有两个有效零担报价设置！");
                }
            }
        }
        StCFullcarCost newCost = new StCFullcarCost();
        newCost.setId(fullcarCost.getId());
        newCost.setStatus(SubmitStatusEnum.SUBMIT.getKey());
        newCost.setSubmitUserId(Long.valueOf(user.getId()));
        newCost.setSubmitTime(LocalDateTime.now());
        StBeanUtils.makeModifierField(newCost, querySession.getUser());
        int update = stCFullcarCostMapper.updateById(newCost);
        if (update <= 0) {
            throw new NDSException("更新失败！");
        }

        // 清除整车报价关系缓存
        String redisKey = StRedisConstant.buildFullcarCostRelationKey(fullcarCost.getCpCPhyWarehouseId(), fullcarCost.getCpCLogisticsId());
        RedisCacheUtil.deleteAll(redisKey);
        log.info("清除整车报价关系缓存成功，key={}", redisKey);

        // 清除根据ID查询的分布式缓存
        String idCacheKey = StRedisConstant.buildFullcarCostRelationByIdKey(fullcarCost.getId());
        RedisCacheUtil.deleteAll(idCacheKey);
        log.info("清除整车报价ID缓存成功，key={}", idCacheKey);
    }

    private Boolean checkTime(Date startDate, Date endDate, Date startDate1, Date endDate1) {
        if (endDate.compareTo(endDate1) == 0) {
            return false;
        } else {
            if (endDate.compareTo(endDate1) == 1) {
                //endDate大，则比较startDate是否小于等于endDate1,是标识有交叉
                if (endDate1.compareTo(startDate) == 1) {
                    return false;
                }
            } else {
                //endDate1大，则比较startDate1是否小于等于endDate,是标识有交叉
                if (endDate.compareTo(startDate1) == 1) {
                    return false;
                }
            }
        }
        return true;
    }
}
