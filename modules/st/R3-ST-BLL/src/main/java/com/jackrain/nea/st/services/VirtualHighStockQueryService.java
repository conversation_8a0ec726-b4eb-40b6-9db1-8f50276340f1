package com.jackrain.nea.st.services;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.st.mapper.StCVirtualHighStockMapper;
import com.jackrain.nea.st.model.common.StEsConstants;
import com.jackrain.nea.st.model.request.VirtualHighStockRequest;
import com.jackrain.nea.st.model.result.VirtualHighStockItemResult;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.utility.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date: 2020/7/7 4:08 下午
 * @Desc:
 */
@Component
@Slf4j
public class VirtualHighStockQueryService {
    @Autowired
    private StCVirtualHighStockMapper stCVirtualHighStockMapper;

    /**
     * 查询
     * @return 返回状态
     * @throws NDSException 异常信息
     */
    public ValueHolderV14<VirtualHighStockItemResult> selectCurrentStock(VirtualHighStockRequest virtualHighStockRequest) {
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("【查询虚高库存入参】{}"), JSONObject.toJSON(virtualHighStockRequest));
        }
        ValueHolderV14<VirtualHighStockItemResult> valueHolder14 = new ValueHolderV14();
        valueHolder14.setCode(StEsConstants.SUCCESS);
        valueHolder14.setMessage("成功");
        String errorMes = checkLockSkuStrategyItemRequest(virtualHighStockRequest);
        if (org.apache.commons.lang.StringUtils.isNotEmpty(errorMes)) {
            valueHolder14.setCode(ResultCode.FAIL);
            valueHolder14.setMessage(errorMes);
            return valueHolder14;
        }
        //已审核且没有作废,且在有效期内，多个策略需取最新时间，如果出现创建时间相同，则取id最大的，前提是需要skuID存在当前策略
        List<VirtualHighStockItemResult> list = stCVirtualHighStockMapper.selectByParam(
                virtualHighStockRequest.getPsCSkuId(),
                virtualHighStockRequest.getSkuId(),
                virtualHighStockRequest.getProId(),
                virtualHighStockRequest.getExpireTime(),
                virtualHighStockRequest.getShopId());
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("查询虚高库存数据库出参{}"), JSONObject.toJSON(list));
        }
        list = list.stream().filter(obj -> {
            // 1. 中台sku不一致的过滤
            if(obj.getPsCSkuId() != null && !obj.getPsCSkuId().equals(String.valueOf(virtualHighStockRequest.getPsCSkuId()))){
                return false;
            }
            //1. 存在平台skuid，但是和渠道库存缓存池中不一致的过滤
            if (StringUtils.isNotEmpty(obj.getSkuId()) && !StringUtils.equals(obj.getSkuId(), virtualHighStockRequest.getSkuId())) {
                return false;
            }
            //2.平台skuid为空且平台商品id有值，但是和渠道库存缓存池中不一致的过滤
            if (StringUtils.isNotEmpty(obj.getProId()) && !StringUtils.equals(obj.getProId(), virtualHighStockRequest.getProId())) {
                return false;
            }
            return true;
        }).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(list)) {
            list.sort(Comparator
                    // 主表时间倒叙排序
                    .comparing(VirtualHighStockItemResult::getMainCreationdate).reversed()
                    // 粒度越细排在前面
                    .thenComparing((k1,k2) -> {
                        int a = 0,b = 0;
                        if (StringUtils.isNotEmpty(k1.getSkuId())) {
                            a = 1;
                        } else if (StringUtils.isNotEmpty(k1.getProId())) {
                            a = 2;
                        } else if (k1.getPsCSkuId() != null) {
                            a = 3;
                        }
                        if (StringUtils.isNotEmpty(k2.getSkuId())) {
                            b = 1;
                        } else if (StringUtils.isNotEmpty(k2.getProId())) {
                            b = 2;
                        } else if (k2.getPsCSkuId() != null) {
                            b = 3;
                        }
                        return a - b;
                    }));
            valueHolder14.setData(list.get(0));
        }
        return valueHolder14;
    }

    /**
     * <AUTHOR>
     * @Description 入参校验
     * @Date 17:21 2020/12/18
     * @param request
     * @return java.lang.String
     **/
    private String checkLockSkuStrategyItemRequest(VirtualHighStockRequest request) {
        if (StringUtils.isEmpty(request.getShopId())) {
            return "店铺不允许为空";
        }
        if (StringUtils.isEmpty(request.getExpireTime())) {
            return "当前时间不允许为空";
        }
        if (request.getPsCSkuId() == null) {
            return "中台sku不允许为空";
        }
        if (StringUtils.isEmpty(request.getProId())) {
            return "平台商品不允许为空";
        }
        if (StringUtils.isEmpty(request.getSkuId())) {
            return "平台sku不允许为空";
        }
        return null;
    }
}
