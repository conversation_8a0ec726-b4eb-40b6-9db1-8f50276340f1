package com.jackrain.nea.st.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.st.common.StConstant;
import com.jackrain.nea.st.mapper.StCProductStrategyItemMapper;
import com.jackrain.nea.st.mapper.StCProductStrategyMapper;
import com.jackrain.nea.st.model.table.*;
import com.jackrain.nea.st.utils.ValueHolderUtils;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.apache.commons.collections4.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;
//
///**
// * <AUTHOR> ShiLong
// * @Date: 2020/7/8 9:22 下午
// * @Desc: 2.店铺商品特殊设置修改比例同步库存
// */
//@Component
//@Slf4j
//public class ShopProductSyncStockService extends CommandAdapter {
//
//    @Autowired
//    private StCProductStrategyItemMapper itemMapper;
//    @Autowired
//    private StCProductStrategyMapper mainMapper;
//    @Reference(group = "sg", version = "1.0")
//    private SgBSyncChannelStockCmd sgBSyncChannelStockCmd;
//
//    @Autowired
//    private RpcSgService rpcSgService;
//    /**
//     * 点击同步按钮时，需判断修改前比例是否为null，如果为Null代表需要同步；
//     * 其次还需要判断不为null情况下，当前修改比例与修改前比例是否相同，如果相同则不需要同步，否则同步
//     * 点击同步后，需要将修改前比例更新一下
//     */
//    @Override
//    public ValueHolder execute(QuerySession session) {
//        DefaultWebEvent event = session.getEvent();
//        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(
//                event.getParameterValue("param"), "yyyy-MM-dd HH:mm:ss", SerializerFeature.WriteMapNullValue),
//                Feature.OrderedField);
//        log.info(LogUtil.format("店铺商品特殊设置修改比例同步库存入参:{}", param);
//        Long id = param.getLong("objid");
//        if (id == null || id < 0) {
//            throw new NDSException("请选择需要同步的库存策略");
//        }
//        this.syncPlatformStock(id,session.getUser());
//        return ValueHolderUtils.getFailValueHolder("暂无需要同步的库存");
//    }
//
//    /**
//     * 点击同步按钮时，需判断修改前比例是否为null，如果为Null代表需要同步；
//     * 其次还需要判断不为null情况下，当前修改比例与修改前比例是否相同，如果相同则不需要同步，否则同步
//     * 点击同步后，需要将修改前比例更新一下
//     */
//    public ValueHolder syncPlatformStock(Long objid, User user) {
//        StCProductStrategyDO stCProductStrategyDO = mainMapper.selectById(objid);
//        //查询明细表信息（需要修改前比例是否为Null，以及不为null的情况）
//        List<StCProductStrategyItemDO> itemList = itemMapper.selectList(new QueryWrapper<StCProductStrategyItemDO>()
//                .lambda()
//                .eq(StCProductStrategyItemDO::getStCProductStrategyId, objid)
//                .eq(StCProductStrategyItemDO::getIsactive, StConstant.ISACTIVE_Y));
//                //.isNotNull(StCProductStrategyItemDO::getPtSkuId));   //明细必须定位到平台skuid
//        if (!CollectionUtils.isEmpty(itemList)) {
//            //收集修改前比例为null的比例(代表需要同步)
//            //收集修改前比例不为Null且与当前修改比例不一致的明细(代表需要同步)
//            //需校验不符合有效期内的
////            StCProductStrategyDO queryResult = mainMapper.selectById(objid);
////            if (!this.checkExpire(queryResult)) {
////                return ValueHolderUtils.getFailValueHolder("暂无需要同步的库存");
////            }
////            List<StCProductStrategyItemDO> rateList = itemList.stream().filter(e ->
////                    e.getBeforeUpdateStockRate() == null
////                            || (e.getBeforeUpdateStockRate() != null
////                            && !e.getStockScale().equals(e.getBeforeUpdateStockRate())))
////                    .collect(Collectors.toList());
//            //调用同步接口,调用成功后需更新修改前比例
//            try {
//                List<SgBSyncChannelStock> channelStockList = this.assembleParam(stCProductStrategyDO, itemList);
//                if (!CollectionUtils.isEmpty(channelStockList)) {
//                    ValueHolderV14<List<SgBSyncChannelStock>> valueHolderV14 = sgBSyncChannelStockCmd.insert(channelStockList,user);
//                    if (valueHolderV14.isOK()) {
////                        int updateResult = itemMapper.updateByIds(rateList);
////                        if (updateResult > 0) {
//                            return ValueHolderUtils.getSuccessValueHolder(objid, StConstant.TAB_ST_C_SYNC_STOCK_STRATEGY_CHANNEL);
////                        }
//                    }
//                    log.debug(LogUtil.format("【店铺商品特殊设置修改比例同步库存失败】data:{}", JSONObject.toJSON(itemList));
//                    return ValueHolderUtils.getFailValueHolder("同步库存失败");
//                }
//            } catch (Exception e) {
//                e.printStackTrace();
//                log.error(LogUtil.format("【店铺商品特殊设置修改比例同步库存失败】data:{}", itemList);
//            }
//        }
//        return ValueHolderUtils.getFailValueHolder("暂无需要同步的库存");
//    }
//
//    /**
//     * 店铺商品特殊设置修改比例同步库存 定时任务
//     */
//    public ValueHolder syncStockTask() {
//        List<StCProductStrategyDO> mainList = mainMapper.selectList(new QueryWrapper<StCProductStrategyDO>().lambda()
//                .eq(StCProductStrategyDO::getIsactive, StConstant.ISACTIVE_Y)
//                .eq(StCProductStrategyDO::getEstatus, StConstant.CON_BILL_STATUS_02));
//        List<Long> mainIds = this.checkParam(mainList);
//        List<StCProductStrategyItemDO> itemDOList = itemMapper.selectList(new QueryWrapper<StCProductStrategyItemDO>().lambda()
//                .in(StCProductStrategyItemDO::getStCProductStrategyId, mainIds)
//                .eq(StCProductStrategyItemDO::getIsactive, StConstant.ISACTIVE_Y));
//        if (!CollectionUtils.isEmpty(itemDOList)) {
//            //收集修改前比例为null的比例(代表需要同步)
//            //收集修改前比例不为Null且与当前修改比例不一致的明细(代表需要同步)
//            List<StCProductStrategyItemDO> rateList = itemDOList.stream().filter(e ->
//                    e.getBeforeUpdateStockRate() == null
//                            || (e.getBeforeUpdateStockRate() != null
//                            && !e.getStockScale().equals(e.getBeforeUpdateStockRate())))
//                    .collect(Collectors.toList());
//            //调用同步接口,调用成功后需更新修改前比例
//            try {
//                int updateResult = itemMapper.updateByIds(rateList);
//                if (updateResult > 0) {
//                    return ValueHolderUtils.getSuccessValueHolder("执行成功");
//                }
//                log.debug(LogUtil.format("【店铺商品特殊设置修改比例同步库存失败】data:{}", rateList);
//                return ValueHolderUtils.getFailValueHolder("同步库存失败");
//            } catch (Exception e) {
//                e.printStackTrace();
//                log.error(LogUtil.format("【店铺商品特殊设置修改比例同步库存失败】data:{}", rateList);
//            }
//        }
//        return ValueHolderUtils.getFailValueHolder("暂无需要同步的库存");
//    }
//
//    /**
//     * 校验当前系统时间在生效开始时间和生效结束时间范围内
//     * @param mainList
//     */
//    private List<Long> checkParam(List<StCProductStrategyDO>  mainList) {
//        List<Long> mainIds = Lists.newArrayList();
//        if (!CollectionUtils.isEmpty(mainList)) {
//            for (StCProductStrategyDO stCProductStrategyDO : mainList) {
//                if (stCProductStrategyDO.getEndTime() != null && stCProductStrategyDO.getBeginTime() != null) {
//                    //当前时间大于开始，小于结束
//                    if (stCProductStrategyDO.getBeginTime().before(new Date()) && new Date().before(stCProductStrategyDO.getEndTime())) {
//                        mainIds.add(stCProductStrategyDO.getId());
//                    }
//                }
//            }
//        }
//        return mainIds;
//    }
//
//    private List<SgBSyncChannelStock> assembleParam(StCProductStrategyDO stCProductStrategyDO,List<StCProductStrategyItemDO> itemList) {
//        List<SgBSyncChannelStock> channelStockList = Lists.newArrayList();
//        for (StCProductStrategyItemDO strategyItemDO : itemList) {
//            SgBSyncChannelStock sgBSyncChannelStock = new SgBSyncChannelStock();
//            sgBSyncChannelStock.setBeginTime(stCProductStrategyDO.getBeginTime());
//            sgBSyncChannelStock.setEndTime(stCProductStrategyDO.getEndTime());
//            if (strategyItemDO.getCpCOrgChannelId() != null) {
//                sgBSyncChannelStock.setCpCOrgChannelId(strategyItemDO.getCpCOrgChannelId());
//            }
//            sgBSyncChannelStock.setCpCShopId(strategyItemDO.getCpCShopId());
//            sgBSyncChannelStock.setCpCShopTitle(strategyItemDO.getCpCShopTitle());//设置店铺名称
//            if (!StringUtils.isEmpty(strategyItemDO.getPtProId())) {
//                sgBSyncChannelStock.setNumberId(String.valueOf(strategyItemDO.getPtProId()));
//            }
//            if (strategyItemDO.getPsCProId() != null) {
//                sgBSyncChannelStock.setPsCProId(strategyItemDO.getPsCProId());
//            }
//            if (strategyItemDO.getPsCSkuId() != null) {
//                sgBSyncChannelStock.setPsCSkuId(strategyItemDO.getPsCSkuId());
//            }
//            if(StringUtils.isNotEmpty(strategyItemDO.getPsCSkuEcode())){
//                sgBSyncChannelStock.setPsCSkuEcode(strategyItemDO.getPsCSkuEcode());
//            }
//            if (!StringUtils.isEmpty(strategyItemDO.getPtSkuId())) {
//                sgBSyncChannelStock.setSkuId(String.valueOf(strategyItemDO.getPtSkuId()));
//            }
//            sgBSyncChannelStock.setStrategyId(strategyItemDO.getStCProductStrategyId());
//            sgBSyncChannelStock.setType(StConstant.PRODUCT_STRATEGY_TYPE);
//            sgBSyncChannelStock.setStatus(StConstant.NO_SYNC_STATUS);
//            sgBSyncChannelStock.setStrategyItemId(strategyItemDO.getId());
//            channelStockList.add(sgBSyncChannelStock);
//        }
//
//        return channelStockList;
//    }
//    private List<SgBSyncChannelStock> assembleParam(Long objid,List<StCProductStrategyItemDO> itemList,User user) {
//        StCProductStrategyDO stCProductStrategyDO = mainMapper.selectById(objid);
//        List<SgBSyncChannelStock> channelStockList = Lists.newArrayList();
//
//        List<StCProductStrategyItemDO> skuStrategyItemDOS = Lists.newArrayList();
//        List<StCProductStrategyItemDO> ptSkuStrategyItemDOS = Lists.newArrayList();
//        Set<Long> cpCShopIds = Sets.newHashSet();
//        Set<String> skuEcodes = Sets.newHashSet();
//        for (StCProductStrategyItemDO itemDO : itemList) {
//            if (itemDO.getPtSkuId() == null) {
//                if (itemDO.getCpCShopId() != null) {
//                    cpCShopIds.add(itemDO.getCpCShopId());
//                }
//                if (StringUtils.isEmpty(itemDO.getPsCSkuEcode())) {
//                    skuEcodes.add(itemDO.getPsCSkuEcode());
//                }
//                skuStrategyItemDOS.add(itemDO);
//            } else {
//                ptSkuStrategyItemDOS.add(itemDO);
//            }
//        }
//        if (CollectionUtils.isNotEmpty(skuStrategyItemDOS)) {
//            SgChannelProductQueryForSTRequest queryForSTRequest = new SgChannelProductQueryForSTRequest();
//            queryForSTRequest.setCpCShopIdList(Lists.newArrayList(cpCShopIds));
//            queryForSTRequest.setPsCSkuEcodeList(Lists.newArrayList(skuEcodes));
//            List<SgChannelProductQueryForSTResult> results = rpcSgService.queryChannelProduct(queryForSTRequest);
//            for (SgChannelProductQueryForSTResult result : results) {
//                SgBSyncChannelStock sgBSyncChannelStock = new SgBSyncChannelStock();
//                sgBSyncChannelStock.setCpCShopId(result.getCpCShopId());
//                sgBSyncChannelStock.setCpCShopTitle(result.getCpCShopTitle());//设置店铺名称
//                sgBSyncChannelStock.setBeginTime(stCProductStrategyDO.getBeginTime());
//                sgBSyncChannelStock.setEndTime(stCProductStrategyDO.getEndTime());
//                sgBSyncChannelStock.setNumberId(result.getNumiid());
//                sgBSyncChannelStock.setPsCProId(result.getPsCProId());
//                sgBSyncChannelStock.setPsCSkuId(result.getPsCSkuId());
//                sgBSyncChannelStock.setPsCSkuEcode(result.getPsCSkuEcode());//条码code
//                sgBSyncChannelStock.setSkuId(result.getSkuId());
//                sgBSyncChannelStock.setStrategyId(objid);
//                sgBSyncChannelStock.setType(StConstant.PRODUCT_STRATEGY_TYPE);
//                sgBSyncChannelStock.setStatus(StConstant.NO_SYNC_STATUS);
//                channelStockList.add(sgBSyncChannelStock);
//            }
//        }
//        if (CollectionUtils.isNotEmpty(ptSkuStrategyItemDOS)) {
//            for (StCProductStrategyItemDO item : ptSkuStrategyItemDOS) {
//                if (item.getStCProductStrategyId().equals(objid)) {
//                    SgBSyncChannelStock sgBSyncChannelStock = new SgBSyncChannelStock();
//                    sgBSyncChannelStock.setBeginTime(stCProductStrategyDO.getBeginTime());
//                    sgBSyncChannelStock.setEndTime(stCProductStrategyDO.getEndTime());
//                    if (item.getCpCOrgChannelId() != null) {
//                        sgBSyncChannelStock.setCpCOrgChannelId(item.getCpCOrgChannelId());
//                    }
//                    sgBSyncChannelStock.setCpCShopId(item.getCpCShopId());
//                    sgBSyncChannelStock.setCpCShopTitle(item.getCpCShopTitle());//设置店铺名称
//                    if (!StringUtils.isEmpty(item.getPtProId())) {
//                        sgBSyncChannelStock.setNumberId(String.valueOf(item.getPtProId()));
//                    }
//                    if (item.getPsCProId() != null) {
//                        sgBSyncChannelStock.setPsCProId(item.getPsCProId());
//                    }
//                    if (item.getPsCSkuId() != null) {
//                        sgBSyncChannelStock.setPsCSkuId(item.getPsCSkuId());
//                        sgBSyncChannelStock.setPsCSkuEcode(item.getPsCSkuEcode());//条码code
//                    }
//                    if (!StringUtils.isEmpty(item.getPtSkuId())) {
//                        sgBSyncChannelStock.setSkuId(String.valueOf(item.getPtSkuId()));
//                    }
//                    sgBSyncChannelStock.setStrategyId(item.getStCProductStrategyId());
//                    sgBSyncChannelStock.setType(StConstant.PRODUCT_STRATEGY_TYPE);
//                    sgBSyncChannelStock.setStatus(StConstant.NO_SYNC_STATUS);
//                    sgBSyncChannelStock.setStrategyItemId(item.getId());
//                    channelStockList.add(sgBSyncChannelStock);
//                }
//            }
//
//        }
//        return channelStockList;
//    }
//
//    /**
//     * 校验有效期
//     * @param stCProductStrategyDO
//     * @return
//     */
//    private boolean checkExpire(StCProductStrategyDO stCProductStrategyDO) {
//        if (stCProductStrategyDO != null && stCProductStrategyDO.getEndTime() != null && stCProductStrategyDO.getBeginTime() != null) {
//            //当前时间大于开始，小于结束
//            if (stCProductStrategyDO.getBeginTime().before(new Date()) && new Date().before(stCProductStrategyDO.getEndTime())) {
//                return true;
//            }
//        }
//        return false;
//    }
//}
