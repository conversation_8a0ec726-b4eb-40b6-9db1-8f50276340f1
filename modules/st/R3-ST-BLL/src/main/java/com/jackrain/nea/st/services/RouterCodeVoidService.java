package com.jackrain.nea.st.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.st.common.StConstant;
import com.jackrain.nea.st.mapper.StCRouterMapper;
import com.jackrain.nea.st.model.table.StCRouterDO;
import com.jackrain.nea.st.utils.StBeanUtils;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.HashMap;

/**
 * @Descroption 路由操作码策略
 * <AUTHOR>
 * @Date 2020/5/11 15:45
 */
@Component
@Slf4j
public class RouterCodeVoidService extends CommandAdapter {
    @Autowired
    private StCRouterMapper mapper;

    @Override
    public ValueHolder execute(QuerySession querySession) throws NDSException {
        //1.获取传入参数
        DefaultWebEvent event = querySession.getEvent();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(event.getParameterValue("param"),
                "yyyy-MM-dd HH:mm:ss", SerializerFeature.WriteMapNullValue), Feature.OrderedField);
        if (param == null) {
            throw new NDSException("参数为空！");
        }
        log.debug(LogUtil.format("Start RouterCodeVoidService.execute. ReceiveParams: {}"), param.toJSONString());
        //2.定义错误记录数
        HashMap<Long, Object> errMap = new HashMap();
        //3.生成审核Json数组
        JSONArray voidArray = StBeanUtils.makeVoidJsonArray(param);
        for (int i = 0; i < voidArray.size(); i++) {
            Long id = voidArray.getLong(i);
            try {
                //4.遍历作废方法
                voidRouter(id, querySession);
            } catch (Exception e) {
                errMap.put(id, e.getMessage());
            }
        }
        //5.返回ValueHolder对象
        return StBeanUtils.getExcuteValueHolder(voidArray.size(), errMap);
    }

    /**
     * @param id
     * @param querySession
     * @return void
     * @Descroption 检查并更新作废状态
     * <AUTHOR>
     * @Date 2020/5/11 15:45
     */
    public void voidRouter(Long id, QuerySession querySession) {
        StCRouterDO stCRouter = mapper.selectById(id);
        checkDistribution(stCRouter);
        //更新作废状态
        StBeanUtils.makeModifierField(stCRouter, querySession.getUser());
        stCRouter.setIsactive(StConstant.ISACTIVE_N);//作废
        setVoidCommonField(stCRouter, querySession.getUser());
        int updateNum = mapper.updateById(stCRouter);
        if (updateNum < 0) {
            throw new NDSException("作废失败！");
        }
        User user = querySession.getUser();
        mapper.updateByMasterId(StConstant.ISACTIVE_N, Long.valueOf(user.getId()), user.getEname(), new Date(), stCRouter.getId());
    }

    private void checkDistribution(StCRouterDO stCRouter) {
        if (stCRouter == null) {
            throw new NDSException("当前记录已不存在！");
        } else {
            if (StConstant.ISACTIVE_N.equals(stCRouter.getIsactive())) {
                throw new NDSException("当前记录已作废，不允许重复作废！");
            }
        }
    }

    /**
     * @param stCRouter
     * @param user
     * @return void
     * @Descroption 设置作废公共字段
     * <AUTHOR>
     * @Date 2020/5/11 15:45
     */
    private void setVoidCommonField(StCRouterDO stCRouter, User user) {
        stCRouter.setDelid(Long.valueOf(user.getId()));
        stCRouter.setDelename(user.getEname());
        stCRouter.setDelname(user.getName());
        stCRouter.setDelTime(new Date());
    }
}
