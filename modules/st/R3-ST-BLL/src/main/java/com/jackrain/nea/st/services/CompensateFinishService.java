package com.jackrain.nea.st.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.st.common.StConstant;
import com.jackrain.nea.st.mapper.StCCompensateMapper;
import com.jackrain.nea.st.model.table.StCCompensateDO;
import com.jackrain.nea.st.model.table.StCDistributionDO;
import com.jackrain.nea.st.utils.StBeanUtils;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.HashMap;

/**
 * <AUTHOR>
 * @Date 2019/3/13 15:22
 */
@Component
@Slf4j
@Transactional
public class CompensateFinishService extends CommandAdapter {

    @Autowired
    private StCCompensateMapper stCCompensateMapper;

    @Override
    public ValueHolder execute(QuerySession querySession) throws NDSException {
        //1.获取传入参数
        DefaultWebEvent event = querySession.getEvent();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(event.getParameterValue("param"),
                "yyyy-MM-dd HH:mm:ss", SerializerFeature.WriteMapNullValue), Feature.OrderedField);
        if (param == null) {
            throw new NDSException("参数为空！");
        }
        log.debug(LogUtil.format("快递赔付结案Json:") + param.toJSONString());
        //2.定义错误记录数
        int errRecord = 0;
        HashMap<Long, Object> errMap = new HashMap<Long, Object>();
        //3.生成审核Json数组
        JSONArray finishArray = StBeanUtils.makeFinishJsonArray(param);
        for (int i = 0; i < finishArray.size(); i++) {
            Long id = finishArray.getLong(i);
            try {
                //4.遍历结案方法
                finishCompensate(id, querySession);
            } catch (Exception e) {
                errRecord++;
                errMap.put(id, e.getMessage());
            }
        }
        //5.返回ValueHolder对象
        return StBeanUtils.getExcuteValueHolder(finishArray.size(), errMap);
    }

    private void finishCompensate(Long id, QuerySession querySession) {
        StCCompensateDO stCCompensateDO = stCCompensateMapper.selectById(id);
        checkCompensate(stCCompensateDO);
        //更新单据状态
        StBeanUtils.makeModifierField(stCCompensateDO, querySession.getUser());
        stCCompensateDO.setBillStatus(StConstant.CON_BILL_STATUS_04);
        setFinishCommonField(stCCompensateDO, querySession.getUser());
        int updateNum = stCCompensateMapper.updateById(stCCompensateDO);
        if (updateNum < 0) {
            throw new NDSException("方案:" + stCCompensateDO.getEname() + "结案失败！");
        }
    }

    /**
     * @param stCCompensateDO
     * @return
     * @Author: 朱宇军
     * @Date 2019/3/13
     */

    private void checkCompensate(StCCompensateDO stCCompensateDO) {
        if (stCCompensateDO == null) {
            throw new NDSException("当前记录已不存在！");
        } else {
            if (StConstant.CON_BILL_STATUS_04.equals(stCCompensateDO.getBillStatus())) {
                throw new NDSException("当前记录已结案，不允许重复结案！");
            } else if (StConstant.CON_BILL_STATUS_03.equals(stCCompensateDO.getBillStatus())) {
                throw new NDSException("当前记录已作废，不允许结案！");
            } else if (StConstant.CON_BILL_STATUS_01.equals(stCCompensateDO.getBillStatus())) {
                throw new NDSException("当前记录未审核，不允许结案！");
            }
        }
    }

    private void setFinishCommonField(StCCompensateDO stCCompensateDO, User user) {
        stCCompensateDO.setFinishid(Long.valueOf(user.getId()));
        stCCompensateDO.setFinishename(user.getEname());
        stCCompensateDO.setFinishname(user.getName());
        stCCompensateDO.setFinishtime(new Date());
    }
}
