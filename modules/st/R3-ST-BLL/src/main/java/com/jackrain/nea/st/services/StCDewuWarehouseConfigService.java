package com.jackrain.nea.st.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.google.common.base.Throwables;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.st.common.StConstant;
import com.jackrain.nea.st.mapper.CpCPhyWarehouseMapper;
import com.jackrain.nea.st.mapper.StCDewuWarehouseConfigMapper;
import com.jackrain.nea.st.model.table.CpCPhyWarehouseDO;
import com.jackrain.nea.st.model.table.StCDewuWarehouseConfig;
import com.jackrain.nea.st.utils.JsonUtils;
import com.jackrain.nea.st.utils.ValueHolderUtils;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.Date;
import java.util.List;

/**
 * 得物仓库配置表服务
 */
@Component
@Slf4j
@Transactional
public class StCDewuWarehouseConfigService extends CommandAdapter {

    @Autowired
    private StCDewuWarehouseConfigMapper stCDewuWarehouseConfigMapper;

    @Autowired
    private CpCPhyWarehouseMapper cpCPhyWarehouseMapper;


    /**
     * 得物仓库配置表保存
     *
     * @param session
     * @return
     */
    @Override
    public ValueHolder execute(QuerySession session) {
        log.debug(LogUtil.format("开始得物仓库配置表保存！"));
        DefaultWebEvent event = session.getEvent();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(event.getParameterValue("param"),
                "yyyy-MM-dd HH:mm:ss", SerializerFeature.WriteMapNullValue), Feature.OrderedField);
        if (param == null) {
            throw new NDSException("参数为空！");
        }
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("Start StCDewuWarehouseConfigService.execute. ReceiveParams: {}"),
                    JSONObject.toJSONString(param, SerializerFeature.WriteMapNullValue));
        }
        Long id = param.getLong("objid");
        JSONObject fixColumn = param.getJSONObject("fixcolumn");
        if (fixColumn != null && id != null) {
            if (id != -1) {
                return updateWarehouseConfig(session, fixColumn, id);
            } else {
                return addWarehouseConfig(session, fixColumn);
            }
        }
        throw new NDSException("当前记录已不存在！");
    }

    /**
     * 得物仓库配置表保存
     *
     * @param session
     * @param fixColumn
     * @return
     */
    private ValueHolder addWarehouseConfig(QuerySession session, JSONObject fixColumn) {
        JSONObject jsonObject = fixColumn.getJSONObject("ST_C_DEWU_WAREHOUSE_CONFIG");
        String json = jsonObject.toString();
        if (StringUtils.isNotEmpty(json)) {
            StCDewuWarehouseConfig stCDewuWarehouseConfig = JsonUtils.jsonParseClass(jsonObject, StCDewuWarehouseConfig.class);

            // 校验仓库编码是否在实体仓档案中存在
            if (!checkWarehouseCode(stCDewuWarehouseConfig)) {
                return ValueHolderUtils.getFailValueHolder("仓库编码在实体仓档案中不存在，请重新输入！");
            }

            // 校验仓库编码和地址ID的组合是否已存在
            if (!checkDuplicateConfig(stCDewuWarehouseConfig)) {
                return ValueHolderUtils.getFailValueHolder("该仓库编码和地址ID的组合已存在，请重新输入！");
            }

            User user = session.getUser();
            stCDewuWarehouseConfig.setId(ModelUtil.getSequence("st_c_dewu_warehouse_config"));
            // 基础信息赋值
            stCDewuWarehouseConfig.setAdClientId(37L);
            stCDewuWarehouseConfig.setAdOrgId(27L);
            stCDewuWarehouseConfig.setCreationdate(new Date());
            stCDewuWarehouseConfig.setOwnerid(Long.valueOf(user.getId()));
            stCDewuWarehouseConfig.setOwnername(user.getName());
            stCDewuWarehouseConfig.setOwnerename(user.getEname());
            stCDewuWarehouseConfig.setIsactive("Y");

            try {
                int count = this.stCDewuWarehouseConfigMapper.insert(stCDewuWarehouseConfig);
                if (count > 0) {
                    return ValueHolderUtils.getSuccessValueHolder(stCDewuWarehouseConfig.getId(), "st_c_dewu_warehouse_config");
                } else {
                    return ValueHolderUtils.getFailValueHolder("保存失败");
                }
            } catch (Exception ex) {
                log.error(LogUtil.format("StCDewuWarehouseConfigService.addWarehouseConfig Error{}"),
                        Throwables.getStackTraceAsString(ex));
                return ValueHolderUtils.getFailValueHolder("得物仓库配置表保存异常！！！");
            }
        }
        throw new NDSException("当前表st_c_dewu_warehouse_config不存在！");
    }

    /**
     * 更新得物仓库配置表
     *
     * @param session
     * @param fixColumn
     * @param id
     * @return
     */
    private ValueHolder updateWarehouseConfig(QuerySession session, JSONObject fixColumn, Long id) {
        JSONObject jsonObject = fixColumn.getJSONObject("ST_C_DEWU_WAREHOUSE_CONFIG");
        StCDewuWarehouseConfig warehouseConfig = JsonUtils.jsonParseClass(jsonObject, StCDewuWarehouseConfig.class);
        StCDewuWarehouseConfig existsWarehouseConfig = this.stCDewuWarehouseConfigMapper.selectById(id);
        log.debug(LogUtil.format("warehouseConfig : ") + warehouseConfig);
        if (existsWarehouseConfig == null) {
            throw new NDSException("当前记录已不存在！");
        }

        // 如果仓库编码有变更，需要校验实体仓档案中是否存在该编码
        if (StringUtils.isNotBlank(warehouseConfig.getWarehouseCode()) &&
                !warehouseConfig.getWarehouseCode().equals(existsWarehouseConfig.getWarehouseCode())) {
            // 校验仓库编码是否在实体仓档案中存在
            if (!checkWarehouseCode(warehouseConfig)) {
                return ValueHolderUtils.getFailValueHolder("仓库编码在实体仓档案中不存在，请重新输入！");
            }

            // 校验仓库编码和地址ID的组合是否已存在
            if (!checkDuplicateConfig(warehouseConfig)) {
                return ValueHolderUtils.getFailValueHolder("该仓库编码和地址ID的组合已存在，请重新输入！");
            }
        }

        warehouseConfig.setId(id);
        User user = session.getUser();
        warehouseConfig.setModifierid(Long.valueOf(user.getId()));
        warehouseConfig.setModifiername(user.getName());
        warehouseConfig.setModifierename(user.getEname());
        warehouseConfig.setModifieddate(new Date());

        try {
            int count = this.stCDewuWarehouseConfigMapper.updateById(warehouseConfig);
            if (count > 0) {
                return ValueHolderUtils.getSuccessValueHolder(warehouseConfig.getId(), "st_c_dewu_warehouse_config");
            } else {
                return ValueHolderUtils.getFailValueHolder("保存失败");
            }
        } catch (Exception ex) {
            log.error(LogUtil.format("StCDewuWarehouseConfigService.updateWarehouseConfig Error{}"),
                    Throwables.getStackTraceAsString(ex));
            return ValueHolderUtils.getFailValueHolder("得物仓库配置表更新异常！！！");
        }
    }

    /**
     * 校验仓库编码是否在实体仓档案中存在
     *
     * @param warehouseConfig
     * @return true-存在，可以使用；false-不存在，不可使用
     */
    private boolean checkWarehouseCode(StCDewuWarehouseConfig warehouseConfig) {
        if (warehouseConfig == null || StringUtils.isBlank(warehouseConfig.getWarehouseCode())) {
            return false;
        }

        // 查询实体仓档案，检查仓库编码是否存在
        LambdaQueryWrapper<CpCPhyWarehouseDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CpCPhyWarehouseDO::getEcode, warehouseConfig.getWarehouseCode());
        queryWrapper.eq(CpCPhyWarehouseDO::getIsactive, StConstant.ISACTIVE_Y);

        List<CpCPhyWarehouseDO> warehouseList = cpCPhyWarehouseMapper.selectList(queryWrapper);
        if (CollectionUtils.isEmpty(warehouseList)) {
            log.error(LogUtil.format("仓库编码[{}]在实体仓档案中不存在"), warehouseConfig.getWarehouseCode());
            return false;
        }

        // 如果存在，则设置仓库ID和仓库名称
        CpCPhyWarehouseDO warehouse = warehouseList.get(0);
        warehouseConfig.setWarehouseId(warehouse.getId());
        warehouseConfig.setWarehouseName(warehouse.getEname());
        return true;
    }

    /**
     * 校验仓库编码和地址ID的组合是否已存在
     *
     * @param warehouseConfig
     * @return true-不存在，可以使用；false-已存在，不可使用
     */
    private boolean checkDuplicateConfig(StCDewuWarehouseConfig warehouseConfig) {
        if (warehouseConfig == null || StringUtils.isBlank(warehouseConfig.getWarehouseCode())
                || StringUtils.isBlank(warehouseConfig.getAddressId())) {
            return false;
        }

        QueryWrapper<StCDewuWarehouseConfig> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("isactive", "Y");
        queryWrapper.eq("warehouse_code", warehouseConfig.getWarehouseCode());

        List<StCDewuWarehouseConfig> list = stCDewuWarehouseConfigMapper.selectList(queryWrapper);
        if (!CollectionUtils.isEmpty(list)) {
            log.error(LogUtil.format("仓库编码[{}]已存在"),
                    warehouseConfig.getWarehouseCode(), warehouseConfig.getAddressId());
            return false;
        }

        return true;
    }
}
