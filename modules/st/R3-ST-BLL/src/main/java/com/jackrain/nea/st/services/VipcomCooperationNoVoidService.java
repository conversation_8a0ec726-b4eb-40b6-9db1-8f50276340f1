package com.jackrain.nea.st.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.st.common.StConstant;
import com.jackrain.nea.st.mapper.StCVipcomCooperationNoMapper;
import com.jackrain.nea.st.model.table.StCVipcomCooperationNo;
import com.jackrain.nea.st.model.table.StCVipcomCooperationNo;
import com.jackrain.nea.st.utils.StBeanUtils;
import com.jackrain.nea.st.utils.ValueHolderUtils;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.HashMap;

/**
 * description：作废常态合作编码
 *
 * <AUTHOR>
 * @date 2021/6/30
 */
@Component
@Slf4j
public class VipcomCooperationNoVoidService extends CommandAdapter {
    @Autowired
    private StCVipcomCooperationNoMapper stCVipcomCooperationNoMapper;

    @Override
    public ValueHolder execute(QuerySession session) throws NDSException {
        DefaultWebEvent event = session.getEvent();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(
                event.getParameterValue("param"), "yyyy-MM-dd HH:mm:ss", SerializerFeature.WriteMapNullValue),
                Feature.OrderedField);
        if (param == null) {
            return ValueHolderUtils.getFailValueHolder("参数为空!");
        }
        //生成数组
        JSONArray voidArray = StBeanUtils.makeVoidJsonArray(param);
        //列表批量
        if (CollectionUtils.isEmpty(voidArray)) {
            return ValueHolderUtils.getFailValueHolder("请选择数据");
        }
        int errRecord = 0;
        HashMap<Long, Object> errMap = new HashMap<>();
        for (int i = 0; i < voidArray.size(); i++) {
            Long id = voidArray.getLong(i);
            try {
                voidVipcomCooperationNo(id, session);
            } catch (Exception ex) {
                errRecord++;
                errMap.put(id, ex.getMessage());
            }
        }

        //返回日志
        return StBeanUtils.getExcuteValueHolder(voidArray.size(), errMap);
    }

    /**
     * description：作废
     *
     * <AUTHOR>
     * @date 2021/6/30
     */
    private void voidVipcomCooperationNo(long id, QuerySession session) {
        //验证
        checkVipcomCooperationNo(id);
        //作废
        StCVipcomCooperationNo project = new StCVipcomCooperationNo();
        project.setId(id);
        project.setIsactive(StConstant.ISACTIVE_N);//作废状态
        project.setDelname(session.getUser().getName());//作废人用户名
        project.setDelid(Long.valueOf(session.getUser().getId()));//作废人
        project.setDelename(session.getUser().getName());//作废人姓名
        project.setDelTime(new Date());//作废时间
        StBeanUtils.makeModifierField(project, session.getUser());
        int update = stCVipcomCooperationNoMapper.updateById(project);
        if (update < 0) {
            throw new NDSException("作废失败！");
        }
    }

    /**
     * description：数据校验
     *
     * <AUTHOR>
     * @date 2021/6/30
     */
    private void checkVipcomCooperationNo(long id) {
        StCVipcomCooperationNo project = stCVipcomCooperationNoMapper.selectById(id);
        if (project == null) {
            throw new NDSException("当前记录已不存在！");
        } else {
            if (StConstant.ISACTIVE_N.equals(project.getIsactive())) {
                throw new NDSException("当前记录已作废，不允许重复作废！");
            }
        }
    }


}
