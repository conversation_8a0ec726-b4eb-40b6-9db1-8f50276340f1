package com.jackrain.nea.st.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.st.common.StConstant;
import com.jackrain.nea.st.mapper.StCSyncSkustockProMapper;
import com.jackrain.nea.st.mapper.StCSyncSkustockStoreMapper;
import com.jackrain.nea.st.mapper.StCSyncSkustockStrategyMapper;
import com.jackrain.nea.st.model.table.StCSyncSkustockStrategyDO;
import com.jackrain.nea.st.utils.StBeanUtils;
import com.jackrain.nea.st.utils.ValueHolderUtils;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @author: 汪聿森
 * @Date: Created in 2020-05-21 17:03
 * @Description : 店铺条码库存同步策略删除
 */
@Component
@Slf4j
public class StCSyncSkustockStrategyDelService extends CommandAdapter {
    @Autowired
    private StCSyncSkustockStrategyMapper stCSyncSkustockStrategyMapper;

    @Autowired
    private StCSyncSkustockProMapper stCSyncSkustockProMapper;

    @Autowired
    private StCSyncSkustockStoreMapper stCSyncSkustockStoreMapper;

    @Override
    public ValueHolder execute(QuerySession querySession) throws NDSException {
        DefaultWebEvent event = querySession.getEvent();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(
                event.getParameterValue("param"), "yyyy-MM-dd HH:mm:ss"), Feature.OrderedField);
        log.info(LogUtil.format("StCSyncSkustockStrategyDelService:{}"), param);
        String isDel = param.getString("isdelmtable");
        Long objid = param.getLong("objid");
        StCSyncSkustockStrategyDO stCSyncSkustockStrategyDO = stCSyncSkustockStrategyMapper.selectById(objid);
        if (stCSyncSkustockStrategyDO == null) {
            return ValueHolderUtils.getFailValueHolder("当前记录已不存在!");
        }
        JSONObject tabitem = param.getJSONObject("tabitem");
        JSONArray errorArray = new JSONArray();
        //判断是删除主表还是明细表单独删除
        if (StConstant.FALSE_STR.equals(isDel)) {
            //单独删除商品明细
            JSONArray itemProArray = tabitem.getJSONArray(StConstant.TAB_ST_C_SYNC_SKUSTOCK_PRO);
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("itemProArray:") + itemProArray);
            }
            if (itemProArray != null) {
                delProItemList(itemProArray, errorArray, stCSyncSkustockStrategyDO);
            }
            //单独删除供货仓明细
            JSONArray itemStoreArray = tabitem.getJSONArray(StConstant.TAB_ST_C_SYNC_SKUSTOCK_STORE);
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("itemStoreArray:") + itemStoreArray);
            }
            if (itemStoreArray != null) {
                delStoreItemList(itemStoreArray, errorArray, stCSyncSkustockStrategyDO);
            }
        } else {
            //删除主表
            delMain(objid, errorArray);
        }
        return StBeanUtils.getExcuteValueHolder(errorArray);
    }

    public void delMain(Long mainId, JSONArray errorArray) {
        int deleteCount = stCSyncSkustockStrategyMapper.deleteById(mainId);
        if (deleteCount <= 0) {
            errorArray.add(StBeanUtils.getJsonObjectInfo(mainId, "店铺条码库存同步策略已不存在"));
        }
    }

    public void delProItemList(JSONArray itemArray, JSONArray errorArray, StCSyncSkustockStrategyDO stCSyncSkustockStrategyDO) {
        for (int i = 0; i < itemArray.size(); i++) {
            Long itemid = itemArray.getLong(i);
            if (stCSyncSkustockProMapper.deleteById(itemid) <= 0) {
                errorArray.add(StBeanUtils.getJsonObjectInfo(itemid, "商品明细删除失败"));
            }
        }
    }

    public void delStoreItemList(JSONArray itemArray, JSONArray errorArray, StCSyncSkustockStrategyDO stCSyncSkustockStrategyDO) {
        for (int i = 0; i < itemArray.size(); i++) {
            Long itemid = itemArray.getLong(i);
            if (stCSyncSkustockStoreMapper.deleteById(itemid) <= 0) {
                errorArray.add(StBeanUtils.getJsonObjectInfo(itemid, "供货仓明细删除失败"));
            }
        }
    }
}
