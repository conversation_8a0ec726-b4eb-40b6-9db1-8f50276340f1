package com.jackrain.nea.st.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.base.Preconditions;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.st.common.StConstant;
import com.jackrain.nea.st.mapper.TOmsVipFullAddressMapper;
import com.jackrain.nea.st.model.table.TOmsVipFullAddressDO;
import com.jackrain.nea.st.utils.StBeanUtils;
import com.jackrain.nea.st.utils.ValueHolderUtils;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

/**
 * description：vip四级地址
 *
 * <AUTHOR>
 * @date 2021/6/30
 */
@Component
@Slf4j
@Transactional
public class TOmsVipFullAddressSaveService extends CommandAdapter {
    @Autowired
    private TOmsVipFullAddressMapper tOmsVipFullAddressDOMapper;

    public ValueHolder execute(QuerySession session) throws NDSException {
        DefaultWebEvent event = session.getEvent();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(event.getParameterValue("param")
                , "yyyy-MM-dd HH:mm:ss", SerializerFeature.WriteMapNullValue), Feature.OrderedField);
        if (log.isDebugEnabled()) {
            log.debug("Start TOmsVipFullAddressSaveService.execute. ReceiveParams: {}", param);
        }
        if (param == null) {
            return ValueHolderUtils.getFailValueHolder("参数为空!");
        }
        //1.拉取请求参数，解析
        Long id = param.getLong("objid");//获取objid参数
        JSONObject fixColumn = param.getJSONObject("fixcolumn");//获取表数据
        TOmsVipFullAddressDO tOmsVipFullAddressDO = JSONObject.parseObject(
                fixColumn.getString("T_OMSVIPFULLADDRESS"), TOmsVipFullAddressDO.class);
        if (tOmsVipFullAddressDO == null) {
            return ValueHolderUtils.getFailValueHolder("参数异常!");
        }
        if (id != -1) {
            return this.updateTOmsVipFullAddressDO(session, id, tOmsVipFullAddressDO);
        } else {
            return this.insertTOmsVipFullAddressDO(session, tOmsVipFullAddressDO);
        }
    }

    /**
     * description：更新数据
     *
     * <AUTHOR>
     * @date 2021/6/30
     */
    public ValueHolder updateTOmsVipFullAddressDO(QuerySession session, Long id, TOmsVipFullAddressDO project) {
        if (project != null) {
            if (project.getId() == null) {
                project.setId(id);
            }
            checkTOmsVipFullAddressDOByFilter(id, project, "update");
            //update基础字段补全
            StBeanUtils.makeModifierField(project, session.getUser());
            if (tOmsVipFullAddressDOMapper.updateById(project) < 0) {
                return ValueHolderUtils.getFailValueHolder("更新失败！");
            }
        }
        return ValueHolderUtils.getSuccessValueHolder(id, StConstant.TAB_ST_C_VIPCOM_COOPERATION_NO);
    }

    /**
     * description：保存数据
     *
     * <AUTHOR>
     * @date 2021/6/30
     */
    private ValueHolder insertTOmsVipFullAddressDO(QuerySession session, TOmsVipFullAddressDO addressDO) {
        long id = 0;
        if (addressDO != null) {
            checkTOmsVipFullAddressDOByFilter(-1L, addressDO, "insert");
            //生成主键
            id = ModelUtil.getSequence(StConstant.TAB_T_OMS_VIP_FULL_ADDRESS);
            addressDO.setId(id);
            StBeanUtils.makeCreateField(addressDO, session.getUser());
            int insertResult = tOmsVipFullAddressDOMapper.insert(addressDO);
            if (insertResult < 0) {
                return ValueHolderUtils.getFailValueHolder("保存失败！");
            }
        }
        return ValueHolderUtils.getSuccessValueHolder(id, StConstant.TAB_T_OMS_VIP_FULL_ADDRESS);
    }

    /**
     * 数据校验和过滤
     *
     * @param id
     * @param addressDO
     * @param action
     */
    private void checkTOmsVipFullAddressDOByFilter(Long id, TOmsVipFullAddressDO addressDO, String action) {
        List<TOmsVipFullAddressDO> tOmsVipFullAddressDOS = null;
        switch (action.toLowerCase()) {
            case "insert":
                tOmsVipFullAddressDOS = gettOmsVipFullAddressDOSByAddressCode(addressDO);
                Preconditions.checkArgument(CollectionUtils.isEmpty(tOmsVipFullAddressDOS)
                        , "当前地址编码:已存在！");
                break;
            case "update":
                TOmsVipFullAddressDO origAddressDO = tOmsVipFullAddressDOMapper.selectById(id);
                Preconditions.checkNotNull(origAddressDO, "当前记录已不存在！");
                String newAddressCode = addressDO.getAddressCode();
                if (StringUtils.isNotBlank(newAddressCode)) {
                    tOmsVipFullAddressDOS = gettOmsVipFullAddressDOSByAddressCode(addressDO);
                    tOmsVipFullAddressDOS.stream().filter(obj -> !obj.getId().equals(id)).collect(Collectors.toList());
                    Preconditions.checkArgument(CollectionUtils.isEmpty(tOmsVipFullAddressDOS)
                            , "当前地址编码:已存在！");
                }
                break;
        }
    }

    private List<TOmsVipFullAddressDO> gettOmsVipFullAddressDOSByAddressCode(TOmsVipFullAddressDO addressDO) {
        LambdaQueryWrapper<TOmsVipFullAddressDO> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(TOmsVipFullAddressDO::getAddressCode, addressDO.getAddressCode());
        List<TOmsVipFullAddressDO> tOmsVipFullAddressDOS = tOmsVipFullAddressDOMapper.selectList(lambdaQueryWrapper);
        return tOmsVipFullAddressDOS;
    }
}
