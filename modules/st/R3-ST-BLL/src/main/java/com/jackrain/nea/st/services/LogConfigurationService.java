package com.jackrain.nea.st.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.google.common.collect.Lists;
import com.jackrain.nea.core.schema.Column;
import com.jackrain.nea.core.schema.Limitvalue;
import com.jackrain.nea.core.schema.LimitvalueGroup;
import com.jackrain.nea.core.schema.Table;
import com.jackrain.nea.cp.api.CsupplierQueryCmd;
import com.jackrain.nea.cp.result.CpCSupplier;
import com.jackrain.nea.cpext.api.CpLogisticsQueryCmd;
import com.jackrain.nea.cpext.api.CpcPhyWareHouseQueryCmd;
import com.jackrain.nea.cpext.api.RegionQueryExtCmd;
import com.jackrain.nea.cpext.model.table.CpCPhyWarehouse;
import com.jackrain.nea.cpext.model.table.CpCRegion;
import com.jackrain.nea.cpext.model.table.CpLogistics;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.ps.api.CproDimItemQueryCmd;
import com.jackrain.nea.ps.api.table.PsCProdimItem;
import com.jackrain.nea.psext.api.PsCProQueryCmd;
import com.jackrain.nea.psext.api.PsCSkuQueryCmd;
import com.jackrain.nea.psext.model.table.PsCPro;
import com.jackrain.nea.psext.model.table.PsCSku;
import com.jackrain.nea.st.annotation.StOperationLog;
import com.jackrain.nea.st.common.StConstant;
import com.jackrain.nea.st.mapper.StCOperationLogMapper;
import com.jackrain.nea.st.model.enums.OperationTypeEnum;
import com.jackrain.nea.st.model.table.StCOperationLogDO;
import com.jackrain.nea.st.model.vo.StCLogChangeVo;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.aspectj.lang.JoinPoint;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Descroption 元数据操作日志处理
 * <AUTHOR>
 * @Date 2020/2/7 15:56
 */
@Component
@Slf4j
public class LogConfigurationService extends LogCommonService {
    @Resource
    private StCOperationLogMapper operationLogMapper;
    @DubboReference(group = "cp-ext", version = "1.0")
    private CpcPhyWareHouseQueryCmd cpcPhyWareHouseQueryCmd;
    @DubboReference(version = "1.0", group = "cp-ext")
    private RegionQueryExtCmd regionQueryExtCmd;
    @DubboReference(group = "ps", version = "1.0")
    private CproDimItemQueryCmd cproDimItemQueryCmd;
    @DubboReference(group = "ps-ext", version = "1.0")
    private PsCProQueryCmd psCProQueryCmd;
    @DubboReference(version = "1.0", group = "cp-ext")
    private CpLogisticsQueryCmd cpLogisticsQueryCmd;
    @DubboReference(version = "1.0", group = "cp")
    private CsupplierQueryCmd csupplierQueryCmd;
    @DubboReference(group = "ps-ext", version = "1.0")
    private PsCSkuQueryCmd psCSkuQueryCmd;

    @Override
    public void generateStLog(JoinPoint joinPoint, StOperationLog operationLog, Object returnValue) {
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("进入元数据操作日志生成...."));
        }
        String mainTableName = operationLog.mainTableName();
        String itemsTableName = operationLog.itemsTableName();
        List<String> itemsTableNameList = null;

        String operationType = operationLog.operationType();
        Map<String, List<String>> focusColumnMap = new HashMap<>();
        Map<String, String> itemOperationKeyMap = new HashMap<>();
        getFocusColumn(mainTableName, focusColumnMap);
        if (StringUtils.isNotEmpty(itemsTableName)) {
            String[] itemsTableNameArr = itemsTableName.split(",");
            for (String itemName : itemsTableNameArr) {
                getFocusColumn(itemName, focusColumnMap);
                getItemOperationKey(itemName, itemOperationKeyMap);
            }
            itemsTableNameList = Lists.newArrayList(itemsTableNameArr);
        }
        StCLogChangeVo logChangeVo = new StCLogChangeVo(mainTableName, operationType, itemsTableNameList, itemOperationKeyMap, focusColumnMap);
        resolvingStLog(joinPoint, logChangeVo);
    }

    /**
     * @param tableName
     * @param focusColumnMap
     * @return Map
     * @Description 需要记录变化的字段
     * <AUTHOR>
     * @date 2020/2/18 15:35
     */
    private void getFocusColumn(String tableName, Map<String, List<String>> focusColumnMap) {
        String[] focusColumn = new String[]{};
        if (StConstant.TAB_ST_C_SYNC_STOCK_STRATEGY.equals(tableName)) {
            focusColumn = new String[]{"IS_SYNC_STOCK"};
        } else if (StConstant.TAB_ST_C_SYNC_STOCK_STRATEGY_ITEM.equals(tableName)) {
            focusColumn = new String[]{"PRIORITY", "RATE", "LOW_STOCK", "ISACTIVE"};
        } else if (StConstant.TAB_ST_C_SYNC_STOCK_STRATEGY_CHANNEL.equals(tableName)) {
            focusColumn = new String[]{"STOCK_RATE", "LOW_STOCK", "ISACTIVE", "CHANNEL_PRIORITY"};
        } else if (StConstant.TAB_ST_C_PRICE.equals(tableName)) {
            //商品价格策略主表
            focusColumn = new String[]{"ENAME", "CP_C_SHOP_ID", "BEGIN_TIME", "END_TIME", "ACTIVE_TYPE", "RANK", "PRO_RANGE", "PRICE_TYPE", "REMARK"};
        } else if (StConstant.TAB_ST_C_PRICE_EXCLUDE_ITEM.equals(tableName)) {
            //商品价格策略-排除商品明细
            focusColumn = new String[]{"DISCERN_RULE", "DISCERN_CONTENT"};
        } else if (StConstant.ST_C_SHOP_LOGISTIC_STRATEGY.equals(tableName)) {
            //店铺物流策略
            focusColumn = new String[]{"CP_C_SHOP_ID", "REMARK"};
        } else if (StConstant.ST_C_SHOP_LOGISTIC_STRATEGY_ITEM.equals(tableName)) {
            //店铺物流明细
            focusColumn = new String[]{"CP_C_PROVINCE_ID", "CP_C_CITY_ID", "CP_C_PHY_WAREHOUSE_ID", "PS_C_PRODIM_ID", "PS_C_PRO_ID", "CP_C_LOGISTICS_ID", "LOGISTIC_TYPE"};
        } else if (StConstant.TAB_ST_C_SHOP_STRATEGY.equals(tableName)) {
            //店铺策略
            focusColumn = new String[]{"CP_C_SHOP_ID", "DEFAULT_STORE_ID", "CP_C_WAREHOUSE_DEF_ID", "CP_C_WAREHOUSE_EXCHANGE_ID", "IS_AG",
                    "IS_FORCE_SEND", "FREEZE_SHOP_CAN_AUDIT", "IS_AUTO_AUDIT", "IS_ENABLE_UNLOCK", "LOCK_DAYS", "CAN_SPLIT",
                    "IS_DETENTION_SPLIT", "IS_ADVANCE_SPLIT", "IS_MANUALLY_CREATE", "CANCEL_RETURN_ORDER", "OCCUPY_TYPE",
                    "IS_PLATFORM_SPLIT", "IS_JD_FLOW_OCCUPY", "IS_ORDER_TMS_TRACK", "IS_RETURN_ORDER_TMS_TRACK", "LOGISTICS_TYPE",
                    "AGREE_REFUND", "REFUND_MONEY_LIMIT", "ADDRESS_ID", "IS_AUTO_INTERCEPT", "REFUND_NOT_FROM_ORDER", "IS_BOX_SPLIT", "IS_AUTO_REFUND"};
        } else if (StConstant.TAB_ST_C_SHOP_STRATEGY_LOGISTICS_ITEM.equals(tableName)) {
            //店铺策略物流公司明细
            focusColumn = new String[]{"CP_C_LOGISTICS_ID", "ISACTIVE"};
        } else if (StConstant.ST_C_BANS_AREA_STRATEGY.equals(tableName)) {
            //物流禁发区域设置
            focusColumn = new String[]{"CP_C_LOGISTICS_ID", "CP_C_PHY_WAREHOUSE_ID", "CP_C_PROVINCE_ID", "CP_C_CITY_ID", "CP_C_REGION_AREA_ID", "REMARK"};
        } else if (StConstant.ST_C_PRO_LOGISTIC_STRATEGY.equals(tableName)) {
            focusColumn = new String[]{"CP_C_PROVINCE_ID", "PS_C_PRODIM_ID", "PS_C_PRO_ID", "PS_C_PRO_ENAME", "CP_C_LOGISTICS_ID", "LOGISTIC_TYPE", "CP_C_PHY_WAREHOUSE_ID", "REMARK"};
        } else if (StConstant.TAB_ST_C_LIVE_CAST_STRATEGY.equals(tableName)) {
            //直播解析策略
            focusColumn = new String[]{"CP_C_SHOP_ID", "CP_C_SHOP_ECODE", "CP_C_SHOP_TITLE", "STRATEGY_NAME", "STRATEGY_NAME", "STRATEGY_DESC", "STRATEGY_STATUS", "BILL_TIME_TYPE", "START_TIME", "END_TIME", "LIVE_PLATFORM", "ANCHOR_ARCHIVES_ID", "ANCHOR_NICK_NAME", "AC_F_MANAGE_ID", "AC_F_MANAGE_ENAME", "COOPERATE_ID", "COOPERATE_ENAME", "LIVE_EVENTS"};
        } else if (StConstant.TAB_ST_C_LIVE_CAST_STRATEGY_ITEM.equals(tableName)) {
            //直播解析策略明细
            focusColumn = new String[]{"RULE_TYPE", "RULE_CONTEXT"};
        } else if (StConstant.TAB_ST_C_UNFULLCAR_COST.equals(tableName)) {
            //零担报价设置
            focusColumn = new String[]{"CP_C_PHY_WAREHOUSE_ID", "CP_C_LOGISTICS_ID", "START_DATE", "END_DATE", "OIL_PRICE_LINKAGE", "REMARK", "ISACTIVE"};
        } else if (StConstant.TAB_ST_C_UNFULLCAR_COST_ITEM.equals(tableName)) {
            //零担报价设置明细
            focusColumn = new String[]{"PROVINCE_ID", "CITY_ID", "ARRIVAL_DAYS", "START_WEIGHT", "END_WEIGHT", "TRUNK_FREIGHT", "DELIVERY_FEE", "FREIGHT", "PREMIUM", "UNLOADING_FEE", "OTHER_FEE"};
        } else if (StConstant.TAB_ST_C_FULLCAR_COST.equals(tableName)) {
            //整车报价设置
            focusColumn = new String[]{"CP_C_PHY_WAREHOUSE_ID", "CP_C_LOGISTICS_ID", "START_DATE", "END_DATE", "OIL_PRICE_LINKAGE", "REMARK", "ISACTIVE"};
        } else if (StConstant.TAB_ST_C_FULLCAR_COST_ITEM.equals(tableName)) {
            //整车报价设置明细
            focusColumn = new String[]{"PROVINCE_ID", "CITY_ID", "ARRIVAL_DAYS", "START_WEIGHT", "END_WEIGHT", "TRUNK_FREIGHT", "DELIVERY_FEE", "FREIGHT", "PREMIUM", "UNLOADING_FEE", "OTHER_FEE"};
        } else if (StConstant.TAB_ST_C_EXPRESS_PRICE_STRATEGY.equals(tableName)) {
            //快递报价设置
            focusColumn = new String[]{"CP_C_PHY_WAREHOUSE_ID", "CP_C_LOGISTICS_ID", "START_DATE", "END_DATE", "PRICE_HOME_DELIVERY", "REMARK", "ISACTIVE"};
        } else if (StConstant.TAB_ST_C_EXPRESS_PRICE_STRATEGY_ITEM.equals(tableName)) {
            //快递报价设置明细
            focusColumn = new String[]{"PROVINCE_ID", "CITY_ID", "START_WEIGHT", "END_WEIGHT", "PRICE_EXPRESS", "PRICE_FIRST_WEIGHT", "PRICE_CONTINUED_WEIGHT"};
        } else if (StConstant.TAB_ST_C_EXPRESS_COST.equals(tableName)) {
            //快运报价设置
            focusColumn = new String[]{"CP_C_PHY_WAREHOUSE_ID", "CP_C_LOGISTICS_ID", "START_DATE", "END_DATE", "REMARK", "ISACTIVE"};
        } else if (StConstant.TAB_ST_C_EXPRESS_COST_ITEM.equals(tableName)) {
            //快运报价设置明细
            focusColumn = new String[]{"PROVINCE_ID", "CITY_ID", "START_WEIGHT", "END_WEIGHT", "PRICE_EXPRESS", "PRICE_FIRST_WEIGHT"};
        } else if (StConstant.TAB_ST_C_WAREHOUSE_LOGISTIC_STRATEGY.equals(tableName)) {
            //仓库物流设置
            focusColumn = new String[]{"CP_C_PHY_WAREHOUSE_ID", "REMARK", "IS_EXPIRE", "ISACTIVE"};
        } else if (StConstant.TAB_ST_C_WAREHOUSE_LOGISTIC_STRATEGY_ITEM.equals(tableName)) {
            //仓库物流设置明细
            focusColumn = new String[]{"CP_C_LOGISTICS_ID", "LOGISTICS_TYPE", "LOGISTICS_SUPPLIER_ID", "ITEM_PRIORITY", "EXPIRE_START_TIME", "EXPIRE_END_TIME", "ENABLE_MAXIMUM_ORDER_VOLUME", "MAXIMUM_ORDER_VOLUME", "ISACTIVE"};
        } else if (StConstant.TAB_ST_C_ALLOCATION_COST.equals(tableName)) {
            //调拨报价设置
            focusColumn = new String[]{"CP_C_LOGISTICS_ID", "START_DATE", "END_DATE", "OIL_PRICE_LINKAGE", "STATUS", "CLOSE_STATUS", "REMARK", "ISACTIVE"};
        } else if (StConstant.TAB_ST_C_ALLOCATION_COST_ITEM.equals(tableName)) {
            //调拨报价设置明细
            focusColumn = new String[]{"CP_C_PHY_WAREHOUSE_ID", "ARRIVAL_DAYS", "START_WEIGHT", "END_WEIGHT", "TRUNK_FREIGHT", "DELIVERY_FEE", "FREIGHT", "PREMIUM", "UNLOADING_FEE", "OTHER_FEE", "ISACTIVE"};
        } else if (StConstant.ST_C_CYCLE_PURCHASE_STRATEGY.equals(tableName)) {
            //周期购策略
            focusColumn = new String[]{"PS_C_PRO_ID", "PS_C_PRO_ENAME", "START_DATE", "END_DATE", "CYCLE_TYPE", "SEND_PATTERN", "TYPE", "QTY_CYCLE", "STATUS", "REMARK", "ISACTIVE"};
        } else if (StConstant.ST_C_CYCLE_PURCHASE_STRATEGY_ITEM.equals(tableName)) {
            //周期购策略明细
            focusColumn = new String[]{"PS_C_SKU_ID", "PS_C_PRO_ENAME", "QTY", "ISACTIVE"};
        } else if (StConstant.ST_C_PREORDER_MODEL_STRATEGY.equals(tableName)) {
            //发货单预导入模版
            focusColumn = new String[]{"CODE", "NAME", "SUBMIT_STATUS", "CP_C_SHOP_ECODE", "CP_C_SHOP_ID", "ISACTIVE"};
        } else if (StConstant.ST_C_PREORDER_FIELD_STRATEGY.equals(tableName)) {
            //发货单预导入模版明细
            focusColumn = new String[]{"STANDARD_FIELD", "CUSTOMIZE_FIELD"};
        } else if (StConstant.ST_C_PREORDER_ITEM_STRATEGY.equals(tableName)) {
            //发货单预导入模版商品明细
            focusColumn = new String[]{"ITEM_NAME", "PRICE_ACTUAL", "SKU_ECODE"};
        } else if (StConstant.ST_ADDED_SERVICE_TYPE_DOC.equals(tableName)) {
            // 增值服务策略档案
            focusColumn = new String[]{"ADDED_TYPE_NAME", "ISACTIVE"};
        } else if (StConstant.ST_ADDED_SERVICE_STRATEGY.equals(tableName)) {
            // 增值服务策略
            focusColumn = new String[]{"ADDED_STRATEGY_NAME", "ISACTIVE"};
        } else if (StConstant.ST_ADDED_SERVICE_STRATEGY_DETAIL.equals(tableName)) {
            // 增值服务策略详情
            focusColumn = new String[]{"ADDED_TYPE_DOC_ID", "UNIT_PRICE", "ISACTIVE"};
        }

        List<String> focusColumnList = Lists.newArrayList(focusColumn);
        focusColumnMap.put(tableName, focusColumnList);
    }

    /**
     * @param tableName
     * @param itemOperationKeyMap
     * @return java.util.Map
     * @Descroption 获取明细新增/删除操作展示键
     * @Author: 洪艺安
     * @Date 2020/2/18
     */
    private void getItemOperationKey(String tableName, Map<String, String> itemOperationKeyMap) {
        String itemOperationKey = "ID";
        if (StConstant.TAB_ST_C_SYNC_STOCK_STRATEGY_ITEM.equals(tableName)) {
            itemOperationKey = "CP_C_STORE_ID";
        } else if (StConstant.TAB_ST_C_SYNC_STOCK_STRATEGY_CHANNEL.equals(tableName)) {
            itemOperationKey = "CP_C_ORG_CHANNEL_ID";
        }
        itemOperationKeyMap.put(tableName, itemOperationKey);
    }

    /**
     * @param joinPoint
     * @param logChangeVo
     * @return void
     * @Description 解析入参
     * <AUTHOR>
     * @date 2020/2/19 10:35
     */
    private void resolvingStLog(JoinPoint joinPoint, StCLogChangeVo logChangeVo) {
        String operationType = logChangeVo.getOperationType();
        if (OperationTypeEnum.MOD.getOperationValue().equals(operationType)) {
            resolvingStLogByMod(joinPoint, logChangeVo);
        } else if (OperationTypeEnum.DEL.getOperationValue().equals(operationType)) {
            resolvingStLogByDel(joinPoint, logChangeVo);
        } else if (OperationTypeEnum.AUDIT.getOperationValue().equals(operationType)
                || OperationTypeEnum.RESERVE_AUDIT.getOperationValue().equals(operationType)
                || OperationTypeEnum.VOID.getOperationValue().equals(operationType)
                || OperationTypeEnum.FINISH.getOperationValue().equals(operationType)) {
            resolvingStCommonLog(joinPoint, logChangeVo);
        } else {
            throw new NDSException("日志操作类型注解不满足!");
        }

    }

    /**
     * 通用日志
     *
     * @param joinPoint
     * @param logChangeVo
     */
    private void resolvingStCommonLog(JoinPoint joinPoint, StCLogChangeVo logChangeVo) {
        Object[] params = joinPoint.getArgs();
        QuerySession session = getQuerySession(params);
        if (session != null) {
            DefaultWebEvent event = session.getEvent();
            JSONObject jsonParam = JSON.parseObject(JSON.toJSONStringWithDateFormat(
                            event.getParameterValue("param"), "yyyy-MM-dd HH:mm:ss", SerializerFeature.WriteMapNullValue),
                    Feature.OrderedField);
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("打印JsonObj：") + jsonParam);
            }
            if (jsonParam != null) {
                List<Long> ids = new ArrayList<>();
                if (jsonParam.getLong("objid") != null) {
                    ids.add(jsonParam.getLong("objid"));
                }
                JSONArray array = jsonParam.getJSONArray("ids");
                if (array != null && array.size() > 0) {
                    List<Long> parseArray = JSONObject.parseArray(array.toJSONString(), Long.class);
                    if (!CollectionUtils.isEmpty(parseArray)) {
                        ids.addAll(parseArray);
                    }
                }
                if (!CollectionUtils.isEmpty(ids)) {
                    for (Long updateId : ids) {
                        insertLog(session, logChangeVo, updateId);
                    }
                }
            }
        }
    }

    /**
     * 通用数据插入
     *
     * @param session
     * @param logChangeVo
     * @param updateId
     */
    private void insertLog(QuerySession session, StCLogChangeVo logChangeVo, Long updateId) {
        String tableName = logChangeVo.getTableName();
        String operationType = logChangeVo.getOperationType();
        Table table = session.getTableManager().getTable(tableName);
        StCOperationLogDO operationLog = getOperationLog(tableName, operationType, updateId, table.getDescription(), "", "", "", session.getUser());
        operationLogMapper.insert(operationLog);

    }

    /**
     * @param joinPoint
     * @param logChangeVo
     * @return void
     * @Description 修改日志
     * <AUTHOR>
     * @date 2020/2/19 10:43
     */
    private void resolvingStLogByMod(JoinPoint joinPoint, StCLogChangeVo logChangeVo) {
        Object[] params = joinPoint.getArgs();
        QuerySession session = getQuerySession(params);
        if (session != null) {
            DefaultWebEvent event = session.getEvent();
            JSONObject jsonParam = JSON.parseObject(JSON.toJSONStringWithDateFormat(
                            event.getParameterValue("param"), "yyyy-MM-dd HH:mm:ss", SerializerFeature.WriteMapNullValue),
                    Feature.OrderedField);
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("打印JsonObj：") + jsonParam);
            }
            if (jsonParam != null) {
                JSONObject fixColumn = jsonParam.getJSONObject("fixcolumn");
                JSONObject beforeValue = jsonParam.getJSONObject("beforevalue");
                JSONObject afterValue = jsonParam.getJSONObject("aftervalue");
                Long updateId = jsonParam.getLong("objid");
                if (fixColumn != null) {
                    //获取主表信息
                    JSONObject mainFixColumnData = fixColumn.getJSONObject(logChangeVo.getTableName());
                    //主表插入
                    if (mainFixColumnData != null) {
                        if (beforeValue != null && afterValue != null) {
                            JSONObject beforeData = beforeValue.getJSONObject(logChangeVo.getTableName());
                            JSONObject afterData = afterValue.getJSONObject(logChangeVo.getTableName());
                            insertMainStLog(session, logChangeVo, updateId, mainFixColumnData, beforeData, afterData);
                        }
                    }
                    //明细逐个表处理
                    List<String> itemTableList = logChangeVo.getItemsTableName();
                    if (!CollectionUtils.isEmpty(itemTableList)) {
                        for (String itemTableName : itemTableList) {
                            JSONArray itemFixColumnDataArr = fixColumn.getJSONArray(itemTableName);
                            JSONArray beforeDataArr = null;
                            JSONArray afterDataArr = null;
                            if (beforeValue != null && afterValue != null) {
                                beforeDataArr = beforeValue.getJSONArray(itemTableName);
                                afterDataArr = afterValue.getJSONArray(itemTableName);
                            }
                            if (null != itemFixColumnDataArr && itemFixColumnDataArr.size() > 0) {
                                insertItemStLog(session, logChangeVo, updateId, itemTableName, itemFixColumnDataArr, beforeDataArr, afterDataArr);
                            }
                        }
                    }
                }
            }
        }
    }

    /**
     * @param joinPoint
     * @param logChangeVo
     * @return void
     * @Description 删除日志
     * <AUTHOR>
     * @date 2020/2/19 10:44
     */
    private void resolvingStLogByDel(JoinPoint joinPoint, StCLogChangeVo logChangeVo) {
        Object[] params = joinPoint.getArgs();
        List<String> itemTableNames = logChangeVo.getItemsTableName();
        QuerySession session = getQuerySession(params);
        if (session != null) {
            DefaultWebEvent event = session.getEvent();
            JSONObject jsonParam = JSON.parseObject(JSON.toJSONStringWithDateFormat(
                            event.getParameterValue("param"), "yyyy-MM-dd HH:mm:ss", SerializerFeature.WriteMapNullValue),
                    Feature.OrderedField);
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("打印删除JsonObj：") + jsonParam);
            }
            if (jsonParam != null) {
                String isDel = jsonParam.getString("isdelmtable");
                if (StConstant.FALSE_STR.equals(isDel)) {
                    for (String itemTableName : itemTableNames) {
                        Long updateId = jsonParam.getLong("objid");
                        JSONObject tabitem = jsonParam.getJSONObject("tabitem");
                        JSONArray itemArray = tabitem.getJSONArray(itemTableName);
                        insertItemDelStLog(session, logChangeVo, updateId, itemTableName, itemArray);
                    }
                }
            }
        }
    }

    /**
     * @param params
     * @return com.jackrain.nea.web.query.QuerySession
     * @Description 获取QuerySession
     * <AUTHOR>
     * @date 2020/2/19 13:11
     */
    private QuerySession getQuerySession(Object[] params) {
        QuerySession session = null;
        for (Object param : params) {
            if (param instanceof QuerySession) {
                session = (QuerySession) param;
                break;
            }
        }
        return session;
    }

    /**
     * @param session
     * @param logChangeVo
     * @param updateId
     * @param fixColumnData
     * @param beforeData
     * @param afterData
     * @return void
     * @Description 主表数据插入
     * <AUTHOR>
     * @date 2020/2/18 16:25
     */
    private void insertMainStLog(QuerySession session, StCLogChangeVo logChangeVo, Long updateId, JSONObject
            fixColumnData, JSONObject beforeData, JSONObject afterData) {
        String tableName = logChangeVo.getTableName();
        String operationType = logChangeVo.getOperationType();
        List focusColomnList = logChangeVo.getFocusColomnMap().get(tableName);
        Table table = session.getTableManager().getTable(tableName);
        List<StCOperationLogDO> operationLogList = Lists.newArrayList();
        for (String key : fixColumnData.keySet()) {
            if (focusColomnList.contains(key)) {
                Column column = table.getColumn(key);
                if (column == null || !focusColomnList.contains(column.getName())) {
                    continue;
                }
                String columnName = column.getDescription(session.getLocale());
                if (StringUtils.isEmpty(columnName)) {
                    continue;
                }
                String columnBeforeValue = StringUtils.isNotEmpty(beforeData.getString(key)) ? beforeData.getString(key) : "";
                String columnAfterValue = StringUtils.isNotEmpty(afterData.getString(key)) ? afterData.getString(key) : "";
                if (columnBeforeValue != null && columnAfterValue != null && columnBeforeValue.compareTo(columnAfterValue) != 0) {
                    StCOperationLogDO operationLog = getOperationLog(tableName, operationType, updateId, table.getDescription(), columnName, columnBeforeValue, columnAfterValue, session.getUser());
                    operationLogList.add(operationLog);
                }
            }
        }
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("打印主表operationLogList的size：") + operationLogList.size());
        }
        if (operationLogList.size() > 0) {
            operationLogMapper.batchInsert(operationLogList);
        }
    }

    /**
     * @param session
     * @param logChangeVo
     * @param updateId
     * @param itemTableName
     * @param itemFixColumnDataArr
     * @param beforeDataArr
     * @param afterDataArr
     * @return void
     * @Descroption 明细数据插入
     * @Author: 洪艺安
     * @Date 2020/2/18
     */
    private void insertItemStLog(QuerySession session, StCLogChangeVo logChangeVo, Long updateId, String
            itemTableName, JSONArray itemFixColumnDataArr, JSONArray beforeDataArr, JSONArray afterDataArr) {
        String operationType = logChangeVo.getOperationType();
        List focusColomnList = logChangeVo.getFocusColomnMap().get(itemTableName);
        String itemOperationKey = logChangeVo.getItemOperationKey().get(itemTableName);
        Table table = session.getTableManager().getTable(itemTableName);
        List<StCOperationLogDO> operationLogList = Lists.newArrayList();
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("进入修改itemFixColumnDataArr:") + itemFixColumnDataArr);
        }
        for (Object item : itemFixColumnDataArr) {
            JSONObject itemFixColumnData = (JSONObject) item;
            JSONObject itemBeforeDataData = null;
            JSONObject itemAfterDataData = null;
            Long itemId = itemFixColumnData.getLong("ID");
            //判断明细新增还是修改
            if (itemFixColumnData.getLong("ID") == -1) {
                for (String key : itemFixColumnData.keySet()) {
                    if (focusColomnList.contains(key)) {
                        Column column = table.getColumn(key);
                        if (column == null) {
                            continue;
                        }
                        String columnName = column.getDescription(session.getLocale());
                        if (StringUtils.isEmpty(columnName)) {
                            continue;
                        }
                        String colomnContent = getColomnContent(table, itemTableName, key, itemFixColumnData.getString(key));
                        operationType = OperationTypeEnum.ADD.getOperationValue();
                        StCOperationLogDO operationLog = getOperationLog(itemTableName, operationType, updateId, table.getDescription(), columnName, "", colomnContent, session.getUser());
                        operationLogList.add(operationLog);
                    }
                }
            } else {
                Column itemOperationKeyColumn = table.getColumn(itemOperationKey);
                //根据ItemId获取对应的JSONObj修改前后对象
                for (Object value : beforeDataArr) {
                    JSONObject beforeDataDataTmp = (JSONObject) value;
                    Long beforeItemId = beforeDataDataTmp.getLong("ID");
                    if (itemId.equals(beforeItemId)) {
                        itemBeforeDataData = beforeDataDataTmp;
                    }
                }
                for (Object o : afterDataArr) {
                    JSONObject afterDataDataTmp = (JSONObject) o;
                    Long afterItemId = afterDataDataTmp.getLong("ID");
                    if (itemId.equals(afterItemId)) {
                        itemAfterDataData = afterDataDataTmp;
                    }
                }
                //遍历修改字段
                for (String key : itemFixColumnData.keySet()) {
                    if (focusColomnList.contains(key)) {
                        Column column = table.getColumn(key);
                        if (column == null || !focusColomnList.contains(column.getName())) {
                            continue;
                        }
                        String columnName = column.getDescription(session.getLocale());
                        if (StringUtils.isEmpty(columnName)) {
                            continue;
                        }
                        String modContent = String.format("[%s-%s]:%s", itemOperationKeyColumn.getDescription(session.getLocale()), itemId, columnName);
                        if (itemBeforeDataData != null && itemAfterDataData != null) {
                            String columnBeforeValue = StringUtils.isNotEmpty(itemBeforeDataData.getString(key)) ? itemBeforeDataData.getString(key) : "";
                            String columnAfterValue = StringUtils.isNotEmpty(itemAfterDataData.getString(key)) ? itemAfterDataData.getString(key) : "";
                            if (columnBeforeValue != null && columnAfterValue != null && columnBeforeValue.compareTo(columnAfterValue) != 0) {
                                StCOperationLogDO operationLog = getOperationLog(itemTableName, operationType, updateId, table.getDescription(), modContent, columnBeforeValue, columnAfterValue, session.getUser());
                                operationLogList.add(operationLog);
                            }
                        }
                    }
                }
            }
        }
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("打印明细operationLogList的size：") + operationLogList.size());
        }
        if (operationLogList.size() > 0) {
            operationLogMapper.batchInsert(operationLogList);
        }
    }


    /**
     * 删除明细记录日志
     *
     * @param session
     * @param logChangeVo
     * @param updateId
     * @param itemTableName
     * @param itemArray
     */
    public void insertItemDelStLog(QuerySession session, StCLogChangeVo logChangeVo, Long updateId, String
            itemTableName, JSONArray itemArray) {
        if (itemArray == null || itemArray.size() == 0) {
            return;
        }
        Table table = session.getTableManager().getTable(itemTableName);
        List<String> itemFixColomnList = logChangeVo.getFocusColomnMap().get(itemTableName);
        List<StCOperationLogDO> operationLogList = Lists.newArrayList();
        //获取原对象
        Map beforeDelObjMap = (Map) session.getAttribute("beforeDelObjMap");
        if (MapUtils.isEmpty(beforeDelObjMap)) {
            return;
        }
        for (int i = 0; i < itemArray.size(); i++) {
            Long itemId = itemArray.getLong(i);
            String itemJSONStr = (String) beforeDelObjMap.get(itemId);
            if (StringUtils.isEmpty(itemJSONStr)) {
                continue;
            }
            JSONObject preItemJSONObj = JSON.parseObject(itemJSONStr);
            String columnBeforeValue = "";
            StringBuilder sb = new StringBuilder();
            if (!org.springframework.util.CollectionUtils.isEmpty(itemFixColomnList)) {
                for (int j = 0; j < itemFixColomnList.size(); j++) {
                    String itemColomn = itemFixColomnList.get(j);
                    String colomnKey = preItemJSONObj.getString(itemColomn);
                    if (StringUtils.isNotEmpty(colomnKey)) {
                        String content = getColomnContent(table, itemTableName, itemColomn, colomnKey);
                        if (sb.length() == 0) {
                            sb.append("[" + content + "]");
                        } else {
                            sb.append(",[" + content + "]");
                        }
                    }
                }
                if (sb.length() > 0) {
                    columnBeforeValue = sb.toString();
                }
            }
            StCOperationLogDO operationLog = getOperationLog(itemTableName, logChangeVo.getOperationType(), updateId, table.getDescription(), "删除" + table.getDescription(), columnBeforeValue, "", session.getUser());
            operationLogList.add(operationLog);
        }
        if (operationLogList.size() > 0) {
            operationLogMapper.batchInsert(operationLogList);
        }
    }

    /**
     * 根据字段类型获得显示值
     *
     * @param table
     * @param itemTableName
     * @param itemColomn
     * @param colomnKey
     * @return
     */
    private String getColomnContent(Table table, String itemTableName,
                                    String itemColomn,
                                    String colomnKey) {
        List<String> warehouseField = getWarehouseField(itemTableName);
        List<String> regionField = getRegionField(itemTableName);
        List<String> limitvalueGroupField = getLimitvalueGroupField(itemTableName);
        List<String> proDimField = getProDimField(itemTableName);
        List<String> proField = getProField(itemTableName);
        List<String> skuField = getSkuField(itemTableName);
        List<String> logisticsField = getLogisticsField(itemTableName);
        List<String> logisticsSupplierField = getLogisticsSupplierField(itemTableName);
        String content = colomnKey;
        if (warehouseField.contains(itemColomn)) {
            content = queryWarehouseName(Long.valueOf(colomnKey));
        } else if (regionField.contains(itemColomn)) {
            content = queryRegionName(Long.valueOf(colomnKey));
        } else if (limitvalueGroupField.contains(itemColomn)) {
            content = queryLimitvalueGroupName(table, itemColomn, colomnKey);
        } else if (proDimField.contains(itemColomn)) {
            content = queryProDimName(Long.valueOf(colomnKey));
        } else if (proField.contains(itemColomn)) {
            content = queryProCode(Long.valueOf(colomnKey));
        } else if (logisticsField.contains(itemColomn)) {
            content = queryLogisticsName(Long.valueOf(colomnKey));
        } else if (logisticsSupplierField.contains(itemColomn)) {
            content = queryLogisticsSupplierName(Long.valueOf(colomnKey));
        } else if (skuField.contains(itemColomn)) {
            content = querySkuCode(Long.valueOf(colomnKey));
        }
        return content;
    }

    private String queryLogisticsSupplierName(Long id) {
        String name = "";
        if (id == null) {
            return name;
        }
        ValueHolderV14<CpCSupplier> v14 = csupplierQueryCmd.queryCpSupplierById(id);
        if (v14 != null && v14.getData() != null) {
            name = v14.getData().getEname();
        }
        return name;
    }

    private List<String> getLogisticsSupplierField(String tableName) {
        String[] focusColumn = new String[]{};
        if (StConstant.TAB_ST_C_WAREHOUSE_LOGISTIC_STRATEGY_ITEM.equals(tableName)) {
            focusColumn = new String[]{"LOGISTICS_SUPPLIER_ID"};
        }
        return Lists.newArrayList(focusColumn);
    }

    private String queryLogisticsName(Long id) {
        String name = "";
        if (id == null) {
            return name;
        }
        ValueHolderV14<Map<Long, CpLogistics>> v14 = cpLogisticsQueryCmd.queryLogisticsByIds(Lists.newArrayList(id));
        if (v14 != null && MapUtils.isNotEmpty(v14.getData()) && v14.getData().get(id) != null) {
            name = v14.getData().get(id).getEname();
        }
        return name;
    }

    private List<String> getLogisticsField(String itemTableName) {
        String[] focusColumn = new String[]{};
        if (StConstant.ST_C_SHOP_LOGISTIC_STRATEGY_ITEM.equals(itemTableName)) {
            focusColumn = new String[]{"CP_C_LOGISTICS_ID"};
        } else if (StConstant.TAB_ST_C_SHOP_STRATEGY_LOGISTICS_ITEM.equals(itemTableName)) {
            focusColumn = new String[]{"CP_C_LOGISTICS_ID"};
        } else if (StConstant.TAB_ST_C_WAREHOUSE_LOGISTIC_STRATEGY_ITEM.equals(itemTableName)) {
            focusColumn = new String[]{"CP_C_LOGISTICS_ID"};
        }
        return Lists.newArrayList(focusColumn);
    }

    private String queryProCode(Long id) {
        String name = "";
        if (id == null) {
            return name;
        }
        ValueHolderV14<PsCPro> proV14 = psCProQueryCmd.queryProById(id);
        if (proV14 != null && proV14.getData() != null) {
            name = proV14.getData().getEcode();
        }
        return name;
    }

    private List<String> getProField(String itemTableName) {
        String[] focusColumn = new String[]{};
        if (StConstant.ST_C_SHOP_LOGISTIC_STRATEGY_ITEM.equals(itemTableName)) {
            focusColumn = new String[]{"PS_C_PRO_ID"};
        }
        return Lists.newArrayList(focusColumn);
    }

    private String querySkuCode(Long id) {
        String code = "";
        if (id == null) {
            return code;
        }
        ValueHolderV14<PsCSku> v14 = psCSkuQueryCmd.querySkuById(id);
        if (v14 != null && v14.getData() != null) {
            code = v14.getData().getEcode();
        }
        return code;
    }

    private List<String> getSkuField(String itemTableName) {
        String[] focusColumn = new String[]{};
        if (StConstant.ST_C_CYCLE_PURCHASE_STRATEGY_ITEM.equals(itemTableName)) {
            focusColumn = new String[]{"PS_C_SKU_ID"};
        }
        return Lists.newArrayList(focusColumn);
    }

    private String queryProDimName(Long id) {
        String name = "";
        if (id == null) {
            return name;
        }
        PsCProdimItem psCProdimItem = cproDimItemQueryCmd.queryPsCProDimItem(id);
        if (psCProdimItem != null) {
            name = psCProdimItem.getEname();
        }
        return name;
    }

    private List<String> getProDimField(String itemTableName) {
        String[] focusColumn = new String[]{};
        if (StConstant.ST_C_SHOP_LOGISTIC_STRATEGY_ITEM.equals(itemTableName)) {
            focusColumn = new String[]{"PS_C_PRODIM_ID"};
        }
        return Lists.newArrayList(focusColumn);
    }

    /**
     * 地区字段
     *
     * @param tableName
     * @return
     */
    public List<String> getRegionField(String tableName) {
        String[] focusColumn = new String[]{};
        if (StConstant.ST_C_SHOP_LOGISTIC_STRATEGY_ITEM.equals(tableName)) {
            focusColumn = new String[]{"CP_C_PROVINCE_ID", "CP_C_CITY_ID"};
        } else if (StConstant.TAB_ST_C_UNFULLCAR_COST_ITEM.equals(tableName)) {
            focusColumn = new String[]{"PROVINCE_ID", "CITY_ID"};
        } else if (StConstant.TAB_ST_C_FULLCAR_COST_ITEM.equals(tableName)) {
            focusColumn = new String[]{"PROVINCE_ID", "CITY_ID"};
        } else if (StConstant.TAB_ST_C_EXPRESS_PRICE_STRATEGY_ITEM.equals(tableName)) {
            focusColumn = new String[]{"PROVINCE_ID", "CITY_ID"};
        } else if (StConstant.TAB_ST_C_EXPRESS_COST_ITEM.equals(tableName)) {
            focusColumn = new String[]{"PROVINCE_ID", "CITY_ID"};
        }
        return Lists.newArrayList(focusColumn);
    }

    public String queryRegionName(Long id) {
        String name = "";
        if (id == null) {
            return name;
        }
        CpCRegion cpCRegion = regionQueryExtCmd.queryRegionById(id);
        if (cpCRegion != null) {
            name = cpCRegion.getEname();
        }
        return name;
    }

    /**
     * 仓库字段
     *
     * @param tableName
     * @return
     */
    public List<String> getWarehouseField(String tableName) {
        String[] focusColumn = new String[]{};
        if (StConstant.ST_C_SHOP_LOGISTIC_STRATEGY_ITEM.equals(tableName)) {
            focusColumn = new String[]{"CP_C_PHY_WAREHOUSE_ID"};
        } else if (StConstant.TAB_ST_C_ALLOCATION_COST_ITEM.equals(tableName)) {
            focusColumn = new String[]{"CP_C_PHY_WAREHOUSE_ID"};
        }
        return Lists.newArrayList(focusColumn);
    }

    /**
     * 查询实体仓名称
     *
     * @param id
     * @return
     */
    public String queryWarehouseName(Long id) {
        String name = "";
        if (id == null) {
            return name;
        }
        CpCPhyWarehouse warehouse = cpcPhyWareHouseQueryCmd.queryCpCPhyWarehouseById(id);
        if (warehouse != null) {
            name = warehouse.getEname();
        }
        return name;
    }

    /**
     * 元数据字段选项组字段
     *
     * @param tableName
     * @return
     */
    public List<String> getLimitvalueGroupField(String tableName) {
        String[] focusColumn = new String[]{};
        if (StConstant.TAB_ST_C_PRICE_EXCLUDE_ITEM.equals(tableName)) {
            focusColumn = new String[]{"DISCERN_RULE"};
        } else if (StConstant.ST_C_SHOP_LOGISTIC_STRATEGY_ITEM.equals(tableName)) {
            focusColumn = new String[]{"LOGISTIC_TYPE"};
        } else if (StConstant.TAB_ST_C_SHOP_STRATEGY_LOGISTICS_ITEM.equals(tableName)) {
            focusColumn = new String[]{"ISACTIVE"};
        } else if (StConstant.TAB_ST_C_LIVE_CAST_STRATEGY_ITEM.equals(tableName)) {
            focusColumn = new String[]{"RULE_TYPE"};
        } else if (StConstant.TAB_ST_C_WAREHOUSE_LOGISTIC_STRATEGY_ITEM.equals(tableName)) {
            focusColumn = new String[]{"LOGISTICS_TYPE", "ISACTIVE"};
        } else if (StConstant.TAB_ST_C_ALLOCATION_COST_ITEM.equals(tableName)) {
            focusColumn = new String[]{"ISACTIVE"};
        } else if (StConstant.ST_C_PREORDER_FIELD_STRATEGY.equals(tableName)) {
            focusColumn = new String[]{"STANDARD_FIELD"};
        }
        return Lists.newArrayList(focusColumn);
    }

    /**
     * 查询字段选项组对应字段值
     *
     * @param table
     * @param itemColomn
     * @param key
     * @return
     */
    public String queryLimitvalueGroupName(Table table, String itemColomn, String key) {
        if (StringUtils.isEmpty(key)) {
            return "";
        }
        String name = key;
        if (table != null) {
            Column column = table.getColumn(itemColomn);
            if (column != null) {
                LimitvalueGroup limitvalueGroup = column.getLimitvalueGroup();
                if (limitvalueGroup != null) {
                    ArrayList<Limitvalue> limitvalues = limitvalueGroup.getAdLimitvalues();
                    if (org.apache.commons.collections.CollectionUtils.isNotEmpty(limitvalues)) {
                        for (Limitvalue limitvalue : limitvalues) {
                            if (key.equals(limitvalue.getValue())) {
                                name = limitvalue.getDescription();
                                break;
                            }
                        }
                    }
                }
            }
        }
        return name;
    }
}
