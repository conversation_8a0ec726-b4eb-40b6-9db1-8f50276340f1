//package com.jackrain.nea.st.services;
//
//import com.alibaba.fastjson.JSON;
//import com.alibaba.fastjson.JSONArray;
//import com.alibaba.fastjson.JSONObject;
//import com.google.common.collect.Lists;
//import com.jackrain.nea.core.schema.Column;
//import com.jackrain.nea.core.schema.Limitvalue;
//import com.jackrain.nea.core.schema.LimitvalueGroup;
//import com.jackrain.nea.core.schema.Table;
//import com.jackrain.nea.cpext.api.CpcPhyWareHouseQueryCmd;
//import com.jackrain.nea.cpext.api.RegionQueryExtCmd;
//import com.jackrain.nea.cpext.model.table.CpCPhyWarehouse;
//import com.jackrain.nea.cpext.model.table.CpCRegion;
//import com.jackrain.nea.model.util.ModelUtil;
//import com.jackrain.nea.st.common.StConstant;
//import com.jackrain.nea.st.mapper.StCOperationLogMapper;
//import com.jackrain.nea.st.model.enums.OperationTypeEnum;
//import com.jackrain.nea.st.model.table.StCOperationLogDO;
//import com.jackrain.nea.st.model.vo.StCLogChangeVo;
//import com.jackrain.nea.st.utils.StBeanUtils;
//import com.jackrain.nea.web.face.User;
//import com.jackrain.nea.web.query.QuerySession;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.commons.collections.CollectionUtils;
//import org.apache.commons.collections.MapUtils;
//import org.apache.commons.lang3.StringUtils;
//import org.apache.dubbo.config.annotation.DubboReference;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.stereotype.Component;
//
//import java.util.ArrayList;
//import java.util.List;
//import java.util.Map;
//
///**
// * <AUTHOR>
// * @Date 2023/1/12 18:15
// * @Description
// */
//@Component
//@Slf4j
////public class LogCommonDeleteItemService {
////
////    @Autowired
////    private StCOperationLogMapper operationLogMapper;
////
////    @DubboReference(group = "cp-ext", version = "1.0")
////    private CpcPhyWareHouseQueryCmd cpcPhyWareHouseQueryCmd;
////
////    @DubboReference(version = "1.0", group = "cp-ext")
////    private RegionQueryExtCmd regionQueryExtCmd;
////
////    /**
////     * 删除明细记录日志
////     *
////     * @param session
////     * @param logChangeVo
////     * @param updateId
////     * @param itemTableName
////     * @param itemArray
////     */
////    public void insertItemDelStLog(QuerySession session, StCLogChangeVo logChangeVo, Long updateId, String itemTableName, JSONArray itemArray) {
////        if (itemArray == null || itemArray.size() == 0) {
////            return;
////        }
////        Table table = session.getTableManager().getTable(itemTableName);
////        List<String> itemFixColomnList = logChangeVo.getFocusColomnMap().get(itemTableName);
////        List<StCOperationLogDO> operationLogList = Lists.newArrayList();
////        List<String> warehouseField = getWarehouseField(itemTableName);
////        List<String> regionField = getRegionField(itemTableName);
////        List<String> limitvalueGroupField = getLimitvalueGroupField(itemTableName);
////        //获取原对象
////        Map beforeDelObjMap = (Map) session.getAttribute("beforeDelObjMap");
////        if (MapUtils.isEmpty(beforeDelObjMap)) {
////            return;
////        }
////        for (int i = 0; i < itemArray.size(); i++) {
////            Long itemId = itemArray.getLong(i);
////            String itemJSONStr = (String) beforeDelObjMap.get(itemId);
////            if (StringUtils.isEmpty(itemJSONStr)) {
////                continue;
////            }
////            JSONObject preItemJSONObj = JSON.parseObject(itemJSONStr);
////            String columnBeforeValue = "";
////            StringBuilder sb = new StringBuilder();
////            if (!org.springframework.util.CollectionUtils.isEmpty(itemFixColomnList)) {
////                for (int j = 0; j < itemFixColomnList.size(); j++) {
////                    String itemColomn = itemFixColomnList.get(j);
////                    String colomnKey = preItemJSONObj.getString(itemColomn);
////                    if (StringUtils.isNotEmpty(colomnKey)) {
////                        String content = getColomnContent(table, warehouseField, regionField, limitvalueGroupField, itemColomn, colomnKey);
////                        if (j == 0) {
////                            sb.append(content);
////                        } else {
////                            sb.append("," + content);
////                        }
////                    }
////                }
////                if (sb.length() > 0) {
////                    columnBeforeValue = sb.toString();
////                }
////            }
////            StCOperationLogDO operationLog = getOperationLog(itemTableName, logChangeVo.getOperationType(), updateId, table.getDescription(), "删除" + table.getDescription(), columnBeforeValue, "", session.getUser());
////            operationLogList.add(operationLog);
////        }
////        if (operationLogList.size() > 0) {
////            operationLogMapper.batchInsert(operationLogList);
////        }
////    }
////
////    /**
////     * 根据字段类型获得显示值
////     *
////     * @param table
////     * @param warehouseField
////     * @param regionField
////     * @param limitvalueGroupField
////     * @param itemColomn
////     * @param colomnKey
////     * @return
////     */
////    private String getColomnContent(Table table, List<String> warehouseField,
////                                    List<String> regionField,
////                                    List<String> limitvalueGroupField,
////                                    String itemColomn,
////                                    String colomnKey) {
////        String content = colomnKey;
////        if (warehouseField.contains(itemColomn)) {
////            content = queryWarehouseName(Long.valueOf(colomnKey));
////        } else if (regionField.contains(itemColomn)) {
////            content = queryRegionName(Long.valueOf(colomnKey));
////        } else if (limitvalueGroupField.contains(itemColomn)) {
////            content = queryLimitvalueGroupName(table, itemColomn, colomnKey);
////        }
////        content = "[" + content + "]";
////        return content;
////    }
////
////    /**
////     * 地区字段
////     *
////     * @param tableName
////     * @return
////     */
////    public List<String> getRegionField(String tableName) {
////        String[] focusColumn = new String[]{};
////        List<String> focusColumnList = Lists.newArrayList(focusColumn);
////        return focusColumnList;
////    }
////
////    public String queryRegionName(Long id) {
////        String name = "";
////        if (id == null) {
////            return name;
////        }
////        CpCRegion cpCRegion = regionQueryExtCmd.queryRegionById(id);
////        if (cpCRegion != null) {
////            name = cpCRegion.getEname();
////        }
////        return name;
////    }
////
////    /**
////     * 仓库字段
////     *
////     * @param tableName
////     * @return
////     */
////    public List<String> getWarehouseField(String tableName) {
////        String[] focusColumn = new String[]{};
////        List<String> focusColumnList = Lists.newArrayList(focusColumn);
////        return focusColumnList;
////    }
////
////    /**
////     * 查询实体仓名称
////     *
////     * @param id
////     * @return
////     */
////    public String queryWarehouseName(Long id) {
////        String name = "";
////        if (id == null) {
////            return name;
////        }
////        CpCPhyWarehouse warehouse = cpcPhyWareHouseQueryCmd.queryCpCPhyWarehouseById(id);
////        if (warehouse != null) {
////            name = warehouse.getEname();
////        }
////        return name;
////    }
////
////    /**
////     * 元数据字段选项组字段
////     *
////     * @param tableName
////     * @return
////     */
////    public List<String> getLimitvalueGroupField(String tableName) {
////        String[] focusColumn = new String[]{};
////        if (StConstant.TAB_ST_C_PRICE_EXCLUDE_ITEM.equals(tableName)) {
////            focusColumn = new String[]{"DISCERN_RULE"};
////        }
////        List<String> focusColumnList = Lists.newArrayList(focusColumn);
////        return focusColumnList;
////    }
////
////    /**
////     * 查询字段选项组对应字段值
////     *
////     * @param table
////     * @param itemColomn
////     * @param key
////     * @return
////     */
////    public String queryLimitvalueGroupName(Table table, String itemColomn, String key) {
////        if (StringUtils.isEmpty(key)) {
////            return "";
////        }
////        String name = key;
////        if (table != null) {
////            Column column = table.getColumn(itemColomn);
////            if (column != null) {
////                LimitvalueGroup limitvalueGroup = column.getLimitvalueGroup();
////                if (limitvalueGroup != null) {
////                    ArrayList<Limitvalue> limitvalues = limitvalueGroup.getAdLimitvalues();
////                    if (CollectionUtils.isNotEmpty(limitvalues)) {
////                        for (Limitvalue limitvalue : limitvalues) {
////                            if (key.equals(limitvalue.getValue())) {
////                                name = limitvalue.getDescription();
////                                break;
////                            }
////                        }
////                    }
////                }
////            }
////        }
////        return name;
////    }
////
////    /**
////     * @param tableName
////     * @param operationType
////     * @param updateId
////     * @param tableDescription
////     * @param columnName
////     * @param columnBeforeValue
////     * @param columnAfterValue
////     * @param user
////     * @return
////     * @Descroption 生成日志对象
////     */
////    public StCOperationLogDO getOperationLog(String tableName, String operationType, Long updateId, String tableDescription, String columnName, String columnBeforeValue, String columnAfterValue, User user) {
////        StCOperationLogDO operationLog = new StCOperationLogDO();
////        operationLog.setId(ModelUtil.getSequence(StConstant.TAB_ST_C_OPERATION_LOG));
////        operationLog.setTableName(tableName);
////        operationLog.setOperationType(OperationTypeEnum.getNameByValue(operationType));
////        operationLog.setUpdateId(updateId);
////        operationLog.setUpdateModelName(tableDescription);
////        operationLog.setModContent(columnName);
////        operationLog.setBeforeData(columnBeforeValue);
////        operationLog.setAfterData(columnAfterValue);
////        StBeanUtils.makeCreateField(operationLog, user);
////        return operationLog;
////    }
////}
