package com.jackrain.nea.st.services;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.jackrain.nea.oc.oms.model.enums.YesNoEnum;
import com.jackrain.nea.st.mapper.StCExpressPriceStrategyItemMapper;
import com.jackrain.nea.st.mapper.StCExpressPriceStrategyMapper;
import com.jackrain.nea.st.model.enums.StCExpressPriceStrategyEnum;
import com.jackrain.nea.st.model.request.StCExpressPriceStrategyQueryRequest;
import com.jackrain.nea.st.model.request.StCExpressPriceStrategyRelationQueryRequest;
import com.jackrain.nea.st.model.result.StCExpressPriceStrategyQueryResult;
import com.jackrain.nea.st.model.result.StCExpressPriceStrategyRelation;
import com.jackrain.nea.st.model.table.StCExpressPriceStrategyDO;
import com.jackrain.nea.st.model.table.StCExpressPriceStrategyItemDO;
import com.jackrain.nea.sys.CommandAdapter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2022/6/9 17:42
 * @Description 快递报价设置 策略 保存的新增修改业务逻辑
 * @Version 1.0
 */
@Component
@Slf4j
public class StCExpressPriceStrategyQueryService extends CommandAdapter {

    @Resource
    private StCExpressPriceStrategyMapper mapper;

    @Resource
    private StCExpressPriceStrategyItemMapper itemMapper;

    public List<StCExpressPriceStrategyQueryResult> queryStCExpressPriceByParams(StCExpressPriceStrategyQueryRequest request) {

        if (request == null) {
            return new ArrayList<>();
        }
        if (request.getCpCPhyWarehouseId() == null) {
            log.error("实体仓不能为空!");
            return new ArrayList<>();
        }
        if (request.getCpCProvinceId() == null) {
            log.error("省不能为空!");
            return new ArrayList<>();
        }

        return mapper.queryStCExpressPriceByParams(request);
    }

    public List<StCExpressPriceStrategyRelation> queryExpressPriceRelation(StCExpressPriceStrategyRelationQueryRequest request) {
        if (request == null) {
            return new ArrayList<>();
        }
        if (request.getCpCPhyWarehouseId() == null) {
            log.error("实体仓不能为空!");
            return new ArrayList<>();
        }
        if (request.getCpCLogisticsId() == null) {
            log.error("物流公司不能为空!");
            return new ArrayList<>();
        }
        List<StCExpressPriceStrategyRelation> relations = new ArrayList<>();
        List<StCExpressPriceStrategyDO> strategyDOS =
                mapper.selectList(new LambdaQueryWrapper<StCExpressPriceStrategyDO>()
                        .eq(StCExpressPriceStrategyDO::getCpCPhyWarehouseId, request.getCpCPhyWarehouseId())
                        .eq(StCExpressPriceStrategyDO::getCpCLogisticsId, request.getCpCLogisticsId())
                        .eq(StCExpressPriceStrategyDO::getStatus, StCExpressPriceStrategyEnum.SUBMITTED.getKey())
                        .eq(StCExpressPriceStrategyDO::getCloseStatus, StCExpressPriceStrategyEnum.UN_CLOSED.getKey())
                        .ge(StCExpressPriceStrategyDO::getEndDate, new Date())
                        .eq(StCExpressPriceStrategyDO::getIsactive, YesNoEnum.Y.getKey()));
        if (CollectionUtils.isEmpty(strategyDOS)) {
            return new ArrayList<>();
        }
        List<Long> strategyIds = strategyDOS.stream().map(StCExpressPriceStrategyDO::getId).collect(Collectors.toList());
        List<StCExpressPriceStrategyItemDO> strategyItemDOS = itemMapper.selectList(new LambdaQueryWrapper<StCExpressPriceStrategyItemDO>()
                .in(StCExpressPriceStrategyItemDO::getStCExpressPriceStrategyId, strategyIds)
                .eq(StCExpressPriceStrategyItemDO::getIsactive, YesNoEnum.Y.getKey()));
        if (CollectionUtils.isEmpty(strategyItemDOS)) {
            log.error("策略明细为空!");
            return new ArrayList<>();
        }
        Map<Long, List<StCExpressPriceStrategyItemDO>> itemGroupMap =
                strategyItemDOS.stream().collect(Collectors.groupingBy(StCExpressPriceStrategyItemDO::getStCExpressPriceStrategyId));
        for (StCExpressPriceStrategyDO strategyDO : strategyDOS) {
            StCExpressPriceStrategyRelation relation = new StCExpressPriceStrategyRelation();
            relations.add(relation);
            relation.setStCExpressPriceStrategyDO(strategyDO);
            relation.setStCExpressPriceStrategyItemDOList(itemGroupMap.get(strategyDO.getId()));
        }
        return relations;
    }

    /**
     * 根据主表ID查询快递报价策略关系
     *
     * @param id 快递报价策略主表ID
     * @return 快递报价策略关系对象，包含主表信息和明细列表
     */
    public StCExpressPriceStrategyRelation queryExpressPriceRelationById(Long id) {
        if (id == null) {
            log.error("主表ID不能为空!");
            return null;
        }

        // 根据ID查询主表信息
        StCExpressPriceStrategyDO strategyDO = mapper.selectById(id);
        if (strategyDO == null) {
            log.error("未找到ID为{}的快递报价策略!", id);
            return null;
        }

        // 查询对应的明细信息
        List<StCExpressPriceStrategyItemDO> strategyItemDOS = itemMapper.selectList(
                new LambdaQueryWrapper<StCExpressPriceStrategyItemDO>()
                        .eq(StCExpressPriceStrategyItemDO::getStCExpressPriceStrategyId, id)
                        .eq(StCExpressPriceStrategyItemDO::getIsactive, YesNoEnum.Y.getKey())
        );

        // 构建返回对象
        StCExpressPriceStrategyRelation relation = new StCExpressPriceStrategyRelation();
        relation.setStCExpressPriceStrategyDO(strategyDO);
        relation.setStCExpressPriceStrategyItemDOList(strategyItemDOS);

        return relation;
    }
}