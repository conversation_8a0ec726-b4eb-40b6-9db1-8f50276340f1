package com.jackrain.nea.st.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.st.common.StConstant;
import com.jackrain.nea.st.mapper.StCDistributionMapper;
import com.jackrain.nea.st.mapper.StCExpressAreaMapper;
import com.jackrain.nea.st.model.table.StCDistributionDO;
import com.jackrain.nea.st.model.table.StCExpressAreaDO;
import com.jackrain.nea.st.utils.StBeanUtils;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.HashMap;

/**
 * @Descroption 物流区域策略作废
 * <AUTHOR>
 * @Date 2019/3/13 15:55
 */
@Component
@Slf4j
public class ExpressAreaVoidService extends CommandAdapter {
    @Autowired
    private StCExpressAreaMapper mapper;

    @Override
    public ValueHolder execute(QuerySession querySession) throws NDSException {
        //1.获取传入参数
        DefaultWebEvent event = querySession.getEvent();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(event.getParameterValue("param"),
                "yyyy-MM-dd HH:mm:ss", SerializerFeature.WriteMapNullValue), Feature.OrderedField);
        if (param == null) {
            throw new NDSException("参数为空！");
        }
        log.debug(LogUtil.format("Start ExpressAreaVoidService.execute. ReceiveParams: {}"), param.toJSONString());
        HashMap<Long, Object> errMap = new HashMap<Long, Object>();
        //3.生成审核Json数组
        JSONArray voidArray = StBeanUtils.makeVoidJsonArray(param);
        for (int i = 0; i < voidArray.size(); i++) {
            Long id = voidArray.getLong(i);
            try {
                //4.遍历作废方法
                voidExpressArea(id, querySession);
            } catch (Exception e) {
                errMap.put(id, e.getMessage());
            }
        }
        //5.返回ValueHolder对象
        return StBeanUtils.getExcuteVoidValueHolder(voidArray.size(), errMap);
    }

    /**
     * @param id
     * @param querySession
     * @return void
     * @Descroption 检查并更新作废状态
     * @Author: 洪艺安
     * @Date 2019/3/13
     */
    public void voidExpressArea(Long id, QuerySession querySession) {
        StCExpressAreaDO expressArea = mapper.selectById(id);
        //主表校验
        checkExpressArea(expressArea);
        //更新作废状态
        StBeanUtils.makeModifierField(expressArea, querySession.getUser());
        expressArea.setIsactive(StConstant.ISACTIVE_N);//作废
        setVoidCommonField(expressArea, querySession.getUser());
        int updateNum = mapper.updateById(expressArea);
        if (updateNum < 0) {
            throw new NDSException("物流公司:" + expressArea.getCpCLogisticsEname() + "作废失败！");
        }
    }

    private void checkExpressArea(StCExpressAreaDO expressArea) {
        if (expressArea == null) {
            throw new NDSException("当前记录已不存在！");
        } else {
            if (StConstant.ISACTIVE_N.equals(expressArea.getIsactive())) {
                throw new NDSException("当前记录已作废，不允许重复作废！");
            }

        }
    }

    private void setVoidCommonField(StCExpressAreaDO expressArea, User user) {
        expressArea.setDelid(Long.valueOf(user.getId()));
        expressArea.setDelename(user.getEname());
        expressArea.setDelname(user.getName());
        expressArea.setDelTime(new Date());
    }
}
