package com.jackrain.nea.st.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.st.common.StConstant;
import com.jackrain.nea.st.mapper.StCVipcomAscriptionMapper;
import com.jackrain.nea.st.model.table.StCVipcomAscriptionDO;
import com.jackrain.nea.st.utils.StBeanUtils;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.Date;
import java.util.HashMap;

/**
 * <AUTHOR>
 * @Date 2019/3/8 10:02
 */
@Component
@Slf4j
public class VipcomAscriptionVoidService extends CommandAdapter {
    @Autowired
    private StCVipcomAscriptionMapper ascriptionMapper;

    @Override
    public ValueHolder execute(QuerySession session) throws NDSException {
        DefaultWebEvent event = session.getEvent();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(
                event.getParameterValue("param"), "yyyy-MM-dd HH:mm:ss", SerializerFeature.WriteMapNullValue),
                Feature.OrderedField);
        int errRecord = 0;
        HashMap<Long, Object> errMap = new HashMap<Long, Object>();
        //生成审核Json数组
        JSONArray voidArray = StBeanUtils.makeVoidJsonArray(param);
        //列表批量
        if (!CollectionUtils.isEmpty(voidArray)) {
            for (int i = 0; i < voidArray.size(); i++) {
                Long id = Long.valueOf(voidArray.get(i).toString());   // 遍历 jsonarray 数组，把每一个对象转成 json 对象
                try {
                    voidVipcomAscription(id, session);

                } catch (Exception ex) {
                    errRecord++;
                    errMap.put(id, ex.getMessage());
                }
            }
        }
        //返回日志
        return StBeanUtils.getExcuteValueHolder(voidArray.size(), errMap);
    }

    /**
     * 档期归属设置 作废
     *
     * @param id
     * @param session
     * @return com.jackrain.nea.util.ValueHolder
     * @Author: 陈秀楼
     * @Date 2019/3/8
     */
    private void voidVipcomAscription(long id, QuerySession session) {
        //验证
        checkVipcomProject(id);
        //作废
        StCVipcomAscriptionDO vipcomAscription = new StCVipcomAscriptionDO();
        vipcomAscription.setId(id);
        vipcomAscription.setIsactive(StConstant.ISACTIVE_N);//作废
        vipcomAscription.setDelname(session.getUser().getName());//作废人用户名
        vipcomAscription.setDelid(Long.valueOf(session.getUser().getId()));//作废人
        vipcomAscription.setDelename(session.getUser().getName());//作废人姓名
        vipcomAscription.setDelTime(new Date());//作废时间
        StBeanUtils.makeModifierField(vipcomAscription, session.getUser());
        int update = ascriptionMapper.updateById(vipcomAscription);
        if (update < 0) {
            throw new NDSException("更新失败！");
        }
    }

    /**
     * 检查日程计划
     *
     * @param id
     * @return java.lang.String
     * @Author: 陈秀楼
     * @Date 2019/3/8
     */
    private void checkVipcomProject(long id) {
        StCVipcomAscriptionDO vipcomAscription = ascriptionMapper.selectById(id);
        if (vipcomAscription == null) {
            throw new NDSException("当前记录已不存在！");
        } else {
            if (vipcomAscription.getIsactive().equals(StConstant.ISACTIVE_N)) {
                throw new NDSException("当前记录已作废，不允许重复作废！");
            }
        }
    }


}
