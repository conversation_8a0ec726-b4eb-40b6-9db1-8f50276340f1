package com.jackrain.nea.st.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.jackrain.nea.st.mapper.StCShopLogisticStrategyItemMapper;
import com.jackrain.nea.st.model.table.StCShopLogisticStrategyItem;
import com.jackrain.nea.st.utils.StBeanUtils;
import com.jackrain.nea.st.utils.ValueHolderUtils;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

/**
 * Created with IntelliJ IDEA.
 * @Author: jg.zhan
 * @Date:  2022/6/21 11:31
 * @Description:   店铺物流设置明细删除
 */
@Component
@Slf4j
@Transactional(rollbackFor = Exception.class)
public class StCShopLogisticStrategyItemDelService extends CommandAdapter {

    @Autowired
    private StCShopLogisticStrategyItemMapper stCShopLogisticStrategyItemMapper;

    @Override
    public ValueHolder execute(QuerySession session) {
        DefaultWebEvent event = session.getEvent();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(
                event.getParameterValue("param"), "yyyy-MM-dd HH:mm:ss"),
                Feature.OrderedField);
        log.info(LogUtil.format("##店铺物流设置明细删除##入参：{}"),param);
        Long id = param.getLong("objid");
        JSONArray errorArray = new JSONArray();
        if (id != null && id > 0) {
            StCShopLogisticStrategyItem stCShopLogisticStrategyItem = stCShopLogisticStrategyItemMapper.selectById(id);
            if (stCShopLogisticStrategyItem == null){
                return ValueHolderUtils.getFailValueHolder("当前记录已不存在！");
            }
            int record = stCShopLogisticStrategyItemMapper.deleteById(id);
            if (record <= 0) {
                errorArray.add("删除明细失败");
            }
        }
        return StBeanUtils.getExcuteValueHolder(errorArray);
    }

}
