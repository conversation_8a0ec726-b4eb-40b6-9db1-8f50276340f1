package com.jackrain.nea.st.services;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.st.common.StConstant;
import com.jackrain.nea.st.mapper.StCBnProblemConfigMapper;
import com.jackrain.nea.st.model.request.BnProblemConfigQueryRequest;
import com.jackrain.nea.st.model.result.BnProblemConfigQueryResult;
import com.jackrain.nea.st.model.table.StCBnProblemConfigDO;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

/**
 * 班牛问题清单查询服务
 */
@Component
@Slf4j
@Transactional(readOnly = true)
public class BnProblemConfigQueryService extends CommandAdapter {

    @Autowired
    private StCBnProblemConfigMapper stCBnProblemConfigMapper;

    /**
     * 查询班牛问题清单列表
     *
     * @param request 查询请求
     * @return 查询结果
     */
    public ValueHolderV14<BnProblemConfigQueryResult> queryBnProblemConfigList(BnProblemConfigQueryRequest request) {
        if (log.isDebugEnabled()) {
            log.debug("BnProblemConfigQueryService.queryBnProblemConfigList, request: {}", JSONObject.toJSONString(request));
        }

        ValueHolderV14<BnProblemConfigQueryResult> result = new ValueHolderV14<>(ResultCode.SUCCESS, "SUCCESS");
        BnProblemConfigQueryResult queryResult = new BnProblemConfigQueryResult();

        try {
            // 构建查询条件
            LambdaQueryWrapper<StCBnProblemConfigDO> queryWrapper = new LambdaQueryWrapper<>();

            // 添加查询条件
            if (request != null) {
                if (request.getId() != null) {
                    queryWrapper.eq(StCBnProblemConfigDO::getId, request.getId());
                }

                if (StringUtils.isNotBlank(request.getProblemText())) {
                    queryWrapper.like(StCBnProblemConfigDO::getProblemText, request.getProblemText());
                }

                if (request.getIsCustomerRequest() != null) {
                    queryWrapper.eq(StCBnProblemConfigDO::getIsCustomerRequest, request.getIsCustomerRequest());
                }

                if (request.getIsSkuCode() != null) {
                    queryWrapper.eq(StCBnProblemConfigDO::getIsSkuCode, request.getIsSkuCode());
                }

                if (request.getIsUrl() != null) {
                    queryWrapper.eq(StCBnProblemConfigDO::getIsUrl, request.getIsUrl());
                }

                if (request.getIsBatch() != null) {
                    queryWrapper.eq(StCBnProblemConfigDO::getIsBatch, request.getIsBatch());
                }

                if (request.getIsCancelReturn() != null) {
                    queryWrapper.eq(StCBnProblemConfigDO::getIsCancelReturn, request.getIsCancelReturn());
                }

                if (StringUtils.isNotBlank(request.getIsactive())) {
                    queryWrapper.eq(StCBnProblemConfigDO::getIsactive, request.getIsactive());
                } else {
                    // 默认只查询激活状态的记录
                    queryWrapper.eq(StCBnProblemConfigDO::getIsactive, StConstant.ISACTIVE_Y);
                }

                if (StringUtils.isNotBlank(request.getSellerRemark())) {
                    queryWrapper.like(StCBnProblemConfigDO::getSellerRemark, request.getSellerRemark());
                }
            } else {
                // 默认只查询激活状态的记录
                queryWrapper.eq(StCBnProblemConfigDO::getIsactive, StConstant.ISACTIVE_Y);
            }

            // 按ID排序
            queryWrapper.orderByAsc(StCBnProblemConfigDO::getId);

            // 执行查询
            List<StCBnProblemConfigDO> problemConfigList = stCBnProblemConfigMapper.selectList(queryWrapper);

            // 设置查询结果
            queryResult.setProblemConfigList(problemConfigList != null ? problemConfigList : new ArrayList<>());

            result.setData(queryResult);

            if (log.isDebugEnabled()) {
                log.debug("查询班牛问题清单列表成功，找到{}条记录",
                        problemConfigList != null ? problemConfigList.size() : 0);
            }

            return result;
        } catch (Exception e) {
            log.error("查询班牛问题清单列表异常", e);
            result.setCode(ResultCode.FAIL);
            result.setMessage("查询班牛问题清单列表异常: " + e.getMessage());
            return result;
        }
    }
}
