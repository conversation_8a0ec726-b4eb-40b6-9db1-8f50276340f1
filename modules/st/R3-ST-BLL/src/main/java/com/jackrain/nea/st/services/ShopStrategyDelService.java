package com.jackrain.nea.st.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.st.annotation.StOperationLog;
import com.jackrain.nea.st.common.StConstant;
import com.jackrain.nea.st.mapper.StCShopStrategyItemMapper;
import com.jackrain.nea.st.mapper.StCShopStrategyLogisticsItemMapper;
import com.jackrain.nea.st.mapper.StCShopStrategyMapper;
import com.jackrain.nea.st.model.table.StCShopStrategyDO;
import com.jackrain.nea.st.model.table.StCShopStrategyLogisticsItem;
import com.jackrain.nea.st.utils.ValueHolderUtils;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2019/3/8 20:19
 */
@Component
@Slf4j
@Transactional
public class ShopStrategyDelService extends CommandAdapter {

    @Autowired
    private StCShopStrategyMapper stCShopStrategyMapper;

    @Autowired
    private StCShopStrategyItemMapper stCShopStrategyItemMapper;

    @Autowired
    private StCShopStrategyLogisticsItemMapper logisticsItemMapper;

    /**
     * @param querySession
     * @return
     * @Author: 朱宇军
     * @Date 2019/3/9
     */

    @Override
    @StOperationLog(operationType = "DEL", mainTableName = "ST_C_SHOP_STRATEGY", itemsTableName = "ST_C_SHOP_STRATEGY_LOGISTICS_ITEM")
    public ValueHolder execute(QuerySession querySession) throws NDSException {
        ValueHolder result = ValueHolderUtils.getDeleteSuccessValueHolder();
        DefaultWebEvent event = querySession.getEvent();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(
                event.getParameterValue("param"), "yyyy-MM-dd HH:mm:ss"), Feature.OrderedField);
        log.debug(LogUtil.format("店铺策略删除Json：") + param.toJSONString());
        String isDel = param.getString("isdelmtable");
        Long objid = param.getLong("objid");

        //判断主表是否存在
        StCShopStrategyDO stCShopStrategy = stCShopStrategyMapper.selectById(objid);
        if (stCShopStrategy == null) {
            result = ValueHolderUtils.getFailValueHolder("当前记录已不存在!");
            return result;
        }
        //判断是删除主表还是明细表单独删除
        if (StConstant.FALSE_STR.equals(isDel)) {
            //单独删除明细
            JSONObject tabitem = param.getJSONObject("tabitem");
            JSONArray itemArray = tabitem.getJSONArray(StConstant.TAB_ST_C_SHOP_STRATEGY_IEM);
            if (itemArray != null && itemArray.size() > 0) {
                result = delSlaver(itemArray, result);
            }

            itemArray = tabitem.getJSONArray(StConstant.TAB_ST_C_SHOP_STRATEGY_LOGISTICS_ITEM);
            if (itemArray != null && itemArray.size() > 0) {
                List<Long> ids = itemArray.toJavaList(Long.class);
                List<StCShopStrategyLogisticsItem> items = logisticsItemMapper.selectBatchIds(ids);
                Map<Long, String> beforeDelObjMap = new HashMap<>();
                if (CollectionUtils.isNotEmpty(items)) {
                    for (StCShopStrategyLogisticsItem item : items) {
                        beforeDelObjMap.put(item.getId(), JSON.toJSONString(item));
                    }
                }
                querySession.setAttribute("beforeDelObjMap", beforeDelObjMap);
                logisticsItemMapper.deleteBatchIds(itemArray.toJavaList(Long.class));
            }
        } else {
            //删除主表跟明细
            result = delShopStrategyMaster(objid);
        }
        return result;
    }

    /**
     * @param masterId
     * @return
     * @Description 删除主表
     * @Author: 朱宇军
     * @Date 2019/3/9
     */

    private ValueHolder delShopStrategyMaster(Long masterId) {
        //删除明细
        if (stCShopStrategyItemMapper.deleteByMasterId(masterId) < 0) {
            return ValueHolderUtils.getFailValueHolder("店铺策略明细删除失败");
        }
        //删除主表数据
        if (stCShopStrategyMapper.deleteById(masterId) < 0) {
            return ValueHolderUtils.getFailValueHolder("店铺策略主表删除失败");
        }

        return ValueHolderUtils.getSuccessValueHolder("删除成功");
    }

    /**
     * @param slaverArray
     * @param result
     * @return
     * @Author: 朱宇军
     * @Date 2019/3/9
     */

    private ValueHolder delSlaver(JSONArray slaverArray, ValueHolder result) {
        for (int i = 0; i < slaverArray.size(); i++) {
            Long slaverId = slaverArray.getLong(i);
            int deleteCount = stCShopStrategyItemMapper.deleteById(slaverId);
            result = ValueHolderUtils.getDeleteSuccessValueHolder();
            //小于0表示数据已经被删除
            if (deleteCount <= 0) {
                result = ValueHolderUtils.getFailValueHolder("店铺策略明细已不存在！");
                return result;
            }
        }
        return result;
    }
}
