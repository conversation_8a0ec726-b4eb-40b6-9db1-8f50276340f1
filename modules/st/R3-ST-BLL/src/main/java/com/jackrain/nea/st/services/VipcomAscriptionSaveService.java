package com.jackrain.nea.st.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.google.common.base.Throwables;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.st.common.StConstant;
import com.jackrain.nea.st.mapper.StCRefundOrderStrategyMapper;
import com.jackrain.nea.st.mapper.StCVipcomAscriptionMapper;
import com.jackrain.nea.st.mapper.StCVipcomProjectItemMapper;
import com.jackrain.nea.st.model.request.RefundOrderStrategyRequest;
import com.jackrain.nea.st.model.request.VipcomAscriptionRequest;
import com.jackrain.nea.st.model.table.StCRefundOrderStrategyDO;
import com.jackrain.nea.st.model.table.StCVipcomAscriptionDO;
import com.jackrain.nea.st.model.table.StCVipcomProjectItemDO;
import com.jackrain.nea.st.utils.JsonUtils;
import com.jackrain.nea.st.utils.StBeanUtils;
import com.jackrain.nea.st.utils.ValueHolderUtils;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 日程归属设置
 *
 * <AUTHOR>
 * @Date 2019/3/8 11:06
 */
@Component
@Slf4j
@Transactional
public class VipcomAscriptionSaveService extends CommandAdapter {
    @Autowired
    private StCVipcomAscriptionMapper ascriptionMapper;
    @Autowired
    private StCVipcomProjectItemMapper projectMapper;

    @Override
    public ValueHolder execute(QuerySession session) throws NDSException {
        DefaultWebEvent event = session.getEvent();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(
                event.getParameterValue("param"), "yyyy-MM-dd HH:mm:ss", SerializerFeature.WriteMapNullValue),
                Feature.OrderedField);
        if (param == null) {
            throw new NDSException("参数为空！");
        }
        log.debug(LogUtil.format("Start VipcomAscriptionSaveService.execute. ReceiveParams: {}"), param.toJSONString());
        Long id = param.getLong("objid");
        JSONObject fixColumn = param.getJSONObject("fixcolumn");
        VipcomAscriptionRequest ascriptionRequest = JsonUtils.jsonParseClass(fixColumn, VipcomAscriptionRequest.class);
        if (fixColumn != null && id != null && ascriptionRequest != null) {
            if (id != -1) {
                return updateVipcomAscription(session, id, ascriptionRequest);
            } else {
                return insertVipcomAscription(session, ascriptionRequest);
            }
        }
        throw new NDSException("当前记录已不存在！");
    }

    /**
     * 日程归属设置更新
     *
     * @param session
     * @param id
     * @param ascriptionRequest
     * @return com.jackrain.nea.util.ValueHolder
     * @Author: 陈秀楼
     * @Date 2019/3/8
     */
    private ValueHolder updateVipcomAscription(QuerySession session, Long id, VipcomAscriptionRequest ascriptionRequest) {
        try {
            StCVipcomAscriptionDO existsAscription = ascriptionMapper.selectById(id);
            if (existsAscription == null) {
                return ValueHolderUtils.getFailValueHolder("当前记录已不存在！");
            }
            //转换成实体对象
            StCVipcomAscriptionDO vipcomAscription = ascriptionRequest.getStCVipcomAscription();
            if (vipcomAscription != null) {
                //update基础字段
                vipcomAscription.setId(id);
                //设置到货时间、发货时间默认值
                if (existsAscription.getArrivalInterval() == null && vipcomAscription.getArrivalInterval() == null) {
                    vipcomAscription.setArrivalInterval(0L);
                }
                if (existsAscription.getSendInterval() == null && vipcomAscription.getSendInterval() == null) {
                    vipcomAscription.setSendInterval(0L);
                }
                StBeanUtils.makeModifierField(vipcomAscription, session.getUser());
                if (ascriptionMapper.updateById(vipcomAscription) > 0) {
                    //更新日程规划明细中，日程归属字段
                    updateVipcomProjectItem(vipcomAscription);
                    return ValueHolderUtils.getSuccessValueHolder(id, StConstant.TAB_ST_C_VIPCOM_ASCRIPTION);
                } else {
                    return ValueHolderUtils.getFailValueHolder("更新失败！");
                }
            } else {
                log.error(LogUtil.format("VipcomAscriptionSaveService.updateVipcomAscription " +
                        "Error：StCVipcomAscriptionDO实体为空"));
                return ValueHolderUtils.getFailValueHolder("更新失败！");
            }
        } catch (Exception ex) {
            log.error(LogUtil.format("VipcomAscriptionSaveService.updateVipcomAscription Error{}"), Throwables.getStackTraceAsString(ex));
            return ValueHolderUtils.getFailValueHolder("保存异常：" + ex.toString());
        }
    }

    /**
     * @param ascription
     * @return
     * @Description 更新日程规划明细中的日程归属字段
     * <AUTHOR>
     * @date 2019-04-08 2019-04-08
     */
    private void updateVipcomProjectItem(StCVipcomAscriptionDO ascription) {
        StCVipcomProjectItemDO projectItem = new StCVipcomProjectItemDO();
        projectItem.setStCVipcomAscriptionId(ascription.getId());
        projectItem.setExpSendtime(ascription.getExpSendtime());
        projectItem.setExpArrivetime(ascription.getExpArrivetime());
        projectItem.setArrivalInterval(ascription.getArrivalInterval());
        projectItem.setSendInterval(ascription.getSendInterval());
        QueryWrapper<StCVipcomProjectItemDO> wrapper = new QueryWrapper<>();
        wrapper.eq("st_c_vipcom_ascription_id", ascription.getId());
        int update = projectMapper.update(projectItem, wrapper);
        if (update < 0) {
            throw new NDSException("更新失败！");
        }
    }

    /**
     * 档期日程归属 插入
     *
     * @param session
     * @param ascriptionRequest
     * @return com.jackrain.nea.util.ValueHolder
     * @Author: 陈秀楼
     * @Date 2019/3/8
     */
    private ValueHolder insertVipcomAscription(QuerySession session, VipcomAscriptionRequest ascriptionRequest) {
        //日程归属设置实体
        try {
            StCVipcomAscriptionDO vipAscription = ascriptionRequest.getStCVipcomAscription();
            if (vipAscription != null) {
                //1. 判断记录是否已存在
                ValueHolder check = checkVipcomAscriptionByFilter(vipAscription.getEname());
                if (check != null) {
                    return check;
                }
                //2.插入
                long Id = ModelUtil.getSequence(StConstant.TAB_ST_C_VIPCOM_ASCRIPTION);
                vipAscription.setId(Id);
                //设置到货间隔、发货间隔默认值
                if (vipAscription.getArrivalInterval() == null) {
                    vipAscription.setArrivalInterval(0L);
                }
                if (vipAscription.getSendInterval() == null) {
                    vipAscription.setSendInterval(0L);
                }
                //基本字段值设置
                StBeanUtils.makeCreateField(vipAscription, session.getUser());
                int insertResult = ascriptionMapper.insert(vipAscription);
                if (insertResult > 0) {
                    return ValueHolderUtils.getSuccessValueHolder(vipAscription.getId(), StConstant.TAB_ST_C_VIPCOM_ASCRIPTION);
                } else {
                    return ValueHolderUtils.getFailValueHolder("保存失败！");
                }
            } else {
                log.error(LogUtil.format("VipcomAscriptionSaveService.insertVipcomAscription " +
                        "Error：StCVipcomAscriptionDO实体为空"));
                return ValueHolderUtils.getFailValueHolder("保存失败");
            }
        } catch (Exception ex) {
            log.error(LogUtil.format("VipcomAscriptionSaveService.insertVipcomAscription Error{}"), Throwables.getStackTraceAsString(ex));
            return ValueHolderUtils.getFailValueHolder("保存异常:" + ex.toString());
        }
    }

    /**
     * @param ename
     * @return com.jackrain.nea.util.ValueHolder
     * @Author: 陈秀楼
     * @Date 2019/3/12
     */
    private ValueHolder checkVipcomAscriptionByFilter(String ename) {
        HashMap<String, Object> map = new HashMap<>();
        map.put("ename", ename);
        List<StCVipcomAscriptionDO> vipAscriptions = ascriptionMapper.selectByMap(map);
        if (!CollectionUtils.isEmpty(vipAscriptions)) {
            return ValueHolderUtils.getFailValueHolder("当前日程归属名称已存在！");
        } else {
            for (int i = 0; i < vipAscriptions.size(); i++) {
                StCVipcomAscriptionDO vipAscription = vipAscriptions.get(i);
                if (vipAscription.getIsactive().equals(StConstant.ISACTIVE_N)) {
                    return ValueHolderUtils.getFailValueHolder("当前记录已作废，不允许操作！");
                }
            }
        }
        return null;
    }


}
