package com.jackrain.nea.st.services;

import com.jackrain.nea.st.mapper.*;
import com.jackrain.nea.st.model.result.DistributionQueryResult;
import com.jackrain.nea.st.model.table.*;
import com.jackrain.nea.utility.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.Date;
import java.util.List;

/**
 * @description: 其他费用策略服务接口--操作费处理后端服务
 * @author: 田钦华
 * @date: 2019-05-21
 */
@Component
@Slf4j
public class OtherFeesQueryService {
    @Autowired
    private StCOperationCostMapper stCOperationcostMapper;

    @Autowired
    private StCPostfeeMapper stCPostfeeMapper;

    @Autowired
    private StCPostfeeItemMapper stCPostfeeItemMapper;

    @Autowired
    private StCPostfeeWarehouseMapper stCPostfeeWarehouseMapper;

    @Autowired
    private StCDistributionMapper stCDistributionMapper;

    @Autowired
    private StCDistributionItemMapper stCDistributionItemMapper;
    /**
     *  根据实体仓id查询操作费方案的信息列表
     * @param cpCPhyWarehouseId 实体仓id
     * @return List<StCOperationcost>操作费方案列表
     * 搬迁：tqh；2019-05-22
     */
    public List<StCOperationcostDO> selectStCOperationcostInfo(Integer planType, Long cpCPhyWarehouseId ){
        log.debug(LogUtil.format("OtherFeesQueryService.selectStCOperationcostInfo入参：实体仓id=", cpCPhyWarehouseId));
        List<StCOperationcostDO> selectStCOperationcostList= stCOperationcostMapper.selectStCOperationcostInfo(planType, cpCPhyWarehouseId,new Date());
        log.debug(LogUtil.format("OtherFeesQueryService.selectStCOperationcostInfo出参:") + selectStCOperationcostList);
        return selectStCOperationcostList;
    }

    /**
     * 根据当前时间查询有效期内的运费方案
     * ST_C_POSTFEE,ST_C_POSTFEE_ITEM,ST_C_POSTFEE_WAREHOUSE
     * @param currentDate 当前时间
     * @return List<StCPostfee>
     *     搬迁：tqh；2019-05-22
     */
    public List<StCPostfeeDO> searchEffectiveFreightPlan(Date currentDate) {
        log.debug(LogUtil.format("OtherFeesQueryService.searchEffectiveFreightPlan入参:"+ "当前时间："+currentDate));
        List<StCPostfeeDO> stCPostfeeList
                = stCPostfeeMapper.selectEffectiveFreightPlan(currentDate);
        log.debug(LogUtil.format("OtherFeesQueryService.searchEffectiveFreightPlan出参:")+ stCPostfeeList);
        return stCPostfeeList;
    }

    /**
     * 根据运费方案id查询运费方案明细信息
     * ST_C_POSTFEE,ST_C_POSTFEE_ITEM,ST_C_POSTFEE_WAREHOUSE
     * @param stCPostfeeId 当前时间
     * @param logisticsId 物流公司
     * @param provinceId 省份id
     * @param cityId 城市id
     * @param areaId 地区id
     * @return List<StCPostfeeItem>
     *       搬迁：tqh；2019-05-22
     */
    public List<StCPostfeeItemDO> searchStCPostfeeItem(Long stCPostfeeId, Long logisticsId,
                                                       Long provinceId, Long cityId, Long areaId) {
        log.debug(LogUtil.format("OtherFeesQueryService.searchStCPostfeeItem入参:" + "运费方案id/物流公司id/省份id/城市id/地区id=",
                stCPostfeeId, logisticsId, provinceId, cityId, areaId));
        List<StCPostfeeItemDO> stCPostfeeItemList
                = stCPostfeeItemMapper.selectStCPostfeeItem(stCPostfeeId, logisticsId, provinceId, cityId, areaId);
        log.debug(LogUtil.format("OtherFeesQueryService.searchStCPostfeeItem出参:")+ stCPostfeeItemList);
        return stCPostfeeItemList;
    }


    /**
     * 根据运费方案id查询运费方案明细信息
     * ST_C_POSTFEE,ST_C_POSTFEE_ITEM,ST_C_POSTFEE_WAREHOUSE
     * @param stCPostfeeId 当前时间
     * @return List<StCPostfeeWarehouse>
     *     搬迁：tqh；2019-05-22
     */
    public List<StCPostfeeWarehouseDO> searchStCPostfeeWarehouse(Long stCPostfeeId) {
        log.debug(LogUtil.format("OtherFeesQueryService.searchStCPostfeeWarehouse入参：运费方案id=", stCPostfeeId));
        List<StCPostfeeWarehouseDO> stCPostfeeItemList
                = stCPostfeeWarehouseMapper.selectStCPostfeeWarehouse(stCPostfeeId);
        log.debug(LogUtil.format("OtherFeesQueryService.selectStCPostfeeWarehouse出参:") + stCPostfeeItemList);
        return stCPostfeeItemList;
    }

    /**
     * 根据经营主体查询分销代销策略
     * @param manageIdSeller 销货方
     * @param manageId 购货方
     * @param currentDate 创建时间
     * @return StCDistributionDO
     */
    public DistributionQueryResult searchDistributionByManage(Long manageIdSeller, Long manageId, Date currentDate) {
        DistributionQueryResult result = new DistributionQueryResult();
        log.debug(LogUtil.format("OtherFeesQueryService.searchDistributionByManage入参：经营主体/manageId=",
                manageIdSeller, manageId));
        List<StCDistributionDO> distributionList
                = stCDistributionMapper.selectDistributionByManage(manageIdSeller, manageId, currentDate);
        if (!CollectionUtils.isEmpty(distributionList)) {
            StCDistributionDO distribution = distributionList.get(0);
            log.debug(LogUtil.format("OtherFeesQueryService.searchDistributionByManage出参:") + distribution);
            List<StCDistributionItemDO> distributionItemList = stCDistributionItemMapper.selectItemByMainId(distribution.getId());
            result.setDistribution(distribution);
            result.setDistributionItemList(distributionItemList);
        }
        return result;
    }
}
