package com.jackrain.nea.st.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.st.annotation.StOperationLog;
import com.jackrain.nea.st.mapper.StCExpressPriceStrategyItemMapper;
import com.jackrain.nea.st.mapper.StCExpressPriceStrategyMapper;
import com.jackrain.nea.st.model.common.StRedisConstant;
import com.jackrain.nea.st.model.enums.StCExpressPriceStrategyEnum;
import com.jackrain.nea.st.model.table.StCExpressPriceStrategyDO;
import com.jackrain.nea.st.model.table.StCExpressPriceStrategyItemDO;
import com.jackrain.nea.st.utils.RedisCacheUtil;
import com.jackrain.nea.st.utils.StBeanUtils;
import com.jackrain.nea.st.utils.ValueHolderUtils;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/6/12 14:17
 * @Description 快递报价设置提交策略
 * @Version 1.0
 */
@Component
@Slf4j
public class StCExpressPriceStrategySubmitService extends CommandAdapter {


    @Autowired
    private StCExpressPriceStrategyMapper stCExpressPriceStrategyMapper;

    @Autowired
    private StCExpressPriceStrategyItemMapper stCExpressPriceStrategyItemMapper;

    @StOperationLog(operationType = "AUDIT", mainTableName = "ST_C_EXPRESS_PRICE_STRATEGY", itemsTableName = "ST_C_EXPRESS_PRICE_STRATEGY_ITEM")
    @Override
    public ValueHolder execute(QuerySession session){
        DefaultWebEvent event = session.getEvent();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(
                event.getParameterValue("param"), "yyyy-MM-dd HH:mm:ss"), Feature.OrderedField);
        log.info(LogUtil.format("快递报价设置提交，参数：{}","快递报价设置提交"),param);
        if (param == null) {
            throw new NDSException("参数为空！");
        }
        JSONArray voidArray = StBeanUtils.makeUnAuditJsonArray(param);
        if (voidArray.size() == 1) {
            checkAction(voidArray.getLong(0),session);
        }else {
            int success = 0;
            int fail = 0;
            for (int i = 0; i < voidArray.size(); i++) {
                Long id = voidArray.getLong(i);
                try {
                    checkAction(id,session);
                    success++;
                } catch (Exception e) {
                    fail++;
                }
            }
            if (success != voidArray.size()) {
                return ValueHolderUtils.getFailValueHolder("快递报价设置提交成功" + success + "条,失败" + fail + "条");
            }
        }

        return ValueHolderUtils.getSuccessValueHolder("提交成功");
    }


    private void checkAction(Long id, QuerySession session) {
        User user = session.getUser();
        StCExpressPriceStrategyDO stCExpressPriceStrategyDO = stCExpressPriceStrategyMapper.selectById(id);
        //判断主表是否存在
        if (stCExpressPriceStrategyDO == null){
            throw new NDSException("当前记录不存在！");
        }
        if (!StCExpressPriceStrategyEnum.UN_SUBMITTED.getKey().equals(stCExpressPriceStrategyDO.getStatus())){
            throw new NDSException("当前单据状态，不允许提交！");
        }
        //判断是否有明细
        Integer count = stCExpressPriceStrategyItemMapper.selectCount(new LambdaQueryWrapper<StCExpressPriceStrategyItemDO>()
                .eq(StCExpressPriceStrategyItemDO::getStCExpressPriceStrategyId, stCExpressPriceStrategyDO.getId()));
        if (count <= 0) {
            throw new NDSException("请维护明细！");
        }
        //判断是否冲突
        List<StCExpressPriceStrategyDO> selectList = stCExpressPriceStrategyMapper.selectList(new LambdaQueryWrapper<StCExpressPriceStrategyDO>()
                .eq(StCExpressPriceStrategyDO::getCpCPhyWarehouseId, stCExpressPriceStrategyDO.getCpCPhyWarehouseId())
                .eq(StCExpressPriceStrategyDO::getCpCLogisticsId, stCExpressPriceStrategyDO.getCpCLogisticsId())
                .eq(StCExpressPriceStrategyDO::getStatus, StCExpressPriceStrategyEnum.SUBMITTED.getKey())
                .eq(StCExpressPriceStrategyDO::getCloseStatus, StCExpressPriceStrategyEnum.UN_CLOSED.getKey()));
        if (CollectionUtils.isNotEmpty(selectList)){
            for (StCExpressPriceStrategyDO cost : selectList) {
                Boolean flag = checkTime(stCExpressPriceStrategyDO.getStartDate(),stCExpressPriceStrategyDO.getEndDate(),cost.getStartDate(),cost.getEndDate());
                if (!flag) {
                    throw new NDSException("不允许有两个有效快递报价设置！");
                }
            }
        }
        StCExpressPriceStrategyDO newDO = new StCExpressPriceStrategyDO();
        newDO.setId(stCExpressPriceStrategyDO.getId());
        newDO.setPriceHomeDelivery(stCExpressPriceStrategyDO.getPriceHomeDelivery());
        newDO.setRemark(stCExpressPriceStrategyDO.getRemark());
        newDO.setStatus(StCExpressPriceStrategyEnum.SUBMITTED.getKey());
        newDO.setSubmitterid(Long.valueOf(user.getId()));
        newDO.setSubmitTime(new Date());
        StBeanUtils.makeModifierField(newDO,user);
        int update = stCExpressPriceStrategyMapper.updateById(newDO);
        if (update <= 0) {
            throw new NDSException("更新失败！");
        }

        // 清除快递报价关系缓存
        String redisKey = StRedisConstant.buildExpressPriceRelationKey(stCExpressPriceStrategyDO.getCpCPhyWarehouseId(), stCExpressPriceStrategyDO.getCpCLogisticsId());
        RedisCacheUtil.deleteAll(redisKey);
        log.info("清除快递报价关系缓存成功，key={}", redisKey);

        // 清除根据ID查询的分布式缓存
        String idCacheKey = StRedisConstant.buildExpressPriceStrategyRelationByIdKey(stCExpressPriceStrategyDO.getId());
        RedisCacheUtil.deleteAll(idCacheKey);
        log.info("清除快递报价策略ID缓存成功，key={}", idCacheKey);
    }

    /**
     * 判断时间是否交叉
     * @param startDate
     * @param endDate
     * @param startDate1
     * @param endDate1
     * @return
     */
    private Boolean checkTime(Date startDate, Date endDate, Date startDate1, Date endDate1) {
        if (endDate.compareTo(endDate1) == 0){
            return false;
        }else {
            if (endDate.compareTo(endDate1) > 0) {
                //endDate大，则比较startDate是否小于等于endDate1,是标识有交叉
                if (endDate1.compareTo(startDate) >= 0) {
                    return false;
                }
            } else {
                //endDate1大，则比较startDate1是否小于等于endDate,是标识有交叉
                if (endDate.compareTo(startDate1) >= 0) {
                    return false;
                }
            }
        }
        return true;
    }



}
