package com.jackrain.nea.st.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.jackrain.nea.cp.api.CpCOrgChannelQueryCmd;
import com.jackrain.nea.cp.entity.CpCOrgChannelEntity;
import com.jackrain.nea.cpext.model.table.CpCStore;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.st.common.StConstant;
import com.jackrain.nea.st.mapper.StCChannelStrategyItemMapper;
import com.jackrain.nea.st.mapper.StCChannelStrategyMapper;
import com.jackrain.nea.st.mapper.StCSyncStockStrategyChannelMapper;
import com.jackrain.nea.st.mapper.StCSyncStockStrategyMapper;
import com.jackrain.nea.st.model.request.ChannelStrategyRequest;
import com.jackrain.nea.st.model.table.StCChannelStrategyDO;
import com.jackrain.nea.st.model.table.StCChannelStrategyItemDO;
import com.jackrain.nea.st.model.table.StCSyncStockStrategyDO;
import com.jackrain.nea.st.rpc.RpcCpService;
import com.jackrain.nea.st.utils.JsonUtils;
import com.jackrain.nea.st.utils.StBeanUtils;
import com.jackrain.nea.st.utils.ValueHolderUtils;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2019/12/4 10:51
 */
@Component
@Slf4j
@Transactional(rollbackFor = {Exception.class}, propagation = Propagation.SUPPORTS)
@Deprecated
public class ChannelStrategySaveService extends CommandAdapter {
    @Autowired
    private StCSyncStockStrategyMapper syncMapper;
    @Autowired
    private StCSyncStockStrategyChannelMapper syncChannelMapper;
    @Autowired
    private StCChannelStrategyMapper mapper;
    @Autowired
    private StCChannelStrategyItemMapper itemMapper;
    @Autowired
    private RpcCpService rpcCpService;
    @Reference(group = "cp", version = "1.0")
    private CpCOrgChannelQueryCmd cpCOrgChannelQueryCmd;

    @Override
    public ValueHolder execute(QuerySession session) {
        DefaultWebEvent event = session.getEvent();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(
                event.getParameterValue("param"), "yyyy-MM-dd HH:mm:ss", SerializerFeature.WriteMapNullValue),
                Feature.OrderedField);
        log.info(LogUtil.format("ChannelStrategySaveServiceParam:{}"), param);
        Long id = param.getLong("objid");
        //获取新增时，复选框选中的值
        JSONObject fixColumn = param.getJSONObject("fixcolumn");
        ChannelStrategyRequest channelStrategyRequest = JsonUtils.jsonParseClass(fixColumn, ChannelStrategyRequest.class);
        if (id != null) {
            if (id != -1) {
                return updateChannelStrategy(session, id, channelStrategyRequest);
            } else {
            }
        }
        throw new NDSException("传入数据异常！");
    }


    private ValueHolder updateChannelStrategy(QuerySession session, Long id, ChannelStrategyRequest channelStrategyRequest) {
        StCChannelStrategyDO existsChannelStrategy = mapper.selectById(id);
        if (existsChannelStrategy == null) {
            return ValueHolderUtils.getFailValueHolder("当前记录已不存在！");
        }

        StCChannelStrategyDO channelStrategy = channelStrategyRequest.getChannelStrategy();
        //主表数据业务更新校验
        if (channelStrategy != null) {
            if (checkChannelStrategy(channelStrategy)) {
                return ValueHolderUtils.getFailValueHolder("该渠道已存在,请选择其他渠道！");
            }
            CpCOrgChannelEntity cpCOrgChannel = getChannelInfo(channelStrategy.getCpCOrgChannelId());
            if (cpCOrgChannel != null) {
                channelStrategy.setCpCOrgChannelEcode(cpCOrgChannel.getEcode());
                channelStrategy.setCpCOrgChannelEname(cpCOrgChannel.getEname());
                channelStrategy.setType(cpCOrgChannel.getType());
            }
            //主表最后修改信息变更
            channelStrategy.setId(id);
            StBeanUtils.makeModifierField(channelStrategy, session.getUser());
            if (mapper.updateById(channelStrategy) <= 0) {
                return ValueHolderUtils.getFailValueHolder("渠道策略主表更新失败");
            }
        }
        //新增子表信息
        List<StCChannelStrategyItemDO> itemList = channelStrategyRequest.getChannelStrategyList();
        if (!CollectionUtils.isEmpty(itemList)) {
            Map map = new HashMap<>();//存储需要新增的实体仓id，作为校验是否存在相同的id，如果是则不能保存成功
            for (StCChannelStrategyItemDO channelStrategyItem : itemList) {
                if (channelStrategyItem.getRate() != null && channelStrategyItem.getRate().compareTo(new BigDecimal("100")) > 0) {
                    return ValueHolderUtils.getFailValueHolder("单个库存比例不能大于100%!");
                }
                if (channelStrategyItem.getId() != null && channelStrategyItem.getId() > 0) {
                    StBeanUtils.makeModifierField(channelStrategyItem, session.getUser());
                    itemMapper.updateById(channelStrategyItem);
                } else {
                    Long storeArrStr = channelStrategyItem.getCpCStoreId();//实体仓id
                    //增加校验：新增时不能新增相同的实体仓;返回值如果为true：代表存在已经保存的实体仓
                    if (checkChannelStrategyItemId(storeArrStr, id)) {
                        return ValueHolderUtils.getFailValueHolder("该供货仓已存在,请选择其他供货仓！");
                    }
                    if (checkCSyncStockStrategyItemId(storeArrStr, id)) {
                        return ValueHolderUtils.getFailValueHolder("同步库存策略的供货渠道中该供货仓已存在,请选择其他供货仓");
                    }
                    if (map.get(storeArrStr) != null) {//初始map为null，所以为空；第二次的时候，如果存在第一次的key，value，则代表需要保存的实体仓中存在重复id，则不能保存成功
                        return ValueHolderUtils.getFailValueHolder("存在重复的供货仓,请选择其他供货仓！");
                    }
                    map.put(storeArrStr, storeArrStr);//将id加入map
                    StCChannelStrategyItemDO newItem = new StCChannelStrategyItemDO();
                    BeanUtils.copyProperties(channelStrategyItem, newItem);
                    newItem.setId(ModelUtil.getSequence(StConstant.TAB_ST_C_CHANNEL_STRATEGY_ITEM));
                    newItem.setStCChannelStrategyId(id);
                    newItem.setCpCStoreId(storeArrStr);
                    newItem.setIsSend(channelStrategyItem.getIsSend() == null ? 1 : channelStrategyItem.getIsSend());
                    StBeanUtils.makeCreateField(newItem, session.getUser());
                    try {
                        //查出逻辑仓的ecode ename
                        List<Integer> storeIdList = new ArrayList<>();
                        storeIdList.add(newItem.getCpCStoreId().intValue());
                        log.info(LogUtil.multiFormat("ChannelStrategySaveService,RPC参数_storeIdList:{}", storeIdList));
//                        List<CpCStore> storeList = new ArrayList<>();
                        List<CpCStore> storeList = rpcCpService.queryStoreInfoByIds(storeIdList);
                        if (!CollectionUtils.isEmpty(storeList)) {
                            CpCStore cpCStore = storeList.get(0);
                            newItem.setCpCStoreEcode(cpCStore.getEcode());
                            newItem.setCpCStoreEname(cpCStore.getEname());
                        }
                    } catch (Exception e) {
                        throw new NDSException("调用获取店仓RPC接口失败");
                    }

                    int insertItemResult = itemMapper.insert(newItem);

                    if (insertItemResult <= 0) {
                        return ValueHolderUtils.getFailValueHolder("保存失败");
                    }
                }
            }
        }
        return ValueHolderUtils.getSuccessValueHolder(id, StConstant.TAB_ST_C_CHANNEL_STRATEGY);
    }


    private boolean checkChannelStrategy(StCChannelStrategyDO channelStrategy) {
        String errMessage = "";
        List<StCChannelStrategyDO> strategy = mapper.selectstCChannelStrategyByType(channelStrategy.getCpCOrgChannelId());//根据实体仓id查询数据库，若能查询到数据，代表已存在申请的记录，则不能重复申请同样的实体仓
        if (strategy != null && strategy.size() > 0) {
            return true;
        }
        return false;
    }

    //校验新增的实体仓不能重复新增
    //参数id：主表的关联id
    //参数cpCStoreId：实体仓id
    private boolean checkChannelStrategyItemId(Long cpCStoreId, Long id) {
        List<StCChannelStrategyItemDO> stCSyncStockStrategyItemDOByCpCStoreIdList = itemMapper.selectstCChannelStrategyItemDO(cpCStoreId, id);//根据实体仓id查询数据库，若能查询到数据，代表已存在申请的记录，则不能重复申请同样的实体仓
        if (stCSyncStockStrategyItemDOByCpCStoreIdList != null && stCSyncStockStrategyItemDOByCpCStoreIdList.size() > 0) {
            return true;
        }
        return false;
    }

    //校验新增的实体仓不能重复新增
    //参数id：主表的关联id
    //参数cpCStoreId：实体仓id
    private boolean checkCSyncStockStrategyItemId(Long cpCStoreId, Long id) {
        //根据逻辑仓所属的渠道策略id找到其关联的同步库存策略
        List<StCSyncStockStrategyDO> syncStockStrategys = itemMapper.selectCpCShopIdByChannelStrategy(id);
        for (StCSyncStockStrategyDO syncStockStrategy : syncStockStrategys) {
            //根据门店id和逻辑仓查询是否数据已存在
            List<StCChannelStrategyItemDO> exist = itemMapper.selectstCChannelStrategyItemDOBySyncStockId(cpCStoreId, syncStockStrategy.getId());
            if (exist != null && exist.size() > 0) {
                return true;
            }
        }
        return false;
    }

    //获取渠道信息
    private CpCOrgChannelEntity getChannelInfo(Long id) {
        try {
            ValueHolder v14 = cpCOrgChannelQueryCmd.getChannel(id);
            return (CpCOrgChannelEntity) v14.get("data");
        } catch (Exception e) {
            throw new NDSException("查群渠道信息RPC接口失败");
        }
    }
}
