package com.jackrain.nea.st.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.jackrain.nea.st.mapper.StCBnProblemConfigMapper;
import com.jackrain.nea.st.utils.ValueHolderUtils;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @ClassName BnProblemConfigDelService
 * @Description 班牛
 * <AUTHOR>
 * @Date 2024/11/11 16:47
 * @Version 1.0
 */
@Component
@Slf4j
public class BnProblemConfigDelService extends CommandAdapter {

    @Autowired
    private StCBnProblemConfigMapper stCBnProblemConfigMapper;

    public ValueHolder del(QuerySession querySession) {
        DefaultWebEvent event = querySession.getEvent();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(
                        event.getParameterValue("param"), "yyyy-MM-dd HH:mm:ss"),
                Feature.OrderedField);
        log.info("BnProblemConfigDelService.param: {}", param.toJSONString());
        Long id = param.getLong("objid");
        stCBnProblemConfigMapper.deleteById(id);
        return ValueHolderUtils.getDeleteSuccessValueHolder();
    }
}
