package com.jackrain.nea.st.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.st.common.StConstant;
import com.jackrain.nea.st.mapper.StCVipcomStocklackItemMapper;
import com.jackrain.nea.st.mapper.StCVipcomStocklackMapper;
import com.jackrain.nea.st.model.table.StCVipcomStocklackDO;
import com.jackrain.nea.st.model.table.StCVipcomStocklackItemDO;
import com.jackrain.nea.st.utils.StBeanUtils;
import com.jackrain.nea.st.utils.ValueHolderUtils;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * @Descroption 唯品会缺货策略-删除逻辑
 * <AUTHOR>
 * @Date 2019/3/11 21:13
 */

@Component
@Slf4j
@Transactional
public class VipcomStocklackDelService extends CommandAdapter {
    @Autowired
    private StCVipcomStocklackMapper stCVipcomStocklackMapper;
    @Autowired
    private StCVipcomStocklackItemMapper stCVipcomStocklackItemMapper;

    @Override
    public ValueHolder execute(QuerySession querySession) throws NDSException {
        DefaultWebEvent event = querySession.getEvent();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(
                event.getParameterValue("param"), "yyyy-MM-dd HH:mm:ss"),
                Feature.OrderedField);
        boolean delMainFlag = param.getBoolean("isdelmtable");//是否删除主表
        Long id = param.getLong("objid");
        if (id != null && id > 0) {
            ValueHolder valueHolder = new ValueHolder();
            StCVipcomStocklackDO stCVipcomStocklackDO = stCVipcomStocklackMapper.selectById(id);
            if (!checkVipcomStocklackStatus(stCVipcomStocklackDO, valueHolder)) {
                return valueHolder;
            }
            if (delMainFlag) {
                List<StCVipcomStocklackItemDO> stCVipcomStocklackItemDOList = stCVipcomStocklackItemMapper.selectStocklackByStocklackId(id);
                if (!stCVipcomStocklackItemDOList.isEmpty() && stCVipcomStocklackItemDOList.size() > 0) {
                    return ValueHolderUtils.getFailValueHolder("唯品会JIT缺货策略存在明细，不允许删除！");
                }
                if ((stCVipcomStocklackMapper.deleteById(stCVipcomStocklackDO)) > 0) {
                    return ValueHolderUtils.getFailValueHolder("删除主表成功！");
                }
            } else {
                //单独删除明细
                JSONObject tabitem = param.getJSONObject("tabitem");
                JSONArray errorArray = new JSONArray();
                JSONArray itemArray = tabitem.getJSONArray(StConstant.TAB_ST_C_VIPCOM_STOCKLACK_ITEM);
                if (itemArray == null || itemArray.size() <= 0) {
                    return ValueHolderUtils.getFailValueHolder("当前明细记录不存在！");
                }
                deleteVipcomStocklackItemByStocklackID(itemArray, errorArray);
                updateVipcomStocklackDate(querySession, id);
                return StBeanUtils.getExcuteValueHolder(errorArray);
            }
        }
        throw new NDSException("当前记录已不存在！");
    }

    private void deleteVipcomStocklackItemByStocklackID(JSONArray itemArray, JSONArray errorArray) {
        if (itemArray.size() > 0) {
            for (int i = 0; i < itemArray.size(); i++) {
                Long itemid = itemArray.getLong(i);
                if ((stCVipcomStocklackItemMapper.deleteById(itemid)) <= 0) {
                    errorArray.add(StBeanUtils.getJsonObjectInfo(itemid, "唯品会JTI缺货策略-明细记录已不存在！"));
                }
            }
        }
    }

    private void updateVipcomStocklackDate(QuerySession session, Long id) {
        StCVipcomStocklackDO stCVipcomStocklackDO = new StCVipcomStocklackDO();
        stCVipcomStocklackDO.setId(id);
        StBeanUtils.makeModifierField(stCVipcomStocklackDO, session.getUser());//修改信息
        stCVipcomStocklackDO.setModifierename(session.getUser().getEname());//修改人账号
        if ((stCVipcomStocklackMapper.updateById(stCVipcomStocklackDO)) <= 0) {
            log.error(LogUtil.format("VipcomStocklackDelService.updateVipcomStocklackDate.Error",
                    "删除明细，主表修改字段信息更新出错id=", id));
        }
    }

    private boolean checkVipcomStocklackStatus(StCVipcomStocklackDO stCVipcomStocklackDO, ValueHolder valueHolder) {
        if (stCVipcomStocklackDO == null) {
            valueHolder.put("code", -1);
            valueHolder.put("message", "当前记录不存在！");
            return false;
        }
        //已作废，不允许删除
        if (stCVipcomStocklackDO.getIsactive().equals(StConstant.ISACTIVE_N)) {
            valueHolder.put("code", -1);
            valueHolder.put("message", "当前记录已作废，不允许删除！");
            return false;
        }
        return true;
    }
}
