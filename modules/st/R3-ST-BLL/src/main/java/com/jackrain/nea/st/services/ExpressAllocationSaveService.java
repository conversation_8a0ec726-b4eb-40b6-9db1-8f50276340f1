package com.jackrain.nea.st.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.alibaba.fastjson.parser.Feature;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.jackrain.nea.cpext.model.table.CpLogistics;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.st.mapper.StCExpressAllocationItemMapper;
import com.jackrain.nea.st.mapper.StCExpressAllocationMapper;
import com.jackrain.nea.st.model.table.StCExpressAllocationDO;
import com.jackrain.nea.st.model.table.StCExpressAllocationItemDO;
import com.jackrain.nea.st.rpc.RpcCpService;
import com.jackrain.nea.st.utils.*;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Bll层-新增保存业务逻辑
 *
 * <AUTHOR> 黄火县
 * @since : 2019-03-08
 * create at : 2019-03-08 16:30
 */
@Component
@Slf4j
@Transactional
public class ExpressAllocationSaveService extends CommandAdapter {
    @Autowired
    private StCExpressAllocationMapper stCMainMapper;

    @Autowired
    private StCExpressAllocationItemMapper stCItemMapper;

    @Autowired
    private RpcCpService rpcCpService;

    private String strTableMain = "ST_C_EXPRESS_ALLOCATION";
    private String strTableList = "ST_C_EXPRESS_ALLOCATION_ITEM";

    /**
     * 新增和修改
     *
     * @param querySession 参数封装
     * @return 返回状态
     * @throws NDSException 异常信息
     */
    @Override
    public ValueHolder execute(QuerySession querySession) throws NDSException {
        ValueHolder valueHolder = new ValueHolder();
        DefaultWebEvent event = querySession.getEvent();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(
                event.getParameterValue("param"), "yyyy-MM-dd HH:mm:ss"),
                Feature.OrderedField);

        if (param != null) {
            Long id = param.getLong("objid");
            JSONObject fixColumn = param.getJSONObject("fixcolumn");
            if (fixColumn != null) {
                JSONObject mainMap = fixColumn.getJSONObject(strTableMain);
                JSONArray itemMap = fixColumn.getJSONArray(strTableList);
                if (id != null && id < 0) {
                    valueHolder = saveFunction(mainMap, itemMap, valueHolder, querySession, id);
                } else {
                    valueHolder = updateFunction(mainMap, itemMap, valueHolder, querySession, id);
                }
            }
        } else {
            throw new NDSException("参数为空！");
        }

        return valueHolder;
    }

    /**
     * 新增操作
     *
     * @param mainMap      主表数据
     * @param itemMap      子表数据
     * @param holder       响应数据
     * @param querySession 封装数据
     * @param objId        id
     * @return 返回状态
     */
    private ValueHolder saveFunction(JSONObject mainMap,
                                     JSONArray itemMap,
                                     ValueHolder holder,
                                     QuerySession querySession, Long objId) {
        StCExpressAllocationDO stCExpressAllocationDO =
                JsonUtils.jsonParseClass(mainMap, StCExpressAllocationDO.class);

        if (stCExpressAllocationDO != null) {

            //状态数据检查
            if (!checkStatus(stCExpressAllocationDO, objId, holder)) {
                return holder;
            }

            stCExpressAllocationDO.setId(ModelUtil.getSequence(strTableMain));
            StBeanUtils.makeCreateField(stCExpressAllocationDO, querySession.getUser());

            if (stCMainMapper.insert(stCExpressAllocationDO) <= 0) {
                holder = ValueHolderUtils.getFailValueHolder("保存失败！");
                return holder;
            }
        } else {
            holder = ValueHolderUtils.getFailValueHolder("保存失败！");
            return holder;
        }

        /**
         * 子表新增
         */
        if (itemMap != null) {
            List<StCExpressAllocationItemDO> stCExpressAllocationItemDOList = JSON.parseObject(itemMap.toJSONString(),
                    new TypeReference<ArrayList<StCExpressAllocationItemDO>>() {
                    });
            if (stCExpressAllocationItemDOList != null) {

                //检查行明细
                checkList(stCExpressAllocationItemDOList, holder, objId);

                for (StCExpressAllocationItemDO stCExpressAllocationItemDO : stCExpressAllocationItemDOList) {
                    stCExpressAllocationItemDO.setId(ModelUtil.getSequence(strTableList));
                    stCExpressAllocationItemDO.setStCExpressAllocationId(stCExpressAllocationDO.getId());
                    // 取得物流公司信息
                    if (stCExpressAllocationItemDO.getCpCLogisticsId() != null) {
                        CpLogistics cpLogistics = rpcCpService.queryLogisticsById(stCExpressAllocationItemDO.getCpCLogisticsId());
                        if (cpLogistics != null) {
                            stCExpressAllocationItemDO.setCpCLogisticsEcode(cpLogistics.getEcode());
                            stCExpressAllocationItemDO.setCpCLogisticsEname(cpLogistics.getEname());
                        }
                    }
                    StBeanUtils.makeCreateField(stCExpressAllocationItemDO, querySession.getUser());

                    //发货数量为0
                    if (stCExpressAllocationItemDO.getSendNum() == null) {
                        stCExpressAllocationItemDO.setSendNum(new BigDecimal("0"));
                    }
                    if (stCItemMapper.isExistExpressAllocationItem(ModelUtil.getSequence(strTableList),stCExpressAllocationItemDO.getCpCShopId())>0){
                        holder = ValueHolderUtils.getFailValueHolder("该仓库该平台店铺已经存在数据，不允许重复新增！");
                        return holder;
                    }
                    if (stCItemMapper.insert(stCExpressAllocationItemDO) < 0) {
                        holder = ValueHolderUtils.getFailValueHolder("保存失败！");
                        return holder;
                    }
                }
            } else {
                holder = ValueHolderUtils.getFailValueHolder("明细JSON转换失败，保存失败！");
                return holder;
            }
        }
        holder = ValueHolderUtils.getSuccessValueHolder(stCExpressAllocationDO.getId(), strTableMain);
        return holder;
    }

    /**
     * 更新操作
     *
     * @param mainMap      主表数据
     * @param itemMap      子表数据
     * @param holder       响应数据
     * @param querySession 封装数据
     * @param objid        主表id
     * @return 返回状态
     */
    private ValueHolder updateFunction(JSONObject mainMap,
                                       JSONArray itemMap,
                                       ValueHolder holder,
                                       QuerySession querySession,
                                       Long objid) {
        //主表更新，objid就是主表ID
        if (mainMap != null && !mainMap.isEmpty()) {
            StCExpressAllocationDO stCExpressAllocationDO = JSON.parseObject(mainMap.toJSONString(),
                    new TypeReference<StCExpressAllocationDO>() {
                    });
            if (!checkStatus(stCExpressAllocationDO, objid, holder)) {
                return holder;
            }

            stCExpressAllocationDO.setId(objid);
            StBeanUtils.makeModifierField(stCExpressAllocationDO, querySession.getUser());

            if (stCMainMapper.updateById(stCExpressAllocationDO) < 0) {
                holder = ValueHolderUtils.getFailValueHolder("保存失败！");
                return holder;
            }
        }

        //判断子表数据是否存在
        if (itemMap != null && !itemMap.isEmpty()) {
            List<StCExpressAllocationItemDO> stCExpressAllocationItemDOList = JSON.parseObject(itemMap.toJSONString(),
                    new TypeReference<ArrayList<StCExpressAllocationItemDO>>() {
                    });
            if (stCExpressAllocationItemDOList != null) {

                //检查行明细
                checkList(stCExpressAllocationItemDOList, holder, objid);

                //比例范围是0=<比例<=100
                StCExpressAllocationItemDO rateDO = stCItemMapper.selectByIdAndScale(objid);
                if (rateDO != null) {
                    BigDecimal bigDecimalSum = rateDO.getScale();
                    BigDecimal bigDecimal = new BigDecimal("100");
                    for (StCExpressAllocationItemDO stCExpressAllocationItemDO : stCExpressAllocationItemDOList) {
                        if (stCExpressAllocationItemDO.getScale() != null) {
                            if (stCExpressAllocationItemDO.getScale().doubleValue() < 0.0) {
                                holder = ValueHolderUtils.getFailValueHolder("发货比例，不允许录入负数！");
                                return holder;
                            }

                            if (stCExpressAllocationItemDO.getId() < 0) {
                                // +
                                bigDecimalSum = bigDecimalSum.add(stCExpressAllocationItemDO.getScale());
                            } else {
                                // -
                                StCExpressAllocationItemDO rateDO1 = stCItemMapper.selectById(
                                        stCExpressAllocationItemDO.getId());
                                BigDecimal bigDecimal1 = rateDO1.getScale();
                                bigDecimal1 = bigDecimal1.subtract(stCExpressAllocationItemDO.getScale());
                                bigDecimalSum = bigDecimalSum.subtract(bigDecimal1);
                            }
                        }
                    }

                    if (bigDecimalSum.compareTo(bigDecimal) >= 1) {
                        holder = ValueHolderUtils.getFailValueHolder("发货比例，总和不能大于100");
                        return holder;
                    }
                }


                for (StCExpressAllocationItemDO stCExpressAllocationItemDO : stCExpressAllocationItemDOList) {
                    Long id = stCExpressAllocationItemDO.getId();
                    // 取得物流公司信息
                    if (stCExpressAllocationItemDO.getCpCLogisticsId() != null) {
                        CpLogistics cpLogistics = rpcCpService.queryLogisticsById(stCExpressAllocationItemDO.getCpCLogisticsId());
                        if (cpLogistics != null) {
                            stCExpressAllocationItemDO.setCpCLogisticsEcode(cpLogistics.getEcode());
                            stCExpressAllocationItemDO.setCpCLogisticsEname(cpLogistics.getEname());
                        }
                    }
                    if (id < 0) {
                        if (stCExpressAllocationItemDO.getSendNum() == null) {
                            stCExpressAllocationItemDO.setSendNum(new BigDecimal("0"));
                        }
                        stCExpressAllocationItemDO.setId(ModelUtil.getSequence(strTableList));
                        stCExpressAllocationItemDO.setStCExpressAllocationId(objid);
                        StBeanUtils.makeCreateField(stCExpressAllocationItemDO, querySession.getUser());

                        if (stCItemMapper.isExistExpressAllocationItem(objid,stCExpressAllocationItemDO.getCpCShopId())>0){
                            holder = ValueHolderUtils.getFailValueHolder("该仓库该平台店铺已经存在数据，不允许重复新增！");
                            return holder;
                        }
                        if (stCItemMapper.insert(stCExpressAllocationItemDO) < 0) {
                            holder = ValueHolderUtils.getFailValueHolder("保存失败！");
                            return holder;
                        }
                    } else {
                        //修改原有的行信息 id>0
                        if (stCItemMapper.selectById(id) != null) {
                            StBeanUtils.makeModifierField(stCExpressAllocationItemDO, querySession.getUser());
                            if (stCItemMapper.isExistExpressAllocationItem(objid,stCExpressAllocationItemDO.getCpCShopId())>0){
                                holder = ValueHolderUtils.getFailValueHolder("该仓库该平台店铺已经存在数据，不允许重复新增！");
                                return holder;
                            }
                            if (stCItemMapper.updateById(stCExpressAllocationItemDO) < 0) {
                                holder = ValueHolderUtils.getFailValueHolder("保存失败！");
                                return holder;
                            }
                        } else {
                            holder = ValueHolderUtils.getFailValueHolder("修改的行明细已被删除！");
                            return holder;
                        }
                    }
                }
                StCExpressAllocationDO stCExpressAllocationNewDO = new StCExpressAllocationDO();
                stCExpressAllocationNewDO.setId(objid);
                StBeanUtils.makeModifierField(stCExpressAllocationNewDO, querySession.getUser());
                stCMainMapper.updateById(stCExpressAllocationNewDO);
            } else {
                holder = ValueHolderUtils.getFailValueHolder("明细JSON转换失败，保存失败！");
                return holder;
            }
        }
        holder = ValueHolderUtils.getSuccessValueHolder(objid, strTableMain);
        return holder;
    }


    private boolean checkStatus(StCExpressAllocationDO stCExpressAllocationDO, Long objId, ValueHolder valueHolder) {
        if (stCExpressAllocationDO.getCpCPhyWarehouseId() != null) {
            if (stCMainMapper.listByWareid(objId, stCExpressAllocationDO.getCpCPhyWarehouseId()) > 0) {
                valueHolder.put("code", -1);
                valueHolder.put("message", "该仓库物流规则已存在，不能重复！");
                return false;
            }
        }

        if (objId > 0) {
            StCExpressAllocationDO stCExpressAllocationDO1 = stCMainMapper.selectById(objId);
            if (stCExpressAllocationDO1 != null) {
                String strActive = stCExpressAllocationDO1.getIsactive();
                if ("N".equals(strActive)) {
                    valueHolder.put("code", -1);
                    valueHolder.put("message", "单据处于作废状态不能编辑！");
                    return false;
                }
            } else {
                valueHolder.put("code", -1);
                valueHolder.put("message", "数据已不存在！");
                return false;
            }
        }
        return true;
    }

    private void checkList(List<StCExpressAllocationItemDO> itemDOList, ValueHolder valueHolder, Long objid) {
        Map<String,String> modifyMap = new HashMap();

        Map<String,String> oldMap = new HashMap<String, String>();

        String strKey = "";
        String strValue = "";

        StringBuffer errorMessages = new StringBuffer();

        for (StCExpressAllocationItemDO stCExpressAllocationItemDO : itemDOList) {
            Double scale = 0.0;
            if (stCExpressAllocationItemDO.getScale() != null) {
                scale = stCExpressAllocationItemDO.getScale().doubleValue();
            }
            Double limitNum = 0.0;
            if (stCExpressAllocationItemDO.getLimitNum() != null) {
                limitNum = stCExpressAllocationItemDO.getLimitNum().doubleValue();
            }
            if (scale < 0.0 || scale > 100.0) {
                errorMessages.append("比例范围是0=<比例<=100");
            }
            if (limitNum < 0.0) {
                errorMessages.append("and");
                errorMessages.append("限制数量不能为负数！");
            }
            if (errorMessages != null && !"".equals(errorMessages.toString()) && errorMessages.length() > 0) {
                String str = new String(errorMessages.toString());
                throw new NDSException(str);
            }

            //判断新增或者修改的物流公司是否重复
//            if (stCExpressAllocationItemDO.getCpCLogisticsId() != null) {
//                strKey =  stCExpressAllocationItemDO.getCpCLogisticsId().toString();
//                strValue = stCExpressAllocationItemDO.getId().toString();
//
//                if (modifyMap.isEmpty()) {
//                    modifyMap.put(strKey, strValue);
//                } else {
//                    if (modifyMap.containsKey(strKey)) {
//                        errorMessages.append("物流公司不能重复！");
//                        String str = new String(errorMessages.toString());
//                        throw new NDSException(str);
//                    } else {
//                        modifyMap.put(strKey, strValue);
//                    }
//                }
//            }
        }

        //objid = -1为新增的时候忽略后续判断
        if (objid < 0) { return; }

        //读取未修改的行
        List<StCExpressAllocationItemDO> expressAllocationItemList = stCItemMapper.listByMainid(objid);
        if (expressAllocationItemList != null) {
            for (StCExpressAllocationItemDO stCDO : expressAllocationItemList) {
                if (stCDO.getId() != null && stCDO.getCpCLogisticsId() != null) {
                    strKey = stCDO.getCpCLogisticsId().toString();
                    strValue = stCDO.getId().toString();
                    //判断如果是修改物流公司的话，则从oldMap去除这行
                    if (!modifyMap.containsValue(strValue)) {
                        oldMap.put(strKey, strValue);
                    }
                }
            }
        }

//        if (!oldMap.isEmpty() && !modifyMap.isEmpty()) {
//            //判断modifyMap的key值 是否存在于oldMap的key值
//            for (String key : modifyMap.keySet()) {
//                if (oldMap.containsKey(key)) {
//                    errorMessages.append("物流公司不能重复！");
//                    String str = new String(errorMessages.toString());
//                    throw new NDSException(str);
//                }
//            }
//        }
    }
}
