package com.jackrain.nea.st.services;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.cpext.api.CpShopQueryCmd;
import com.jackrain.nea.cpext.model.table.CpShop;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.st.mapper.JdDistributionLogisticsMapper;
import com.jackrain.nea.st.mapper.JdDistributionMapper;
import com.jackrain.nea.st.model.table.JdDistribution;
import com.jackrain.nea.st.utils.ValueHolderUtils;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collections;
import java.util.Date;
import java.util.List;

/**
 * @ClassName JdDistributionService
 * @Description 京东分销商新增
 * <AUTHOR>
 * @Date 2022/12/9 17:13
 * @Version 1.0
 */
@Component
@Slf4j
public class JdDistributionService {

    @Autowired
    private JdDistributionMapper jdDistributionMapper;
    @Autowired
    private JdDistributionLogisticsMapper logisticsMapper;
    @Autowired
    private ApplicationContext applicationContext;
    @Reference(group = "cp-ext", version = "1.0")
    private CpShopQueryCmd cpShopQueryCmd;

    public ValueHolder deleteJdDistribution(QuerySession session) {
        ValueHolder valueHolder = new ValueHolder();
        DefaultWebEvent event = session.getEvent();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(
                        event.getParameterValue("param"), "yyyy-MM-dd HH:mm:ss", SerializerFeature.WriteMapNullValue),
                Feature.OrderedField);
        if (log.isDebugEnabled()) {
            log.debug("start delete jd distribution Receive Params:{}", param);
        }
        Long id = param.getLong("objid");
        JdDistribution jdDistribution = jdDistributionMapper.getById(id);
        if (ObjectUtil.isNull(jdDistribution)) {
            valueHolder.put("code", ResultCode.FAIL);
            valueHolder.put("message", "记录不存在:");
        }

        applicationContext.getBean(JdDistributionService.class).delete(jdDistribution.getId());
        valueHolder.put("code", ResultCode.SUCCESS);
        valueHolder.put("message", "success");
        return valueHolder;
    }

    @Transactional(rollbackFor = Exception.class)
    public void delete(Long distributionId) {
        jdDistributionMapper.deleteById(distributionId);
        logisticsMapper.deleteByDistributionId(distributionId);
    }

    public ValueHolder execute(QuerySession session) {
        DefaultWebEvent event = session.getEvent();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(
                        event.getParameterValue("param"), "yyyy-MM-dd HH:mm:ss", SerializerFeature.WriteMapNullValue),
                Feature.OrderedField);
        if (log.isDebugEnabled()) {
            log.debug("Start Update jd distribution Receive Params:{}", param);
        }
        Long id = param.getLong("objid");
        JSONObject fixColumn = param.getJSONObject("fixcolumn");
        if (fixColumn != null && id != null) {
            if (id < 0) {
                return addJdDistribution(fixColumn, session.getUser());
            } else {
                return updateJdDistribution(fixColumn, id);
            }
        }
        throw new NDSException("当前记录已不存在！");
    }


    public ValueHolder updateJdDistribution(JSONObject fixColumn, Long id) {
        ValueHolder valueHolder = new ValueHolder();
        String string = fixColumn.getString("JD_DISTRIBUTION");
        JdDistribution distribution = JSON.parseObject(string, JdDistribution.class);
        String cpCDistributionId = distribution.getCpCDistributionId();
        Long shopId = distribution.getCpCShopId();
        JdDistribution jdDistribution = jdDistributionMapper.getByDistributionId(shopId, cpCDistributionId);
        if (ObjectUtil.isNotNull(jdDistribution) && ObjectUtil.notEqual(jdDistribution.getId(), distribution.getId())) {
            valueHolder.put("code", ResultCode.FAIL);
            valueHolder.put("message", "不允许存在重复的分销商ID");
            return valueHolder;
        }

        List<CpShop> cpShopList = cpShopQueryCmd.queryShopByIds(Collections.singletonList(distribution.getCpCShopId()));
        // 增加校验店铺的准确性
        if (CollectionUtils.isEmpty(cpShopList)) {
            valueHolder.put("code", ResultCode.FAIL);
            valueHolder.put("message", "店铺数据有误");
            return valueHolder;
        }
        CpShop cpShop = cpShopList.get(0);
        // todo 确认下到底可以修改哪些字段
        return null;
    }

    public ValueHolder addJdDistribution(JSONObject fixColumn, User user) {
        ValueHolder valueHolder = new ValueHolder();
        valueHolder.put("code", ResultCode.SUCCESS);
        valueHolder.put("message", "success");
        String string = fixColumn.getString("JD_DISTRIBUTION");
        JdDistribution distribution = JSON.parseObject(string, JdDistribution.class);
        // 校验分销商id是否已存在表中
        String cpCDistributionId = distribution.getCpCDistributionId();
        Long shopId = distribution.getCpCShopId();
        JdDistribution jdDistribution = jdDistributionMapper.getByDistributionId(shopId, cpCDistributionId);
        if (ObjectUtil.isNotNull(jdDistribution)) {
            valueHolder.put("code", ResultCode.FAIL);
            valueHolder.put("message", "不允许存在重复的分销商ID");
            return valueHolder;
        }
        List<CpShop> cpShopList = cpShopQueryCmd.queryShopByIds(Collections.singletonList(distribution.getCpCShopId()));
        // 增加校验店铺的准确性
        if (CollectionUtils.isEmpty(cpShopList)) {
            valueHolder.put("code", ResultCode.FAIL);
            valueHolder.put("message", "店铺数据有误");
            return valueHolder;
        }
        CpShop cpShop = cpShopList.get(0);
        // 校验平台信息
        if (ObjectUtil.notEqual(cpShop.getCpCPlatformId(), 242L)) {
            valueHolder.put("code", ResultCode.FAIL);
            valueHolder.put("message", "店铺平台信息有误，请选择京东代销平台相关店铺");
            return valueHolder;
        }
        distribution.setCpCShopEcode(cpShop.getEcode());
        distribution.setCpCShopEname(cpShop.getCpCShopTitle());
        Long id = ModelUtil.getSequence("JD_DISTRIBUTION");
        distribution.setId(id);
        distribution.setAdClientId((long) user.getClientId());
        distribution.setAdOrgId((long) user.getOrgId());
        distribution.setCreationdate(new Date());
        distribution.setModifieddate(new Date());
        distribution.setOwnerid(Long.valueOf(user.getId()));
        distribution.setOwnername(user.getName());
        distribution.setModifierid(Long.valueOf(user.getId()));
        distribution.setModifiername(user.getEname());
        distribution.setIsactive("Y");
        jdDistributionMapper.insert(distribution);
        return ValueHolderUtils.getSuccessValueHolder(id, "JD_DISTRIBUTION");
    }
}
