package com.jackrain.nea.st.services;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.jackrain.nea.redis.util.RedisOpsUtil;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.st.common.StConstant;
import com.jackrain.nea.st.mapper.StCOrderLabelMapper;
import com.jackrain.nea.st.mapper.StCOrderLabelShopItemMapper;
import com.jackrain.nea.st.model.table.StCOrderLabelDO;
import com.jackrain.nea.st.model.table.StCOrderLabelShopItemDO;
import com.jackrain.nea.st.utils.RedisConstant;
import com.jackrain.nea.st.utils.StBeanUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;

/**
 * @ClassName : StCOrderLabelCloseService  
 * @Description : 订单打标自动结案
 * <AUTHOR>  YCH
 * @Date: 2021-11-25 10:30  
 */
@Component
@Slf4j
public class StCOrderLabelCloseService {

    @Autowired
    private StCOrderLabelMapper stCOrderLabelMapper;
    @Autowired
    private StCOrderLabelShopItemMapper stCOrderLabelShopItemMapper;

    @Autowired
    private RedisOpsUtil<String, String> redisUtil;

    public void closeStCOrderLabel() {
        List<StCOrderLabelDO> stCOrderLabelDOS = stCOrderLabelMapper.selectList(new QueryWrapper<StCOrderLabelDO>().lambda()
                .eq(StCOrderLabelDO::getStatus, StConstant.HOLD_ORDER_STATUS_02));

        if (CollectionUtils.isEmpty(stCOrderLabelDOS)){
            return;
        }
        for (StCOrderLabelDO stCOrderLabelDO : stCOrderLabelDOS){
            //当前系统时间大于结束时间更新状态已结案
            Date current = new Date();
            if(current.before(stCOrderLabelDO.getEndTime())){
                continue;
            }

            StCOrderLabelDO update = new StCOrderLabelDO();
            update.setId(stCOrderLabelDO.getId());
            update.setStatus(StConstant.HOLD_ORDER_STATUS_04);
            StBeanUtils.makeModifierField(update,SystemUserResource.getRootUser());
            stCOrderLabelMapper.updateById(update);
            List<StCOrderLabelShopItemDO> stCOrderLabelShopItemDOS = stCOrderLabelShopItemMapper.selectStCOrderLabelShopItemList(stCOrderLabelDO.getId());
            if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(stCOrderLabelShopItemDOS)) {
                for (StCOrderLabelShopItemDO stCOrderLabelShopItemDO : stCOrderLabelShopItemDOS) {
                    redisUtil.strRedisTemplate.delete(RedisConstant.ST_ORDER_LABEL + stCOrderLabelShopItemDO.getCpCShopId());
                }
            }
        }
    }
}
