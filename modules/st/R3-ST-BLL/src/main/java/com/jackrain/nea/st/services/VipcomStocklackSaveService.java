package com.jackrain.nea.st.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.google.common.base.Throwables;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.st.common.StConstant;
import com.jackrain.nea.st.mapper.StCVipcomStocklackItemMapper;
import com.jackrain.nea.st.mapper.StCVipcomStocklackMapper;
import com.jackrain.nea.st.model.table.StCVipcomStocklackDO;
import com.jackrain.nea.st.model.table.StCVipcomStocklackItemDO;
import com.jackrain.nea.st.utils.StBeanUtils;
import com.jackrain.nea.st.utils.ValueHolderUtils;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * @Descroption 唯品会缺货策略-保存逻辑
 * <AUTHOR>
 * @Date 2019/3/11 20:13
 */
@Component
@Slf4j
@Transactional
public class VipcomStocklackSaveService extends CommandAdapter {
    @Autowired
    private StCVipcomStocklackMapper stCVipcomStocklackMapper;
    @Autowired
    private StCVipcomStocklackItemMapper stCVipcomStocklackItemMapper;
    /**
     * @param querySession
     * @return com.jackrain.nea.util.ValueHolder
     * @Descroption
     * @Author: 郑小龙
     * @Date 2019/3/11
     */
    @Override
    public ValueHolder execute(QuerySession querySession) throws NDSException {
        DefaultWebEvent event = querySession.getEvent();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(
                event.getParameterValue("param"), "yyyy-MM-dd HH:mm:ss"),
                Feature.OrderedField);
        Long id = param.getLong("objid");
        JSONObject fixColumn = param.getJSONObject("fixcolumn");
        if (fixColumn != null) {
            if (id != null && id > 0) {
                return updateVipcomStocklack(querySession, fixColumn, id);
            } else {
                return addVipcomStocklack(querySession, fixColumn);
            }
        }
        throw new NDSException("当前记录已不存在！");
    }

    /**
     * @param session
     * @param fixColumn
     * @return com.jackrain.nea.util.ValueHolder
     * @Descroption 保存逻辑
     * @Author: 郑小龙
     * @Date 2019/3/11
     */
    private ValueHolder addVipcomStocklack(QuerySession session, JSONObject fixColumn) {
        ValueHolder valueHolder = new ValueHolder();
        String vipcomStocklack = fixColumn.getString(StConstant.TAB_ST_C_VIPCOM_STOCKLACK);
        if (StringUtils.isNotEmpty(vipcomStocklack)) {
            StCVipcomStocklackDO stCVipcomStocklackDO = JSON.parseObject(vipcomStocklack, StCVipcomStocklackDO.class);
            stCVipcomStocklackDO.setId(ModelUtil.getSequence(StConstant.TAB_ST_C_VIPCOM_STOCKLACK));
            //基本字段值设置
            StBeanUtils.makeCreateField(stCVipcomStocklackDO, session.getUser());//创建修改信息
            stCVipcomStocklackDO.setOwnerename(session.getUser().getEname());//创建人账号
            stCVipcomStocklackDO.setModifierename(session.getUser().getEname());//修改人账号
            //校验输入的 po单号和配货单号是否能匹配
            //============暂时先注释，产品讨论暂时不做，之后需要在放开========//
////            String checkMessageStr = checkPoAndDistributionId(stCVipcomStocklackDO);
////            if(StringUtils.isNotEmpty(checkMessageStr)){
////                return ValueHolderUtils.getFailValueHolder(checkMessageStr);
////            }
            JSONArray errorArray = new JSONArray();
            if ((stCVipcomStocklackMapper.insert(stCVipcomStocklackDO)) > 0) {
                saveVipcomStocklackItem(session, fixColumn, stCVipcomStocklackDO.getId(), errorArray);
                return ValueHolderUtils.getSuccessValueHolder(stCVipcomStocklackDO.getId(), StConstant.TAB_ST_C_VIPCOM_STOCKLACK);
            } else {
                return ValueHolderUtils.getFailValueHolder("保存失败");
            }
        }
        throw new NDSException("当前表" + StConstant.TAB_ST_C_VIPCOM_STOCKLACK + "不存在！");
    }



    /**
     * @param session
     * @param fixColumn
     * @param id
     * @return com.jackrain.nea.util.ValueHolder
     * @Descroption 修改逻辑
     * @Author: 郑小龙
     * @Date 2019/3/11
     */
    private ValueHolder updateVipcomStocklack(QuerySession session, JSONObject fixColumn, Long id) {
        ValueHolder valueHolder = new ValueHolder();
        //操作费主表状态数据检查
        if (!checkVipcomStocklack(id, valueHolder)) {
            return valueHolder;
        }
        String vipcomStocklack = fixColumn.getString(StConstant.TAB_ST_C_VIPCOM_STOCKLACK);
        JSONArray errorArray = new JSONArray();
        int insertPrice = 1;//保存明细
        if (StringUtils.isNotEmpty(vipcomStocklack)) {
            StCVipcomStocklackDO stCVipcomStocklackDO = JSON.parseObject(vipcomStocklack, StCVipcomStocklackDO.class);
            stCVipcomStocklackDO.setId(id);
            //基本字段值设置
            StBeanUtils.makeModifierField(stCVipcomStocklackDO, session.getUser());//修改信息
            stCVipcomStocklackDO.setModifierename(session.getUser().getEname());//修改人账号
            //校验输入的 po单号和配货单号是否能匹配
            //==========暂时先注释，产品讨论暂时不做，之后需要在放开=======//
            //String checkMessageStr = checkPoAndDistributionId(stCVipcomStocklackDO);
//            if(StringUtils.isNotEmpty(checkMessageStr)){
//                return ValueHolderUtils.getFailValueHolder(checkMessageStr);
//            }
            insertPrice = stCVipcomStocklackMapper.updateById(stCVipcomStocklackDO);
        }
        if (insertPrice > 0) {
            saveVipcomStocklackItem(session, fixColumn, id, errorArray);
        } else {
            return ValueHolderUtils.getFailValueHolder("保存失败");
        }
        updateVipcomStocklack(session, id);
        return StBeanUtils.getExcuteValueHolder(errorArray);
    }

    /**
     * @param session
     * @param fixColumn
     * @param id
     * @param errorArray
     * @return void
     * @Descroption 明细保存逻辑
     * @Author: 郑小龙
     * @Date 2019/3/11
     */
    private void saveVipcomStocklackItem(QuerySession session, JSONObject fixColumn, Long id, JSONArray errorArray) {
        String vipcomStocklackItem = fixColumn.getString(StConstant.TAB_ST_C_VIPCOM_STOCKLACK_ITEM);
        if (StringUtils.isNotEmpty(vipcomStocklackItem)) {
            try {
                List<StCVipcomStocklackItemDO> stCVipcomStocklackItemDOList = JSON.parseArray(vipcomStocklackItem, StCVipcomStocklackItemDO.class);
                if (stCVipcomStocklackItemDOList.isEmpty() || stCVipcomStocklackItemDOList.size() <= 0) {
                    //errorArray.add(StBeanUtils.getJsonObjectInfo(id, "唯品会JTI缺货策略-明细无数据！"));
                    //throw new NDSException("唯品会JTI缺货策略-明细无数据！");
                    return;
                }
                for (StCVipcomStocklackItemDO stCVipcomStocklackItemDO : stCVipcomStocklackItemDOList) {
                    //sku存在,则更新当前sku所在明细行数据
                    if (stCVipcomStocklackItemDO.getPsCSkuId() != null && stCVipcomStocklackItemDO.getPsCSkuId() > 0) {
                        List<StCVipcomStocklackItemDO> stCVipcomStocklackItemDOS = stCVipcomStocklackItemMapper.selectStocklackBySkuIdAndStocklackId(
                                stCVipcomStocklackItemDO.getPsCSkuId(), id);
                        if (!stCVipcomStocklackItemDOS.isEmpty() && stCVipcomStocklackItemDOS.size() > 0) {
                            stCVipcomStocklackItemDO.setId(stCVipcomStocklackItemDOS.get(0).getId());
                        }
                    }
                    stCVipcomStocklackItemDO.setModifierename(session.getUser().getEname());//修改人账号
                    if (stCVipcomStocklackItemDO.getId() != null && stCVipcomStocklackItemDO.getId() > 0) {
                        //修改
                        StBeanUtils.makeModifierField(stCVipcomStocklackItemDO, session.getUser());//修改信息
                        if ((stCVipcomStocklackItemMapper.updateById(stCVipcomStocklackItemDO)) <= 0) {
                            //errorArray.add(StBeanUtils.getJsonObjectInfo(stCVipcomStocklackItemDO.getId(), "唯品会JTI缺货策略-明细修改失败！"));
                            throw new NDSException("唯品会JTI缺货策略-明细修改失败！");
                        }
                    } else {
                        //创建
                        stCVipcomStocklackItemDO.setStCVipcomStocklackId(id);
                        stCVipcomStocklackItemDO.setId(ModelUtil.getSequence(StConstant.TAB_ST_C_VIPCOM_STOCKLACK_ITEM));
                        StBeanUtils.makeCreateField(stCVipcomStocklackItemDO, session.getUser());//创建信息
                        stCVipcomStocklackItemDO.setOwnerename(session.getUser().getEname());//创建人账号
                        if ((stCVipcomStocklackItemMapper.insert(stCVipcomStocklackItemDO)) <= 0) {
                            //errorArray.add(StBeanUtils.getJsonObjectInfo(stCVipcomStocklackItemDO.getId(), "唯品会JTI缺货策略-明细保存失败！"));
                            throw new NDSException("唯品会JTI缺货策略-明细修改失败！");
                        }
                    }
                }
            } catch (Exception ex) {
                log.error(LogUtil.format("VipcomStocklackSaveService.saveVipcomStocklackItem.Error{}"), Throwables.getStackTraceAsString(ex));
                throw new NDSException("唯品会JIT缺货策略保存异常" + ex.getMessage());
            }
        }
    }

    /**
     * @param session
     * @param id
     * @return void
     * @Descroption 更新主表修改字段信息
     * @Author: 郑小龙
     * @Date 2019/3/11
     */
    private void updateVipcomStocklack(QuerySession session, Long id) {
        StCVipcomStocklackDO stCVipcomStocklackDO = new StCVipcomStocklackDO();
        stCVipcomStocklackDO.setId(id);
        StBeanUtils.makeModifierField(stCVipcomStocklackDO, session.getUser());//修改信息
        stCVipcomStocklackMapper.updateById(stCVipcomStocklackDO);
        if ((stCVipcomStocklackMapper.updateById(stCVipcomStocklackDO)) <= 0) {
            log.error(LogUtil.format("VipcomStocklackSaveService.updateVipcomStocklack.Error"
                    , "保存明细，主表修改字段信息更新出错id=", id));
        }
    }

    /**
     * @param id
     * @param valueHolder
     * @return boolean
     * @Descroption 状态判断
     * @Author: 郑小龙
     * @Date 2019/3/11
     */
    private boolean checkVipcomStocklack(Long id, ValueHolder valueHolder) {
        StCVipcomStocklackDO stCVipcomStocklackDO = stCVipcomStocklackMapper.selectById(id);
        if (stCVipcomStocklackDO == null) {
            valueHolder.put("code", -1);
            valueHolder.put("message", "当前记录已不存在！");
            return false;
        }
        //已作废，不允许编辑
        if (stCVipcomStocklackDO.getIsactive().equals(StConstant.ISACTIVE_N)) {
            valueHolder.put("code", -1);
            valueHolder.put("message", "当前记录已作废，不允许编辑！");
            return false;
        }
        return true;
    }
}
