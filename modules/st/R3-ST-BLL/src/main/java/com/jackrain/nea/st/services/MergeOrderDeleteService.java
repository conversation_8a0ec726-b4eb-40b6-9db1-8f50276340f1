package com.jackrain.nea.st.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.base.Throwables;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.model.resources.OmsRedisKeyResources;
import com.jackrain.nea.redis.util.RedisOpsUtil;
import com.jackrain.nea.st.common.StConstant;
import com.jackrain.nea.st.mapper.StCMergeCategoryLimitItemMapper;
import com.jackrain.nea.st.mapper.StCMergeOrderMapper;
import com.jackrain.nea.st.model.table.StCMergeCategoryLimitItemDO;
import com.jackrain.nea.st.model.table.StCMergeOrderDO;
import com.jackrain.nea.st.utils.RedisCacheUtil;
import com.jackrain.nea.st.utils.RedisConstant;
import com.jackrain.nea.st.utils.StBeanUtils;
import com.jackrain.nea.st.utils.ValueHolderUtils;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * description：合单策略删除
 *
 * <AUTHOR>
 * @date 2021/5/13
 */
@Component
@Slf4j
@Transactional
public class MergeOrderDeleteService extends CommandAdapter {

    @Autowired
    private RedisOpsUtil redisUtil;

    @Autowired
    private StCMergeOrderMapper stCMergeOrderMapper;
    @Autowired
    private StCMergeCategoryLimitItemMapper mergeCategoryLimitItemMapper;

    @Override
    public ValueHolder execute(QuerySession querySession) throws NDSException {
        DefaultWebEvent event = querySession.getEvent();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(
                event.getParameterValue("param"), "yyyy-MM-dd HH:mm:ss"),
                Feature.OrderedField);
        boolean delMainFlag = param.getBoolean("isdelmtable");//是否删除主表
        Long id = param.getLong("objid");
        if (id != null && id > 0) {
            ValueHolder valueHolder = new ValueHolder();
            StCMergeOrderDO stCMergeOrderDO = stCMergeOrderMapper.selectById(id);
            if (!checkMergeOrderStatus(stCMergeOrderDO, valueHolder)) {
                return valueHolder;
            }
            if (delMainFlag) {
                List<StCMergeCategoryLimitItemDO> stCCategoryLimitItemDOList = mergeCategoryLimitItemMapper.selectList(new LambdaQueryWrapper<StCMergeCategoryLimitItemDO>().eq(StCMergeCategoryLimitItemDO::getStCMergeOrderId, id));
                if ((!stCCategoryLimitItemDOList.isEmpty() && stCCategoryLimitItemDOList.size() > 0)) {
                    return ValueHolderUtils.getFailValueHolder("订单合单策略存在明细，不允许删除！");
                }
                if ((stCMergeOrderMapper.deleteById(stCMergeOrderDO)) > 0) {
                    delRedisKey();
                    return ValueHolderUtils.getFailValueHolder("删除主表成功！");
                }
            } else {
                //单独删除明细
                JSONObject tabitem = param.getJSONObject("tabitem");
                JSONArray errorArray = new JSONArray();
                JSONArray itemArray = tabitem.getJSONArray(StConstant.TAB_ST_C_MERGE_CATEGORY_LIMIT_ITEM);
                if (itemArray != null && itemArray.size() > 0) {
                    deleteCategoryLimitItemByMergeOrderID(itemArray, errorArray);
                }
                updateMergeOrderDate(querySession, stCMergeOrderDO);
                return StBeanUtils.getExcuteValueHolder(errorArray);
            }
        }
        throw new NDSException("当前记录已不存在！");
    }

    private void deleteCategoryLimitItemByMergeOrderID(JSONArray itemArray, JSONArray errorArray) {
        if (itemArray.size() > 0) {
            for (int i = 0; i < itemArray.size(); i++) {
                Long itemid = itemArray.getLong(i);
                if ((mergeCategoryLimitItemMapper.deleteById(itemid)) <= 0) {
                    errorArray.add(StBeanUtils.getJsonObjectInfo(itemid, "订单合单策略-明细记录已不存在！"));
                }
            }
        }
    }

    private void updateMergeOrderDate(QuerySession session, StCMergeOrderDO stCMergeOrderDO) {
        StCMergeOrderDO update = new StCMergeOrderDO();
        update.setId(stCMergeOrderDO.getId());
        StBeanUtils.makeModifierField(update, session.getUser());//修改信息
        if ((stCMergeOrderMapper.updateById(stCMergeOrderDO)) <= 0) {
            log.error(LogUtil.format("MergeOrderDelService.updateMergeOrderDate.Error: 删除明细，主表修改字段信息更新出错id:{}"),
                    stCMergeOrderDO.getId());
        } else {
            if (stCMergeOrderDO.getCpCShopId() != null) {
                RedisCacheUtil.delete(stCMergeOrderDO.getCpCShopId(), RedisConstant.SHOP_PRICE_STRATEGY);
            }
        }
    }

    private boolean checkMergeOrderStatus(StCMergeOrderDO stCMergeOrderDO, ValueHolder valueHolder) {
        if (stCMergeOrderDO == null) {
            valueHolder.put("code", -1);
            valueHolder.put("message", "当前记录不存在！");
            return false;
        }
        if (stCMergeOrderDO.getIsactive().equals(StConstant.ISACTIVE_N)) {
            valueHolder.put("code", -1);
            valueHolder.put("message", "当前记录不存在！");
            return false;
        }
        return true;
    }

    private void delRedisKey() {
        try {
            String redisKey = OmsRedisKeyResources.buildAutoMergeAllListRedisKey();
            if (redisUtil.strRedisTemplate.hasKey(redisKey)) {
                redisUtil.strRedisTemplate.delete(redisKey);
            }
        } catch (Exception e) {
            log.error(LogUtil.format("redis删除自动合单策略异常{}"), Throwables.getStackTraceAsString(e));
        }
    }
}
