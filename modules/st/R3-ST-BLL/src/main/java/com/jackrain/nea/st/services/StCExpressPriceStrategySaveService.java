package com.jackrain.nea.st.services;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.st.annotation.StOperationLog;
import com.jackrain.nea.st.common.StConstant;
import com.jackrain.nea.st.mapper.StCExpressPriceStrategyItemMapper;
import com.jackrain.nea.st.mapper.StCExpressPriceStrategyMapper;
import com.jackrain.nea.st.model.enums.StCExpressPriceStrategyEnum;
import com.jackrain.nea.st.model.table.StCExpressPriceStrategyDO;
import com.jackrain.nea.st.model.table.StCExpressPriceStrategyItemDO;
import com.jackrain.nea.st.utils.JsonUtils;
import com.jackrain.nea.st.utils.StBeanUtils;
import com.jackrain.nea.st.utils.ValueHolderUtils;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2022/6/9 17:42
 * @Description 快递报价设置 策略 保存的新增修改业务逻辑
 * @Version 1.0
 */
@Component
@Slf4j
public class StCExpressPriceStrategySaveService extends CommandAdapter {

    @Resource
    private StCExpressPriceStrategyMapper stCExpressPriceStrategyMapper;

    @Resource
    private StCExpressPriceStrategyItemMapper stCExpressPriceStrategyItemMapper;


    /**
     * 新增和修改
     *
     * @param querySession 参数封装
     * @return 返回状态
     * @throws NDSException 异常信息
     */
    @StOperationLog(mainTableName = "ST_C_EXPRESS_PRICE_STRATEGY", itemsTableName = "ST_C_EXPRESS_PRICE_STRATEGY_ITEM")
    @Override
    public ValueHolder execute(QuerySession querySession) throws NDSException {
        ValueHolder valueHolder = new ValueHolder();
        DefaultWebEvent event = querySession.getEvent();
        List<String> nullKeyList = new ArrayList<>();
        if (log.isDebugEnabled()) {
            log.debug("Start StExpressPriceService execute event:{}", JSONObject.toJSONString(event));
        }
        JSONObject param = JsonUtils.filterNull((JSONObject) event.getParameterValue("param"), nullKeyList);

        if (param != null) {
            Long id = param.getLong("objid");
            JSONObject fixColumn = param.getJSONObject("fixcolumn");
            if (fixColumn != null) {
                JSONObject mainJsonObject = fixColumn.getJSONObject(StConstant.TAB_ST_C_EXPRESS_PRICE_STRATEGY);
                JSONArray itemJsonArray = fixColumn.getJSONArray(StConstant.TAB_ST_C_EXPRESS_PRICE_STRATEGY_ITEM);
                if (id != null && id < 0) {
                    //新增
                    valueHolder = saveFunction(mainJsonObject, querySession);
                } else {
                    //编辑
                    valueHolder = updateFunction(mainJsonObject, itemJsonArray, querySession, id);
                }
            }
        } else {
            throw new NDSException("参数为空！");
        }
        return valueHolder;
    }


    /**
     * 新增操作
     *
     * @param mainJsonObject
     * @param querySession
     * @return
     */
    private ValueHolder saveFunction(JSONObject mainJsonObject, QuerySession querySession) {
        StCExpressPriceStrategyDO stCExpressPriceStrategyDO = JSON.parseObject(mainJsonObject.toJSONString(),
                new TypeReference<StCExpressPriceStrategyDO>() {
                });
        ValueHolder holder;
        if (stCExpressPriceStrategyDO != null) {
            stCExpressPriceStrategyDO.setId(ModelUtil.getSequence(StConstant.TAB_ST_C_EXPRESS_PRICE_STRATEGY));
            StBeanUtils.makeCreateField(stCExpressPriceStrategyDO, querySession.getUser());
            reSetDateTimeRange(stCExpressPriceStrategyDO);
            if (stCExpressPriceStrategyDO.getEndDate().getTime() < stCExpressPriceStrategyDO.getStartDate().getTime()) {
                holder = ValueHolderUtils.getFailValueHolder("结束日期不允许小于开始日期");
                return holder;
            }
            if (stCExpressPriceStrategyDO.getPriceHomeDelivery() == null) {
                stCExpressPriceStrategyDO.setPriceHomeDelivery(BigDecimal.ZERO);
            }
            if (stCExpressPriceStrategyMapper.insert(stCExpressPriceStrategyDO) <= 0) {
                holder = ValueHolderUtils.getFailValueHolder("保存失败！");
                return holder;
            }
        } else {
            holder = ValueHolderUtils.getFailValueHolder("保存失败！");
            return holder;
        }
        holder = ValueHolderUtils.getSuccessValueHolder(stCExpressPriceStrategyDO.getId(), StConstant.TAB_ST_C_EXPRESS_PRICE_STRATEGY);
        return holder;
    }


    /**
     * 更新操作
     *
     * @param mainJsonObject
     * @param itemJsonArray
     * @param querySession
     * @param objid
     * @return
     */
    private ValueHolder updateFunction(JSONObject mainJsonObject, JSONArray itemJsonArray, QuerySession querySession, Long objid) {

        StCExpressPriceStrategyDO oldMainData = stCExpressPriceStrategyMapper.selectById(objid);
        if (oldMainData == null) {
            return ValueHolderUtils.getFailValueHolder("当前记录已存在，不允许编辑！");
        }
        if (StCExpressPriceStrategyEnum.CLOSED.getKey().equals(oldMainData.getCloseStatus())) {
            return ValueHolderUtils.getFailValueHolder("当前记录已结案，不允许编辑！");
        }
        if (StCExpressPriceStrategyEnum.SUBMITTED.getKey().equals(oldMainData.getStatus())) {
            return ValueHolderUtils.getFailValueHolder("当前记录已提交，不允许编辑！");
        }
        //主表更新，objid就是主表ID
        StCExpressPriceStrategyDO stCExpressPriceStrategyDO = new StCExpressPriceStrategyDO();
        if (mainJsonObject != null && !mainJsonObject.isEmpty()) {
            stCExpressPriceStrategyDO = JSON.parseObject(mainJsonObject.toJSONString(),
                    new TypeReference<StCExpressPriceStrategyDO>() {
                    });
            //校验日期是否违法
            if (stCExpressPriceStrategyDO.getStartDate() == null) {
                stCExpressPriceStrategyDO.setStartDate(oldMainData.getStartDate());
            }
            if (stCExpressPriceStrategyDO.getEndDate() == null) {
                stCExpressPriceStrategyDO.setEndDate(oldMainData.getEndDate());
            }
            reSetDateTimeRange(stCExpressPriceStrategyDO);
            if (stCExpressPriceStrategyDO.getEndDate().getTime() < stCExpressPriceStrategyDO.getStartDate().getTime()) {
                return ValueHolderUtils.getFailValueHolder("结束日期不允许小于开始日期");
            }
        }

        //判断子表数据是否存在
        if (itemJsonArray != null && !itemJsonArray.isEmpty()) {
            //补充可为空字段
            List<StCExpressPriceStrategyItemDO> stCExpressPriceStrategyItemDOList = convert(itemJsonArray);
            //检查行明细
            checkItem(stCExpressPriceStrategyItemDOList, objid);
            for (StCExpressPriceStrategyItemDO stCExpressPriceStrategyItemDO : stCExpressPriceStrategyItemDOList) {
                Long id = stCExpressPriceStrategyItemDO.getId();
                if (id < 0) {
                    stCExpressPriceStrategyItemDO.setId(ModelUtil.getSequence(StConstant.TAB_ST_C_EXPRESS_PRICE_STRATEGY_ITEM));
                    stCExpressPriceStrategyItemDO.setStCExpressPriceStrategyId(objid);
                    StBeanUtils.makeCreateField(stCExpressPriceStrategyItemDO, querySession.getUser());
                    if (stCExpressPriceStrategyItemMapper.insert(stCExpressPriceStrategyItemDO) <= 0) {
                        return ValueHolderUtils.getFailValueHolder("保存失败！");
                    }
                } else {
                    //修改原有的行信息 id>0
                    StBeanUtils.makeModifierField(stCExpressPriceStrategyItemDO, querySession.getUser());
                    if (stCExpressPriceStrategyItemMapper.updateById(stCExpressPriceStrategyItemDO) <= 0) {
                        return ValueHolderUtils.getFailValueHolder("保存失败！");
                    }
                }
            }
        }
        if (stCExpressPriceStrategyDO.getPriceHomeDelivery() == null && (mainJsonObject == null || !mainJsonObject.containsKey("PRICE_HOME_DELIVERY"))) {
            stCExpressPriceStrategyDO.setPriceHomeDelivery(oldMainData.getPriceHomeDelivery());
        }
        //改为空默认为0
        if (stCExpressPriceStrategyDO.getPriceHomeDelivery() == null) {
            stCExpressPriceStrategyDO.setPriceHomeDelivery(BigDecimal.ZERO);
        }
        if (stCExpressPriceStrategyDO.getRemark() == null && (mainJsonObject == null || !mainJsonObject.containsKey("REMARK"))) {
            stCExpressPriceStrategyDO.setRemark(oldMainData.getRemark());
        }
        stCExpressPriceStrategyDO.setId(objid);
        StBeanUtils.makeModifierField(stCExpressPriceStrategyDO, querySession.getUser());
        stCExpressPriceStrategyMapper.updateById(stCExpressPriceStrategyDO);
        return ValueHolderUtils.getSuccessValueHolder(objid, StConstant.TAB_ST_C_EXPRESS_PRICE_STRATEGY);
    }

    private List<StCExpressPriceStrategyItemDO> convert(JSONArray itemJsonArray) {
        if (itemJsonArray == null || itemJsonArray.isEmpty()) {
            return new ArrayList<>();
        }
        List<StCExpressPriceStrategyItemDO> itemDOList = new ArrayList<>();
        for (int i = 0; i < itemJsonArray.size(); i++) {
            JSONObject itemJsonObject = itemJsonArray.getJSONObject(i);
            StCExpressPriceStrategyItemDO itemDO = JSON.parseObject(itemJsonObject.toJSONString(),
                    new TypeReference<StCExpressPriceStrategyItemDO>() {
                    });
            itemDOList.add(itemDO);
            Long itemId = itemJsonObject.getLong("ID");
            if (itemId != null && itemId > 0) {
                StCExpressPriceStrategyItemDO oldItem = stCExpressPriceStrategyItemMapper.selectById(itemDO.getId());
                if (oldItem == null) {
                    throw new NDSException("修改的明细数据不存在或已删除！");
                }
                if (itemDO.getProvinceId() == null) {
                    itemDO.setProvinceId(oldItem.getProvinceId());
                }
                if (itemDO.getCityId() == null && !itemJsonObject.containsKey("CITY_ID")) {
                    itemDO.setCityId(oldItem.getCityId());
                }
                if (itemDO.getStartWeight() == null) {
                    itemDO.setStartWeight(oldItem.getStartWeight());
                }
                if (itemDO.getEndWeight() == null) {
                    itemDO.setEndWeight(oldItem.getEndWeight());
                }
                if (itemDO.getPriceExpress() == null && !itemJsonObject.containsKey("PRICE_EXPRESS")) {
                    itemDO.setPriceExpress(oldItem.getPriceExpress());
                }
                if (itemDO.getPriceFirstWeight() == null && !itemJsonObject.containsKey("PRICE_FIRST_WEIGHT")) {
                    itemDO.setPriceFirstWeight(oldItem.getPriceFirstWeight());
                }
                if (itemDO.getPriceContinuedWeight() == null && !itemJsonObject.containsKey("PRICE_CONTINUED_WEIGHT")) {
                    itemDO.setPriceContinuedWeight(oldItem.getPriceContinuedWeight());
                }
            }
        }
        return itemDOList;
    }


    /**
     * 明细前置条件保存校验
     *
     * @param itemDOList
     * @return
     */
    private void checkItem(List<StCExpressPriceStrategyItemDO> itemDOList, Long objid) {
        //校验有且只有一种计费方式
        for (StCExpressPriceStrategyItemDO itemDO : itemDOList) {
            if (!checkPriceStrategy(itemDO)) {
                throw new NDSException("有且仅支持一种报价方式！");
            }
            if (itemDO.getEndWeight().compareTo(itemDO.getStartWeight()) <= 0) {
                throw new NDSException("结束重量必须大于起始重量！");
            }
        }

        //将子表按照省份分组，先验证集合内的交叉情况（保留原有逻辑：相同省份&重量存在交集不允许保存）
        Map<Long, List<StCExpressPriceStrategyItemDO>> listMapByProvince = itemDOList.stream().collect(Collectors.groupingBy(StCExpressPriceStrategyItemDO::getProvinceId));
        for (Long provinceId : listMapByProvince.keySet()) {
            List<StCExpressPriceStrategyItemDO> strategyItemDOList = listMapByProvince.get(provinceId);
            if (strategyItemDOList != null && strategyItemDOList.size() > 1) {
                for (int i = 0; i < strategyItemDOList.size(); i++) {
                    for (int j = i + 1; j < strategyItemDOList.size(); j++) {
                        StCExpressPriceStrategyItemDO one = strategyItemDOList.get(i);
                        StCExpressPriceStrategyItemDO two = strategyItemDOList.get(j);
                        // 校验相同省份和城市的重量不能有交集
                        if ((one.getCityId() == null && two.getCityId() == null) ||
                                (one.getCityId() != null && one.getCityId().equals(two.getCityId()))) {
                            Boolean flag = checkWeight(one.getStartWeight(), one.getEndWeight(), two.getStartWeight(), two.getEndWeight());
                            if (!flag) {
                                throw new NDSException("录入的明细已存在，不允许重复录入！");
                            }
                        }
                    }
                }
            }
        }

        //与库内数据校验
        for (StCExpressPriceStrategyItemDO itemDO : itemDOList) {
            LambdaQueryWrapper<StCExpressPriceStrategyItemDO> queryWrapper = new LambdaQueryWrapper<StCExpressPriceStrategyItemDO>()
                    .eq(StCExpressPriceStrategyItemDO::getStCExpressPriceStrategyId, objid)
                    .eq(StCExpressPriceStrategyItemDO::getProvinceId, itemDO.getProvinceId())
                    .ne(StCExpressPriceStrategyItemDO::getId, itemDO.getId());

            // 如果当前明细有城市，则需要校验相同省份&相同城市&重量存在交集不允许保存
            if (itemDO.getCityId() != null) {
                queryWrapper.eq(StCExpressPriceStrategyItemDO::getCityId, itemDO.getCityId());
            } else {
                // 如果当前明细没有城市，则按原有逻辑校验相同省份&重量存在交集不允许保存（只查询城市为空的记录）
                queryWrapper.isNull(StCExpressPriceStrategyItemDO::getCityId);
            }

            List<StCExpressPriceStrategyItemDO> selectList = stCExpressPriceStrategyItemMapper.selectList(queryWrapper);
            if (CollectionUtils.isNotEmpty(selectList)) {
                for (StCExpressPriceStrategyItemDO strategyItemDO : selectList) {
                    Boolean flag = checkWeight(itemDO.getStartWeight(), itemDO.getEndWeight(), strategyItemDO.getStartWeight(), strategyItemDO.getEndWeight());
                    if (!flag) {
                        throw new NDSException("录入的明细已存在，不允许重复录入！");
                    }
                }
            }
        }
    }

    private boolean checkPriceStrategy(StCExpressPriceStrategyItemDO itemDO) {
        if (itemDO.getPriceExpress() != null) {
            return itemDO.getPriceFirstWeight() == null && itemDO.getPriceContinuedWeight() == null;
        } else {
            return itemDO.getPriceFirstWeight() != null && itemDO.getPriceContinuedWeight() != null;
        }
    }

    /**
     * 验证重量是否存在交叉
     *
     * @param startWeight
     * @param endWeight
     * @param startWeight1
     * @param endWeight1
     * @return
     */
    private Boolean checkWeight(BigDecimal startWeight, BigDecimal endWeight, BigDecimal startWeight1, BigDecimal endWeight1) {
        if (endWeight.compareTo(endWeight1) == 0) {
            return false;
        } else {
            if (endWeight.compareTo(endWeight1) == 1) {
                //endWeight大，当startWeight小于endWeight1时，返回false
                if (startWeight.compareTo(endWeight1) == -1) {
                    return false;
                }
            } else {
                //endWeight1大，当startWeight1小于endWeight时，返回false
                if (startWeight1.compareTo(endWeight) == -1) {
                    return false;
                }
            }
        }
        return true;
    }

    /**
     * 开始日期取当天0点，结束日期取当天23:59:59
     *
     * @param mainObj 主对象
     */
    private void reSetDateTimeRange(StCExpressPriceStrategyDO mainObj) {
        if (Objects.isNull(mainObj)) {
            return;
        }
        if (Objects.nonNull(mainObj.getStartDate())) {
            mainObj.setStartDate(DateUtil.beginOfDay(mainObj.getStartDate()));
        }
        if (Objects.nonNull(mainObj.getEndDate())) {
            /*mainObj.setEndDate(DateUtil.endOfDay(mainObj.getEndDate()));*/
            DateTime dateTime = DateUtil.endOfDay(mainObj.getEndDate());
            dateTime.setTime(dateTime.getTime() - 999);
            mainObj.setEndDate(dateTime);
        }
    }
}