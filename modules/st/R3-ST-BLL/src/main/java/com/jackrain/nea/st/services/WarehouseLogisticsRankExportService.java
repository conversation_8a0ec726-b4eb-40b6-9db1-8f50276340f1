package com.jackrain.nea.st.services;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.google.common.collect.Lists;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.st.config.R3OssConfig;
import com.jackrain.nea.st.mapper.StCWarehouseLogisticsItemMapper;
import com.jackrain.nea.st.model.result.WarehouseLogisticsRankResult;
import com.jackrain.nea.st.model.table.StCWarehouseLogisticsItemDO;
import com.jackrain.nea.st.utils.ExportUtil;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @author:huang.zaizai
 * @since: 2019/8/15
 * @create at : 2019/8/15 22:37
 */
@Component
@Slf4j
public class WarehouseLogisticsRankExportService extends CommandAdapter {
    @Autowired
    private ExportUtil exportUtil;
    @Autowired
    private WarehouseLogisticsQueryService queryService;
    @Autowired
    private StCWarehouseLogisticsItemMapper itemMapper;
    @Autowired
    private R3OssConfig r3OssConfig;

    public ValueHolderV14 exportWarehouseLogisticsRank(JSONObject obj, User user){
        ValueHolderV14 vh = new ValueHolderV14(ResultCode.SUCCESS, "仓库物流优先级明细导出成功！");
        List<WarehouseLogisticsRankResult> mainExcelList = Lists.newLinkedList();

        ValueHolderV14<List<WarehouseLogisticsRankResult>> tableVh = queryService.queryRankResultTable(obj);
        if (tableVh.isOK()) {
            mainExcelList = tableVh.getData();
        }
        exportUtil.setEndpoint(r3OssConfig.getEndpoint());
        exportUtil.setAccessKeyId(r3OssConfig.getAccessKeyId());
        exportUtil.setAccessKeySecret(r3OssConfig.getAccessKeySecret());
        exportUtil.setBucketName(r3OssConfig.getBucketName());
        if(StringUtils.isEmpty(r3OssConfig.getTimeout())){
            //如果获取不到apllo配置参数，设置默认过期时间为30分钟
            exportUtil.setTimeout("1800000");
        } else {
            exportUtil.setTimeout(r3OssConfig.getTimeout());
        }
        /**
         *  拼接Excel主表sheet表头字段和列表
         * */
        String mainNames[] = {"省", "市"};
        String orderKeys[] = {"cpCRegionProvinceEname", "cpCRegionCityEname"};
        List<String> mainName = Lists.newArrayList(mainNames);
        List<String> mainKey = Lists.newArrayList(orderKeys);

        Long id = obj.getLong("objid");
        //获取物流公司信息
        List<StCWarehouseLogisticsItemDO> itemList = itemMapper.selectList(new QueryWrapper<StCWarehouseLogisticsItemDO>()
                .lambda().eq(StCWarehouseLogisticsItemDO::getStCWarehouseLogisticsId, id));
        if (CollectionUtils.isNotEmpty(itemList)) {
            itemList = itemList.stream().sorted(Comparator.comparing(StCWarehouseLogisticsItemDO::getId))
                    .collect(Collectors.toList());
        }
        int i = 1;
        for (StCWarehouseLogisticsItemDO item : itemList) {
            mainName.add(item.getCpCLogisticsEname());
            mainKey.add("rank" + i);
            i ++;
        }
        XSSFWorkbook hssfWorkbook = new XSSFWorkbook();
        exportUtil.executeSheet(hssfWorkbook, "仓库物流优先级明细", "", mainName,
                Lists.newArrayList(), mainKey, mainExcelList, false);
        String putMsg = exportUtil.saveFileAndPutOss(hssfWorkbook, "仓库物流优先级明细导出",
                user, "OSS-Bucket/EXPORT/StCWarehouseLogisticsRank/");
        if(StringUtils.isEmpty(putMsg)){
            vh.setCode(ResultCode.FAIL);
            vh.setMessage("仓库物流优先级明细导出失败");
        }
        vh.setData(putMsg);
        return vh;
    }
}
