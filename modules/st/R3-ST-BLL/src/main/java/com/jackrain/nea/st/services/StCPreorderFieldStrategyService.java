package com.jackrain.nea.st.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * @ClassName StCPreorderFieldStrategyService
 * @Description 导入模板字段明细
 * <AUTHOR>
 * @Date 2022/12/23 17:13
 * @Version 1.0
 */
@Component
@Slf4j
public class StCPreorderFieldStrategyService {

    public ValueHolder delFieldStrategy(QuerySession session) {
        ValueHolder valueHolder = new ValueHolder();
        valueHolder.put("code", ResultCode.SUCCESS);
        valueHolder.put("message", "success");

        DefaultWebEvent event = session.getEvent();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(
                        event.getParameterValue("param"), "yyyy-MM-dd HH:mm:ss", SerializerFeature.WriteMapNullValue),
                Feature.OrderedField);
        JSONObject jsonObject = param.getJSONObject("data");
        log.info("StCPreorderFieldStrategyService delFieldStrategy jsonObject:{}", jsonObject.toJSONString());
        return valueHolder;
    }
}
