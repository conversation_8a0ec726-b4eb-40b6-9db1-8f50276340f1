package com.jackrain.nea.st.services;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.google.common.collect.Lists;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.st.common.StConstant;
import com.jackrain.nea.st.mapper.StCSyncSkustockStoreMapper;
import com.jackrain.nea.st.mapper.StCSyncSkustockStrategyMapper;
import com.jackrain.nea.st.model.result.SgSyncSkuStockResult;
import com.jackrain.nea.st.model.result.StCSyncSkustockStrategyResult;
import com.jackrain.nea.st.model.table.StCSyncSkustockStoreDO;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.utility.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;

import static java.util.stream.Collectors.toList;

/**
 * @author: 汪聿森
 * @Date: Created in 2020-05-20 15:41
 * @Description : 查询需要同步的条码的供货仓id给库存中心同步库存
 */
@Component
@Slf4j
public class SgSyncSkuStockService {
    @Autowired
    private StCSyncSkustockStoreMapper stCSyncSkustockStoreMapper;

    @Autowired
    private StCSyncSkustockStrategyMapper stCSyncSkustockStrategyMapper;


    public ValueHolderV14 querySkuStockSync(Long cpCShopId, Long skuId) {
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("SgSyncSkuStockService入参：cpCShopId/skuId=", cpCShopId, skuId));
        }
        ValueHolderV14 vh = new ValueHolderV14();
        if (cpCShopId == null || skuId == null) {
            vh.setMessage("传入参数为空！");
            vh.setCode(ResultCode.FAIL);
            return vh;
        }
        List<Long> isExist = Lists.newArrayList();
        List<StCSyncSkustockStrategyResult> stCSyncSkustockStrategyResults = stCSyncSkustockStrategyMapper.selectValidTemplat(cpCShopId, skuId);
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("stCSyncSkustockStrategyResults：") + stCSyncSkustockStrategyResults);
        }
        List<Long> existTemplatIds = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(stCSyncSkustockStrategyResults)) {
            for (StCSyncSkustockStrategyResult stCSyncSkustockStrategyResult : stCSyncSkustockStrategyResults) {
                Date beginTime = stCSyncSkustockStrategyResult.getBeginTime();
                Date endTime = stCSyncSkustockStrategyResult.getEndTime();
                Date currDate = new Date();
                //在生效时间内
                if (beginTime.before(currDate) && endTime.after(currDate) ) {
                    existTemplatIds.add(stCSyncSkustockStrategyResult.getId());
                }
            }
            if (CollectionUtils.isEmpty(existTemplatIds)) {
                vh.setMessage("不存在生效的方案！");
                vh.setCode(ResultCode.FAIL);
                return vh;
            }
            List<Long> exist = existTemplatIds.stream().distinct().collect(toList());
            List<StCSyncSkustockStoreDO> stCSyncSkustockStoreDOS = stCSyncSkustockStoreMapper.selectList(new QueryWrapper<StCSyncSkustockStoreDO>().lambda()
                    .in(StCSyncSkustockStoreDO::getStCSyncSkustockStrategyId, exist)
                    .eq(StCSyncSkustockStoreDO::getIsactive, StConstant.ISACTIVE_Y));
            if (CollectionUtils.isNotEmpty(stCSyncSkustockStoreDOS)) {
                isExist = stCSyncSkustockStoreDOS.stream().map(StCSyncSkustockStoreDO::getCpCStoreId).collect(toList()).stream().distinct().collect(toList());
            }
        } else {
            vh.setMessage("不存在方案！");
            vh.setCode(ResultCode.FAIL);
            vh.setData(null);
            return vh;
        }
        if (CollectionUtils.isNotEmpty(isExist)) {
            SgSyncSkuStockResult sgSyncSkuStockResult = new SgSyncSkuStockResult();
            sgSyncSkuStockResult.setIsExist(isExist);
            vh.setMessage("查询成功！");
            vh.setCode(ResultCode.SUCCESS);
            vh.setData(sgSyncSkuStockResult);
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("查询结果：") + vh);
            }
            return vh;
        } else {
            vh.setMessage("逻辑仓不存在！");
            vh.setCode(ResultCode.FAIL);
            vh.setData(null);
            return vh;
        }
//        List<Long> notExist = Lists.newArrayList();
//        //根据店铺查询方案（已审核）
//        List<StCSyncSkustockStrategyDO> stCSyncSkustockStrategyDOList = stCSyncSkustockStrategyMapper.selectList(new QueryWrapper<StCSyncSkustockStrategyDO>().lambda()
//                .eq(StCSyncSkustockStrategyDO::getCpCShopId, cpCShopId)
//                .eq(StCSyncSkustockStrategyDO::getBillStatus, StConstant.SKUSTOCK_STATUS_02)
//                .eq(StCSyncSkustockStrategyDO::getIsactive, StConstant.ISACTIVE_Y));
//        if (log.isDebugEnabled()) {
//            log.debug(LogUtil.format("根据店铺查询已审核的方案:" + stCSyncSkustockStrategyDOList);
//        }
//        if (CollectionUtils.isEmpty(stCSyncSkustockStrategyDOList)) {
//            vh.setMessage("根据店铺ID【" + cpCShopId + "】没有查询到方案！");
//            vh.setCode(ResultCode.FAIL);
//            return vh;
//        }
//        //存储生效方案id
//        List<Long> existTemplatIds = Lists.newArrayList();
//        //已审核方案中过滤出在生效时间内的方案
//        for (StCSyncSkustockStrategyDO stCSyncSkustockStrategyDO : stCSyncSkustockStrategyDOList) {
//            Date beginTime = stCSyncSkustockStrategyDO.getBeginTime();
//            Date endTime = stCSyncSkustockStrategyDO.getEndTime();
//            Date currDate = new Date();
//            //在生效时间内
//            if (beginTime.compareTo(currDate) <= 0 && endTime.compareTo(currDate) >= 0) {
//                existTemplatIds.add(stCSyncSkustockStrategyDO.getId());
//            }
//        }
//        if (CollectionUtils.isEmpty(existTemplatIds)) {
//            vh.setMessage("不存在生效的方案！");
//            vh.setCode(ResultCode.FAIL);
//            return vh;
//        }
//        List<Long> mainId = Lists.newArrayList();
//        //查询所有有效方案条码
//        List<StCSyncSkustockStrategyResult> stCSyncSkustockStrategyDOSPro = stCSyncSkustockStrategyMapper.selectValidProInfo(existTemplatIds);
//        //查询有效店铺下所有供货仓
//        List<StCSyncSkustockStrategyResult> stCSyncSkustockStrategyDOSStore = stCSyncSkustockStrategyMapper.selectValidStoreInfo(existTemplatIds);
//        if (CollectionUtils.isNotEmpty(stCSyncSkustockStrategyDOSPro)) {
//            //根据传入的条码id 过滤出存在条码
//            List<StCSyncSkustockStrategyResult> stCSyncSkustockStrategyDOSNew = stCSyncSkustockStrategyDOSPro.stream().filter(s -> s.getPsCSkuId().equals(skuId)).collect(toList());
//            //不为空 说明传进来的条码存在方案中
//            if (CollectionUtils.isNotEmpty(stCSyncSkustockStrategyDOSNew)) {
//                for (StCSyncSkustockStrategyResult stCSyncSkustockStrategyResult : stCSyncSkustockStrategyDOSNew) {
//                    //记录存在条码的主表id
//                    mainId.add(stCSyncSkustockStrategyResult.getId());
//                }
//            }
//            //不为空  根据主表id查询出存在方案的供货仓信息
//            if (CollectionUtils.isNotEmpty(mainId)) {
//                List<StCSyncSkustockStrategyResult> existStoreIds = stCSyncSkustockStrategyMapper.selectValidStoreInfo(mainId);
//                isExist = existStoreIds.stream().map(StCSyncSkustockStrategyResult::getCpCStoreId).collect(toList()).stream().distinct().collect(toList());
//                List<Long> allStoreList = stCSyncSkustockStrategyDOSStore.stream().map(StCSyncSkustockStrategyResult::getCpCStoreId).collect(toList()).stream().distinct().collect(toList());
//                List<Long> finalIsExist = isExist;
//                notExist = allStoreList.stream().filter(item -> !finalIsExist.contains(item)).collect(toList());
//            } else {
//                notExist = stCSyncSkustockStrategyDOSStore.stream().map(StCSyncSkustockStrategyResult::getCpCStoreId).collect(toList()).stream().distinct().collect(toList());
//            }
//            //过滤出所有的逻辑仓并去重
//            List<Long> allStoreIds = stCSyncSkustockStrategyDOS.stream().map(StCSyncSkustockStrategyResult::getCpCStoreId).collect(toList()).stream().distinct().collect(toList());
//            List<StCSyncSkustockStrategyResult> stCSyncSkustockStrategyDOSNew = stCSyncSkustockStrategyDOS.stream().filter(s -> s.getPsCSkuId().equals(skuId)).collect(toList());
//            //根据传入的条码id 过滤出存在条码的逻辑仓id
//            isExist = stCSyncSkustockStrategyDOSNew.stream().map(StCSyncSkustockStrategyResult::getCpCStoreId).collect(toList());
//            List<Long> finalIsExist = isExist;
//            notExist=allStoreIds.stream().filter(item -> !finalIsExist.contains(item)).collect(toList());
//        }
//        if (log.isDebugEnabled()) {
//            log.debug(LogUtil.format("isExist:" + notExist);
//        }
//        if (log.isDebugEnabled()) {
//            log.debug(LogUtil.format("notExist:" + notExist);
//        }
//        if (notExist.size() > 0 || isExist.size() > 0) {
//            SgSyncSkuStockResult sgSyncSkuStockResult = new SgSyncSkuStockResult();
//            sgSyncSkuStockResult.setIsExist(isExist);
//            sgSyncSkuStockResult.setNotExist(notExist);
//            vh.setMessage("查询成功！");
//            vh.setCode(ResultCode.SUCCESS);
//            vh.setData(sgSyncSkuStockResult);
//            return vh;
//        }
//        if (log.isDebugEnabled()) {
//            log.debug(LogUtil.format("店铺条码库存同步供货仓查询结果：" + vh);
//        }
//        return vh;
    }
}
