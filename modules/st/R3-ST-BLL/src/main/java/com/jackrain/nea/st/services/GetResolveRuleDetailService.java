package com.jackrain.nea.st.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.st.mapper.StCResolveRuleMapper;
import com.jackrain.nea.st.model.table.StCResolveRuleDO;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * @author: 汪聿森
 * @Date: Created in 2019-05-20 14:23
 * @Description : ${description}
 */
@Component
@Slf4j
@Transactional
public class GetResolveRuleDetailService  extends CommandAdapter {
    @Autowired
    private StCResolveRuleMapper stCResolveRuleMapper;

    @Override
    public ValueHolder execute(QuerySession querySession) throws NDSException {
        ValueHolder valueHolder = new ValueHolder();
        DefaultWebEvent event = querySession.getEvent();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(
                event.getParameterValue("param"), "yyyy-MM-dd HH:mm:ss"),
                Feature.OrderedField);

        Long objid = param.getLong("objid");

        if (objid != null && objid > 0) {
            StCResolveRuleDO mainDO = stCResolveRuleMapper.selectById(objid);
            valueHolder.put("code", 0);
            valueHolder.put("message", "查询成功");
            valueHolder.put("data", mainDO);
        } else {
            valueHolder.put("code", -1);
            valueHolder.put("message", "查询失败");
        }
        return valueHolder;
    }
}
