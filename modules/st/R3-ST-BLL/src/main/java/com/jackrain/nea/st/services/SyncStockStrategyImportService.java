//package com.jackrain.nea.st.services;
//
//import com.google.common.collect.Lists;
//import com.jackrain.nea.constants.ResultCode;
//import com.jackrain.nea.cp.api.CpCOrgChannelQueryCmd;
//import com.jackrain.nea.cp.entity.CpCOrgChannelEntity;
//import com.jackrain.nea.cp.entity.CpCOrgChannelItemEntity;
//import com.jackrain.nea.cp.request.CpCOrgChannelItemQueryRequest;
//import com.jackrain.nea.cp.request.CpCOrgChannelQueryCmdRequest;
//import com.jackrain.nea.model.util.ModelUtil;
//import com.jackrain.nea.sg.oms.api.SgChannelStoreChangeCmd;
//import com.jackrain.nea.sg.oms.model.request.SgChannelStoreChangeRequest;
//import com.jackrain.nea.st.common.StConstant;
//import com.jackrain.nea.st.config.R3OssConfig;
//import com.jackrain.nea.st.model.enums.OperationTypeEnum;
//import com.jackrain.nea.st.model.request.SyncStockStrategyChannelQueryRequest;
//import com.jackrain.nea.st.model.request.SyncStockStrategyImportRequest;
//import com.jackrain.nea.st.model.request.SyncStockStrategyQueryRequest;
//import com.jackrain.nea.st.model.table.StCOperationLogDO;
//import com.jackrain.nea.st.model.table.StCSyncStockStrategyChannelDO;
//import com.jackrain.nea.st.model.table.StCSyncStockStrategyDO;
//import com.jackrain.nea.st.utils.ExportUtil;
//import com.jackrain.nea.st.utils.RedisCacheUtil;
//import com.jackrain.nea.st.utils.RedisConstant;
//import com.jackrain.nea.st.utils.RegexUtils;
//import com.jackrain.nea.st.utils.StBeanUtils;
//import com.jackrain.nea.sys.domain.ValueHolderV14;
//import com.jackrain.nea.util.ApplicationContextHandle;
//import com.jackrain.nea.util.ValueHolder;
//import com.jackrain.nea.utils.AssertUtils;
//import com.jackrain.nea.web.face.User;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.commons.collections.CollectionUtils;
//import org.apache.commons.lang3.StringUtils;
//import org.apache.dubbo.config.annotation.Reference;
//import org.apache.poi.xssf.usermodel.XSSFWorkbook;
//import org.springframework.beans.BeanUtils;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.stereotype.Component;
//import org.springframework.transaction.annotation.Transactional;
//
//import java.math.BigDecimal;
//import java.util.*;
//import java.util.stream.Collectors;
//
///**
// * 同步库存策略导入service
// *
// * <AUTHOR>
// * @since 2020-08-18
// * create at : 2020-08-18 17:44
// */
//@Component
//@Slf4j
//public class SyncStockStrategyImportService {
//
//    @Autowired
//    private ExportUtil exportUtil;
//    @Autowired
//    private R3OssConfig r3OssConfig;
//    @Reference(group = "cp", version = "1.0")
//    private CpCOrgChannelQueryCmd orgChannelQueryCmd;
//    @Reference(group = "sg", version = "1.0")
//    private SgChannelStoreChangeCmd sgChannelStoreChangeCmd;
//    @Autowired
//    private SyncStockStrategyQueryService syncStockStrategyQueryService;
//    @Autowired
//    private SyncStockStrategyChannelService syncStockStrategyChannelService;
//    @Autowired
//    private StCOperationLogService stCOperationLogService;
//
//    /**
//     * 下载同步库存策略导入模板
//     *
//     * @param user 当前操作用户
//     * @return 结果对象
//     */
//    public ValueHolderV14<String> downloadTemp(User user) {
//        ValueHolderV14<String> vh = new ValueHolderV14<>(ResultCode.SUCCESS, "库存同步策略导入模板下载成功！");
//        String endpoint = r3OssConfig.getEndpoint();
//        String accessKeyId = r3OssConfig.getAccessKeyId();
//        String accessKeySecret = r3OssConfig.getAccessKeySecret();
//        String bucketName = r3OssConfig.getBucketName();
//        String timeout = r3OssConfig.getTimeout();
//        exportUtil.setEndpoint(endpoint);
//        exportUtil.setAccessKeyId(accessKeyId);
//        exportUtil.setAccessKeySecret(accessKeySecret);
//        exportUtil.setBucketName(bucketName);
//
//        // 拼接Excel主表sheet表头字段,生成Excel
//        XSSFWorkbook hssfWorkbook = new XSSFWorkbook();
//        if (StringUtils.isEmpty(timeout)) {
//            // 如果获取不到apllo配置参数，设置默认过期时间为30分钟
//            timeout = "1800000";
//        }
//        exportUtil.setTimeout(timeout);
//        String filePath = "OSS-Bucket/EXPORT/StCSyncStockStrategy/";
//        String[] mainNames = {"渠道仓编码", "店铺编码", "比例", "熔断值", "渠道仓优先级"};
//        String[] mustNames = {"渠道仓编码", "店铺编码", "比例", "熔断值", "渠道仓优先级"};
//        String[] orderKeys = {"channelCode", "shopCode", "stockRate", "lowStock", "channelPriority"};
//        List<String> mainList = Lists.newArrayList(mainNames);
//        List<String> mustList = Lists.newArrayList(mustNames);
//        List<String> orderKeyList = Lists.newArrayList(orderKeys);
//        exportUtil.executeSheet(hssfWorkbook, "店铺同步库存策略导入", "", mainList, mustList,
//                orderKeyList, Lists.newArrayList(), false);
//        String sdd = exportUtil.saveFileAndPutOss(hssfWorkbook, "店铺同步库存策略导入模板", user, filePath);
//        vh.setData(sdd);
//        return vh;
//    }
//
//    /**
//     * 导入同步库存策略
//     *
//     * @param importList 导入列表
//     * @param user       当前操作用户
//     * @return 结果对象
//     */
//    public ValueHolderV14 importSyncStockStrategy(List<SyncStockStrategyImportRequest> importList, User user) {
//        ValueHolderV14 v14 = new ValueHolderV14();
//        try {
//            Map<String, CpCOrgChannelEntity> channelMap = new HashMap<>(importList.size());
//            Map<String, StCSyncStockStrategyDO> syncStockStrategyMap = new HashMap<>(importList.size());
//            Map<Long, StCSyncStockStrategyDO> syncStockStrategyIdMap = new HashMap<>(importList.size());
//            // 查询和填充渠道和策略Map
//            this.fillChannelAndStrategyMap(importList, channelMap, syncStockStrategyMap, syncStockStrategyIdMap);
//            // 查询现有的同步库存策略渠道明细Map
//            Map<String, StCSyncStockStrategyChannelDO> strategyChannelMap = getStrategyChannelMap(syncStockStrategyMap);
//            // 参数校验（需求要求：整体成功或整体失败，全部校验通过后才执行后续逻辑）
//            this.checkImportData(importList, channelMap, syncStockStrategyMap, strategyChannelMap);
//            // 批量保存或更新策略渠道明细（主表不动-只更新策略渠道明细表）
//            List<StCSyncStockStrategyChannelDO> insertStrategyChannelList = new ArrayList<>();
//            List<StCSyncStockStrategyChannelDO> updateStrategyChannelList = new ArrayList<>();
//
//            // 构建保存或更新同步库存策略渠道明细集合
//            Set<Long> channelIds = new HashSet<>();
//            List<StCSyncStockStrategyChannelDO> allStrategyChannelList = new ArrayList<>();
//            Map<Long,StCSyncStockStrategyChannelDO> originalItemMap = new HashMap<>();
//            importList.forEach(im -> {
//                // 校验过后确保是一定存在的
//                StCSyncStockStrategyDO strategy = syncStockStrategyMap.get(im.getShopCode());
//                // 已存在则更新,不存在则进行创建
//                String key = strategy.getId() + "_" + im.getChannelCode();
//                if (strategyChannelMap.containsKey(key)) {
//                    StCSyncStockStrategyChannelDO strategyChannel = strategyChannelMap.get(key);
//                    StCSyncStockStrategyChannelDO targetItem = new StCSyncStockStrategyChannelDO();
//                    BeanUtils.copyProperties(strategyChannel, targetItem);
//                    originalItemMap.put(targetItem.getId(), targetItem);
//                    strategyChannel.setLowStock(im.getLowStock());
//                    strategyChannel.setStockRate(im.getStockRate());
//                    strategyChannel.setChannelPriority(im.getChannelPriority());
//                    strategyChannel.setCpCShopId(strategy.getCpCShopId());
//                    strategyChannel.setCpCShopEcode(strategy.getCpCShopEcode());
//                    strategyChannel.setCpCShopTitle(strategy.getCpCShopTitle());
//                    StBeanUtils.makeModifierField(strategyChannel, user);
//                    updateStrategyChannelList.add(strategyChannel);
//                    channelIds.add(strategyChannel.getCpCOrgChannelId());
//                    allStrategyChannelList.add(strategyChannel);
//                    RedisCacheUtil.delete(strategy.getCpCShopId(), RedisConstant.SHOP_SYNC_STOCK_STRATEGY);
//                    RedisCacheUtil.deleteAll(RedisConstant.SHOP_SYNC_STOCK_STRATEGY_ALL);
//                } else {
//                    CpCOrgChannelEntity channelEntity = channelMap.get(im.getChannelCode());
//                    StCSyncStockStrategyChannelDO newStrategyChannel = new StCSyncStockStrategyChannelDO();
//                    newStrategyChannel.setId(ModelUtil.getSequence(StConstant.TAB_ST_C_SYNC_STOCK_STRATEGY_CHANNEL));
//                    newStrategyChannel.setStCSyncStockStrategyId(strategy.getId());
//                    newStrategyChannel.setCpCOrgChannelId(channelEntity.getId());
//                    newStrategyChannel.setCpCOrgChannelEcode(channelEntity.getEcode());
//                    newStrategyChannel.setCpCOrgChannelEname(channelEntity.getEname());
//                    newStrategyChannel.setStockRate(im.getStockRate());
//                    newStrategyChannel.setLowStock(im.getLowStock());
//                    newStrategyChannel.setChannelPriority(im.getChannelPriority());
//                    newStrategyChannel.setCpCShopId(strategy.getCpCShopId());
//                    newStrategyChannel.setCpCShopEcode(strategy.getCpCShopEcode());
//                    newStrategyChannel.setCpCShopTitle(strategy.getCpCShopTitle());
//                    StBeanUtils.makeCreateField(newStrategyChannel, user);
//                    insertStrategyChannelList.add(newStrategyChannel);
//                    channelIds.add(channelEntity.getId());
//                    allStrategyChannelList.add(newStrategyChannel);
//                    RedisCacheUtil.delete(strategy.getCpCShopId(), RedisConstant.SHOP_SYNC_STOCK_STRATEGY);
//                    RedisCacheUtil.deleteAll(RedisConstant.SHOP_SYNC_STOCK_STRATEGY_ALL);
//                }
//            });
//            // 构造请求变更的店铺逻辑仓关系对象
//            List<SgChannelStoreChangeRequest> channelStoreChangeRequestList = buildChannelStoreChangeRequest(
//                    syncStockStrategyIdMap, channelIds, allStrategyChannelList);
//            // 切面编程，同一个类中事务要生效不能直接调用，需要从容器中获取
//            SyncStockStrategyImportService importService = ApplicationContextHandle
//                    .getBean(SyncStockStrategyImportService.class);
//            importService.batchSaveStrategyChannel(user, insertStrategyChannelList, updateStrategyChannelList,
//                    channelStoreChangeRequestList, originalItemMap);
//            // 删除redis中平台店铺与逻辑仓关系(沟通确定之后没发现有啥地方使用，暂不考虑)
//        } catch (Exception e) {
//            v14.setCode(ResultCode.FAIL);
//            v14.setMessage("导入失败：" + e.getMessage());
//        }
//        return v14;
//    }
//
//    @Transactional(rollbackFor = Exception.class)
//    public void batchSaveStrategyChannel(User user, List<StCSyncStockStrategyChannelDO> insertStrategyChannelList,
//                                         List<StCSyncStockStrategyChannelDO> updateStrategyChannelList,
//                                         List<SgChannelStoreChangeRequest> channelStoreChangeRequestList,
//                                         Map<Long,StCSyncStockStrategyChannelDO> originalItemMap) {
//        if (CollectionUtils.isNotEmpty(insertStrategyChannelList)) {
//            boolean isOk = syncStockStrategyChannelService.saveBatch(insertStrategyChannelList);
//            AssertUtils.isTrue(isOk, "保存店铺同步库存策略渠道明细失败！");
//        }
//        if (CollectionUtils.isNotEmpty(updateStrategyChannelList)) {
//            boolean isOk = syncStockStrategyChannelService.updateBatchById(updateStrategyChannelList);
//            AssertUtils.isTrue(isOk, "更新店铺同步库存策略渠道明细失败！");
//        }
//
//        SyncStockStrategyImportService importService = ApplicationContextHandle
//                .getBean(SyncStockStrategyImportService.class);
//        importService.recordChangeLog(originalItemMap, updateStrategyChannelList, insertStrategyChannelList, user);
//
//        //  如果去到仓下存在逻辑仓则，需要同步店铺逻辑仓关系
//        if (CollectionUtils.isNotEmpty(channelStoreChangeRequestList)) {
//            // 批量同步SG-》渠道逻辑仓关系 ()
//            ValueHolderV14 sgRpcVh = sgChannelStoreChangeCmd.saveSbGChannelStoreList(channelStoreChangeRequestList, user);
//            AssertUtils.isTrue(sgRpcVh.isOK(), "调用SG-RPC接口更新店铺逻辑仓关系失败：" + sgRpcVh.getMessage() + "!");
//        }
//    }
//
//    /**
//     * 记录操作日志
//     * @param originalItemMap 原明细map
//     * @param updateStrategyChannelList 更新的list
//     * @param insertStrategyChannelList 插入的list
//     * @param user
//     */
//    @Transactional(rollbackFor = Exception.class)
//    public void recordChangeLog(Map<Long,StCSyncStockStrategyChannelDO> originalItemMap,
//                                List<StCSyncStockStrategyChannelDO> updateStrategyChannelList,
//                                List<StCSyncStockStrategyChannelDO> insertStrategyChannelList,
//                                User user){
//
//        List<StCOperationLogDO> allList = new ArrayList<>();
//        insertStrategyChannelList.forEach(item->{
//            StCOperationLogDO stCOperationLogDO = new StCOperationLogDO();
//            stCOperationLogDO.setId(ModelUtil.getSequence(StConstant.TAB_ST_C_OPERATION_LOG));
//            stCOperationLogDO.setAfterData(item.getCpCOrgChannelEname());
//            stCOperationLogDO.setOperationType(OperationTypeEnum.ADD.getOperationName());
//            stCOperationLogDO.setTableName("ST_C_SYNC_STOCK_STRATEGY_CHANNEL");
//            stCOperationLogDO.setUpdateId(item.getStCSyncStockStrategyId());
//            stCOperationLogDO.setUpdateModelName("供货渠道");
//            stCOperationLogDO.setModContent("渠道仓");
//            StBeanUtils.makeCreateField(stCOperationLogDO, user);
//            allList.add(stCOperationLogDO);
//        });
//
//        updateStrategyChannelList.forEach(item->{
//            StCSyncStockStrategyChannelDO originalItem = originalItemMap.get(item.getId());
//            if (item.getStockRate().compareTo(originalItem.getStockRate()) != 0) {
//                StCOperationLogDO stCOperationLogDO = new StCOperationLogDO();
//                stCOperationLogDO.setUpdateId(originalItem.getStCSyncStockStrategyId());
//                stCOperationLogDO.setBeforeData(String.valueOf(originalItem.getStockRate()));
//                stCOperationLogDO.setAfterData(String.valueOf(item.getStockRate()));
//                stCOperationLogDO.setModContent("[渠道仓-"+item.getCpCOrgChannelEname()+"]:比例");
//                buildDefaultLog(stCOperationLogDO,user);
//                allList.add(stCOperationLogDO);
//            }
//            if (item.getLowStock().compareTo(originalItem.getLowStock()) != 0) {
//                StCOperationLogDO stCOperationLogDO = new StCOperationLogDO();
//                stCOperationLogDO.setUpdateId(originalItem.getStCSyncStockStrategyId());
//                stCOperationLogDO.setBeforeData(String.valueOf(originalItem.getLowStock()));
//                stCOperationLogDO.setAfterData(String.valueOf(item.getLowStock()));
//                stCOperationLogDO.setModContent("[渠道仓-"+item.getCpCOrgChannelEname()+"]:熔断值");
//                buildDefaultLog(stCOperationLogDO,user);
//                allList.add(stCOperationLogDO);
//            }
//            if (!item.getChannelPriority().equals(originalItem.getChannelPriority())) {
//                StCOperationLogDO stCOperationLogDO = new StCOperationLogDO();
//                stCOperationLogDO.setUpdateId(originalItem.getStCSyncStockStrategyId());
//                stCOperationLogDO.setBeforeData(String.valueOf(originalItem.getChannelPriority()));
//                stCOperationLogDO.setAfterData(String.valueOf(item.getChannelPriority()));
//                stCOperationLogDO.setModContent("[渠道仓-"+item.getCpCOrgChannelEname()+"]:渠道仓优先级");
//                buildDefaultLog(stCOperationLogDO,user);
//                allList.add(stCOperationLogDO);
//            }
//        });
//        stCOperationLogService.saveBatch(allList, 500);
//    }
//
//    private void buildDefaultLog(StCOperationLogDO stCOperationLogDO,User user){
//        StBeanUtils.makeCreateField(stCOperationLogDO, user);
//        stCOperationLogDO.setId(ModelUtil.getSequence(StConstant.TAB_ST_C_OPERATION_LOG));
//        stCOperationLogDO.setOperationType(OperationTypeEnum.MOD.getOperationName());
//        stCOperationLogDO.setTableName("ST_C_SYNC_STOCK_STRATEGY_CHANNEL");
//        stCOperationLogDO.setUpdateModelName("供货渠道");
//    }
//
//    /**
//     * 根据导入数据查询填充渠道和策略Map，供后续校验使用
//     *
//     * @param importList             导入数据集合
//     * @param channelMap             渠道信息Map
//     * @param syncStockStrategyMap   同步策略信息Map
//     * @param syncStockStrategyIdMap 同步策略信息ID-MAP
//     */
//    private void fillChannelAndStrategyMap(List<SyncStockStrategyImportRequest> importList,
//                                           Map<String, CpCOrgChannelEntity> channelMap,
//                                           Map<String, StCSyncStockStrategyDO> syncStockStrategyMap,
//                                           Map<Long, StCSyncStockStrategyDO> syncStockStrategyIdMap) {
//        // 提取渠道编码和店铺编码
//        Set<String> channelCodes = new HashSet<>();
//        Set<String> shopCodes = new HashSet<>();
//        importList.forEach(im -> {
//            if (StringUtils.isNotBlank(im.getChannelCode())) {
//                channelCodes.add(im.getChannelCode());
//            }
//            if (StringUtils.isNotBlank(im.getShopCode())) {
//                shopCodes.add(im.getShopCode());
//            }
//        });
//        AssertUtils.notEmpty(channelCodes, "渠道编码全部为空！");
//        AssertUtils.notEmpty(shopCodes, "店铺编码全部为空！");
//
//        // 查询渠道仓档案
//        CpCOrgChannelQueryCmdRequest channelQueryReq = new CpCOrgChannelQueryCmdRequest();
//        channelQueryReq.setIsactive(StConstant.ISACTIVE_Y);
//        channelQueryReq.setEcodeList(new ArrayList<>(channelCodes));
//        ValueHolder channelVH = orgChannelQueryCmd.getChannel(channelQueryReq);
//        AssertUtils.isTrue(channelVH.isOK(), "调用渠道仓RPC接口查询渠道信息失败：" + channelVH.get("message"));
//        List<CpCOrgChannelEntity> channelList = (List<CpCOrgChannelEntity>) channelVH.get("data");
//        if (CollectionUtils.isNotEmpty(channelList)) {
//            channelMap.putAll(channelList.stream().collect(Collectors.toMap(CpCOrgChannelEntity::getEcode,
//                    v -> v, (v1, v2) -> v1)));
//        }
//
//        // 查询店铺库存同步策略
//        SyncStockStrategyQueryRequest strategyQueryRequest = new SyncStockStrategyQueryRequest();
//        strategyQueryRequest.setIsactive(StConstant.ISACTIVE_Y);
//        strategyQueryRequest.setShopCodes(new ArrayList<>(shopCodes));
//        List<StCSyncStockStrategyDO> strategyList = syncStockStrategyQueryService
//                .querySyncStockStrategy(strategyQueryRequest);
//        if (CollectionUtils.isNotEmpty(strategyList)) {
//            syncStockStrategyMap.putAll(strategyList.stream().collect(
//                    Collectors.toMap(StCSyncStockStrategyDO::getCpCShopEcode, v -> v, (v1, v2) -> v1)));
//            syncStockStrategyIdMap.putAll(strategyList.stream().collect(
//                    Collectors.toMap(StCSyncStockStrategyDO::getId, v -> v, (v1, v2) -> v1)));
//        }
//    }
//
//    /**
//     * 导入数据校验
//     *
//     * @param importList           导入数据集合
//     * @param channelMap           渠道信息Map
//     * @param syncStockStrategyMap 同步策略信息Map
//     * @param strategyChannelMap 同步策略明细Map
//     */
//    private void checkImportData(List<SyncStockStrategyImportRequest> importList,
//                                 Map<String, CpCOrgChannelEntity> channelMap,
//                                 Map<String, StCSyncStockStrategyDO> syncStockStrategyMap,
//                                 Map<String, StCSyncStockStrategyChannelDO> strategyChannelMap) {
//        int i = 0;
//        StringBuffer allError = new StringBuffer("");
//        Collection<StCSyncStockStrategyChannelDO> ChannelDOs = strategyChannelMap.values();
//
//        // 用于判断导入的店铺编码+渠道仓优先级是否重复
//        Map<String,String> itemExistMap = new HashMap<>(importList.size());
//
//        // 原优先级map,key 优先级,Val id
//        Map<Integer, Long> originPriorityMap = ChannelDOs.stream().filter(item -> item.getChannelPriority() != null)
//                .collect(Collectors.toMap(StCSyncStockStrategyChannelDO::getChannelPriority, StCSyncStockStrategyChannelDO::getId, (key1, key2) -> key2));
//
//        // 传入的记录若为更新则id赋值
//        importList.forEach(item -> {
//            if(Objects.nonNull(item.getChannelCode()) && Objects.nonNull(item.getShopCode())) {
//                Optional<StCSyncStockStrategyChannelDO> first = ChannelDOs.stream()
//                        .filter(originItem -> item.getChannelCode()
//                                .equals(originItem.getCpCOrgChannelEcode()) && item.getShopCode().equals(originItem.getCpCShopEcode()))
//                        .findFirst();
//                if (first.isPresent()) {
//                    item.setId(first.get().getId());
//                }
//            }
//        });
//
//        // 传入优先级map,key 优先级,Val id
//        Map<Long, Integer> newPriorityMap = importList.stream().filter(item -> item.getChannelPriority() != null)
//                .collect(Collectors.toMap(SyncStockStrategyImportRequest::getId, SyncStockStrategyImportRequest::getChannelPriority, (key1, key2) -> key2));
//
//        for (SyncStockStrategyImportRequest syncReq : importList) {
//            StringBuffer sb = new StringBuffer();
//            i++;
//            if (StringUtils.isBlank(syncReq.getChannelCode())) {
//                sb.append("[渠道编码为空!]");
//            } else if (!channelMap.containsKey(syncReq.getChannelCode())) {
//                sb.append("[渠道编码查询信息不存在或已作废!]");
//            }
//            if (StringUtils.isBlank(syncReq.getShopCode())) {
//                sb.append("[店铺编码为空!]");
//            } else if (!syncStockStrategyMap.containsKey(syncReq.getShopCode())) {
//                sb.append("[店铺库存同步策略不存在或已作废!]");
//            }
//            if (syncReq.getStockRate() == null) {
//                sb.append("[比例为空！]");
//            } else if (syncReq.getStockRate().compareTo(BigDecimal.ZERO) < 0 ||
//                    syncReq.getStockRate().compareTo(BigDecimal.valueOf(100)) > 0) {
//                sb.append("[比例区间必须为：0~100！]");
//            }
//            if (syncReq.getLowStock() == null) {
//                sb.append("[熔断值为空！]");
//            } else if (syncReq.getLowStock().compareTo(BigDecimal.ZERO) < 0) {
//                sb.append("[熔断值不能小于0！]");
//            }
//            if (!RegexUtils.isPureDigital(String.valueOf(syncReq.getChannelPriority()))) {
//                sb.append("[渠道仓优先级必须为正整数！]");
//            }
//
//            StCSyncStockStrategyDO stCSyncStockStrategyDO = syncStockStrategyMap.get(syncReq.getShopCode());
//            List<Integer> existChannelPriorityList = ChannelDOs.stream().
//                    filter(item -> item.getStCSyncStockStrategyId().equals(stCSyncStockStrategyDO.getId()) &&
//                            !item.getCpCOrgChannelEcode().equals(syncReq.getChannelCode()))
//                    .map(StCSyncStockStrategyChannelDO::getChannelPriority)
//                    .filter(Objects::nonNull)
//                    .collect(Collectors.toList());
//
//            Integer channelPriority = syncReq.getChannelPriority();
//
//            // 同一个店铺，原渠道仓A 优先级1，原渠道仓B 优先级2，更新为渠道仓A 优先级2，原渠道仓B 优先级1，需要做特殊处理
//            if (existChannelPriorityList.contains(channelPriority) &&
//                    (newPriorityMap.get(originPriorityMap.get(channelPriority)) == null ||
//                            channelPriority.equals(newPriorityMap.get(originPriorityMap.get(channelPriority)))) ||
//                    itemExistMap.containsKey(syncReq.getShopCode() + channelPriority)) {
//                sb.append("[同一个策略内渠道仓优先级不能相等！]");
//            }
//
//            itemExistMap.put(syncReq.getShopCode() + channelPriority, "true");
//
//            if (sb.length() > 1) {
//                sb.insert(0, "数据行第" + i + "行：");
//                sb.append(";");
//                allError.append(sb.toString());
//            }
//        }
//        AssertUtils.notEmpty(importList, "导入同步库存策略数据为空！");
//        if (allError.length() > 1) {
//            AssertUtils.logAndThrow(allError.toString());
//        }
//    }
//
//    /**
//     * 获取同步策略渠道明细Map
//     *
//     * @param syncStockStrategyMap 同步库存策略Map
//     * @return 同步策略渠道明细Map
//     */
//    private Map<String, StCSyncStockStrategyChannelDO> getStrategyChannelMap(
//            Map<String, StCSyncStockStrategyDO> syncStockStrategyMap) {
//        List<Long> strategyIds = syncStockStrategyMap.values().stream().map(f -> f.getId())
//                .collect(Collectors.toList());
//        // 查询同步库存策略渠道明细
//        SyncStockStrategyChannelQueryRequest queryRequest = new SyncStockStrategyChannelQueryRequest();
//        queryRequest.setSyncStockStrategyIds(strategyIds);
//        queryRequest.setIsactive(StConstant.ISACTIVE_Y);
//        List<StCSyncStockStrategyChannelDO> strategyChannelList =
//                syncStockStrategyChannelService.getSyncStockStrategyChannel(queryRequest);
//        Map<String, StCSyncStockStrategyChannelDO> strategyChannelMap = new HashMap<>();
//        if (CollectionUtils.isNotEmpty(strategyChannelList)) {
//            strategyChannelMap.putAll(strategyChannelList.stream().collect(
//                    Collectors.toMap(k -> k.getStCSyncStockStrategyId() + "_" + k.getCpCOrgChannelEcode(),
//                            v -> v, (v1, v2) -> v1)));
//        }
//        return strategyChannelMap;
//    }
//
//    /**
//     * 构建店铺逻辑仓关系请求
//     *
//     * @param syncStockStrategyIdMap 同步库存策略Map
//     * @param channelIds             渠道id集合
//     * @param allStrategyChannelList 所有同步策略渠道明细
//     * @return 店铺逻辑仓关系更新请求对象
//     */
//    private List<SgChannelStoreChangeRequest> buildChannelStoreChangeRequest(
//            Map<Long, StCSyncStockStrategyDO> syncStockStrategyIdMap,
//            Set<Long> channelIds, List<StCSyncStockStrategyChannelDO> allStrategyChannelList) {
//        // 获取渠道明细Map
//        CpCOrgChannelItemQueryRequest request = new CpCOrgChannelItemQueryRequest();
//        request.setIsactive(StConstant.ISACTIVE_Y);
//        request.setChannelIds(new ArrayList<>(channelIds));
//        ValueHolder channelItemVH = orgChannelQueryCmd.getChannelItems(request);
//        AssertUtils.isTrue(channelItemVH.isOK(), "查询渠道仓档案明细失败！");
//        List<CpCOrgChannelItemEntity> channelItems = (List<CpCOrgChannelItemEntity>) channelItemVH.get("data");
//        Map<Long, List<CpCOrgChannelItemEntity>> channelItemMap = new HashMap<>();
//        if (CollectionUtils.isNotEmpty(channelItems)) {
//            channelItems.forEach(channelItem -> {
//                if (channelItemMap.containsKey(channelItem.getCpCOrgChannelId())) {
//                    List<CpCOrgChannelItemEntity> items = channelItemMap.get(channelItem.getCpCOrgChannelId());
//                    items.add(channelItem);
//                } else {
//                    List<CpCOrgChannelItemEntity> items = new ArrayList<>();
//                    items.add(channelItem);
//                    channelItemMap.put(channelItem.getCpCOrgChannelId(), items);
//                }
//            });
//        }
//        // 调用SG的RPC接口更新店铺逻辑仓关系请求对象
//        List<SgChannelStoreChangeRequest> channelStoreChangeRequestList = new ArrayList<>();
//        allStrategyChannelList.forEach(sc -> {
//            List<CpCOrgChannelItemEntity> tempChannelItems = channelItemMap.get(sc.getCpCOrgChannelId());
//            StCSyncStockStrategyDO strategyDO = syncStockStrategyIdMap.get(sc.getStCSyncStockStrategyId());
//            if (CollectionUtils.isNotEmpty(tempChannelItems)) {
//                tempChannelItems.forEach(tci -> {
//                    SgChannelStoreChangeRequest storeChange = new SgChannelStoreChangeRequest();
//                    storeChange.setCpCShopId(strategyDO.getCpCShopId());
//                    storeChange.setCpCShopEcode(strategyDO.getCpCShopEcode());
//                    storeChange.setCpCShopTitle(strategyDO.getCpCShopTitle());
//                    storeChange.setCpCStoreId(tci.getCpCStoreId());
//                    storeChange.setCpCStoreEcode(tci.getCpCStoreEcode());
//                    storeChange.setCpCStoreEname(tci.getCpCStoreEname());
//                    storeChange.setPriority(tci.getSupplyPriority());
//                    storeChange.setRate(sc.getStockRate());
//                    if (sc.getLowStock() != null) {
//                        storeChange.setLowStock(sc.getLowStock());
//                    }
//                    storeChange.setIsSend(StConstant.ISACTIVE_Y.equals(tci.getIsactive()) ? 1 : 0);
//                    storeChange.setCpCChannelId(tci.getCpCOrgChannelId());
//                    storeChange.setCpCChannelEcode(sc.getCpCOrgChannelEcode());
//                    storeChange.setCpCChannelEname(sc.getCpCOrgChannelEname());
//                    channelStoreChangeRequestList.add(storeChange);
//                });
//            }
//        });
//        return channelStoreChangeRequestList;
//    }
//}
