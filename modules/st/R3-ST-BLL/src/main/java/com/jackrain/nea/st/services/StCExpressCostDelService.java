package com.jackrain.nea.st.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.st.annotation.StOperationLog;
import com.jackrain.nea.st.common.StConstant;
import com.jackrain.nea.st.mapper.StCExpressCostItemMapper;
import com.jackrain.nea.st.mapper.StCExpressCostMapper;
import com.jackrain.nea.st.model.enums.CloseStatusEnum;
import com.jackrain.nea.st.model.enums.SubmitStatusEnum;
import com.jackrain.nea.st.model.table.StCExpressCost;
import com.jackrain.nea.st.model.table.StCExpressCostItem;
import com.jackrain.nea.st.utils.ValueHolderUtils;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2022/8/8 10:59
 * @Description
 */
@Component
@Slf4j
public class StCExpressCostDelService extends CommandAdapter {
    @Autowired
    private StCExpressCostMapper stCExpressCostMapper;

    @Autowired
    private StCExpressCostItemMapper stCExpressCostItemMapper;

    @StOperationLog(operationType = "DEL", mainTableName = "ST_C_EXPRESS_COST", itemsTableName = "ST_C_EXPRESS_COST_ITEM")
    @Override
    public ValueHolder execute(QuerySession querySession) throws NDSException {
        DefaultWebEvent event = querySession.getEvent();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(
                event.getParameterValue("param"), "yyyy-MM-dd HH:mm:ss"), Feature.OrderedField);
        String isDel = param.getString("isdelmtable");
        Long objid = param.getLong("objid");
        //判断主表是否存在
        StCExpressCost expressCost = stCExpressCostMapper.selectById(objid);
        if (expressCost == null){
            return ValueHolderUtils.getFailValueHolder("当前记录不存在！");
        }
        //判断是主表删除还是明细删除
        if (StConstant.FALSE_STR.equals(isDel)){
            //单独删除明细
            //主表ID
            if (CloseStatusEnum.CLOSE.getKey().equals(expressCost.getCloseStatus())){
                return ValueHolderUtils.getFailValueHolder("当前记录已结案，不允许删除！");
            }
            if (SubmitStatusEnum.SUBMIT.getKey().equals(expressCost.getStatus())){
                return ValueHolderUtils.getFailValueHolder("当前记录已提交，不允许删除！");
            }
            JSONObject tabitem = param.getJSONObject("tabitem");
            JSONArray expressCostItemArray = tabitem.getJSONArray(StConstant.TAB_ST_C_EXPRESS_COST_ITEM);
            if (expressCostItemArray != null && expressCostItemArray.size() > 0) {
                return delExpressCostItemArray(expressCostItemArray, querySession);
            }else {
                return ValueHolderUtils.getFailValueHolder("请选择需要删除的明细数据！");
            }
        }else {
            if (!SubmitStatusEnum.NO_SUBMIT.getKey().equals(expressCost.getStatus())){
                return ValueHolderUtils.getFailValueHolder("当前单据状态，不允许删除！");
            }
            return delExpressCost(objid,querySession);
        }
    }

    private ValueHolder delExpressCost(Long objid, QuerySession querySession) {
        //删除明
        stCExpressCostItemMapper.delete(new LambdaQueryWrapper<StCExpressCostItem>().in(StCExpressCostItem::getStCExpressCostId,objid));
        //删除主表
        if (stCExpressCostMapper.deleteById(objid) <= 0){
            return ValueHolderUtils.getFailValueHolder("当前记录不存在！");
        }
        return ValueHolderUtils.getDeleteSuccessValueHolder();
    }

    private ValueHolder delExpressCostItemArray(JSONArray unfullcarCostItemArray, QuerySession querySession) {
        List<Long> ids = JSONObject.parseArray(unfullcarCostItemArray.toJSONString(), Long.class);
        if (!CollectionUtils.isEmpty(ids)) {
            List<StCExpressCostItem> itemList = stCExpressCostItemMapper.selectBatchIds(ids);
            if (!CollectionUtils.isEmpty(itemList)) {
                Map<Long, String> beforeDelObjMap = new HashMap<>();
                for (StCExpressCostItem item : itemList) {
                    beforeDelObjMap.put(item.getId(), JSON.toJSONString(item));
                }
                querySession.setAttribute("beforeDelObjMap", beforeDelObjMap);
            }
        }
        for (int i = 0; i < unfullcarCostItemArray.size(); i++) {
            Long slaverId = unfullcarCostItemArray.getLong(i);
            int deleteCount = stCExpressCostItemMapper.deleteById(slaverId);
            if (deleteCount <= 0) {
                return ValueHolderUtils.getFailValueHolder("零担费用明细已不存在！");
            }
        }
        return ValueHolderUtils.getDeleteSuccessValueHolder();
    }

}
