package com.jackrain.nea.st.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.st.common.StConstant;
import com.jackrain.nea.st.mapper.CpCPhyWarehouseMapper;
import com.jackrain.nea.st.mapper.RegionQueryMapper;
import com.jackrain.nea.st.mapper.StCPreOccupyWarehousePriorityMapper;
import com.jackrain.nea.st.model.table.CpCPhyWarehouseDO;
import com.jackrain.nea.st.model.table.CpCRegion;
import com.jackrain.nea.st.model.table.StCPreOccupyWarehousePriority;
import com.jackrain.nea.st.utils.JsonUtils;
import com.jackrain.nea.st.utils.StBeanUtils;
import com.jackrain.nea.st.utils.ValueHolderUtils;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @ClassName StCPreOccupyWarehousePriorityService
 * @Description 订单预寻源
 * <AUTHOR>
 * @Date 2025/2/27 09:12
 * @Version 1.0
 */
@Component
@Slf4j
public class StCPreOccupyWarehousePriorityService {

    @Autowired
    private StCPreOccupyWarehousePriorityMapper stCPreOccupyWarehousePriorityMapper;
    @Autowired
    private RegionQueryMapper regionQueryMapper;
    @Autowired
    private CpCPhyWarehouseMapper cpCPhyWarehouseMapper;


    public ValueHolder saveOrUpdate(QuerySession session) {
        DefaultWebEvent event = session.getEvent();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(
                        event.getParameterValue("param"), "yyyy-MM-dd HH:mm:ss", SerializerFeature.WriteMapNullValue),
                Feature.OrderedField);
        if (param != null) {
            Long id = param.getLong("objid");
            JSONObject fixColumn = param.getJSONObject("fixcolumn");
            JSONObject requestParam = fixColumn.getJSONObject("ST_C_PRE_OCCUPY_WAREHOUSE_PRIORITY");
            StCPreOccupyWarehousePriority stCPreOccupyWarehousePriority = JsonUtils.jsonParseClass(requestParam, StCPreOccupyWarehousePriority.class);
            if (fixColumn != null && id != null) {
                if (id != -1) {
                    return ValueHolderUtils.getSuccessValueHolder("success");
                } else {
                    return insert(stCPreOccupyWarehousePriority, session.getUser());
                }
            }
        }
        return null;
    }

    public ValueHolder delete(QuerySession session) {
        DefaultWebEvent event = session.getEvent();
        User user = session.getUser();
        String ids = String.valueOf(event.getData().get("OBJIDS"));
        log.info("删除订单预寻源：{}", ids);
        if (StringUtils.isEmpty(ids)) {
            return ValueHolderUtils.getFailValueHolder("请选择需要删除的订单预寻源");
        }
        for (String id : ids.split(",")) {
            Long objid = Long.valueOf(id);
            StCPreOccupyWarehousePriority update = new StCPreOccupyWarehousePriority();
            update.setId(objid);
            update.setIsactive("N");
            StBeanUtils.makeModifierField(update, user);
            stCPreOccupyWarehousePriorityMapper.updateById(update);
        }
        return ValueHolderUtils.getSuccessValueHolder("success");
    }

    private ValueHolder insert(StCPreOccupyWarehousePriority stCPreOccupyWarehousePriority, User user) {
        Long warehouseId = stCPreOccupyWarehousePriority.getCpCPhyWarehouseId();
        // 根据省份id 查询数据库
        StCPreOccupyWarehousePriority stCPreOccupyWarehousePriorityDB = stCPreOccupyWarehousePriorityMapper.selectByCpCWarehouseId(warehouseId);
        if (stCPreOccupyWarehousePriorityDB != null) {
            return ValueHolderUtils.getFailValueHolder("发货仓已存在，禁止新增！");
        }
        CpCPhyWarehouseDO cpCPhyWarehouseDO = cpCPhyWarehouseMapper.selectById(warehouseId);
        if (cpCPhyWarehouseDO == null) {
            return ValueHolderUtils.getFailValueHolder("发货仓库不存在，禁止新增！");
        }
        Long cpCProvinceId = stCPreOccupyWarehousePriority.getCpCProvinceId();
        // 根据省份id 获取省份信息
        CpCRegion cpCRegion = regionQueryMapper.selectById(cpCProvinceId);
        if (cpCRegion == null) {
            return ValueHolderUtils.getFailValueHolder("收货省不存在，禁止新增！");
        }
        stCPreOccupyWarehousePriority.setCpCProvinceId(cpCRegion.getId());
        stCPreOccupyWarehousePriority.setCpCProvinceName(cpCRegion.getEname());
        stCPreOccupyWarehousePriority.setCpCProvinceCode(cpCRegion.getEcode());

        stCPreOccupyWarehousePriority.setCpCPhyWarehouseId(cpCPhyWarehouseDO.getId());
        stCPreOccupyWarehousePriority.setCpCPhyWarehouseEcode(cpCPhyWarehouseDO.getEcode());
        stCPreOccupyWarehousePriority.setCpCPhyWarehouseEname(cpCPhyWarehouseDO.getEname());
        Long id = ModelUtil.getSequence(StConstant.ST_C_PRE_OCCUPY_WAREHOUSE_PRIORITY);
        stCPreOccupyWarehousePriority.setId(id);
        StBeanUtils.makeCreateField(stCPreOccupyWarehousePriority, user);
        stCPreOccupyWarehousePriorityMapper.insert(stCPreOccupyWarehousePriority);
        return ValueHolderUtils.getSuccessValueHolder(id, StConstant.ST_C_PRE_OCCUPY_WAREHOUSE_PRIORITY, "");
    }

    public ValueHolderV14<StCPreOccupyWarehousePriority> queryByWarehouseEcode(String warehouseEcode) {
        ValueHolderV14<StCPreOccupyWarehousePriority> result = new ValueHolderV14<>();
        StCPreOccupyWarehousePriority stCPreOccupyWarehousePriority = stCPreOccupyWarehousePriorityMapper.selectByCpCWarehouseEcode(warehouseEcode);
        if (stCPreOccupyWarehousePriority != null) {
            result.setCode(ResultCode.SUCCESS);
            result.setMessage("查询成功");
            result.setData(stCPreOccupyWarehousePriority);
            return result;
        }
        result.setCode(ResultCode.FAIL);
        result.setMessage("查询不到对应策略！");
        return result;
    }
}
