package com.jackrain.nea.st.services;

import com.jackrain.nea.st.mapper.StCVipcomMailMapper;
import com.jackrain.nea.st.model.result.MailInfoResult;
import com.jackrain.nea.st.model.table.StCVipcomMailDO;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.utility.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;
@Component
@Slf4j
public class VipcomMailFindSenderService {
    @Autowired
    private StCVipcomMailMapper stCVipcomMailMapper;

    public ValueHolderV14<MailInfoResult> querySenderByNodeAndShop(Integer taskNode, Long shopId, Long wareHouseId){
        ValueHolderV14 vh = new ValueHolderV14();

        if(taskNode == null){
            vh.setCode(-1);
            vh.setMessage("节点不能为空！");
            log.debug(LogUtil.format("节点不能为空！"));
            return vh;
        }
        if(shopId == null){
            vh.setCode(-1);
            vh.setMessage("店铺ID不能为空！");
            log.debug(LogUtil.format("店铺ID不能为空！"));
            return vh;
        }
        List<StCVipcomMailDO> stCVipcomMailDOS = stCVipcomMailMapper.selectByNodeAndShop(taskNode,shopId,wareHouseId);
        if (CollectionUtils.isEmpty(stCVipcomMailDOS)){
            vh.setCode(-1);
            vh.setMessage("邮件配置为空！");
            log.debug(LogUtil.format("邮件配置为空！"));
            return vh;
        }

        MailInfoResult mailInfoResult = new MailInfoResult();
        mailInfoResult.setStCVipcomMailDOList(stCVipcomMailDOS);
        vh.setData(mailInfoResult);

        return vh;
    }
}
