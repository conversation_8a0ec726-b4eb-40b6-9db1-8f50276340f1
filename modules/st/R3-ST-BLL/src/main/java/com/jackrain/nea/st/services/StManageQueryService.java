package com.jackrain.nea.st.services;

import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.st.mapper.AcFManageMapper;
import com.jackrain.nea.st.model.table.AcFManageDO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @author:洪艺安
 * @since: 2019/7/18
 * @create at : 2019/7/18 20:22
 */
@Component
@Slf4j
public class StManageQueryService {
    @Autowired
    private AcFManageMapper mapper;

    public AcFManageDO queryManagerById(Long manageId) throws NDSException {
        return mapper.selectById(manageId);
    }
}
