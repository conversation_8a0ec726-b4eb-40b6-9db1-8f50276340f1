package com.jackrain.nea.st.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.oc.oms.model.enums.IsActiveEnum;
import com.jackrain.nea.st.common.StConstant;
import com.jackrain.nea.st.mapper.StCVipcomCooperationNoMapper;
import com.jackrain.nea.st.model.table.StCVipcomCooperationNo;
import com.jackrain.nea.st.utils.StBeanUtils;
import com.jackrain.nea.st.utils.ValueHolderUtils;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.HashMap;

/**
 * description：常态合作编码保存
 *
 * <AUTHOR>
 * @date 2021/6/30
 */
@Component
@Slf4j
@Transactional
public class VipcomCooperationNoSaveService extends CommandAdapter {
    @Autowired
    private StCVipcomCooperationNoMapper vipcomCooperationNoMapper;


    @Override
    public ValueHolder execute(QuerySession session) {
        DefaultWebEvent event = session.getEvent();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(
                event.getParameterValue("param"), "yyyy-MM-dd HH:mm:ss", SerializerFeature.WriteMapNullValue),
                Feature.OrderedField);
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("Start VipcomCooperationNoSaveService.execute. ReceiveParams: {}"), param);
        }
        if (param == null) {
            return ValueHolderUtils.getFailValueHolder("参数为空!");
        }
        //1.拉取请求参数，解析
        Long id = param.getLong("objid");//获取objid参数
        JSONObject fixColumn = param.getJSONObject("fixcolumn");//获取表数据
        StCVipcomCooperationNo stCVipcomCooperationNo = JSONObject.parseObject(fixColumn.getString("ST_C_VIPCOM_COOPERATION_NO"), StCVipcomCooperationNo.class);
        if (stCVipcomCooperationNo == null) {
            return ValueHolderUtils.getFailValueHolder("参数异常!");
        }
        if (id != -1) {
            return this.updateVipcomCooperationNo(session, id, stCVipcomCooperationNo);
        } else {
            return this.insertVipcomCooperationNo(session, stCVipcomCooperationNo);
        }
    }

    /**
     * description：更新数据
     *
     * <AUTHOR>
     * @date 2021/6/30
     */
    public ValueHolder updateVipcomCooperationNo(QuerySession session, Long id, StCVipcomCooperationNo project) {
        if (project != null) {
            if (project.getId() == null) {
                project.setId(id);
            }
            checkVipcomCooperationNoByFilter(id, project, "update");
            //update基础字段补全
            StBeanUtils.makeModifierField(project, session.getUser());
            if (vipcomCooperationNoMapper.updateById(project) < 0) {
                return ValueHolderUtils.getFailValueHolder("更新失败！");
            }
        }
        return ValueHolderUtils.getSuccessValueHolder(id, StConstant.TAB_ST_C_VIPCOM_COOPERATION_NO);
    }

    /**
     * description：保存数据
     *
     * <AUTHOR>
     * @date 2021/6/30
     */
    private ValueHolder insertVipcomCooperationNo(QuerySession session, StCVipcomCooperationNo project) {
        long id = 0;
        if (project != null) {
            checkVipcomCooperationNoByFilter(-1L, project, "insert");
            //生成主键
            id = ModelUtil.getSequence(StConstant.TAB_ST_C_VIPCOM_COOPERATION_NO);
            project.setId(id);
            StBeanUtils.makeCreateField(project, session.getUser());
            int insertResult = vipcomCooperationNoMapper.insert(project);
            if (insertResult < 0) {
                return ValueHolderUtils.getFailValueHolder("保存失败！");
            }
        }
        return ValueHolderUtils.getSuccessValueHolder(id, StConstant.TAB_ST_C_VIPCOM_COOPERATION_NO);
    }

    /**
     * 数据校验和过滤
     *
     * @param id
     * @param project
     * @param action
     */
    private void checkVipcomCooperationNoByFilter(Long id, StCVipcomCooperationNo project, String action) {
        Date beginTime = project.getSellTimeFrom();
        Date endTime = project.getSellTimeTo();
        //时间判断
        if (beginTime != null && endTime != null) {
            if (endTime.compareTo(beginTime) < 0) {
                throw new NDSException("合作编码有效结束日期不能小于合作编码有效开始日期！");
            }
        }
        switch (action.toLowerCase()) {
            case "insert":
                HashMap<String, Object> map = new HashMap<>();
                map.put("cooperation_no", project.getCooperationNo());
                map.put("isactive", IsActiveEnum.Y.getKey());
                if (CollectionUtils.isNotEmpty(vipcomCooperationNoMapper.selectByMap(map))) {
                    throw new NDSException("当前常态合作编码已存在！");
                }
                break;
            case "update":
                StCVipcomCooperationNo existsproject = vipcomCooperationNoMapper.selectById(id);
                if (existsproject == null) {
                    throw new NDSException("当前记录已不存在！");
                }
                if (!IsActiveEnum.Y.getKey().equals(existsproject.getIsactive())) {
                    throw new NDSException("当前记录已作废！");
                }
                if (existsproject.getCooperationNo().equals(project.getCooperationNo())) {
                    throw new NDSException("当前常态合作编码已存在！");
                }
                break;
        }
    }
}
