package com.jackrain.nea.st.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.jackrain.nea.cpext.model.table.CpShop;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.oc.oms.model.resources.OmsRedisKeyResources;
import com.jackrain.nea.redis.util.RedisOpsUtil;
import com.jackrain.nea.st.common.StConstant;
import com.jackrain.nea.st.mapper.StCOrderPushDelayStrategyMapper;
import com.jackrain.nea.st.model.request.StCOrderPushDelayStrategyRequest;
import com.jackrain.nea.st.model.table.StCOrderPushDelayStrategy;
import com.jackrain.nea.st.rpc.RpcCpService;
import com.jackrain.nea.st.utils.JsonUtils;
import com.jackrain.nea.st.utils.RedisCacheUtil;
import com.jackrain.nea.st.utils.RedisConstant;
import com.jackrain.nea.st.utils.StBeanUtils;
import com.jackrain.nea.st.utils.ValueHolderUtils;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;

/**
 * @Author: ganquan
 * @Date Create In 2020/7/1 15:07
 * @Description: 订单推单延时策略保存
 */
@Component
@Slf4j
public class StCOrderPushDelayStrategySaveService extends CommandAdapter {

    @Autowired
    private StCOrderPushDelayStrategyMapper stCOrderPushDelayStrategyMapper;

    @Autowired
    private RpcCpService rpcCpService;

    @Autowired
    private RedisOpsUtil<String, Object> redisUtil;

    @Override
    public ValueHolder execute(QuerySession session) {
        DefaultWebEvent event = session.getEvent();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(
                event.getParameterValue("param"), "yyyy-MM-dd HH:mm:ss", SerializerFeature.WriteMapNullValue),
                Feature.OrderedField);
        log.info(LogUtil.format("StCOrderPushDelayStrategySaveServiceParam:{}"), param);
        Long id = param.getLong("objid");
        JSONObject fixColumn = param.getJSONObject("fixcolumn");
        //获取新增时，复选框选中的值
        StCOrderPushDelayStrategyRequest stCOrderPushDelayStrategyRequest = JsonUtils.jsonParseClass(fixColumn, StCOrderPushDelayStrategyRequest.class);
        if (fixColumn != null && id != null) {
            if (id != -1) {
                return updateOrderPushDelayStrategy(session, id, stCOrderPushDelayStrategyRequest);
            } else {
                return addDistribution(session, id, stCOrderPushDelayStrategyRequest);
            }
        }
        throw new NDSException("传入数据异常！");
    }

    /**
     * <AUTHOR>
     * @Description 新增订单推单延时策略
     * @Date 15:20 2020/7/1
     * @param session
     * @param id
     * @param stCOrderPushDelayStrategyRequest
     * @return com.jackrain.nea.util.ValueHolder
     **/
    private ValueHolder addDistribution(QuerySession session, Long id, StCOrderPushDelayStrategyRequest stCOrderPushDelayStrategyRequest) {
        StCOrderPushDelayStrategy stCOrderPushDelayStrategy = stCOrderPushDelayStrategyRequest.getStCOrderPushDelayStrategy();
        if (stCOrderPushDelayStrategy == null) {
            throw new NDSException("订单推单延时策略为空！");
        }
        //主表数据业务更新校验
        checkOrderPushDelayStrategy(stCOrderPushDelayStrategy);
        stCOrderPushDelayStrategy.setId(ModelUtil.getSequence(StConstant.TAB_ST_C_ORDER_PUSH_DELAY_STRATEGY));
        //填充店铺信息
        List<Long> shopIds = Arrays.asList(stCOrderPushDelayStrategy.getCpCShopId());
        List<CpShop> cpShops = rpcCpService.queryShopByIds(shopIds);
        if (CollectionUtils.isNotEmpty(cpShops)) {
            CpShop cpShop = cpShops.get(0);
            stCOrderPushDelayStrategy.setCpCShopEcode(cpShop.getEcode());
            stCOrderPushDelayStrategy.setCpCShopEname(cpShop.getCpCShopTitle());
        }
        //主表创建信息更新
        StBeanUtils.makeCreateField(stCOrderPushDelayStrategy, session.getUser());
        //主表最后信息修改
        StBeanUtils.makeModifierField(stCOrderPushDelayStrategy, session.getUser());
        int insertResult = stCOrderPushDelayStrategyMapper.insert(stCOrderPushDelayStrategy);
        if (insertResult <= 0) {
            return ValueHolderUtils.getFailValueHolder("保存失败");
        }
        RedisCacheUtil.delete(stCOrderPushDelayStrategy.getCpCShopId(), RedisConstant.SHOP_ORDER_PUSH_DELAY);
        return ValueHolderUtils.getSuccessValueHolder(stCOrderPushDelayStrategy.getId(), StConstant.TAB_ST_C_ORDER_PUSH_DELAY_STRATEGY);
    }

    /**
     * <AUTHOR>
     * @Description 校验店铺名是否重复
     * @Date 15:26 2020/7/1
     * @param stCOrderPushDelayStrategy
     * @return void
     **/
    private void checkOrderPushDelayStrategy(StCOrderPushDelayStrategy stCOrderPushDelayStrategy) {
        Long cpCShopId = stCOrderPushDelayStrategy.getCpCShopId();
        if (cpCShopId == null) {
            throw new NDSException("订单推单延时策略中店铺不能为空！");
        }
        List<StCOrderPushDelayStrategy> stCOrderPushDelayStrategies =
                stCOrderPushDelayStrategyMapper.selectList(new QueryWrapper<StCOrderPushDelayStrategy>()
                .lambda().eq(StCOrderPushDelayStrategy::getCpCShopId, cpCShopId));
        if (CollectionUtils.isNotEmpty(stCOrderPushDelayStrategies)) {
            throw new NDSException("该店铺对应的订单推单延时策略已存在！");
        }
    }

    /**
     * <AUTHOR>
     * @Description 修改订单推单延时策略
     * @Date 15:16 2020/7/1
     * @param session
     * @param id
     * @param stCOrderPushDelayStrategyRequest
     * @return com.jackrain.nea.util.ValueHolder
     **/
    private ValueHolder updateOrderPushDelayStrategy(QuerySession session, Long id, StCOrderPushDelayStrategyRequest stCOrderPushDelayStrategyRequest) {
        StCOrderPushDelayStrategy stCOrderPushDelayStrategy = stCOrderPushDelayStrategyRequest.getStCOrderPushDelayStrategy();
        if (stCOrderPushDelayStrategy == null) {
            throw new NDSException("订单推单延时策略为空！");
        }
        StCOrderPushDelayStrategy exists = stCOrderPushDelayStrategyMapper.selectById(id);
        if (exists == null) {
            throw new NDSException("当前记录已不存在！");
        }
        // 更新策略时，删除REDIS
      /*  String redisKey = OmsRedisKeyResources.buildLockStrderPushDelayRedisKey(exists.getCpCShopId());
        if (redisUtil.objRedisTemplate.hasKey(redisKey)) {
            redisUtil.objRedisTemplate.delete(redisKey);
        }*/
        stCOrderPushDelayStrategy.setId(id);
        StBeanUtils.makeModifierField(stCOrderPushDelayStrategy, session.getUser());
        if (stCOrderPushDelayStrategyMapper.updateById(stCOrderPushDelayStrategy) <= 0) {
            throw new NDSException("订单推单延时策略策略更新失败");
        }
        log.debug(LogUtil.format("Start Cache Redis Data Param#{}"), JSONObject.toJSONString(exists));
        RedisCacheUtil.delete(exists.getCpCShopId(), RedisConstant.SHOP_ORDER_PUSH_DELAY);
        log.debug(LogUtil.format("Finish Cache Redis Data"));
        return ValueHolderUtils.getSuccessValueHolder(id, StConstant.TAB_ST_C_ORDER_PUSH_DELAY_STRATEGY);
    }

}
