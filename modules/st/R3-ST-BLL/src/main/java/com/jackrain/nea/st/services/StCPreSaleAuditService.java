package com.jackrain.nea.st.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.st.common.StConstant;
import com.jackrain.nea.st.mapper.StCPreSaleItemMapper;
import com.jackrain.nea.st.mapper.StCPreSaleMapper;
import com.jackrain.nea.st.model.table.StCPreSaleDO;
import com.jackrain.nea.st.model.table.StCPreSaleItemDO;
import com.jackrain.nea.st.utils.StBeanUtils;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;

/**
 * <AUTHOR>
 * @Description 预售解析策略审核业务类
 * @Date 2020/06/10
 * @Copyright 2019-2020
 */
@Component
@Slf4j
@Transactional
public class StCPreSaleAuditService extends CommandAdapter {

    @Autowired
    private StCPreSaleMapper stCPreSaleMapper;

    @Autowired
    private StCPreSaleItemMapper stCPreSaleItemMapper;


    /**
     * @return com.jackrain.nea.util.ValueHolder
     * <AUTHOR>
     * @Description 预售解析策略审核
     * @Date  2020/06/10
     * @Param [session]
     **/
    public ValueHolder execute(QuerySession session) throws NDSException{
        DefaultWebEvent event = session.getEvent();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(
                event.getParameterValue("param"), "yyyy-MM-dd HH:mm:ss", SerializerFeature.WriteMapNullValue),
                Feature.OrderedField);
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("请求JSON：{}"), param.toString());
        }
        if (param == null) {
            throw new NDSException("参数为空！");
        }

        ValueHolder resultValueHolder = new ValueHolder();
        HashMap<Long, Object> errMap = new HashMap<Long, Object>();
        //生成审核Json数组
        JSONArray auditArray = StBeanUtils.makeAuditJsonArray(param);
        for (int i = 0; i < auditArray.size(); i++) {
            Long id = Long.valueOf(auditArray.get(i).toString());
            //审核验证
            checkAudit(id);
            StCPreSaleDO stCPreSaleDO = new StCPreSaleDO();
            stCPreSaleDO.setId(id);
            stCPreSaleDO.setPreSaleStatus(StConstant.PRE_SALE_STATUS_02);
            StBeanUtils.makeModifierField(stCPreSaleDO,session.getUser());
            //更新单据状态
            int count = stCPreSaleMapper.updateById(stCPreSaleDO);
            if (count < 0) {
                throw new NDSException("审核失败");
            }

        }
        resultValueHolder = StBeanUtils.getExcuteValueHolder(auditArray.size(),errMap);
        return resultValueHolder;
    }

    /**
     * @return com.alibaba.fastjson.JSONObject
     * <AUTHOR>
     * @Description 审核验证
     * @Date 2020/06/10
     * @Param [id]
     **/
    private JSONObject checkAudit(Long id) throws NDSException {
        //记录不存在
        StCPreSaleDO stCPreSaleDO = stCPreSaleMapper.selectById(id);
        if (stCPreSaleDO == null) {
            throw new NDSException("当前记录已不存在！");
        } else {
            if (StConstant.PRE_SALE_STATUS_02.equals(stCPreSaleDO.getPreSaleStatus())) {
                throw new NDSException("方案已审核，不允许重复审核！");
            }else if(StConstant.PRE_SALE_STATUS_03.equals(stCPreSaleDO.getPreSaleStatus())){
                throw new NDSException("方案已作废，不允许审核！");
            }else if(StConstant.PRE_SALE_STATUS_04.equals(stCPreSaleDO.getPreSaleStatus())){
                throw new NDSException("方案已结案，不允许审核！");
            }
        }

        //明细为空
        Integer itemSize = stCPreSaleItemMapper.selectCount(new QueryWrapper<StCPreSaleItemDO>()
                .eq("ST_C_PRE_SALE_ID", id));
        if (itemSize == 0) {
            throw new NDSException("方案无明细，不允许审核！");
        }
        return null;
    }
}
