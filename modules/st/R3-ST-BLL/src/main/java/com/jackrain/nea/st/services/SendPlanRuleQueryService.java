package com.jackrain.nea.st.services;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.jackrain.nea.psext.api.utils.JsonUtils;
import com.jackrain.nea.st.mapper.*;
import com.jackrain.nea.st.model.result.WarehouseRankResult;
import com.jackrain.nea.st.model.table.*;
import com.jackrain.nea.utility.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @description: 派单策略服务接口
 * @author: 汪聿森
 * @date: 2019-05-21
 */
@Component
@Slf4j
public class SendPlanRuleQueryService {

    @Autowired
    private StCSendPlanMapper stCSendPlanMapper;

    @Autowired
    private AdParamMapper adParamMapper;

    @Autowired
    private StCSendRuleWarehouseRateMapper stCSendRuleWarehouseRateMapper;

    @Autowired
    private StCSendRuleAddressRankMapper stCSendRuleAddressRank1Mapper;

    @Autowired
    private StCSendRuleAddressVipMapper stCSendRuleAddressVipMapper;

    @Autowired
    private StCSendRuleMapper stCSendRuleMapper;

    /**
     * 查找派单方案
     *
     * @param shopId      店铺Id
     * @param currentDate 当前日期
     * @return List<Long
     */
    public List<Long> selectSendPlanList(Long shopId, Date currentDate) {
        log.debug(LogUtil.format("SendPlanRuleQueryService.selectSendPlanList入参：currentDate：{},shopId=", shopId), currentDate);
        List<Long> sendPlanList = stCSendPlanMapper.querySendPlanList(shopId, currentDate);
        log.debug(LogUtil.format("SendPlanRuleQueryService.selectSendPlanList返回:") + JSONObject.toJSONString(sendPlanList));
        return sendPlanList;
    }

    /**
     * 在AD_PARAM表中先取出 指定系统参数key所对应的所有的值
     *
     * @param name
     * @return
     */
    public AdParam selectSysParamValue(String name) {
        return adParamMapper.selectSysParamValue(name);
    }

    /**
     * 根据方案查找派单规则明细
     *
     * @param planId 派单方案
     * @return Long
     */
    public List<Long> selectSendPlanItemList(Long planId) {
        log.debug(LogUtil.format("SendPlanRuleQueryService.selectSendPlanItemList入参：planId=", planId));
        List<Long> sendPlanItemList = stCSendPlanMapper.querySendPlanItemList(planId);
        log.debug(LogUtil.format("SendPlanRuleQueryService.selectSendPlanItemList返回:") + JSONObject.toJSONString(sendPlanItemList));
        return sendPlanItemList;
    }

    /**
     * 根据方案查找派单规则明细
     *
     * @param planId 派单方案
     * @return Long
     */
    public List<StCSendPlanItemDO> selectSendPlanItemListByPlanId(Long planId) {
        log.debug(LogUtil.format("SendPlanRuleQueryService.selectSendPlanItemListByPlanId入参：planId=", planId));
        List<StCSendPlanItemDO> sendPlanItemList = stCSendPlanMapper.selectSendPlanItemListByPlanId(planId);
        log.debug(LogUtil.format("SendPlanRuleQueryService.selectSendPlanItemListByPlanId返回:") + JSONObject.toJSONString(sendPlanItemList));
        return sendPlanItemList;
    }

    /**
     * 根据实体仓库去匹配仓库发货比例
     *
     * @param warehouseList 实体发货仓库
     * @param sendRuleId    派单规则
     * @return List<OcStCSendRuleWarehouseRate>
     */
    public List<StCSendRuleWarehouseRateDO> selectWarehouseRateMapper(List<Long> warehouseList, Long sendRuleId) {
        log.debug(LogUtil.multiFormat(LogUtil.format("SendPlanRuleQueryService.selectWarehouseRateMapper入参：" +
                "sendRuleId=", sendRuleId), warehouseList));
        List<StCSendRuleWarehouseRateDO> stCSendRuleWarehouseRateDOList = stCSendRuleWarehouseRateMapper.queryWarehouseRateList(warehouseList, sendRuleId);
        log.debug(LogUtil.format("SendPlanRuleQueryService.selectWarehouseRateMapper返回:") + JSONObject.toJSONString(stCSendRuleWarehouseRateDOList));
        return stCSendRuleWarehouseRateDOList;
    }

    /**
     * 统计发货仓库发货数量
     *
     * @param warehouseList 实体发货仓库
     * @param sendRuleId    派单规则
     * @return BigDecimal
     */
    public BigDecimal selectQtySendTotal(List<Long> warehouseList, Long sendRuleId) {
        log.debug(LogUtil.multiFormat(LogUtil.format("SendPlanRuleQueryService.selectQtySendTotal入参：" +
                "sendRuleId=", sendRuleId), warehouseList));
        BigDecimal total = stCSendRuleWarehouseRateMapper.queryQtySendTotal(warehouseList, sendRuleId);
        log.debug(LogUtil.format("SendPlanRuleQueryService.selectQtySendTotal返回：") + total);
        return total;
    }

    /**
     * 更新发货仓库数量
     *
     * @param warehouseId 仓库Id
     * @return Integer
     */
    public Integer updateRuleWarehouseRate(Long warehouseId) {
        log.debug(LogUtil.format("SendPlanRuleQueryService.updateRuleWarehouseRate入参：warehouseId=",warehouseId));
        Integer total = stCSendPlanMapper.updateRuleWarehouseRate(warehouseId);
        log.debug(LogUtil.format("SendPlanRuleQueryService.updateRuleWarehouseRate返回：") + total);
        return total;
    }

    /**
     * 根据方案类型排序,大促-活动-日常
     *
     * @param sendPlanList 方案集合
     * @return List<Long>
     */
    public List<Long> querySendPlanByActiveList(List<Long> sendPlanList) {
        log.debug(LogUtil.multiFormat("SendPlanRuleQueryService.querySendPlanByActiveList入参：sendPlanList=", sendPlanList));
        List<Long> sendPlanByActiveList = stCSendPlanMapper.querySendPlanByActiveList(sendPlanList);
        log.debug(LogUtil.format("SendPlanRuleQueryService.querySendPlanByActiveList返回:") + JSONObject.toJSONString(sendPlanByActiveList));
        return sendPlanByActiveList;
    }

    /**
     * 根据规则ID查找派单规则
     *
     * @param sendRuleId 规则Id
     * @return JSONObject
     */
    public StCSendRuleDO selectSendRuleType(Long sendRuleId) {
        log.debug(LogUtil.format("SendPlanRuleQueryService.selectSendRuleType入参：sendRuleId=" + sendRuleId));
        StCSendRuleDO stCSendRuleDO = stCSendRuleMapper.querySendRuleTypeById(sendRuleId);
        log.debug(LogUtil.format("SendPlanRuleQueryService.selectSendRuleType返回：") + JSONObject.toJSONString(stCSendRuleDO));
        return stCSendRuleDO;
    }

    /**
     * 查找分仓比例的规则
     *
     * @param sendRuleIds 规则IDs
     * @return List<Long>
     */
    public Long selectSendRuleIds(List<Long> sendRuleIds, String type) {
        log.debug(LogUtil.format(LogUtil.multiFormat("SendPlanRuleQueryService.selectSendRuleIds入参：" +
                "sendRuleIds=", sendRuleIds) + "type=", type));
        Long sendRuleIdList = stCSendRuleMapper.selectSendRuleByIds(sendRuleIds, type);
        log.debug(LogUtil.format("SendPlanRuleQueryService.selectSendRuleIds返回：") + JSONObject.toJSONString(sendRuleIdList));
        return sendRuleIdList;
    }

    /**
     * 查找分仓比例的规则
     *
     * @param sendRuleIds 规则IDs
     * @return List<StCSendRuleDO>
     */
    public List<StCSendRuleDO> selectSendRuleByIdList(List<Long> sendRuleIds) {
        log.debug(LogUtil.multiFormat("SendPlanRuleQueryService.selectSendRuleByIds入参：sendRuleIds=", sendRuleIds));
        List<StCSendRuleDO> sendRuleIdList = stCSendRuleMapper.selectSendRuleByIdList(sendRuleIds);
        log.debug(LogUtil.format("SendPlanRuleQueryService.selectSendRuleByIds返回：") + JSONObject.toJSONString(sendRuleIdList));
        return sendRuleIdList;
    }

    /**
     * 根据实体仓库去匹配地址就近的仓库
     *
     * @param cpPhyWarehouseList 实体发货仓库
     * @param cRegionProvinceId  省份Id
     * @param sendRuleId         派单规则
     * @return List<Long>
     */
    public List<Long> selectAddressWarehouseList(List<Long> cpPhyWarehouseList, Long cRegionProvinceId, Long sendRuleId) {
        log.debug(LogUtil.format("SendPlanRuleQueryService.selectAddressWarehouseList入参：cpPhyWarehouseList={}," +
                "cRegionProvinceId/sendRuleId=", cRegionProvinceId, sendRuleId));
        List<StCSendRuleAddressRankDO> addressRankList = stCSendRuleAddressRank1Mapper.queryAddressRank(cRegionProvinceId, sendRuleId);
        List<Long> warehouseList = Lists.newArrayList();
        for (StCSendRuleAddressRankDO rank : addressRankList) {
            if (StringUtils.isNotBlank(rank.getWarehouseRank())) {
                List<WarehouseRankResult> warehouseRankList = JsonUtils.jsonToList(WarehouseRankResult.class, rank.getWarehouseRank());
                warehouseRankList = warehouseRankList.stream().sorted(Comparator.comparing(WarehouseRankResult::getRank))
                        .collect(Collectors.toList());
                for (WarehouseRankResult result : warehouseRankList) {
                    if (cpPhyWarehouseList.contains(result.getWarehouseId())) {
                        warehouseList.add(result.getWarehouseId());
                    }
                }
            }
            break;
        }
        log.debug(LogUtil.format("SendPlanRuleQueryService.selectAddressWarehouseList返回：") + JSONObject.toJSONString(warehouseList));
        return warehouseList;
    }

    /**
     * 根据实体仓库去匹配地址就近的仓库优先级
     *
     * @param cRegionProvinceId 省份Id
     * @param sendRuleId        派单规则
     * @return List<StCSendRuleAddressRankDO>
     */
    public List<StCSendRuleAddressRankDO> selectSendRuleAddressRankList(Long cRegionProvinceId, Long sendRuleId) {
        log.debug(LogUtil.format("SendPlanRuleQueryService.selectSendRuleAddressRankList入参：cRegionProvinceId" +
                "/sendRuleId=", cRegionProvinceId, sendRuleId));
        List<StCSendRuleAddressRankDO> addressRankList = stCSendRuleAddressRank1Mapper.queryAddressRank(cRegionProvinceId, sendRuleId);
        log.debug(LogUtil.format("SendPlanRuleQueryService.selectSendRuleAddressRankList返回：") + JSONObject.toJSONString(addressRankList));
        return addressRankList;
    }

    /**
     * 查找按唯品会的返回仓库优先级
     *
     * @param sendRuleId         规则ID
     * @param cpCVipcomWahouseId 唯品会仓库ID
     * @return
     */
    public List<StCSendRuleAddressVipDo> selectSendRuleAddressVoList(Long sendRuleId, Long cpCVipcomWahouseId) {
        return stCSendRuleAddressVipMapper.queryAddressRank(sendRuleId, cpCVipcomWahouseId);
    }

    /**
     * 查找派单方案
     *
     * @param shopId 店铺Id
     * @return List<StCSendPlanDO>
     */
    public List<StCSendPlanDO> selectSendPlanListByShopId(Long shopId) {
        log.debug(LogUtil.format("SendPlanRuleQueryService.selectSendPlanListByShopId入参：shopId=", shopId));
        List<StCSendPlanDO> sendPlanList = stCSendPlanMapper.selectSendPlanListByShopId(shopId);
        log.debug(LogUtil.format("SendPlanRuleQueryService.selectSendPlanListByShopId返回：") + JSONObject.toJSONString(sendPlanList));
        return sendPlanList;
    }


    /**
     * 根据店铺id 查询卫平会派单规则
     *
     * @param shopId 规则ID
     * @return list
     */
    public List<StCSendRuleAddressVipDo> findRuleAddressVipByShopId(Long shopId, String jitCode) {
        log.debug(LogUtil.format("SendPlanRuleQueryService.findRuleAddressVipByShopId.param：shopId/jitCode="
                , shopId, jitCode));
        return stCSendRuleAddressVipMapper.findRuleAddressVipByShopId(shopId);
    }




    /**
     * 根据店铺id 查询卫平会派单规则
     *
     * @param shopId 规则ID
     * @return list
     */
    public List<StCSendRuleAddressVipDo> findRuleAddressVipByShopId(Long shopId) {
        return stCSendRuleAddressVipMapper.findRuleAddressVipByShopId(shopId);
    }


}
