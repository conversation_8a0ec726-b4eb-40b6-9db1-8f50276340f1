package com.jackrain.nea.st.services;

import com.jackrain.nea.redis.util.RedisOpsUtil;
import com.jackrain.nea.st.mapper.StCOrderSplitMergeTypeMapper;
import com.jackrain.nea.st.model.table.StCOrderSplitMergeType;
import com.jackrain.nea.st.utils.RedisConstant;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * @Description: 拆单原因获取
 * @author: 江家雷
 * @since: 2020/11/12
 * create at : 2020/11/12 11:03
 */
@Component
@Slf4j
public class SplitOrderReasonService {

    @Autowired
    StCOrderSplitMergeTypeMapper mapper;

    @Autowired
    private RedisOpsUtil redisUtil;

    public Integer getSplitReason(String code) {
        code = code.toUpperCase();
        String redisKey = RedisConstant.bulidSplitTypeKey(code);
        if (redisUtil.objRedisTemplate.hasKey(redisKey)) {
            return (Integer) redisUtil.objRedisTemplate.opsForValue().get(redisKey);
        }
        List<StCOrderSplitMergeType> list = mapper.selectStCOrderSplitMergeTypeList();
        if (CollectionUtils.isNotEmpty(list)) {
            for (StCOrderSplitMergeType type : list) {
                String key = RedisConstant.bulidSplitTypeKey(type.getTypeCode());
                redisUtil.objRedisTemplate.opsForValue().set(key, type.getType(), 5, TimeUnit.HOURS);
            }
        }
        if (redisUtil.objRedisTemplate.hasKey(redisKey)) {
            return (Integer) redisUtil.objRedisTemplate.opsForValue().get(redisKey);
        }
        return null;
    }

    public List<StCOrderSplitMergeType> getStCOrderSplitMergeType(List<Long> ids) {
        List<StCOrderSplitMergeType> list = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(ids)) {
            for (Long id : ids) {
                String redisKey = RedisConstant.bulidSplitTypeIdKey(id);
                if (redisUtil.objRedisTemplate.hasKey(redisKey)) {
                    list.add((StCOrderSplitMergeType) redisUtil.objRedisTemplate.opsForValue().get(redisKey));
                } else {
                    StCOrderSplitMergeType type = mapper.selectById(id);
                    if (type != null) {
                        list.add(type);
                        String key = RedisConstant.bulidSplitTypeIdKey(id);
                        redisUtil.objRedisTemplate.opsForValue().set(key, type, 5, TimeUnit.HOURS);
                    }
                }
            }
        }

        return list ;
    }
}