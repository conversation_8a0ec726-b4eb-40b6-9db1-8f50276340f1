package com.jackrain.nea.st.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.google.common.base.Throwables;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.st.common.StConstant;
import com.jackrain.nea.st.mapper.StCRefundOrderStrategyMapper;
import com.jackrain.nea.st.model.table.StCRefundOrderStrategyDO;
import com.jackrain.nea.st.model.table.StCVipcomProjectDO;
import com.jackrain.nea.st.utils.StBeanUtils;
import com.jackrain.nea.st.utils.ValueHolderUtils;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2019/3/8 11:06
 */
@Component
@Slf4j
@Transactional
public class RefundOrderStrategySaveService extends CommandAdapter {
    @Autowired
    private StCRefundOrderStrategyMapper stCRefundOrderStrategyMapper;

    public ValueHolder execute(QuerySession session) {
        DefaultWebEvent event = session.getEvent();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(
                event.getParameterValue("param"), "yyyy-MM-dd HH:mm:ss", SerializerFeature.WriteMapNullValue),
                Feature.OrderedField);

        Long id = param.getLong("objid");
        JSONObject fixColumn = param.getJSONObject("fixcolumn");
        if (fixColumn != null && id != null) {
            if (id != -1) {
                return updateRefundOrderStrategy(session, fixColumn, id);
            } else {
                return insertRefundOrderStrategy(session, fixColumn);
            }
        }
        throw new NDSException("当前记录已不存在！");
    }

    /**
     * 日程计划更新
     *
     * @param session
     * @param fixColumn
     * @param id
     * @return com.jackrain.nea.util.ValueHolder
     * @Author: 陈秀楼
     * @Date 2019/3/8
     */
    private ValueHolder updateRefundOrderStrategy(QuerySession session, JSONObject fixColumn, Long id) {

        String refundStr = fixColumn.getString(StConstant.TAB_ST_C_REFUND_ORDER_STRATEGY);//档期日程规划主表
        if (StringUtils.isNotEmpty(refundStr)) {
            //转换成实体对象
            StCRefundOrderStrategyDO stCRefundOrderStrategyDO = JSON.parseObject(refundStr, StCRefundOrderStrategyDO.class);
            StCRefundOrderStrategyDO existsVipcomProject = stCRefundOrderStrategyMapper.selectById(id);
            if (existsVipcomProject == null) {
                return ValueHolderUtils.getFailValueHolder("当前记录已不存在！");
            }
            //update基础字段
            StBeanUtils.makeModifierField(stCRefundOrderStrategyDO, session.getUser());
            try {
                if (stCRefundOrderStrategyMapper.updateById(stCRefundOrderStrategyDO) > 0) {
                    return ValueHolderUtils.getSuccessValueHolder(id, StConstant.TAB_ST_C_REFUND_ORDER_STRATEGY);
                } else {
                    return ValueHolderUtils.getFailValueHolder("保存失败");
                }
            } catch (Exception ex) {
                log.error(LogUtil.format("RefundOrderStrategySaveService.updateRefundOrderStrategy Error{}"),
                        Throwables.getStackTraceAsString(ex));
                return ValueHolderUtils.getFailValueHolder("保存异常");
            }
        } else {
            return ValueHolderUtils.getSuccessValueHolder(id, StConstant.TAB_ST_C_REFUND_ORDER_STRATEGY);
        }
    }

    /**
     * 日程计划插入
     *
     * @param session
     * @param fixColumn
     * @return com.jackrain.nea.util.ValueHolder
     * @Author: 陈秀楼
     * @Date 2019/3/8
     */
    private ValueHolder insertRefundOrderStrategy(QuerySession session, JSONObject fixColumn) {
        ValueHolder holder = new ValueHolder();
        String refundStr = fixColumn.getString(StConstant.TAB_ST_C_REFUND_ORDER_STRATEGY);//退货审核策略
        if (StringUtils.isNotEmpty(refundStr)) {
            //转换成实体对象
            StCRefundOrderStrategyDO stCRefundOrderStrategyDO = JSON.parseObject(refundStr, StCRefundOrderStrategyDO.class);
            //判断记录是否已存在
            HashMap<String, Object> map = new HashMap<>();
            map.put("cp_c_shop_id", stCRefundOrderStrategyDO.getCpCShopId());
            map.put("begin_time", stCRefundOrderStrategyDO.getBeginTime());
            map.put("end_time", stCRefundOrderStrategyDO.getEndTime());
            List<StCRefundOrderStrategyDO> stCRefundOrderStrategyList = stCRefundOrderStrategyMapper.selectByMap(map);
            if (stCRefundOrderStrategyList != null && !stCRefundOrderStrategyList.isEmpty()) {
                return ValueHolderUtils.getFailValueHolder("当前记录已存在！");
            }
            long Id = ModelUtil.getSequence(StConstant.TAB_ST_C_REFUND_ORDER_STRATEGY);
            stCRefundOrderStrategyDO.setId(Id);
            //基本字段值设置
            StBeanUtils.makeCreateField(stCRefundOrderStrategyDO, session.getUser());
            try {
                int insertResult = stCRefundOrderStrategyMapper.insert(stCRefundOrderStrategyDO);
                if (insertResult > 0) {
                    return ValueHolderUtils.getSuccessValueHolder(stCRefundOrderStrategyDO.getId(), StConstant.TAB_ST_C_REFUND_ORDER_STRATEGY);
                } else {
                    return ValueHolderUtils.getFailValueHolder("保存失败！");
                }
            } catch (Exception ex) {
                log.error(LogUtil.format("RefundOrderStrategySaveService.insertRefundOrderStrategy Error{}"),
                        Throwables.getStackTraceAsString(ex));
                return ValueHolderUtils.getFailValueHolder("保存异常！");
            }
        }
        throw new NDSException("当前表SST_C_VIPCOM_PROJECT不存在！");
    }
}
