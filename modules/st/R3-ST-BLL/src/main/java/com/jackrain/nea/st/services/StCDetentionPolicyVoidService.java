package com.jackrain.nea.st.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.st.common.StConstant;
import com.jackrain.nea.st.mapper.StCDetentionPolicyMapper;
import com.jackrain.nea.st.model.table.StCDetentionPolicy;
import com.jackrain.nea.st.model.table.StCSendPlanDO;
import com.jackrain.nea.st.utils.RedisCacheUtil;
import com.jackrain.nea.st.utils.RedisConstant;
import com.jackrain.nea.st.utils.StBeanUtils;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.HashMap;

/**
 * @program: r3-st
 * @description: 预售卡单作废
 * @author: liuwj
 * @create: 2021-06-17 20:16
 **/
@Component
@Slf4j
public class StCDetentionPolicyVoidService extends CommandAdapter {
    @Autowired
    private StCDetentionPolicyMapper mapper;

    @Override
    public ValueHolder execute(QuerySession querySession) throws NDSException {
        //1.获取传入参数
        DefaultWebEvent event = querySession.getEvent();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(
                event.getParameterValue("param"), "yyyy-MM-dd HH:mm:ss"),
                Feature.OrderedField);
        if (param == null) {
            throw new NDSException("参数为空！");
        }
        log.debug(LogUtil.format("Start StCDetentionPolicyVoidService.execute. ReceiveParams: {}"),
                param.toJSONString());
        //2.定义错误记录数
        HashMap<Long, Object> errMap = new HashMap();
        //3.生成审核Json数组
        JSONArray voidArray = StBeanUtils.makeVoidJsonArray(param);
        for (int i = 0; i < voidArray.size(); i++) {
            Long id = voidArray.getLong(i);
            try {
                //4.遍历作废方法
                voidAction(id, querySession);
            } catch (Exception e) {
                errMap.put(id, e.getMessage());
            }
        }
        //5.返回ValueHolder对象
        return StBeanUtils.getExcuteValueHolder(voidArray.size(), errMap);
    }


    /**
     * @param id
     * @param querySession
     * @return void
     * @Descroption 检查并更新作废状态
     * @Author: 黄火县
     * @Date 2019/3/12
     */
    public void voidAction(Long id, QuerySession querySession) {
        StCDetentionPolicy stCDetentionPolicy = mapper.selectById(id);
        //主表校验
        checkAction(stCDetentionPolicy);
        stCDetentionPolicy.setEstatus(StConstant.HOLD_ORDER_STATUS_03);
        stCDetentionPolicy.setIsactive(StConstant.ISACTIVE_N);
        //更新作废状态
        StBeanUtils.makeModifierField(stCDetentionPolicy, querySession.getUser());
        int updateNum = mapper.updateById(stCDetentionPolicy);
        if (updateNum < 0) {
            throw new NDSException("方案:" + stCDetentionPolicy.getName() + "作废失败！");
        }
    }

    private void checkAction(StCDetentionPolicy stCDetentionPolicy) {
        if (stCDetentionPolicy == null) {
            throw new NDSException("当前记录已不存在！");
        } else {
            if (StConstant.ISACTIVE_N.equals(stCDetentionPolicy.getIsactive())) {
                throw new NDSException("当前记录已作废，不允许重复作废！");
            }
            if (!StConstant.CON_BILL_STATUS_01.equals(stCDetentionPolicy.getEstatus())) {
                throw new NDSException("当前记录非未审核状态，不允许作废！");
            }
        }
    }
}
