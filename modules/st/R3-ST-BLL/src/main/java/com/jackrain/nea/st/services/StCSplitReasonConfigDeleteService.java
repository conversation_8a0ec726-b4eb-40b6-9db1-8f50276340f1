package com.jackrain.nea.st.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.st.mapper.StCSplitReasonConfigMapper;
import com.jackrain.nea.st.mapper.StCSplitReasonMapper;
import com.jackrain.nea.st.model.table.StCSplitReasonConfigDO;
import com.jackrain.nea.st.model.table.StCSplitReasonDO;
import com.jackrain.nea.st.utils.StBeanUtils;
import com.jackrain.nea.st.utils.ValueHolderUtils;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * @program: r3-st
 * @description: 自定义拆单删除业务类
 * @author: liuwj
 * @create: 2021-05-31 15:03
 **/
@Component
@Slf4j
@Transactional
public class StCSplitReasonConfigDeleteService extends CommandAdapter {
    @Autowired
    StCSplitReasonConfigMapper stCSplitReasonConfigMapper;

    @Autowired
    StCSplitReasonMapper stCSplitReasonMapper;

    /**
     * <AUTHOR>
     * @Date 15:05 2021/5/31
     * @Description 删除服务
     */
    @Override
    public ValueHolder execute(QuerySession querySession) {
        DefaultWebEvent event = querySession.getEvent();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(
                event.getParameterValue("param"), "yyyy-MM-dd HH:mm:ss"),
                Feature.OrderedField);
        boolean delMainFlag = param.getBoolean("isdelmtable");//是否删除主表
        Long id = param.getLong("objid");
        JSONArray errorArray = new JSONArray();
        if (id != null && id > 0) {
            ValueHolder valueHolder = new ValueHolder();
            StCSplitReasonConfigDO stCSplitReasonConfigDO = stCSplitReasonConfigMapper.selectById(id);
            if (stCSplitReasonConfigDO == null){
                return ValueHolderUtils.getFailValueHolder("当前记录已不存在！");
            }
            if (delMainFlag) {
                List<StCSplitReasonDO> splitReasonDOList = stCSplitReasonMapper.selectList(new QueryWrapper<StCSplitReasonDO>()
                .lambda().eq(StCSplitReasonDO :: getSplitReasonConfigId,id)
                         .eq(StCSplitReasonDO :: getIsactive,"Y"));
                if (CollectionUtils.isNotEmpty(splitReasonDOList)) {
                    String msg = "";
                    for (StCSplitReasonDO stCSplitReasonDO : splitReasonDOList) {
                        msg += stCSplitReasonDO.getEcode()+",";
                    }
                    return ValueHolderUtils.getFailValueHolder("明细["+stCSplitReasonConfigDO.getEcode()+"]已被[自定义拆单原因表] ["+msg.substring(0,msg.length()-1)+"]引用，请先解除引用！");
                }
                int count = stCSplitReasonConfigMapper.deleteById(stCSplitReasonConfigDO);
                if (count<= 0) {
                    errorArray.add("删除主表失败");
                }
            }
        }
        return StBeanUtils.getExcuteValueHolder(errorArray);
    }
}
