package com.jackrain.nea.st.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.redis.util.RedisOpsUtil;
import com.jackrain.nea.st.common.StConstant;
import com.jackrain.nea.st.mapper.StCOrderLabelItemMapper;
import com.jackrain.nea.st.mapper.StCOrderLabelLogMapper;
import com.jackrain.nea.st.mapper.StCOrderLabelMapper;
import com.jackrain.nea.st.mapper.StCOrderLabelShopItemMapper;
import com.jackrain.nea.st.model.table.StCOrderLabelDO;
import com.jackrain.nea.st.model.table.StCOrderLabelItemDO;
import com.jackrain.nea.st.model.table.StCOrderLabelLogDO;
import com.jackrain.nea.st.model.table.StCOrderLabelShopItemDO;
import com.jackrain.nea.st.utils.RedisConstant;
import com.jackrain.nea.st.utils.StBeanUtils;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.utils.AssertUtils;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.HashMap;
import java.util.List;

/**
 * @ClassName : StCOrderLabelAuditService
 * @Description : 订单打标策略审核
 * <AUTHOR>  YCH
 * @Date: 2021-11-23 14:53
 */
@Component
@Slf4j
public class StCOrderLabelAuditService extends CommandAdapter {
    @Autowired
    private StCOrderLabelMapper stCOrderLabelMapper;
    @Autowired
    private StCOrderLabelShopItemMapper stCOrderLabelShopItemMapper;

    @Autowired
    private StCOrderLabelItemMapper itemMapper;

    @Autowired
    private StCOrderLabelLogMapper stCOrderLabelLogMapper;

    @Autowired
    private RedisOpsUtil<String, String> redisUtil;

    @Override
    public ValueHolder execute(QuerySession querySession) throws NDSException {
        //1.获取传入参数
        DefaultWebEvent event = querySession.getEvent();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(event.getParameterValue("param"),
                "yyyy-MM-dd HH:mm:ss", SerializerFeature.WriteMapNullValue), Feature.OrderedField);
        if (param == null) {
            throw new NDSException("参数为空！");
        }
        log.debug(LogUtil.format("Start StCOrderLabelAuditService.execute. ReceiveParams: {}"), param.toJSONString());
        //2.定义错误记录数
        HashMap<Long, Object> errMap = new HashMap();
        //3.生成审核Json数组
        JSONArray auditArray = StBeanUtils.makeAuditJsonArray(param);
        for (int i = 0; i < auditArray.size(); i++) {
            Long id = auditArray.getLong(i);
            try {
                //4.遍历审核方法
                auditStCHoldOrder(id, querySession);
                //保存操作日志
                saveLog(id, querySession);
            } catch (Exception e) {
                errMap.put(id, e.getMessage());
            }
        }
        //5.返回ValueHolder对象
        return StBeanUtils.getExcuteValueHolder(auditArray.size(), errMap);
    }

    private void saveLog(Long id, QuerySession querySession) {
        StCOrderLabelLogDO stCOrderLabelLogDO = new StCOrderLabelLogDO();
        stCOrderLabelLogDO.setId(ModelUtil.getSequence(StConstant.TAB_ST_C_ORDER_LABEL_LOG));
        stCOrderLabelLogDO.setStCOrderLabelId(id);
        stCOrderLabelLogDO.setModcontent("审核");
        stCOrderLabelLogDO.setBmod("待审核");
        stCOrderLabelLogDO.setAmod("已审核");
        StBeanUtils.makeCreateField(stCOrderLabelLogDO, querySession.getUser());
        stCOrderLabelLogMapper.insert(stCOrderLabelLogDO);
    }

    public void auditStCHoldOrder(Long id, QuerySession querySession) {
        StCOrderLabelDO orderLabelDO = stCOrderLabelMapper.selectById(id);
        //主表校验
        checkEstatus(orderLabelDO);

        LambdaQueryWrapper<StCOrderLabelItemDO> itemWrapper = new LambdaQueryWrapper<>();
        itemWrapper.eq(StCOrderLabelItemDO::getStCOrderLabelId, orderLabelDO.getId());
        itemWrapper.eq(StCOrderLabelItemDO::getIsactive, StConstant.ISACTIVE_Y);
        Integer count = itemMapper.selectCount(itemWrapper);
        AssertUtils.isTrue(count > 0, "当前策略无明细");

        //更新单据状态
        orderLabelDO.setStatus(StConstant.HOLD_ORDER_STATUS_02);
        StBeanUtils.makeModifierField(orderLabelDO, querySession.getUser());
        orderLabelDO.setStatusId(querySession.getUser().getId().longValue());
        orderLabelDO.setStatusDate(new Date());
        int updateNum = stCOrderLabelMapper.updateById(orderLabelDO);
        if (updateNum < 0) {
            throw new NDSException("审核失败！");
        }
        List<StCOrderLabelShopItemDO> stCOrderLabelShopItemDOS = stCOrderLabelShopItemMapper.selectStCOrderLabelShopItemList(id);
        if (CollectionUtils.isNotEmpty(stCOrderLabelShopItemDOS)) {
            for (StCOrderLabelShopItemDO stCOrderLabelShopItemDO : stCOrderLabelShopItemDOS) {
                redisUtil.strRedisTemplate.delete(RedisConstant.ST_ORDER_LABEL + stCOrderLabelShopItemDO.getCpCShopId());
            }
        }
    }

    private void checkEstatus(StCOrderLabelDO stCHoldOrderDO) {
        if (stCHoldOrderDO == null) {
            throw new NDSException("当前记录已不存在！");
        } else {
            if (StConstant.CON_BILL_STATUS_02.equals(stCHoldOrderDO.getStatus())) {
                throw new NDSException("当前记录已审核，不允许重复审核！");
            } else if (StConstant.CON_BILL_STATUS_03.equals(stCHoldOrderDO.getStatus())) {
                throw new NDSException("当前记录已作废，不允许审核！");
            } else if (StConstant.CON_BILL_STATUS_04.equals(stCHoldOrderDO.getStatus())) {
                throw new NDSException("当前记录已结案，不允许审核！");
            }
        }
    }

}
