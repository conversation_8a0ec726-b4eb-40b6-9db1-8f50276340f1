package com.jackrain.nea.st.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.google.common.base.Throwables;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.st.common.StConstant;
import com.jackrain.nea.st.mapper.StCExchangeRefuseReasonItemMapper;
import com.jackrain.nea.st.mapper.StCExchangeStrategyOrderMapper;
import com.jackrain.nea.st.model.request.ExchangeStrategyOderRequest;
import com.jackrain.nea.st.model.table.StCExchangeRefuseReasonItemDO;
import com.jackrain.nea.st.model.table.StCExchangeStrategyOrderDO;
import com.jackrain.nea.st.utils.*;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.List;
import java.util.Optional;


/**
 * <AUTHOR> ruan.gz
 * @Description : 保存换货策略请求
 * @Date : 2020/6/19
 **/
@Component
@Slf4j
@Transactional
public class ExchangeStrategyOrderSaveService extends CommandAdapter {

    @Autowired
    private StCExchangeStrategyOrderMapper stCExchangeStrategyOrderMapper;
    @Autowired
    private StCExchangeRefuseReasonItemMapper stCExchangeRefuseReasonItemMapper;
    /**
     * @return com.jackrain.nea.util.ValueHolder
     * <AUTHOR>
     * @Description 新增修改主方法
     * @Date 2019/3/12
     * @Param [session]
     **/
    public ValueHolder saveExchangeStrategyOrder(QuerySession session) {
        DefaultWebEvent event = session.getEvent();
        JSONObject param = JsonUtils.filterNull(
                (JSONObject) event.getParameterValue("param"), null);
        log.debug(LogUtil.format("请求JSON: ")+param.toString());
        //1.拉取请求参数，解析
        //if (param == null) {
        //    log.debug(LogUtil.format(":获取param参数失败！");
        //    return ValueHolderUtils.getFailValueHolder("保存失败！");
        //}

        Long id = param.getLong("objid");//获取objid参数
        JSONObject fixColumn = param.getJSONObject("fixcolumn");//获取表数据

        if (fixColumn.getJSONObject("ST_C_EXCHANGE_ORDER_STRATEGY") != null) {
            String deviationAmtAgreeStr = fixColumn.getJSONObject("ST_C_EXCHANGE_ORDER_STRATEGY").getString(
                    "DEVIATION_AMT_AGREE");
            if (StringUtils.isNotEmpty(deviationAmtAgreeStr)) {
                try {
                    Integer deviationAmtAgree = Integer.valueOf(deviationAmtAgreeStr);
                    if (deviationAmtAgree < 0) {
                        throw new NDSException("偏差N元同意换货，必须填写非负数");
                    }
                } catch (Exception e) {
                    throw new NDSException("偏差N元同意换货，必须填写非负数");
                }
            }

            String deviationAmtRefuseStr = fixColumn.getJSONObject("ST_C_EXCHANGE_ORDER_STRATEGY").getString(
                    "DEVIATION_AMT_REFUSE");
            if (StringUtils.isNotEmpty(deviationAmtRefuseStr)) {
                try {
                    Integer deviationAmtRefuse = Integer.valueOf(deviationAmtRefuseStr);
                    if (deviationAmtRefuse < 0) {
                        throw new NDSException("偏差N元自动拒绝换货，必须填写非负数");
                    }
                } catch (Exception e) {
                    throw new NDSException("偏差N元自动拒绝换货，必须填写非负数");
                }
            }
        }

        //2.转换为请求bean，判断转是否成功校验参数
        ExchangeStrategyOderRequest request = JsonUtils.jsonParseClass(fixColumn, ExchangeStrategyOderRequest.class);
        if (fixColumn != null && id != null) {
            if (id != -1) {
                return updateOrder(session, request, id,fixColumn);
            } else {
                return addOrder(session, request,fixColumn);
            }
        }
        throw new NDSException("当前记录已不存在！");
    }

    /**
     * @return com.jackrain.nea.util.ValueHolder
     * <AUTHOR>
     * @Description 保存方法
     * @Date 2019/3/13
     * @Param [session, mergeOderRequest, id]
     **/
    private ValueHolder updateOrder(QuerySession session, ExchangeStrategyOderRequest mergeOderRequest, Long id, JSONObject fixColumn) {
        StCExchangeStrategyOrderDO stCMergeOrderDO = mergeOderRequest.getStCExchangeStrategyOrderDO();
        if (stCMergeOrderDO != null) {
            StCExchangeStrategyOrderDO st = stCExchangeStrategyOrderMapper.selectById(id);
            if (st == null) {
                throw new NDSException("当前记录已不存在！");
            }
            //update基础字段
            stCMergeOrderDO.setId(id);
            stCMergeOrderDO.setModifierid(Long.valueOf(session.getUser().getId()));
            stCMergeOrderDO.setModifieddate(new Timestamp(System.currentTimeMillis()));
            stCMergeOrderDO.setModifiername(session.getUser().getName());
            stCMergeOrderDO.setModifierename(session.getUser().getEname());
            stCMergeOrderDO.setDeviationAmtAgree(Optional.ofNullable(stCMergeOrderDO.getDeviationAmtAgree()).orElse(BigDecimal.ZERO));
            stCMergeOrderDO.setDeviationAmtRefuse(Optional.ofNullable(stCMergeOrderDO.getDeviationAmtRefuse()).orElse(BigDecimal.ZERO));
            int count = stCExchangeStrategyOrderMapper.updateById(stCMergeOrderDO);
            if (count < 0) {
                log.debug(LogUtil.format("订单换货策略主表更新失败！"));
                return ValueHolderUtils.getFailValueHolder("保存失败");
            }
        }
        saveItem(session, fixColumn, id);
        StCExchangeStrategyOrderDO stCOrderDO = stCExchangeStrategyOrderMapper.selectById(id);
        if (stCOrderDO != null) {
            RedisCacheUtil.delete(stCOrderDO.getCpCShopId(), RedisConstant.SHOP_EXCHANGE_STRATEGY);
        }
        return ValueHolderUtils.getSuccessValueHolder(id, StConstant.TAB_ST_C_EXCHANGE_REFUSE_REASON);
    }

    /**
     * @return com.jackrain.nea.util.ValueHolder
     * <AUTHOR>
     * @Description 新增方法
     * @Date 2019/3/13
     * @Param [session, sellOwnGoodsRequest]
     **/
    private ValueHolder addOrder(QuerySession session, ExchangeStrategyOderRequest request, JSONObject fixColumn) {
        StCExchangeStrategyOrderDO stCOrderDO = request.getStCExchangeStrategyOrderDO();
        long id = -1;
        if (stCOrderDO != null) {
            //主表实体
            stCOrderDO.setId(ModelUtil.getSequence(StConstant.TAB_ST_C_EXCHANGE_STRATEGY_ORDER));
            id = stCOrderDO.getId();
            stCOrderDO.setIsactive("Y");
            //基本字段值设置
            StBeanUtils.makeCreateField(stCOrderDO, session.getUser());
            stCOrderDO.setModifierename(session.getUser().getEname());
            stCOrderDO.setOwnerename(session.getUser().getEname());
            int insertResult = stCExchangeStrategyOrderMapper.insert(stCOrderDO);
            if (insertResult <= 0) {
                log.debug(LogUtil.format("订单换货策略插入表失败！"));
                return ValueHolderUtils.getFailValueHolder("订单换货策略保存失败！");
            }
            saveItem(session,fixColumn,id);
            RedisCacheUtil.delete(stCOrderDO.getCpCShopId(), RedisConstant.SHOP_EXCHANGE_STRATEGY);
        }
        return ValueHolderUtils.getSuccessValueHolder(id, StConstant.TAB_ST_C_EXCHANGE_STRATEGY_ORDER);
    }

    private void saveItem(QuerySession session, JSONObject fixColumn, Long id) {
        String item = fixColumn.getString(StConstant.TAB_ST_C_EXCHANGE_REFUSE_REASON);
        if (StringUtils.isNotEmpty(item)) {
            try {
                List<StCExchangeRefuseReasonItemDO> itemDOList = JSON.parseArray(item, StCExchangeRefuseReasonItemDO.class);
                if (itemDOList.isEmpty() || itemDOList.size() <= 0) {
                    return;
                }
                User user = SystemUserResource.getRootUser();
                itemDOList.forEach(itemDO -> {
                    itemDO.setModifierename(user.getEname());//修改人账号
                    if (itemDO.getId() != null && itemDO.getId() > 0) {
                        //明细修改
                        StBeanUtils.makeModifierField(itemDO, user);//修改信息
                        if ((stCExchangeRefuseReasonItemMapper.updateById(itemDO)) <= 0) {
                            throw new NDSException("明细修改失败！");
                        }
                    } else {
                        //明细创建
                        itemDO.setId(ModelUtil.getSequence(StConstant.TAB_ST_C_PRICE_ITEM));
                        itemDO.setExchangeOrderStrategyId(id);
                        StBeanUtils.makeCreateField(itemDO, user);//创建信息
                        itemDO.setOwnerename(user.getEname());//创建人账号
                        if ((stCExchangeRefuseReasonItemMapper.insert(itemDO)) <= 0) {
                            throw new NDSException("明细保存失败！");
                        }
                    }
                });
            } catch (Exception ex) {
                log.error(LogUtil.format("ExchangeStrategyOrderSaveService.saveItem.Error{}"),
                        Throwables.getStackTraceAsString(ex));
                throw new NDSException("明细保存异常" + ex.getMessage());
            }
        }
    }

    public ValueHolder delStrategyOrder(QuerySession querySession) {
        ValueHolder holder = new ValueHolder();
        DefaultWebEvent event = querySession.getEvent();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(
                event.getParameterValue("param"), "yyyy-MM-dd HH:mm:ss"), Feature.OrderedField);

        if (param == null || param.size() == 0) {
            holder = ValueHolderUtils.getFailValueHolder("参数为空!");
            return holder;
        }

        Long objid = param.getLong("objid");
        JSONObject tabitem = param.getJSONObject("tabitem");
        JSONArray itemArray = tabitem.getJSONArray("ST_C_EXCHANGE_REFUSE_REASON_ITEM");

        //判断主表是否存在
        StCExchangeStrategyOrderDO stCExchangeStrategyOrderDO = stCExchangeStrategyOrderMapper.selectById(objid);
        if (stCExchangeStrategyOrderDO == null) {
            holder = ValueHolderUtils.getFailValueHolder("当前记录已不存在！");
            return holder;
        }
        //单独删除明细
        return delItem(itemArray, objid, querySession, stCExchangeStrategyOrderDO);
    }

    public ValueHolder delItem(JSONArray itemArray, Long mainId, QuerySession querySession,
                               StCExchangeStrategyOrderDO stCExpressAllocationDO) {
        ValueHolder holder = new ValueHolder();
        int iSuc = 0;
        //明细表记录不存在，则提示：当前记录已不存在！
        for (int i = 0; i < itemArray.size(); i++) {
            Long itemId = itemArray.getLong(i);
            StCExchangeRefuseReasonItemDO itemDO = stCExchangeRefuseReasonItemMapper.selectById(itemId);
            if (itemDO != null) {
                if (stCExchangeRefuseReasonItemMapper.deleteById(itemId) > 0) {
                    iSuc += 1;
                }
            }
        }
        if (iSuc > 0) {
            StBeanUtils.makeModifierField(stCExpressAllocationDO, querySession.getUser());
            stCExchangeStrategyOrderMapper.updateById(stCExpressAllocationDO);
            holder = ValueHolderUtils.getDeleteSuccessValueHolder();
            RedisCacheUtil.delete(stCExpressAllocationDO.getCpCShopId(), RedisConstant.SHOP_EXCHANGE_STRATEGY);
        } else {
            holder = ValueHolderUtils.getFailValueHolder("删除记录不存在！");
        }
        return holder;
    }


}
