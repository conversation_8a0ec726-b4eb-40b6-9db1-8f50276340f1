package com.jackrain.nea.st.services;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.collect.Lists;
import com.jackrain.nea.config.PropertiesConf;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.cpext.api.CpLogisticsSelectServiceCmd;
import com.jackrain.nea.cpext.api.CpcPhyWareHouseQueryCmd;
import com.jackrain.nea.cpext.api.RegionQueryExtCmd;
import com.jackrain.nea.cpext.model.table.CpCPhyWarehouse;
import com.jackrain.nea.cpext.model.table.CpCRegion;
import com.jackrain.nea.cpext.model.table.CpLogistics;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.st.common.StConstant;
import com.jackrain.nea.st.mapper.StCUnfullcarCostItemMapper;
import com.jackrain.nea.st.mapper.StCUnfullcarCostMapper;
import com.jackrain.nea.st.model.enums.CloseStatusEnum;
import com.jackrain.nea.st.model.enums.LogisticsTypeEnum;
import com.jackrain.nea.st.model.enums.SubmitStatusEnum;
import com.jackrain.nea.st.model.poi.UnfullcarCostExportPoi;
import com.jackrain.nea.st.model.request.SyncUnfullcarCostImportRequest;
import com.jackrain.nea.st.model.table.StCUnfullcarCost;
import com.jackrain.nea.st.model.table.StCUnfullcarCostItem;
import com.jackrain.nea.st.result.StImportErrorMsgResult;
import com.jackrain.nea.st.utils.StBeanUtils;
import com.jackrain.nea.st.utils.StCExportUtil;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Reference;
import org.apache.poi.hssf.util.HSSFColor;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.FillPatternType;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.VerticalAlignment;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.XSSFCellStyle;
import org.apache.poi.xssf.usermodel.XSSFFont;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2022/6/13 18:01
 */
@Component
@Slf4j
@Transactional
public class StCUnfullcarCostImportService {

    @Reference(group = "cp-ext",version = "1.0")
    private CpcPhyWareHouseQueryCmd cpcPhyWareHouseQueryCmd;

    @Reference(group = "cp-ext",version = "1.0")
    private CpLogisticsSelectServiceCmd cpLogisticsSelectServiceCmd;

    @Reference(group = "cp-ext",version = "1.0")
    private RegionQueryExtCmd regionQueryExtCmd;

    @Autowired
    private StCExportUtil stCExportUtil;

    @Autowired
    private StCUnfullcarCostMapper stCUnfullcarCostMapper;

    @Autowired
    private StCUnfullcarCostItemMapper stCUnfullcarCostItemMapper;

    private DateFormat format = new SimpleDateFormat("yyyyMMdd");

    @Autowired
    private PropertiesConf pconf;

    public ValueHolderV14<List<StImportErrorMsgResult>> importUnfullcarCost(List<SyncUnfullcarCostImportRequest> importList, User user) {
        ValueHolderV14<List<StImportErrorMsgResult>> holderV14 = new ValueHolderV14<>();

        try {
            List<StImportErrorMsgResult> errorList = new ArrayList<>();
            //仓库信息
            Map<String, CpCPhyWarehouse> warehouseMap = new HashMap<>();
            ValueHolderV14<List<CpCPhyWarehouse>> warehourseResult = cpcPhyWareHouseQueryCmd.queryWarehourseIsY();
            if (ResultCode.SUCCESS==warehourseResult.getCode() && !CollectionUtils.isEmpty(warehourseResult.getData())) {
                warehouseMap = warehourseResult.getData().stream().collect(Collectors.toMap(CpCPhyWarehouse::getEname, Function.identity(), (x, y) -> y));
            }
            if (CollectionUtils.isEmpty(warehouseMap)) {
                holderV14.setCode(ResultCode.FAIL);
                holderV14.setMessage("请先维护实体仓信息！");
                return holderV14;
            }
            //物流信息
            Map<String, CpLogistics> logisticsMap = new HashMap<>();
            ValueHolderV14<List<CpLogistics>> logisticsResult = cpLogisticsSelectServiceCmd.queryLogisticsByType(LogisticsTypeEnum.BULK_CARGO_TRANSPORTATION.getKey());
            if (ResultCode.SUCCESS == logisticsResult.getCode() && !CollectionUtils.isEmpty(logisticsResult.getData())) {
                logisticsMap = logisticsResult.getData().stream().collect(Collectors.toMap(CpLogistics::getEname, Function.identity(), (x, y) -> y));
            }
            if (CollectionUtils.isEmpty(logisticsMap)) {
                holderV14.setCode(ResultCode.FAIL);
                holderV14.setMessage("请先维护物流公司信息！");
                return holderV14;
            }
            //省份信息
            Map<String, CpCRegion> provinceMap = new HashMap<>();
            //城市信息
            Map<String, CpCRegion> cityMap = new HashMap<>();
            List<String> regionTypes = Lists.newArrayList("PROV", "CITY");
            List<CpCRegion> regionList = regionQueryExtCmd.queryAllRegion(regionTypes);
            if (!CollectionUtils.isEmpty(regionList)) {
                Map<String, List<CpCRegion>> listMap = regionList.stream().collect(Collectors.groupingBy(CpCRegion::getRegiontype));
                if (!CollectionUtils.isEmpty(listMap)) {
                    provinceMap = listMap.get("PROV").stream().collect(Collectors.toMap(CpCRegion::getEname,Function.identity(),(x,y) -> y));
                    cityMap = listMap.get("CITY").stream().collect(Collectors.toMap(CpCRegion::getEname,Function.identity(),(x,y) -> y));
                }
            }
            if (CollectionUtils.isEmpty(provinceMap)) {
                holderV14.setCode(ResultCode.FAIL);
                holderV14.setMessage("请先维护省份信息！");
                return holderV14;
            }

            if (CollectionUtils.isEmpty(cityMap)) {
                holderV14.setCode(ResultCode.FAIL);
                holderV14.setMessage("请先维护城市信息！");
                return holderV14;
            }
            //构建主子表关系，用于后面对象构建
            Map<String,List<SyncUnfullcarCostImportRequest>> groupByMainData = new HashMap<>();
            this.importDataCheck(importList,errorList,warehouseMap,logisticsMap,provinceMap,cityMap,groupByMainData);

            if (!CollectionUtils.isEmpty(errorList)) {
                holderV14.setCode(ResultCode.FAIL);
                holderV14.setMessage("导入失败，导入数据存在错误！");
                holderV14.setData(errorList);
                return holderV14;
            }

            //构建保存和更新的对象集合
            List<StCUnfullcarCost> unfullcarCostList = new ArrayList<>();
            List<StCUnfullcarCostItem> unfullcarCostItemList = new ArrayList<>();
            this.buildSaveAndUpdateObj(groupByMainData,unfullcarCostList,unfullcarCostItemList,user);

            //数据库更新（带事务）
            if (!CollectionUtils.isEmpty(unfullcarCostList) && !CollectionUtils.isEmpty(unfullcarCostItemList)) {
                StCUnfullcarCostImportService importService = ApplicationContextHandle.getBean(StCUnfullcarCostImportService.class);
                importService.saveAll(unfullcarCostList,unfullcarCostItemList);
            }
            holderV14.setCode(ResultCode.SUCCESS);
            holderV14.setMessage("导入成功");
        } catch (Exception e) {
            holderV14.setCode(ResultCode.FAIL);
            holderV14.setMessage("零担费用设置导入处理异常：" + e.getMessage());
            return holderV14;
        }
        return holderV14;
    }

    /**
     * 主子表批量保存
     * @param unfullcarCostList
     * @param unfullcarCostItemList
     */
    @Transactional(rollbackFor = Exception.class)
    public void saveAll(List<StCUnfullcarCost> unfullcarCostList, List<StCUnfullcarCostItem> unfullcarCostItemList) {
        if (log.isDebugEnabled()) {
            log.debug("StCUnfullcarCostImportService-saveAll start");
        }

        stCUnfullcarCostMapper.batchInsert(unfullcarCostList);
        stCUnfullcarCostItemMapper.batchInsert(unfullcarCostItemList);

    }

    /**
     * 根据import对象封装DTO对象
     * @param groupByMainData
     * @param unfullcarCostList
     * @param unfullcarCostItemList
     */
    private void buildSaveAndUpdateObj(Map<String, List<SyncUnfullcarCostImportRequest>> groupByMainData, List<StCUnfullcarCost> unfullcarCostList, List<StCUnfullcarCostItem> unfullcarCostItemList,User user) {
        if (!CollectionUtils.isEmpty(groupByMainData)) {
            Set<String> keySet = groupByMainData.keySet();
            for (String key : keySet) {
                List<SyncUnfullcarCostImportRequest> importRequestList = groupByMainData.get(key);
                if (!CollectionUtils.isEmpty(importRequestList)) {
                    Long unfullcarCostId = null;
                    for (int i= 0;i < importRequestList.size();i++) {
                        SyncUnfullcarCostImportRequest importRequest = importRequestList.get(i);
                        if (importRequest != null) {
                            StCUnfullcarCostItem unfullcarCostItem = new StCUnfullcarCostItem();
                            if (i == 0) {
                                unfullcarCostId = ModelUtil.getSequence(StConstant.TAB_ST_C_UNFULLCAR_COST);
                                StCUnfullcarCost unfullcarCost = new StCUnfullcarCost();
                                unfullcarCost.setId(unfullcarCostId);
                                unfullcarCost.setCpCPhyWarehouseId(importRequest.getWarehouseId());
                                unfullcarCost.setCpCLogisticsId(importRequest.getLogisticsId());
                                unfullcarCost.setStartDate(importRequest.getStartDateForDate());
                                unfullcarCost.setEndDate(importRequest.getEndDateForDate());
                                if  (importRequest.getOilPriceLinkageNum() != null) {
                                    unfullcarCost.setOilPriceLinkage(importRequest.getOilPriceLinkageNum());
                                }
                                if (!StringUtils.isEmpty(importRequest.getRemark())) {
                                    unfullcarCost.setRemark(importRequest.getRemark());
                                }
                                unfullcarCost.setStatus(SubmitStatusEnum.NO_SUBMIT.getKey());
                                unfullcarCost.setCloseStatus(CloseStatusEnum.NO_CLOSE.getKey());
                                StBeanUtils.makeCreateField(unfullcarCost, user);
                                reSetDateTimeRange(unfullcarCost);
                                
                                unfullcarCostList.add(unfullcarCost);
                            }
                            unfullcarCostItem.setId(ModelUtil.getSequence(StConstant.TAB_ST_C_UNFULLCAR_COST_ITEM));
                            unfullcarCostItem.setUnfullcarCostId(unfullcarCostId);
                            unfullcarCostItem.setProvinceId(importRequest.getProvinceId());
                            unfullcarCostItem.setCityId(importRequest.getCityId());
                            unfullcarCostItem.setArrivalDays(importRequest.getArrivalDaysNum());
                            unfullcarCostItem.setStartWeight(importRequest.getStartWeightNum());
                            unfullcarCostItem.setEndWeight(importRequest.getEndWeightNum());
                            unfullcarCostItem.setTrunkFreight(importRequest.getTrunkFreightNum());
                            unfullcarCostItem.setDeliveryFee(importRequest.getDeliveryFeeNum());
                            unfullcarCostItem.setFreight(importRequest.getFreightNum());
                            unfullcarCostItem.setPremium(importRequest.getPremiumNum());
                            unfullcarCostItem.setUnloadingFee(importRequest.getUnloadingFeeNum());
                            unfullcarCostItem.setOtherFee(importRequest.getOtherFeeNum());
                            StBeanUtils.makeCreateField(unfullcarCostItem, user);
                            unfullcarCostItemList.add(unfullcarCostItem);
                        }
                    }
                }
            }
        }
    }

    /**
     * 开始日期取当天0点，结束日期取当天23:59:59
     *
     * @param mainObj 主对象
     */
    private void reSetDateTimeRange(StCUnfullcarCost mainObj) {
        if (Objects.isNull(mainObj)) {
            return;
        }
        if (Objects.nonNull(mainObj.getStartDate())) {
            mainObj.setStartDate(DateUtil.beginOfDay(mainObj.getStartDate()));
        }
        if (Objects.nonNull(mainObj.getEndDate())) {
            /*mainObj.setEndDate(DateUtil.endOfDay(mainObj.getEndDate()));*/
            DateTime dateTime = DateUtil.endOfDay(mainObj.getEndDate());
            dateTime.setTime(dateTime.getTime() - 999);
            mainObj.setEndDate(dateTime);
        }
    }

    private void importDataCheck(List<SyncUnfullcarCostImportRequest> importList, List<StImportErrorMsgResult> errorList, Map<String, CpCPhyWarehouse> warehouseMap, Map<String,
            CpLogistics> logisticsMap, Map<String, CpCRegion> provinceMap, Map<String, CpCRegion> cityMap,Map<String,List<SyncUnfullcarCostImportRequest>> groupByMainData) {

        if (log.isDebugEnabled()) {
            log.debug("StCUnfullcarCostImportService-importDataCheck  start");
        }
        SimpleDateFormat format = new SimpleDateFormat("yyyyMMdd");
        format.setLenient(false);

        //已主表信息及城市信息作为key分组
        Map<String,List<SyncUnfullcarCostImportRequest>> importByMainDate = new HashMap<>();
        //检查必填是否为空
        //检查基础档案是否能够匹配
        //子表信息是否允许保存（以主表KEY+城市作为key转map）
        for (int i = 0 ; i < importList.size();i++) {
            StringBuffer sb = new StringBuffer();
            SyncUnfullcarCostImportRequest importRequest = importList.get(i);
            importRequest.setRowNum(i+3);
            if (StringUtils.isEmpty(importRequest.getWarehouseName())) {
                sb.append("仓库信息不能为空！");
            }else {
                CpCPhyWarehouse warehouse = warehouseMap.get(importRequest.getWarehouseName());
                if (warehouse == null) {
                    sb.append("仓库信息与实体仓档案未能匹配！");
                }else {
                    importRequest.setWarehouseId(warehouse.getId());
                }
            }
            if (StringUtils.isEmpty(importRequest.getLogisticsName())) {
                sb.append("物流公司信息不能为空！");
            }else {
                CpLogistics logistics = logisticsMap.get(importRequest.getLogisticsName());
                if (logistics == null) {
                    sb.append("物流公司信息与物流公司档案未能匹配！");
                }else {
                    importRequest.setLogisticsId(logistics.getId());
                }
            }
            if (StringUtils.isEmpty(importRequest.getStartDate())) {
                sb.append("开始日期不能为空！");
            }else {
                boolean flag = isValidDate(importRequest.getStartDate());
                if (flag) {
                    try {
                        importRequest.setStartDateForDate(format.parse(importRequest.getStartDate()));
                    } catch (ParseException e) {
                        sb.append("开始日期格式错误！");
                    }
                }else {
                    sb.append("开始日期格式错误！");
                }
            }
            if (StringUtils.isEmpty(importRequest.getEndDate())) {
                sb.append("结束日期不能为空！");
            }else {
                boolean flag = isValidDate(importRequest.getEndDate());
                if (flag) {
                    try {
                        importRequest.setEndDateForDate(format.parse(importRequest.getEndDate()));
                    } catch (ParseException e) {
                        sb.append("结束日期格式错误！");
                    }
                }else {
                    sb.append("结束日期格式错误！");
                }
            }
            if (StringUtils.isEmpty(importRequest.getProvince())) {
                sb.append("目的省份不能为空！");
            }else {
                CpCRegion province = provinceMap.get(importRequest.getProvince());
                if (province == null) {
                    sb.append("目的省份与省份档案未能匹配！");
                }else {
                    importRequest.setProvinceId(province.getId());
                }
            }
            if (StringUtils.isEmpty(importRequest.getCity())) {
                sb.append("目的城市不能为空");
            }else {
                CpCRegion city = cityMap.get(importRequest.getCity());
                if (city == null) {
                    sb.append("目的城市与城市档案未能匹配！");
                }else {
                    if (city.getCUpId().equals(importRequest.getProvinceId())){
                        importRequest.setCityId(city.getId());
                    }else {
                        sb.append("城市和省份对应关系错误！");
                    }

                }
            }
            if (!StringUtils.isEmpty(importRequest.getOilPriceLinkage())){
                boolean flag = isBigDecimal(importRequest.getOilPriceLinkage());
                if (flag){
                    importRequest.setOilPriceLinkageNum(new BigDecimal(importRequest.getOilPriceLinkage()));
                }else {
                    sb.append("油价联动格式错误！");
                }
            }else {
                importRequest.setOilPriceLinkageNum(new BigDecimal(0));
            }
            if (!StringUtils.isEmpty(importRequest.getArrivalDays())){
                boolean flag = isInteger(importRequest.getArrivalDays());
                if (flag){
                    importRequest.setArrivalDaysNum(new Integer(importRequest.getArrivalDays()));
                }else {
                    sb.append("到货天数格式错误！");
                }
            }else {
                importRequest.setArrivalDaysNum(new Integer(0));
            }

            if (StringUtils.isEmpty(importRequest.getStartWeight())) {
                sb.append("起始重量不能为空！");
            }else {
                boolean flag = isBigDecimal(importRequest.getStartWeight());
                if (flag){
                    importRequest.setStartWeightNum(new BigDecimal(importRequest.getStartWeight()));
                }else {
                    sb.append("起始重量格式错误！");
                }
            }
            if (StringUtils.isEmpty(importRequest.getEndWeight())) {
                sb.append("结束重量不能为空");
            }else {
                boolean flag = isBigDecimal(importRequest.getEndWeight());
                if (flag){
                    importRequest.setEndWeightNum(new BigDecimal(importRequest.getEndWeight()));
                }else {
                    sb.append("结束重量格式错误！");
                }
            }
            if (!StringUtils.isEmpty(importRequest.getTrunkFreight())){
                boolean flag = isBigDecimal(importRequest.getTrunkFreight());
                if (flag){
                    importRequest.setTrunkFreightNum(new BigDecimal(importRequest.getTrunkFreight()));
                }else {
                    sb.append("干线运费格式错误！");
                }
            }else {
                importRequest.setTrunkFreightNum(new BigDecimal(0));
            }
            if (!StringUtils.isEmpty(importRequest.getDeliveryFee())){
                boolean flag = isBigDecimal(importRequest.getDeliveryFee());
                if (flag){
                    importRequest.setDeliveryFeeNum(new BigDecimal(importRequest.getDeliveryFee()));
                }else {
                    sb.append("提货费格式错误！");
                }
            }else {
                importRequest.setDeliveryFeeNum(new BigDecimal(0));
            }
            if (!StringUtils.isEmpty(importRequest.getFreight())){
                boolean flag = isBigDecimal(importRequest.getPremium());
                if (flag){
                    importRequest.setFreightNum(new BigDecimal(importRequest.getFreight()));
                }else {
                    sb.append("送货费格式错误！");
                }
            }else {
                importRequest.setFreightNum(new BigDecimal(0));
            }
//            if (!StringUtils.isEmpty(importRequest.getCustomerBDeliveryFee())){
//                boolean flag = isBigDecimal(importRequest.getCustomerBDeliveryFee());
//                if (flag){
//                    importRequest.setCustomerBDeliveryFeeNum(new BigDecimal(importRequest.getCustomerBDeliveryFee()));
//                }else {
//                    sb.append("A类客户提货费格式错误！");
//                }
//            }else {
//                importRequest.setCustomerBDeliveryFeeNum(new BigDecimal(0));
//            }
            if (!StringUtils.isEmpty(importRequest.getPremium())){
                boolean flag = isBigDecimal(importRequest.getPremium());
                if (flag){
                    importRequest.setPremiumNum(new BigDecimal(importRequest.getPremium()));
                }else {
                    sb.append("保费格式错误！");
                }
            }else {
                importRequest.setPremiumNum(new BigDecimal(0));
            }
            if (!StringUtils.isEmpty(importRequest.getUnloadingFee())){
                boolean flag = isBigDecimal(importRequest.getUnloadingFee());
                if (flag){
                    importRequest.setUnloadingFeeNum(new BigDecimal(importRequest.getUnloadingFee()));
                }else {
                    sb.append("卸货费格式错误！");
                }
            }else {
                importRequest.setUnloadingFeeNum(new BigDecimal(0));
            }
            if (!StringUtils.isEmpty(importRequest.getOtherFee())){
                boolean flag = isBigDecimal(importRequest.getOtherFee());
                if (flag){
                    importRequest.setOtherFeeNum(new BigDecimal(importRequest.getOtherFee()));
                }else {
                    sb.append("其他费用格式错误！");
                }
            }else {
                importRequest.setOtherFeeNum(new BigDecimal(0));
            }
            if (importRequest.getStartDateForDate() != null && importRequest.getEndDateForDate() != null && importRequest.getStartDateForDate().getTime() > importRequest.getEndDateForDate().getTime()) {
                sb.append("开始时间不能大于结束时间！");
            }
            if (importRequest.getStartWeightNum().compareTo(importRequest.getEndWeightNum()) == 1) {
                sb.append("起始重量不能大于结束重量！");
            }
            if (sb.length() > 0) {
                StImportErrorMsgResult errorMsgResult = new StImportErrorMsgResult();
                errorMsgResult.setRowNum(i + 3);
                errorMsgResult.setErrorMsg(sb.toString());
                errorList.add(errorMsgResult);
            }
        }
        if (!CollectionUtils.isEmpty(errorList)) {
            //数据验证有问题
            return;
        }else {
            //数据验证没有问题，开始验证逻辑问题
            for (SyncUnfullcarCostImportRequest importRequest : importList) {
                //城市维度分组
                String key = importRequest.getWarehouseName() + importRequest.getLogisticsName() + importRequest.getStartDate() + importRequest.getEndDate() + importRequest.getOilPriceLinkage() + importRequest.getRemark() + importRequest.getCity();
                if (importByMainDate.containsKey(key)){
                    List<SyncUnfullcarCostImportRequest> requestList = importByMainDate.get(key);
                    requestList.add(importRequest);
                    importByMainDate.put(key,requestList);
                }else {
                    List<SyncUnfullcarCostImportRequest> requestList = new ArrayList<>();
                    requestList.add(importRequest);
                    importByMainDate.put(key,requestList);
                }
                //主表维度分组
                String key1 = importRequest.getWarehouseName() + importRequest.getLogisticsName() + importRequest.getStartDate() + importRequest.getEndDate() + importRequest.getOilPriceLinkage() + importRequest.getRemark();
                if (groupByMainData.containsKey(key1)){
                    List<SyncUnfullcarCostImportRequest> requestList = groupByMainData.get(key1);
                    requestList.add(importRequest);
                    groupByMainData.put(key1,requestList);
                }else {
                    List<SyncUnfullcarCostImportRequest> requestList = new ArrayList<>();
                    requestList.add(importRequest);
                    groupByMainData.put(key1,requestList);
                }
            }
            Integer errRow = 0;
            for (String key : importByMainDate.keySet()) {
                List<SyncUnfullcarCostImportRequest> list = importByMainDate.get(key);
                if (list.size() > 1) {
                    for (int i=0;i < list.size();i++) {
                        for (int j = i+1;j < list.size();j++) {
                            SyncUnfullcarCostImportRequest one = list.get(i);
                            SyncUnfullcarCostImportRequest two = list.get(j);
                            Boolean flag = checkWeight(one.getStartWeightNum(), one.getEndWeightNum(), two.getStartWeightNum(), two.getEndWeightNum());
                            if (!flag) {
                                StImportErrorMsgResult errorMsg = new StImportErrorMsgResult();
                                errorMsg.setRowNum(errRow++);
                                errorMsg.setErrorMsg("第" + one.getRowNum() + "行和第" + two.getRowNum() + "行的起始重量和结束重量存在交叉！");
                                errorList.add(errorMsg);
                            }
                        }
                    }
                }
            }

        }
    }

    /**
     * 判断字符串是否为合法的日期格式
     * @param dateStr 待判断的字符串
     * @return
     */
    public static boolean isValidDate(String dateStr){
        //判断结果 默认为true
        boolean judgeresult=true;
        if (dateStr == null || dateStr.trim().length() != 8) {
            return false;
        }
        for (int i=0;i<dateStr.length();i++){
            if (!Character.isDigit(dateStr.charAt(i))) {
                return false;
            }
        }
        String yearStr=dateStr.substring(0,3);
        if(yearStr.startsWith("0")){
            judgeresult=false;
        }
        return judgeresult;
    }

    /**
     * 判断字符串是否是4位以内整数
     * @param str
     * @return
     */
    private boolean isInteger(String str){
        if (str == null || str.trim().length() == 0 || str.trim().length() > 4) {
            return false;
        }
        for (int i=0;i<str.length();i++){
            if (!Character.isDigit(str.charAt(i))) {
                return false;
            }
        }
        return true;
    }

    /**
     * 判断字符串是否能转成BigDecimal
     * @param str
     * @return
     */
    private boolean isBigDecimal(String str){
        if (str == null || str.trim().length() == 0) {
            return false;
        }
        char[] chars = str.toCharArray();
        int sz = chars.length;
        int i = (chars[0] == '-') ? 1 : 0;
        if (i == sz) {
            return false; //第一位是-号，切长度为1则不行
        }

        if (chars[i] == '.') {
            return false;//除了负号，第一位不能为'小数点'
        }

        boolean radixPoint = false;
        for (; i < sz; i++) {
            if (chars[i] == '.') {
                if (radixPoint) return false;
                radixPoint = true;
            } else if (!(chars[i] >= '0' && chars[i] <= '9')) {
                return false;
            }
        }
        return true;
    }

    private Boolean checkWeight(BigDecimal startWeight, BigDecimal endWeight, BigDecimal startWeight1, BigDecimal endWeight1) {
        if (endWeight.compareTo(endWeight1) == 0) {
            return false;
        }else {
            if (endWeight.compareTo(endWeight1) == 1) {
                //endWeight大，当startWeight小于endWeight1时，返回false
                if (startWeight.compareTo(endWeight1) == -1) {
                    return false;
                }
            }else {
                //endWeight1大，当startWeight1小于endWeight时，返回false
                if (startWeight1.compareTo(endWeight) == -1) {
                    return false;
                }
            }
        }
        return true;
    }

    /**
     * 下载错误信息
     * @param user
     * @param errMsgList
     * @return
     */
    public String downloadImportErrMsg(User user, List<StImportErrorMsgResult> errMsgList) {
        String columnNames[] = {"数据行", "错误原因"};
        List<Integer> columnWidthList = Lists.newArrayList(10, 120);
        List<String> columnList = Lists.newArrayList(columnNames);
        String keys[] = {"rowNum", "errorMsg"};
        List<String> keyList = Lists.newArrayList(keys);
        stCExportUtil.setEndpoint(pconf.getProperty("r3.oss.endpoint"));
        stCExportUtil.setAccessKeyId(pconf.getProperty("r3.oss.accessKey"));
        stCExportUtil.setAccessKeySecret(pconf.getProperty("r3.oss.secretKey"));
        stCExportUtil.setBucketName(pconf.getProperty("r3.oss.bucketName"));
        String timeout = pconf.getProperty("r3.oss.timeout");
        if (StringUtils.isEmpty(timeout)) {
            timeout = "1800000";
        }
        stCExportUtil.setTimeout(timeout);
        // 零担费用设置模板存放地址
        String filePath = "OSS-Bucket/EXPORT/ST_C_UNFULLCAR_COST/";

        Workbook hssfWorkbook = stCExportUtil.execute("错误信息", "零担费用设置导入-错误信息",
                columnList, keyList, errMsgList, columnWidthList, columnList);
        return stCExportUtil.saveFileAndPutOss(hssfWorkbook, "零担费用设置导入-错误信息", user, filePath);
    }

    /**
     * 导出零担费用设置
     * @param ids
     * @return
     */
    public ValueHolderV14<String> exportUnfullcarCost(String ids,User user) {

        ValueHolderV14<String> holderV14 = new ValueHolderV14<>();
        List<Long> idList = Arrays.stream(ids.split(",")).map(s -> Long.parseLong(s.trim())).collect(Collectors.toList());

        //仓库信息
        Map<Long, CpCPhyWarehouse> warehouseMap = new HashMap<>();
        ValueHolderV14<List<CpCPhyWarehouse>> warehourseResult = cpcPhyWareHouseQueryCmd.queryWarehourseIsY();
        if (ResultCode.SUCCESS==warehourseResult.getCode() && !CollectionUtils.isEmpty(warehourseResult.getData())) {
            warehouseMap = warehourseResult.getData().stream().collect(Collectors.toMap(CpCPhyWarehouse::getId, Function.identity(), (x, y) -> y));
        }
        if (CollectionUtils.isEmpty(warehouseMap)) {
            holderV14.setCode(ResultCode.FAIL);
            holderV14.setMessage("请先维护实体仓信息！");
            return holderV14;
        }
        //物流信息
        Map<Long, CpLogistics> logisticsMap = new HashMap<>();
        ValueHolderV14<List<CpLogistics>> logisticsResult = cpLogisticsSelectServiceCmd.queryLogisticsByType(LogisticsTypeEnum.BULK_CARGO_TRANSPORTATION.getKey());
        if (ResultCode.SUCCESS == logisticsResult.getCode() && !CollectionUtils.isEmpty(logisticsResult.getData())) {
            logisticsMap = logisticsResult.getData().stream().collect(Collectors.toMap(CpLogistics::getId, Function.identity(), (x, y) -> y));
        }
        if (CollectionUtils.isEmpty(logisticsMap)) {
            holderV14.setCode(ResultCode.FAIL);
            holderV14.setMessage("请先维护物流公司信息！");
            return holderV14;
        }
        //省份信息
        Map<Long, CpCRegion> provinceMap = new HashMap<>();
        //城市信息
        Map<Long, CpCRegion> cityMap = new HashMap<>();
        List<String> regionTypes = Lists.newArrayList("PROV", "CITY");
        List<CpCRegion> regionList = regionQueryExtCmd.queryAllRegion(regionTypes);
        if (!CollectionUtils.isEmpty(regionList)) {
            Map<String, List<CpCRegion>> listMap = regionList.stream().collect(Collectors.groupingBy(CpCRegion::getRegiontype));
            if (!CollectionUtils.isEmpty(listMap)) {
                provinceMap = listMap.get("PROV").stream().collect(Collectors.toMap(CpCRegion::getId,Function.identity(),(x,y) -> y));
                cityMap = listMap.get("CITY").stream().collect(Collectors.toMap(CpCRegion::getId,Function.identity(),(x,y) -> y));
            }
        }
        if (CollectionUtils.isEmpty(provinceMap)) {
            holderV14.setCode(ResultCode.FAIL);
            holderV14.setMessage("请先维护省份信息！");
            return holderV14;
        }

        if (CollectionUtils.isEmpty(cityMap)) {
            holderV14.setCode(ResultCode.FAIL);
            holderV14.setMessage("请先维护城市信息！");
            return holderV14;
        }

        //循环查询主子表数据，转成SXSSFWorkbook
        XSSFWorkbook workbook = new XSSFWorkbook();
        Sheet sheet = workbook.createSheet("零担报价设置导出");
        List<UnfullcarCostExportPoi> exportPoiList = new ArrayList<>();
        for (int i=0;i < idList.size();i++) {
            exportPoiList = getPoi(idList.get(i),warehouseMap,logisticsMap,provinceMap,cityMap);
            if (i == 0) {
                //第一个POI创建sheet,和表头
                this.createExcel(workbook,exportPoiList,sheet,true,user);
            }else {
                this.createExcel(workbook,exportPoiList,sheet,false,user);
            }
        }

        //上传OSS，并获得下载地址
        log.info(" Start ExportUnfullcarCost.createExcel");
        stCExportUtil.setEndpoint(pconf.getProperty("r3.oss.endpoint"));
        stCExportUtil.setAccessKeyId(pconf.getProperty("r3.oss.accessKey"));
        stCExportUtil.setAccessKeySecret(pconf.getProperty("r3.oss.secretKey"));
        stCExportUtil.setBucketName(pconf.getProperty("r3.oss.bucketName"));
        String timeout = pconf.getProperty("r3.oss.timeout");
        if (StringUtils.isEmpty(timeout)) {
            timeout = "1800000";
        }
        stCExportUtil.setTimeout(timeout);
        // 零担费用设置模板存放地址
        String filePath1 = "OSS-Bucket/EXPORT/ST_C_UNFULLCAR_COST/";
        String fileUrl = stCExportUtil.saveFileAndPutOss(workbook, "零担费用设置", user, filePath1);
        if (StringUtils.isEmpty(fileUrl)) {
            holderV14.setCode(ResultCode.FAIL);
            holderV14.setMessage("导出失败！获得下载地址失败！");
            return holderV14;
        }
        holderV14.setCode(ResultCode.SUCCESS);
        holderV14.setMessage("导出成功");
        holderV14.setData(fileUrl);
        return holderV14;
    }

    /**
     * 创建workBOOOK
     * @param workbook
     * @param exportPoiList
     * @param sheet
     * @param flag 是否是第一条数据
     * @param user
     */
    private void createExcel(XSSFWorkbook workbook, List<UnfullcarCostExportPoi> exportPoiList, Sheet sheet, boolean flag, User user) {
        List<String> columnNames = Lists.newArrayList("仓库", "物流公司", "开始日期", "结束日期", "油价联动（%）", "备注", "目的省份", "目的城市", "到货天数", "起始重量（T/不包含）", "结束重量（T/包含）", "干线运费（元/T）", "提货费（元/票）", "送货费（元/票）", "保费（元/T）", "卸货费（元/T）", "其他费用");
        if (flag) {
            int firstRowNum = 0;
            String titleName = "注意：\r\n" +
                    "1、红色标注项为必填项；\r\n" +
                    "2、日期格式：YYYYMMDD,例20190102； \r\n" +
                    "3、表头（1-2行）不允许修改，否则无法识别；\r\n" +
                    "4、输入时，不允许有空隔，不允许有空行，删除第3行样例数据，否则无法识别；\r\n" +
                    "5、仓库、物流公司、目的省份、目的城市需与基础档案保持一致，否者无法识别；\r\n" +
                    "6、到货天数最大4位整数；\r\n" +
                    "7、油价联动（%）、起始重量（T/不包含）、结束重量（T/包含）、干线运费（元/T）、提货费（元/票）、A类客户提货费、B类客户提货费、保费（元/T）、卸货费（元/T）、其他费用最大16位整数2位小数\r\n";
            ArrayList<String> mustColumnNames = Lists.newArrayList("仓库", "物流公司", "开始日期", "结束日期", "目的省份", "目的城市", "起始重量（T/不包含）", "结束重量（T/包含）");
            // 创建第0行 也就是标题
            sheet.addMergedRegion(new CellRangeAddress(0, 0, 0,
                    columnNames.size()-1)); // 合并列标题
            Row row = sheet.createRow(firstRowNum);
            row.setHeightInPoints(135);// 设备标题的高度
            // 第三步创建标题的单元格样式style2以及字体样式headerFont1
            XSSFCellStyle style = workbook.createCellStyle();
            style.setAlignment(HorizontalAlignment.LEFT);
            style.setVerticalAlignment(VerticalAlignment.CENTER);
//            style2.setFillForegroundColor((short) 0x29);
            //style2.setFillForegroundColor(HSSFColor.HSSFColorPredefined.LIGHT_TURQUOISE.getIndex());
            style.setFillPattern(FillPatternType.SOLID_FOREGROUND);
            style.setWrapText(true); //开启自动换行
            XSSFFont headerFont1 = workbook.createFont(); // 创建字体样式
//            headerFont1.setBold(Boolean.TRUE); // 字体加粗
//            headerFont1.setFontName("黑体"); // 设置字体类型
            headerFont1.setFontHeightInPoints((short) 10); // 设置字体大小
            style.setFont(headerFont1); // 为标题样式设置字体样式
            Cell cell = row.createCell(0);// 创建标题第一列
            cell.setCellValue(titleName); // 设置值标题
            cell.setCellStyle(style); // 设置标题样式
            firstRowNum++;
            // 创建第1行 也就是表头
            Row row1 = sheet.createRow(1);
            row1.setHeightInPoints(15);// 设置表头高度
            for (int i=0;i<columnNames.size();i++) {
                Cell cell1 = row1.createCell(i);
                cell1.setCellValue(columnNames.get(i));
                XSSFCellStyle style1 = workbook.createCellStyle();
                XSSFFont font = workbook.createFont();
                if (mustColumnNames.contains(columnNames.get(i))) {
                    font.setColor(HSSFColor.RED.index);
                }else {
                    font.setColor(HSSFColor.BLACK.index);
                }
                style1.setFont(font);
                cell1.setCellStyle(style1);
//                sheet.autoSizeColumn(i);
//                sheet.setColumnWidth(i,sheet.getColumnWidth(i)*17/10);
            }
        }

        int lastRowNum = sheet.getLastRowNum() + 1;
        for (UnfullcarCostExportPoi exportPoi : exportPoiList) {
            Row row = sheet.createRow(lastRowNum);
            row.setHeightInPoints(15);
            Cell cell1 = row.createCell(0);
            cell1.setCellValue(exportPoi.getWarehouseName());
            Cell cell2 = row.createCell(1);
            cell2.setCellValue(exportPoi.getLogisticsName());
            Cell cell3 = row.createCell(2);
            cell3.setCellValue(exportPoi.getStartDate());
            Cell cell4 = row.createCell(3);
            cell4.setCellValue(exportPoi.getEndDate());
            Cell cell5 = row.createCell(4);
            cell5.setCellValue(exportPoi.getOilPriceLinkage());
            Cell cell6 = row.createCell(5);
            cell6.setCellValue(exportPoi.getRemark());
            Cell cell7 = row.createCell(6);
            cell7.setCellValue(exportPoi.getProvince());
            Cell cell8 = row.createCell(7);
            cell8.setCellValue(exportPoi.getCity());
            Cell cell9 = row.createCell(8);
            cell9.setCellValue(exportPoi.getArrivalDays());
            Cell cell10 = row.createCell(9);
            cell10.setCellValue(exportPoi.getStartWeight());
            Cell cell11 = row.createCell(10);
            cell11.setCellValue(exportPoi.getEndWeight());
            Cell cell12 = row.createCell(11);
            cell12.setCellValue(exportPoi.getTrunkFreight());
            Cell cell13 = row.createCell(12);
            cell13.setCellValue(exportPoi.getDeliveryFee());
            Cell cell14 = row.createCell(13);
            cell14.setCellValue(exportPoi.getFreight());
//            Cell cell15 = row.createCell(14);
//            cell15.setCellValue(exportPoi.getCustomerBDeliveryFee());
            Cell cell16 = row.createCell(14);
            cell16.setCellValue(exportPoi.getPremium());
            Cell cell17 = row.createCell(15);
            cell17.setCellValue(exportPoi.getUnloadingFee());
            Cell cell18 = row.createCell(16);
            cell18.setCellValue(exportPoi.getOtherFee());
            lastRowNum++;
        }
    }

    /**
     * 转换为POI
     * @param id
     * @param warehouseMap
     * @param logisticsMap
     * @param provinceMap
     * @param cityMap
     * @return
     */
    private List<UnfullcarCostExportPoi> getPoi(Long id, Map<Long, CpCPhyWarehouse> warehouseMap, Map<Long, CpLogistics> logisticsMap, Map<Long, CpCRegion> provinceMap, Map<Long, CpCRegion> cityMap) {
        ArrayList<UnfullcarCostExportPoi> exportPoiList = new ArrayList<>();
        //查询主表信息
        StCUnfullcarCost unfullcarCost = stCUnfullcarCostMapper.selectById(id);
        if (unfullcarCost == null) {
            throw new NDSException("主数据不存在或已删除！");
        }
        String warehouseName = null;
        String logisticsName = null;
        String startDate = null;
        String endtDate = null;
        String oilPriceLinkage = null;
        String remark = null;
        if (unfullcarCost.getCpCPhyWarehouseId() != null && warehouseMap.get(unfullcarCost.getCpCPhyWarehouseId()) != null) {
            warehouseName = warehouseMap.get(unfullcarCost.getCpCPhyWarehouseId()).getEname();
        }
        if (unfullcarCost.getCpCLogisticsId() != null && logisticsMap.get(unfullcarCost.getCpCLogisticsId()) != null) {
            logisticsName = logisticsMap.get(unfullcarCost.getCpCLogisticsId()).getEname();
        }
        if (unfullcarCost.getStartDate() != null) {
            startDate = format.format(unfullcarCost.getStartDate());
        }
        if (unfullcarCost.getEndDate() != null) {
            endtDate = format.format(unfullcarCost.getEndDate());
        }
        if (unfullcarCost.getOilPriceLinkage() != null) {
            oilPriceLinkage = unfullcarCost.getOilPriceLinkage().stripTrailingZeros().toPlainString();
        }
        if (unfullcarCost.getRemark() != null) {
            remark = unfullcarCost.getRemark();
        }
        List<StCUnfullcarCostItem> costItemList = stCUnfullcarCostItemMapper.selectList(new LambdaQueryWrapper<StCUnfullcarCostItem>()
                .eq(StCUnfullcarCostItem::getUnfullcarCostId, id));
        if (CollectionUtils.isEmpty(costItemList)) {
            UnfullcarCostExportPoi exportPoi = new UnfullcarCostExportPoi();
            exportPoi.setWarehouseName(warehouseName);
            exportPoi.setLogisticsName(logisticsName);
            exportPoi.setStartDate(startDate);
            exportPoi.setEndDate(endtDate);
            exportPoi.setOilPriceLinkage(oilPriceLinkage);
            exportPoi.setRemark(remark);
            exportPoiList.add(exportPoi);
            return exportPoiList;
        }else {
            for (StCUnfullcarCostItem costItem : costItemList) {
                UnfullcarCostExportPoi exportPoi = new UnfullcarCostExportPoi();
                exportPoi.setWarehouseName(warehouseName);
                exportPoi.setLogisticsName(logisticsName);
                exportPoi.setStartDate(startDate);
                exportPoi.setEndDate(endtDate);
                exportPoi.setOilPriceLinkage(oilPriceLinkage);
                exportPoi.setRemark(remark);
                if (costItem.getProvinceId() != null && provinceMap.get(costItem.getProvinceId()) != null) {
                    exportPoi.setProvince(provinceMap.get(costItem.getProvinceId()).getEname());
                }
                if (costItem.getCityId() != null && cityMap.get(costItem.getCityId()) != null) {
                    exportPoi.setCity(cityMap.get(costItem.getCityId()).getEname());
                }
                if (costItem.getArrivalDays() != null) {
                    exportPoi.setArrivalDays(costItem.getArrivalDays().toString());
                }
                if (costItem.getStartWeight() != null) {
                    exportPoi.setStartWeight(costItem.getStartWeight().stripTrailingZeros().toPlainString());
                }
                if (costItem.getEndWeight() != null) {
                    exportPoi.setEndWeight(costItem.getEndWeight().stripTrailingZeros().toPlainString());
                }
                if (costItem.getTrunkFreight() != null) {
                    exportPoi.setTrunkFreight(costItem.getTrunkFreight().stripTrailingZeros().toPlainString());
                }
                if (costItem.getDeliveryFee() != null) {
                    exportPoi.setDeliveryFee(costItem.getDeliveryFee().stripTrailingZeros().toPlainString());
                }
                if (costItem.getFreight() != null) {
                    exportPoi.setFreight(costItem.getFreight().stripTrailingZeros().toPlainString());
                }
//                if (costItem.getCustomerADeliveryFee() != null) {
//                    exportPoi.setCustomerADeliveryFee(costItem.getCustomerADeliveryFee().stripTrailingZeros().toPlainString());
//                }
//                if (costItem.getCustomerBDeliveryFee() != null) {
//                    exportPoi.setCustomerBDeliveryFee(costItem.getCustomerBDeliveryFee().stripTrailingZeros().toPlainString());
//                }
                if (costItem.getPremium() != null) {
                    exportPoi.setPremium(costItem.getPremium().stripTrailingZeros().toPlainString());
                }
                if (costItem.getUnloadingFee() != null) {
                    exportPoi.setUnloadingFee(costItem.getUnloadingFee().stripTrailingZeros().toPlainString());
                }
                if (costItem.getOtherFee() != null) {
                    exportPoi.setOtherFee(costItem.getOtherFee().stripTrailingZeros().toPlainString());
                }
                exportPoiList.add(exportPoi);
            }
        }
        return exportPoiList;
    }
}
