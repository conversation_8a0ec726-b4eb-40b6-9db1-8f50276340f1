package com.jackrain.nea.st.services;

import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.st.mapper.AdLimitvalueMapper;
import com.jackrain.nea.st.mapper.StCVipcomMailMapper;
import com.jackrain.nea.st.model.table.StCVipcomMailDO;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @description: 唯品会邮件设置获取字段查询接口
 * @author: 田钦华
 * @date: 2019/4/23 17:09
 */
@Component
@Slf4j
public class VipcomMailSetColumnSelectService {
    @Autowired
    private AdLimitvalueMapper dLimitvalueMapper;

    @Autowired
    private StCVipcomMailMapper stCVipcomMailMapper;

    public ValueHolderV14<StCVipcomMailDO> selectMailDatasByid(Long id) {
           //主键id，用于查询明细信息
            return selectVipMailSelectFunc(id);//根据id获取邮件标题和内容
    }
    /**
     * @param id
     * @return
     * @Author: 田钦华
     * @Date 2019/4/24
     */

    private ValueHolderV14<StCVipcomMailDO> selectVipMailSelectFunc(Long id) {
        ValueHolderV14<StCVipcomMailDO> valueHolderV14 = new ValueHolderV14();
        StCVipcomMailDO stCVipcomMailDOList = stCVipcomMailMapper.selectById(id);
        if(stCVipcomMailDOList != null ){
            valueHolderV14.setCode(ResultCode.SUCCESS);
            valueHolderV14.setMessage("查询成功！");
            valueHolderV14.setData(stCVipcomMailDOList);
        }else{
            valueHolderV14.setCode(ResultCode.FAIL);
            valueHolderV14.setMessage("无次记录数据！");
            valueHolderV14.setData(null);
        }
        return valueHolderV14;
    }

}
