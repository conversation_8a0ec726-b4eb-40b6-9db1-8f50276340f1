package com.jackrain.nea.st.services;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.st.common.StConstant;
import com.jackrain.nea.st.mapper.StCLockSkuStrategyItemMapper;
import com.jackrain.nea.st.model.request.StCLockSkuStrategyItemRequest;
import com.jackrain.nea.st.model.table.StCLockSkuStrategyItemDO;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.utility.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * @Author: ganquan
 * @Date Create In 2020/12/18 17:36
 * @Description: 锁库条码策略查询
 */
@Slf4j
@Component
public class LockSkuStrategyQueryService {

    @Autowired
    private StCLockSkuStrategyItemMapper stCLockSkuStrategyItemMapper;

    /**
     * <AUTHOR>
     * @Description 查询有效锁库条码策略明细
     * @Date 16:07 2020/12/18
     * @param request
     * @return com.jackrain.nea.sys.domain.ValueHolderV14<com.jackrain.nea.st.model.table.StCLockSkuStrategyItemDO>
     **/
    public ValueHolderV14<List<StCLockSkuStrategyItemDO>> queryLockSkuStrategyItemValid(StCLockSkuStrategyItemRequest request) {
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("【查询有效锁库条码策略明细入参】{}"), JSONObject.toJSON(request));
        }
        ValueHolderV14<List<StCLockSkuStrategyItemDO>> valueHolder14 = new ValueHolderV14();
        valueHolder14.setCode(ResultCode.SUCCESS);
        valueHolder14.setMessage("成功");
        String errorMes = checkLockSkuStrategyItemRequest(request);
        if (StringUtils.isNotEmpty(errorMes)) {
            valueHolder14.setCode(ResultCode.FAIL);
            valueHolder14.setMessage(errorMes);
            return valueHolder14;
        }
        LambdaQueryWrapper<StCLockSkuStrategyItemDO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(StCLockSkuStrategyItemDO::getCpCShopId, request.getCpCShopId());
        wrapper.eq(StCLockSkuStrategyItemDO::getStatus, StConstant.CON_BILL_STATUS_02);
        wrapper.le(StCLockSkuStrategyItemDO::getBeginTime, request.getExpireTime());
        wrapper.ge(StCLockSkuStrategyItemDO::getEndTime, request.getExpireTime());
        wrapper.and(queryWrapper -> queryWrapper
                .eq(StCLockSkuStrategyItemDO::getPsCSkuId, request.getPsCSkuId()).or()
                .eq(StCLockSkuStrategyItemDO::getPtSkuId, request.getPtSkuId()).or()
                .eq(StCLockSkuStrategyItemDO::getPtProId, request.getPtProId())
        );
        List<StCLockSkuStrategyItemDO> itemList = stCLockSkuStrategyItemMapper.selectList(wrapper);
        if (CollectionUtils.isNotEmpty(itemList)) {
            itemList = itemList.stream().filter(obj -> {
                // 1. 中台sku不一致的过滤
                if(obj.getPsCSkuId() != null && !obj.getPsCSkuId().equals(request.getPsCSkuId())){
                    return false;
                }
                // 2. 存在平台skuid，但是和渠道库存缓存池中不一致的过滤
                if (StringUtils.isNotEmpty(obj.getPtSkuId()) && !StringUtils.equals(obj.getPtSkuId(), request.getPtSkuId())) {
                    return false;
                }
                // 3.平台skuid为空且平台商品id有值，但是和渠道库存缓存池中不一致的过滤
                if (StringUtils.isNotEmpty(obj.getPtProId()) && !StringUtils.equals(obj.getPtProId(), request.getPtProId())) {
                    return false;
                }
                return true;
            }).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(itemList)) {
                valueHolder14.setData(itemList);
            }
        }
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("【查询有效锁库条码策略明细出参】{}"), JSONObject.toJSON(itemList));
        }
        return valueHolder14;
    }

    /**
     * <AUTHOR>
     * @Description 入参校验
     * @Date 17:21 2020/12/18
     * @param request
     * @return java.lang.String
     **/
    private String checkLockSkuStrategyItemRequest(StCLockSkuStrategyItemRequest request) {
        if (request.getCpCShopId() == null) {
            return "店铺不允许为空";
        }
        if (request.getExpireTime() == null) {
            return "当前时间不允许为空";
        }
        if (request.getPsCSkuId() == null) {
            return "中台sku不允许为空";
        }
        if (StringUtils.isEmpty(request.getPtProId())) {
            return "平台商品不允许为空";
        }
        if (StringUtils.isEmpty(request.getPtSkuId())) {
            return "平台sku不允许为空";
        }
        return null;
    }

}
