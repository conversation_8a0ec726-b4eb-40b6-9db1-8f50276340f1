package com.jackrain.nea.st.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.alibaba.fastjson.parser.Feature;
import com.google.common.base.Throwables;
import com.google.common.collect.Lists;
import com.jackrain.nea.common.ReferenceUtil;
import com.jackrain.nea.cpext.api.GeneralOrganizationCmd;
import com.jackrain.nea.cpext.model.table.CpShop;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.psext.api.SkuLikeQueryCmd;
import com.jackrain.nea.psext.request.SkuQueryListRequest;
import com.jackrain.nea.st.api.SyncStockStrategyQueryServiceCmd;
import com.jackrain.nea.st.common.StConstant;
import com.jackrain.nea.st.mapper.StCSyncSkustockProMapper;
import com.jackrain.nea.st.mapper.StCSyncSkustockStoreMapper;
import com.jackrain.nea.st.mapper.StCSyncSkustockStrategyMapper;
import com.jackrain.nea.st.model.result.CpCOrgChannelResult;
import com.jackrain.nea.st.model.table.StCSyncSkustockProDO;
import com.jackrain.nea.st.model.table.StCSyncSkustockStoreDO;
import com.jackrain.nea.st.model.table.StCSyncSkustockStrategyDO;
import com.jackrain.nea.st.utils.JsonUtils;
import com.jackrain.nea.st.utils.StBeanUtils;
import com.jackrain.nea.st.utils.ValueHolderUtils;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2020/5/18 21:29
 * 店铺条码库存同步策略ADD
 */
@Component
@Slf4j
@Transactional
public class StCSyncSkustockStrategyService extends CommandAdapter {
    @Autowired
    private StCSyncSkustockStrategyMapper stCSyncSkustockStrategyMapper;

    @Autowired
    private StCSyncSkustockProMapper stCSyncSkustockProMapper;

    @Autowired
    private StCSyncSkustockStoreMapper stCSyncSkustockStoreMapper;

    @Autowired
    private SyncStockStrategyQueryServiceCmd strategyQueryServiceCmd;

    @Override
    public ValueHolder execute(QuerySession querySession) {
        ValueHolder valueHolder = new ValueHolder();
        DefaultWebEvent event = querySession.getEvent();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(
                event.getParameterValue("param"), "yyyy-MM-dd HH:mm:ss"),
                Feature.OrderedField);
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("店铺条码库存同步策略保存参数：") + param);
        }
        if (param != null) {
            Long id = param.getLong("objid");
            JSONObject fixColumn = param.getJSONObject("fixcolumn");
            if (fixColumn != null) {
                JSONObject mainTableMap = fixColumn.getJSONObject(StConstant.TAB_ST_C_SYNC_SKUSTOCK_STRATEGY);
                JSONArray subProTableMap = fixColumn.getJSONArray(StConstant.TAB_ST_C_SYNC_SKUSTOCK_PRO);
                JSONArray subStoreTableMap = fixColumn.getJSONArray(StConstant.TAB_ST_C_SYNC_SKUSTOCK_STORE);
                if (id != null && id < 0) {
                    valueHolder = insertStCSyncSkuStockStrategy(mainTableMap, subProTableMap, subStoreTableMap, valueHolder, querySession, id);
                } else {
                    valueHolder = updateStCSyncSkustockStrategy(mainTableMap, subProTableMap, subStoreTableMap, valueHolder, querySession, id);
                }
            }
        } else {
            throw new NDSException("参数为空！");
        }

        return valueHolder;
    }

    /**
     * 新增
     *
     * @param mainTableMap
     * @param subProTableMap
     * @param subStoreTableMap
     * @param holder
     * @param querySession
     * @param objid
     * @return
     */
    private ValueHolder insertStCSyncSkuStockStrategy(JSONObject mainTableMap, JSONArray subProTableMap, JSONArray subStoreTableMap, ValueHolder holder,
                                                      QuerySession querySession, Long objid) {
        StCSyncSkustockStrategyDO stCSyncSkustockStrategyDO = JsonUtils.jsonParseClass(mainTableMap, StCSyncSkustockStrategyDO.class);
        //状态数据检查
        if (!checkStatus(mainTableMap, stCSyncSkustockStrategyDO, objid, holder)) {
            return holder;
        }
        Long id = null;
        if (stCSyncSkustockStrategyDO != null) {
            id = stCSyncSkustockStrategyDO.getId();
            stCSyncSkustockStrategyDO.setId(ModelUtil.getSequence(StConstant.TAB_ST_C_SYNC_SKUSTOCK_STRATEGY));
            stCSyncSkustockStrategyDO.setBillStatus(StConstant.SKUSTOCK_STATUS_01);
            GeneralOrganizationCmd eneralOrganizationCmd =
                    (GeneralOrganizationCmd) ReferenceUtil.refer(ApplicationContextHandle.getApplicationContext(),
                            GeneralOrganizationCmd.class.getName(), "cp-ext", "1.0");
            try {
                CpShop cpShop = eneralOrganizationCmd.queryShopById(stCSyncSkustockStrategyDO.getCpCShopId());
                if (cpShop != null) {
                    stCSyncSkustockStrategyDO.setCpCShopEcode(cpShop.getEcode());
                    stCSyncSkustockStrategyDO.setCpCShopTitle(cpShop.getCpCShopTitle());
                }
            } catch (Exception e) {
                log.error(LogUtil.format("根据店铺ID查询店铺信息异常{}"), Throwables.getStackTraceAsString(e));
            }
            StBeanUtils.makeCreateField(stCSyncSkustockStrategyDO, querySession.getUser());
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("主表新增参数：{}"), stCSyncSkustockStrategyDO);
            }
            if (stCSyncSkustockStrategyMapper.insert(stCSyncSkustockStrategyDO) < 0) {
                holder = ValueHolderUtils.getFailValueHolder("保存主表信息失败！");
                return holder;
            }
        }
        if (!saveOrUpdateSubTable(holder, querySession, stCSyncSkustockStrategyDO, subProTableMap, subStoreTableMap)) {
            return holder;
        }
        holder = ValueHolderUtils.getSuccessValueHolder(id, StConstant.TAB_ST_C_SYNC_SKUSTOCK_STRATEGY);
        return holder;
    }

    /**
     * 更新
     *
     * @param mainTableMap
     * @param subProTableMap
     * @param subStoreTableMap
     * @param holder
     * @param querySession
     * @param objid
     * @return
     */
    private ValueHolder updateStCSyncSkustockStrategy(JSONObject mainTableMap, JSONArray subProTableMap, JSONArray subStoreTableMap, ValueHolder holder,
                                                      QuerySession querySession, Long objid) {

        StCSyncSkustockStrategyDO stCSyncSkustockStrategyDO = JsonUtils.jsonParseClass(mainTableMap, StCSyncSkustockStrategyDO.class);
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("更新入参：" + stCSyncSkustockStrategyDO + ",==" + (stCSyncSkustockStrategyDO != null)));
        }
        if (stCSyncSkustockStrategyDO != null) {
            //状态数据检查
            if (!checkStatus(mainTableMap, stCSyncSkustockStrategyDO, objid, holder)) {
                return holder;
            }
            stCSyncSkustockStrategyDO.setId(objid);
            StBeanUtils.makeModifierField(stCSyncSkustockStrategyDO, querySession.getUser());
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("主表更新参数：") + stCSyncSkustockStrategyDO);
            }
            if (stCSyncSkustockStrategyMapper.updateById(stCSyncSkustockStrategyDO) < 0) {
                holder = ValueHolderUtils.getFailValueHolder("更新主表信息失败！");
                return holder;
            }
        } else {
            stCSyncSkustockStrategyDO = stCSyncSkustockStrategyMapper.selectById(objid);
        }
        if (!saveOrUpdateSubTable(holder, querySession, stCSyncSkustockStrategyDO, subProTableMap, subStoreTableMap)) {
            return holder;
        }
        holder = ValueHolderUtils.getSuccessValueHolder(stCSyncSkustockStrategyDO.getId(), StConstant.TAB_ST_C_SYNC_SKUSTOCK_STRATEGY);
        return holder;
    }

    /**
     * 子表新增或更新
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean saveOrUpdateSubTable(ValueHolder holder, QuerySession querySession,
                                        StCSyncSkustockStrategyDO stCSyncSkustockStrategyDO,
                                        JSONArray subProTableMap,
                                        JSONArray subStoreTableMap) {
        if (subProTableMap != null) {
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("subProTableMap:") + subProTableMap);
            }
            Boolean aBoolean = saveProInfo(holder, querySession, stCSyncSkustockStrategyDO, subProTableMap);
            if (!aBoolean) {
                return false;
            }
        }
        //供货仓信息新增
        if (subStoreTableMap != null) {
            List<StCSyncSkustockStoreDO> stCSyncSkustockStoreDOS = JSON.parseObject(subStoreTableMap.toJSONString(),
                    new TypeReference<ArrayList<StCSyncSkustockStoreDO>>() {
                    });
            if (CollectionUtils.isNotEmpty(stCSyncSkustockStoreDOS)) {
                List<StCSyncSkustockStoreDO> stCSyncSkustockStoreDOInset = new ArrayList<StCSyncSkustockStoreDO>();
                for (StCSyncSkustockStoreDO stCSyncSkustockStoreDO : stCSyncSkustockStoreDOS) {
                    Long cpCStoreId = stCSyncSkustockStoreDO.getCpCStoreId();
                    HashMap<String, Object> map = new HashMap<>();
                    map.put("CP_C_STORE_ID", cpCStoreId);
                    map.put("ST_C_SYNC_SKUSTOCK_STRATEGY_ID", stCSyncSkustockStrategyDO.getId());
                    if (stCSyncSkustockStoreMapper.selectByMap(map).isEmpty()) {
                        List<CpCOrgChannelResult> cpCOrgChannelResults = strategyQueryServiceCmd.querySyncStockStrategyStore(stCSyncSkustockStrategyDO.getCpCShopId());
                        if (CollectionUtils.isNotEmpty(cpCOrgChannelResults)) {
                            List<Long> storeIds = cpCOrgChannelResults.stream().map(CpCOrgChannelResult::getCpCStoreId).collect(Collectors.toList());
                            boolean present = cpCOrgChannelResults.stream().filter(m -> m.getCpCStoreId().equals(cpCStoreId)).findAny().isPresent();
                            if (!present) {
                                holder.put("code", -1);
                                holder.put("message", "供货仓不是该店铺的供货仓！");
                                return false;
                            }
                        } else {
                            holder.put("code", -1);
                            holder.put("message", "该店铺下没有供货仓！");
                            return false;
                        }
                        //过滤出是该店铺的供货仓
                        List<CpCOrgChannelResult> collect = cpCOrgChannelResults.stream().filter(s -> s.getCpCStoreId().equals(cpCStoreId)).collect(Collectors.toList());
                        if (CollectionUtils.isNotEmpty(collect)) {
                            if (stCSyncSkustockStoreDO.getAreatype() != null && !stCSyncSkustockStoreDO.getAreatype().equals(collect.get(0).getAreatype())) {
                                holder.put("code", -1);
                                holder.put("message", "该供货仓不属于所选所属区域！");
                                return false;
                            }
                        }
                        StCSyncSkustockStoreDO stCSyncSkustockStoreDONew = new StCSyncSkustockStoreDO();
                        BeanUtils.copyProperties(stCSyncSkustockStoreDO, stCSyncSkustockStoreDONew);
                        stCSyncSkustockStoreDONew.setId(ModelUtil.getSequence(StConstant.TAB_ST_C_SYNC_SKUSTOCK_STORE));
                        stCSyncSkustockStoreDONew.setStCSyncSkustockStrategyId(stCSyncSkustockStrategyDO.getId());
                        stCSyncSkustockStoreDONew.setIsactive(StConstant.ISACTIVE_Y);
                        stCSyncSkustockStoreDONew.setCpCStoreId(cpCStoreId);
                        stCSyncSkustockStoreDONew.setCpCStoreEcode(collect.get(0).getCpCStoreEcode());
                        stCSyncSkustockStoreDONew.setCpCStoreEname(collect.get(0).getCpCStoreEname());
                        stCSyncSkustockStoreDONew.setAreatype(collect.get(0).getAreatype());
//                        GeneralOrganizationCmd eneralOrganizationCmd =
//                                (GeneralOrganizationCmd) ReferenceUtil.refer(ApplicationContextHandle.getApplicationContext(),
//                                        GeneralOrganizationCmd.class.getName(), "cp-ext", "1.0");
//                        try {
//                            CpStore cpStore = eneralOrganizationCmd.selectCpCStoreById(cpCStoreId);
//                            if (cpStore != null) {
//                                stCSyncSkustockStoreDONew.setCpCStoreEcode(cpStore.getEcode());
//                                stCSyncSkustockStoreDONew.setCpCStoreEname(cpStore.getEname());
//                            }
//                        } catch (Exception e) {
//                            log.debug(LogUtil.format("根据供货仓ID查询逻辑仓信息异常：" + e.getMessage());
//                            e.printStackTrace();
//                        }
                        StBeanUtils.makeCreateField(stCSyncSkustockStoreDONew, querySession.getUser());
                        stCSyncSkustockStoreDOInset.add(stCSyncSkustockStoreDONew);
                    } else {
                        holder.put("code", -1);
                        holder.put("message", "供货仓【" + cpCStoreId + "】已经存在！");
                        return false;
                    }
                }
                if (log.isDebugEnabled()) {
                    log.debug(LogUtil.format("店铺条码库存同步策略供货仓信息保存参数：") + stCSyncSkustockStoreDOInset);
                }
                if (CollectionUtils.isNotEmpty(stCSyncSkustockStoreDOInset)) {
                    if (stCSyncSkustockStoreMapper.batchInsert(stCSyncSkustockStoreDOInset) < 0) {
                        holder.put("code", -1);
                        holder.put("message", "供货仓保存失败！");
                        return false;
                    }
                }
            }
        }
        return true;
    }

    public Boolean saveProInfo(ValueHolder holder, QuerySession querySession, StCSyncSkustockStrategyDO stCSyncSkustockStrategyDO, JSONArray subProTableMap) {
        //商品信息新增
        List<StCSyncSkustockProDO> stCSyncSkustockProDOList = JSON.parseObject(subProTableMap.toJSONString(),
                new TypeReference<ArrayList<StCSyncSkustockProDO>>() {
                });
        if (CollectionUtils.isNotEmpty(stCSyncSkustockProDOList)) {
            SkuLikeQueryCmd skuLikeQueryCmd =
                    (SkuLikeQueryCmd) ReferenceUtil.refer(ApplicationContextHandle.getApplicationContext(),
                            SkuLikeQueryCmd.class.getName(), "ps-ext", "1.0");
            List<StCSyncSkustockProDO> stCSyncSkustockProDOListInset = new ArrayList<StCSyncSkustockProDO>();
            for (StCSyncSkustockProDO stCSyncSkustockProDO : stCSyncSkustockProDOList) {
                //判断方案是否存在  店铺+条码
                int count = stCSyncSkustockStrategyMapper.selectIsExist(stCSyncSkustockStrategyDO.getCpCShopId(), stCSyncSkustockProDO.getPsCSkuId());
                if (count > 0) {
                    holder.put("code", -1);
                    holder.put("message", "店铺+条码【" + stCSyncSkustockStrategyDO.getCpCShopTitle() + "," + stCSyncSkustockProDO.getPsCSkuId() + "】该方案已经存在！");
                    return false;
                }
                Integer psCSkuId = Math.toIntExact(stCSyncSkustockProDO.getPsCSkuId());
                if (psCSkuId != null) {
                    List<Integer> ids = Lists.newArrayList();
                    ids.add(psCSkuId);
                    List<SkuQueryListRequest> skuQueryListRequests = skuLikeQueryCmd.querySkuByIds(ids);
                    if (CollectionUtils.isNotEmpty(skuQueryListRequests)) {
                        StCSyncSkustockProDO stCSyncSkustockProDONew = new StCSyncSkustockProDO();
                        BeanUtils.copyProperties(stCSyncSkustockProDO, stCSyncSkustockProDONew);
                        stCSyncSkustockProDONew.setId(ModelUtil.getSequence(StConstant.TAB_ST_C_SYNC_SKUSTOCK_PRO));
                        stCSyncSkustockProDONew.setStCSyncSkustockStrategyId(stCSyncSkustockStrategyDO.getId());
                        stCSyncSkustockProDONew.setGbcode(skuQueryListRequests.get(0).getGbcode());
                        stCSyncSkustockProDONew.setPsCProId(skuQueryListRequests.get(0).getPsCProId());
                        stCSyncSkustockProDONew.setPsCProEcode(skuQueryListRequests.get(0).getPsCProEcode());
                        stCSyncSkustockProDONew.setPsCProEname(skuQueryListRequests.get(0).getPsCProEname());
                        stCSyncSkustockProDONew.setPsCSkuId(skuQueryListRequests.get(0).getId());
                        stCSyncSkustockProDONew.setPsCSkuEcode(skuQueryListRequests.get(0).getEcode());
                        stCSyncSkustockProDONew.setPsCClrId(skuQueryListRequests.get(0).getColorId());
                        stCSyncSkustockProDONew.setPsCClrEcode(skuQueryListRequests.get(0).getColorEcode());
                        stCSyncSkustockProDONew.setPsCClrEname(skuQueryListRequests.get(0).getColorName());
                        stCSyncSkustockProDONew.setPsCSizeId(skuQueryListRequests.get(0).getSizeId());
                        stCSyncSkustockProDONew.setPsCSizeEcode(skuQueryListRequests.get(0).getSizeEcode());
                        stCSyncSkustockProDONew.setPsCSizeEname(skuQueryListRequests.get(0).getSizeName());
                        stCSyncSkustockProDONew.setIsactive(StConstant.ISACTIVE_Y);
                        StBeanUtils.makeCreateField(stCSyncSkustockProDONew, querySession.getUser());
                        stCSyncSkustockProDOListInset.add(stCSyncSkustockProDONew);
                    } else {
                        holder.put("code", -1);
                        holder.put("message", "条码【" + psCSkuId + "】不存在！");
                        return false;
                    }
                }
            }
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("店铺条码库存同步策略商品信息保存参数：") + stCSyncSkustockProDOListInset);
            }
            if (CollectionUtils.isNotEmpty(stCSyncSkustockProDOListInset)) {
                if (stCSyncSkustockProMapper.batchInsert(stCSyncSkustockProDOListInset) < 0) {
                    holder.put("code", -1);
                    holder.put("message", "商品信息保存失败！");
                    return false;
                }
            }
        } else {
            holder.put("code", -1);
            holder.put("message", "明细不存在！");
            return false;
        }
        return true;
    }

    /**
     * 单据校验
     *
     * @param stCSyncSkustockStrategyDO
     * @param objid
     * @param valueHolder
     * @return
     */
    private boolean checkStatus(JSONObject mainTableMap, StCSyncSkustockStrategyDO stCSyncSkustockStrategyDO, Long objid, ValueHolder valueHolder) {
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("单据校验1：") + mainTableMap);
            log.debug(LogUtil.format("单据校验2：") + stCSyncSkustockStrategyDO);
        }
        if (objid > 0) {
            StCSyncSkustockStrategyDO stCSyncSkustockStrategy = stCSyncSkustockStrategyMapper.selectById(objid);
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("stCSyncSkustockStrategy：") + stCSyncSkustockStrategy);
            }
            if (stCSyncSkustockStrategy != null) {
                Integer iStatus = stCSyncSkustockStrategy.getBillStatus();
                if (iStatus != 1) {
                    valueHolder.put("code", -1);
                    valueHolder.put("message", "单据处于未审核状态才能进行编辑！");
                    return false;
                }
            } else {
                valueHolder.put("code", -1);
                valueHolder.put("message", "数据已不存在");
                return false;
            }
            Date beginTime = null;
            Date endTime = null;
            if (mainTableMap.containsKey("BEGIN_TIME") && mainTableMap.containsKey("END_TIME")) {
                // 1、判断必填项是否必填  开始时间要小于结束时间
                beginTime = stCSyncSkustockStrategyDO.getBeginTime();
                endTime = stCSyncSkustockStrategyDO.getEndTime();
                if (beginTime == null || endTime == null) {
                    valueHolder.put("code", -1);
                    valueHolder.put("message", "结束日期或开始日期不能为空！");
                    return false;
                }
            } else if (mainTableMap.containsKey("BEGIN_TIME") && !mainTableMap.containsKey("END_TIME")) {
                // 1、判断必填项是否必填  开始时间要小于结束时间
                beginTime = stCSyncSkustockStrategyDO.getBeginTime();
                endTime = stCSyncSkustockStrategy.getEndTime();
                if (beginTime == null || endTime == null) {
                    valueHolder.put("code", -1);
                    valueHolder.put("message", "结束日期或开始日期不能为空！");
                    return false;
                }
            } else if (!mainTableMap.containsKey("BEGIN_TIME") && mainTableMap.containsKey("END_TIME")) {
                // 1、判断必填项是否必填  开始时间要小于结束时间
                beginTime = stCSyncSkustockStrategy.getBeginTime();
                endTime = stCSyncSkustockStrategyDO.getEndTime();
                if (beginTime == null || endTime == null) {
                    valueHolder.put("code", -1);
                    valueHolder.put("message", "结束日期或开始日期不能为空！");
                    return false;
                }
            }
            if (beginTime != null) {
                stCSyncSkustockStrategyDO.setBeginTime(beginTime);
            }
            if (endTime != null) {
                stCSyncSkustockStrategyDO.setEndTime(endTime);
            }

            if (mainTableMap.containsKey("CP_C_SHOP_ID")) {
                GeneralOrganizationCmd eneralOrganizationCmd =
                        (GeneralOrganizationCmd) ReferenceUtil.refer(ApplicationContextHandle.getApplicationContext(),
                                GeneralOrganizationCmd.class.getName(), "cp-ext", "1.0");
                try {
                    CpShop cpShop = eneralOrganizationCmd.queryShopById(stCSyncSkustockStrategyDO.getCpCShopId());
                    if (cpShop != null) {
                        stCSyncSkustockStrategyDO.setCpCShopId(stCSyncSkustockStrategyDO.getCpCShopId());
                        stCSyncSkustockStrategyDO.setCpCShopEcode(cpShop.getEcode());
                        stCSyncSkustockStrategyDO.setCpCShopTitle(cpShop.getCpCShopTitle());
                    }
                } catch (Exception e) {
                    log.debug(LogUtil.format("根据店铺ID查询店铺信息异常：{}"), Throwables.getStackTraceAsString(e));
                }
            }
            if (mainTableMap.containsKey("TEMPLATE_NAME")) {
                HashMap<String, Object> map = new HashMap<>();
                map.put("TEMPLATE_NAME", stCSyncSkustockStrategyDO.getTemplateName());
                if (!stCSyncSkustockStrategyMapper.selectByMap(map).isEmpty()) {
                    valueHolder.put("code", -1);
                    valueHolder.put("message", "方案名称已经存在！");
                    return false;
                } else {
                    stCSyncSkustockStrategyDO.setTemplateName(stCSyncSkustockStrategyDO.getTemplateName());
                }
            }
        } else {
            // 1、判断必填项是否必填  开始时间要小于结束时间
            Date beginTime = stCSyncSkustockStrategyDO.getBeginTime();
            Date endTime = stCSyncSkustockStrategyDO.getEndTime();
            if (beginTime == null || endTime == null) {
                valueHolder.put("code", -1);
                valueHolder.put("message", "结束日期或开始日期不能为空！");
                return false;
            }
            if (beginTime != null && endTime != null) {
                if (endTime.compareTo(beginTime) < 0) {
                    valueHolder.put("code", -1);
                    valueHolder.put("message", "结束日期不能小于开始日期！");
                    return false;
                }
            }
            Long cpCShopId = stCSyncSkustockStrategyDO.getCpCShopId();
            if (cpCShopId == null) {
                valueHolder.put("code", -1);
                valueHolder.put("message", "店铺不能为空！");
                return false;
            }
            HashMap<String, Object> map = new HashMap<>();
            String templateName = stCSyncSkustockStrategyDO.getTemplateName();
            map.put("TEMPLATE_NAME", templateName);
            if (StringUtils.isBlank(templateName)) {
                valueHolder.put("code", -1);
                valueHolder.put("message", "方案名称不能为空！");
                return false;
            }
            if (!stCSyncSkustockStrategyMapper.selectByMap(map).isEmpty()) {
                valueHolder.put("code", -1);
                valueHolder.put("message", "方案名称已经存在！");
                return false;
            }
            map.clear();
        }
        return true;
    }
}
