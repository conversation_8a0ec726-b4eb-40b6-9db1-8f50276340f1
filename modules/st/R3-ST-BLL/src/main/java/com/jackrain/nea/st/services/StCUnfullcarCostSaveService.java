package com.jackrain.nea.st.services;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.st.annotation.StOperationLog;
import com.jackrain.nea.st.common.StConstant;
import com.jackrain.nea.st.mapper.StCUnfullcarCostItemMapper;
import com.jackrain.nea.st.mapper.StCUnfullcarCostMapper;
import com.jackrain.nea.st.model.enums.CloseStatusEnum;
import com.jackrain.nea.st.model.enums.SubmitStatusEnum;
import com.jackrain.nea.st.model.table.StCUnfullcarCost;
import com.jackrain.nea.st.model.table.StCUnfullcarCostItem;
import com.jackrain.nea.st.utils.JsonUtils;
import com.jackrain.nea.st.utils.StBeanUtils;
import com.jackrain.nea.st.utils.ValueHolderUtils;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.face.impl.R3SystemUserResource;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2022/6/11 21:29
 */
@Component
@Slf4j
@Transactional
public class StCUnfullcarCostSaveService extends CommandAdapter {

    @Autowired
    private StCUnfullcarCostMapper stCUnfullcarCostMapper;

    @Autowired
    private StCUnfullcarCostItemMapper stCUnfullcarCostItemMapper;

    @StOperationLog(mainTableName = "ST_C_UNFULLCAR_COST", itemsTableName = "ST_C_UNFULLCAR_COST_ITEM")
    @Override
    public ValueHolder execute(QuerySession querySession) throws NDSException {
        ValueHolder valueHolder = new ValueHolder();
        DefaultWebEvent event = querySession.getEvent();

        List<String> nullKeyList = new ArrayList<>();
        JSONObject param = JsonUtils.filterNull((JSONObject) event.getParameterValue("param"), nullKeyList);

        Long id = param.getLong("objid");
        JSONObject fixColumn = param.getJSONObject("fixcolumn");
        if (log.isInfoEnabled()) {
            log.debug("id:{},fixColumn:{}", id, fixColumn);
        }
        if (fixColumn != null) {
            JSONObject mainMap = fixColumn.getJSONObject(StConstant.TAB_ST_C_UNFULLCAR_COST);
            JSONArray itemMap = fixColumn.getJSONArray(StConstant.TAB_ST_C_UNFULLCAR_COST_ITEM);
            if (id != null && id < 0) {
                //新增
                valueHolder = saveFunction(mainMap, querySession);
            } else {
                //编辑
                valueHolder = updateFunction(mainMap, itemMap, valueHolder, querySession, id);
            }
        }
        return valueHolder;
    }

    /**
     * 新增操作
     *
     * @param mainMap      主表数据
     * @param querySession 封装数据
     * @return 返回状态
     */
    private ValueHolder saveFunction(JSONObject mainMap, QuerySession querySession) {
        StCUnfullcarCost stCUnfullcarCost = JsonUtils.jsonParseClass(mainMap, StCUnfullcarCost.class);
        ValueHolder holder;
        if (stCUnfullcarCost != null) {
            stCUnfullcarCost.setId(ModelUtil.getSequence(StConstant.TAB_ST_C_UNFULLCAR_COST));
            StBeanUtils.makeCreateField(stCUnfullcarCost, querySession.getUser());

            reSetDateTimeRange(stCUnfullcarCost);

            if (stCUnfullcarCost.getEndDate().getTime() < stCUnfullcarCost.getStartDate().getTime()) {
                holder = ValueHolderUtils.getFailValueHolder("结束日期不允许小于开始日期");
                return holder;
            }
            if (stCUnfullcarCostMapper.insert(stCUnfullcarCost) <= 0) {
                holder = ValueHolderUtils.getFailValueHolder("保存失败！");
                return holder;
            }
        } else {
            holder = ValueHolderUtils.getFailValueHolder("保存失败！");
            return holder;
        }
        holder = ValueHolderUtils.getSuccessValueHolder(stCUnfullcarCost.getId(), StConstant.TAB_ST_C_UNFULLCAR_COST);
        return holder;
    }

    public ValueHolder saveFunction2(JSONObject mainMap) {
        StCUnfullcarCost stCUnfullcarCost = JsonUtils.jsonParseClass(mainMap, StCUnfullcarCost.class);
        ValueHolder holder;
        if (stCUnfullcarCost != null) {
            stCUnfullcarCost.setId(ModelUtil.getSequence(StConstant.TAB_ST_C_UNFULLCAR_COST));
            StBeanUtils.makeCreateField(stCUnfullcarCost, R3SystemUserResource.getSystemRootUser());

            reSetDateTimeRange(stCUnfullcarCost);

            if (stCUnfullcarCost.getEndDate().getTime() < stCUnfullcarCost.getStartDate().getTime()) {
                holder = ValueHolderUtils.getFailValueHolder("结束日期不允许小于开始日期");
                return holder;
            }
            if (stCUnfullcarCostMapper.insert(stCUnfullcarCost) <= 0) {
                holder = ValueHolderUtils.getFailValueHolder("保存失败！");
                return holder;
            }
        } else {
            holder = ValueHolderUtils.getFailValueHolder("保存失败！");
            return holder;
        }
        holder = ValueHolderUtils.getSuccessValueHolder(stCUnfullcarCost.getId(), StConstant.TAB_ST_C_UNFULLCAR_COST);
        return holder;
    }

    private ValueHolder updateFunction(JSONObject mainMap, JSONArray itemMap, ValueHolder holder, QuerySession querySession, Long objid) {

        StCUnfullcarCost oldMainData = stCUnfullcarCostMapper.selectById(objid);
        if (oldMainData == null) {
            holder = ValueHolderUtils.getFailValueHolder("当前记录不存在，不允许编辑！");
            return holder;
        }
        if (CloseStatusEnum.CLOSE.getKey().equals(oldMainData.getCloseStatus())) {
            holder = ValueHolderUtils.getFailValueHolder("当前记录已结案，不允许编辑！");
            return holder;
        }
        if (SubmitStatusEnum.SUBMIT.getKey().equals(oldMainData.getStatus())) {
            holder = ValueHolderUtils.getFailValueHolder("当前记录已提交，不允许编辑！");
            return holder;
        }

        //主表更新，objid就是主表ID
        if (mainMap != null && !mainMap.isEmpty()) {
            StCUnfullcarCost stCUnfullcarCost = JsonUtils.jsonParseClass(mainMap, StCUnfullcarCost.class);
            //校验日期是否违法
            if (stCUnfullcarCost.getStartDate() == null) {
                stCUnfullcarCost.setStartDate(oldMainData.getStartDate());
            }
            if (stCUnfullcarCost.getEndDate() == null) {
                stCUnfullcarCost.setEndDate(oldMainData.getEndDate());
            }
            reSetDateTimeRange(stCUnfullcarCost);

            if (stCUnfullcarCost.getEndDate().getTime() < stCUnfullcarCost.getStartDate().getTime()) {
                holder = ValueHolderUtils.getFailValueHolder("结束日期不允许小于开始日期");
                return holder;
            }

            stCUnfullcarCost.setId(objid);
            StBeanUtils.makeModifierField(stCUnfullcarCost, querySession.getUser());

            if (stCUnfullcarCostMapper.updateById(stCUnfullcarCost) < 0) {
                holder = ValueHolderUtils.getFailValueHolder("保存失败！");
                return holder;
            }
        }

        //判断子表数据是否存在
        if (itemMap != null && !itemMap.isEmpty()) {
            List<StCUnfullcarCostItem> stCUnfullcarCostItemList = JSON.parseObject(itemMap.toJSONString(),
                    new TypeReference<ArrayList<StCUnfullcarCostItem>>() {
                    });
            if (CollectionUtils.isNotEmpty(stCUnfullcarCostItemList)) {

                //检查行明细
                checkList(stCUnfullcarCostItemList, holder, objid);

                for (StCUnfullcarCostItem stCUnfullcarCostItem : stCUnfullcarCostItemList) {
                    Long id = stCUnfullcarCostItem.getId();
                    if (id < 0) {
                        stCUnfullcarCostItem.setId(ModelUtil.getSequence(StConstant.TAB_ST_C_UNFULLCAR_COST_ITEM));
                        stCUnfullcarCostItem.setUnfullcarCostId(objid);
                        StBeanUtils.makeCreateField(stCUnfullcarCostItem, querySession.getUser());

                        if (stCUnfullcarCostItemMapper.insert(stCUnfullcarCostItem) < 0) {
                            holder = ValueHolderUtils.getFailValueHolder("保存失败！");
                            return holder;
                        }
                    } else {
                        //修改原有的行信息 id>0
                        if (stCUnfullcarCostItemMapper.selectById(id) != null) {
                            StBeanUtils.makeModifierField(stCUnfullcarCostItem, querySession.getUser());
                            if (stCUnfullcarCostItemMapper.updateById(stCUnfullcarCostItem) < 0) {
                                holder = ValueHolderUtils.getFailValueHolder("保存失败！");
                                return holder;
                            }
                        } else {
                            holder = ValueHolderUtils.getFailValueHolder("修改的行明细已被删除！");
                            return holder;
                        }
                    }
                }
                StCUnfullcarCost stCUnfullcarCost = new StCUnfullcarCost();
                stCUnfullcarCost.setId(objid);
                StBeanUtils.makeModifierField(stCUnfullcarCost, querySession.getUser());
                stCUnfullcarCostMapper.updateById(stCUnfullcarCost);
            } else {
                holder = ValueHolderUtils.getFailValueHolder("明细JSON转换失败，保存失败！");
                return holder;
            }
        }

        holder = ValueHolderUtils.getSuccessValueHolder(objid, StConstant.TAB_ST_C_UNFULLCAR_COST);
        return holder;
    }

    private void checkList(List<StCUnfullcarCostItem> stCUnfullcarCostItemList, ValueHolder holder, Long objid) {
        //为新增和修改数据赋值，用于后续比较
        for (StCUnfullcarCostItem item : stCUnfullcarCostItemList) {
            if (item.getId() != null && item.getId() < 0) {
                //新增
                if (item.getStartWeight() == null) {
                    item.setStartWeight(new BigDecimal(0));
                }
                if (item.getEndWeight() == null) {
                    item.setEndWeight(new BigDecimal(0));
                }
            } else {
                //编辑
                if (item.getStartWeight() == null || item.getEndWeight() == null || item.getCityId() == null) {
                    StCUnfullcarCostItem oldOne = stCUnfullcarCostItemMapper.selectById(item.getId());
                    if (oldOne == null) {
                        throw new NDSException("修改的明细不存在！");
                    }
                    if (item.getCityId() == null) {
                        item.setCityId(oldOne.getCityId());
                    }
                    if (item.getStartWeight() == null) {
                        item.setStartWeight(oldOne.getStartWeight());
                    }
                    if (item.getEndWeight() == null) {
                        item.setEndWeight(oldOne.getEndWeight());
                    }
                }
            }
            if (item.getStartWeight().compareTo(item.getEndWeight()) == 1) {
                throw new NDSException("起始重量不允许大于结束重量！");
            }
        }
        //当编辑多条时，现在集合内判断重量交叉问题
        if (stCUnfullcarCostItemList.size() > 1) {
            Map<Long, List<StCUnfullcarCostItem>> listMap = stCUnfullcarCostItemList.stream().collect(Collectors.groupingBy(StCUnfullcarCostItem::getCityId));
            Set<Long> cityIds = listMap.keySet();
            for (Long cityId : cityIds) {
                List<StCUnfullcarCostItem> itemByCityList = listMap.get(cityId);
                if (itemByCityList != null && itemByCityList.size() > 1) {
                    for (int i = 0; i < itemByCityList.size(); i++) {
                        for (int j = i + 1; j < itemByCityList.size(); j++) {
                            StCUnfullcarCostItem one = itemByCityList.get(i);
                            StCUnfullcarCostItem two = itemByCityList.get(j);
                            Boolean flag = checkWeight(one.getStartWeight(), one.getEndWeight(), two.getStartWeight(), two.getEndWeight());
                            if (!flag) {
                                throw new NDSException("录入或修改的明细已存在，不允许重复录入！");
                            }
                        }
                    }
                }
            }
        }
        //与库内数据比较
        for (StCUnfullcarCostItem costItem : stCUnfullcarCostItemList) {
            List<StCUnfullcarCostItem> itemList = stCUnfullcarCostItemMapper.selectList(new LambdaQueryWrapper<StCUnfullcarCostItem>()
                    .eq(StCUnfullcarCostItem::getUnfullcarCostId, objid)
                    .eq(StCUnfullcarCostItem::getCityId, costItem.getCityId())
                    .ne(StCUnfullcarCostItem::getId, costItem.getId()));
            if (CollectionUtils.isNotEmpty(itemList)) {
                for (StCUnfullcarCostItem item : itemList) {
                    Boolean flag = checkWeight(costItem.getStartWeight(), costItem.getEndWeight(), item.getStartWeight(), item.getEndWeight());
                    if (!flag) {
                        throw new NDSException("录入或修改的明细已存在，不允许重复录入！");
                    }
                }
            }
        }

    }

    private Boolean checkWeight(BigDecimal startWeight, BigDecimal endWeight, BigDecimal startWeight1, BigDecimal endWeight1) {
        if (endWeight.compareTo(endWeight1) == 0) {
            return false;
        } else {
            if (endWeight.compareTo(endWeight1) == 1) {
                //endWeight大，当startWeight小于endWeight1时，返回false
                if (startWeight.compareTo(endWeight1) == -1) {
                    return false;
                }
            } else {
                //endWeight1大，当startWeight1小于endWeight时，返回false
                if (startWeight1.compareTo(endWeight) == -1) {
                    return false;
                }
            }
        }
        return true;
    }

    /**
     * 开始日期取当天0点，结束日期取当天23:59:59
     *
     * @param mainObj 主对象
     */
    private void reSetDateTimeRange(StCUnfullcarCost mainObj) {
        if (Objects.isNull(mainObj)) {
            return;
        }
        if (Objects.nonNull(mainObj.getStartDate())) {
            mainObj.setStartDate(DateUtil.beginOfDay(mainObj.getStartDate()));
        }
        if (Objects.nonNull(mainObj.getEndDate())) {
            /*mainObj.setEndDate(DateUtil.endOfDay(mainObj.getEndDate()));*/
            DateTime dateTime = DateUtil.endOfDay(mainObj.getEndDate());
            dateTime.setTime(dateTime.getTime() - 999);
            mainObj.setEndDate(dateTime);
        }
    }
}
