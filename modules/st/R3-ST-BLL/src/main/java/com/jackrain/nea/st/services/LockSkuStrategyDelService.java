//package com.jackrain.nea.st.services;
//
//import com.alibaba.fastjson.JSON;
//import com.alibaba.fastjson.JSONArray;
//import com.alibaba.fastjson.JSONObject;
//import com.alibaba.fastjson.parser.Feature;
//import com.google.common.collect.Lists;
//import com.jackrain.nea.exception.NDSException;
//import com.jackrain.nea.sg.basic.api.SgLockSkuStrategyBasicCmd;
//import com.jackrain.nea.st.common.StConstant;
//import com.jackrain.nea.st.mapper.StCLockSkuStrategyItemMapper;
//import com.jackrain.nea.st.mapper.StCLockSkuStrategyMapper;
//import com.jackrain.nea.st.model.table.StCLockSkuStrategyDO;
//import com.jackrain.nea.st.model.table.StCLockSkuStrategyItemDO;
//import com.jackrain.nea.st.utils.DatasToEsUtils;
//import com.jackrain.nea.st.utils.StBeanUtils;
//import com.jackrain.nea.st.utils.ValueHolderUtils;
//import com.jackrain.nea.sys.CommandAdapter;
//import com.jackrain.nea.util.ValueHolder;
//import com.jackrain.nea.web.query.DefaultWebEvent;
//import com.jackrain.nea.web.query.QuerySession;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.dubbo.config.annotation.Reference;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.stereotype.Component;
//import org.springframework.transaction.annotation.Transactional;
//
//import java.io.IOException;
//import java.util.List;
//
///**
// * 删除业务逻辑
// *
// * <AUTHOR> 陈俊明
// * @since : 2019-03-12
// * create at : 2019-03-12 17:51
// */
//
//@Component
//@Slf4j
//@Transactional
//public class LockSkuStrategyDelService extends CommandAdapter {
//    @Autowired
//    private StCLockSkuStrategyMapper stCLockSkuStrategyMapper;
//
//    @Autowired
//    private StCLockSkuStrategyItemMapper stCLockSkuStrategyItemMapper;
//
//    @Reference(group = "sg", version = "1.0")
//    private SgLockSkuStrategyBasicCmd skuStrategyBasicCmd;
//    @Autowired
//    private ShopLockSkuSyncStockService shopLockSkuSyncStockService;
//
//
//    /**
//     * 主子表删除
//     *
//     * @param querySession
//     * @return
//     * @throws NDSException
//     */
//    @Override
//    public ValueHolder execute(QuerySession querySession) throws NDSException {
//        ValueHolder holder = new ValueHolder();
//        DefaultWebEvent event = querySession.getEvent();
//        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(
//                event.getParameterValue("param"), "yyyy-MM-dd HH:mm:ss"), Feature.OrderedField);
//
//        if (param == null || param.size() == 0) {
//            throw new NDSException("参数为空！");
//        }
//
//        String isDel = param.getString("isdelmtable");
//        Long objid = param.getLong("objid");
//
//        //判断主表是否存在
//        StCLockSkuStrategyDO stCLockSkuStrategyDO = stCLockSkuStrategyMapper.selectById(objid);
//        if (stCLockSkuStrategyDO == null) {
//            holder = ValueHolderUtils.getFailValueHolder("当前记录已不存在！");
//            return holder;
//        }
//
//        String strActive = stCLockSkuStrategyDO.getIsactive();
//        if ("N".equals(strActive)) {
//            holder = ValueHolderUtils.getFailValueHolder("当前单据已作废，不允许删除！");
//            return holder;
//        }
//
//
//        JSONObject tabitem = param.getJSONObject("tabitem");
//
//        //判断是删除主表还是明细表单独删除
//        if ("false".equals(isDel)) {
//            JSONArray itemArray = tabitem.getJSONArray("ST_C_LOCK_SKU_STRATEGY_ITEM");
//            //单独删除明细
//            holder = delItem(itemArray, objid, holder, querySession, stCLockSkuStrategyDO);
//        } else {
//            //删除主表
//            holder = delMain(objid, holder, querySession);
//        }
//        return holder;
//    }
//
//    /**
//     * 删除明细表
//     *
//     * @param itemArray 子表数据
//     * @param mainId    主表id
//     * @param holder    封装数据
//     * @return 返回状态
//     */
//    private ValueHolder delItem(JSONArray itemArray, Long mainId, ValueHolder holder, QuerySession querySession,
//                                StCLockSkuStrategyDO stCLockSkuStrategyDO) {
//        List<Long> ids = Lists.newArrayListWithExpectedSize(itemArray.size());
//        //明细表记录不存在，则提示：当前记录已不存在！
//        for (int i = 0; i < itemArray.size(); i++) {
//            Long itemId = itemArray.getLong(i);
//            StCLockSkuStrategyItemDO stCLockSkuStrategyItemDO = stCLockSkuStrategyItemMapper.selectById(itemId);
//            if (stCLockSkuStrategyItemDO == null) {
//                holder = ValueHolderUtils.getFailValueHolder("当前记录已不存在！");
//                return holder;
//            } else {
//                int deleteCount = stCLockSkuStrategyItemMapper.deleteById(itemId);
//                if (deleteCount < 0) {
//                    holder = ValueHolderUtils.getFailValueHolder("删除失败！");
//                    return holder;
//                } else {
//                    StBeanUtils.makeModifierField(stCLockSkuStrategyDO, querySession.getUser());
//                    if (stCLockSkuStrategyMapper.updateById(stCLockSkuStrategyDO) < 0) {
//                        holder = ValueHolderUtils.getFailValueHolder("删除失败！");
//                        return holder;
//                    } else {
//                        //更新到同步库存中间
//                        //shopLockSkuSyncStockService.updateSgBSyncChannelStock(mainId,null,StConstant.SKUSTOCK_STATUS_03,querySession.getUser());
//                        ids.add(itemId);
//                        try {
//                            DatasToEsUtils.deleteLoclSkuEsData(stCLockSkuStrategyDO, stCLockSkuStrategyItemDO, StConstant.TAB_ST_C_LOCK_SKU_STRATEGY_ITEM);//删除
//                        } catch (IOException e) {
//                            e.printStackTrace();
//                            log.error(LogUtil.format("删除ES数据异常:" + e);
//                        }
//                        //deleteProductEsData(stCLockSkuStrategyDO,false,itemId);
//                    }
//                }
//            }
//        }
//
//        if (!ids.isEmpty()) {
//            // 删除同步至PG
//            skuStrategyBasicCmd.deleteItems(ids);
//        }
//        holder = ValueHolderUtils.getDeleteSuccessValueHolder();
//        return holder;
//    }
//
//    /**
//     * 删除主表
//     *
//     * @param mainId 主表id
//     * @param holder 封装数据
//     * @return 返回状态
//     */
//    private ValueHolder delMain(Long mainId, ValueHolder holder, QuerySession querySession) {
//        //组装主表数据
//        StCLockSkuStrategyDO stCLockSkuStrategyDO = new StCLockSkuStrategyDO();
//        stCLockSkuStrategyDO.setId(mainId);
//        //根据主表id查询明细数据，以便于删除
//        List<StCLockSkuStrategyItemDO> stCLockSkuStrategyItemDOList = stCLockSkuStrategyItemMapper.listByItemId(mainId);
//        if (stCLockSkuStrategyItemDOList.size() > 0) {
//            for (StCLockSkuStrategyItemDO stCLockSkuStrategyItemDO : stCLockSkuStrategyItemDOList) {
//                if (stCLockSkuStrategyItemMapper.deleteById(stCLockSkuStrategyItemDO.getId()) <= 0) {
//                    throw new NDSException("无此明细记录！");
//                } else {
//                    //删除成功推es
//                    //加一个查询ES的校验：查看是否存在此数值，有则删除，无责不调用删除方法
//                    try {
//                        DatasToEsUtils.deleteLoclSkuEsData(stCLockSkuStrategyDO, stCLockSkuStrategyItemDO, StConstant.TAB_ST_C_LOCK_SKU_STRATEGY_ITEM);//删除
//                    } catch (Exception ex) {
//                        if (ex.toString().contains("404 Not Found")) {
//                            log.debug(LogUtil.format("店铺锁库条码特殊设置策略明细表s删除ES数据失败：" + ex.toString());
//                        } else {
//                            throw new NDSException("店铺锁库条码特殊设置策略明细表s删除ES数据失败！" + ex.toString());
//                        }
//                    }
//                }
//            }
//        }
//        //根据主表id删除主表数据
//        if (stCLockSkuStrategyMapper.deleteById(mainId) <= 0) {
//            throw new NDSException("删除失败,请联系管理员！");
//        } else {
//            // 删除同步至PG
//            skuStrategyBasicCmd.delete(Lists.newArrayList(mainId));
//            try {
//                DatasToEsUtils.deleteLoclSkuEsData(stCLockSkuStrategyDO, null, StConstant.TAB_ST_C_LOCK_SKU_STRATEGY);//主表删除
//            } catch (Exception ex) {
//                if (ex.toString().contains("404 Not Found")) {
//                    log.debug(LogUtil.format("店铺锁库条码特殊设置策略删除ES数据失败：" + ex.toString());
//                } else {
//                    throw new NDSException("店铺锁库条码特殊设置策略删除ES数据失败！" + ex.toString());
//                }
//            }
//        }
//        return holder;
//    }
//}
