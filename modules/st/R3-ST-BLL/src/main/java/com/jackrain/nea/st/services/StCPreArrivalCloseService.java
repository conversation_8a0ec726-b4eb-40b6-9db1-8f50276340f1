package com.jackrain.nea.st.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.st.common.StConstant;
import com.jackrain.nea.st.mapper.StCPreArrivalMapper;
import com.jackrain.nea.st.model.table.StCPreArrivalDO;
import com.jackrain.nea.st.utils.StBeanUtils;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.face.impl.UserImpl;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description 预到货策略结案业务类
 * @Date 2020/06/10
 * @Copyright 2019-2020
 */
@Component
@Slf4j
@Transactional
public class StCPreArrivalCloseService extends CommandAdapter {

    @Autowired
    private StCPreArrivalMapper stCPreArrivalMapper;


    /**
      * @Description:  预到货策略结案接口,自动结案
      * @Param:   stCPreArrivalId
      * @return:
      * @Author: 江家雷
      * @Date: 2020/6/14
      */
    public void closeStCPreArrival(){

        Map<String,Object> params = new HashMap<>();
        params.put("pre_arrival_status",StConstant.PRE_ARRIVAL_STATUS_02);
        List<StCPreArrivalDO> preList = stCPreArrivalMapper.selectByMap(params);
        if(CollectionUtils.isEmpty(preList)){
            return ;
        }
        for(StCPreArrivalDO pre : preList){
            Date current = new Date();
            if(current.before(pre.getEndTime())){
                continue;
            }
            //结案验证
            closeCheck(pre.getId());
            StCPreArrivalDO preArrivalDO = new StCPreArrivalDO();
            preArrivalDO.setId(pre.getId());
            StBeanUtils.makeModifierField(pre,getRootUser());
            preArrivalDO.setPreArrivalStatus(StConstant.PRE_ARRIVAL_STATUS_04);
            //更新单据状态
            int count = stCPreArrivalMapper.updateById(preArrivalDO);
            if (count < 0) {
                throw new NDSException("结案失败");
            }
            // TODO 冲销库存

        }

    }

    /**
     * @return com.jackrain.nea.util.ValueHolder
     * <AUTHOR>
     * @Description 预到货策略结案
     * @Date  2020/06/10
     * @Param [session]
     **/
    public ValueHolder execute(QuerySession session) throws NDSException{
        DefaultWebEvent event = session.getEvent();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(
                event.getParameterValue("param"), "yyyy-MM-dd HH:mm:ss", SerializerFeature.WriteMapNullValue),
                Feature.OrderedField);
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("请求JSON：") + param.toString());
        }
        if (param == null) {
            throw new NDSException("参数为空！");
        }

        ValueHolder resultValueHolder = new ValueHolder();
        HashMap<Long, Object> errMap = new HashMap<Long, Object>();
        //生成结案Json数组
        JSONArray auditArray = StBeanUtils.makeAuditJsonArray(param);
        for (int i = 0; i < auditArray.size(); i++) {
            Long id = Long.valueOf(auditArray.get(i).toString());
            //结案验证
            closeCheck(id);
            StCPreArrivalDO preArrivalDO = new StCPreArrivalDO();
            preArrivalDO.setId(id);
            preArrivalDO.setPreArrivalStatus(StConstant.PRE_ARRIVAL_STATUS_04);
            StBeanUtils.makeModifierField(preArrivalDO,session.getUser());
            //更新单据状态
            int count = stCPreArrivalMapper.updateById(preArrivalDO);
            if (count < 0) {
                throw new NDSException("结案失败");
            }

            // TODO 冲销库存
        }
        resultValueHolder = StBeanUtils.getExcuteValueHolder(auditArray.size(),errMap);
        return resultValueHolder;
    }

    /**
     * @return com.alibaba.fastjson.JSONObject
     * <AUTHOR>
     * @Description 结案验证
     * @Date 2020/06/10
     * @Param [id]
     **/
    private JSONObject closeCheck(Long id) throws NDSException {
        //记录不存在
        StCPreArrivalDO preArrivalDO = stCPreArrivalMapper.selectById(id);
        if (preArrivalDO == null) {
            throw new NDSException("当前记录已不存在！");
        } else {
            if (StConstant.CON_BILL_STATUS_01.equals(preArrivalDO.getPreArrivalStatus())) {
                throw new NDSException("方案未审核，不允许结案！");
            }else if(StConstant.CON_BILL_STATUS_03.equals(preArrivalDO.getPreArrivalStatus())){
                throw new NDSException("方案已作废，不允许结案！");
            }else if(StConstant.CON_BILL_STATUS_04.equals(preArrivalDO.getPreArrivalStatus())){
                throw new NDSException("方案已结案，不允许重复结案！");
            }
        }
        return null;
    }

    private User getRootUser() {
        UserImpl user = new UserImpl();
        user.setId(893);
        user.setName("root");
        user.setEname("root");
        return user;
    }
}
