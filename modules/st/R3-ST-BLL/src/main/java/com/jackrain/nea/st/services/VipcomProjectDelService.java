package com.jackrain.nea.st.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.jackrain.nea.cp.constants.R3CommonResultConstants;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.st.mapper.StCVipcomProjectItemMapper;
import com.jackrain.nea.st.mapper.StCVipcomProjectMapper;
import com.jackrain.nea.st.model.table.StCVipcomProjectDO;
import com.jackrain.nea.st.utils.StBeanUtils;
import com.jackrain.nea.st.utils.ValueHolderUtils;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

/**
 * @Descroption 日程计划设置-删除
 * <AUTHOR>
 * @Date 2019/3/7 10:55
 */
@Component
@Slf4j
@Transactional
public class VipcomProjectDelService extends CommandAdapter {
    @Autowired
    private StCVipcomProjectMapper projectMapper;
    @Autowired
    private StCVipcomProjectItemMapper itemMapper;

    @Override
    public ValueHolder execute(QuerySession querySession) throws NDSException {
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("Start VipcomProjectDelService.QuerySession=") + querySession.toString() + ";");
        }
        DefaultWebEvent event = querySession.getEvent();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(
                event.getParameterValue("param"), "yyyy-MM-dd HH:mm:ss"),
                Feature.OrderedField);
        Long id = param.getLong("objid");
        if (id != null && id > 0) {
            ValueHolder valueHolder = new ValueHolder();
            //1. 检查主表
            StCVipcomProjectDO project = projectMapper.selectById(id);
            if (!checkExpressStatus(project, valueHolder)) {
                return valueHolder;
            }
            //2.删除主表
            if ((projectMapper.deleteById(project)) > 0) {
                updateModifyInfo(querySession,id);
                return ValueHolderUtils.getSuccessValueHolder("删除档期日程规划成功！");
            }
        }
        throw new NDSException("当前记录已不存在！");
    }

    /**
     * 更新删除人信息
     * @param session
     * @param id
     */
    private void updateModifyInfo(QuerySession session, Long id) {
        StCVipcomProjectDO project = new StCVipcomProjectDO();
        project.setId(id);
        //修改信息
        StBeanUtils.makeModifierField(project, session.getUser());
        //修改人账号
        project.setModifierename(session.getUser().getEname());
        if ((projectMapper.updateById(project)) <= 0) {
            log.error(LogUtil.format("VipcomProjectDelService.updateModifyInfo Error", "删除主表信息更新出错id:") + id);
        }
    }

    private boolean checkExpressStatus(StCVipcomProjectDO express, ValueHolder valueHolder) {
        if (express == null) {
            valueHolder.put("code", -1);
            valueHolder.put("message", "当前记录不存在！");
            return false;
        }
        if(R3CommonResultConstants.VALUE_Y.equals(express.getIsactive())){
            valueHolder.put("code", -1);
            valueHolder.put("message", "已启用的日程规划不允许删除！");
            return false;
        }
        return true;
    }

}
