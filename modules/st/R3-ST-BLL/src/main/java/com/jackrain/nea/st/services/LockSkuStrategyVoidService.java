//package com.jackrain.nea.st.services;
//
//import com.alibaba.fastjson.JSON;
//import com.alibaba.fastjson.JSONArray;
//import com.alibaba.fastjson.JSONObject;
//import com.alibaba.fastjson.parser.Feature;
//import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
//import com.google.common.collect.Lists;
//import com.jackrain.nea.exception.NDSException;
//import com.jackrain.nea.sg.basic.api.SgLockSkuStrategyBasicCmd;
//import com.jackrain.nea.st.common.StConstant;
//import com.jackrain.nea.st.mapper.StCLockSkuStrategyItemMapper;
//import com.jackrain.nea.st.mapper.StCLockSkuStrategyMapper;
//import com.jackrain.nea.st.model.table.StCLockSkuStrategyDO;
//import com.jackrain.nea.st.model.table.StCLockSkuStrategyItemDO;
//import com.jackrain.nea.st.model.table.StCLockStockStrategyItemDO;
//import com.jackrain.nea.st.utils.DatasToEsUtils;
//import com.jackrain.nea.st.utils.ListUtils;
//import com.jackrain.nea.st.utils.StBeanUtils;
//import com.jackrain.nea.sys.CommandAdapter;
//import com.jackrain.nea.util.ValueHolder;
//import com.jackrain.nea.web.query.DefaultWebEvent;
//import com.jackrain.nea.web.query.QuerySession;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.commons.collections.CollectionUtils;
//import org.apache.dubbo.config.annotation.Reference;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.stereotype.Component;
//import org.springframework.transaction.annotation.Transactional;
//
//import java.util.Date;
//import java.util.List;
//
//@Component
//@Slf4j
//@Transactional
//
//public class LockSkuStrategyVoidService extends CommandAdapter {
//    @Autowired
//    private StCLockSkuStrategyMapper stCLockSkuStrategyMapper;
//
//    @Autowired
//    private StCLockSkuStrategyItemMapper stCLockSkuStrategyItemMapper;
//
//    @Reference(group = "sg", version = "1.0")
//    private SgLockSkuStrategyBasicCmd skuStrategyBasicCmd;
//
//    @Override
//    public ValueHolder execute(QuerySession querySession) throws NDSException {
//        ValueHolder valueHolder = new ValueHolder();
//        DefaultWebEvent event = querySession.getEvent();
//        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(
//                event.getParameterValue("param"), "yyyy-MM-dd HH:mm:ss"),
//                Feature.OrderedField);
//
//        if (param != null) {
//            JSONArray itemArray = StBeanUtils.makeVoidJsonArray(param);
//
//            valueHolder = voidLockSkuStrategy(itemArray, valueHolder, querySession);
//        } else {
//            throw new NDSException("参数为空！");
//        }
//        return valueHolder;
//    }
//
//    private JSONObject checkLockSkuStrategyStatus(StCLockSkuStrategyDO stCLockSkuStrategyDO, Long objid, JSONObject errJo) {
//        String strActive = stCLockSkuStrategyDO.getIsactive();
//        if ("N".equals(strActive)) {
//            errJo.put("objid", objid);
//            errJo.put("message", "单据已作废，不能重复作废！");
//            return errJo;
//        }
//
//        //不是未审核，不允许作废
//        if (!StConstant.CON_BILL_STATUS_01.equals(stCLockSkuStrategyDO.getEstatus())) {
//            errJo.put("objid", objid);
//            errJo.put("message", "当前记录不是未审核，不允许作废！");
//            return errJo;
//        }
//        return null;
//    }
//
//    private ValueHolder voidLockSkuStrategy(JSONArray itemArray, ValueHolder valueHolder, QuerySession querySession) {
//        //作废记录前，先判断是否存在
//        if (itemArray.size() > 0) {
//            JSONArray errorArray = new JSONArray();
//            List<Long> ids = Lists.newArrayListWithExpectedSize(itemArray.size());
//            for (int i = 0; i < itemArray.size(); i++) {
//                JSONObject errJo = new JSONObject();
//                Long objid = itemArray.getLong(i);
//                StCLockSkuStrategyDO stCLockSkuStrategyDO = stCLockSkuStrategyMapper.selectById(objid);
//
//                if (stCLockSkuStrategyDO != null) {
//                    JSONObject errorJson = checkLockSkuStrategyStatus(stCLockSkuStrategyDO, objid, errJo);
//                    if (errorJson != null) {
//                        errorArray.add(errorJson);
//                    } else {
//                        StBeanUtils.makeModifierField(stCLockSkuStrategyDO, querySession.getUser());
//                        //BILL_STATUS    1:未审核 2:已审核 3:已作废 4:已结案
//                        stCLockSkuStrategyDO.setEstatus(StConstant.CON_BILL_STATUS_03);
//                        stCLockSkuStrategyDO.setIsactive("N");
//                        stCLockSkuStrategyDO.setDelerId(Long.valueOf(querySession.getUser().getId()));//作废人
//                        stCLockSkuStrategyDO.setDelname(querySession.getUser().getName());
//                        stCLockSkuStrategyDO.setDelename(querySession.getUser().getEname());
//                        stCLockSkuStrategyDO.setDelTime(new Date());//作废时间
//                        int iResult = stCLockSkuStrategyMapper.updateById(stCLockSkuStrategyDO);
//                        if (iResult <= 0) {
//                            errJo.put("objid", objid);
//                            errJo.put("message", "作废失败！");
//                            errorArray.add(errJo);
//                        } else {
//                            ids.add(objid);
//                            //更新子表明细状态并推送ES数据
//                            try {
//                                stCLockSkuStrategyDO = stCLockSkuStrategyMapper.selectById(objid);
//                                StCLockSkuStrategyItemDO item = new StCLockSkuStrategyItemDO();
//                                item.setStatus(StConstant.CON_BILL_STATUS_03);
//                                QueryWrapper<StCLockSkuStrategyItemDO> wrapper = new QueryWrapper<>();
//                                wrapper.eq("ST_C_LOCK_SKU_STRATEGY_ID", objid);
//                                stCLockSkuStrategyItemMapper.update(item, wrapper);
//                                List<StCLockSkuStrategyItemDO> itemList = ListUtils.batchQueryByCondition(wrapper, stCLockSkuStrategyItemMapper, 1000);
//                                DatasToEsUtils.insertLoclSkuEsData(stCLockSkuStrategyDO, null, StConstant.TAB_ST_C_LOCK_SKU_STRATEGY);
//                                if (CollectionUtils.isNotEmpty(itemList)) {
//                                    DatasToEsUtils.insertLoclSkuEsData(stCLockSkuStrategyDO, itemList, StConstant.TAB_ST_C_LOCK_SKU_STRATEGY_ITEM);
//                                }
//                            } catch (Exception ex) {
//                                log.debug(LogUtil.format("店铺锁库条码特殊设置策略主表作废推数据到ES失败：" + ex.toString());
//                            }
//                        }
//                    }
//                } else {
//                    //记录不存在
//                    errJo.put("objid", objid);
//                    errJo.put("message", "当前记录不存在！");
//                    errorArray.add(errJo);
//                }
//            }
//            if (!ids.isEmpty()) {
//                // 作废同步至PG
//                skuStrategyBasicCmd.status(ids, StConstant.CON_BILL_STATUS_03);
//            }
//            valueHolder = StBeanUtils.getProcessValueHolder(itemArray, errorArray, "作废");
//            return valueHolder;
//        }
//        return valueHolder;
//    }
//}