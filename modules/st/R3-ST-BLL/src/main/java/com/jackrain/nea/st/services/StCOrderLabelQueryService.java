package com.jackrain.nea.st.services;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.jackrain.nea.st.mapper.StCOrderLabelItemMapper;
import com.jackrain.nea.st.mapper.StCOrderLabelMapper;
import com.jackrain.nea.st.model.request.StCDepositPreSaleSinkRequest;
import com.jackrain.nea.st.model.request.StCOrderLabelRequest;
import com.jackrain.nea.st.model.table.StCDepositPreSaleSinkItemDO;
import com.jackrain.nea.st.model.table.StCOrderLabelDO;
import com.jackrain.nea.st.model.table.StCOrderLabelItemDO;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.utility.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @ClassName : StCOrderLabelQueryService  
 * @Description : 
 * <AUTHOR>  YCH
 * @Date: 2021-11-30 10:58  
 */
@Component
@Slf4j
public class StCOrderLabelQueryService {

    @Autowired
    private StCOrderLabelMapper stCOrderLabelMapper;
    @Autowired
    private StCOrderLabelItemMapper stCOrderLabelItemMapper;

    public List<StCOrderLabelRequest> queryOrderLabelk(Long cpShopId) {
        log.info(LogUtil.format(" StCOrderLabelQueryService.queryOrderLabelk.param :{}", cpShopId));
        if (cpShopId == null){
            log.info(LogUtil.format("StCDepositPreSaleSinkQueryService.queryDepositPreSaleSink.cpCPhyWarehouse 为空"));
            return null;
        }
        //返参
        List<StCOrderLabelRequest> stCOrderLabelRequests = new ArrayList<>();
        //满足店铺订单打标策略
        List<StCOrderLabelDO> stCOrderLabelDOS = stCOrderLabelMapper.selectByShopId(String.valueOf(cpShopId));

        if (CollectionUtils.isNotEmpty(stCOrderLabelDOS)){
            for (StCOrderLabelDO stCOrderLabelDO : stCOrderLabelDOS){
                StCOrderLabelRequest stCOrderLabelRequest =new StCOrderLabelRequest();
                //主表
                stCOrderLabelRequest.setStCOrderLabelDO(stCOrderLabelDO);
                List<StCOrderLabelItemDO> orderLabelItemDOS = stCOrderLabelItemMapper.selectList(new QueryWrapper<StCOrderLabelItemDO>().lambda()
                        .eq(StCOrderLabelItemDO::getStCOrderLabelId, stCOrderLabelDO.getId())
                        .eq(StCOrderLabelItemDO::getIsactive, "Y"));
                if (CollectionUtils.isNotEmpty(orderLabelItemDOS)){
                    //明细
                    stCOrderLabelRequest.setStCOrderLabelItemDOList(orderLabelItemDOS);
                }
                stCOrderLabelRequests.add(stCOrderLabelRequest);
            }
            return stCOrderLabelRequests;
        }
        log.info(LogUtil.format(" StCOrderLabelQueryService.queryOrderLabelk.返回数据为: {}"),
                JSON.toJSONString(stCOrderLabelRequests));
        return null;
    }


    /**
     * 新的查询打标策略
     *
     * @param cpShopId
     * @return
     */
    public ValueHolderV14<List<StCOrderLabelRequest>> selectOrderLabel(Long cpShopId) {
        ValueHolderV14<List<StCOrderLabelRequest>> holder = new ValueHolderV14<>();
        if (cpShopId == null) {
            holder.setCode(-1);
            holder.setMessage("参数不能为空");
            return holder;
        }
        List<StCOrderLabelRequest> stCOrderLabelRequests = new ArrayList<>();
        //满足店铺订单打标策略
        List<StCOrderLabelDO> stCOrderLabelDOS = stCOrderLabelMapper.selectStCOrderLabelList(cpShopId);
        if (CollectionUtils.isEmpty(stCOrderLabelDOS)){
            holder.setCode(0);
            holder.setMessage("未查询到数据");
            return holder;
        }
        List<Long> mainIds = stCOrderLabelDOS.stream().map(StCOrderLabelDO::getId).collect(Collectors.toList());
        List<StCOrderLabelItemDO> stCOrderLabelItemDOS = stCOrderLabelItemMapper.selectStCOrderLabelItemListByMainIds(mainIds);
        if (CollectionUtils.isEmpty(stCOrderLabelItemDOS)) {
            holder.setCode(0);
            holder.setMessage("未查询到数据");
            return holder;
        }
        Map<Long, List<StCOrderLabelItemDO>> itemMap =
                stCOrderLabelItemDOS.stream().collect(Collectors.groupingBy(StCOrderLabelItemDO::getStCOrderLabelId));

        for (StCOrderLabelDO stCOrderLabelDO : stCOrderLabelDOS) {
            StCOrderLabelRequest request = new StCOrderLabelRequest();
            request.setStCOrderLabelDO(stCOrderLabelDO);
            List<StCOrderLabelItemDO> stCOrderLabelItemDOS1 = itemMap.get(stCOrderLabelDO.getId());
            List<StCOrderLabelItemDO> stItem = stCOrderLabelItemDOS1.stream().filter(p -> p.getType() != null && p.getType().equals(1)).collect(Collectors.toList());
            List<StCOrderLabelItemDO> proItem = stCOrderLabelItemDOS1.stream().filter(p -> p.getType() != null && p.getType().equals(2)).collect(Collectors.toList());

            request.setStCOrderLabelItemDOList(proItem);
            request.setStCOrderLabelStrategyItemDOList(stItem);
            stCOrderLabelRequests.add(request);
        }
        holder.setCode(0);
        holder.setMessage("查询到数据");
        holder.setData(stCOrderLabelRequests);
        return holder;
    }
}
