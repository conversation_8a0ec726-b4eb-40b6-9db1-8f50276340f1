package com.jackrain.nea.st.services;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.st.mapper.StCInventoryOwnershipMapper;
import com.jackrain.nea.st.mapper.StCInventorySkuOwnershipItemMapper;
import com.jackrain.nea.st.model.result.InventorySkuOwnership;
import com.jackrain.nea.st.model.table.StCInventoryOwnershipDO;
import com.jackrain.nea.st.model.table.StCInventorySkuOwnershipItemDO;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.utility.ExceptionUtil;
import com.jackrain.nea.utility.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

/**
 * @author: huang.zaizai
 * @since: 2019/08/07
 * create at : 2019/7/24 09:00
 */
@Slf4j
@Component
public class InventoryOwnershipQueryService {
    @Autowired
    private StCInventoryOwnershipMapper inventoryOwnershipMapper;
    @Autowired
    private StCInventorySkuOwnershipItemMapper inventorySkuOwnershipItemMapper;

    /**
     * 查询省市区树
     * @param request
     * @return ValueHolderV14
     */
    public ValueHolderV14 queryInventoryOwnershipV14(@NotNull InventorySkuOwnership request) {
        ValueHolderV14<InventorySkuOwnership> vh = new ValueHolderV14<>();
        try {
            InventorySkuOwnership queryResult = queryInventoryOwnership(request);
            vh.setData(queryResult);
            vh.setCode(ResultCode.SUCCESS);
            vh.setMessage("查询成功");
        } catch (Exception e) {
            vh.setCode(ResultCode.FAIL);
            vh.setMessage("查询失败," + ExceptionUtil.getMessage(e));
        }
        log.debug(LogUtil.format("InventoryOwnershipQueryService.queryInventoryOwnershipV14返回:") + JSONObject.toJSONString(vh));
        return vh;
    }

    private InventorySkuOwnership queryInventoryOwnership(InventorySkuOwnership request) {
        //库存条码归属策略
        List<StCInventorySkuOwnershipItemDO> skuItemList = inventorySkuOwnershipItemMapper
                                                    .selectInventorySkuOwnershipItem(request.getPsCSkuId(),request.getCpCShopChannelType(), new Date());
        if (CollectionUtils.isEmpty(skuItemList)) {
            //库存归属策略
            List<StCInventoryOwnershipDO> inventoryOwnershipList = inventoryOwnershipMapper
                                                    .queryInventoryOwnership(request.getPsCBrandId(),request.getCpCShopChannelType(), request.getMaterialType());
            if (CollectionUtils.isNotEmpty(inventoryOwnershipList)) {
                StCInventoryOwnershipDO inventoryOwnership = inventoryOwnershipList.get(0);
                request.setOwnershipNum(inventoryOwnership.getInternetShopOwnership());
                request.setCpCShopId(inventoryOwnership.getCpCShopId());
            }
        } else {
            StCInventorySkuOwnershipItemDO skuItem = skuItemList.get(0);
            request.setOwnershipNum(skuItem.getOwnershipNum());
            request.setCpCShopId(skuItem.getCpCShopId());
        }
        if (request.getCpCShopId() == null) {
            throw new NDSException("当前无有效库存归属！");
        }
        return request;
    }
}
