package com.jackrain.nea.st.services;

import com.alibaba.fastjson.JSON;
import com.jackrain.nea.st.mapper.StAddedServiceStrategyDetailMapper;
import com.jackrain.nea.st.mapper.StAddedServiceStrategyMapper;
import com.jackrain.nea.st.mapper.StAddedServiceTypeDocMapper;
import com.jackrain.nea.st.model.table.StAddedServiceStrategyDO;
import com.jackrain.nea.st.model.table.StAddedServiceStrategyDetailDO;
import com.jackrain.nea.st.model.table.StAddedServiceTypeDocDO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @program: ryytn-st-v3.0
 * @description: 增值服务查询Service
 * @author: haiyang
 * @create: 2023-10-25 17:27
 **/
@Slf4j
@Component
public class StAddedServiceStrategyQueryService {

    @Autowired
    private StAddedServiceTypeDocMapper addedServiceTypeDocMapper;

    @Autowired
    private StAddedServiceStrategyMapper addedServiceStrategyMapper;

    @Autowired
    private StAddedServiceStrategyDetailMapper addedServiceStrategyDetailMapper;


    public StAddedServiceTypeDocDO selectDocByTypeName(String typeName) {
        return addedServiceTypeDocMapper.selectByTypeName(typeName);
    }

    public StAddedServiceStrategyDO selectById(Long id) {
        return addedServiceStrategyMapper.selectById(id);
    }


    public StAddedServiceStrategyDetailDO selectDetailByTypeDocIdAndCpCPhyWarehouseId(Long typeDocId, Long cpCPhyWarehouseId) {
        // 先根据[档案id]查询明细，再根据明细中的[策略id]获取对应的策略，再通过[实体仓id]筛选出所需策略，正常只有一条；最后通过策略id+docId map一条返回
        // strategyId + typeDoc唯一；但策略id不变的情况下可编辑【实体仓id】
        List<StAddedServiceStrategyDetailDO> strategyDetailDOList = addedServiceStrategyDetailMapper.selectByTypeDocId(typeDocId);
        if (CollectionUtils.isNotEmpty(strategyDetailDOList)) {
            Map<String, StAddedServiceStrategyDetailDO> strategyIdAndDocIdMap = strategyDetailDOList.stream().collect(Collectors.toMap(e -> e.getAddedStrategyId() + "_" + e.getAddedTypeDocId(), Function.identity(), (v1, v2) -> v1));
            List<Long> strategyIds = strategyDetailDOList.stream().map(StAddedServiceStrategyDetailDO::getAddedStrategyId).distinct().collect(Collectors.toList());
            List<StAddedServiceStrategyDO> addedServiceStrategyDOList = addedServiceStrategyMapper.selectBatchIds(strategyIds);
            log.info("db.addedServiceStrategyDOList: {}", JSON.toJSONString(addedServiceStrategyDOList));
            List<StAddedServiceStrategyDO> filterResults = addedServiceStrategyDOList.stream().filter(e -> e.getCpCPhyWarehouseId().equals(cpCPhyWarehouseId)).collect(Collectors.toList());
            log.info("filter.addedServiceStrategyDOList: {}", filterResults);
            if (CollectionUtils.isNotEmpty(filterResults)) {
                if (filterResults.size() > 1) {
                    log.warn("docIdAndCpcStoreId.matched.multi.results, docId: {}, cpCPhyWarehouseId: {}", typeDocId, cpCPhyWarehouseId);
                }
                StAddedServiceStrategyDO strategyDO = filterResults.get(0);
                String key = strategyDO.getId() + "_" + typeDocId;
                return strategyIdAndDocIdMap.get(key);
            }
        }
        return null;
    }
}
