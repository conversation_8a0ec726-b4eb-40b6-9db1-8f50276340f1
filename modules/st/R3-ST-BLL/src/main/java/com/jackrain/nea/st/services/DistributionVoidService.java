package com.jackrain.nea.st.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.st.common.StConstant;
import com.jackrain.nea.st.mapper.StCDistributionMapper;
import com.jackrain.nea.st.model.table.StCDistributionDO;
import com.jackrain.nea.st.utils.StBeanUtils;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.HashMap;

/**
 * @Descroption 分销代销策略作废
 * <AUTHOR>
 * @Date 2019/3/12 15:55
 */
@Component
@Slf4j
public class DistributionVoidService extends CommandAdapter {
    @Autowired
    private StCDistributionMapper mapper;
    @Override
    public ValueHolder execute(QuerySession querySession) throws NDSException {
        //1.获取传入参数
        DefaultWebEvent event = querySession.getEvent();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(event.getParameterValue("param"),
                "yyyy-MM-dd HH:mm:ss", SerializerFeature.WriteMapNullValue), Feature.OrderedField);
        if (param == null) {
            throw new NDSException("参数为空！");
        }
        log.debug(LogUtil.format("Start DistributionVoidService.execute. ReceiveParams: {}"), param.toJSONString());
        //2.定义错误记录数
        HashMap<Long, Object> errMap = new HashMap<>();
        //3.生成作废Json数组
        JSONArray voidArray = StBeanUtils.makeVoidJsonArray(param);
        for (int i = 0; i < voidArray.size(); i++) {
            Long id = voidArray.getLong(i);
            try {
                //4.遍历作废方法
                voidDistribution(id, querySession);
            } catch (Exception e) {
                errMap.put(id, e.getMessage());
            }
        }
        //5.返回ValueHolder对象
        return StBeanUtils.getExcuteValueHolder(voidArray.size(), errMap);
    }

    /**
     * @param id
     * @param querySession
     * @return void
     * @Descroption 检查并更新作废状态
     * @Author: 洪艺安
     * @Date 2019/3/12
     */
    public void voidDistribution(Long id, QuerySession querySession) {
        StCDistributionDO distribution = mapper.selectById(id);
        //1.主表校验
        checkDistribution(distribution);
        //2.更新作废状态
        StBeanUtils.makeModifierField(distribution, querySession.getUser());
        distribution.setIsactive(StConstant.ISACTIVE_N);
        distribution.setBillStatus(StConstant.CON_BILL_STATUS_03);
        setVoidCommonField(distribution, querySession.getUser());
        int updateNum = mapper.updateById(distribution);
        if (updateNum < 0) {
            throw new NDSException("方案:" + distribution.getEname() + "作废失败！");
        }
    }

    private void checkDistribution(StCDistributionDO distribution) {
        if (distribution == null) {
            throw new NDSException("当前记录已不存在！");
        } else {
            if (StConstant.ISACTIVE_N.equals(distribution.getIsactive())) {
                throw new NDSException("当前记录已作废，不允许重复作废！");
            }
            if (!StConstant.CON_BILL_STATUS_01.equals(distribution.getBillStatus())) {
                throw new NDSException("代销方案非未审核状态，不允许作废！");
            }
        }
    }

    /**
     * @param distribution
     * @param user
     * @return void
     * @Descroption 设置作废公共字段
     * @Author: 洪艺安
     * @Date 2019/3/12
     */
    private void setVoidCommonField(StCDistributionDO distribution, User user) {
        distribution.setDelid(Long.valueOf(user.getId()));
        distribution.setDelename(user.getEname());
        distribution.setDelname(user.getName());
        distribution.setDelTime(new Date());
    }
}
