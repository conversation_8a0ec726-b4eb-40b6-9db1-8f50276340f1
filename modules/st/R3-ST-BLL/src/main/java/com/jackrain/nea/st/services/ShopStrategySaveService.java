package com.jackrain.nea.st.services;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.burgeon.r3.sg.core.enums.YesNoEnum;
import com.google.common.base.Throwables;
import com.jackrain.nea.cpext.api.CpLogisticsSelectServiceCmd;
import com.jackrain.nea.cpext.api.CpcPhyWareHouseQueryCmd;
import com.jackrain.nea.cpext.model.table.CpCPhyWarehouse;
import com.jackrain.nea.cpext.model.table.CpLogistics;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.oc.oms.model.resources.OmsRedisKeyResources;
import com.jackrain.nea.redis.util.RedisOpsUtil;
import com.jackrain.nea.st.annotation.StOperationLog;
import com.jackrain.nea.st.common.StConstant;
import com.jackrain.nea.st.mapper.StCShopStrategyItemMapper;
import com.jackrain.nea.st.mapper.StCShopStrategyLogisticsItemMapper;
import com.jackrain.nea.st.mapper.StCShopStrategyMapper;
import com.jackrain.nea.st.model.enums.OperationTypeEnum;
import com.jackrain.nea.st.model.enums.StCShopStrategyLogisticsTypeEnum;
import com.jackrain.nea.st.model.request.ShopStrategyRequest;
import com.jackrain.nea.st.model.table.StCOperationLogDO;
import com.jackrain.nea.st.model.table.StCShopStrategyDO;
import com.jackrain.nea.st.model.table.StCShopStrategyItemDO;
import com.jackrain.nea.st.model.table.StCShopStrategyLogisticsItem;
import com.jackrain.nea.st.utils.JsonUtils;
import com.jackrain.nea.st.utils.StBeanUtils;
import com.jackrain.nea.st.utils.ValueHolderUtils;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utils.AssertUtils;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2019/3/8 13:10
 */
@Component
@Slf4j
@Transactional
public class ShopStrategySaveService extends CommandAdapter {

    private static final Pattern ZHENG_ZHENG_SHU = Pattern.compile("^[1-9]\\d*$");

    @Autowired
    private StCShopStrategyMapper stCShopStrategyMapper;
    @Autowired
    private StCShopStrategyItemMapper stCShopStrategyItemMapper;
    @Autowired
    private StCShopStrategyLogisticsItemMapper logisticsItemMapper;

    @Autowired
    private RedisOpsUtil<String, Object> redisUtil;

    @DubboReference(group = "cp-ext", version = "1.0")
    private CpcPhyWareHouseQueryCmd cpcPhyWareHouseQueryCmd;

    @DubboReference(group = "cp-ext", version = "1.0")
    private CpLogisticsSelectServiceCmd logisticsCmd;

    @Resource
    private LogCommonService logCommonService;

    /**
     * @param querySession
     * @return
     * @Author: 朱宇军
     * @Date 2019/3/8
     */
    @Override
    @StOperationLog(mainTableName = "ST_C_SHOP_STRATEGY", itemsTableName = "ST_C_SHOP_STRATEGY_LOGISTICS_ITEM")
    public ValueHolder execute(QuerySession querySession) throws NDSException {
        ValueHolder valueHolder = new ValueHolder();
        DefaultWebEvent event = querySession.getEvent();
        List<String> nullKeyList = new ArrayList<>();
        JSONObject param = JsonUtils.filterNull((JSONObject) event.getParameterValue("param"), nullKeyList);
        log.info("shopStrategySaveService execute param:{}", param.toJSONString());
        Long id = param.getLong("objid");
        JSONObject fixColumn = param.getJSONObject("fixcolumn");
        if (fixColumn != null) {
            ShopStrategyRequest strategyCmdRequest = JsonUtils.jsonParseClass(fixColumn, ShopStrategyRequest.class);
            if (id != null && id < 0) {
                //执行新增操作,新增时，主表要做重复检测，子表不需要，因此状态只需要返回主表
                valueHolder = saveShopStrategy(strategyCmdRequest, querySession, id);
            }
            if (id != null && id > 0) {
                //更新操作
                valueHolder = updateShopStrategy(strategyCmdRequest, querySession, id, nullKeyList);
            }
        }

        return valueHolder;
    }

    /**
     * @param strategyCmdRequest
     * @param querySession
     * @param objid              主表主键
     * @return
     * @Author: 朱宇军
     * @Date 2019/3/8
     */

    private ValueHolder saveShopStrategy(ShopStrategyRequest strategyCmdRequest, QuerySession querySession, Long objid) {
        ValueHolder valueHolder = new ValueHolder();
        StCShopStrategyDO stCShopStrategyDO = strategyCmdRequest.getStCShopStrategyDO();
        if ("Y".equals(stCShopStrategyDO.getIsEnableUnlock()) &&
                (stCShopStrategyDO.getLockDays() == null || Long.valueOf(0).equals(stCShopStrategyDO.getLockDays()))) {
            return ValueHolderUtils.getFailValueHolder("启用解锁，锁单天数只能为正整数");
        }
        stCShopStrategyDO.setId(ModelUtil.getSequence(StConstant.TAB_ST_C_SHOP_STRATEGY));
        StBeanUtils.makeCreateField(stCShopStrategyDO, querySession.getUser());
        if (stCShopStrategyDO.getCpCWarehouseExchangeId() != null) {
            CpCPhyWarehouse stCWarehouseQueryResult = cpcPhyWareHouseQueryCmd.queryCpCPhyWarehouseById(stCShopStrategyDO.getCpCWarehouseExchangeId());
            if (stCWarehouseQueryResult != null) {
                stCShopStrategyDO.setCpCWarehouseExchangeName(stCWarehouseQueryResult != null ? stCWarehouseQueryResult.getEname() : null);
                stCShopStrategyDO.setCpCWarehouseExchangeCode(stCWarehouseQueryResult != null ? stCWarehouseQueryResult.getEcode() : null);
            }
        }
        if (stCShopStrategyDO.getCpCWarehouseDefId() != null) {
            CpCPhyWarehouse stCWarehouseQueryResult = cpcPhyWareHouseQueryCmd.queryCpCPhyWarehouseById(stCShopStrategyDO.getCpCWarehouseDefId());
            if (stCWarehouseQueryResult != null) {
                stCShopStrategyDO.setCpCWarehouseDefName(stCWarehouseQueryResult != null ? stCWarehouseQueryResult.getEname() : null);
                stCShopStrategyDO.setCpCWarehouseDefCode(stCWarehouseQueryResult != null ? stCWarehouseQueryResult.getEcode() : null);
            }
        }
        int insertResult = stCShopStrategyMapper.insert(stCShopStrategyDO);
        if (insertResult < 0) {
            return ValueHolderUtils.getFailValueHolder("店铺策略主表保存失败");
        }
        /**
         * 子表新增
         */
        List<StCShopStrategyItemDO> stCShopStrategyItemDOList = strategyCmdRequest.getStCShopStrategyItemDOList();
        if (stCShopStrategyItemDOList != null && stCShopStrategyItemDOList.size() > 0) {
            StCShopStrategyItemDO stCShopStrategyItemDO = stCShopStrategyItemDOList.get(0);
            stCShopStrategyItemDO.setId(ModelUtil.getSequence(StConstant.TAB_ST_C_SHOP_STRATEGY_IEM));
            stCShopStrategyItemDO.setStCShopStrategyId(stCShopStrategyDO.getId());
            StBeanUtils.makeCreateField(stCShopStrategyItemDO, querySession.getUser());
            int insertItemResult = stCShopStrategyItemMapper.insert(stCShopStrategyItemDO);
            if (insertItemResult < 0) {
                return ValueHolderUtils.getFailValueHolder("店铺策略明细保存失败");
            }
        }

        List<StCShopStrategyLogisticsItem> logisticsItemList = strategyCmdRequest.getStCShopStrategyLogisticsItemList();
        if (CollectionUtils.isNotEmpty(logisticsItemList)) {
            logisticsItemList.forEach(x -> {
                CpLogistics logistics = logisticsCmd.checkCpLogistic(x.getCpCLogisticsId());
                AssertUtils.notNull(logistics, "物流公司不存在");

                x.setId(ModelUtil.getSequence(StConstant.TAB_ST_C_SHOP_STRATEGY_LOGISTICS_ITEM));
                StBeanUtils.makeCreateField(x, querySession.getUser());
                x.setStCShopStrategyId(stCShopStrategyDO.getId());
                x.setCpCLogisticsEcode(logistics.getEcode());
                x.setCpCLogisticsEname(logistics.getEname());
            });
            logisticsItemMapper.batchInsert(logisticsItemList);
        }

        //清除缓存 新增的策略，哪来的缓存
        deleteRedisByKey(stCShopStrategyDO.getId());
        return valueHolder;
    }

    /**
     * @param shopStrategyRequest
     * @param querySession
     * @param objid               主表主键
     * @return
     * @Author: 朱宇军
     * @Date 2019/3/8
     */
    private ValueHolder updateShopStrategy(ShopStrategyRequest shopStrategyRequest, QuerySession querySession, Long objid, List<String> nullKeyList) {
        StCShopStrategyDO stCShopStrategyDO = stCShopStrategyMapper.selectById(objid);
        boolean delItem = false;
        if (stCShopStrategyDO == null) {
            return ValueHolderUtils.getFailValueHolder("当前记录已不存在！");
        }
        StCShopStrategyDO stCShopStrategy = shopStrategyRequest.getStCShopStrategyDO();
        if (stCShopStrategy != null) {
            if (!StringUtils.isEmpty(stCShopStrategy.getLogisticsType())
                    && StCShopStrategyLogisticsTypeEnum.STATUS_0.getCode().equals(String.valueOf(stCShopStrategy.getLogisticsType()))) {
                delItem = true;
            }
            String isEnableUnlock = stCShopStrategy.getIsEnableUnlock();
            if (isEnableUnlock == null) {
                isEnableUnlock = stCShopStrategyDO.getIsEnableUnlock();
            }
            Long lockDays = stCShopStrategy.getLockDays();
            if (lockDays == null && !nullKeyList.contains("LOCK_DAYS")) {
                lockDays = stCShopStrategyDO.getLockDays();
            }
            if ("Y".equals(isEnableUnlock) && (lockDays == null || Long.valueOf(0).equals(lockDays))) {
                return ValueHolderUtils.getFailValueHolder("启用解锁，锁单天数只能为正整数");
            }

            //转单延迟配置
            String isDelayTransfer = stCShopStrategy.getIsDelayTransfer();
            Long delayTransferTime = stCShopStrategy.getDelayTransferTime();
//            if (isDelayTransfer == null) {
//                isDelayTransfer = stCShopStrategyDO.getIsDelayTransfer();
//            }
            if (YesNoEnum.Y.getKey().equals(isDelayTransfer)) {
                //如果启用，校验延迟时间
                if ((Objects.isNull(delayTransferTime) || !ZHENG_ZHENG_SHU.matcher(delayTransferTime.toString()).find())) {
                    return ValueHolderUtils.getFailValueHolder("启用延迟转单，延迟转单时长必填且只能为正整数");
                }
            }

            stCShopStrategy.setId(objid);
            StBeanUtils.makeModifierField(stCShopStrategy, querySession.getUser());
            if (stCShopStrategy.getCpCWarehouseExchangeId() != null) {
                CpCPhyWarehouse stCWarehouseQueryResult = cpcPhyWareHouseQueryCmd.queryCpCPhyWarehouseById(stCShopStrategy.getCpCWarehouseExchangeId());
                if (stCWarehouseQueryResult != null) {
                    stCShopStrategy.setCpCWarehouseExchangeName(stCWarehouseQueryResult != null ? stCWarehouseQueryResult.getEname() : null);
                    stCShopStrategy.setCpCWarehouseExchangeCode(stCWarehouseQueryResult != null ? stCWarehouseQueryResult.getEcode() : null);
                }
            }
            if (stCShopStrategy.getCpCWarehouseDefId() != null) {
                CpCPhyWarehouse stCWarehouseQueryResult = cpcPhyWareHouseQueryCmd.queryCpCPhyWarehouseById(stCShopStrategy.getCpCWarehouseDefId());
                if (stCWarehouseQueryResult != null) {
                    stCShopStrategy.setCpCWarehouseDefName(stCWarehouseQueryResult != null ? stCWarehouseQueryResult.getEname() : null);
                    stCShopStrategy.setCpCWarehouseDefCode(stCWarehouseQueryResult != null ? stCWarehouseQueryResult.getEcode() : null);
                }
            }
            int updateMapper = stCShopStrategyMapper.updateById(stCShopStrategy);
            if (updateMapper < 0) {
                return ValueHolderUtils.getFailValueHolder("店铺策略主表更新失败");
            }

            JSONObject jsonObject = new JSONObject();
            jsonObject.put("ID", objid);
            Boolean updateFlg = false;
            if (stCShopStrategy.getDefaultStoreId() == null && nullKeyList.contains("DEFAULT_STORE_ID")) {
                jsonObject.put("DEFAULT_STORE_ID", null);
                updateFlg = true;
            }
            if (stCShopStrategy.getLockDays() == null && nullKeyList.contains("LOCK_DAYS")) {
                jsonObject.put("LOCK_DAYS", null);
                updateFlg = true;
            }
            if (stCShopStrategy.getCpCWarehouseDefId() == null && nullKeyList.contains("CP_C_WAREHOUSE_DEF_ID")) {
                jsonObject.put("CP_C_WAREHOUSE_DEF_ID", null);
                updateFlg = true;
            }
            if (stCShopStrategy.getCpCWarehouseExchangeId() == null && nullKeyList.contains("CP_C_WAREHOUSE_EXCHANGE_ID")) {
                jsonObject.put("CP_C_WAREHOUSE_EXCHANGE_ID", null);
                jsonObject.put("CP_C_WAREHOUSE_EXCHANGE_NAME", null);
                jsonObject.put("CP_C_WAREHOUSE_EXCHANGE_CODE", null);
                updateFlg = true;
            }
            if (stCShopStrategy.getAddressId() == null && nullKeyList.contains("ADDRESS_ID")) {
                jsonObject.put("ADDRESS_ID", null);
                updateFlg = true;
            }

            if (YesNoEnum.N.getKey().equals(isDelayTransfer)) {
                jsonObject.put("DELAY_TRANSFER_TIME", null);
                updateFlg = true;
            }

            if (updateFlg) {
                stCShopStrategyMapper.updateAtrributes(jsonObject);
            }
            // 更新策略时需要删除redis
            String redisKey = OmsRedisKeyResources.buildLockShopStrategyRedisKey(stCShopStrategyDO.getCpCShopId());
            if (redisUtil.objRedisTemplate.hasKey(redisKey)) {
                redisUtil.objRedisTemplate.delete(redisKey);
            }

        }

        List<StCShopStrategyItemDO> stCShopStrategyItemDOList = shopStrategyRequest.getStCShopStrategyItemDOList();
        Map<String, String> map = new HashMap<>();
        if (stCShopStrategyItemDOList != null && stCShopStrategyItemDOList.size() > 0) {
            for (StCShopStrategyItemDO stCShopStrategyItemDO : stCShopStrategyItemDOList) {
                List<StCShopStrategyItemDO> itemDOList = stCShopStrategyItemMapper.selectByMasterIdDiffpricesku(objid, stCShopStrategyItemDO.getDiffpricesku());
                if (itemDOList != null && itemDOList.size() > 0) {
                    return ValueHolderUtils.getFailValueHolder("补差价条码不能重复");
                }
                if (map.get(stCShopStrategyItemDO.getDiffpricesku()) != null) {
                    return ValueHolderUtils.getFailValueHolder("补差价条码不能重复");
                }
                map.put(stCShopStrategyItemDO.getDiffpricesku(), stCShopStrategyItemDO.getDiffpricesku());
                if (stCShopStrategyItemDO.getId() > 0) {
                    //店铺策略明细修改
                    stCShopStrategyItemDO.setStCShopStrategyId(objid);
                    StBeanUtils.makeModifierField(stCShopStrategyItemDO, querySession.getUser());
                    if (stCShopStrategyItemMapper.updateById(stCShopStrategyItemDO) < 0) {
                        return ValueHolderUtils.getFailValueHolder("店铺策略明细修改失败");
                    }
                } else {
                    //店铺策略明细新增保存
                    //明细创建
                    stCShopStrategyItemDO.setStCShopStrategyId(objid);
                    stCShopStrategyItemDO.setId(ModelUtil.getSequence(StConstant.TAB_ST_C_SHOP_STRATEGY));
                    StBeanUtils.makeCreateField(stCShopStrategyItemDO, querySession.getUser());//创建信息
                    if (stCShopStrategyItemMapper.insert(stCShopStrategyItemDO) < 0) {
                        return ValueHolderUtils.getFailValueHolder("店铺策略明细保存失败");
                    }
                }
            }
            StCShopStrategyDO stCShopStrategyNewDO = new StCShopStrategyDO();
            stCShopStrategyNewDO.setId(objid);
            StBeanUtils.makeModifierField(stCShopStrategyNewDO, querySession.getUser());
            stCShopStrategyMapper.updateById(stCShopStrategyNewDO);
        }

        try {
            List<StCShopStrategyLogisticsItem> logisticsItemList = shopStrategyRequest.getStCShopStrategyLogisticsItemList();
            if (CollectionUtils.isNotEmpty(logisticsItemList)) {
                List<StCShopStrategyLogisticsItem> insertList = new ArrayList<>();
                logisticsItemList.forEach(x -> {
                    if (x.getCpCLogisticsId() != null && x.getCpCLogisticsId() > 0L) {
                        CpLogistics logistics = logisticsCmd.checkCpLogistic(x.getCpCLogisticsId());
                        AssertUtils.notNull(logistics, "物流公司不存在");

                        x.setCpCLogisticsEcode(logistics.getEcode());
                        x.setCpCLogisticsEname(logistics.getEname());
                    }

                    if (x.getId() == null || x.getId() < 1L) {
                        x.setId(ModelUtil.getSequence(StConstant.TAB_ST_C_SHOP_STRATEGY_LOGISTICS_ITEM));
                        StBeanUtils.makeCreateField(x, querySession.getUser());
                        x.setStCShopStrategyId(stCShopStrategyDO.getId());
                        insertList.add(x);
                        return;
                    }

                    StBeanUtils.makeModifierField(x, querySession.getUser());
                    logisticsItemMapper.updateById(x);
                });
                if (CollectionUtils.isNotEmpty(insertList)) {
                    logisticsItemMapper.batchInsert(insertList);
                }
            }

        } catch (Exception e) {
            log.error("店铺策略物流明细插入失败：{}", Throwables.getStackTraceAsString(e));
        }
        if (delItem) {
            List<StCShopStrategyLogisticsItem> logisticsItemList =
                    logisticsItemMapper.selectList(new LambdaQueryWrapper<StCShopStrategyLogisticsItem>()
                            .eq(StCShopStrategyLogisticsItem::getStCShopStrategyId, objid));
            if (CollectionUtils.isNotEmpty(logisticsItemList)) {
                Set<Long> itemIds = logisticsItemList.stream().map(StCShopStrategyLogisticsItem::getId).collect(Collectors.toSet());
                logisticsItemMapper.deleteBatchIds(itemIds);
                //增加删除日志
                List<StCOperationLogDO> operationLogList = new ArrayList<>();
                for (StCShopStrategyLogisticsItem logisticsItem : logisticsItemList) {
                    String beforValue = "[" + logisticsItem.getCpCLogisticsEname() + "],["
                            + (YesNoEnum.Y.getKey().equals(logisticsItem.getIsactive()) ? "是" : "否") + "]";
                    StCOperationLogDO operationLog = logCommonService.getOperationLog("ST_C_SHOP_STRATEGY",
                            OperationTypeEnum.DEL.getOperationValue(), logisticsItem.getStCShopStrategyId(),
                            "店铺策略-物流公司明细", "删除店铺策略-物流公司明细", beforValue,
                            null, querySession.getUser());
                    operationLogList.add(operationLog);
                }
                logCommonService.batchInsertLog(operationLogList);
            }
        }

        deleteRedisByKey(stCShopStrategyDO.getCpCShopId());
        return ValueHolderUtils.getSuccessValueHolder(objid, StConstant.TAB_ST_C_SHOP_STRATEGY);
    }

    /**
     * 保存和删除清空Redis
     *
     * @param shopId
     * @return
     */
    private void deleteRedisByKey(Long shopId) {
        List<String> list = new ArrayList<>();
        //OMS构造店铺策略key
        list.add(OmsRedisKeyResources.buildLockShopStrategyRedisKey(shopId));
        //OMS构造查询所有店铺策略key
        list.add(OmsRedisKeyResources.bulidLockAllStCShopStrategyKey());
        //OMS店铺策略明细表key
        list.add(OmsRedisKeyResources.bulidLockStCShopStrategyItemKey(shopId));
        for (String key : list) {
            if (redisUtil.objRedisTemplate.hasKey(key)) {
                redisUtil.objRedisTemplate.delete(key);
            }
        }
    }
}
