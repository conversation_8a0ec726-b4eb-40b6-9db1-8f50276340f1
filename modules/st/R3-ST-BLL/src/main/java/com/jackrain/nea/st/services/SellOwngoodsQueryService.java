package com.jackrain.nea.st.services;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.st.mapper.StCSellOwngoodsItemMapper;
import com.jackrain.nea.st.model.request.SellOwngoodsQueryRequest;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.utility.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.assertj.core.util.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.Date;
import java.util.List;

@Component
@Slf4j
public class SellOwngoodsQueryService {
    @Autowired
    private StCSellOwngoodsItemMapper itemMapper;

    public ValueHolderV14<List<Long>> selectSkuBySellOwngoods(SellOwngoodsQueryRequest request) throws NDSException {
        log.debug(LogUtil.format(" SellOwngoodsQueryService.selectSkuBySellOwngoods入参：")
                + JSONObject.toJSONString(request));
        ValueHolderV14<List<Long>> result = new ValueHolderV14<>();
        if (null == request.getCpCShopId()) {
            result.setCode(ResultCode.FAIL);
            result.setMessage("店铺ID/条码为空");
            return result;
        }
        if (null == request.getEffectiveDate()) {
            request.setEffectiveDate(new Date());
        }
        List<Long> skuIdList;
        if (CollectionUtils.isEmpty(request.getSkuIdList())) {
            skuIdList = itemMapper.selectSkuByShopId(request.getCpCShopId(),
                                                     request.getEffectiveDate());
        } else {
            skuIdList = itemMapper.selectSkuBySellOwngoods(request.getCpCShopId(),
                                                    request.getSkuIdList(),
                                                    request.getEffectiveDate());
        }
        log.debug(LogUtil.format("SellOwngoodsQueryService.selectSkuBySellOwngoods返回:")+ JSONObject.toJSONString(skuIdList));
        if (CollectionUtils.isEmpty(skuIdList)) {
            result.setCode(ResultCode.SUCCESS);
            result.setData(Lists.newArrayList());
            result.setMessage("查询不到对应经销商自有商品！");
            return result;
        }
        result.setCode(ResultCode.SUCCESS);
        result.setData(skuIdList);
        result.setMessage("获取成功");
        return result;
    }
}
