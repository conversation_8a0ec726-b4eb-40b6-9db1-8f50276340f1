package com.jackrain.nea.st.services;



import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.st.mapper.StCVipcomAscriptionMapper;
import com.jackrain.nea.st.model.table.StCVipcomAscriptionDO;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @Descroption 日程归属接口服务
 * <AUTHOR>
 * @Date 2019/3/27 17:51
 */
@Component
@Slf4j
public class VipcomAscriptionProvideService {
    @Autowired
    private StCVipcomAscriptionMapper ascriptionMapper;

    public ValueHolderV14<StCVipcomAscriptionDO> selectVipcomById(Long id) throws NDSException {
        ValueHolderV14<StCVipcomAscriptionDO> valueHolderV14 = new ValueHolderV14<>();
        StCVipcomAscriptionDO stCVipcomAscriptionDO = ascriptionMapper.selectById(id);
        valueHolderV14.setCode(-1);
        valueHolderV14.setMessage("查询失败，无数据记录！");
        if (stCVipcomAscriptionDO != null) {
            valueHolderV14.setCode(0);
            valueHolderV14.setMessage("查询成功！");
            valueHolderV14.setData(stCVipcomAscriptionDO);
        }
        return valueHolderV14;
    }
}
