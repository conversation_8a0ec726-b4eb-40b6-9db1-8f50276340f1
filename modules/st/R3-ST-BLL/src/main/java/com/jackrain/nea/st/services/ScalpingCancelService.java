package com.jackrain.nea.st.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.st.common.StConstant;
import com.jackrain.nea.st.mapper.StCScalpingMapper;
import com.jackrain.nea.st.model.table.StCScalpingDO;
import com.jackrain.nea.st.utils.StBeanUtils;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;

/**
 * @Descroption 刷单方案-反审逻辑
 * <AUTHOR>
 * @Date 2019/3/26
 */
@Component
@Slf4j
@Transactional
public class ScalpingCancelService extends CommandAdapter {
    @Autowired
    private StCScalpingMapper stCMainMapper;

    @Override
    public ValueHolder execute(QuerySession querySession) throws NDSException {
        DefaultWebEvent event = querySession.getEvent();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(
                event.getParameterValue("param"), "yyyy-MM-dd HH:mm:ss"),
                Feature.OrderedField);

        JSONArray itemArray = StBeanUtils.makeAuditJsonArray(param);
        JSONArray errorArray = new JSONArray();
        if (itemArray.size() > 0) {
            for (int i = 0; i < itemArray.size(); i++) {
                Long itemid = itemArray.getLong(i);
                cancelOperationcostByID(querySession, itemid, errorArray);
            }
        }
        return StBeanUtils.getProcessValueHolder(itemArray, errorArray, StConstant.CON_BILL_ACTION_CANCLE_AUDIT);
    }

    private void cancelOperationcostByID(QuerySession session, Long id, JSONArray errorArray) {
        StCScalpingDO stCMainDO = stCMainMapper.selectById(id);
        JSONArray tempArray = new JSONArray();
        checkMianStatus(stCMainDO, id, tempArray);
        if (tempArray.size() > 0) {
            errorArray.addAll(tempArray);
            return;
        }
        StBeanUtils.makeModifierField(stCMainDO, session.getUser());//修改信息
        stCMainDO.setModifierename(session.getUser().getEname());//修改人账号
        stCMainDO.setEstatus(StConstant.CON_BILL_STATUS_01);
        stCMainDO.setReverseId(Long.valueOf(session.getUser().getId()));//反审核人
        stCMainDO.setReverseTime(new Date());//反审核时间
        stCMainDO.setReverseName(session.getUser().getName());//反审核人姓名
        stCMainDO.setReverseEname(session.getUser().getEname());//反审核人账号
        if ((stCMainMapper.updateById(stCMainDO)) <= 0) {
            errorArray.add(StBeanUtils.getJsonObjectInfo(id, "店铺:" + stCMainDO.getCpCShopTitle() + ",方案反审失败！"));
        }
    }

    private void checkMianStatus(StCScalpingDO stCMainDO, Long id, JSONArray errorArray) {
        if (stCMainDO == null) {
            errorArray.add(StBeanUtils.getJsonObjectInfo(id, "当前记录已不存在！"));
            return;
        }
        //不是已审核，不允许反审核
        if (stCMainDO.getEstatus() == null ||
                !StConstant.CON_BILL_STATUS_02.equals(stCMainDO.getEstatus())) {
            errorArray.add(StBeanUtils.getJsonObjectInfo(id, "当前记录不是已审核，不允许反审！"));
            return;
        }
    }
}
