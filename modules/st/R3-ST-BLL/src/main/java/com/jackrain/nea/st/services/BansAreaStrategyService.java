package com.jackrain.nea.st.services;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.burgeon.r3.sg.core.enums.YesNoEnum;
import com.google.common.collect.Lists;
import com.jackrain.nea.cpext.model.result.CpCRegionResult;
import com.jackrain.nea.cpext.model.table.CpCRegionAlias;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.redis.config.CusRedisTemplate;
import com.jackrain.nea.redis.util.RedisOpsUtil;
import com.jackrain.nea.st.annotation.StOperationLog;
import com.jackrain.nea.st.common.StConstant;
import com.jackrain.nea.st.mapper.CpCPhyWarehouseMapper;
import com.jackrain.nea.st.mapper.StCBansAreaStrategyMapper;
import com.jackrain.nea.st.model.enums.OperationTypeEnum;
import com.jackrain.nea.st.model.request.StCBansAreaStrategyRequest;
import com.jackrain.nea.st.model.result.StCBansAreaStrategyResult;
import com.jackrain.nea.st.model.table.CpCPhyWarehouseDO;
import com.jackrain.nea.st.model.table.StCBansAreaStrategy;
import com.jackrain.nea.st.model.table.StCOperationLogDO;
import com.jackrain.nea.st.rpc.RpcCpService;
import com.jackrain.nea.st.utils.ExportUtil;
import com.jackrain.nea.st.utils.JsonUtils;
import com.jackrain.nea.st.utils.StBeanUtils;
import com.jackrain.nea.sys.constants.ResultCode;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.utils.AssertUtils;
import com.jackrain.nea.utils.ValueHolderUtils;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2022/6/13 16:02
 */
@Component
@Slf4j
public class BansAreaStrategyService {
    @Autowired
    private StCBansAreaStrategyMapper mapper;
    @Autowired
    private ExportUtil exportUtil;
    @Autowired
    private RpcCpService rpcCpService;
    @Resource
    private LogCommonService logCommonService;
    @Value("${r3.st.excel.bans.area.strategy.column.size:5}")
    private int remitSize;
    @Value("${r3.st.excel.import.max.error.count:500}")
    private int maxErrorCount;

    @StOperationLog(mainTableName = "ST_C_BANS_AREA_STRATEGY")
    public ValueHolder save(QuerySession querySession) {
        //获取传入参数
        DefaultWebEvent event = querySession.getEvent();
        JSONObject param = JsonUtils.filterNull((JSONObject) event.getParameterValue("param"), null);
        log.info(LogUtil.format("BansAreaStrategyService.save,param:{}",
                "BansAreaStrategyService.save"), JSONObject.toJSONString(param));
        Long id = param.getLong("objid");
        JSONObject data = param.getJSONObject("fixcolumn");
        String str = data.getString(StConstant.ST_C_BANS_AREA_STRATEGY);
        if (StringUtils.isEmpty(str)) {
            throw new NDSException("保存数据为空！");
        }
        //json转换成对象
        StCBansAreaStrategy saveModel =
                JSONObject.parseObject(str, StCBansAreaStrategy.class);
        User user = querySession.getUser();
        if (id == null || id < 1) {
            //校验如果区县不为空，城市必须不为空
            if (Objects.nonNull(saveModel.getCpCRegionAreaId()) && Objects.isNull(saveModel.getCpCCityId())) {
                throw new NDSException("城市为空，不允许保存！");
            }
            //查询新增数据是否重复
            LambdaQueryWrapper<StCBansAreaStrategy> wrapper = getAddQueryWrapper(saveModel);
            Integer count = mapper.selectCount(wrapper);
            if (count != null && count > 0) {
                return ValueHolderUtils.getFailValueHolder("记录已经存在，不允许重复录入！");
            }

            // 物流禁发区清空缓存
            CusRedisTemplate<String, String> redisTemplate = RedisOpsUtil.getStrRedisTemplate();
            redisTemplate.delete("st:bans:express");

            // 新增
            id = ModelUtil.getSequence(StConstant.ST_C_BANS_AREA_STRATEGY);
            saveModel.setId(id);
            StBeanUtils.makeCreateField(saveModel, user);
            mapper.insert(saveModel);
        } else {
            StCBansAreaStrategy beforModel = mapper.selectById(id);
            if (beforModel == null) {
                return ValueHolderUtils.getFailValueHolder("当前记录已不存在！");
            }
            if (Objects.nonNull(saveModel.getCpCRegionAreaId()) && Objects.isNull(saveModel.getCpCCityId())) {
                if (Objects.isNull(beforModel.getCpCCityId())) {
                    throw new NDSException("城市为空，不允许保存！");
                }
            }

            // 物流禁发区清空缓存
            CusRedisTemplate<String, String> redisTemplate = RedisOpsUtil.getStrRedisTemplate();
            redisTemplate.delete("st:bans:express");
            //更新数据查重
            LambdaQueryWrapper<StCBansAreaStrategy> wrapper = getUpdateQueryWrapper(param, saveModel, beforModel);
            Integer count = mapper.selectCount(wrapper);
            if (count != null && count > 0) {
                return ValueHolderUtils.getFailValueHolder("记录已经存在，不允许重复录入！");
            }
            StBeanUtils.makeModifierField(saveModel, user);
            saveModel.setId(beforModel.getId());
            mapper.updateById(saveModel);
        }
        return ValueHolderUtils.getSuccessValueHolder(id, "保存成功！");
    }

    /**
     * 构建更新查重条件
     *
     * @param param
     * @param saveModel
     * @param beforModel
     * @return
     */
    private LambdaQueryWrapper<StCBansAreaStrategy> getUpdateQueryWrapper(JSONObject param, StCBansAreaStrategy saveModel, StCBansAreaStrategy beforModel) {
        Long cpCLogisticsId = saveModel.getCpCLogisticsId();
        Long warehouseId = saveModel.getCpCPhyWarehouseId();
        Long cpCProvinceId = saveModel.getCpCProvinceId();
        Long cpCCityId = saveModel.getCpCCityId();
        Long cpCRegionAreaId = saveModel.getCpCRegionAreaId();
        LambdaQueryWrapper<StCBansAreaStrategy> wrapper = new QueryWrapper<StCBansAreaStrategy>().lambda()
                .eq(StCBansAreaStrategy::getCpCLogisticsId, cpCLogisticsId == null ? beforModel.getCpCLogisticsId() : cpCLogisticsId)
                .eq(StCBansAreaStrategy::getCpCProvinceId, cpCProvinceId == null ? beforModel.getCpCProvinceId() : cpCProvinceId)
                .eq(StCBansAreaStrategy::getIsactive, YesNoEnum.Y.getKey());
        if (warehouseId != null) {
            wrapper.eq(StCBansAreaStrategy::getCpCPhyWarehouseId, warehouseId);
        } else {
            if (param.getJSONObject("fixcolumn").getJSONObject("ST_C_BANS_AREA_STRATEGY").containsKey("CP_C_PHY_WAREHOUSE_ID")
                    || beforModel.getCpCPhyWarehouseId() == null) {
                wrapper.isNull(StCBansAreaStrategy::getCpCPhyWarehouseId);
            } else {
                wrapper.eq(StCBansAreaStrategy::getCpCPhyWarehouseId, beforModel.getCpCPhyWarehouseId());
            }
        }
        if (cpCCityId != null) {
            wrapper.eq(StCBansAreaStrategy::getCpCCityId, cpCCityId);
        } else {
            if (param.getJSONObject("fixcolumn").getJSONObject("ST_C_BANS_AREA_STRATEGY").containsKey("CP_C_CITY_ID")
                    || beforModel.getCpCCityId() == null) {
                wrapper.isNull(StCBansAreaStrategy::getCpCCityId);
            } else {
                wrapper.eq(StCBansAreaStrategy::getCpCCityId, beforModel.getCpCCityId());
            }
        }
        if (cpCRegionAreaId != null) {
            wrapper.eq(StCBansAreaStrategy::getCpCRegionAreaId, cpCRegionAreaId);
        } else {
            if (param.getJSONObject("fixcolumn").getJSONObject("ST_C_BANS_AREA_STRATEGY").containsKey("CP_C_REGION_AREA_ID")
                    || beforModel.getCpCRegionAreaId() == null) {
                wrapper.isNull(StCBansAreaStrategy::getCpCRegionAreaId);
            } else {
                wrapper.eq(StCBansAreaStrategy::getCpCRegionAreaId, beforModel.getCpCRegionAreaId());
            }
        }
        return wrapper;
    }

    /**
     * 构建新增查重条件
     *
     * @param saveModel
     * @return
     */
    private LambdaQueryWrapper<StCBansAreaStrategy> getAddQueryWrapper(StCBansAreaStrategy saveModel) {
        LambdaQueryWrapper<StCBansAreaStrategy> wrapper = new QueryWrapper<StCBansAreaStrategy>().lambda()
                .eq(StCBansAreaStrategy::getCpCLogisticsId, saveModel.getCpCLogisticsId())
                .eq(StCBansAreaStrategy::getCpCProvinceId, saveModel.getCpCProvinceId())
                .eq(StCBansAreaStrategy::getIsactive, YesNoEnum.Y.getKey());
        if (saveModel.getCpCPhyWarehouseId() != null) {
            wrapper.eq(StCBansAreaStrategy::getCpCPhyWarehouseId, saveModel.getCpCPhyWarehouseId());
        } else {
            wrapper.isNull(StCBansAreaStrategy::getCpCPhyWarehouseId);
        }
        if (saveModel.getCpCCityId() != null) {
            wrapper.eq(StCBansAreaStrategy::getCpCCityId, saveModel.getCpCCityId());
        } else {
            wrapper.isNull(StCBansAreaStrategy::getCpCCityId);
        }
        if (saveModel.getCpCRegionAreaId() != null) {
            wrapper.eq(StCBansAreaStrategy::getCpCRegionAreaId, saveModel.getCpCRegionAreaId());
        } else {
            wrapper.isNull(StCBansAreaStrategy::getCpCRegionAreaId);
        }
        return wrapper;
    }

    public ValueHolderV14<String> downLoadImprotTemp(User user) {
        ValueHolderV14<String> vh = new ValueHolderV14<>(ResultCode.SUCCESS, "物流禁发区域导入模板下载成功！");
        // 拼接Excel主表sheet表头字段,生成Excel
        XSSFWorkbook hssfWorkbook = new XSSFWorkbook();
        String filePath = "OSS-Bucket/EXPORT/st_c_bans_area_strategy/";
        String[] mainNames = {"省份", "城市", "区县", "实体仓", "备注"};
        String[] mustNames = {"省份"};
        //String[] orderKeys = {"CP_C_PROVINCE_ID", "CP_C_CITY_ID", "CP_C_REGION_AREA_ID", "REMARK"};
        List<String> mainList = Lists.newArrayList(mainNames);
        List<String> mustList = Lists.newArrayList(mustNames);
        //  List<String> orderKeyList = Lists.newArrayList(orderKeys);
        exportUtil.executeSheet(hssfWorkbook, "物流禁发区域导入", "", mainList, mustList,
                Lists.newArrayList(), Lists.newArrayList(), false);
        String sdd = exportUtil.saveFileAndPutOss(hssfWorkbook, "物流禁发区域导入模板", user, filePath);
        vh.setData(sdd);
        return vh;
    }


    public ValueHolderV14 importBansArea(User user, List<Map<String, String>> excelList, Long logisticsId) {
        ValueHolderV14 vh = new ValueHolderV14<>(ResultCode.SUCCESS, "导入成功！");
        AssertUtils.cannot(StringUtils.isEmpty(logisticsId), "物流公司为空");
        AssertUtils.cannot(ObjectUtils.isEmpty(user), "用户信息为空");

        // 物流禁发区清空缓存
        CusRedisTemplate<String, String> redisTemplate = RedisOpsUtil.getStrRedisTemplate();
        redisTemplate.delete("st:bans:express");

//        LambdaQueryWrapper<StCBansAreaStrategy> deleteWrapper = new LambdaQueryWrapper<>();
//        deleteWrapper.eq(StCBansAreaStrategy::getCpCLogisticsId, logisticsId);
//        mapper.delete(deleteWrapper);

        int index = 1;
        // 修改list
        List<StCBansAreaStrategy> saveList = Lists.newArrayList();
        Map<String, List<String>> errMsgMap = new HashMap<>();
        List<CpCRegionResult> regionAll = rpcCpService.queryRegion();
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("BansAreaStrategyService.importBansArea result= {}",
                    "rpcCpService.queryRegion"), JSONObject.toJSONString(regionAll));
        }
        //根据物流公司查询出来再根据省份分组用于后面比对是否存在相同数据
        List<StCBansAreaStrategy> strategyList =
                mapper.selectList(new LambdaQueryWrapper<StCBansAreaStrategy>()
                        .eq(StCBansAreaStrategy::getCpCLogisticsId, logisticsId)
                        .eq(StCBansAreaStrategy::getIsactive, YesNoEnum.Y.getKey()));
        Map<Long, List<StCBansAreaStrategy>> strategyMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(strategyList)) {
            strategyMap = strategyList.stream().collect(Collectors.groupingBy(StCBansAreaStrategy::getCpCProvinceId));
        }
        Map<String, Integer> excelData = new HashMap<>();
        for (Map<String, String> columnMap : excelList) {
            if (index == 1) {

                log.info(" aaa {}", JSONObject.toJSONString(columnMap), remitSize, columnMap.size());

                if (columnMap.size() != remitSize
                        || !"省份".equals(columnMap.get(ExportUtil.ROW_STR + index + ExportUtil.CELL_STR + 0))
                        || !"实体仓".equals(columnMap.get(ExportUtil.ROW_STR + index + ExportUtil.CELL_STR + 3))
                        || !"城市".equals(columnMap.get(ExportUtil.ROW_STR + index + ExportUtil.CELL_STR + 1))
                        || !"区县".equals(columnMap.get(ExportUtil.ROW_STR + index + ExportUtil.CELL_STR + 2))
                        || !"备注".equals(columnMap.get(ExportUtil.ROW_STR + index + ExportUtil.CELL_STR + 4))) {
                    throw new NDSException("读取表头信息失败，请检查表头是否正确");
                }
            } else {
                if (errMsgMap.size() == maxErrorCount) {
                    break;
                }
                StCBansAreaStrategy model = checkRemitResult(index, columnMap, errMsgMap, user, regionAll, strategyMap, excelData);
                if (!ObjectUtils.isEmpty(model)) {
                    model.setCpCLogisticsId(logisticsId);
                    saveList.add(model);
                }
            }
            index++;
        }
        excelData.clear();
        int size = errMsgMap.size();
        if (size > 0) {
            // 生成存放错误信息excel
            XSSFWorkbook hssfWorkbook = new XSSFWorkbook();
            exportUtil.executeErrorInfoSheet(hssfWorkbook, "错误信息", errMsgMap, "行号");
            String putMsg = exportUtil.saveFileAndPutOss(hssfWorkbook, "物流禁发区域导入错误", user,
                    "OSS-Bucket/EXPORT/st_c_bans_area_strategy/error/");
            vh.setData(putMsg);
            vh.setCode(ResultCode.FAIL);
            vh.setMessage("全部导入失败！");
            return vh;
        }
        if (!CollectionUtils.isEmpty(saveList)) {
            mapper.batchInsert(saveList);
        }
//        if(errMsgMap.size() == 0){
//            vh.setCode(ResultCode.SUCCESS);
//            vh.setMessage("全部导入成功！");
//        }else if(saveList.size()>0){
//            vh.setCode(ResultCode.FAIL);
//            vh.setMessage("部分成功，成功条数："+saveList.size()+",失败条数："+errMsgMap.size());
//        }else{
//            vh.setCode(ResultCode.FAIL);
//            vh.setMessage("全部导入失败！");
//        }
        return vh;
    }

    /**
     * 构建变更对象
     *
     * @param index
     * @param columnMap
     * @param errMsgMap
     * @param user
     * @param strategyMap
     * @param excelData
     * @return
     */
    private StCBansAreaStrategy checkRemitResult(int index, Map<String, String> columnMap, Map<String, List<String>> errMsgMap,
                                                 User user, List<CpCRegionResult> regionAll,
                                                 Map<Long, List<StCBansAreaStrategy>> strategyMap, Map<String, Integer> excelData) {
        StCBansAreaStrategy model = new StCBansAreaStrategy();
        List<String> errorList = Lists.newArrayList();
        // 省份
        String province = columnMap.get(ExportUtil.ROW_STR + index + ExportUtil.CELL_STR + 0);
        if (StringUtils.isEmpty(province)) {
            errorList.add("省份不能为空！");
            return null;
        }
        Long provinceId = getRegionId(regionAll, province, null, null);
        if (provinceId == null) {
            //查询别名
            ValueHolderV14<List<CpCRegionAlias>> v14 = rpcCpService.selectRegionInfo(province, 1);
            if(v14!=null && v14.isOK() && !CollectionUtils.isEmpty(v14.getData())){
                List<CpCRegionAlias> data = v14.getData();
                provinceId = data.get(0).getCpCRegionId();
            }
        }
        if (provinceId == null) {
            errorList.add(province + "在系统中没有维护！");
        }

        model.setCpCProvinceId(provinceId);
        if (province != null) {
            // 城市
            String city = columnMap.get(ExportUtil.ROW_STR + index + ExportUtil.CELL_STR + 1);
            Long cityId = null;
            if (!StringUtils.isEmpty(city)) {
                CpCRegionResult cityRegionResult = getRegionResult(regionAll, province, city, null);
                if (cityRegionResult != null) {
                    cityId = cityRegionResult.getId();
                    if (!ObjectUtil.equals(cityRegionResult.getCUpId(), provinceId)) {
                        errorList.add(city + "不属于" + province + "！");
                    }
                }
                if (cityId == null) {
                    //查询别名
                    ValueHolderV14<List<CpCRegionAlias>> v14City = rpcCpService.selectRegionInfo(city, 2);
                    if (v14City != null && v14City.isOK() && !CollectionUtils.isEmpty(v14City.getData())) {
                        List<CpCRegionAlias> data = v14City.getData();
                        cityId = data.get(0).getCpCRegionId();
                        if (!ObjectUtil.equals(data.get(0).getCUpId(), provinceId)) {
                            errorList.add(city + "不属于" + province + "！");
                        }
                    }
                }
                if (cityId == null) {
                    errorList.add(city + "在系统中没有维护！");
                }
                model.setCpCCityId(cityId);
            }

            // 区县
            String regionArea = columnMap.get(ExportUtil.ROW_STR + index + ExportUtil.CELL_STR + 2);
            Long regionAreaId = null;
            if (!StringUtils.isEmpty(regionArea)) {
                //如果市为空
                if (StringUtils.isEmpty(city)) {
                    errorList.add("城市不能为空！");
                }
                CpCRegionResult areaRegionResult = getRegionResult(regionAll, province, city, regionArea);
                if (areaRegionResult != null) {
                    regionAreaId = areaRegionResult.getId();
                    if (!ObjectUtil.equals(areaRegionResult.getCUpId(), cityId)) {
                        errorList.add(regionArea + "不属于" + city + "！");
                    }
                }
                if (regionAreaId == null) {
                    //查询别名
                    ValueHolderV14<List<CpCRegionAlias>> v14Region = rpcCpService.selectRegionInfo(regionArea, 3);
                    if (v14Region != null && v14Region.isOK() && !CollectionUtils.isEmpty(v14Region.getData())) {
                        List<CpCRegionAlias> data = v14Region.getData();
                        regionAreaId = data.get(0).getCpCRegionId();
                        if (!ObjectUtil.equals(data.get(0).getCUpId(), cityId)) {
                            errorList.add(regionArea + "不属于" + city + "！");
                        }
                    }
                }
                if (regionAreaId == null) {
                    errorList.add(regionArea + "在系统中没有维护！");
                }
                model.setCpCRegionAreaId(regionAreaId);
            }
        }

        // 实体仓
        String warehouseName = columnMap.get(ExportUtil.ROW_STR + index + ExportUtil.CELL_STR + 3);
        if (!StringUtils.isEmpty(warehouseName)) {
            CpCPhyWarehouseMapper warehouseMapper = ApplicationContextHandle.getBean(CpCPhyWarehouseMapper.class);
            LambdaQueryWrapper<CpCPhyWarehouseDO> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(CpCPhyWarehouseDO::getEname, warehouseName);
            wrapper.eq(CpCPhyWarehouseDO::getIsactive, StConstant.ISACTIVE_Y);
            List<CpCPhyWarehouseDO> warehouseDOList = warehouseMapper.selectList(wrapper);
            if (CollectionUtils.isEmpty(warehouseDOList)) {
                errorList.add(warehouseName + "在系统中没有维护！");
            } else {
                model.setCpCPhyWarehouseId(warehouseDOList.get(0).getId());
            }
        }

        // 备注
        model.setRemark(columnMap.get(ExportUtil.ROW_STR + index + ExportUtil.CELL_STR + 4));
        String dataKey = model.getCpCProvinceId() + "&" + model.getCpCPhyWarehouseId() + "&" + model.getCpCCityId() + "&" + model.getCpCRegionAreaId();
        if (excelData.containsKey(dataKey)) {
            Integer line = excelData.get(dataKey);
            errorList.add("与表格中第" + line + "行的数据相同！");
        } else {
            excelData.put(dataKey, index);
        }
        if (errorList.size() == 0) {
            List<StCBansAreaStrategy> strategyList = strategyMap.get(model.getCpCProvinceId());
            if (!CollectionUtils.isEmpty(strategyList)) {
                for (StCBansAreaStrategy strategy : strategyList) {
                    if (ObjectUtil.equals(strategy.getCpCPhyWarehouseId(), model.getCpCPhyWarehouseId())
                            && ObjectUtil.equals(strategy.getCpCProvinceId(), model.getCpCProvinceId())
                            && ObjectUtil.equals(strategy.getCpCCityId(), model.getCpCCityId())
                            && ObjectUtil.equals(strategy.getCpCRegionAreaId(), model.getCpCRegionAreaId())) {
                        errorList.add("已存在相同记录！");
                        break;
                    }
                }
            }
        }
        if (errorList.size() != 0) {
            errMsgMap.put(index + "", errorList);
            return null;
        }
        model.setId(ModelUtil.getSequence(StConstant.ST_C_BANS_AREA_STRATEGY));
        StBeanUtils.makeCreateField(model, user);
        return model;
    }


    Long getRegionId(List<CpCRegionResult> regionAll, String province, String city, String regionArea) {
        if (CollectionUtils.isEmpty(regionAll)) {
            return null;
        }
        for (CpCRegionResult cpCRegionResult : regionAll) {
            if (!cpCRegionResult.getEname().equals(province)) {
                continue;
            }
            if (StringUtils.isEmpty(city)) {
                return cpCRegionResult.getId();
            }
            List<CpCRegionResult> regionCity = cpCRegionResult.getRegionList();
            if (CollectionUtils.isEmpty(regionCity)) {
                return null;
            }
            for (CpCRegionResult cRegionResult : regionCity) {
                if (!city.equals(cRegionResult.getEname())) {
                    continue;
                }
                if (StringUtils.isEmpty(regionArea)) {
                    return cRegionResult.getId();
                }
                List<CpCRegionResult> regionAreas = cRegionResult.getRegionList();
                if (CollectionUtils.isEmpty(regionAreas)) {
                    return null;
                }
                for (CpCRegionResult area : regionAreas) {
                    if (regionArea.equals(area.getEname())) {
                        return area.getId();
                    }
                }
            }
        }
        return null;
    }

    CpCRegionResult getRegionResult(List<CpCRegionResult> regionAll, String province, String city, String regionArea) {
        if (CollectionUtils.isEmpty(regionAll)) {
            return null;
        }
        for (CpCRegionResult cpCRegionResult : regionAll) {
            if (!cpCRegionResult.getEname().equals(province)) {
                continue;
            }
            if (StringUtils.isEmpty(city)) {
                return cpCRegionResult;
            }
            List<CpCRegionResult> regionCity = cpCRegionResult.getRegionList();
            if (CollectionUtils.isEmpty(regionCity)) {
                return null;
            }
            for (CpCRegionResult cRegionResult : regionCity) {
                if (!city.equals(cRegionResult.getEname())) {
                    continue;
                }
                if (StringUtils.isEmpty(regionArea)) {
                    return cRegionResult;
                }
                List<CpCRegionResult> regionAreas = cRegionResult.getRegionList();
                if (CollectionUtils.isEmpty(regionAreas)) {
                    return null;
                }
                for (CpCRegionResult area : regionAreas) {
                    if (regionArea.equals(area.getEname())) {
                        return area;
                    }
                }
            }
        }
        return null;
    }

    public ValueHolderV14<StCBansAreaStrategyResult> queryBansAreaStrategy(StCBansAreaStrategyRequest request) {

        if (log.isDebugEnabled()) {
            log.debug("BansAreaStrategyService.StCBansAreaStrategyRequest request:{}", JSONObject.toJSONString(request));
        }

        if (request == null || request.getCpCPhyWarehouseId() == null || request.getCpCProvinceId() == null) {
            return new ValueHolderV14<>(ResultCode.FAIL, "请求参数不能为空");
        }

        LambdaQueryWrapper<StCBansAreaStrategy> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(StCBansAreaStrategy::getCpCProvinceId, request.getCpCProvinceId());
        wrapper.and(x -> x.eq(StCBansAreaStrategy::getCpCPhyWarehouseId, request.getCpCPhyWarehouseId()).or().isNull(StCBansAreaStrategy::getCpCPhyWarehouseId));
        if (request.getCpCCityId() == null) {
            wrapper.isNull(StCBansAreaStrategy::getCpCCityId).isNull(StCBansAreaStrategy::getCpCRegionAreaId);
        } else {
            wrapper.and(x -> x.eq(StCBansAreaStrategy::getCpCCityId, request.getCpCCityId()).or().isNull(StCBansAreaStrategy::getCpCCityId));
            if (request.getCpCRegionAreaId() == null) {
                wrapper.isNull(StCBansAreaStrategy::getCpCRegionAreaId);
            } else {
                wrapper.and(x -> x.eq(StCBansAreaStrategy::getCpCRegionAreaId, request.getCpCRegionAreaId()).or().isNull(StCBansAreaStrategy::getCpCRegionAreaId));
            }
        }
        wrapper.eq(StCBansAreaStrategy::getIsactive, StConstant.ISACTIVE_Y);
        List<StCBansAreaStrategy> result = mapper.selectList(wrapper);

        StCBansAreaStrategyResult obj = new StCBansAreaStrategyResult();
        obj.setCpCLogisticsIdList(result.stream().map(StCBansAreaStrategy::getCpCLogisticsId).collect(Collectors.toList()));

        return new ValueHolderV14<>(obj, ResultCode.SUCCESS, "SUCCESS");
    }

    @Transactional(rollbackFor = Exception.class)
    public ValueHolder delete(QuerySession querySession) {
        DefaultWebEvent event = querySession.getEvent();
        JSONObject param = JsonUtils.filterNull((JSONObject) event.getParameterValue("param"), null);
        log.info(LogUtil.format("BansAreaStrategyService.delete,param:{}",
                "BansAreaStrategyService.delete"), JSONObject.toJSONString(param));
        Long id = param.getLong("objid");

        StCBansAreaStrategy strategy = mapper.selectById(id);
        if (strategy == null) {
            return ValueHolderUtils.getFailValueHolder("记录已不存在！");
        }
        if (YesNoEnum.N.getKey().equals(strategy.getIsactive())) {
            return ValueHolderUtils.getFailValueHolder("已作废数据不允许删除！");
        }

        // 物流禁发区清空缓存
        CusRedisTemplate<String, String> redisTemplate = RedisOpsUtil.getStrRedisTemplate();
        redisTemplate.delete("st:bans:express");

//        mapper.deleteById(id);
        StCBansAreaStrategy updateStrategy = new StCBansAreaStrategy();
        updateStrategy.setId(strategy.getId());
        updateStrategy.setIsactive(YesNoEnum.N.getKey());
        StBeanUtils.makeModifierField(updateStrategy, querySession.getUser());
        mapper.updateById(updateStrategy);

        //增加操作日志
        StCOperationLogDO operationLog = logCommonService.getOperationLog("ST_C_BANS_AREA_STRATEGY",
                OperationTypeEnum.VOID.getOperationValue(), id, "物流禁发区域设置",
                "删除（作废)", null, null, querySession.getUser());
        logCommonService.insertLog(operationLog);
        return ValueHolderUtils.getSuccessValueHolder(id, "保存成功！");
    }
}
