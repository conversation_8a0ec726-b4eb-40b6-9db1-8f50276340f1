package com.jackrain.nea.st.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.base.Throwables;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.oc.oms.model.resources.OmsRedisKeyResources;
import com.jackrain.nea.psext.model.table.PsCProCategory;
import com.jackrain.nea.redis.util.RedisOpsUtil;
import com.jackrain.nea.st.common.StConstant;
import com.jackrain.nea.st.mapper.StCMergeCategoryLimitItemMapper;
import com.jackrain.nea.st.mapper.StCMergeOrderMapper;
import com.jackrain.nea.st.model.request.MergeOderRequest;
import com.jackrain.nea.st.model.table.StCMergeCategoryLimitItemDO;
import com.jackrain.nea.st.model.table.StCMergeOrderDO;
import com.jackrain.nea.st.rpc.RpcPsService;
import com.jackrain.nea.st.utils.JsonUtils;
import com.jackrain.nea.st.utils.StBeanUtils;
import com.jackrain.nea.st.utils.ValueHolderUtils;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.common.utils.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.sql.Timestamp;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description 订单合并策略新增修改业务类
 * @Date 2019/3/12
 * @Param
 * @return
 **/
@Component
@Slf4j
@Transactional
public class MergeOrderSaveService extends CommandAdapter {

    @Autowired
    private StCMergeOrderMapper stCMergeOrderMapper;

    @Autowired
    private StCMergeCategoryLimitItemMapper mergeCategoryLimitItemMapper;

    @Autowired
    private RedisOpsUtil redisUtil;

    @Autowired
    private RpcPsService rpcPsService;

    /**
     * @return com.jackrain.nea.util.ValueHolder
     * <AUTHOR>
     * @Description 新增修改主方法
     * @Date 2019/3/12
     * @Param [session]
     **/
    public ValueHolder saveMergeOrder(QuerySession session) {
        ValueHolder valueHolder = new ValueHolder();
        DefaultWebEvent event = session.getEvent();
        JSONObject param = JsonUtils.filterNull((JSONObject) event.getParameterValue("param"), null);
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("请求JSON：{}"), param);
        }
        //1.拉取请求参数，解析
        Long id = param.getLong("objid");//获取objid参数
        JSONObject fixColumn = param.getJSONObject("fixcolumn");//获取表数据
        //2.转换为请求bean，判断转是否成功校验参数
        MergeOderRequest mergeOderRequest = JsonUtils.jsonParseClass(fixColumn, MergeOderRequest.class);
        if (fixColumn == null || id == null) {
            throw new NDSException("参数异常");
        }
        if (mergeOderRequest != null) {
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("订单合单策略入参：{}"), mergeOderRequest);
            }
            //店铺清除rediskey
            delRedisKey();
            if (id != -1) {
                valueHolder = updateMergeOrder(session, mergeOderRequest, id);
            } else {
                valueHolder = addMergeOrder(session, mergeOderRequest);
            }
            if ((int) valueHolder.get("code") == ResultCode.SUCCESS) {
                this.saveCategoryLimitItem(session.getUser(), fixColumn, id);
            }
            return valueHolder;
        } else {
            this.saveCategoryLimitItem(session.getUser(), fixColumn, id);
        }
        return ValueHolderUtils.getSuccessValueHolder(id, StConstant.TAB_ST_C_MERGE_ORDER);
    }

    /**
     * @return com.jackrain.nea.util.ValueHolder
     * <AUTHOR>
     * @Description 保存方法
     * @Date 2019/3/13
     * @Param [session, mergeOderRequest, id]
     **/
    private ValueHolder updateMergeOrder(QuerySession session, MergeOderRequest mergeOderRequest, Long id) {
        StCMergeOrderDO stCMergeOrderDO = mergeOderRequest.getStCMergeOrderDO();
        if (stCMergeOrderDO != null) {
            StCMergeOrderDO stCMergeOrderDO1 = stCMergeOrderMapper.selectById(id);
            if (stCMergeOrderDO1 == null) {
                throw new NDSException("当前记录已不存在！");
            }

            //update基础字段
            stCMergeOrderDO.setId(id);
            stCMergeOrderDO.setModifierid(Long.valueOf(session.getUser().getId()));
            stCMergeOrderDO.setModifieddate(new Timestamp(System.currentTimeMillis()));
            stCMergeOrderDO.setModifiername(session.getUser().getName());
            stCMergeOrderDO.setModifierename(session.getUser().getEname());
            int count = stCMergeOrderMapper.updateById(stCMergeOrderDO);
            if (count < 0) {
                log.debug(LogUtil.format("订单合并策略主表更新失败！"));
                return ValueHolderUtils.getFailValueHolder("保存失败");
            }
        }
        return ValueHolderUtils.getSuccessValueHolder(id, StConstant.TAB_ST_C_MERGE_ORDER);
    }

    /**
     * @return com.jackrain.nea.util.ValueHolder
     * <AUTHOR>
     * @Description 新增方法
     * @Date 2019/3/13
     * @Param [session, sellOwnGoodsRequest]
     **/
    private ValueHolder addMergeOrder(QuerySession session, MergeOderRequest mergeOderRequest) {
        StCMergeOrderDO stCMergeOrderDO = mergeOderRequest.getStCMergeOrderDO();
        long id = -1;
        if (stCMergeOrderDO != null) {
            //主表实体
            stCMergeOrderDO.setId(ModelUtil.getSequence(StConstant.TAB_ST_C_MERGE_ORDER));
            id = stCMergeOrderDO.getId();
            stCMergeOrderDO.setIsactive("Y");
            //基本字段值设置
            StBeanUtils.makeCreateField(stCMergeOrderDO, session.getUser());
            stCMergeOrderDO.setModifierename(session.getUser().getEname());
            stCMergeOrderDO.setOwnerename(session.getUser().getEname());
            int insertResult = stCMergeOrderMapper.insert(stCMergeOrderDO);
            if (insertResult <= 0) {
                if (log.isInfoEnabled()) {
                    log.debug(LogUtil.format("订单合并策略插入表失败！"));
                }
                return ValueHolderUtils.getFailValueHolder("订单合并策略保存失败！");
            }
        }
        return ValueHolderUtils.getSuccessValueHolder(id, StConstant.TAB_ST_C_MERGE_ORDER);
    }

    private void delRedisKey() {
        try {
            String redisKey = OmsRedisKeyResources.buildAutoMergeAllListRedisKey();
            if (redisUtil.strRedisTemplate.hasKey(redisKey)) {
                redisUtil.strRedisTemplate.delete(redisKey);
            }
        } catch (Exception e) {
            log.error(LogUtil.format("redis删除自动合单策略异常：{}"), Throwables.getStackTraceAsString(e));
        }
    }

    private void saveCategoryLimitItem(User user, JSONObject fixColumn, Long id) {
        String categoryLimitItem = fixColumn.getString(StConstant.TAB_ST_C_MERGE_CATEGORY_LIMIT_ITEM);
        if (StringUtils.isNotEmpty(categoryLimitItem)) {
            try {
                List<StCMergeCategoryLimitItemDO> stCMergeCategoryLimitItemDOList = JSON.parseArray(categoryLimitItem, StCMergeCategoryLimitItemDO.class);
                if (stCMergeCategoryLimitItemDOList.isEmpty() || stCMergeCategoryLimitItemDOList.size() <= 0) {
                    return;
                }
                //订单合并品类限制明细保存
                for (StCMergeCategoryLimitItemDO stCMergeCategoryLimitItemDO : stCMergeCategoryLimitItemDOList) {
                    if (stCMergeCategoryLimitItemDO.getId() != null && stCMergeCategoryLimitItemDO.getId() > 0) {
                        //品类限制明细修改
                        StBeanUtils.makeModifierField(stCMergeCategoryLimitItemDO, user);//修改信息
                        if ((mergeCategoryLimitItemMapper.updateById(stCMergeCategoryLimitItemDO)) <= 0) {
                            throw new NDSException("订单合并策略-品类限制明细修改失败！");
                        }
                    } else {
                        //校验品类信息
                        this.checkData(id, stCMergeCategoryLimitItemDO);
                        PsCProCategory psCProCategory = rpcPsService.queryProCategoryById(stCMergeCategoryLimitItemDO.getPsCProCategoryId());
                        if (psCProCategory != null) {
                            stCMergeCategoryLimitItemDO.setPsCProCategoryEcode(psCProCategory.getEcode());
                            stCMergeCategoryLimitItemDO.setPsCProCategoryEname(psCProCategory.getEname());
                        }
                        //品类限制明细创建
                        stCMergeCategoryLimitItemDO.setStCMergeOrderId(id);
                        stCMergeCategoryLimitItemDO.setId(ModelUtil.getSequence(StConstant.TAB_ST_C_MERGE_CATEGORY_LIMIT_ITEM));
                        StBeanUtils.makeCreateField(stCMergeCategoryLimitItemDO, user);//创建信息
                        if ((mergeCategoryLimitItemMapper.insert(stCMergeCategoryLimitItemDO)) <= 0) {
                            throw new NDSException("订单合并策略-品类限制明细保存失败！");
                        }
                    }
                }
            } catch (Exception ex) {
                log.error(LogUtil.format("MergeCategoryLimitSaveService.saveMergeCategoryLimitItem.Error：{}"),
                        Throwables.getStackTraceAsString(ex));
                throw new NDSException("订单合并策略品类限制明细保存异常:" + ex.getMessage());
            }
        }
    }

    private void checkData(Long id, StCMergeCategoryLimitItemDO stCMergeCategoryLimitItemDO) {
        if (stCMergeCategoryLimitItemDO.getPsCProCategoryId() != null && stCMergeCategoryLimitItemDO.getPsCProCategoryId() > 0) {
            List<StCMergeCategoryLimitItemDO> stCMergeCategoryLimitItemDOID = mergeCategoryLimitItemMapper.selectList(new LambdaQueryWrapper<StCMergeCategoryLimitItemDO>()
                    .eq(StCMergeCategoryLimitItemDO::getStCMergeOrderId, id)
                    .eq(StCMergeCategoryLimitItemDO::getPsCProCategoryId, stCMergeCategoryLimitItemDO.getPsCProCategoryId()));
            if (!stCMergeCategoryLimitItemDOID.isEmpty() && stCMergeCategoryLimitItemDOID.size() > 0) {
                throw new NDSException("品类已存在，不可重复添加!");
            }
        }
        if (stCMergeCategoryLimitItemDO.getSinglePackageMaxNum() < 1) {
            throw new NDSException("单包裹数量值必须为正整数型!");
        }
        if (stCMergeCategoryLimitItemDO.getOtherCategoryMaxNum() < 1) {
            throw new NDSException("其它品类最多容纳数必须为正整数型!");
        }
    }

}
