package com.jackrain.nea.st.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.st.annotation.StOperationLog;
import com.jackrain.nea.st.mapper.StCExpressCostItemMapper;
import com.jackrain.nea.st.mapper.StCExpressCostMapper;
import com.jackrain.nea.st.model.common.StRedisConstant;
import com.jackrain.nea.st.model.enums.CloseStatusEnum;
import com.jackrain.nea.st.model.enums.SubmitStatusEnum;
import com.jackrain.nea.st.model.table.StCExpressCost;
import com.jackrain.nea.st.model.table.StCExpressCostItem;
import com.jackrain.nea.st.utils.RedisCacheUtil;
import com.jackrain.nea.st.utils.StBeanUtils;
import com.jackrain.nea.st.utils.ValueHolderUtils;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/8/8 15:24
 * @Description
 */
@Component
@Slf4j
public class StCExpressCostSubmitService extends CommandAdapter {

    @Autowired
    private StCExpressCostMapper stCExpressCostMapper;

    @Autowired
    private StCExpressCostItemMapper stCExpressCostItemMapper;

    @StOperationLog(operationType = "AUDIT", mainTableName = "ST_C_EXPRESS_COST", itemsTableName = "ST_C_EXPRESS_COST_ITEM")
    @Override
    public ValueHolder execute(QuerySession querySession) throws NDSException {
        DefaultWebEvent event = querySession.getEvent();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(
                event.getParameterValue("param"), "yyyy-MM-dd HH:mm:ss"), Feature.OrderedField);
        if (param == null) {
            throw new NDSException("参数为空！");
        }
        JSONArray voidArray = StBeanUtils.makeUnAuditJsonArray(param);
        if (voidArray.size() == 1) {
            voidAction(voidArray.getLong(0),querySession);
        }else {
            int success = 0;
            int fail = 0;
            for (int i = 0; i < voidArray.size(); i++) {
                Long id = voidArray.getLong(i);
                try {
                    voidAction(id,querySession);
                    success++;
                } catch (Exception e) {
                    fail++;
                }
            }
            if (success != voidArray.size()) {
                return ValueHolderUtils.getFailValueHolder("快运报价设置提交成功" + success + "条,失败" + fail + "条");
            }
        }

        return ValueHolderUtils.getSuccessValueHolder("提交成功");
    }

    private void voidAction(Long id, QuerySession querySession) {
        User user = querySession.getUser();
        StCExpressCost expressCost = stCExpressCostMapper.selectById(id);
        //判断主表是否存在
        if (expressCost == null){
            throw new NDSException("当前记录不存在！");
        }
        if (!SubmitStatusEnum.NO_SUBMIT.getKey().equals(expressCost.getStatus())){
            throw new NDSException("当前单据状态，不允许提交！");
        }
        //判断是否有明细
        Integer count = stCExpressCostItemMapper.selectCount(new LambdaQueryWrapper<StCExpressCostItem>()
                .eq(StCExpressCostItem::getStCExpressCostId, expressCost.getId()));
        if (count <= 0) {
            throw new NDSException("请维护明细！");
        }
        //判断是否冲突
        List<StCExpressCost> selectList = stCExpressCostMapper.selectList(new LambdaQueryWrapper<StCExpressCost>()
                .eq(StCExpressCost::getCpCPhyWarehouseId, expressCost.getCpCPhyWarehouseId())
                .eq(StCExpressCost::getCpCLogisticsId, expressCost.getCpCLogisticsId())
                .eq(StCExpressCost::getStatus, SubmitStatusEnum.SUBMIT.getKey())
                .eq(StCExpressCost::getCloseStatus, CloseStatusEnum.NO_CLOSE.getKey()));
        if (CollectionUtils.isNotEmpty(selectList)){
            for (StCExpressCost cost : selectList) {
                Boolean flag = checkTime(expressCost.getStartDate(),expressCost.getEndDate(),cost.getStartDate(),cost.getEndDate());
                if (!flag) {
                    throw new NDSException("不允许有两个有效快运报价设置！");
                }
            }
        }
        StCExpressCost newCost = new StCExpressCost();
        newCost.setId(expressCost.getId());
        newCost.setRemark(expressCost.getRemark());
        newCost.setStatus(SubmitStatusEnum.SUBMIT.getKey());
        newCost.setSubmitterid(Long.valueOf(user.getId()));
        newCost.setSubmitTime(new Date());
        StBeanUtils.makeModifierField(newCost, querySession.getUser());
        int update = stCExpressCostMapper.updateById(newCost);
        if (update <= 0) {
            throw new NDSException("更新失败！");
        }

        // 清除快运报价关系缓存
        String redisKey = StRedisConstant.buildExpressCostRelationKey(expressCost.getCpCPhyWarehouseId(), expressCost.getCpCLogisticsId());
        RedisCacheUtil.deleteAll(redisKey);
        log.info("清除快运报价关系缓存成功，key={}", redisKey);

        // 清除根据ID查询的分布式缓存
        String idCacheKey = StRedisConstant.buildExpressCostRelationByIdKey(expressCost.getId());
        RedisCacheUtil.deleteAll(idCacheKey);
        log.info("清除快运报价ID缓存成功，key={}", idCacheKey);
    }

    private Boolean checkTime(Date startDate, Date endDate, Date startDate1, Date endDate1) {
        if (endDate.compareTo(endDate1) == 0){
            return false;
        }else {
            if (endDate.compareTo(endDate1) == 1){
                //endDate大，则比较startDate是否小于等于endDate1,是标识有交叉
                if (endDate1.compareTo(startDate) == 1) {
                    return false;
                }
            }else {
                //endDate1大，则比较startDate1是否小于等于endDate,是标识有交叉
                if (endDate.compareTo(startDate1) == 1) {
                    return false;
                }
            }
        }
        return true;
    }

}
