package com.jackrain.nea.st.services;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.burgeon.r3.sg.channel.model.enumerate.YseNoEnum;
import com.burgeon.r3.sg.core.enums.YesNoEnum;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.redis.config.CusRedisTemplate;
import com.jackrain.nea.redis.util.RedisOpsUtil;
import com.jackrain.nea.st.common.StConstant;
import com.jackrain.nea.st.mapper.StCKeywordsInterceptStrategyMapper;
import com.jackrain.nea.st.model.common.StCConstants;
import com.jackrain.nea.st.model.table.StCKeywordsInterceptStrategy;
import com.jackrain.nea.st.utils.JsonUtils;
import com.jackrain.nea.st.utils.StBeanUtils;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.utils.ValueHolderUtils;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/3/10
 */
@Component
@Slf4j
public class StCKeywordsInterceptStrategyService {
    @Autowired
    private StCKeywordsInterceptStrategyMapper mapper;

    public ValueHolder save(QuerySession querySession) {
        //获取传入参数
        DefaultWebEvent event = querySession.getEvent();
        JSONObject param = JsonUtils.filterNull((JSONObject) event.getParameterValue("param"), null);
        log.info(LogUtil.format("StCKeywordsInterceptStrategyService.save,param:{}",
                "StCKeywordsInterceptStrategyService.save"), JSONObject.toJSONString(param));
        Long id = param.getLong("objid");
        JSONObject data = param.getJSONObject("fixcolumn");
        String str = data.getString(StConstant.ST_C_KEYWORDS_INTERCEPT_STRATEGY);
        if (StringUtils.isEmpty(str)) {
            throw new NDSException("保存数据为空！");
        }
        //json转换成对象
        StCKeywordsInterceptStrategy saveModel =
                JSONObject.parseObject(str, StCKeywordsInterceptStrategy.class);
        User user = querySession.getUser();
        if (id == null || id < 1) {
            // 新增
            id = ModelUtil.getSequence(StConstant.ST_C_KEYWORDS_INTERCEPT_STRATEGY);
            saveModel.setId(id);
            StBeanUtils.makeCreateField(saveModel, user);
            saveModel.setIsactive(YseNoEnum.NO.getCode());
            mapper.insert(saveModel);
        } else {
            return ValueHolderUtils.getFailValueHolder("关键字快递拦截策略不允许修改");
        }
        return ValueHolderUtils.getSuccessValueHolder(id, "保存成功！");
    }

    public ValueHolder delete(QuerySession querySession) {
        DefaultWebEvent event = querySession.getEvent();
        JSONObject param = JsonUtils.filterNull((JSONObject) event.getParameterValue("param"), null);
        log.info(LogUtil.format("StCKeywordsInterceptStrategyService.delete,param:{}",
                "StCKeywordsInterceptStrategyService.delete"), JSONObject.toJSONString(param));
        Long id = param.getLong("objid");

        StCKeywordsInterceptStrategy keywordsInterceptStrategy = mapper.selectById(id);
        if (keywordsInterceptStrategy == null) {
            return ValueHolderUtils.getFailValueHolder("记录已不存在！");
        }
        if (YesNoEnum.Y.getKey().equals(keywordsInterceptStrategy.getIsactive())) {
            return ValueHolderUtils.getFailValueHolder("启用数据不允许删除！");
        }
        mapper.deleteById(id);
        return ValueHolderUtils.getSuccessValueHolder(id, "删除成功！");
    }

    public ValueHolder enable(QuerySession querySession) {
        DefaultWebEvent event = querySession.getEvent();
        JSONObject param = (JSONObject) event.getParameterValue("param");
        log.info(LogUtil.format("StCKeywordsInterceptStrategyService.enable,param:{}",
                "StCKeywordsInterceptStrategyService.enable"), JSONObject.toJSONString(param));
        JSONArray ids = param.getJSONArray("ids");
        if (ids == null || ids.size() == 0) {
            return ValueHolderUtils.getFailValueHolder("勾选数据为空！");
        }
        try {
            List<StCKeywordsInterceptStrategy> strategyList = checkByEnable(ids.toJavaList(Long.class));
            for (StCKeywordsInterceptStrategy strategy : strategyList) {
                enableOne(strategy, querySession.getUser());
            }
        } catch (Exception e) {
            return ValueHolderUtils.getFailValueHolder(e.getMessage());
        }
        return ValueHolderUtils.getSuccessValueHolder("启用成功！");
    }

    /**
     * 校验策略状态（启用）
     *
     * @param idList
     * @return
     */
    private List<StCKeywordsInterceptStrategy> checkByEnable(List<Long> idList) {
        List<StCKeywordsInterceptStrategy> strategyList = mapper.selectBatchIds(idList);
        if (CollectionUtils.isEmpty(strategyList)) {
            throw new NDSException("记录已不存在！");
        }
        for (StCKeywordsInterceptStrategy strategy : strategyList) {
            if (YesNoEnum.Y.getKey().equals(strategy.getIsactive())) {
                throw new NDSException("数据已启用！");
            }
        }
        return strategyList;
    }

    /**
     * 逐个启用
     *
     * @param strategy
     * @param user
     */
    private void enableOne(StCKeywordsInterceptStrategy strategy, User user) {
        StCKeywordsInterceptStrategy updateStrategy = new StCKeywordsInterceptStrategy();
        updateStrategy.setId(strategy.getId());
        updateStrategy.setIsactive(YseNoEnum.YES.getCode());
        StBeanUtils.makeModifierField(updateStrategy, user);
        mapper.updateById(updateStrategy);
        // 处理缓存
        delCache(strategy.getCpCPlatformId());
    }

    public ValueHolder disable(QuerySession querySession) {
        DefaultWebEvent event = querySession.getEvent();
        JSONObject param = (JSONObject) event.getParameterValue("param");
        log.info(LogUtil.format("StCKeywordsInterceptStrategyService.disable,param:{}",
                "StCKeywordsInterceptStrategyService.disable"), JSONObject.toJSONString(param));
        JSONArray ids = param.getJSONArray("ids");
        if (ids == null || ids.size() == 0) {
            return ValueHolderUtils.getFailValueHolder("勾选数据为空！");
        }
        try {
            List<StCKeywordsInterceptStrategy> strategyList = checkByDisable(ids.toJavaList(Long.class));
            for (StCKeywordsInterceptStrategy strategy : strategyList) {
                disableOne(strategy, querySession.getUser());
            }
        } catch (Exception e) {
            return ValueHolderUtils.getFailValueHolder(e.getMessage());
        }
        return ValueHolderUtils.getSuccessValueHolder("禁用成功！");
    }

    /**
     * 校验策略状态（禁用）
     *
     * @param idList
     * @return
     */
    private List<StCKeywordsInterceptStrategy> checkByDisable(List<Long> idList) {
        List<StCKeywordsInterceptStrategy> strategyList = mapper.selectBatchIds(idList);
        if (CollectionUtils.isEmpty(strategyList)) {
            throw new NDSException("记录已不存在！");
        }
        for (StCKeywordsInterceptStrategy strategy : strategyList) {
            if (YesNoEnum.N.getKey().equals(strategy.getIsactive())) {
                throw new NDSException("数据已禁用！");
            }
        }
        return strategyList;
    }

    /**
     * 逐个禁用
     *
     * @param strategy
     * @param user
     */
    private void disableOne(StCKeywordsInterceptStrategy strategy, User user) {
        StCKeywordsInterceptStrategy updateStrategy = new StCKeywordsInterceptStrategy();
        updateStrategy.setId(strategy.getId());
        updateStrategy.setIsactive(YseNoEnum.NO.getCode());
        StBeanUtils.makeModifierField(updateStrategy, user);
        mapper.updateById(updateStrategy);
        // 处理缓存
        delCache(strategy.getCpCPlatformId());
    }

    private void delCache(Long platformId) {
        String key = StCConstants.ST_C_KEYWORDS_INTERCEPT_STRATEGY + platformId;
        CusRedisTemplate<String, String> redisTemplate = RedisOpsUtil.getStrRedisTemplate();
        redisTemplate.delete(key);
    }

    /**
     * @param platformId
     * @return
     */
    public List<StCKeywordsInterceptStrategy> queryByPlatformId(Long platformId) {
        LambdaQueryWrapper<StCKeywordsInterceptStrategy> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(StCKeywordsInterceptStrategy::getCpCPlatformId, platformId);
        queryWrapper.eq(StCKeywordsInterceptStrategy::getIsactive, YseNoEnum.YES.getCode());
        return mapper.selectList(queryWrapper);
    }
}
