package com.jackrain.nea.st.services;

import com.google.common.base.Throwables;
import com.jackrain.nea.config.PropertiesConf;
import com.jackrain.nea.cp.entity.CpCOrgChannelEntity;
import com.jackrain.nea.cp.entity.CpCOrgChannelItemEntity;
import com.jackrain.nea.cp.request.CpCOrgChannelQueryCmdRequest;
import com.jackrain.nea.cpext.model.table.CpShop;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.ip.model.SpecialbarcodeRetModel;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.psext.model.table.PsCPro;
import com.jackrain.nea.psext.model.table.PsCSku;
import com.jackrain.nea.st.common.StConstant;
import com.jackrain.nea.st.mapper.StCProductStrategyItemMapper;
import com.jackrain.nea.st.mapper.StCProductStrategyMapper;
import com.jackrain.nea.st.model.request.ProductStrategyItemRequest;
import com.jackrain.nea.st.model.table.StCProductStrategyDO;
import com.jackrain.nea.st.model.table.StCProductStrategyItemDO;
import com.jackrain.nea.st.rpc.RpcCpService;
import com.jackrain.nea.st.rpc.RpcIpService;
import com.jackrain.nea.st.rpc.RpcPsService;
import com.jackrain.nea.st.utils.DatasToEsUtils;
import com.jackrain.nea.st.utils.ListPageUtil;
import com.jackrain.nea.st.utils.StBeanUtils;
import com.jackrain.nea.st.utils.ValueHolderUtils;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.utils.AssertUtils;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description 店铺商品特殊设置新增修改业务类
 * @Date 2019/3/12
 **/
@Component
@Slf4j
@Transactional
public class ProductStrategySaveService extends CommandAdapter {

    @Autowired
    private StCProductStrategyMapper stCProductStrategyMapper;
    @Autowired
    private StCProductStrategyItemMapper stCProductStrategyItemMapper;
    @Autowired
    private ProductStrategyItemService productStrategyItemService;
    @Autowired
    private RpcPsService rpcPsService;
    @Autowired
    private RpcIpService rpcIpService;

//    @Autowired
//    private RpcSgService rpcSgService;

    @Autowired
    private RpcCpService rpcCpService;

    @Autowired
    private PropertiesConf propertiesConf;

    @Autowired
    private SyncStockStrategyQueryService syncStockStrategyQueryService;

//    @Autowired
//    private GeneralStrategyCheckService generalStrategyCheckService;

//    /**
//     * @return com.jackrain.nea.util.ValueHolder
//     * <AUTHOR>
//     * @Description 新增修改主方法
//     * @Date 2019/3/12
//     * @Param [session]
//     **/
//    public ValueHolder saveProductStrategy(QuerySession session) {
//        DefaultWebEvent event = session.getEvent();
//        JSONObject param = JsonUtils.filterNull((JSONObject) event.getParameterValue("param"), null);
//        log.info(LogUtil.format("ProductStrategySaveServiceParam:{}", param);
//        //1.拉取请求参数，解析
//        Long id = param.getLong("objid");//获取objid参数
//        JSONObject fixColumn = param.getJSONObject("fixcolumn");//获取表数据
//
//        //2.转换为请求bean，判断转是否成功校验参数
//        ProductStrategyRequest productStrategyRequest = JsonUtils.jsonParseClass(fixColumn, ProductStrategyRequest.class);
//        if (log.isDebugEnabled()) {
//            log.debug(LogUtil.format(" 导入明细入参:" + JSONObject.toJSONString(productStrategyRequest));
//        }
//        if (productStrategyRequest == null) {
//            log.debug(LogUtil.format(":获取转换请求对象失败！");
//            return ValueHolderUtils.getFailValueHolder("保存失败！");
//        }
//        if (fixColumn != null && id != null) {
//            if (id != -1) {
//                return updateProductStrategy(session, productStrategyRequest, id);
//            } else {
//                return addProductStrategy(session, productStrategyRequest);
//            }
//        }
//        throw new NDSException("当前记录已不存在！");
//    }

//    /**
//     * @return com.jackrain.nea.util.ValueHolder
//     * <AUTHOR>
//     * @Description 新增方法
//     * @Date 2019/3/12
//     * @Param [session, productStrategyRequest, id]
//     **/
//    public ValueHolder addProductStrategy(QuerySession session, ProductStrategyRequest productStrategyRequest) {
//
//        StCProductStrategyDO stCProductStrategyDO = productStrategyRequest.getStCProductStrategyDO();
//        List<StCProductStrategyItemDO> stCProductStrategyItemDOList = dealStCProductStrategyItemDOList(productStrategyRequest.getStrategyItemRequestList());
//        long id = -1;
//        //1.判断主表
//        if (stCProductStrategyDO != null) {
//            //保存验证
//            ValueHolder resultValueHolder = saveMainCheck(stCProductStrategyDO, id);
//            if (resultValueHolder != null && !resultValueHolder.isOK()) {
//                return resultValueHolder;
//            }
//            //ID序列
//            stCProductStrategyDO.setId(ModelUtil.getSequence(StConstant.TAB_ST_C_PRODUCT_STRATEGY));
//            stCProductStrategyDO.setEstatus(StConstant.CON_BILL_STATUS_01);
//            // 方案ID
//            JSONObject sequence = new JSONObject();
//            String seqProductStrategy = SequenceGenUtil.generateSquence("SEQ_PRODUCT_STRATEGY", sequence, session.getUser().getLocale(), false);
//            stCProductStrategyDO.setPlanName(seqProductStrategy);
//            id = stCProductStrategyDO.getId();
//            //基本字段值设置
//            StBeanUtils.makeCreateField(stCProductStrategyDO, session.getUser());
//            stCProductStrategyDO.setModifierename(session.getUser().getEname());
//            stCProductStrategyDO.setOwnerename(session.getUser().getEname());
//
//            int insertResult = stCProductStrategyMapper.insert(stCProductStrategyDO);
//            if (insertResult <= 0) {
//                // 主表插入失败
//                log.debug(LogUtil.format(":店铺商品特殊设置 主表保存失败！" + id);
//                return ValueHolderUtils.getFailValueHolder("保存失败！");
//            } else {
//                // 插入成功立即push es
//                try {
//                    DatasToEsUtils.insertProductEsData(stCProductStrategyDO, null, StConstant.TAB_ST_C_PRODUCT_STRATEGY);
//                } catch (IOException ex) {
//                    log.debug(LogUtil.format("店铺商品特殊设置主表推数据到ES失败：" + ex.toString());
//                }
//            }
//            //2.店铺商品特殊设置明细保存
//            if (CollectionUtils.isNotEmpty(stCProductStrategyItemDOList)) {
//                List<StCProductStrategyItemDO> productStrategyItemList = Lists.newArrayList();
//                for (StCProductStrategyItemDO stCProductStrategyItemDO : stCProductStrategyItemDOList) {
//                    ValueHolder valueHolder = subCheck(stCProductStrategyItemDO);
//                    if (valueHolder != null) {
//                        return valueHolder;
//                    }
//                    List<SgChannelProductQueryForSTResult> sgChannelProductQueryForSTResultList =
//                            generalStrategyCheckService.checkParam(StConstant.TAB_ST_C_PRODUCT_STRATEGY, stCProductStrategyItemDO.getCpCShopId(), stCProductStrategyItemDO);
//                    if (CollectionUtils.isEmpty(sgChannelProductQueryForSTResultList)) {
//                        return ValueHolderUtils.getFailValueHolder("无法查询到渠道商品！");
//                    }
//                    List<StCProductStrategyItemDO> itemList = getShopProductStrategyItemList(stCProductStrategyItemDO,
//                            sgChannelProductQueryForSTResultList);
//                    if (CollectionUtils.isNotEmpty(itemList)) {
//                        productStrategyItemList.addAll(itemList);
//                    }
//                }
//                ProductStrategySaveService bean = ApplicationContextHandle.getBean(ProductStrategySaveService.class);
//                ValueHolder valueHolder1 = bean.saveNewShopProductStrategyItem(session, stCProductStrategyDO, productStrategyItemList);
//                if (valueHolder1 != null) {
//                    throw new NDSException(valueHolder1.getData().get("message").toString());
//                }
//            }
//        }
//        return ValueHolderUtils.getSuccessValueHolder(id, StConstant.TAB_ST_C_PRODUCT_STRATEGY);
//    }
//
//    /**
//     * @Description 商品编码查找对应的条码档案
//     **/
//    private List<StCProductStrategyItemDO> getProductStrategyItemList(List<StCProductStrategyItemDO> stCProductStrategyItemDOList
//            , Map<Long, Integer> proSizeMap) {
//        List<StCProductStrategyItemDO> productStrategyItemList = Lists.newArrayList();
//        for (StCProductStrategyItemDO stCProductStrategyItemDO : stCProductStrategyItemDOList) {
//            if (stCProductStrategyItemDO.getId() == -1 &&
//                    stCProductStrategyItemDO.getPsCProId() != null && stCProductStrategyItemDO.getPsCSkuId() == null) {
//                List<PsCSkuExt> psCSkuExts = new ArrayList<>();
//                PsSkuResult psSkuResult = rpcPsService.getSkuResultByProId(stCProductStrategyItemDO.getPsCProId());
//                if (!CollectionUtils.isEmpty(psSkuResult.getSkuList())) {
//                    psCSkuExts = psSkuResult.getSkuList();
//                }
//                for (PsCSkuExt psCSkuExt : psCSkuExts) {
//                    StCProductStrategyItemDO productStrategyItem = new StCProductStrategyItemDO();
//                    BeanUtils.copyProperties(stCProductStrategyItemDO, productStrategyItem);
//                    productStrategyItem.setPsCSkuId(psCSkuExt.getId());
//                    productStrategyItemList.add(productStrategyItem);
//                }
//                proSizeMap.put(stCProductStrategyItemDO.getPsCProId(), psCSkuExts.size());
//            } else {
//                productStrategyItemList.add(stCProductStrategyItemDO);
//            }
//        }
//        return productStrategyItemList;
//    }

//    /**
//     * @return com.jackrain.nea.util.ValueHolder
//     * <AUTHOR>
//     * @Description 修改方法
//     * @Date 2019/3/12
//     * @Param [session, productStrategyRequest, id]
//     **/
//    public ValueHolder updateProductStrategy(QuerySession session, ProductStrategyRequest productStrategyRequest, Long id) {
//
//        StCProductStrategyDO stCProductStrategyDO = productStrategyRequest.getStCProductStrategyDO();
//        // excel导入时导入店铺编码，且多值逗号隔开
//        List<StCProductStrategyItemDO> stCProductStrategyItemDOList = dealStCProductStrategyItemDOList(productStrategyRequest.getStrategyItemRequestList());
//
//        //1.主表修改
//        if (stCProductStrategyDO != null) {
//            //保存验证
//            ValueHolder resultValueHolder = saveMainCheck(stCProductStrategyDO, id);
//            if (resultValueHolder != null && !resultValueHolder.isOK()) {
//                return resultValueHolder;
//            }
//            stCProductStrategyDO.setId(id);
//            //基本字段值设置
//            StBeanUtils.makeModifierField(stCProductStrategyDO, session.getUser());
//            stCProductStrategyDO.setModifierename(session.getUser().getEname());
//
//            int count = stCProductStrategyMapper.updateById(stCProductStrategyDO);
//            if (count <= 0) {
//                log.debug(LogUtil.format(":店铺商品特殊设置主表修改保存失败！" + id);
//                return ValueHolderUtils.getFailValueHolder("保存失败！");
//            } else {
//                //主表推送ES数据
//                try {
//                    //做更新的需要先查询更新后数据库的实体在推ES
//                    stCProductStrategyDO = stCProductStrategyMapper.selectById(id);
//                    DatasToEsUtils.insertProductEsData(stCProductStrategyDO, null, StConstant.TAB_ST_C_PRODUCT_STRATEGY);
//                } catch (Exception ex) {
//                    log.debug(StConstant.TAB_ST_C_PRODUCT_STRATEGY + "店铺商品特殊设置主表更新数据至ES失败：" + ex.toString());
//                }
//            }
//        } else {//如果主表不变，只是更改明细，前端没传主表部分json，那么这个主表的实体就会为null，然后在保存明细表的时候有个校验获取 stCProductStrategyDO.getId（）就会报错
//            //故而在这里赋值stCProductStrategyDO实体的主键id的值
//            stCProductStrategyDO = new StCProductStrategyDO();
//            stCProductStrategyDO.setId(id);
//        }
//        //2.明细修改
//        if (CollectionUtils.isNotEmpty(stCProductStrategyItemDOList)) {
//            ValueHolder FailValueHolder = updateProductStrategyItems(session, stCProductStrategyDO, stCProductStrategyItemDOList);
//            if (FailValueHolder != null) {
//                return FailValueHolder;
//            }
//        }
//        return ValueHolderUtils.getSuccessValueHolder(id, StConstant.TAB_ST_C_PRODUCT_STRATEGY);
//    }

//    /**
//     * <ul>
//     *     <li>
//     *         更新接口更新特殊商品设置明细
//     *     </li>
//     * </ul>
//     *
//     * @param session                      {@link QuerySession}
//     * @param stCProductStrategyDO         主表信息
//     * @param stCProductStrategyItemDOList 明细信息
//     * @return 更新结果
//     */
//    private ValueHolder updateProductStrategyItems(QuerySession session, StCProductStrategyDO stCProductStrategyDO, List<StCProductStrategyItemDO> stCProductStrategyItemDOList) {
//        // zyj 20191225 店铺商品特殊设置明细修改-获取渠道商品信息
//        List<StCProductStrategyItemDO> productStrategyItemList = Lists.newArrayList();
//        for (StCProductStrategyItemDO stCProductStrategyItemDO : stCProductStrategyItemDOList) {
//            List<SgChannelProductQueryForSTResult> sgChannelProductQueryForSTResultList =
//                    generalStrategyCheckService.checkParam(StConstant.TAB_ST_C_PRODUCT_STRATEGY, stCProductStrategyItemDO.getCpCShopId(), stCProductStrategyItemDO);
//            if (CollectionUtils.isEmpty(sgChannelProductQueryForSTResultList)) {
//                return ValueHolderUtils.getFailValueHolder(getStCProductStrategyItemErrorInfo(stCProductStrategyItemDO));
//            }
//            //获取店铺商品信息
//            List<StCProductStrategyItemDO> itemList = getShopProductStrategyItemList(stCProductStrategyItemDO, sgChannelProductQueryForSTResultList);
//            if (CollectionUtils.isNotEmpty(itemList)) {
//                productStrategyItemList.addAll(itemList);
//            }
//        }
//
//        List<StCProductStrategyItemDO> newStCProductStrategyItemDOList = new ArrayList<>();
//        for (StCProductStrategyItemDO stCProductStrategyItemDO : productStrategyItemList) {
//            //验证库存比例和低库存数
//            ValueHolder checkVh = subCheck(stCProductStrategyItemDO);
//            if (checkVh != null) {
//                throw new NDSException(checkVh.getData().get("message").toString());
//            }
//            //判断明细是新增还是修改
//            Long itemId = stCProductStrategyItemDO.getId();
//            if (itemId != -1) {
//                //基本字段值设置
//                StBeanUtils.makeModifierField(stCProductStrategyItemDO, session.getUser());
//                int updateCnt = stCProductStrategyItemMapper.updateById(stCProductStrategyItemDO);
//                if (updateCnt <= 0) {
//                    log.debug(LogUtil.format("店铺商品特殊设置明细表更新失败！" + stCProductStrategyItemDO.getStCProductStrategyId());
//                    throw new NDSException("保存失败！");
//                }
//            } else {
//                newStCProductStrategyItemDOList.add(stCProductStrategyItemDO);
//            }
//        }
//        try {
//            //修改的明细推送ES数据
//            QueryWrapper<StCProductStrategyItemDO> wrapper = new QueryWrapper<>();
//            wrapper.eq("ST_C_PRODUCT_STRATEGY_ID", stCProductStrategyDO.getId());
//            List<StCProductStrategyItemDO> newItemList = ListUtils.batchQueryByCondition(wrapper, stCProductStrategyItemMapper, 1000);
//            if (CollectionUtils.isNotEmpty(newItemList)) {
//                DatasToEsUtils.insertProductEsData(stCProductStrategyDO, newItemList, StConstant.TAB_ST_C_PRODUCT_STRATEGY_ITEM);
//            }
//        } catch (Exception e) {
//            log.error(LogUtil.format("updateProductStrategy item es update error: ", e);
//        }
//        if (CollectionUtils.isNotEmpty(newStCProductStrategyItemDOList)) {
//            ProductStrategySaveService bean = ApplicationContextHandle.getBean(ProductStrategySaveService.class);
//            ValueHolder valueHolder = bean.saveNewShopProductStrategyItem(session, stCProductStrategyDO, newStCProductStrategyItemDOList);
//            if (valueHolder != null) {
//                throw new NDSException(valueHolder.getData().get("message").toString());
//            }
//        }
//        return null;
//    }
//
//    private String getStCProductStrategyItemErrorInfo(StCProductStrategyItemDO item) {
//        StringBuffer errorInfo = new StringBuffer("店铺编码:")
//                .append(item.getCpCShopEcode()).append(",");
//        String queryStr = "";
//        if (StringUtils.isNotEmpty(item.getPtSkuId())) {
//            queryStr = "平台条码ID:" + item.getPtSkuId() + ",";
//        } else if (StringUtils.isNotEmpty(item.getPtProId())) {
//            queryStr = "平台商品ID:" + item.getPtProId() + ",";
//        } else if (item.getPsCSkuId() == null) {
//            PsCSku psCSku = rpcPsService.getSkuById(item.getPsCSkuId());
//            if (psCSku != null) {
//                queryStr = "条码:" + psCSku.getEcode() + ",";
//            } else{
//                queryStr = "条码ID:" + item.getPsCSkuId() + ",";
//            }
//        } else if (item.getPsCProId() == null) {
//            PsCPro psCPro = rpcPsService.queryProByID(item.getPsCProId());
//            if (psCPro != null) {
//                queryStr = "商品编码:" + psCPro.getEcode() + ",";
//            } else {
//                queryStr = "商品编码ID:" + item.getPsCProId() + ",";
//            }
//        }
//        errorInfo.append(queryStr).append("无法查询到渠道商品！");
//        return errorInfo.toString();
//    }

    /**
     * @param stCProductStrategyItemRequestList 店铺商品特殊设置明细列表
     * @return java.util.List<com.jackrain.nea.st.model.table.StCProductStrategyItemDO>
     * <AUTHOR>
     * @Description 店铺商品特殊设置明细 店铺、渠道信息填充
     * @Date 9:46 2020/6/22
     **/
    private List<StCProductStrategyItemDO> dealStCProductStrategyItemDOList(List<ProductStrategyItemRequest> stCProductStrategyItemRequestList) {
        if (CollectionUtils.isEmpty(stCProductStrategyItemRequestList)) {
            return null;
        }
        List<StCProductStrategyItemDO> resultList = new ArrayList<>();
        List<CpShop> cpShops = rpcCpService.queryAllShopIsY();
        Map<String, CpShop> shopIdMap = cpShops.stream().collect(
                Collectors.toMap(c -> String.valueOf(c.getId()), a -> a, (k1, k2) -> k1));
        CpCOrgChannelQueryCmdRequest request = new CpCOrgChannelQueryCmdRequest();
        List<CpCOrgChannelEntity> channelList = rpcCpService.getChannel(request);
        Map<Long, CpCOrgChannelEntity> channelIdMap = channelList.stream().collect(Collectors.toMap(CpCOrgChannelEntity::getId, a -> a, (k1, k2) -> k1));
        for (ProductStrategyItemRequest item : stCProductStrategyItemRequestList) {
            //修改的数据特殊处理
            Long id = item.getId();
            if (id == null || id == -1) {
                //渠道特殊处理
                Long cpCOrgChannelId = item.getCpCOrgChannelId();
                if (cpCOrgChannelId == null) {
                    continue;
                }
                CpCOrgChannelEntity cpCOrgChannelEntity = channelIdMap.get(cpCOrgChannelId);
                if (cpCOrgChannelEntity == null) {
                    AssertUtils.logAndThrow("无法获取渠道信息！");
                } else {
                    item.setCpCOrgChannelId(cpCOrgChannelEntity.getId());
                    item.setCpCOrgChannelEcode(cpCOrgChannelEntity.getEcode());
                    item.setCpCOrgChannelEname(cpCOrgChannelEntity.getEname());
                }
                //店铺特殊处理
                String cpCShopIds = item.getCpCShopIds();
                if (StringUtils.isEmpty(cpCShopIds)) {
                    continue;
                }
                String[] cpCShopIdArray = cpCShopIds.split(",");
                for (String cpCShopId : cpCShopIdArray) {
                    CpShop cpShop = shopIdMap.get(cpCShopId);
                    if (cpShop == null) {
                        AssertUtils.logAndThrow("无法获取店铺信息！");
                    } else {
                        StCProductStrategyItemDO itemDO = new StCProductStrategyItemDO();
                        item.setCpCShopId(cpShop.getId());
                        item.setCpCShopEcode(cpShop.getEcode());
                        item.setCpCShopTitle(cpShop.getCpCShopTitle());
                        BeanUtils.copyProperties(item, itemDO);
                        resultList.add(itemDO);
                    }
                }
            } else {
                StCProductStrategyItemDO stCProductStrategyItemDO = stCProductStrategyItemMapper.selectById(id);
                if (stCProductStrategyItemDO != null) {
                    Long lowStock = item.getLowStock();
                    BigDecimal stockScale = item.getStockScale();
                    if (lowStock != null) {
                        stCProductStrategyItemDO.setLowStock(lowStock);
                    }
                    if (stockScale != null) {
                        stCProductStrategyItemDO.setStockScale(stockScale);
                    }
                    resultList.add(stCProductStrategyItemDO);
                }
            }
        }
        return resultList;
    }

    /**
     * @return com.jackrain.nea.util.ValueHolder
     * <AUTHOR>
     * @Description 主表保存验证
     * @Date 2019/3/18
     * @Param [stCProductStrategy, objid, valueHolder]
     **/
    private ValueHolder saveMainCheck(StCProductStrategyDO stCProductStrategy, Long objid) {
        //新增不验证存在性
        if (objid != -1) {
            StCProductStrategyDO stCProductStrategyDO = stCProductStrategyMapper.selectById(objid);
            //记录不存在
            if (stCProductStrategyDO == null) {
                return ValueHolderUtils.getFailValueHolder("当前记录已不存在！");
            }
            stCProductStrategy.setBeginTime(stCProductStrategy.getBeginTime() == null ? stCProductStrategyDO.getBeginTime() : stCProductStrategy.getBeginTime());
            stCProductStrategy.setEndTime(stCProductStrategy.getEndTime() == null ? stCProductStrategyDO.getEndTime() : stCProductStrategy.getEndTime());
        }
        // 时间校验
        if (stCProductStrategy.getBeginTime() != null && stCProductStrategy.getEndTime() != null) {
            if (stCProductStrategy.getEndTime().before(stCProductStrategy.getBeginTime())) {
                return ValueHolderUtils.getFailValueHolder("方案的结束日期不能小于生效日期！");
            }

            if (stCProductStrategy.getEndTime().before(new Date())) {
                return ValueHolderUtils.getFailValueHolder("方案的结束日期不能小于当前日期！");
            }
        }
        return null;
    }

    /**
     * 验证比库存数和库存比例
     *
     * @param stCProductStrategyItemDO
     * @return ValueHolder
     */
    private ValueHolder subCheck(StCProductStrategyItemDO stCProductStrategyItemDO) {
        Long lowStock = stCProductStrategyItemDO.getLowStock();
        BigDecimal stockScale = stCProductStrategyItemDO.getStockScale();

        //低库存数不能为负数
        if (lowStock != null && lowStock < 0) {
            return ValueHolderUtils.getFailValueHolder("低库存数不能为负数！");
        }
        //库存比例不能为负数, compare推荐用 > = < 来作比较
        if (stockScale != null && stockScale.compareTo(BigDecimal.valueOf(0)) < 0) {
            return ValueHolderUtils.getFailValueHolder("库存比例不能为负数！");
        }
        //库存比例不能超过100
        if (stockScale != null && stockScale.compareTo(BigDecimal.valueOf(100)) > 0) {
            return ValueHolderUtils.getFailValueHolder("库存比例不能超过100！");
        }
        //选择的渠道仓在店铺关联的渠道仓范围下
        Long cpCShopId = stCProductStrategyItemDO.getCpCShopId();
        Long cpCOrgChannelId = stCProductStrategyItemDO.getCpCOrgChannelId();
        List<CpCOrgChannelItemEntity> cpCOrgChannelItemEntities = syncStockStrategyQueryService.querySyncStockStrategy(cpCShopId);
        if (CollectionUtils.isEmpty(cpCOrgChannelItemEntities)) {
            return ValueHolderUtils.getFailValueHolder("店铺关联渠道仓为空！");
        }
        Map<Long, Long> channelMap = cpCOrgChannelItemEntities.stream().collect(
                Collectors.toMap(CpCOrgChannelItemEntity::getCpCOrgChannelId, CpCOrgChannelItemEntity::getCpCOrgChannelId, (k1, k2) -> k1));
        if (channelMap.get(cpCOrgChannelId) == null) {
            return ValueHolderUtils.getFailValueHolder("店铺:" + stCProductStrategyItemDO.getCpCShopTitle()
                    + "下不存在对应的渠道仓:" + stCProductStrategyItemDO.getCpCOrgChannelEname() + "！");
        }
        return null;
    }

    /**
     * @return com.jackrain.nea.util.ValueHolder
     * <AUTHOR>
     * @Description 验证方案的平台商品ID和平台sku唯一
     * @Date 2019-4-10
     * @Param [isNew, id, ptPro, proSku, valueHolder]
     **/
    private ValueHolder checkProSku(Boolean isNew, Long id, String ptProId, Long psSkuId) {
        //新增验证方案的平台商品ID和平台sku唯一
        //2020-06-19 根据需求去除平台商品ID和平台sku唯一验证
        if (isNew) {
            List<StCProductStrategyItemDO> stCProductStrategyItemDOList = stCProductStrategyItemMapper.selectByPtproPssku(ptProId, psSkuId);
            if (!CollectionUtils.isEmpty(stCProductStrategyItemDOList)) {
                return ValueHolderUtils.getFailValueHolder("此平台商品ID+条码已在其他有效方案中存在，不予许保存！");
            }
        } else {
            List<StCProductStrategyItemDO> stCProductStrategyItemDOList = stCProductStrategyItemMapper.selectByIdPtproPssku(id, ptProId, psSkuId);
            if (!CollectionUtils.isEmpty(stCProductStrategyItemDOList)) {
                return ValueHolderUtils.getFailValueHolder("此平台商品ID+条码已在其他有效方案中存在，不予许保存！");
            }
        }

        return null;
    }

    /**
     * @param strategyId 策略id
     * @param prsku      中台条码
     * @param ptSku      平台条码
     * @param channelId  渠道仓id
     * @return
     */
    private ValueHolder checkProSku(Long strategyId, Long prsku, String ptSku, Long channelId) {

        //2020-08-13 需求验证条码和平台条码id唯一
        List<StCProductStrategyItemDO> stCProductStrategyItemDOList = stCProductStrategyItemMapper.selectByPtpskuPssku(strategyId, ptSku, prsku, channelId);
        if (!CollectionUtils.isEmpty(stCProductStrategyItemDOList)) {
            return ValueHolderUtils.getFailValueHolder("此条码或平台商品条码已在方案中存在，不予许保存！");
        }
        return null;
    }

    /**
     * 子表新增
     */
    private ValueHolder saveProductStrategyItem(QuerySession session, StCProductStrategyDO stCProductStrategyDO
            , StCProductStrategyItemDO stCProductStrategyItemDONew) {
        // 旧数据取得，防止重复
        List<StCProductStrategyItemDO> stCProductStrategyItemDOOldList = stCProductStrategyItemMapper.listByItemId(stCProductStrategyDO.getId());
        Map<String, StCProductStrategyItemDO> productStrategyItemDOOldMap = new HashMap<>();

        for (StCProductStrategyItemDO stCProductStrategyItemDO : stCProductStrategyItemDOOldList) {
            String key = stCProductStrategyItemDO.getPtProId() + " " + stCProductStrategyItemDO.getPtSkuId()
                    + " " + stCProductStrategyItemDO.getPsCProEcode() + " " + stCProductStrategyItemDO.getPsCSkuEcode();
            productStrategyItemDOOldMap.put(key, stCProductStrategyItemDO);
        }

        setProSku(stCProductStrategyItemDONew);
        List<SpecialbarcodeRetModel> specialbarcodeRetModelList = rpcIpService.getSpecialbarcode(session.getUser(), stCProductStrategyItemDONew.getCpCShopId()
                , stCProductStrategyItemDONew.getPtProId(), stCProductStrategyItemDONew.getPtSkuId()
                , null, stCProductStrategyItemDONew.getPsCSkuEcode());

        if (specialbarcodeRetModelList == null) {
            return ValueHolderUtils.getFailValueHolder("无法匹配平台数据！");
        }

        //准备商品，条码信息
        List<String> skuCds = new ArrayList<>();
        Map<String, PsCSku> skuMap = new HashMap<>();
        for (SpecialbarcodeRetModel specialbarcodeRetModel : specialbarcodeRetModelList) {
            skuCds.add(specialbarcodeRetModel.getPsCSkuId());
        }
        if (!CollectionUtils.isEmpty(skuCds)) {
            List<PsCSku> psCSkuList = rpcPsService.querySkuByEcodes(skuCds);
            if (psCSkuList != null) {
                skuMap = psCSkuList.stream().collect(Collectors.toMap(PsCSku::getEcode, Function.identity()));
            }
        }

        //子表新增
        List<StCProductStrategyItemDO> esList = new ArrayList<>();
        for (SpecialbarcodeRetModel specialbarcodeRetModel : specialbarcodeRetModelList) {

            StCProductStrategyItemDO stCProductStrategyItemDO = new StCProductStrategyItemDO();
            //序号
            stCProductStrategyItemDO.setId(ModelUtil.getSequence(StConstant.TAB_ST_C_PRODUCT_STRATEGY_ITEM));
            //主表关联id
            stCProductStrategyItemDO.setStCProductStrategyId(stCProductStrategyDO.getId());
            stCProductStrategyItemDO.setCpCShopId(stCProductStrategyItemDONew.getCpCShopId());
            stCProductStrategyItemDO.setLowStock(stCProductStrategyItemDONew.getLowStock());
            stCProductStrategyItemDO.setStockScale(stCProductStrategyItemDONew.getStockScale());

            stCProductStrategyItemDO.setPtProId(specialbarcodeRetModel.getPtProId());
            stCProductStrategyItemDO.setPtSkuId(specialbarcodeRetModel.getPtSkuId());

            stCProductStrategyItemDO.setPsCSkuEcode(specialbarcodeRetModel.getPsCSkuId());
            if (skuMap.containsKey(specialbarcodeRetModel.getPsCSkuId())) {
                PsCSku psCSku = skuMap.get(specialbarcodeRetModel.getPsCSkuId());
                stCProductStrategyItemDO.setPsCSkuId(psCSku.getId());
                stCProductStrategyItemDO.setPsCProId(psCSku.getPsCProId());
                stCProductStrategyItemDO.setPsCProEcode(psCSku.getPsCProEcode());
                stCProductStrategyItemDO.setPsCProEname(psCSku.getPsCProEname());
            }
            String key = stCProductStrategyItemDO.getPtProId() + " " + stCProductStrategyItemDO.getPtSkuId()
                    + " " + stCProductStrategyItemDO.getPsCProEcode() + " " + stCProductStrategyItemDO.getPsCSkuEcode();
            if (!productStrategyItemDOOldMap.containsKey(key)) {
                //基本字段值设置
                StBeanUtils.makeCreateField(stCProductStrategyItemDO, session.getUser());

                //验证平台商品ID+条码唯一性
                ValueHolder valueHolder = checkProSku(true, -1L, stCProductStrategyItemDO.getPtProId(), stCProductStrategyItemDO.getPsCSkuId());
                if (valueHolder != null) {
                    return ValueHolderUtils.getFailValueHolder(valueHolder.getData().get("message").toString());
                }
                if (stCProductStrategyItemMapper.insert(stCProductStrategyItemDO) < 0) {
                    return ValueHolderUtils.getFailValueHolder("保存失败！");
                } else {
                    esList.add(stCProductStrategyItemDO);
                }
            }
        }
        //更新es
        try {
            if (CollectionUtils.isNotEmpty(esList)) {
                DatasToEsUtils.insertProductEsData(stCProductStrategyDO, esList, StConstant.TAB_ST_C_PRODUCT_STRATEGY_ITEM);
            }
            log.debug(LogUtil.format("店铺商品特殊设置明细表推数据到ES成功》》》》》》》》"));
        } catch (Exception ex) {
            log.debug(LogUtil.format("店铺商品特殊设置明细表推数据到ES失败：{}"), Throwables.getStackTraceAsString(ex));
        }
        return null;
    }

    /**
     * @Description 商品编码ID获取商品信息
     **/
    private void setProSku(StCProductStrategyItemDO stCProductStrategyItemDO) {

        if (stCProductStrategyItemDO.getPsCProId() != null) {
            PsCPro psCPro = rpcPsService.queryProByID(stCProductStrategyItemDO.getPsCProId());
            if (psCPro != null) {
                stCProductStrategyItemDO.setPsCProEcode(psCPro.getEcode());
                stCProductStrategyItemDO.setPsCProEname(psCPro.getEname());
            }
        }

        if (stCProductStrategyItemDO.getPsCSkuId() != null) {
            PsCSku psCSku = rpcPsService.getSkuById(stCProductStrategyItemDO.getPsCSkuId());
            if (psCSku != null) {
                stCProductStrategyItemDO.setPsCSkuEcode(psCSku.getEcode());
            }
        }
    }

//    /**
//     * 根据店铺商品特殊设置明细条件查询渠道商品
//     *
//     * @param stCProductStrategyItemDOList
//     * @return
//     */
//    private List<SgChannelProductQueryForSTResult> getChannelProduct(List<StCProductStrategyItemDO> stCProductStrategyItemDOList) {
//        List<SgChannelProductQueryForSTResult> newSgChannelProductQueryForSTResultList = new ArrayList<>();
//        SgChannelProductQueryForSTRequest sgChannelProductQueryForSTRequest = new SgChannelProductQueryForSTRequest();
//        if (log.isDebugEnabled()) {
//            log.debug(LogUtil.format("入参:" + JSONObject.toJSONString(stCProductStrategyItemDOList));
//        }
//        //对店铺进行分组
//        Map<Long, List<StCProductStrategyItemDO>> mapList = stCProductStrategyItemDOList.stream().collect(Collectors.groupingBy(x -> x.getCpCShopId()));
//        for (Long shopId : mapList.keySet()) {
//            List<StCProductStrategyItemDO> itemDOList = mapList.get(shopId);
//            //平台商品id
//            List<String> ptProIdList = itemDOList.stream().filter(x -> x.getPtProId() != null).map(x -> String.valueOf(x.getPtProId())).collect(Collectors.toList());
//            //平台skuid
//            List<String> ptSkuIdList = itemDOList.stream().filter(x -> x.getPtSkuId() != null).map(x -> String.valueOf(x.getPtSkuId())).collect(Collectors.toList());
//            //商品编码
//            List<Long> psCProIdList = itemDOList.stream().filter(x -> x.getPsCProId() != null).map(x -> x.getPsCProId()).collect(Collectors.toList());
//            //系统条码
//            List<Long> psCSkuIdList = itemDOList.stream().filter(x -> x.getPsCSkuId() != null).map(x -> x.getPsCSkuId()).collect(Collectors.toList());
//
//            sgChannelProductQueryForSTRequest.setPtProIdList(ptProIdList);
//            sgChannelProductQueryForSTRequest.setPtSkuIdList(ptSkuIdList);
//            sgChannelProductQueryForSTRequest.setPsCProIdList(psCProIdList);
//            sgChannelProductQueryForSTRequest.setPsCSkuIdList(psCSkuIdList);
//            sgChannelProductQueryForSTRequest.setCpCShopId(shopId);
//            if (log.isDebugEnabled()) {
//                log.debug(LogUtil.format(" 【店铺商品策略】查询渠道商品入参:" + JSONObject.toJSONString(sgChannelProductQueryForSTRequest));
//            }
//            String pageNum = propertiesConf.getProperty("r3.st.queryChannelProduct.pageNum");
//            if (StringUtils.isEmpty(pageNum)) {
//                pageNum = "2000";
//            }
//            Integer count = rpcSgService.queryChannelProductCount(sgChannelProductQueryForSTRequest);
//            if (count.intValue() == 0) {
//                return new ArrayList<>();
//            }
//            sgChannelProductQueryForSTRequest.setPageNum(Integer.valueOf(pageNum));
//            ListPageUtil pageResultList = new ListPageUtil(count, Integer.valueOf(pageNum));
//            for (int i = 1; i <= pageResultList.getPageCount(); i++) {
//                List<SgChannelProductQueryForSTResult> tempList = rpcSgService.queryChannelProduct(sgChannelProductQueryForSTRequest);
//                if (tempList != null && !tempList.isEmpty()) {
//                    newSgChannelProductQueryForSTResultList.addAll(tempList);
//                    Optional<SgChannelProductQueryForSTResult> resultOp = tempList.stream().max(Comparator.comparingLong(SgChannelProductQueryForSTResult::getId));
//                    if (resultOp.isPresent()) {
//                        SgChannelProductQueryForSTResult sgChannelProductQueryForSTResult = resultOp.get();
//                        sgChannelProductQueryForSTRequest.setId(sgChannelProductQueryForSTResult.getId());
//                    }
//                }
//
//                if (i == pageResultList.getPageCount()) {
//                    break;
//                }
//            }
//
//        }
//        return newSgChannelProductQueryForSTResultList;
//    }
//
//    /**
//     * @Description 根据查询的渠道商品跟本地商品信息进行比较，获取最新的需要保存的店铺商品
//     **/
//    private List<StCProductStrategyItemDO> getShopProductStrategyItemList(StCProductStrategyItemDO stCProductStrategyItemDO,
//                                                                          List<SgChannelProductQueryForSTResult> resultList) {
//        List<StCProductStrategyItemDO> productStrategyItemList = Lists.newArrayList();
//        if (CollectionUtils.isNotEmpty(resultList)) {
//            if (log.isDebugEnabled()) {
//                log.debug(LogUtil.format(" 需要保存的渠道商品:" + JSONObject.toJSONString(resultList));
//            }
//            for (SgChannelProductQueryForSTResult sgChannelProductQueryForSTResult : resultList) {
//                StCProductStrategyItemDO strategyItemDO = new StCProductStrategyItemDO();
//                BeanUtils.copyProperties(stCProductStrategyItemDO, strategyItemDO);
//                //店铺
//                strategyItemDO.setCpCShopId(sgChannelProductQueryForSTResult.getCpCShopId());
//                strategyItemDO.setCpCShopTitle(sgChannelProductQueryForSTResult.getCpCShopTitle());
//                //商品名称
//                strategyItemDO.setPsCProEcode(sgChannelProductQueryForSTResult.getPsCProEcode());
//                strategyItemDO.setPsCProEname(sgChannelProductQueryForSTResult.getPsCProEname());
//                strategyItemDO.setPsCProId(sgChannelProductQueryForSTResult.getPsCProId());
//                //条码
//                strategyItemDO.setPsCSkuEcode(sgChannelProductQueryForSTResult.getPsCSkuEcode());
//                strategyItemDO.setPsCSkuId(sgChannelProductQueryForSTResult.getPsCSkuId());
//                //平台商品条码信息
//                strategyItemDO.setPtProId(sgChannelProductQueryForSTResult.getNumiid());
//                strategyItemDO.setPtSkuId(sgChannelProductQueryForSTResult.getSkuId());
//                productStrategyItemList.add(strategyItemDO);
//            }
//        }
//        return productStrategyItemList;
//    }

    /**
     * 子表新增并推送es
     */
    public ValueHolder saveNewShopProductStrategyItem(QuerySession session, StCProductStrategyDO stCProductStrategyDO
            , List<StCProductStrategyItemDO> stCProductStrategyItemDOList) {
        // 旧数据取得，防止重复
        List<StCProductStrategyItemDO> stCProductStrategyItemDOOldList = stCProductStrategyItemMapper.listByItemId(stCProductStrategyDO.getId());
        Map<String, StCProductStrategyItemDO> productStrategyItemDOOldMap = new HashMap<>();
        // 数据防止重复
        Map<String, StCProductStrategyItemDO> uniqueMap = new HashMap<>();

        for (StCProductStrategyItemDO stCProductStrategyItemDO : stCProductStrategyItemDOOldList) {
            String key = stCProductStrategyItemDO.getPtProId() + "-" + stCProductStrategyItemDO.getPtSkuId()
                    + "-" + stCProductStrategyItemDO.getPsCProEcode() + "-" + stCProductStrategyItemDO.getPsCSkuEcode()
                    + "-" + stCProductStrategyItemDO.getCpCOrgChannelId() + "-" + stCProductStrategyItemDO.getCpCShopId();
            productStrategyItemDOOldMap.put(key, stCProductStrategyItemDO);
        }

        //子表新增
        String pageNum = propertiesConf.getProperty("r3.st.queryChannelProduct.pageNum");
        if (StringUtils.isEmpty(pageNum)) {
            pageNum = "2000";
        }
        ListPageUtil pager = new ListPageUtil(stCProductStrategyItemDOList, Integer.valueOf(pageNum));
        List<StCProductStrategyItemDO> itemDoUpdateList = new ArrayList<>();
        List<StCProductStrategyItemDO> itemDoInsertList = new ArrayList<>();
        for (int i = 1; i <= pager.getPageCount(); i++) {
            List<StCProductStrategyItemDO> list = pager.getPagedList(i);
            Long[] idList = ModelUtil.getSequence(StConstant.TAB_ST_C_PRODUCT_STRATEGY_ITEM, list.size());
            for (int j = 0; j < list.size(); j++) {
                StCProductStrategyItemDO stCProductStrategyItemDO = list.get(j);
                //序号
                stCProductStrategyItemDO.setId(idList[j]);
                //主表关联id
                stCProductStrategyItemDO.setStCProductStrategyId(stCProductStrategyDO.getId());
                stCProductStrategyItemDO.setPlanName(stCProductStrategyDO.getPlanName());
                stCProductStrategyItemDO.setStatus(stCProductStrategyDO.getEstatus());
                stCProductStrategyItemDO.setBeginTime(stCProductStrategyDO.getBeginTime());
                stCProductStrategyItemDO.setEndTime(stCProductStrategyDO.getEndTime());
                stCProductStrategyItemDO.setMainCreationdate(stCProductStrategyDO.getCreationdate());
                String key = stCProductStrategyItemDO.getPtProId() + "-" + stCProductStrategyItemDO.getPtSkuId()
                        + "-" + stCProductStrategyItemDO.getPsCProEcode() + "-" + stCProductStrategyItemDO.getPsCSkuEcode()
                        + "-" + stCProductStrategyItemDO.getCpCOrgChannelId() + "-" + stCProductStrategyItemDO.getCpCShopId();
                //去除插入或修改的数据重复
                if (uniqueMap.containsKey(key)) {
                    continue;
                } else {
                    uniqueMap.put(key, stCProductStrategyItemDO);
                }
                if (!productStrategyItemDOOldMap.containsKey(key)) {
                    //基本字段值设置
                    StBeanUtils.makeCreateField(stCProductStrategyItemDO, session.getUser());
                    itemDoInsertList.add(stCProductStrategyItemDO);

                } else {
                    StCProductStrategyItemDO updateItem = productStrategyItemDOOldMap.get(key);
                    StBeanUtils.makeModifierField(updateItem, session.getUser());
                    updateItem.setLowStock(stCProductStrategyItemDO.getLowStock());
                    updateItem.setStockScale(stCProductStrategyItemDO.getStockScale());
                    itemDoUpdateList.add(updateItem);
                }
            }
        }
        ProductStrategySaveService bean =
                ApplicationContextHandle.getBean(ProductStrategySaveService.class);
        bean.batchSaveOrUpdate(stCProductStrategyDO, itemDoUpdateList, itemDoInsertList);
        return null;
    }

    @Transactional(rollbackFor = Exception.class)
    public void batchSaveOrUpdate(StCProductStrategyDO stCProductStrategyDO,
                             List<StCProductStrategyItemDO> itemDoUpdateList,
                             List<StCProductStrategyItemDO> itemDoInsertList) {

        List<StCProductStrategyItemDO> allList = new ArrayList<>();

        try {
            if (CollectionUtils.isNotEmpty(itemDoInsertList)) {
                productStrategyItemService.saveBatch(itemDoInsertList, 500);
                allList.addAll(itemDoInsertList);
            }

            if (CollectionUtils.isNotEmpty(itemDoUpdateList)) {
                productStrategyItemService.updateBatchById(itemDoUpdateList, 500);
                allList.addAll(itemDoUpdateList);
            }
        } catch (Exception e){
            throw new NDSException("批量插入更新异常", e);
        }

        if (CollectionUtils.isNotEmpty(allList)) {
            try {
                DatasToEsUtils.insertProductEsData(stCProductStrategyDO, allList, StConstant.TAB_ST_C_PRODUCT_STRATEGY_ITEM);
                if(log.isDebugEnabled()) {
                    log.debug(LogUtil.format("店铺商品特殊设置明细表推数据到ES成功》》》》》》》》"));
                }
            } catch (Exception ex) {
                log.error(LogUtil.format("店铺商品特殊设置明细表推数据到ES失败：{}"), Throwables.getStackTraceAsString(ex));
            }
        }
    }

//
//    /**
//     * 根据店铺商品特殊设置明细条件查询渠道商品(单个)
//     *
//     * @param stCProductStrategyItemDOList
//     * @return
//     */
//    private List<StCProductStrategyItemDO> getSingleChannelProduct(List<StCProductStrategyItemDO> stCProductStrategyItemDOList) {
//        List<StCProductStrategyItemDO> strategyItemDOS = new ArrayList<>();
//        for (StCProductStrategyItemDO stCProductStrategyItemDO : stCProductStrategyItemDOList) {
//            SgChannelProductQueryForSTRequest sgChannelProductQueryForSTRequest = new SgChannelProductQueryForSTRequest();
//            List<SgChannelProductQueryForSTResult> newSgChannelProductQueryForSTResultList = new ArrayList<>();
//            if (stCProductStrategyItemDO.getPtSkuId() == null && stCProductStrategyItemDO.getPtProId() == null &&
//                    stCProductStrategyItemDO.getPsCSkuId() == null && stCProductStrategyItemDO.getPsCProId() == null) {
//                sgChannelProductQueryForSTRequest.setCpCShopId(stCProductStrategyItemDO.getCpCShopId());
//                String pageNum = propertiesConf.getProperty("r3.st.queryChannelProduct.pageNum");
//                if (StringUtils.isEmpty(pageNum)) {
//                    pageNum = "2000";
//                }
//                Integer count = rpcSgService.queryChannelProductCount(sgChannelProductQueryForSTRequest);
//                if (count.intValue() == 0) {
//                    return new ArrayList<>();
//                }
//                sgChannelProductQueryForSTRequest.setPageNum(Integer.valueOf(pageNum));
//                sgChannelProductQueryForSTRequest.setIsSort("true");
//                ListPageUtil pageResultList = new ListPageUtil(count, Integer.valueOf(pageNum));
//                for (int i = 1; i <= pageResultList.getPageCount(); i++) {
//                    List<SgChannelProductQueryForSTResult> tempList = rpcSgService.queryChannelProduct(sgChannelProductQueryForSTRequest);
//                    if (tempList != null && !tempList.isEmpty()) {
//                        newSgChannelProductQueryForSTResultList.addAll(tempList);
//                        Optional<SgChannelProductQueryForSTResult> resultOp = tempList.stream().max(Comparator.comparingLong(SgChannelProductQueryForSTResult::getId));
//                        if (resultOp.isPresent()) {
//                            SgChannelProductQueryForSTResult sgChannelProductQueryForSTResult = resultOp.get();
//                            sgChannelProductQueryForSTRequest.setId(sgChannelProductQueryForSTResult.getId());
//                        }
//                    }
//                    if (i == pageResultList.getPageCount()) {
//                        break;
//                    }
//                }
//            } else {
//                sgChannelProductQueryForSTRequest.setCpCShopId(stCProductStrategyItemDO.getCpCShopId());
//                sgChannelProductQueryForSTRequest.setPtSkuId(stCProductStrategyItemDO.getPtSkuId() != null ? String.valueOf(stCProductStrategyItemDO.getPtSkuId()) : null);
//                sgChannelProductQueryForSTRequest.setPtProId(stCProductStrategyItemDO.getPtProId() != null ? String.valueOf(stCProductStrategyItemDO.getPtProId()) : null);
//                sgChannelProductQueryForSTRequest.setPsCSkuId(stCProductStrategyItemDO.getPsCSkuId() != null ? stCProductStrategyItemDO.getPsCSkuId() : null);
//                sgChannelProductQueryForSTRequest.setPsCProId(stCProductStrategyItemDO.getPsCProId() != null ? stCProductStrategyItemDO.getPsCProId() : null);
//                if (log.isDebugEnabled()) {
//                    log.debug(LogUtil.format(" 【店铺商品特殊设置策略】查询渠道商品入参:" + JSONObject.toJSONString(sgChannelProductQueryForSTRequest));
//                }
//                newSgChannelProductQueryForSTResultList.addAll(rpcSgService.queryChannelProduct(sgChannelProductQueryForSTRequest));
//            }
//            if (CollectionUtils.isNotEmpty(newSgChannelProductQueryForSTResultList)) {
//                for (SgChannelProductQueryForSTResult result : newSgChannelProductQueryForSTResultList) {
//                    StCProductStrategyItemDO strategyItemDO = new StCProductStrategyItemDO();
//                    BeanUtils.copyProperties(stCProductStrategyItemDO, strategyItemDO);
//                    strategyItemDO.setCpCShopTitle(result.getCpCShopTitle());
//                    strategyItemDO.setCpCShopId(result.getCpCShopId());
//                    strategyItemDO.setPsCProEcode(result.getPsCProEcode());
//                    strategyItemDO.setPsCProEname(result.getPsCProEname());
//                    strategyItemDO.setPsCProId(result.getPsCProId());
//                    strategyItemDO.setPsCProEname(result.getPsCProEname());
//                    strategyItemDO.setPsCProEcode(result.getPsCProEcode());
//                    strategyItemDO.setPsCSkuEcode(result.getPsCSkuEcode());
//                    strategyItemDO.setPsCSkuId(result.getPsCSkuId());
//                    strategyItemDO.setPtProId(result.getNumiid());
//                    strategyItemDO.setPtSkuId(result.getSkuId());
//                    strategyItemDOS.add(strategyItemDO);
//                }
//            }
//        }
//        return strategyItemDOS;
//    }
}
