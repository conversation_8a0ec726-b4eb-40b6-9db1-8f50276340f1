package com.jackrain.nea.st.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.ext.sequence.observer.SequenceExec;
import com.jackrain.nea.ext.sequence.util.SequenceGenUtil;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.st.common.StConstant;
import com.jackrain.nea.st.mapper.StCOperationCostItemMapper;
import com.jackrain.nea.st.mapper.StCOperationCostMapper;
import com.jackrain.nea.st.model.table.StCOperationcostDO;
import com.jackrain.nea.st.model.table.StCOperationcostItemDO;
import com.jackrain.nea.st.utils.StBeanUtils;
import com.jackrain.nea.st.utils.ValueHolderUtils;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.HashMap;


/**
 * @Descroption 操作费方案-保存逻辑
 * <AUTHOR>
 * @Date 2019/3/9
 */
@Component
@Slf4j
@Transactional
public class OperationcostSaveService extends CommandAdapter {
    @Autowired
    private StCOperationCostMapper stCOperationcostMapper;
    @Autowired
    private StCOperationCostItemMapper stCOperationcostItemMapper;

    @Override
    public ValueHolder execute(QuerySession querySession) throws NDSException {
        DefaultWebEvent event = querySession.getEvent();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(
                event.getParameterValue("param"), "yyyy-MM-dd HH:mm:ss"),
                Feature.OrderedField);
        Long id = param.getLong("objid");
        JSONObject fixColumn = param.getJSONObject("fixcolumn");
        if (fixColumn != null) {
            if (id != null && id > 0) {
                return updateOperationcost(querySession, fixColumn, id);
            } else {
                return addOperationcost(querySession, fixColumn);
            }
        }
        throw new NDSException("当前记录已不存在！");
    }

    private ValueHolder addOperationcost(QuerySession session, JSONObject fixColumn) {
        String operation = fixColumn.getString(StConstant.TAB_ST_C_OPERATIONCOST);
        if (StringUtils.isNotEmpty(operation)) {
            StCOperationcostDO stCOperationcostDO = JSON.parseObject(operation, StCOperationcostDO.class);
            stCOperationcostDO.setId(ModelUtil.getSequence(StConstant.TAB_ST_C_OPERATIONCOST));
            //时间判断
            ValueHolder valueHolder = new ValueHolder();
            if (!checkOperationcostDateTime(stCOperationcostDO, valueHolder)) {
                return valueHolder;
            }
            //基本字段值设置
            StBeanUtils.makeCreateField(stCOperationcostDO, session.getUser());//创建修改信息
            stCOperationcostDO.setModifierename(session.getUser().getEname());//修改人账号
            stCOperationcostDO.setOwnerename(session.getUser().getEname());//创建人账号
            if ((stCOperationcostMapper.insert(stCOperationcostDO)) > 0) {
                //单据编号
                JSONObject sequence = new JSONObject();
                sequence.put("TAB_ST_C_OPERATIONCOST", "PARAN");
                SequenceExec exec = SequenceGenUtil.preGenerateSequence()
                        .add("SEQ_ST_C_OPERATIONCOST", sequence, stCOperationcostDO.getId(),
                                stCOperationcostMapper, "updateSequence");
                exec.exec();

                //操作费实体仓明细
                if (stCOperationcostDO.getCpCPhyWarehouseId() != null) {
                    String ids = stCOperationcostDO.getCpCPhyWarehouseId();
                    String[] lIds = ids.split(",");
                    saveOperationcostItem(session, stCOperationcostDO.getId(), lIds);
                }
                return ValueHolderUtils.getSuccessValueHolder(stCOperationcostDO.getId(), StConstant.TAB_ST_C_OPERATIONCOST);
            } else {
                return ValueHolderUtils.getFailValueHolder("保存失败");
            }
        }
        throw new NDSException("当前表" + StConstant.TAB_ST_C_OPERATIONCOST + "不存在！");
    }

    private ValueHolder updateOperationcost(QuerySession session, JSONObject fixColumn, Long id) {
        ValueHolder valueHolder = new ValueHolder();
        //操作费主表状态数据检查
        if (!checkOperationcost(id, valueHolder)) {
            return valueHolder;
        }
        String operation = fixColumn.getString(StConstant.TAB_ST_C_OPERATIONCOST);
        JSONArray errorArray = new JSONArray();
        int insertPrice = 1;//保存明细
        if (StringUtils.isNotEmpty(operation)) {
            StCOperationcostDO stCOperationcostDO = JSON.parseObject(operation, StCOperationcostDO.class);
            stCOperationcostDO.setId(id);
            StCOperationcostDO stCOperationcostDO_Old = stCOperationcostMapper.selectById(id);
            if (stCOperationcostDO.getBeginTime() == null) {
                stCOperationcostDO.setBeginTime(stCOperationcostDO_Old.getBeginTime());
            }
            if (stCOperationcostDO.getEndTime() == null) {
                stCOperationcostDO.setEndTime(stCOperationcostDO_Old.getEndTime());
            }

            //时间判断
            if (!checkOperationcostDateTime(stCOperationcostDO, valueHolder)) {
                return valueHolder;
            }
            //基本字段值设置
            StBeanUtils.makeModifierField(stCOperationcostDO, session.getUser());//修改信息
            stCOperationcostDO.setModifierename(session.getUser().getEname());//修改人账号

            if (stCOperationcostMapper.updateById(stCOperationcostDO) > 0) {
                //操作费实体仓明细
                if (stCOperationcostDO.getCpCPhyWarehouseId() != null) {
                    String ids = stCOperationcostDO.getCpCPhyWarehouseId();
                    String[] lIds = ids.split(",");
                    saveOperationcostItem(session, stCOperationcostDO.getId(), lIds);
                }
                return StBeanUtils.getExcuteValueHolder(errorArray);
            }
        }
        return ValueHolderUtils.getFailValueHolder("保存失败");
    }

    private void saveOperationcostItem(QuerySession session, Long id, String[] ids) {
        //先删除
        HashMap<String, Object> map = new HashMap<>();
        map.put("ST_C_OPERATIONCOST_ID", id);
        int delete = stCOperationcostItemMapper.deleteByMap(map);
        if (delete < 0) {
            throw new NDSException("操作费方案-实体仓库明细插入失败！");
        }
        for (String wareId : ids) {
            if (wareId.isEmpty()) continue;
            StCOperationcostItemDO item = new StCOperationcostItemDO();
            item.setId(ModelUtil.getSequence(StConstant.TAB_ST_C_OPERATIONCOST_ITEM));//主键
            item.setStCOperationcostId(id);//主表外键
            item.setCpCPhyWarehouseId(Long.valueOf(wareId));//店铺外键
            StBeanUtils.makeCreateField(item, session.getUser());
            item.setModifierename(session.getUser().getEname());//修改人账号
            item.setOwnerename(session.getUser().getEname());//创建人账号
            int insert = stCOperationcostItemMapper.insert(item);
            if (insert < 0) {
                throw new NDSException("操作费方案-实体仓库明细插入失败！");
            }
        }
    }

    private boolean checkOperationcost(Long id, ValueHolder valueHolder) {
        StCOperationcostDO stCOperationcostDO = stCOperationcostMapper.selectById(id);
        if (stCOperationcostDO == null) {
            valueHolder.put("code", -1);
            valueHolder.put("message", "当前记录已不存在！");
            return false;
        }
        if (stCOperationcostDO.getBillStatus() == null
                || !StConstant.CON_BILL_STATUS_01.equals(stCOperationcostDO.getBillStatus())) {
            valueHolder.put("code", -1);
            valueHolder.put("message", "当前记录不是未审核，不允许编辑！");
            return false;
        }
        return true;
    }


    /**
     * @param stCOperationcostDO
     * @param valueHolder
     * @return booleangetExcuteValueHolder
     * @Descroption 时间判断
     * @Author: 郑小龙
     * @Date 2019/3/12
     */
    private boolean checkOperationcostDateTime(StCOperationcostDO stCOperationcostDO, ValueHolder valueHolder) {

        if (stCOperationcostDO == null) {
            return true;
        }

        if (stCOperationcostDO.getEname() != null && stCOperationcostMapper.selectStCOperationcostByEname(stCOperationcostDO.getEname()) != 0) {
            valueHolder.put("code", -1);
            valueHolder.put("message", "方案名称" + stCOperationcostDO.getEname() + "已存在!");
            return false;
        }

        if (stCOperationcostDO.getEndTime() != null && stCOperationcostDO.getBeginTime() != null) {
            if (stCOperationcostDO.getBeginTime().before(new Date())) {
                valueHolder.put("code", -1);
                valueHolder.put("message", "方案的生效日期不能小于当前日期！");
                return false;
            }

            if (stCOperationcostDO.getEndTime().before(stCOperationcostDO.getBeginTime())) {
                valueHolder.put("code", -1);
                valueHolder.put("message", "方案的结束日期不能小于生效日期！");
                return false;
            }
        }
        return true;
    }
}
