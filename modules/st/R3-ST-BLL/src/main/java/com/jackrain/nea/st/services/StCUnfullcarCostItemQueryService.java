package com.jackrain.nea.st.services;

import com.alibaba.fastjson.JSON;
import com.jackrain.nea.st.mapper.StCUnfullcarCostItemMapper;
import com.jackrain.nea.st.model.table.StCUnfullcarCostItem;
import com.jackrain.nea.st.request.StCUnfullcarCostArrivalDaysRequest;
import com.jackrain.nea.st.result.StCUnfullcarCostArrivalDaysResponse;
import com.jackrain.nea.sys.constants.ResultCode;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.utility.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.ListUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2024-02-27 11:04
 */
@Slf4j
@Component
public class StCUnfullcarCostItemQueryService {
    @Resource
    private StCUnfullcarCostItemMapper unfullcarCostItemMapper;

    public ValueHolderV14<StCUnfullcarCostArrivalDaysResponse> queryArrivalDays(StCUnfullcarCostArrivalDaysRequest request) {
        if (Objects.isNull(request.getCpCLogisticsId())
                || Objects.isNull(request.getCpCPhyWarehouseId())
                || Objects.isNull(request.getProvinceId())) {
            log.info(LogUtil.format("入参均必填，入参:{}",
                    "StCUnfullcarCostItemQueryCmdImpl.queryArrivalDays"), JSON.toJSONString(request));
            return new ValueHolderV14<>(ResultCode.FAIL, "入参均必填");
        }

        List<StCUnfullcarCostItem> unfullcarCostItemList =
                unfullcarCostItemMapper.listUnfullcardItem(request.getCpCLogisticsId(), request.getCpCPhyWarehouseId(), request.getProvinceId());
        Integer arrivalDays = ListUtils.emptyIfNull(unfullcarCostItemList).stream()
                .filter(obj -> Objects.nonNull(obj.getArrivalDays()))
                .findAny().map(StCUnfullcarCostItem::getArrivalDays)
                .orElse(null);
        if (Objects.isNull(arrivalDays)) {
            return new ValueHolderV14<>(ResultCode.FAIL, "未找到对应预计到达天数");
        }
        StCUnfullcarCostArrivalDaysResponse resp = new StCUnfullcarCostArrivalDaysResponse();
        resp.setArrivalDays(arrivalDays);

        return new ValueHolderV14<>(resp, ResultCode.SUCCESS, "查询成功");
    }
}
