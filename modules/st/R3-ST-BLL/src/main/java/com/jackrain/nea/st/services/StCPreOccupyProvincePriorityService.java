package com.jackrain.nea.st.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.st.common.StConstant;
import com.jackrain.nea.st.mapper.CpCPhyWarehouseMapper;
import com.jackrain.nea.st.mapper.RegionQueryMapper;
import com.jackrain.nea.st.mapper.StCPreOccupyProvincePriorityMapper;
import com.jackrain.nea.st.model.table.CpCPhyWarehouseDO;
import com.jackrain.nea.st.model.table.CpCRegion;
import com.jackrain.nea.st.model.table.StCPreOccupyProvincePriority;
import com.jackrain.nea.st.utils.JsonUtils;
import com.jackrain.nea.st.utils.StBeanUtils;
import com.jackrain.nea.st.utils.ValueHolderUtils;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @ClassName StCPreOccupyProvincePriorityService
 * @Description 订单预寻源
 * <AUTHOR>
 * @Date 2025/2/26 10:59
 * @Version 1.0
 */
@Component
@Slf4j
public class StCPreOccupyProvincePriorityService {

    @Autowired
    private StCPreOccupyProvincePriorityMapper stCPreOccupyProvincePriorityMapper;
    @Autowired
    private RegionQueryMapper regionQueryMapper;
    @Autowired
    private CpCPhyWarehouseMapper cpCPhyWarehouseMapper;

    public ValueHolder saveOrUpdate(QuerySession session) {
        DefaultWebEvent event = session.getEvent();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(
                        event.getParameterValue("param"), "yyyy-MM-dd HH:mm:ss", SerializerFeature.WriteMapNullValue),
                Feature.OrderedField);
        if (param != null) {
            Long id = param.getLong("objid");
            JSONObject fixColumn = param.getJSONObject("fixcolumn");
            JSONObject requestParam = fixColumn.getJSONObject("ST_C_PRE_OCCUPY_PROVINCE_PRIORITY");
            StCPreOccupyProvincePriority stCPreOccupyProvincePriority = JsonUtils.jsonParseClass(requestParam, StCPreOccupyProvincePriority.class);
            if (fixColumn != null && id != null) {
                if (id != -1) {
                    return ValueHolderUtils.getSuccessValueHolder("success");
                } else {
                    return insert(stCPreOccupyProvincePriority, session.getUser());
                }
            }
        }
        return null;
    }

    public ValueHolder delete(QuerySession session) {
        DefaultWebEvent event = session.getEvent();
        User user = session.getUser();
        String ids = String.valueOf(event.getData().get("OBJIDS"));
        log.info("删除订单预寻源：{}", ids);
        if (StringUtils.isEmpty(ids)) {
            return ValueHolderUtils.getFailValueHolder("请选择需要删除的订单预寻源");
        }
        for (String id : ids.split(",")) {
            Long objid = Long.valueOf(id);
            StCPreOccupyProvincePriority stCPreOccupyProvincePriorityDB = new StCPreOccupyProvincePriority();
            stCPreOccupyProvincePriorityDB.setId(objid);
            stCPreOccupyProvincePriorityDB.setIsactive("N");
            StBeanUtils.makeModifierField(stCPreOccupyProvincePriorityDB, user);
            stCPreOccupyProvincePriorityMapper.updateById(stCPreOccupyProvincePriorityDB);
        }
        return ValueHolderUtils.getSuccessValueHolder("success");
    }

    private ValueHolder insert(StCPreOccupyProvincePriority stCPreOccupyProvincePriority, User user) {
        Long cpCProvinceId = stCPreOccupyProvincePriority.getCpCProvinceId();
        // 根据省份id 查询数据库
        StCPreOccupyProvincePriority stCPreOccupyProvincePriorityDB = stCPreOccupyProvincePriorityMapper.selectByCpCProvinceId(cpCProvinceId);
        if (stCPreOccupyProvincePriorityDB != null) {
            return ValueHolderUtils.getFailValueHolder("收货省已存在，禁止新增！");
        }
        // 根据省份id 获取省份信息
        CpCRegion cpCRegion = regionQueryMapper.selectById(cpCProvinceId);
        if (cpCRegion == null) {
            return ValueHolderUtils.getFailValueHolder("收货省不存在，禁止新增！");
        }
        stCPreOccupyProvincePriority.setCpCProvinceId(cpCRegion.getId());
        stCPreOccupyProvincePriority.setCpCProvinceName(cpCRegion.getEname());
        stCPreOccupyProvincePriority.setCpCProvinceCode(cpCRegion.getEcode());
        Long warehouseId = stCPreOccupyProvincePriority.getCpCPhyWarehouseId();
        CpCPhyWarehouseDO cpCPhyWarehouseDO = cpCPhyWarehouseMapper.selectById(warehouseId);
        if (cpCPhyWarehouseDO == null) {
            return ValueHolderUtils.getFailValueHolder("收货仓库不存在，禁止新增！");
        }
        stCPreOccupyProvincePriority.setCpCPhyWarehouseId(cpCPhyWarehouseDO.getId());
        stCPreOccupyProvincePriority.setCpCPhyWarehouseEcode(cpCPhyWarehouseDO.getEcode());
        stCPreOccupyProvincePriority.setCpCPhyWarehouseEname(cpCPhyWarehouseDO.getEname());
        Long id = ModelUtil.getSequence(StConstant.ST_C_PRE_OCCUPY_PROVINCE_PRIORITY);
        stCPreOccupyProvincePriority.setId(id);
        StBeanUtils.makeCreateField(stCPreOccupyProvincePriority, user);
        stCPreOccupyProvincePriorityMapper.insert(stCPreOccupyProvincePriority);
        return ValueHolderUtils.getSuccessValueHolder(id, StConstant.ST_C_PRE_OCCUPY_PROVINCE_PRIORITY, "");
    }

    public ValueHolderV14<StCPreOccupyProvincePriority> queryByProvinceEcode(String provinceEcode) {
        ValueHolderV14<StCPreOccupyProvincePriority> result = new ValueHolderV14<>();
        StCPreOccupyProvincePriority stCPreOccupyProvincePriority = stCPreOccupyProvincePriorityMapper.selectByCpCProvinceEcode(provinceEcode);
        if (stCPreOccupyProvincePriority == null) {
            result.setCode(ResultCode.FAIL);
            result.setMessage("收货省不存在，禁止新增！");
            return result;
        }
        result.setData(stCPreOccupyProvincePriority);
        result.setCode(ResultCode.SUCCESS);
        return result;
    }
}
