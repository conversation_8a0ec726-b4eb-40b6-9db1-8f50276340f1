package com.jackrain.nea.st.validate;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.cpext.model.table.CpShop;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.st.common.StConstant;
import com.jackrain.nea.st.model.table.StCLiveCastStrategyDO;
import com.jackrain.nea.st.rpc.RpcCpService;
import com.jackrain.nea.st.services.LiveCastStrategyService;
import com.jackrain.nea.tableService.DbRowAction;
import com.jackrain.nea.tableService.MainTableRecord;
import com.jackrain.nea.tableService.TableServiceContext;
import com.jackrain.nea.tableService.validate.BaseValidator;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.utils.AssertUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Locale;
import java.util.Objects;

/**
 * Description： 新增更新校验类
 * Author: RESET
 * Date: Created in 2020/7/1 15:53
 * Modified By:
 */
@Slf4j
@Component
public class StCLiveCastAddAndSaveValidate extends BaseValidator {

    @Override
    public void validate(TableServiceContext context, MainTableRecord mainTableRecord) {
        DbRowAction action = mainTableRecord.getAction();
        // log.info(LogUtil.format("live.validate.mainTableRecord.getMainData:{}", JSON.toJSONString(mainTableRecord.getMainData()));
        log.info(LogUtil.format("live.validate.mainTableRecord.getCommitData:{}"),
                JSON.toJSONString(mainTableRecord.getCommitData()));
        log.info(LogUtil.format("live.validate.mainTableRecord.getOrignalData:{}"),
                JSON.toJSONString(mainTableRecord.getOrignalData()));
        log.info(LogUtil.format("live.validate.mainTableRecord.getId:{}"),
                JSON.toJSONString(mainTableRecord.getId()));
        //log.info(LogUtil.format("live.validate.mainTableRecord.getTable:{}", JSON.toJSONString(mainTableRecord.getTable()));
        //log.info(LogUtil.format("live.validate.mainTableRecord.getSubTables:{}", JSON.toJSONString(mainTableRecord.getSubTables()));

        switch (action) {
            case INSERT:
                validateInsert(context, mainTableRecord);
                break;
            case UPDATE:
                validateUpdate(context, mainTableRecord);
                break;
            default:
                break;
        }
    }

    /**
     * 更新校验（考本）
     * @param context
     * @param row
     */
    public void validateUpdateBySuper(TableServiceContext context, MainTableRecord row) {
        Long objId = row.getId();
        if (objId == null) {
            throw new NDSException(Resources.getMessage("请先选择需要编辑的记录！", context.getLocale(), new Object[0]));
        } else {
            JSONObject data = row.getMainData().getOrignalData();
            if (data == null) {
                throw new NDSException(Resources.getMessage("当前记录已不存在！", context.getLocale(), new Object[0]));
            } else {
                Integer status = data.getInteger("STATUS");
                String isActive = data.getString("ISACTIVE");
                if ("N".equals(isActive)) {
                    throw new NDSException(Resources.getMessage("当前记录已作废,不允许编辑！", context.getLocale(), new Object[0]));
                } else if (status != null && (status == 2 || status == 3)) {
                    throw new NDSException(Resources.getMessage("当前记录已审核,不允许编辑！", context.getLocale(), new Object[0]));
                }
            }
        }
    }

    /**
     * 更新校验
     *  1. 条件填充店铺
     *  2. 非空校验
     * @param context
     * @param mainTableRecord
     */
    private void validateUpdate(TableServiceContext context, MainTableRecord mainTableRecord) {
        validateUpdateBySuper(context, mainTableRecord);
        // validateCPermission(context, mainTableRecord);

        // 更新提取数据
        JSONObject data = mainTableRecord.getMainData().getCommitData();

        if (Objects.nonNull(data.getLong(StCLiveConsts.CP_C_SHOP_ID))) {
            // 店铺有变更，附带值需要同时变更
            fillShopField(data);
        }
    }

    /**
     * validateCPermission
     * @param context
     * @param mainTableRecord
     */
    private void validateCPermission(TableServiceContext context, MainTableRecord mainTableRecord) {
        JSONObject data = mainTableRecord.getMainData().getCommitData();
        Integer status = data.getInteger(StCLiveConsts.STRATEGY_STATUS);

        // 已审核不允许更新
        boolean check = Objects.nonNull(status) && !StConstant.LIVE_STRATEGY_STATUS_INIT.equals(status);
        AssertUtils.isTrue(check, "非初始状态不允许更新！", context.getLocale());
    }

    /**
     * 插入校验
     * @param context
     * @param mainTableRecord
     */
    private void validateInsert(TableServiceContext context, MainTableRecord mainTableRecord) {
        Locale locale = context.getLocale();
        // 保存提取数据
        JSONObject data = mainTableRecord.getMainData().getCommitData();

        // 非空校验
        checkNotNull(data, locale);

        // 名称存在校验
        AssertUtils.isTrue(!checkExistsStrategyName(data.getString(StCLiveConsts.STRATEGY_NAME)), "策略名称已存在！", locale);

        // 填充
        fillShopField(data);
        fillOthers(data);
    }

    /**
     * 非空校验
     * @param data
     * @param locale
     */
    private void checkNotNull(JSONObject data, Locale locale) {
        // 非空校验
        // 店铺
        AssertUtils.notNull(data.get(StCLiveConsts.CP_C_SHOP_ID), "店铺不能为空！", locale);
        // 名称
        AssertUtils.notNull(data.get(StCLiveConsts.STRATEGY_NAME), "策略名称不能为空！", locale);
        // 主播
        //AssertUtils.notNull(data.get(StCLiveConsts.ANCHOR_ID), "主播ID不能为空！", locale);
        //AssertUtils.notNull(data.get(StCLiveConsts.ANCHOR_NICK_NAME), "主播昵称不能为空！", locale);
        AssertUtils.notNull(data.get(StCLiveConsts.BILL_TIME_TYPE), "时间类型不能为空！", locale);
        AssertUtils.notNull(data.get(StCLiveConsts.LIVE_PLATFORM), "直播平台不能为空！", locale);
        AssertUtils.notNull(data.get(StCLiveConsts.START_TIME), "开始时间不能为空！", locale);
        AssertUtils.notNull(data.get(StCLiveConsts.END_TIME), "结束时间不能为空！", locale);
    }

    /**
     * 填充其他信息
     * @param data
     */
    private void fillOthers(JSONObject data) {
        // 填充状态，新增为初始状态
        data.put(StCLiveConsts.STRATEGY_STATUS, StConstant.LIVE_STRATEGY_STATUS_INIT);
    }

    /**
     * 填充店铺信息
     * @param data
     */
    private void fillShopField(JSONObject data) {
        RpcCpService rpcCpService = ApplicationContextHandle.getBean(RpcCpService.class);

        // 取店铺ID
        Long cpCShopId = data.getLong(StCLiveConsts.CP_C_SHOP_ID);
        // 查店铺信息并填充
        if (Objects.nonNull(cpCShopId)) {
            CpShop cpShop = rpcCpService.selectCpCShopById(cpCShopId);

            if (Objects.nonNull(cpShop)) {
                data.put(StCLiveConsts.CP_C_SHOP_ECODE, cpShop.getEcode());
                data.put(StCLiveConsts.CP_C_SHOP_TITLE, cpShop.getCpCShopTitle());
            }
        }
    }

    /**
     * 校验名称是否已存在
     * @param strategyName
     */
    private boolean checkExistsStrategyName(String strategyName) {
        LiveCastStrategyService service = ApplicationContextHandle.getBean(LiveCastStrategyService.class);

        // 查
        StCLiveCastStrategyDO strategyDO = service.findByStrategyName(strategyName);
        return Objects.nonNull(strategyDO);
    }

}
