package com.jackrain.nea.st.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.github.pagehelper.util.StringUtil;
import com.jackrain.nea.cpext.model.table.CpCustomer;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.ext.sequence.observer.SequenceExec;
import com.jackrain.nea.ext.sequence.util.SequenceGenUtil;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.st.common.StConstant;
import com.jackrain.nea.st.mapper.StCSellOwngoodsCustomerMapper;
import com.jackrain.nea.st.mapper.StCSellOwngoodsItemMapper;
import com.jackrain.nea.st.mapper.StCSellOwngoodsMapper;
import com.jackrain.nea.st.model.componentJsonModel.PopCheckboxJson;
import com.jackrain.nea.st.model.componentJsonModel.PopCheckboxListsJson;
import com.jackrain.nea.st.model.componentJsonModel.PopCheckboxResultJson;
import com.jackrain.nea.st.model.componentJsonModel.PopCheckboxValueJson;
import com.jackrain.nea.st.model.table.StCSellOwngoodsCustomerDO;
import com.jackrain.nea.st.model.table.StCSellOwngoodsDO;
import com.jackrain.nea.st.model.table.StCSellOwngoodsItemDO;
import com.jackrain.nea.st.rpc.RpcCpService;
import com.jackrain.nea.st.utils.JsonUtils;
import com.jackrain.nea.st.utils.StBeanUtils;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.beanutils.ConvertUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * @Descroption 经销商自有商品复制
 * <AUTHOR>
 * @Date 2019/5/13
 */
@Component
@Slf4j
@Transactional
public class SellOwnGoodsCopyService {
    @Autowired
    private StCSellOwngoodsMapper mapper;
    @Autowired
    private StCSellOwngoodsItemMapper itemMapper;
    @Autowired
    private StCSellOwngoodsCustomerMapper customerMapper;
    @Autowired
    private RpcCpService rpcCpService;

    public ValueHolderV14<List<StCSellOwngoodsDO>> execute(QuerySession querySession) throws NDSException {
        //1.获取传入参数
        DefaultWebEvent event = querySession.getEvent();
        JSONObject param = JSON.parseObject(JSON.toJSONString(event.getParameterValue("param"), SerializerFeature.WriteMapNullValue), Feature.OrderedField);
        ValueHolderV14<List<StCSellOwngoodsDO>> valueHolderV14 = new ValueHolderV14<>();
        String errMessage = "";
        if (param == null) {
            valueHolderV14.setCode(-1);
            valueHolderV14.setMessage("参数为空！");
            return valueHolderV14;
        }
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("Start SellOwnGoodsCopyService.execute. ReceiveParams: {}"), param.toJSONString());
        }
        String customerIdStr = param.getString("CP_C_CUSTOMER_ID");
        JSONArray idsArray = param.getJSONArray("IDS");
        if (StringUtil.isEmpty(customerIdStr)) {
            valueHolderV14.setCode(-1);
            valueHolderV14.setMessage("未选中经销商！");
            return valueHolderV14;
        }
        if (idsArray != null) {
            for (int i = 0; i < idsArray.size(); i++) {
                long id = idsArray.getLong(i);
                StCSellOwngoodsDO mainDO = mapper.selectById(id);
                List<StCSellOwngoodsItemDO> itemList = itemMapper.selectItemByMainId(id);
                //主表数据复制
                StCSellOwngoodsDO copyMainDO = new StCSellOwngoodsDO();
                if (mainDO != null) {
                    String billNo = mainDO.getBillNo();
                    try {
                        //1.业务信息复制
                        BeanUtils.copyProperties(mainDO, copyMainDO);
                        //2.经销商信息替换且分销代销方案审核状态设置为未审核
                        copyMainDO.setBillStatus(StConstant.CON_BILL_STATUS_01);
                        //setCustomerId(copyMainDO,customerIdStr);
                        copyMainDO.setCpCCustomerId(customerIdStr);
                        //3.主表id及创建修改时间赋值
                        copyMainDO.setId(ModelUtil.getSequence(StConstant.TAB_ST_C_SELL_OWNGOODS));
                        //主表创建信息更新
                        StBeanUtils.makeCreateField(copyMainDO, querySession.getUser());
                        copyMainDO.setOwnerename(querySession.getUser().getEname());
                        copyMainDO.setModifierename(querySession.getUser().getEname());
                        //4.主表数据保存
                        int insertResult = mapper.insert(copyMainDO);
                        if (insertResult <= 0) {
                            throw new NDSException("方案[" + billNo + "]复制插入主表数据失败!");
                        }
                        //5.单据编号生成更新
                        JSONObject sequence = new JSONObject();
                        sequence.put("ST_C_SELL_OWNGOODS", "PARAN");
                        SequenceExec exec = SequenceGenUtil.preGenerateSequence()
                                .add("SEQ_ST_C_SELL_OWNGOODS", sequence, id, mapper, "updateSequence");
                        exec.exec();

                        //6.拆分保存经销商信息
                        String[] customerIdArr = customerIdStr.split(",");
                        List<StCSellOwngoodsCustomerDO> customerList = insertCustomerItem(querySession, copyMainDO.getId(), customerIdArr);
                    } catch (Exception e) {
                        errMessage = e.getMessage();
                        break;
                    }
                }
                //商品明细复制
                if (!CollectionUtils.isEmpty(itemList)) {
                    for (StCSellOwngoodsItemDO item : itemList) {
                        StCSellOwngoodsItemDO copyItemDO = new StCSellOwngoodsItemDO();
                        try {
                            BeanUtils.copyProperties(item, copyItemDO);
                            copyItemDO.setId(ModelUtil.getSequence(StConstant.TAB_ST_C_SELL_OWNGOODS_ITEM));
                            copyItemDO.setStCSellOwngoodsId(copyMainDO.getId());
                            StBeanUtils.makeCreateField(copyItemDO, querySession.getUser());
                            copyItemDO.setOwnerename(querySession.getUser().getEname());
                            copyItemDO.setModifierename(querySession.getUser().getEname());
                            if (itemMapper.insert(copyItemDO) < 1) {
                                throw new NDSException("方案[" + mainDO.getBillNo() + "]复制商品明细插入失败");
                            }
                        } catch (Exception e) {
                            errMessage = e.getMessage();
                            break;
                        }
                    }
                }

            }
        }
        if (StringUtil.isNotEmpty(errMessage)) {
            valueHolderV14.setCode(-1);
            valueHolderV14.setMessage(errMessage);
        } else {
            valueHolderV14.setCode(0);
            valueHolderV14.setMessage("方案复制成功！");
        }
        return valueHolderV14;
    }

    /**
     * @param mainnDO
     * @param customerId
     * @return void
     * @Descroption 设置转换后的经销商ID
     */
    private void setCustomerId(StCSellOwngoodsDO mainnDO, String customerId) {
        String customerJson = getCustomerJson(customerId);
        mainnDO.setCpCCustomerId(customerJson);
    }

    /**
     * @param customerId
     * @return java.lang.String
     * @Descroption 转换经销商ID为json格式
     */
    private String getCustomerJson(String customerId) {
        String[] customerStrArr = customerId.split(",");
        String[] commonArr = {};
        long[] customerLongArr = (long[]) ConvertUtils.convert(customerStrArr, long.class);
        PopCheckboxJson popCheckboxJson = new PopCheckboxJson();
        popCheckboxJson.setTotal(customerStrArr.length);
        popCheckboxJson.setIdArray(commonArr);
        //设置PopCheckboxValueJson
        PopCheckboxValueJson popCheckboxValueJson = new PopCheckboxValueJson();
        popCheckboxValueJson.setTablename("CP_C_CUSTOMER");
        popCheckboxValueJson.setCondition(commonArr);
        popCheckboxValueJson.setNotin(commonArr);
        popCheckboxValueJson.setExclude(commonArr);
        popCheckboxValueJson.setGlobal("");
        popCheckboxValueJson.setIn(customerLongArr);
        popCheckboxJson.setPopCheckboxValue(popCheckboxValueJson);
        //设置PopCheckboxListsJson
        PopCheckboxListsJson popCheckboxListsJson = new PopCheckboxListsJson();
        List<PopCheckboxResultJson> resultList = new ArrayList();
        for (int i = 0; i < customerLongArr.length; i++) {
            long screen = customerLongArr[i];
            long[] screenArr = {screen};
            PopCheckboxResultJson popCheckboxResultJson = new PopCheckboxResultJson();
            CpCustomer customerDO = rpcCpService.getStoreCustomerById(screen);
            if (customerDO != null) {
                popCheckboxResultJson.setScreenString(customerDO.getEcode());
            }
            popCheckboxResultJson.setScreen(screen);
            popCheckboxResultJson.setExclude(false);
            popCheckboxResultJson.setIdList(screenArr);
            resultList.add(popCheckboxResultJson);
        }
        popCheckboxListsJson.setPopCheckboxResultJson(resultList);
        popCheckboxJson.setPopCheckboxLists(popCheckboxListsJson);
        return JsonUtils.toJsonString(popCheckboxJson);
    }

    /**
     * @param session
     * @param mainId
     * @param customerIds
     * @return java.util.List<com.jackrain.nea.st.model.table.StCSellOwngoodsCustomerDO>
     * @Descroption 插入经销商明细表
     */
    private List<StCSellOwngoodsCustomerDO> insertCustomerItem(QuerySession session, Long mainId, String[] customerIds) {
        List<StCSellOwngoodsCustomerDO> customerList = new ArrayList();
        for (int i = 0; i < customerIds.length; i++) {
            long customerIdLong = Long.valueOf(customerIds[i]);
            StCSellOwngoodsCustomerDO stCSellOwngoodsCustomerDO = new StCSellOwngoodsCustomerDO();
            stCSellOwngoodsCustomerDO.setId(ModelUtil.getSequence(StConstant.TAB_ST_C_SELL_OWNGOODS_CUSTOMER));
            stCSellOwngoodsCustomerDO.setStCSellOwngoodsId(mainId);
            stCSellOwngoodsCustomerDO.setCpCCustomerId(customerIdLong);
            StBeanUtils.makeCreateField(stCSellOwngoodsCustomerDO, session.getUser());
            stCSellOwngoodsCustomerDO.setOwnerename(session.getUser().getEname());
            stCSellOwngoodsCustomerDO.setModifierename(session.getUser().getEname());
            customerList.add(stCSellOwngoodsCustomerDO);
            if (customerMapper.insert(stCSellOwngoodsCustomerDO) < 1) {
                throw new NDSException("经销商自有商品经销商明细插入失败！");
            }
        }
        return customerList;
    }
}
