package com.jackrain.nea.st.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.st.common.StConstant;
import com.jackrain.nea.st.mapper.StAddedServiceStrategyDetailMapper;
import com.jackrain.nea.st.mapper.StAddedServiceStrategyMapper;
import com.jackrain.nea.st.model.table.StAddedServiceStrategyDO;
import com.jackrain.nea.st.model.table.StAddedServiceStrategyDetailDO;
import com.jackrain.nea.st.utils.ValueHolderUtils;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @program: ryytn-st-v3.0
 * @description: 增值服务策略删除
 * @author: haiyang
 * @create: 2023-10-25 13:48
 **/

@Slf4j
@Component
@Transactional
public class StAddedServiceStrategyDelService extends CommandAdapter {

    @Autowired
    private StAddedServiceStrategyMapper addedServiceStrategyMapper;

    @Autowired
    private StAddedServiceStrategyDetailMapper addedServiceStrategyDetailMapper;

    public ValueHolder execute(QuerySession session) throws NDSException {
        DefaultWebEvent event = session.getEvent();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(
                        event.getParameterValue("param"), "yyyy-MM-dd HH:mm:ss"),
                Feature.OrderedField);
        log.info("StAddedServiceStrategyDelService.param: {}", param.toJSONString());
        //是否删除主表
        boolean delMainFlag = param.getBoolean("isdelmtable");
        Long id = param.getLong("objid");
        if (id != null && id > 0) {
            StAddedServiceStrategyDO strategyDO = addedServiceStrategyMapper.selectById(id);
            if (strategyDO == null) {
                return ValueHolderUtils.getFailValueHolder("当前记录已不存在！");
            }
            if (delMainFlag) {
                return deleteStrategy(strategyDO);
            } else {
                //单独删除明细
                JSONObject tabitem = param.getJSONObject("tabitem");
                log.info("StAddedServiceStrategyDelService.detail: {}", tabitem.toJSONString());
                JSONArray itemArray = tabitem.getJSONArray(StConstant.ST_ADDED_SERVICE_STRATEGY_DETAIL);
                if (itemArray != null && itemArray.size() > 0) {
                    return deleteStrategyDetail(itemArray, session);
                }
            }
        }
        return ValueHolderUtils.getSuccessValueHolder("无可操作数据");
    }

    private ValueHolder deleteStrategyDetail(JSONArray itemArray, QuerySession session) {
        List<Long> ids = JSONObject.parseArray(itemArray.toJSONString(), Long.class);
        List<StAddedServiceStrategyDetailDO> strategyDetailDOList = addedServiceStrategyDetailMapper.selectBatchIds(ids);
        if (CollectionUtils.isNotEmpty(strategyDetailDOList)) {
            Map<Long, String> beforeDelObjMap = new HashMap<>();
            for (StAddedServiceStrategyDetailDO item : strategyDetailDOList) {
                beforeDelObjMap.put(item.getId(), JSON.toJSONString(item));
            }
            session.setAttribute("beforeDelObjMap", beforeDelObjMap);
        }
        addedServiceStrategyDetailMapper.deleteBatchIds(ids);
        return ValueHolderUtils.getDeleteSuccessValueHolder();
    }

    private ValueHolder deleteStrategy(StAddedServiceStrategyDO strategyDO) {
        Long id = strategyDO.getId();
        addedServiceStrategyMapper.deleteById(id);
        addedServiceStrategyDetailMapper.delete(new LambdaQueryWrapper<StAddedServiceStrategyDetailDO>().in(StAddedServiceStrategyDetailDO::getAddedStrategyId,id));
        return ValueHolderUtils.getDeleteSuccessValueHolder();
    }
}
