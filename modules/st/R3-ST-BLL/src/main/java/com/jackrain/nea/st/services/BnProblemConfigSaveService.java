package com.jackrain.nea.st.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.st.common.StConstant;
import com.jackrain.nea.st.mapper.StCBnProblemConfigMapper;
import com.jackrain.nea.st.model.table.StCBnProblemConfigDO;
import com.jackrain.nea.st.utils.JsonUtils;
import com.jackrain.nea.st.utils.StBeanUtils;
import com.jackrain.nea.st.utils.ValueHolderUtils;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * @ClassName BnProblemConfigSaveService
 * @Description 班牛
 * <AUTHOR>
 * @Date 2024/11/11 15:08
 * @Version 1.0
 */
@Component
@Slf4j
public class BnProblemConfigSaveService extends CommandAdapter {

    @Autowired
    private StCBnProblemConfigMapper stCBnProblemConfigMapper;

    public ValueHolder save(QuerySession querySession) throws NDSException {
        ValueHolder valueHolder = new ValueHolder();
        DefaultWebEvent event = querySession.getEvent();
        User user = querySession.getUser();
        List<String> nullKeyList = new ArrayList<>();
        JSONObject param = JsonUtils.filterNull((JSONObject) event.getParameterValue("param"), nullKeyList);
        JSONObject fixColumn = param.getJSONObject("fixcolumn");
        String bnTable = fixColumn.getString(StConstant.ST_C_BN_PROBLEM_CONFIG);

        StCBnProblemConfigDO bnProblemConfigDO = JSON.parseObject(bnTable, StCBnProblemConfigDO.class);
        // 因为目前没有编辑的按钮 所以只能新增。 新增的话 需要判断 问题类型
        String problemText = bnProblemConfigDO.getProblemText();
        List<StCBnProblemConfigDO> problemConfigDOList = stCBnProblemConfigMapper.selectByProblemText(problemText);
        if (CollectionUtils.isNotEmpty(problemConfigDOList)) {
            valueHolder.put("code", ResultCode.FAIL);
            valueHolder.put("message", "问题类型:" + problemText + ",已存在，请勿重复添加");
        }
        bnProblemConfigDO.setId(ModelUtil.getSequence(StConstant.ST_C_BN_PROBLEM_CONFIG));
        StBeanUtils.makeCreateField(bnProblemConfigDO, user);
        stCBnProblemConfigMapper.insert(bnProblemConfigDO);
        return ValueHolderUtils.getSuccessValueHolderByMessage(bnProblemConfigDO.getId(), StConstant.ST_C_BN_PROBLEM_CONFIG, "保存成功！");
    }
}
