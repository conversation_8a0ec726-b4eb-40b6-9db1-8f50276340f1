package com.jackrain.nea.st.services;

import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.st.common.StConstant;
import com.jackrain.nea.st.mapper.StCCompensateLogisticsMapper;
import com.jackrain.nea.st.model.request.CompensateQueryRequest;
import com.jackrain.nea.st.model.table.StCCompensateLogisticsDO;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Component
@Slf4j
public class CompensateQueryService {
    @Autowired
    private StCCompensateLogisticsMapper stCCompensateLogisticsMapper;

    public ValueHolderV14<BigDecimal> selectByAcPayable(CompensateQueryRequest request) throws NDSException {

        ValueHolderV14<BigDecimal> result = new ValueHolderV14<>();
        if (null == request.getLogisticsId() || null == request.getPhyWarehouseId()) {
            result.setCode(ResultCode.FAIL);
            result.setMessage("实体仓ID/快递公司ID为空");
            return result;
        }
        if (null == request.getCurrentDate()) {
            request.setCurrentDate(new Date());
        }
        List<StCCompensateLogisticsDO> compensateLogisticsList = stCCompensateLogisticsMapper.selectByAcPayable(
                                                                    request.getLogisticsId(),
                                                                    request.getPhyWarehouseId(),
                                                                    request.getCurrentDate());
        if (CollectionUtils.isEmpty(compensateLogisticsList)) {
            result.setCode(ResultCode.FAIL);
            result.setMessage("查询不到对应快递赔付策略！");
            return result;
        }
        // 应付金额
        BigDecimal payable = BigDecimal.ZERO;
        StCCompensateLogisticsDO compensateLogistics =compensateLogisticsList.get(0);
        if (StConstant.COMPENSATE_PRICE.equals(compensateLogistics.getCompensateType())) {
            if (StConstant.PRICE.equals(compensateLogistics.getCompensateStandard())) {
                //当赔付标准=销售价时，应付金额（返回金额）=销售价*倍数
                payable = this.multiplyBigDecimal(request.getPrice(), compensateLogistics.getMultiple(), 2);
            } else if (StConstant.PRICE_LIST.equals(compensateLogistics.getCompensateStandard())) {
                //当赔付标准=标准价时，应付金额（返回金额）=标准价*倍数
                payable = this.multiplyBigDecimal(request.getPriceList(), compensateLogistics.getMultiple(), 2);
            }
        } else if (StConstant.COMPENSATE_FIXED.equals(compensateLogistics.getCompensateType())) {
            //赔付类型=固定结算时，直接取结算价，返回金额
            payable = compensateLogistics.getSettlementprice();
        }
        result.setCode(ResultCode.SUCCESS);
        result.setData(payable);
        result.setMessage("获取成功");
        return result;
    }

    /**
     * 乘法(default roundingMode = BigDecimal.ROUND_HALF_UP)
     *
     * @param dec1 数字1
     * @param dec2 数字2
     * @param scale 位数
     * @return dec1 * dec2
     */
    private BigDecimal multiplyBigDecimal(final BigDecimal dec1, final BigDecimal dec2,
                                                final int scale) {
        return (dec1 != null ? dec1 : BigDecimal.ZERO)
                .multiply(dec2 != null ? dec2 : BigDecimal.ZERO).setScale(scale, BigDecimal.ROUND_HALF_UP);
    }
}
