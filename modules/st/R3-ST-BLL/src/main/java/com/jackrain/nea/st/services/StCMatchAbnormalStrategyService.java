package com.jackrain.nea.st.services;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.oc.oms.model.enums.YesNoEnum;
import com.jackrain.nea.st.mapper.StCMatchAbnormalStrategyMapper;
import com.jackrain.nea.st.model.enums.CommStatusEnum;
import com.jackrain.nea.st.model.enums.MatchAbnormalHandleTypeEnum;
import com.jackrain.nea.st.model.table.StCMatchAbnormalStrategy;
import com.jackrain.nea.st.utils.StBeanUtils;
import com.jackrain.nea.web.face.User;
import io.searchbox.strings.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @program: r3-st
 * @description: 发货单异常类型定义
 * @author: caomalai
 * @create: 2022-10-16 16:24
 **/
@Component
@Slf4j
public class StCMatchAbnormalStrategyService {
    @Autowired
    private StCMatchAbnormalStrategyMapper stCMatchAbnormalStrategyMapper;

    @Transactional(rollbackFor = Exception.class)
    public Long save(JSONObject param, User user) {
        JSONObject fixColumn = param.getJSONObject("fixcolumn");
        Long objid = param.getLong("objid");

        String tableName = param.getString("table");
        JSONObject jsonObject = fixColumn.getJSONObject(tableName);
        //新增
        if (Objects.isNull(objid) || objid < 0) {
            if(Objects.isNull(jsonObject)){
                throw new NDSException("参数不能为空");
            }
            StCMatchAbnormalStrategy stCMatchAbnormalStrategy
                    = JSONObject.parseObject(jsonObject.toJSONString(), StCMatchAbnormalStrategy.class);
            if (StringUtils.isBlank(stCMatchAbnormalStrategy.getMatchKeywords())) {
                throw new NDSException("识别关键字不能为空");
            }
            if (Objects.isNull(stCMatchAbnormalStrategy.getAbnormalType())) {
                throw new NDSException("异常类型不能为空");
            }
            if (Objects.isNull(stCMatchAbnormalStrategy.getAbnormalDesc())) {
                throw new NDSException("异常类型说明不能为空");
            }

            if (!Objects.equals(stCMatchAbnormalStrategy.getAbnormalHandleType(), MatchAbnormalHandleTypeEnum.ONLY_TAG.getValue())) {
                if (Objects.isNull(stCMatchAbnormalStrategy.getAbnormalHandleTime())) {
                    throw new NDSException("请填写卡/hold单时长！");
                }
                if (stCMatchAbnormalStrategy.getAbnormalHandleTime() < 0) {
                    throw new NDSException("卡/hold单时长必须为正整数！");
                }
                if (Objects.isNull(stCMatchAbnormalStrategy.getAbnormalHandleUnit())) {
                    throw new NDSException("请填写异常处理时间单位！");
                }
            } else {
                stCMatchAbnormalStrategy.setAbnormalHandleTime(null);
                stCMatchAbnormalStrategy.setAbnormalHandleUnit(null);
            }
            //校验唯一性
            QueryWrapper<StCMatchAbnormalStrategy> queryWrapper = new QueryWrapper<>();
            queryWrapper.lambda().eq(StCMatchAbnormalStrategy::getMatchKeywords, stCMatchAbnormalStrategy.getMatchKeywords());
            List<StCMatchAbnormalStrategy> itemList = stCMatchAbnormalStrategyMapper.selectList(queryWrapper);
            if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(itemList)) {
                throw new NDSException("识别关键字重复，不允许保存！");
            }

            Long id = ModelUtil.getSequence(tableName.toLowerCase());
            stCMatchAbnormalStrategy.setId(id);
            StBeanUtils.makeCreateField(stCMatchAbnormalStrategy, user);
            stCMatchAbnormalStrategyMapper.insert(stCMatchAbnormalStrategy);
            objid = id;
        } else {
            //更新
            StCMatchAbnormalStrategy stCMatchAbnormalStrategy
                    = JSONObject.parseObject(jsonObject.toJSONString(), StCMatchAbnormalStrategy.class);
            //校验唯一性
            QueryWrapper<StCMatchAbnormalStrategy> queryWrapper = new QueryWrapper<>();
            queryWrapper.lambda().eq(StCMatchAbnormalStrategy::getMatchKeywords, stCMatchAbnormalStrategy.getMatchKeywords())
                    .ne(objid > 0, StCMatchAbnormalStrategy::getId, stCMatchAbnormalStrategy.getId());
            List<StCMatchAbnormalStrategy> itemList = stCMatchAbnormalStrategyMapper.selectList(queryWrapper);
            if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(itemList)) {
                throw new NDSException("识别关键字重复，不允许保存！");
            }
            if (ObjectUtil.isNotNull(stCMatchAbnormalStrategy.getAbnormalHandleTime()) && stCMatchAbnormalStrategy.getAbnormalHandleTime() < 0) {
                throw new NDSException("卡/hold单时长必须为正整数！");
            }

            if (ObjectUtil.isNotNull(stCMatchAbnormalStrategy.getAbnormalHandleType()) &&
                    Objects.equals(stCMatchAbnormalStrategy.getAbnormalHandleType(), MatchAbnormalHandleTypeEnum.ONLY_TAG.getValue())) {
                stCMatchAbnormalStrategyMapper.clearAbnormalTime(objid);
            }
            stCMatchAbnormalStrategy.setId(objid);
            StBeanUtils.makeModifierField(stCMatchAbnormalStrategy, user);
            if (CommStatusEnum.YES.desc().equals(stCMatchAbnormalStrategy.getIsactive())) {
                stCMatchAbnormalStrategy.setIsactive(CommStatusEnum.YES.charVal());
            }
            if (CommStatusEnum.NO.desc().equals(stCMatchAbnormalStrategy.getIsactive())) {
                stCMatchAbnormalStrategy.setIsactive(CommStatusEnum.NO.charVal());
            }
            stCMatchAbnormalStrategyMapper.updateById(stCMatchAbnormalStrategy);
        }
        return objid;
    }

    /**
     * 删除
     * @param param
     */
    public void delete(JSONObject param) {
        Long objid = param.getLong("objid");
        //删除主表
        stCMatchAbnormalStrategyMapper.deleteById(objid);
    }

    /**
     * 匹配异常
     * @param abnormalType
     * @param abnormalContent
     * @return
     */
    public List<StCMatchAbnormalStrategy> match(Integer abnormalType, String abnormalContent){
        if(Objects.isNull(abnormalType)){
            throw new NDSException("异常类型不能为空");
        }
        if(StringUtils.isBlank(abnormalContent)){
            throw new NDSException("识别内容不能为空");
        }
        List<StCMatchAbnormalStrategy> stCMatchAbnormalStrategies
                = stCMatchAbnormalStrategyMapper.selectList(new QueryWrapper<StCMatchAbnormalStrategy>().lambda()
                .eq(StCMatchAbnormalStrategy::getAbnormalType, abnormalType)
                .eq(StCMatchAbnormalStrategy::getIsactive, YesNoEnum.Y.getKey())
            );
        List<StCMatchAbnormalStrategy> strategies = new ArrayList<>();
        for(StCMatchAbnormalStrategy item:stCMatchAbnormalStrategies){
            if(abnormalContent.contains(item.getMatchKeywords())){
                strategies.add(item);
            }
        }
        if(CollectionUtils.isNotEmpty(strategies)){
            //根据ID去重后的数据
            return strategies.stream().collect(Collectors.collectingAndThen(Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(a -> a.getId()))), ArrayList::new));
        }
        return strategies;
    }
}
