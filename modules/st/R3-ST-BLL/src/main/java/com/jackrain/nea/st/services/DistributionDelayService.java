package com.jackrain.nea.st.services;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.es.util.ElasticSearchUtil;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.st.common.EsConstant;
import com.jackrain.nea.st.common.StConstant;
import com.jackrain.nea.st.mapper.StCDistributionMapper;
import com.jackrain.nea.st.model.table.StCDistributionDO;
import com.jackrain.nea.st.utils.StBeanUtils;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.Date;
import java.util.List;

/**
 * @Descroption 分销代销策略延迟
 * <AUTHOR>
 * @Date 2019/3/12 22:55
 */
@Component
@Slf4j
public class DistributionDelayService {
    @Autowired
    private StCDistributionMapper mapper;
    @Autowired
    private DistributionUtilService distributionUtilService;

    /**
     * 延期设置
     *
     * @param obj  入参
     * @param user 用户
     * @return 结果
     */
    public ValueHolderV14 distributionDelay(JSONObject obj, User user) {
        ValueHolderV14 vh = new ValueHolderV14();

        if (null == obj) {
            vh.setCode(ResultCode.FAIL);
            vh.setMessage("请求参数不能为空！");
            return vh;
        }

        //获取ID
        JSONArray idsArray = obj.getJSONArray("ids");
        Date date = obj.getDate("delayDate");

        if (idsArray != null && idsArray.size() > 0) {
            List<StCDistributionDO> distributionDOList = mapper
                    .selectBatchIds(JSONObject.parseArray(idsArray.toJSONString(), Long.class));
            for (StCDistributionDO distributionDO : distributionDOList) {
                Long objId = distributionDO.getId();

                if (checkStCDistributionDO(vh, date, distributionDO)) {
                    return vh;
                }
                StBeanUtils.makeModifierField(distributionDO, user);
                if (mapper.updateById(distributionDO) < 0) {
                    vh.setCode(ResultCode.FAIL);
                    vh.setMessage("更新结束日期出错！");
                    return vh;
                }
                //将结束日期更新后的数据推送至ES
                try {
                    // distributionDO = mapper.selectById(objId);
                    ElasticSearchUtil.indexDocument(EsConstant.ST_C_DISTRIBUTION_INDEX, EsConstant.ST_C_DISTRIBUTION_TYPE, distributionDOList, objId);
                } catch (IOException e) {
                    throw new NDSException("分销代销[" + distributionDO.getBillNo() + "]延期数据推送ES失败！");
                }
            }
        } else {
            vh.setCode(ResultCode.FAIL);
            vh.setMessage("单据ID为空，传参失败！");
            return vh;
        }

        vh.setCode(ResultCode.SUCCESS);
        vh.setMessage("方案延期成功！");
        return vh;
    }

    private boolean checkStCDistributionDO(ValueHolderV14 vh, Date date, StCDistributionDO distributionDO) {
        if (!StConstant.CON_BILL_STATUS_02.equals(distributionDO.getBillStatus())) {
            vh.setCode(ResultCode.FAIL);
            vh.setMessage("当前记录不是已审核，不允许延期！");
            return true;
        }

        if (date != null) {
            if (date.before(new Date())) {
                vh.setCode(ResultCode.FAIL);
                vh.setMessage("结束时间不能早于当前时间！");
                return true;
            }
        } else {
            vh.setCode(ResultCode.FAIL);
            vh.setMessage("请选择延期结束日期！");
            return true;
        }

        Date beginDate = distributionDO.getBeginTime();
        if (beginDate != null) {
            if (date.before(beginDate)) {
                vh.setCode(ResultCode.FAIL);
                vh.setMessage("结束时间不能早于开始时间！");
                return true;
            }
        }
        Date endDate = distributionDO.getEndTime();
        if (endDate != null) {
            if (!date.after(endDate)) {
                vh.setCode(ResultCode.FAIL);
                vh.setMessage("结束时间不能早于原来结束时间！");
                return true;
            }
        }

        distributionDO.setEndTime(date);
        return false;
    }
}
