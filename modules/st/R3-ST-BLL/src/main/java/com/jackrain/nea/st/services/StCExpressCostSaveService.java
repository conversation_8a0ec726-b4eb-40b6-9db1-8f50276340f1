package com.jackrain.nea.st.services;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.st.annotation.StOperationLog;
import com.jackrain.nea.st.common.StConstant;
import com.jackrain.nea.st.mapper.StCExpressCostItemMapper;
import com.jackrain.nea.st.mapper.StCExpressCostMapper;
import com.jackrain.nea.st.model.enums.CloseStatusEnum;
import com.jackrain.nea.st.model.enums.SubmitStatusEnum;
import com.jackrain.nea.st.model.table.StCExpressCost;
import com.jackrain.nea.st.model.table.StCExpressCostItem;
import com.jackrain.nea.st.utils.JsonUtils;
import com.jackrain.nea.st.utils.StBeanUtils;
import com.jackrain.nea.st.utils.ValueHolderUtils;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2022/8/8 9:44
 * @Description
 */
@Component
@Slf4j
public class StCExpressCostSaveService extends CommandAdapter {

    @Autowired
    private StCExpressCostMapper stCExpressCostMapper;

    @Autowired
    private StCExpressCostItemMapper stCExpressCostItemMapper;


    /**
     * 新增和修改
     *
     * @param querySession 参数封装
     * @return 返回状态
     * @throws NDSException 异常信息
     */
    @StOperationLog(mainTableName = "ST_C_EXPRESS_COST", itemsTableName = "ST_C_EXPRESS_COST_ITEM")
    @Override
    public ValueHolder execute(QuerySession querySession) throws NDSException {
        ValueHolder valueHolder = new ValueHolder();
        DefaultWebEvent event = querySession.getEvent();
        List<String> nullKeyList = new ArrayList<>();
        if (log.isDebugEnabled()) {
            log.debug("Start StExpressCostService execute event:{}", JSONObject.toJSONString(event));
        }
        JSONObject param = JsonUtils.filterNull((JSONObject) event.getParameterValue("param"), nullKeyList);

        if (param != null) {
            Long id = param.getLong("objid");
            JSONObject fixColumn = param.getJSONObject("fixcolumn");
            if (fixColumn != null) {
                JSONObject mainJSONObject = fixColumn.getJSONObject(StConstant.TAB_ST_C_EXPRESS_COST);
                JSONArray itemJSONArray = fixColumn.getJSONArray(StConstant.TAB_ST_C_EXPRESS_COST_ITEM);
                if (id != null && id < 0) {
                    //新增
                    valueHolder = saveFunction(mainJSONObject, querySession);
                } else {
                    //编辑
                    valueHolder = updateFunction(mainJSONObject, itemJSONArray, querySession, id);
                }
            }
        } else {
            throw new NDSException("参数为空！");
        }
        return valueHolder;
    }


    /**
     * 新增操作
     *
     * @param mainJSONObject
     * @param querySession
     * @return
     */
    private ValueHolder saveFunction(JSONObject mainJSONObject, QuerySession querySession) {
        StCExpressCost stCExpressCost = JsonUtils.jsonParseClass(mainJSONObject, StCExpressCost.class);
        if (stCExpressCost != null) {
            stCExpressCost.setId(ModelUtil.getSequence(StConstant.TAB_ST_C_EXPRESS_COST));
            StBeanUtils.makeCreateField(stCExpressCost, querySession.getUser());
            reSetDateTimeRange(stCExpressCost);
            if (stCExpressCost.getEndDate().getTime() < stCExpressCost.getStartDate().getTime()) {
                return ValueHolderUtils.getFailValueHolder("结束日期不允许小于开始日期");
            }

            if (stCExpressCostMapper.insert(stCExpressCost) <= 0) {
                return ValueHolderUtils.getFailValueHolder("保存失败！");
            }
        } else {
            return ValueHolderUtils.getFailValueHolder("保存失败！");
        }
        return ValueHolderUtils.getSuccessValueHolder(stCExpressCost.getId(), StConstant.TAB_ST_C_EXPRESS_COST);
    }


    /**
     * 更新操作
     *
     * @param mainJSONObject
     * @param itemJSONArray
     * @param querySession
     * @param objid
     * @return
     */
    private ValueHolder updateFunction(JSONObject mainJSONObject, JSONArray itemJSONArray, QuerySession querySession, Long objid) {
        StCExpressCost oldMainData = stCExpressCostMapper.selectById(objid);
        if (oldMainData == null) {
            return ValueHolderUtils.getFailValueHolder("当前记录已存在，不允许编辑！");
        }
        if (CloseStatusEnum.CLOSE.getKey().equals(oldMainData.getCloseStatus())) {
            return ValueHolderUtils.getFailValueHolder("当前记录已结案，不允许编辑！");
        }
        if (SubmitStatusEnum.SUBMIT.getKey().equals(oldMainData.getStatus())) {
            return ValueHolderUtils.getFailValueHolder("当前记录已提交，不允许编辑！");
        }

        //主表更新，objid就是主表ID
        StCExpressCost stCExpressCost = new StCExpressCost();
        if (mainJSONObject != null && !mainJSONObject.isEmpty()) {
            stCExpressCost = JSON.parseObject(mainJSONObject.toJSONString(),
                    new TypeReference<StCExpressCost>() {
                    });
            //校验日期是否违法
            if (stCExpressCost.getStartDate() == null) {
                stCExpressCost.setStartDate(oldMainData.getStartDate());
            }
            if (stCExpressCost.getEndDate() == null) {
                stCExpressCost.setEndDate(oldMainData.getEndDate());
            }
            reSetDateTimeRange(stCExpressCost);
            if (stCExpressCost.getEndDate().getTime() < stCExpressCost.getStartDate().getTime()) {
                return ValueHolderUtils.getFailValueHolder("结束日期不允许小于开始日期");
            }
        }

        //判断子表数据是否存在
        if (itemJSONArray != null && !itemJSONArray.isEmpty()) {
            List<StCExpressCostItem> stCExpressCostItemList = convert(itemJSONArray);
            //检查行明细
            checkItem(stCExpressCostItemList, objid);
            for (StCExpressCostItem stCExpressCostItem : stCExpressCostItemList) {
                Long id = stCExpressCostItem.getId();
                if (id < 0) {
                    stCExpressCostItem.setId(ModelUtil.getSequence(StConstant.TAB_ST_C_EXPRESS_COST_ITEM));
                    stCExpressCostItem.setStCExpressCostId(objid);
                    StBeanUtils.makeCreateField(stCExpressCostItem, querySession.getUser());

                    if (stCExpressCostItemMapper.insert(stCExpressCostItem) <= 0) {
                        return ValueHolderUtils.getFailValueHolder("保存失败！");
                    }
                } else {
                    //修改原有的行信息 id>0
                    StBeanUtils.makeModifierField(stCExpressCostItem, querySession.getUser());
                    if (stCExpressCostItemMapper.updateById(stCExpressCostItem) <= 0) {
                        return ValueHolderUtils.getFailValueHolder("保存失败！");
                    }
                }
            }
            StCExpressCostItem stCExpressCostItem = new StCExpressCostItem();
            stCExpressCostItem.setId(objid);
            StBeanUtils.makeModifierField(stCExpressCostItem, querySession.getUser());
            stCExpressCostItemMapper.updateById(stCExpressCostItem);
        }
        if (stCExpressCost.getRemark() == null && (mainJSONObject == null || !mainJSONObject.containsKey("REMARK"))) {
            stCExpressCost.setRemark(oldMainData.getRemark());
        }
        stCExpressCost.setId(objid);
        StBeanUtils.makeModifierField(stCExpressCost, querySession.getUser());
        if (stCExpressCostMapper.updateById(stCExpressCost) < 0) {
            return ValueHolderUtils.getFailValueHolder("保存失败！");
        }
        return ValueHolderUtils.getSuccessValueHolder(objid, StConstant.TAB_ST_C_EXPRESS_COST);
    }

    private List<StCExpressCostItem> convert(JSONArray itemJsonArray) {
        if (itemJsonArray == null || itemJsonArray.isEmpty()) {
            return new ArrayList<>();
        }
        List<StCExpressCostItem> itemDOList = new ArrayList<>();
        for (int i = 0; i < itemJsonArray.size(); i++) {
            JSONObject itemJsonObject = itemJsonArray.getJSONObject(i);
            StCExpressCostItem itemDO = JSON.parseObject(itemJsonObject.toJSONString(),
                    new TypeReference<StCExpressCostItem>() {
                    });
            itemDOList.add(itemDO);
            Long itemId = itemJsonObject.getLong("ID");
            if (itemId != null && itemId > 0) {
                StCExpressCostItem oldItem = stCExpressCostItemMapper.selectById(itemDO.getId());
                if (oldItem == null) {
                    throw new NDSException("修改的明细数据不存在或已删除！");
                }
                if (itemDO.getProvinceId() == null) {
                    itemDO.setProvinceId(oldItem.getProvinceId());
                }
                if (itemDO.getCityId() == null && !itemJsonObject.containsKey("CITY_ID")) {
                    itemDO.setCityId(oldItem.getCityId());
                }
                if (itemDO.getStartWeight() == null) {
                    itemDO.setStartWeight(oldItem.getStartWeight());
                }
                if (itemDO.getEndWeight() == null) {
                    itemDO.setEndWeight(oldItem.getEndWeight());
                }
                if (itemDO.getPriceExpress() == null && !itemJsonObject.containsKey("PRICE_EXPRESS")) {
                    itemDO.setPriceExpress(oldItem.getPriceExpress());
                }
                if (itemDO.getPriceFirstWeight() == null ) {
                    itemDO.setPriceFirstWeight(oldItem.getPriceFirstWeight());
                }

            }
        }
        return itemDOList;
    }


    /**
     * 明细前置条件保存校验
     *
     * @param itemDOList
     * @return
     */
    private void checkItem(List<StCExpressCostItem> itemDOList, Long objid) {
        for (StCExpressCostItem itemDO : itemDOList) {
            if (itemDO.getEndWeight().compareTo(itemDO.getStartWeight()) <= 0) {
                throw new NDSException("结束重量必须大于起始重量！");
            }
        }

        //将子表按照省份分组，先验证集合内的交叉情况
        Map<Long, List<StCExpressCostItem>> listMapByProvince = itemDOList.stream().collect(Collectors.groupingBy(StCExpressCostItem::getProvinceId));
        for (Long provinceId : listMapByProvince.keySet()) {
            List<StCExpressCostItem> expressCostItemList = listMapByProvince.get(provinceId);
            if (expressCostItemList != null && expressCostItemList.size() > 1) {
                for (int i = 0; i < expressCostItemList.size(); i++) {
                    for (int j = i + 1; j < expressCostItemList.size(); j++) {
                        StCExpressCostItem one = expressCostItemList.get(i);
                        StCExpressCostItem two = expressCostItemList.get(j);
                        // 校验相同省份和城市的重量不能有交集
                        if ((one.getCityId() == null && two.getCityId() == null) ||
                                (one.getCityId() != null && one.getCityId().equals(two.getCityId()))) {
                            Boolean flag = checkWeight(one.getStartWeight(), one.getEndWeight(), two.getStartWeight(), two.getEndWeight());
                            if (!flag) {
                                throw new NDSException("录入的明细已存在，不允许重复录入！");
                            }
                        }
                    }
                }
            }
        }

        //与库内数据校验
        for (StCExpressCostItem itemDO : itemDOList) {
            LambdaQueryWrapper<StCExpressCostItem> queryWrapper = new LambdaQueryWrapper<StCExpressCostItem>()
                    .eq(StCExpressCostItem::getStCExpressCostId, objid)
                    .eq(StCExpressCostItem::getProvinceId, itemDO.getProvinceId())
                    .ne(StCExpressCostItem::getId, itemDO.getId());

            // 如果当前明细有城市，则需要校验相同省份&相同城市&重量存在交集不允许保存
            if (itemDO.getCityId() != null) {
                queryWrapper.eq(StCExpressCostItem::getCityId, itemDO.getCityId());
            } else {
                // 如果当前明细没有城市，则按原有逻辑校验相同省份&重量存在交集不允许保存（只查询城市为空的记录）
                queryWrapper.isNull(StCExpressCostItem::getCityId);
            }

            List<StCExpressCostItem> selectList = stCExpressCostItemMapper.selectList(queryWrapper);
            if (CollectionUtils.isNotEmpty(selectList)) {
                for (StCExpressCostItem costItemDO : selectList) {
                    Boolean flag = checkWeight(itemDO.getStartWeight(), itemDO.getEndWeight(), costItemDO.getStartWeight(), costItemDO.getEndWeight());
                    if (!flag) {
                        throw new NDSException("录入的明细已存在，不允许重复录入！");
                    }
                }
            }
        }
    }

    /**
     * 验证重量是否存在交叉
     *
     * @param startWeight
     * @param endWeight
     * @param startWeight1
     * @param endWeight1
     * @return
     */
    private Boolean checkWeight(BigDecimal startWeight, BigDecimal endWeight, BigDecimal startWeight1, BigDecimal endWeight1) {
        if (endWeight.compareTo(endWeight1) == 0) {
            return false;
        } else {
            if (endWeight.compareTo(endWeight1) == 1) {
                //endWeight大，当startWeight小于endWeight1时，返回false
                if (startWeight.compareTo(endWeight1) == -1) {
                    return false;
                }
            } else {
                //endWeight1大，当startWeight1小于endWeight时，返回false
                if (startWeight1.compareTo(endWeight) == -1) {
                    return false;
                }
            }
        }
        return true;
    }

    /**
     * 开始日期取当天0点，结束日期取当天23:59:59
     *
     * @param mainObj 主对象
     */
    private void reSetDateTimeRange(StCExpressCost mainObj) {
        if (Objects.isNull(mainObj)) {
            return;
        }
        if (Objects.nonNull(mainObj.getStartDate())) {
            mainObj.setStartDate(DateUtil.beginOfDay(mainObj.getStartDate()));
        }
        if (Objects.nonNull(mainObj.getEndDate())) {
            /*mainObj.setEndDate(DateUtil.endOfDay(mainObj.getEndDate()));*/
            DateTime dateTime = DateUtil.endOfDay(mainObj.getEndDate());
            dateTime.setTime(dateTime.getTime() - 999);
            mainObj.setEndDate(dateTime);
        }
    }
}
