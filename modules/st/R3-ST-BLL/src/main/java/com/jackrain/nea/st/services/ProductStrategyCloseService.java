package com.jackrain.nea.st.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.google.common.base.Throwables;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.st.common.StConstant;
import com.jackrain.nea.st.mapper.StCProductStrategyItemMapper;
import com.jackrain.nea.st.mapper.StCProductStrategyMapper;
import com.jackrain.nea.st.model.table.StCProductStrategyDO;
import com.jackrain.nea.st.model.table.StCProductStrategyItemDO;
import com.jackrain.nea.st.utils.DatasToEsUtils;
import com.jackrain.nea.st.utils.ListUtils;
import com.jackrain.nea.st.utils.StBeanUtils;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.HashMap;
import java.util.List;

@Component
@Slf4j
@Transactional
public class ProductStrategyCloseService extends CommandAdapter {

    @Autowired
    private StCProductStrategyMapper stCProductStrategyMapper;

    @Autowired
    private StCProductStrategyItemMapper stCProductStrategyItemMapper;

//    @Autowired
//    private ShopUniversalStockService shopUniversalStockService;

    /**
     * <AUTHOR>
     * @Description 结案操作
     * @Date  2019/3/25
     * @Param [session]
     * @return com.jackrain.nea.util.ValueHolder
    **/
    public ValueHolder closeProductStrategy(QuerySession session) {
        DefaultWebEvent event = session.getEvent();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(
                event.getParameterValue("param"), "yyyy-MM-dd HH:mm:ss", SerializerFeature.WriteMapNullValue),
                Feature.OrderedField);
        log.debug(LogUtil.format("请求JSON：") + param.toString());
        ValueHolder resultValueHolder;
        HashMap<Long, Object> errMap = new HashMap<>(16);
        //生成结案Json数组
        JSONArray auditArray = StBeanUtils.makeFinishJsonArray(param);
        for (Object o : auditArray) {
            Long id = Long.valueOf(o.toString());
            try {
                //结案验证
                checkAudit(id);

                // 主表信息
                User user = session.getUser();
                StCProductStrategyDO stCProductStrategyDO = new StCProductStrategyDO();
                stCProductStrategyDO.setId(id);
                stCProductStrategyDO.setEstatus(StConstant.CON_BILL_STATUS_04);
                stCProductStrategyDO.setFinishid(Long.valueOf(user.getId()));
                stCProductStrategyDO.setFinishename(user.getEname());
                stCProductStrategyDO.setFinishname(user.getName());
                stCProductStrategyDO.setFinishtime(new Date());
                makeCheckerField(stCProductStrategyDO, session.getUser());
                //更新单据状态
                int count = stCProductStrategyMapper.updateById(stCProductStrategyDO);
                if (count < 0) {
                    log.debug(LogUtil.format("更新结案信息失败！", id));
                    throw new Exception();
                } else {
//                    ValueHolder holder = shopUniversalStockService.deleteSgBSyncChannelStock(StConstant.PRODUCT_STRATEGY_TYPE.longValue(), id);
//                    log.debug(LogUtil.format("商品特殊 结案 删除同步库存中间表数据 策略id：" + id + " 结果：" + JSONObject.toJSONString(holder));
                    //更新明细表状态，并主表和明细表都推送ES数据
                    try {
                        //做更新的需要先查询更新后数据库的实体在推ES
                        stCProductStrategyDO = stCProductStrategyMapper.selectById(id);
                        StCProductStrategyItemDO item = new StCProductStrategyItemDO();
                        item.setStatus(StConstant.CON_BILL_STATUS_04);
                        QueryWrapper<StCProductStrategyItemDO> wrapper = new QueryWrapper<>();
                        wrapper.eq("st_c_product_strategy_id", id);
                        stCProductStrategyItemMapper.update(item, wrapper);
                        List<StCProductStrategyItemDO> itemList = ListUtils.batchQueryByCondition(wrapper, stCProductStrategyItemMapper, 1000);
                        DatasToEsUtils.insertProductEsData(stCProductStrategyDO, null, StConstant.TAB_ST_C_PRODUCT_STRATEGY);
                        if (CollectionUtils.isNotEmpty(itemList)) {
                            DatasToEsUtils.insertProductEsData(stCProductStrategyDO, itemList, StConstant.TAB_ST_C_PRODUCT_STRATEGY_ITEM);
                        }
                    } catch (Exception ex) {
                        log.debug(StConstant.TAB_ST_C_PRODUCT_STRATEGY + "店铺商品特殊设置主表更新结案信息数据至ES失败：" + ex.toString());
                    }
                }
            } catch (Exception e) {
                errMap.put(id, e.getMessage());
            }
        }
        resultValueHolder = StBeanUtils.getExcuteValueHolder(auditArray.size(),errMap);
        return resultValueHolder;
    }

    /**
     * <AUTHOR>
     * @Description 批量结案店铺商品特殊设置
     * @Date 13:56 2020/12/18
     * @param ids
     * @param user
     * @return com.jackrain.nea.sys.domain.ValueHolderV14
     **/
    @Transactional(rollbackFor = Exception.class)
    public ValueHolderV14 batchCloseProductStrategy(List<Long> ids, User user) {
        ValueHolderV14 valueHolder = new ValueHolderV14();
        if (CollectionUtils.isEmpty(ids)) {
            valueHolder.setCode(ResultCode.FAIL);
            valueHolder.setMessage("传入结案参数列表为空");
            return valueHolder;
        }
        try{
            /**1. 批量修改主表数据*/
            StCProductStrategyDO main = new StCProductStrategyDO();
            main.setEstatus(StConstant.CON_BILL_STATUS_04);
            main.setFinishid(Long.valueOf(user.getId()));
            main.setFinishename(user.getEname());
            main.setFinishname(user.getName());
            main.setFinishtime(new Date());
            StBeanUtils.makeModifierField(main,user);
            QueryWrapper<StCProductStrategyDO> wrapper = new QueryWrapper<>();
            wrapper.in("id", ids);
            stCProductStrategyMapper.update(main, wrapper);

            /**2. 批量修改明细数据*/
            StCProductStrategyItemDO item = new StCProductStrategyItemDO();
            item.setStatus(StConstant.CON_BILL_STATUS_04);
            StBeanUtils.makeModifierField(item,user);
            QueryWrapper<StCProductStrategyItemDO> itemWrapper = new QueryWrapper<>();
            itemWrapper.in("st_c_product_strategy_id", ids);
            stCProductStrategyItemMapper.update(item, itemWrapper);

            /**3. 批量更新es*/
            List<StCProductStrategyDO> productStrategyDOS = stCProductStrategyMapper.selectList(wrapper);
            for (StCProductStrategyDO productStrategyDO : productStrategyDOS) {
                DatasToEsUtils.insertProductEsData(productStrategyDO, null, StConstant.TAB_ST_C_PRODUCT_STRATEGY);

                QueryWrapper<StCProductStrategyItemDO> esWrapper = new QueryWrapper<>();
                esWrapper.eq("st_c_product_strategy_id", productStrategyDO.getId());
                List<StCProductStrategyItemDO> itemList = ListUtils.batchQueryByCondition(esWrapper, stCProductStrategyItemMapper, 1000);
                if (CollectionUtils.isNotEmpty(itemList)) {
                    DatasToEsUtils.insertProductEsData(productStrategyDO, itemList, StConstant.TAB_ST_C_PRODUCT_STRATEGY_ITEM);
                }
            }
            valueHolder.setCode(ResultCode.SUCCESS);
        } catch (Exception e) {
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("【执行批量店铺商品特殊设置结案异常】{}"), Throwables.getStackTraceAsString(e));
            }
            valueHolder.setCode(ResultCode.FAIL);
            valueHolder.setMessage(e.getMessage());
        }
        return valueHolder;
    }

    /**
     * <AUTHOR>
     * @Description 结案验证
     * @Date  2019/3/25
     * @Param [id]
     **/
    private void checkAudit(Long id) throws Exception {
        //记录不存在
        StCProductStrategyDO stCProductStrategyDO = stCProductStrategyMapper.selectById(id);
        if (stCProductStrategyDO == null) {
            JSONObject errJo = new JSONObject();
            errJo.put("objid", id);
            errJo.put("message", "当前记录已不存在！");
            log.debug(LogUtil.format("id当前记录已不存在！", id));
            throw new Exception("当前记录已不存在！");
        }

        //状态为非已审核
        StCProductStrategyDO stCProductStrategyDO1 = stCProductStrategyMapper.selectByIdAndStatus(id, StConstant.CON_BILL_STATUS_02);
        if (stCProductStrategyDO1 == null) {
            JSONObject errJo = new JSONObject();
            errJo.put("objid", id);
            errJo.put("message", "方案状态不是已审核，不允许结案！");
            log.debug(LogUtil.format("方案状态不是已审核，不允许结案！", id));
            throw new Exception("方案状态不是已审核，不允许结案！");
        }

    }
    /**
     * <AUTHOR>
     * @Description 结案人基础信息封装
     * @Date  2019/3/25
     * @Param [stCProductStrategyDO, user]
     * @return void
     **/
    private void makeCheckerField(StCProductStrategyDO stCProductStrategyDO, User user) {

        stCProductStrategyDO.setFinishid(Long.valueOf(user.getId()));//结案人ID
        stCProductStrategyDO.setFinishname(user.getName());//结案人姓名
        stCProductStrategyDO.setFinishename(user.getEname());//结案人工号
        stCProductStrategyDO.setFinishtime(new Date());//结案时间
    }

}
