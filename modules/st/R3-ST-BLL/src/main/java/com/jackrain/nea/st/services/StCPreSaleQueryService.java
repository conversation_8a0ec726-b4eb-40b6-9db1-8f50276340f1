package com.jackrain.nea.st.services;

import com.jackrain.nea.st.common.StConstant;
import com.jackrain.nea.st.mapper.StCPreSaleItemMapper;
import com.jackrain.nea.st.mapper.StCPreSaleMapper;
import com.jackrain.nea.st.model.result.StCPreSaleResult;
import com.jackrain.nea.st.model.table.StCPreSaleDO;
import com.jackrain.nea.st.model.table.StCPreSaleItemDO;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Hashtable;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description 预到货策略查询
 * @Date 2020/06/10
 * @Copyright 2019-2020
 */
@Component
@Slf4j
@Transactional
public class StCPreSaleQueryService extends CommandAdapter {

    @Autowired
    private StCPreSaleMapper stCPreSaleMapper;

    @Autowired
    private StCPreSaleItemMapper stCPreSaleItemMapper;

    /**
     * @return com.jackrain.nea.util.ValueHolderV14
     * <AUTHOR>
     * @Description 预售解析策略查询
     * @Date  2020/06/14
     **/
    public ValueHolderV14<List<StCPreSaleResult>> queryStCPreSale(Long shopId, Integer preSaleWay){

        ValueHolderV14<List<StCPreSaleResult>> result = new ValueHolderV14<>();
        List<StCPreSaleResult> results = new ArrayList<>();
        List<StCPreSaleDO> list = stCPreSaleMapper.selectByShopIdAndPreSaleWay(shopId, preSaleWay);
        if(!CollectionUtils.isEmpty(list)){
            for(StCPreSaleDO dto : list){
                StCPreSaleResult stCPreSaleResult = new StCPreSaleResult();
                List<StCPreSaleItemDO> items = stCPreSaleItemMapper.selectByPreSaleIdAndPreSaleWay(dto.getId(),preSaleWay);
                stCPreSaleResult.setStCPreSaleDO(dto);
                stCPreSaleResult.setItemDOList(items);
                results.add(stCPreSaleResult);
            }
        }
        result.setData(results);
        return result;
    }

}
