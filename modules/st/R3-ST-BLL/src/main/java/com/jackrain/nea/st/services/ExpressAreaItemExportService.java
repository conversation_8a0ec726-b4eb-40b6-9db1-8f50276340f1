package com.jackrain.nea.st.services;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.st.config.R3OssConfig;
import com.jackrain.nea.st.model.result.ExpressAreaItemResult;
import com.jackrain.nea.st.model.result.ExpressAreaItemTableResult;
import com.jackrain.nea.st.utils.ExportUtil;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @author:huang.<PERSON><PERSON><PERSON>
 * @since: 2019/8/9
 * @create at : 2019/8/9 22:37
 */
@Component
@Slf4j
public class ExpressAreaItemExportService extends CommandAdapter {
    @Autowired
    private ExportUtil exportUtil;
    @Autowired
    private ExpressAreaQueryService queryService;
    @Autowired
    private R3OssConfig r3OssConfig;

    public ValueHolderV14 exportExpressAreaItem(JSONObject obj, User user){
        ValueHolderV14 vh = new ValueHolderV14(ResultCode.SUCCESS, "物流区域明细导出成功！");
        List<ExpressAreaItemResult> mainExcelList = Lists.newLinkedList();

        ValueHolderV14<ExpressAreaItemTableResult> tableVh = queryService.queryExpressAreaItemTable(obj);
        if (tableVh.isOK()) {
            mainExcelList = tableVh.getData().getItemResultList();
            for (ExpressAreaItemResult item : mainExcelList) {
                item.setIsArrive("Y".equals(item.getIsArrive()) ? "是" : "否");
            }
        }
        exportUtil.setEndpoint(r3OssConfig.getEndpoint());
        exportUtil.setAccessKeyId(r3OssConfig.getAccessKeyId());
        exportUtil.setAccessKeySecret(r3OssConfig.getAccessKeySecret());
        exportUtil.setBucketName(r3OssConfig.getBucketName());
        if(StringUtils.isEmpty(r3OssConfig.getTimeout())){
            //如果获取不到apllo配置参数，设置默认过期时间为30分钟
            exportUtil.setTimeout("1800000");
        } else {
            exportUtil.setTimeout(r3OssConfig.getTimeout());
        }
        /**
         *  拼接Excel主表sheet表头字段和列表
         * */
        String mainNames[] = {"省", "市", "区", "排除区域", "是否到达"};
        String orderKeys[] = {"cpCRegionProvinceEname", "cpCRegionCityEname", "cpCRegionAreaEname", "exclusionArea", "isArrive"};
        List<String> mainName = Lists.newArrayList(mainNames);
        List<String> mainKey = Lists.newArrayList(orderKeys);
        XSSFWorkbook hssfWorkbook = new XSSFWorkbook();
        exportUtil.executeSheet(hssfWorkbook, "物流区域明细", "", mainName,
                Lists.newArrayList(), mainKey, mainExcelList, false);
        String putMsg = exportUtil.saveFileAndPutOss(hssfWorkbook, "物流区域明细导出",
                user, "OSS-Bucket/EXPORT/StCExpressAreaItem/");
        if(StringUtils.isEmpty(putMsg)){
            vh.setCode(ResultCode.FAIL);
            vh.setMessage("物流区域明细导出失败");
        }
        vh.setData(putMsg);
        return vh;
    }
}
