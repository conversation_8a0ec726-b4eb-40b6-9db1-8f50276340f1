package com.jackrain.nea.st.services;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.cpext.model.table.CpCStore;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.st.common.StConstant;
import com.jackrain.nea.st.mapper.StCChannelStrategyItemMapper;
import com.jackrain.nea.st.mapper.StCChannelStrategyMapper;
import com.jackrain.nea.st.model.table.*;
import com.jackrain.nea.st.rpc.RpcCpService;
import com.jackrain.nea.st.utils.RedisCacheUtil;
import com.jackrain.nea.st.utils.RedisConstant;
import com.jackrain.nea.st.utils.StBeanUtils;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.assertj.core.util.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * @author: huang.zaizai
 * @since: 2020-04-21
 * @create at : 2020-04-21 19:52
 */
@Slf4j
@Component
@Deprecated
public class ChannelStrategyBatchService {

    @Autowired
    private StCChannelStrategyMapper channelStrategyMapper;
    @Autowired
    private StCChannelStrategyItemMapper channelStrategyItemMapper;
    @Autowired
    private RpcCpService rpcCpService;
    @Autowired
    private ChannelStrategyDelService delService;
    @Autowired
    private ChannelStrategySaveService saveService;

    /**
     * 批量删除明细
     *
     * @param object   {"CP_C_STORE_ID":"55,263","IDS":["15"]}
     * @param user 当前登录的用户
     * @return
     * @throws NDSException
     */
    public ValueHolderV14 channelStrategyBatchChangeFun(JSONObject object, User user) throws NDSException {
        ValueHolderV14 valueHolderV14 = new ValueHolderV14();
        valueHolderV14.setCode(ResultCode.SUCCESS);
        valueHolderV14.setMessage("批量处理成功！");

        // 当IDS为空则为所有的数据，否则为勾选的【渠道库存策略】的列表ID
        JSONArray ids = object.getJSONArray("IDS");
        // CP_C_STORE_ID,弹窗为所选中的供货渠道id
        String storeIds = object.getString("CP_C_STORE_ID");

        List<Long> idList = Lists.newArrayList();
        if (ids != null && ids.size() > 0) {
            for (int i = 0; i < ids.size(); i++) {
                idList.add(ids.getLong(i));
            }
        }
        List<Long> storeIdList = Lists.newArrayList();
        if (storeIds != null) {
            String[] storeIdStr = storeIds.split(",");
            for (String storeId : storeIdStr) {
                storeIdList.add(Long.valueOf(storeId));
            }
        }

        List<StCChannelStrategyItemDO> items = channelStrategyItemMapper.selectItemByStoreAndId(storeIdList, idList);

        try {
            switch (object.getString("TYPE").toUpperCase()) {
                case "ADD":
                    // 新增
                    if (CollectionUtils.isEmpty(items)) {
                        batchChangeForAdd(object, user, idList, storeIdList);
                    } else {
                        StCChannelStrategyItemDO item = items.get(0);
                        StCChannelStrategyDO channelStrategy = channelStrategyMapper.selectById(item.getStCChannelStrategyId());
                        throw new NDSException("供货仓" + item.getCpCStoreEname() + "在渠道策略" + channelStrategy.getCpCOrgChannelEname() + "已存在，不允许新增！");
                    }
                    break;
                case "UPD":
                    // 更新
                    if (!CollectionUtils.isEmpty(items)) {
                        batchChangeForUpd(object, user, items);
                    } else {
                        throw new NDSException("渠道策略不存在选中的供货仓！");
                    }
                    break;
                case "DEL":
                    // 删除
                    if (!CollectionUtils.isEmpty(items)) {
                    } else {
                        throw new NDSException("渠道策略不存在选中的供货仓！");
                    }
                    break;
                default:
                    break;
            }
        } catch (Exception ex) {
            valueHolderV14.setCode(ResultCode.FAIL);
            valueHolderV14.setMessage(ex.getMessage());
        }
        return valueHolderV14;
    }



    private void batchChangeForUpd(JSONObject object, User user, List<StCChannelStrategyItemDO> items) {
        for (StCChannelStrategyItemDO item : items) {
            //更新明细表数据
            StCChannelStrategyItemDO itemNew = new StCChannelStrategyItemDO();
            if (StringUtils.isNotBlank(object.getString("PRIORITY"))) {
                itemNew.setPriority(object.getInteger("PRIORITY"));
            }
            if (StringUtils.isNotBlank(object.getString("RATE"))) {
                itemNew.setRate(object.getBigDecimal("RATE"));
            }
            if (StringUtils.isNotBlank(object.getString("LOW_STOCK"))) {
                itemNew.setLowStock(object.getBigDecimal("LOW_STOCK"));
            }
            if (StringUtils.isNotBlank(object.getString("IS_SEND"))) {
                itemNew.setIsSend(object.getInteger("IS_SEND"));
            }
            itemNew.setId(item.getId());
            StBeanUtils.makeModifierField(itemNew, user);

            if (itemNew.getRate() != null && itemNew.getRate().compareTo(new BigDecimal("100")) > 0) {
                throw new NDSException("单个库存比例不能大于100%!");
            }
            channelStrategyItemMapper.updateById(itemNew);

        }
    }

    private void batchChangeForAdd(JSONObject object, User user, List<Long> idList, List<Long> storeIdList) {
        for (Long storeId : storeIdList) {
            //查出逻辑仓的ecode ename
            List<Integer> storeIdSelectList = new ArrayList<>();
            storeIdSelectList.add(storeId.intValue());
            List<CpCStore> storeList = rpcCpService.queryStoreInfoByIds(storeIdSelectList);
            if (!CollectionUtils.isEmpty(storeList)) {
                CpCStore cpCStore = storeList.get(0);
                for (Long id : idList) {
                    StCChannelStrategyItemDO itemNew = new StCChannelStrategyItemDO();

                    itemNew.setId(ModelUtil.getSequence(StConstant.TAB_ST_C_CHANNEL_STRATEGY_ITEM));
                    itemNew.setStCChannelStrategyId(id);
                    itemNew.setPriority(StringUtils.isNotBlank(object.getString("PRIORITY")) ? object.getInteger("PRIORITY") : 1);
                    itemNew.setRate(StringUtils.isNotBlank(object.getString("RATE")) ? object.getBigDecimal("RATE") : BigDecimal.ZERO);
                    itemNew.setLowStock(StringUtils.isNotBlank(object.getString("LOW_STOCK")) ? object.getBigDecimal("LOW_STOCK") : BigDecimal.ZERO);
                    itemNew.setIsSend(StringUtils.isNotBlank(object.getString("IS_SEND")) ? object.getInteger("IS_SEND") : 1);

                    StBeanUtils.makeCreateField(itemNew, user);
                    itemNew.setCpCStoreId(cpCStore.getId());
                    itemNew.setCpCStoreEcode(cpCStore.getEcode());
                    itemNew.setCpCStoreEname(cpCStore.getEname());

                    if (itemNew.getRate() != null && itemNew.getRate().compareTo(new BigDecimal("100")) > 0) {
                        throw new NDSException("单个库存比例不能大于100%!");
                    }
                    channelStrategyItemMapper.insert(itemNew);

                }
            } else {
                throw new NDSException("调用获取店仓RPC接口失败");
            }
        }
    }
}
