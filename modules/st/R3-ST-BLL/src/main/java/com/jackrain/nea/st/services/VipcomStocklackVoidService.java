package com.jackrain.nea.st.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.st.common.StConstant;
import com.jackrain.nea.st.mapper.StCVipcomStocklackMapper;
import com.jackrain.nea.st.model.table.StCVipcomStocklackDO;
import com.jackrain.nea.st.utils.StBeanUtils;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;

/**
 * @Descroption 唯品会缺货策略-作废逻辑
 * <AUTHOR>
 * @Date 2019/3/11 21:36
 */

@Component
@Slf4j
@Transactional
public class VipcomStocklackVoidService extends CommandAdapter {
    @Autowired
    private StCVipcomStocklackMapper stCVipcomStocklackMapper;

    /**
     * @Descroption
     * @Author: 郑小龙
     * @Date 2019/3/11
     */
    @Override
    public ValueHolder execute(QuerySession querySession) throws NDSException {
        DefaultWebEvent event = querySession.getEvent();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(
                event.getParameterValue("param"), "yyyy-MM-dd HH:mm:ss"),
                Feature.OrderedField);

        JSONArray itemArray = StBeanUtils.makeVoidJsonArray(param);
        JSONArray errorArray = new JSONArray();
        if (itemArray.size() > 0) {
            for (int i = 0; i < itemArray.size(); i++) {
                Long itemid = itemArray.getLong(i);
                saveVipcomStocklackByID(querySession, itemid, errorArray);
            }
        }
        return StBeanUtils.getProcessValueHolder(itemArray, errorArray, StConstant.CON_BILL_ACTION_VOID);
    }

    /**
     * @Descroption 作废保存
     * @Author: 郑小龙
     * @Date 2019/3/11
     */
    private void saveVipcomStocklackByID(QuerySession session, Long id, JSONArray errorArray) {
        StCVipcomStocklackDO stCVipcomStocklackDO = stCVipcomStocklackMapper.selectById(id);
        JSONArray tempArray = new JSONArray();
        checkVipcomStocklackStatus(stCVipcomStocklackDO, id, tempArray);
        if (tempArray.size() > 0) {
            errorArray.addAll(tempArray);
            return;
        }
        StBeanUtils.makeModifierField(stCVipcomStocklackDO, session.getUser());//修改信息
        stCVipcomStocklackDO.setIsactive(StConstant.ISACTIVE_N);//作废状态
        stCVipcomStocklackDO.setDelid(Long.valueOf(session.getUser().getId()));//作废人
        stCVipcomStocklackDO.setDelTime(new Date());//作废时间
        stCVipcomStocklackDO.setDelname(session.getUser().getName());//作废人姓名
        stCVipcomStocklackDO.setDelename(session.getUser().getEname());//作废人账号
        stCVipcomStocklackDO.setModifierename(session.getUser().getEname());//修改人账号
        if ((stCVipcomStocklackMapper.updateById(stCVipcomStocklackDO)) <= 0) {
            errorArray.add(StBeanUtils.getJsonObjectInfo(id, "POID:" + stCVipcomStocklackDO.getPoId() + ",作废失败！"));
        }
    }

    /**
     * @Descroption 作废判断
     * @Author: 郑小龙
     * @Date 2019/3/11
     */
    private void checkVipcomStocklackStatus(StCVipcomStocklackDO stCVipcomStocklackDO, Long id, JSONArray errorArray) {
        if (stCVipcomStocklackDO == null) {
            errorArray.add(StBeanUtils.getJsonObjectInfo(id, "当前记录已不存在!"));
            return;
        }
        //不是未作废，不允许作废
        if (stCVipcomStocklackDO.getIsactive().equals(StConstant.ISACTIVE_N)) {
            errorArray.add(StBeanUtils.getJsonObjectInfo(id, "当前记录已作废，不允许作废!"));
            return;
        }
    }
}
