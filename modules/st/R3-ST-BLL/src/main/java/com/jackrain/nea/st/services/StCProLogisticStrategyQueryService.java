package com.jackrain.nea.st.services;

import com.jackrain.nea.st.mapper.StCProLogisticStrategyMapper;
import com.jackrain.nea.st.model.request.StCProLogisticStrategyQueryRequest;
import com.jackrain.nea.st.model.table.StCProLogisticStrategy;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.sys.constants.ResultCode;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * Created with IntelliJ IDEA.
 *
 * @Author: jg.zhan
 * @Date: 2022/6/21 14:47
 * @Description: 商品物流设置
 */
@Component
@Slf4j
public class StCProLogisticStrategyQueryService extends CommandAdapter {

    @Autowired
    private StCProLogisticStrategyMapper mapper;

    public ValueHolderV14<List<StCProLogisticStrategy>> queryStCProLogisticStrategy(StCProLogisticStrategyQueryRequest request) {

        if (request == null || CollectionUtils.isEmpty(request.getPsCProdimIdList()) ||
                CollectionUtils.isEmpty(request.getPsCProIdList()) || request.getProvinceId() == null || request.getWarehouseId() == null) {
            return new ValueHolderV14<>(ResultCode.FAIL, "请求参数不能为空");
        }

        List<StCProLogisticStrategy> result = mapper.queryStCProLogisticStrategy(request);
        return new ValueHolderV14<>(result, ResultCode.SUCCESS, "SUCCESS");
    }

}
