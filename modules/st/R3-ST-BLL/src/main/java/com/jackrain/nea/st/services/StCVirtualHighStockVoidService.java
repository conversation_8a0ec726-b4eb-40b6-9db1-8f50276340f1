package com.jackrain.nea.st.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.google.common.base.Throwables;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.st.common.StConstant;
import com.jackrain.nea.st.mapper.StCVirtualHighStockItemMapper;
import com.jackrain.nea.st.mapper.StCVirtualHighStockMapper;
import com.jackrain.nea.st.model.table.StCShopVirtualHighStockDO;
import com.jackrain.nea.st.model.table.StCShopVirtualHighStockItemDO;
import com.jackrain.nea.st.utils.StBeanUtils;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;

/**
 * <AUTHOR> ShiLong
 * @Date: 2020/6/22 10:08 下午
 * @Desc: 商铺虚高库存作废
 */

@Component
@Slf4j
public class StCVirtualHighStockVoidService extends CommandAdapter {

    @Autowired
    private StCVirtualHighStockMapper stCVirtualHighStockMapper;

    @Autowired
    private StCVirtualHighStockItemMapper stCVirtualHighStockItemMapper;

    @Override
    public ValueHolder execute(QuerySession querySession) throws NDSException {
        DefaultWebEvent event = querySession.getEvent();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(
                event.getParameterValue("param"), "yyyy-MM-dd HH:mm:ss"),
                Feature.OrderedField);
        JSONArray itemArray = StBeanUtils.makeVoidJsonArray(param);
        JSONArray errorArray = new JSONArray();
        if (itemArray.size() > 0) {
            StCVirtualHighStockVoidService bean =
                    ApplicationContextHandle.getBean(StCVirtualHighStockVoidService.class);
            for (int i = 0; i < itemArray.size(); i++) {
                Long itemId = itemArray.getLong(i);
                bean.saveAutocheckByID(querySession, itemId, errorArray);
            }
        }
        return StBeanUtils.getProcessValueHolder(itemArray, errorArray, StConstant.TAB_ST_C_SHOP_VIRTUAL_HIGH_STOCK);
    }


    @Transactional(rollbackFor = Exception.class)
    public void saveAutocheckByID(QuerySession session, Long id, JSONArray errorArray) {
        StCShopVirtualHighStockDO reuslt = stCVirtualHighStockMapper.selectById(id);
        JSONArray tempArray = new JSONArray();
        checkAutocheckStatus(reuslt, id, tempArray);
        if (tempArray.size() > 0) {
            errorArray.addAll(tempArray);
            return;
        }

        // 修改主表
        int upateResult = updateVirtualHighStock(session, reuslt);
        
        if (upateResult <= 0) {
            errorArray.add(StBeanUtils.getJsonObjectInfo(id, "方案ID:" + reuslt.getPlan() + ",作废失败！"));
        } else {
            //推送ES数据
            try {
                //做更新的需要先查询更新后数据库的实体在推ES
                StCShopVirtualHighStockItemDO item = new StCShopVirtualHighStockItemDO();
                item.setStatus(StConstant.CON_BILL_STATUS_03);
                QueryWrapper<StCShopVirtualHighStockItemDO> wrapper = new QueryWrapper<>();
                wrapper.eq("st_c_shop_virtual_high_stock_id", id);
                stCVirtualHighStockItemMapper.update(item, wrapper);
                StCVirtualHighStockDelayService.pushVirtualHighItemsToEle(reuslt, wrapper, stCVirtualHighStockItemMapper);
            } catch (Exception ex) {
                log.error(LogUtil.format("店铺商品虚高库存推数据到ES失败：{}"), Throwables.getStackTraceAsString(ex));
            }
        }
    }

    /**
     * <ul>
     *     <li>
     *         主表信息修改
     *     </li>
     * </ul>
     * @param session {@link QuerySession}
     * @param reuslt  主表信息
     * @return 修改影响条数
     */
    private int updateVirtualHighStock(QuerySession session, StCShopVirtualHighStockDO reuslt) {
        //修改信息
        StBeanUtils.makeModifierField(reuslt, session.getUser());
        //作废状态
        reuslt.setIsactive(StConstant.ISACTIVE_N);
        reuslt.setState(StConstant.CON_BILL_STATUS_03);
        //作废人
        reuslt.setCancelId(Long.valueOf(session.getUser().getId()));
        //作废时间
        reuslt.setCancelTime(new Date());
        //作废人姓名
        reuslt.setCancelName(session.getUser().getName());
        return stCVirtualHighStockMapper.updateById(reuslt);
    }

    private void checkAutocheckStatus(StCShopVirtualHighStockDO stCAutocheckDO, Long id, JSONArray errorArray) {
        if (stCAutocheckDO == null) {
            errorArray.add(StBeanUtils.getJsonObjectInfo(id, "当前记录已不存在！"));
            return;
        }
        //记录已作废，不允许作废
        if (stCAutocheckDO.getIsactive().equals(StConstant.ISACTIVE_N)) {
            errorArray.add(StBeanUtils.getJsonObjectInfo(id, "当前记录已作废，不允许作废！"));
            return;
        }
        if (!stCAutocheckDO.getState().equals(StConstant.CON_BILL_STATUS_01)) {
            errorArray.add(StBeanUtils.getJsonObjectInfo(id, "当前记录不是未审核，不允许作废！"));
        }
    }

    public static JSONArray makeVoidJsonArray(JSONObject param) {
        Long objid = param.getLong("objid");
        JSONArray auditJsonArray = param.getJSONArray("objids");
        if (auditJsonArray == null) {
            auditJsonArray = new JSONArray();
        }
        if (objid == null && auditJsonArray.size() <= 0) {
            throw new NDSException("请至少选择1条记录！");
        }
        //非空单对象加入json数组
        if (objid != null && objid > 0) {
            auditJsonArray = new JSONArray();
            auditJsonArray.add(objid.toString());
        }
        return auditJsonArray;
    }

}
