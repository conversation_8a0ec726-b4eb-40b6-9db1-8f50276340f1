package com.jackrain.nea.st.services;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.collect.Lists;
import com.jackrain.nea.config.PropertiesConf;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.cpext.api.CpLogisticsSelectServiceCmd;
import com.jackrain.nea.cpext.api.CpcPhyWareHouseQueryCmd;
import com.jackrain.nea.cpext.api.RegionQueryExtCmd;
import com.jackrain.nea.cpext.model.table.CpCPhyWarehouse;
import com.jackrain.nea.cpext.model.table.CpCRegion;
import com.jackrain.nea.cpext.model.table.CpLogistics;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.st.common.StConstant;
import com.jackrain.nea.st.mapper.StCFullcarCostItemMapper;
import com.jackrain.nea.st.mapper.StCFullcarCostMapper;
import com.jackrain.nea.st.model.enums.CloseStatusEnum;
import com.jackrain.nea.st.model.enums.LogisticsTypeEnum;
import com.jackrain.nea.st.model.enums.SubmitStatusEnum;
import com.jackrain.nea.st.model.poi.FullcarCostExportPoi;
import com.jackrain.nea.st.model.request.SyncFullcarCostImportRequest;
import com.jackrain.nea.st.model.table.StCFullcarCost;
import com.jackrain.nea.st.model.table.StCFullcarCostItem;
import com.jackrain.nea.st.result.StImportErrorMsgResult;
import com.jackrain.nea.st.utils.ExportUtil;
import com.jackrain.nea.st.utils.StBeanUtils;
import com.jackrain.nea.st.utils.StCExportUtil;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.face.impl.UserImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Reference;
import org.apache.poi.hssf.util.HSSFColor;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.FillPatternType;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.VerticalAlignment;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.XSSFCellStyle;
import org.apache.poi.xssf.usermodel.XSSFFont;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2022/6/13 18:01
 */
@Component
@Slf4j
@Transactional
public class StCFullcarCostImportService {

    @Reference(group = "cp-ext", version = "1.0")
    private CpcPhyWareHouseQueryCmd cpcPhyWareHouseQueryCmd;
    @Reference(group = "cp-ext", version = "1.0")
    private CpLogisticsSelectServiceCmd cpLogisticsSelectServiceCmd;
    @Reference(group = "cp-ext", version = "1.0")
    private RegionQueryExtCmd regionQueryExtCmd;
    @Resource
    private StCExportUtil stCExportUtil;
    @Resource
    private ExportUtil exportUtil;
    @Resource
    private StCFullcarCostMapper stCFullcarCostMapper;
    @Resource
    private StCFullcarCostItemMapper stCFullcarCostItemMapper;
    @Resource
    private PropertiesConf pconf;
    private final DateFormat format = new SimpleDateFormat("yyyyMMdd");
    private final String[] mainNames = {"仓库", "物流公司", "开始日期", "结束日期", "油价联动（%）", "备注", "目的省份", "目的城市",
            "到货天数", "起始重量（T/不包含）", "结束重量（T/包含）", "干线运费（元/T）", "提货费（元/票）", "送货费（元/票）", "保费（元/T）", "卸货费（元/T）", "其他费用"};
    private final String[] mustNames = {"仓库", "物流公司", "开始日期", "结束日期", "目的省份", "目的城市", "起始重量（T/不包含）", "结束重量（T/包含）"};
    private final String titleName = "注意：\n" +
            "1、红色标注项为必填项；\n" +
            "2、日期格式：YYYYMMDD,例20190102； \n" +
            "3、表头（1-2行）不允许修改，否则无法识别；\n" +
            "4、输入时，不允许有空隔，不允许有空行，删除第3行样例数据，否则无法识别；\n" +
            "5、仓库、物流公司、目的省份、目的城市需与基础档案保持一致，否者无法识别；\n" +
            "6、到货天数最大4位整数；\n" +
            "7、油价联动（%）、起始重量（T/不包含）、结束重量（T/包含）、干线运费（元/T）、提货费（元/票）、A类客户提货费、B类客户提货费、保费（元/T）、卸货费（元/T）、其他费用最大16位整数2位小数\n";

    public ValueHolderV14<String> queryTemplateDownloadUrl() {
        ValueHolderV14<String> holderV14 = new ValueHolderV14(ResultCode.SUCCESS, "整车报价设置头明细导入模板下载成功！");
        UserImpl user = new UserImpl();
        user.setName("");
        List<String> mainList = Lists.newArrayList(mainNames);
        List<String> mustList = Lists.newArrayList(mustNames);
        //生成Excel
        XSSFWorkbook hssfWorkbook = new XSSFWorkbook();
        hssfWorkbook = exportUtil.executeSheet(hssfWorkbook, "整车报价设置头明细", titleName, mainList, mustList,
                Lists.newArrayList(), Lists.newArrayList(), false);
        String putMsg = exportUtil.saveFileAndPutOss(hssfWorkbook, "整车报价设置头明细导入模板",
                user, "OSS-Bucket/EXPORT/ST_C_FULLCAR_COST/");
        holderV14.setData(putMsg);
        return holderV14;
    }

    public ValueHolderV14<List<StImportErrorMsgResult>> importFullcarCost(List<SyncFullcarCostImportRequest> importList, User user) {
        ValueHolderV14<List<StImportErrorMsgResult>> holderV14 = new ValueHolderV14<>();

        try {
            List<StImportErrorMsgResult> errorList = new ArrayList<>();
            //仓库信息
            Map<String, CpCPhyWarehouse> warehouseMap = new HashMap<>();
            ValueHolderV14<List<CpCPhyWarehouse>> warehouseResult = cpcPhyWareHouseQueryCmd.queryWarehourseIsY();
            if (ResultCode.SUCCESS == warehouseResult.getCode() && !CollectionUtils.isEmpty(warehouseResult.getData())) {
                warehouseMap = warehouseResult.getData().stream().collect(Collectors.toMap(CpCPhyWarehouse::getEname, Function.identity(), (x, y) -> y));
            }
            if (CollectionUtils.isEmpty(warehouseMap)) {
                holderV14.setCode(ResultCode.FAIL);
                holderV14.setMessage("请先维护实体仓信息！");
                return holderV14;
            }
            //物流信息
            Map<String, CpLogistics> logisticsMap = new HashMap<>();
            ValueHolderV14<List<CpLogistics>> logisticsResult = cpLogisticsSelectServiceCmd.queryLogisticsByType(LogisticsTypeEnum.CARGO_TRANSPORTATION.getKey());
            if (ResultCode.SUCCESS == logisticsResult.getCode() && !CollectionUtils.isEmpty(logisticsResult.getData())) {
                logisticsMap = logisticsResult.getData().stream().collect(Collectors.toMap(CpLogistics::getEname, Function.identity(), (x, y) -> y));
            }
            if (CollectionUtils.isEmpty(logisticsMap)) {
                holderV14.setCode(ResultCode.FAIL);
                holderV14.setMessage("请先维护物流公司信息！");
                return holderV14;
            }
            //省份信息
            Map<String, CpCRegion> provinceMap = new HashMap<>();
            //城市信息
            Map<String, CpCRegion> cityMap = new HashMap<>();
            List<String> regionTypes = Lists.newArrayList("PROV", "CITY");
            List<CpCRegion> regionList = regionQueryExtCmd.queryAllRegion(regionTypes);
            if (!CollectionUtils.isEmpty(regionList)) {
                Map<String, List<CpCRegion>> listMap = regionList.stream().collect(Collectors.groupingBy(CpCRegion::getRegiontype));
                if (!CollectionUtils.isEmpty(listMap)) {
                    provinceMap = listMap.get("PROV").stream().collect(Collectors.toMap(CpCRegion::getEname, Function.identity(), (x, y) -> y));
                    cityMap = listMap.get("CITY").stream().collect(Collectors.toMap(CpCRegion::getEname, Function.identity(), (x, y) -> y));
                }
            }
            if (CollectionUtils.isEmpty(provinceMap)) {
                holderV14.setCode(ResultCode.FAIL);
                holderV14.setMessage("请先维护省份信息！");
                return holderV14;
            }

            if (CollectionUtils.isEmpty(cityMap)) {
                holderV14.setCode(ResultCode.FAIL);
                holderV14.setMessage("请先维护城市信息！");
                return holderV14;
            }
            //构建主子表关系，用于后面对象构建
            Map<String, List<SyncFullcarCostImportRequest>> groupByMainData = new HashMap<>();
            this.importDataCheck(importList, errorList, warehouseMap, logisticsMap, provinceMap, cityMap, groupByMainData);

            if (!CollectionUtils.isEmpty(errorList)) {
                holderV14.setCode(ResultCode.FAIL);
                holderV14.setMessage("导入失败，导入数据存在错误！");
                holderV14.setData(errorList);
                return holderV14;
            }

            //构建保存和更新的对象集合
            List<StCFullcarCost> fullcarCostList = new ArrayList<>();
            List<StCFullcarCostItem> fullcarCostItemList = new ArrayList<>();
            this.buildSaveAndUpdateObj(groupByMainData, fullcarCostList, fullcarCostItemList, user);

            //数据库更新（带事务）
            if (!CollectionUtils.isEmpty(fullcarCostList) && !CollectionUtils.isEmpty(fullcarCostItemList)) {
                StCFullcarCostImportService importService = ApplicationContextHandle.getBean(StCFullcarCostImportService.class);
                importService.saveAll(fullcarCostList, fullcarCostItemList);
            }
            holderV14.setCode(ResultCode.SUCCESS);
            holderV14.setMessage("导入成功");
        } catch (Exception e) {
            holderV14.setCode(ResultCode.FAIL);
            holderV14.setMessage("整车费用设置导入处理异常：" + e.getMessage());
            return holderV14;
        }
        return holderV14;
    }

    /**
     * 主子表批量保存
     *
     * @param fullcarCostList     主表
     * @param fullcarCostItemList 明细
     */
    @Transactional(rollbackFor = Exception.class)
    public void saveAll(List<StCFullcarCost> fullcarCostList, List<StCFullcarCostItem> fullcarCostItemList) {
        if (log.isDebugEnabled()) {
            log.debug("StCFullcarCostImportService-saveAll start");
        }

        stCFullcarCostMapper.batchInsert(fullcarCostList);
        stCFullcarCostItemMapper.batchInsert(fullcarCostItemList);

    }

    /**
     * 根据import对象封装DTO对象
     *
     * @param groupByMainData
     * @param fullcarCostList
     * @param fullcarCostItemList
     */
    private void buildSaveAndUpdateObj(Map<String, List<SyncFullcarCostImportRequest>> groupByMainData,
                                       List<StCFullcarCost> fullcarCostList, List<StCFullcarCostItem> fullcarCostItemList, User user) {
        if (!CollectionUtils.isEmpty(groupByMainData)) {
            Set<String> keySet = groupByMainData.keySet();
            for (String key : keySet) {
                List<SyncFullcarCostImportRequest> importRequestList = groupByMainData.get(key);
                if (!CollectionUtils.isEmpty(importRequestList)) {
                    Long fullcarCostId = null;
                    for (int i = 0; i < importRequestList.size(); i++) {
                        SyncFullcarCostImportRequest importRequest = importRequestList.get(i);
                        if (importRequest != null) {
                            StCFullcarCostItem fullcarCostItem = new StCFullcarCostItem();
                            if (i == 0) {
                                fullcarCostId = ModelUtil.getSequence(StConstant.TAB_ST_C_FULLCAR_COST);
                                StCFullcarCost fullcarCost = new StCFullcarCost();
                                fullcarCost.setId(fullcarCostId);
                                fullcarCost.setCpCPhyWarehouseId(importRequest.getWarehouseId());
                                fullcarCost.setCpCLogisticsId(importRequest.getLogisticsId());
                                fullcarCost.setStartDate(importRequest.getStartDateForDate());
                                fullcarCost.setEndDate(importRequest.getEndDateForDate());
                                if (importRequest.getOilPriceLinkageNum() != null) {
                                    fullcarCost.setOilPriceLinkage(importRequest.getOilPriceLinkageNum());
                                }
                                if (!StringUtils.isEmpty(importRequest.getRemark())) {
                                    fullcarCost.setRemark(importRequest.getRemark());
                                }
                                fullcarCost.setStatus(SubmitStatusEnum.NO_SUBMIT.getKey());
                                fullcarCost.setCloseStatus(CloseStatusEnum.NO_CLOSE.getKey());
                                StBeanUtils.makeCreateField(fullcarCost, user);
                                reSetDateTimeRange(fullcarCost);
                                
                                fullcarCostList.add(fullcarCost);
                            }
                            fullcarCostItem.setId(ModelUtil.getSequence(StConstant.TAB_ST_C_FULLCAR_COST_ITEM));
                            fullcarCostItem.setFullcarCostId(fullcarCostId);
                            fullcarCostItem.setProvinceId(importRequest.getProvinceId());
                            fullcarCostItem.setCityId(importRequest.getCityId());
                            fullcarCostItem.setArrivalDays(importRequest.getArrivalDaysNum());
                            fullcarCostItem.setStartWeight(importRequest.getStartWeightNum());
                            fullcarCostItem.setEndWeight(importRequest.getEndWeightNum());
                            fullcarCostItem.setTrunkFreight(importRequest.getTrunkFreightNum());
                            fullcarCostItem.setDeliveryFee(importRequest.getDeliveryFeeNum());
                            fullcarCostItem.setFreight(importRequest.getFreightNum());
                            fullcarCostItem.setPremium(importRequest.getPremiumNum());
                            fullcarCostItem.setUnloadingFee(importRequest.getUnloadingFeeNum());
                            fullcarCostItem.setOtherFee(importRequest.getOtherFeeNum());
                            StBeanUtils.makeCreateField(fullcarCostItem, user);
                            fullcarCostItemList.add(fullcarCostItem);
                        }
                    }
                }
            }
        }
    }

    /**
     * 开始日期取当天0点，结束日期取当天23:59:59
     *
     * @param mainObj 主对象
     */
    private void reSetDateTimeRange(StCFullcarCost mainObj) {
        if (Objects.isNull(mainObj)) {
            return;
        }
        if (Objects.nonNull(mainObj.getStartDate())) {
            mainObj.setStartDate(DateUtil.beginOfDay(mainObj.getStartDate()));
        }
        if (Objects.nonNull(mainObj.getEndDate())) {
            /*mainObj.setEndDate(DateUtil.endOfDay(mainObj.getEndDate()));*/
            DateTime dateTime = DateUtil.endOfDay(mainObj.getEndDate());
            dateTime.setTime(dateTime.getTime() - 999);
            mainObj.setEndDate(dateTime);
        }
    }

    private void importDataCheck(List<SyncFullcarCostImportRequest> importList, List<StImportErrorMsgResult> errorList, Map<String, CpCPhyWarehouse> warehouseMap, Map<String,
            CpLogistics> logisticsMap, Map<String, CpCRegion> provinceMap, Map<String, CpCRegion> cityMap, Map<String, List<SyncFullcarCostImportRequest>> groupByMainData) {

        if (log.isDebugEnabled()) {
            log.debug("StCFullcarCostImportService-importDataCheck  start");
        }
        SimpleDateFormat format = new SimpleDateFormat("yyyyMMdd");
        format.setLenient(false);

        //已主表信息及城市信息作为key分组
        Map<String, List<SyncFullcarCostImportRequest>> importByMainDate = new HashMap<>();
        //检查必填是否为空
        //检查基础档案是否能够匹配
        //子表信息是否允许保存（以主表KEY+城市作为key转map）
        for (int i = 0; i < importList.size(); i++) {
            StringBuffer sb = new StringBuffer();
            SyncFullcarCostImportRequest importRequest = importList.get(i);
            importRequest.setRowNum(i + 3);
            if (StringUtils.isEmpty(importRequest.getWarehouseName())) {
                sb.append("仓库信息不能为空！");
            } else {
                CpCPhyWarehouse warehouse = warehouseMap.get(importRequest.getWarehouseName());
                if (warehouse == null) {
                    sb.append("仓库信息与实体仓档案未能匹配！");
                } else {
                    importRequest.setWarehouseId(warehouse.getId());
                }
            }
            if (StringUtils.isEmpty(importRequest.getLogisticsName())) {
                sb.append("物流公司信息不能为空！");
            } else {
                CpLogistics logistics = logisticsMap.get(importRequest.getLogisticsName());
                if (logistics == null) {
                    sb.append("物流公司信息与物流公司档案未能匹配！");
                } else {
                    importRequest.setLogisticsId(logistics.getId());
                }
            }
            if (StringUtils.isEmpty(importRequest.getStartDate())) {
                sb.append("开始日期不能为空！");
            } else {
                boolean flag = isValidDate(importRequest.getStartDate());
                if (flag) {
                    try {
                        importRequest.setStartDateForDate(format.parse(importRequest.getStartDate()));
                    } catch (ParseException e) {
                        sb.append("开始日期格式错误！");
                    }
                } else {
                    sb.append("开始日期格式错误！");
                }
            }
            if (StringUtils.isEmpty(importRequest.getEndDate())) {
                sb.append("结束日期不能为空！");
            } else {
                boolean flag = isValidDate(importRequest.getEndDate());
                if (flag) {
                    try {
                        importRequest.setEndDateForDate(format.parse(importRequest.getEndDate()));
                    } catch (ParseException e) {
                        sb.append("结束日期格式错误！");
                    }
                } else {
                    sb.append("结束日期格式错误！");
                }
            }
            if (StringUtils.isEmpty(importRequest.getProvince())) {
                sb.append("目的省份不能为空！");
            } else {
                CpCRegion province = provinceMap.get(importRequest.getProvince());
                if (province == null) {
                    sb.append("目的省份与省份档案未能匹配！");
                } else {
                    importRequest.setProvinceId(province.getId());
                }
            }
            if (StringUtils.isEmpty(importRequest.getCity())) {
                sb.append("目的城市不能为空");
            } else {
                CpCRegion city = cityMap.get(importRequest.getCity());
                if (city == null) {
                    sb.append("目的城市与城市档案未能匹配！");
                } else {
                    if (city.getCUpId().equals(importRequest.getProvinceId())) {
                        importRequest.setCityId(city.getId());
                    } else {
                        sb.append("城市和省份对应关系错误！");
                    }

                }
            }
            if (!StringUtils.isEmpty(importRequest.getOilPriceLinkage())) {
                boolean flag = isBigDecimal(importRequest.getOilPriceLinkage());
                if (flag) {
                    importRequest.setOilPriceLinkageNum(new BigDecimal(importRequest.getOilPriceLinkage()));
                } else {
                    sb.append("油价联动格式错误！");
                }
            } else {
                importRequest.setOilPriceLinkageNum(new BigDecimal(0));
            }
            if (!StringUtils.isEmpty(importRequest.getArrivalDays())) {
                boolean flag = isInteger(importRequest.getArrivalDays());
                if (flag) {
                    importRequest.setArrivalDaysNum(new Integer(importRequest.getArrivalDays()));
                } else {
                    sb.append("到货天数格式错误！");
                }
            } else {
                importRequest.setArrivalDaysNum(0);
            }

            if (StringUtils.isEmpty(importRequest.getStartWeight())) {
                sb.append("起始重量不能为空！");
            } else {
                boolean flag = isBigDecimal(importRequest.getStartWeight());
                if (flag) {
                    importRequest.setStartWeightNum(new BigDecimal(importRequest.getStartWeight()));
                } else {
                    sb.append("起始重量格式错误！");
                }
            }
            if (StringUtils.isEmpty(importRequest.getEndWeight())) {
                sb.append("结束重量不能为空");
            } else {
                boolean flag = isBigDecimal(importRequest.getEndWeight());
                if (flag) {
                    importRequest.setEndWeightNum(new BigDecimal(importRequest.getEndWeight()));
                } else {
                    sb.append("结束重量格式错误！");
                }
            }
            if (!StringUtils.isEmpty(importRequest.getTrunkFreight())) {
                boolean flag = isBigDecimal(importRequest.getTrunkFreight());
                if (flag) {
                    importRequest.setTrunkFreightNum(new BigDecimal(importRequest.getTrunkFreight()));
                } else {
                    sb.append("干线运费格式错误！");
                }
            } else {
                importRequest.setTrunkFreightNum(new BigDecimal(0));
            }
            if (!StringUtils.isEmpty(importRequest.getDeliveryFee())) {
                boolean flag = isBigDecimal(importRequest.getDeliveryFee());
                if (flag) {
                    importRequest.setDeliveryFeeNum(new BigDecimal(importRequest.getDeliveryFee()));
                } else {
                    sb.append("提货费格式错误！");
                }
            } else {
                importRequest.setDeliveryFeeNum(new BigDecimal(0));
            }
            if (!StringUtils.isEmpty(importRequest.getFreight())) {
                boolean flag = isBigDecimal(importRequest.getPremium());
                if (flag) {
                    importRequest.setFreightNum(new BigDecimal(importRequest.getFreight()));
                } else {
                    sb.append("送货费格式错误！");
                }
            } else {
                importRequest.setFreightNum(new BigDecimal(0));
            }
            if (!StringUtils.isEmpty(importRequest.getPremium())) {
                boolean flag = isBigDecimal(importRequest.getPremium());
                if (flag) {
                    importRequest.setPremiumNum(new BigDecimal(importRequest.getPremium()));
                } else {
                    sb.append("保费格式错误！");
                }
            } else {
                importRequest.setPremiumNum(new BigDecimal(0));
            }
            if (!StringUtils.isEmpty(importRequest.getUnloadingFee())) {
                boolean flag = isBigDecimal(importRequest.getUnloadingFee());
                if (flag) {
                    importRequest.setUnloadingFeeNum(new BigDecimal(importRequest.getUnloadingFee()));
                } else {
                    sb.append("卸货费格式错误！");
                }
            } else {
                importRequest.setUnloadingFeeNum(new BigDecimal(0));
            }
            if (!StringUtils.isEmpty(importRequest.getOtherFee())) {
                boolean flag = isBigDecimal(importRequest.getOtherFee());
                if (flag) {
                    importRequest.setOtherFeeNum(new BigDecimal(importRequest.getOtherFee()));
                } else {
                    sb.append("其他费用格式错误！");
                }
            } else {
                importRequest.setOtherFeeNum(new BigDecimal(0));
            }
            if (importRequest.getStartDateForDate() != null && importRequest.getEndDateForDate() != null && importRequest.getStartDateForDate().getTime() > importRequest.getEndDateForDate().getTime()) {
                sb.append("开始时间不能大于结束时间！");
            }
            if (importRequest.getStartWeightNum().compareTo(importRequest.getEndWeightNum()) > 0) {
                sb.append("起始重量不能大于结束重量！");
            }
            if (sb.length() > 0) {
                StImportErrorMsgResult errorMsgResult = new StImportErrorMsgResult();
                errorMsgResult.setRowNum(i + 3);
                errorMsgResult.setErrorMsg(sb.toString());
                errorList.add(errorMsgResult);
            }
        }
        if (CollectionUtils.isEmpty(errorList)) {
            //数据验证没有问题，开始验证逻辑问题
            for (SyncFullcarCostImportRequest importRequest : importList) {
                //城市维度分组
                String key = importRequest.getWarehouseName() + importRequest.getLogisticsName() + importRequest.getStartDate() +
                        importRequest.getEndDate() + importRequest.getOilPriceLinkage() + importRequest.getRemark() + importRequest.getCity();
                if (importByMainDate.containsKey(key)) {
                    List<SyncFullcarCostImportRequest> requestList = importByMainDate.get(key);
                    requestList.add(importRequest);
                    importByMainDate.put(key, requestList);
                } else {
                    List<SyncFullcarCostImportRequest> requestList = new ArrayList<>();
                    requestList.add(importRequest);
                    importByMainDate.put(key, requestList);
                }
                //主表维度分组
                String key1 = importRequest.getWarehouseName() + importRequest.getLogisticsName() + importRequest.getStartDate() +
                        importRequest.getEndDate() + importRequest.getOilPriceLinkage() + importRequest.getRemark();
                if (groupByMainData.containsKey(key1)) {
                    List<SyncFullcarCostImportRequest> requestList = groupByMainData.get(key1);
                    requestList.add(importRequest);
                    groupByMainData.put(key1, requestList);
                } else {
                    List<SyncFullcarCostImportRequest> requestList = new ArrayList<>();
                    requestList.add(importRequest);
                    groupByMainData.put(key1, requestList);
                }
            }
            int errRow = 0;
            for (String key : importByMainDate.keySet()) {
                List<SyncFullcarCostImportRequest> list = importByMainDate.get(key);
                if (list.size() > 1) {
                    for (int i = 0; i < list.size(); i++) {
                        for (int j = i + 1; j < list.size(); j++) {
                            SyncFullcarCostImportRequest one = list.get(i);
                            SyncFullcarCostImportRequest two = list.get(j);
                            Boolean flag = checkWeight(one.getStartWeightNum(), one.getEndWeightNum(), two.getStartWeightNum(), two.getEndWeightNum());
                            if (!flag) {
                                StImportErrorMsgResult errorMsg = new StImportErrorMsgResult();
                                errorMsg.setRowNum(errRow++);
                                errorMsg.setErrorMsg("第" + one.getRowNum() + "行和第" + two.getRowNum() + "行的起始重量和结束重量存在交叉！");
                                errorList.add(errorMsg);
                            }
                        }
                    }
                }
            }
        }
    }

    /**
     * 判断字符串是否为合法的日期格式
     *
     * @param dateStr 待判断的字符串
     * @return
     */
    public static boolean isValidDate(String dateStr) {
        //判断结果 默认为true
        boolean judgeresult = true;
        if (dateStr == null || dateStr.trim().length() != 8) {
            return false;
        }
        for (int i = 0; i < dateStr.length(); i++) {
            if (!Character.isDigit(dateStr.charAt(i))) {
                return false;
            }
        }
        String yearStr = dateStr.substring(0, 3);
        if (yearStr.startsWith("0")) {
            judgeresult = false;
        }
        return judgeresult;
    }

    /**
     * 判断字符串是否是4位以内整数
     *
     * @param str
     * @return
     */
    private boolean isInteger(String str) {
        if (str == null || str.trim().length() == 0 || str.trim().length() > 4) {
            return false;
        }
        for (int i = 0; i < str.length(); i++) {
            if (!Character.isDigit(str.charAt(i))) {
                return false;
            }
        }
        return true;
    }

    /**
     * 判断字符串是否能转成BigDecimal
     *
     * @param str
     * @return
     */
    private boolean isBigDecimal(String str) {
        if (str == null || str.trim().length() == 0) {
            return false;
        }
        char[] chars = str.toCharArray();
        int sz = chars.length;
        int i = (chars[0] == '-') ? 1 : 0;
        if (i == sz) {
            //第一位是-号，切长度为1则不行
            return false;
        }

        if (chars[i] == '.') {
            //除了负号，第一位不能为'小数点'
            return false;
        }

        boolean radixPoint = false;
        for (; i < sz; i++) {
            if (chars[i] == '.') {
                if (radixPoint) {
                    return false;
                }
                radixPoint = true;
            } else if (!(chars[i] >= '0' && chars[i] <= '9')) {
                return false;
            }
        }
        return true;
    }

    private Boolean checkWeight(BigDecimal startWeight, BigDecimal endWeight, BigDecimal startWeight1, BigDecimal endWeight1) {
        if (endWeight.compareTo(endWeight1) == 0) {
            return false;
        } else {
            if (endWeight.compareTo(endWeight1) > 0) {
                //endWeight大，当startWeight小于endWeight1时，返回false
                return startWeight.compareTo(endWeight1) >= 0;
            } else {
                //endWeight1大，当startWeight1小于endWeight时，返回false
                return startWeight1.compareTo(endWeight) >= 0;
            }
        }
    }

    /**
     * 下载错误信息
     *
     * @param user       用户信息
     * @param errMsgList 错误信息
     * @return
     */
    public String downloadImportErrMsg(User user, List<StImportErrorMsgResult> errMsgList) {
        String[] columnNames = {"数据行", "错误原因"};
        List<Integer> columnWidthList = Lists.newArrayList(10, 120);
        List<String> columnList = Lists.newArrayList(columnNames);
        String[] keys = {"rowNum", "errorMsg"};
        List<String> keyList = Lists.newArrayList(keys);
        stCExportUtil.setEndpoint(pconf.getProperty("r3.oss.endpoint"));
        stCExportUtil.setAccessKeyId(pconf.getProperty("r3.oss.accessKey"));
        stCExportUtil.setAccessKeySecret(pconf.getProperty("r3.oss.secretKey"));
        stCExportUtil.setBucketName(pconf.getProperty("r3.oss.bucketName"));
        String timeout = pconf.getProperty("r3.oss.timeout");
        if (StringUtils.isEmpty(timeout)) {
            timeout = "1800000";
        }
        stCExportUtil.setTimeout(timeout);
        // 整车费用设置模板存放地址
        String filePath = "OSS-Bucket/EXPORT/ST_C_FULLCAR_COST/";

        Workbook hssfWorkbook = stCExportUtil.execute("错误信息", "整车报价设置导入-错误信息",
                columnList, keyList, errMsgList, columnWidthList, columnList);
        return stCExportUtil.saveFileAndPutOss(hssfWorkbook, "整车报价设置导入-错误信息", user, filePath);
    }

    /**
     * 导出整车费用设置
     *
     * @param ids
     * @return
     */
    public ValueHolderV14<String> exportFullcarCost(String ids, User user) {
        ValueHolderV14<String> holderV14 = new ValueHolderV14<>();
        List<Long> idList = Arrays.stream(ids.split(",")).map(s -> Long.parseLong(s.trim())).collect(Collectors.toList());

        //仓库信息
        Map<Long, CpCPhyWarehouse> warehouseMap = new HashMap<>();
        ValueHolderV14<List<CpCPhyWarehouse>> warehourseResult = cpcPhyWareHouseQueryCmd.queryWarehourseIsY();
        if (ResultCode.SUCCESS == warehourseResult.getCode() && !CollectionUtils.isEmpty(warehourseResult.getData())) {
            warehouseMap = warehourseResult.getData().stream().collect(Collectors.toMap(CpCPhyWarehouse::getId, Function.identity(), (x, y) -> y));
        }
        if (CollectionUtils.isEmpty(warehouseMap)) {
            holderV14.setCode(ResultCode.FAIL);
            holderV14.setMessage("请先维护实体仓信息！");
            return holderV14;
        }
        //物流信息
        Map<Long, CpLogistics> logisticsMap = new HashMap<>();
        ValueHolderV14<List<CpLogistics>> logisticsResult = cpLogisticsSelectServiceCmd.queryLogisticsByType(LogisticsTypeEnum.CARGO_TRANSPORTATION.getKey());
        if (ResultCode.SUCCESS == logisticsResult.getCode() && !CollectionUtils.isEmpty(logisticsResult.getData())) {
            logisticsMap = logisticsResult.getData().stream().collect(Collectors.toMap(CpLogistics::getId, Function.identity(), (x, y) -> y));
        }
        if (CollectionUtils.isEmpty(logisticsMap)) {
            holderV14.setCode(ResultCode.FAIL);
            holderV14.setMessage("请先维护物流公司信息！");
            return holderV14;
        }
        //省份信息
        Map<Long, CpCRegion> provinceMap = new HashMap<>();
        //城市信息
        Map<Long, CpCRegion> cityMap = new HashMap<>();
        List<String> regionTypes = Lists.newArrayList("PROV", "CITY");
        List<CpCRegion> regionList = regionQueryExtCmd.queryAllRegion(regionTypes);
        if (!CollectionUtils.isEmpty(regionList)) {
            Map<String, List<CpCRegion>> listMap = regionList.stream().collect(Collectors.groupingBy(CpCRegion::getRegiontype));
            if (!CollectionUtils.isEmpty(listMap)) {
                provinceMap = listMap.get("PROV").stream().collect(Collectors.toMap(CpCRegion::getId, Function.identity(), (x, y) -> y));
                cityMap = listMap.get("CITY").stream().collect(Collectors.toMap(CpCRegion::getId, Function.identity(), (x, y) -> y));
            }
        }
        if (CollectionUtils.isEmpty(provinceMap)) {
            holderV14.setCode(ResultCode.FAIL);
            holderV14.setMessage("请先维护省份信息！");
            return holderV14;
        }

        if (CollectionUtils.isEmpty(cityMap)) {
            holderV14.setCode(ResultCode.FAIL);
            holderV14.setMessage("请先维护城市信息！");
            return holderV14;
        }

        //循环查询主子表数据，转成SXSSFWorkbook
        XSSFWorkbook workbook = new XSSFWorkbook();
        Sheet sheet = workbook.createSheet("整车报价设置导出");
        List<FullcarCostExportPoi> exportPoiList;
        for (int i = 0; i < idList.size(); i++) {
            exportPoiList = getPoi(idList.get(i), warehouseMap, logisticsMap, provinceMap, cityMap);
            //第一个POI创建sheet,和表头
            this.createExcel(workbook, exportPoiList, sheet, i == 0);
        }

        //上传OSS，并获得下载地址
        log.info(" Start ExportFullcarCost.createExcel");
        stCExportUtil.setEndpoint(pconf.getProperty("r3.oss.endpoint"));
        stCExportUtil.setAccessKeyId(pconf.getProperty("r3.oss.accessKey"));
        stCExportUtil.setAccessKeySecret(pconf.getProperty("r3.oss.secretKey"));
        stCExportUtil.setBucketName(pconf.getProperty("r3.oss.bucketName"));
        String timeout = pconf.getProperty("r3.oss.timeout");
        if (StringUtils.isEmpty(timeout)) {
            timeout = "1800000";
        }
        stCExportUtil.setTimeout(timeout);
        // 整车费用设置模板存放地址
        String filePath1 = "OSS-Bucket/EXPORT/ST_C_FULLCAR_COST/";
        String fileUrl = stCExportUtil.saveFileAndPutOss(workbook, "整车报价设置", user, filePath1);
        if (StringUtils.isEmpty(fileUrl)) {
            holderV14.setCode(ResultCode.FAIL);
            holderV14.setMessage("导出失败！获得下载地址失败！");
            return holderV14;
        }
        holderV14.setCode(ResultCode.SUCCESS);
        holderV14.setMessage("导出成功");
        holderV14.setData(fileUrl);
        return holderV14;
    }

    /**
     * 创建workBOOOK
     *
     * @param workbook
     * @param exportPoiList
     * @param sheet
     * @param flag          是否是第一条数据
     */
    private void createExcel(XSSFWorkbook workbook, List<FullcarCostExportPoi> exportPoiList, Sheet sheet, boolean flag) {
        List<String> columnNames = Lists.newArrayList(mainNames);
        if (flag) {
            List<String> mustColumnNames = Lists.newArrayList(mustNames);
            // 创建第0行 也就是标题 合并列标题
            sheet.addMergedRegion(new CellRangeAddress(0, 0, 0, columnNames.size() - 1));
            Row row = sheet.createRow(0);
            // 设备标题的高度
            row.setHeightInPoints(135);
            // 第三步创建标题的单元格样式style2以及字体样式headerFont1
            XSSFCellStyle style = workbook.createCellStyle();
            style.setAlignment(HorizontalAlignment.LEFT);
            style.setVerticalAlignment(VerticalAlignment.CENTER);
            style.setFillPattern(FillPatternType.SOLID_FOREGROUND);
            //开启自动换行
            style.setWrapText(true);
            // 创建字体样式
            XSSFFont headerFont1 = workbook.createFont();
            // 设置字体大小
            headerFont1.setFontHeightInPoints((short) 10);
            // 为标题样式设置字体样式
            style.setFont(headerFont1);
            // 创建标题第一列
            Cell cell = row.createCell(0);
            // 设置值标题
            cell.setCellValue(titleName);
            // 设置标题样式
            cell.setCellStyle(style);
            // 创建第1行 也就是表头
            Row row1 = sheet.createRow(1);
            row1.setHeightInPoints(15);// 设置表头高度
            for (int i = 0; i < columnNames.size(); i++) {
                Cell cell1 = row1.createCell(i);
                cell1.setCellValue(columnNames.get(i));
                XSSFCellStyle style1 = workbook.createCellStyle();
                XSSFFont font = workbook.createFont();
                if (mustColumnNames.contains(columnNames.get(i))) {
                    font.setColor(HSSFColor.RED.index);
                } else {
                    font.setColor(HSSFColor.BLACK.index);
                }
                style1.setFont(font);
                cell1.setCellStyle(style1);
            }
        }

        int lastRowNum = sheet.getLastRowNum() + 1;
        for (FullcarCostExportPoi exportPoi : exportPoiList) {
            Row row = sheet.createRow(lastRowNum);
            row.setHeightInPoints(15);
            Cell cell1 = row.createCell(0);
            cell1.setCellValue(exportPoi.getWarehouseName());
            Cell cell2 = row.createCell(1);
            cell2.setCellValue(exportPoi.getLogisticsName());
            Cell cell3 = row.createCell(2);
            cell3.setCellValue(exportPoi.getStartDate());
            Cell cell4 = row.createCell(3);
            cell4.setCellValue(exportPoi.getEndDate());
            Cell cell5 = row.createCell(4);
            cell5.setCellValue(exportPoi.getOilPriceLinkage());
            Cell cell6 = row.createCell(5);
            cell6.setCellValue(exportPoi.getRemark());
            Cell cell7 = row.createCell(6);
            cell7.setCellValue(exportPoi.getProvince());
            Cell cell8 = row.createCell(7);
            cell8.setCellValue(exportPoi.getCity());
            Cell cell9 = row.createCell(8);
            cell9.setCellValue(exportPoi.getArrivalDays());
            Cell cell10 = row.createCell(9);
            cell10.setCellValue(exportPoi.getStartWeight());
            Cell cell11 = row.createCell(10);
            cell11.setCellValue(exportPoi.getEndWeight());
            Cell cell12 = row.createCell(11);
            cell12.setCellValue(exportPoi.getTrunkFreight());
            Cell cell13 = row.createCell(12);
            cell13.setCellValue(exportPoi.getDeliveryFee());
            Cell cell14 = row.createCell(13);
            cell14.setCellValue(exportPoi.getFreight());
            Cell cell16 = row.createCell(14);
            cell16.setCellValue(exportPoi.getPremium());
            Cell cell17 = row.createCell(15);
            cell17.setCellValue(exportPoi.getUnloadingFee());
            Cell cell18 = row.createCell(16);
            cell18.setCellValue(exportPoi.getOtherFee());
            lastRowNum++;
        }
    }

    /**
     * 转换为POI
     *
     * @param id
     * @param warehouseMap
     * @param logisticsMap
     * @param provinceMap
     * @param cityMap
     * @return
     */
    private List<FullcarCostExportPoi> getPoi(Long id, Map<Long, CpCPhyWarehouse> warehouseMap, Map<Long, CpLogistics> logisticsMap, Map<Long, CpCRegion> provinceMap, Map<Long, CpCRegion> cityMap) {
        ArrayList<FullcarCostExportPoi> exportPoiList = new ArrayList<>();
        //查询主表信息
        StCFullcarCost fullcarCost = stCFullcarCostMapper.selectById(id);
        if (fullcarCost == null) {
            throw new NDSException("主数据不存在或已删除！");
        }
        String warehouseName = null;
        String logisticsName = null;
        String startDate = null;
        String endtDate = null;
        String oilPriceLinkage = null;
        String remark = null;
        if (fullcarCost.getCpCPhyWarehouseId() != null && warehouseMap.get(fullcarCost.getCpCPhyWarehouseId()) != null) {
            warehouseName = warehouseMap.get(fullcarCost.getCpCPhyWarehouseId()).getEname();
        }
        if (fullcarCost.getCpCLogisticsId() != null && logisticsMap.get(fullcarCost.getCpCLogisticsId()) != null) {
            logisticsName = logisticsMap.get(fullcarCost.getCpCLogisticsId()).getEname();
        }
        if (fullcarCost.getStartDate() != null) {
            startDate = format.format(fullcarCost.getStartDate());
        }
        if (fullcarCost.getEndDate() != null) {
            endtDate = format.format(fullcarCost.getEndDate());
        }
        if (fullcarCost.getOilPriceLinkage() != null) {
            oilPriceLinkage = fullcarCost.getOilPriceLinkage().stripTrailingZeros().toPlainString();
        }
        if (fullcarCost.getRemark() != null) {
            remark = fullcarCost.getRemark();
        }
        List<StCFullcarCostItem> costItemList = stCFullcarCostItemMapper.selectList(new LambdaQueryWrapper<StCFullcarCostItem>()
                .eq(StCFullcarCostItem::getFullcarCostId, id));
        if (CollectionUtils.isEmpty(costItemList)) {
            FullcarCostExportPoi exportPoi = new FullcarCostExportPoi();
            exportPoi.setWarehouseName(warehouseName);
            exportPoi.setLogisticsName(logisticsName);
            exportPoi.setStartDate(startDate);
            exportPoi.setEndDate(endtDate);
            exportPoi.setOilPriceLinkage(oilPriceLinkage);
            exportPoi.setRemark(remark);
            exportPoiList.add(exportPoi);
            return exportPoiList;
        } else {
            for (StCFullcarCostItem costItem : costItemList) {
                FullcarCostExportPoi exportPoi = new FullcarCostExportPoi();
                exportPoi.setWarehouseName(warehouseName);
                exportPoi.setLogisticsName(logisticsName);
                exportPoi.setStartDate(startDate);
                exportPoi.setEndDate(endtDate);
                exportPoi.setOilPriceLinkage(oilPriceLinkage);
                exportPoi.setRemark(remark);
                if (costItem.getProvinceId() != null && provinceMap.get(costItem.getProvinceId()) != null) {
                    exportPoi.setProvince(provinceMap.get(costItem.getProvinceId()).getEname());
                }
                if (costItem.getCityId() != null && cityMap.get(costItem.getCityId()) != null) {
                    exportPoi.setCity(cityMap.get(costItem.getCityId()).getEname());
                }
                if (costItem.getArrivalDays() != null) {
                    exportPoi.setArrivalDays(costItem.getArrivalDays().toString());
                }
                if (costItem.getStartWeight() != null) {
                    exportPoi.setStartWeight(costItem.getStartWeight().stripTrailingZeros().toPlainString());
                }
                if (costItem.getEndWeight() != null) {
                    exportPoi.setEndWeight(costItem.getEndWeight().stripTrailingZeros().toPlainString());
                }
                if (costItem.getTrunkFreight() != null) {
                    exportPoi.setTrunkFreight(costItem.getTrunkFreight().stripTrailingZeros().toPlainString());
                }
                if (costItem.getDeliveryFee() != null) {
                    exportPoi.setDeliveryFee(costItem.getDeliveryFee().stripTrailingZeros().toPlainString());
                }
                if (costItem.getFreight() != null) {
                    exportPoi.setFreight(costItem.getFreight().stripTrailingZeros().toPlainString());
                }
                if (costItem.getPremium() != null) {
                    exportPoi.setPremium(costItem.getPremium().stripTrailingZeros().toPlainString());
                }
                if (costItem.getUnloadingFee() != null) {
                    exportPoi.setUnloadingFee(costItem.getUnloadingFee().stripTrailingZeros().toPlainString());
                }
                if (costItem.getOtherFee() != null) {
                    exportPoi.setOtherFee(costItem.getOtherFee().stripTrailingZeros().toPlainString());
                }
                exportPoiList.add(exportPoi);
            }
        }
        return exportPoiList;
    }
}
