package com.jackrain.nea.st.services;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.st.common.StConstant;
import com.jackrain.nea.st.mapper.StCOperationCostMapper;
import com.jackrain.nea.st.model.table.StCOperationcostDO;
import com.jackrain.nea.st.utils.StBeanUtils;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;

/**
 * @Descroption 操作费方案-作废逻辑
 * <AUTHOR>
 * @Date 2019/3/9
 */
@Component
@Slf4j
@Transactional
public class OperationcostVoidService extends CommandAdapter {
    @Autowired
    private StCOperationCostMapper stCOperationcostMapper;

    @Override
    public ValueHolder execute(QuerySession querySession) throws NDSException {
        DefaultWebEvent event = querySession.getEvent();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(
                event.getParameterValue("param"), "yyyy-MM-dd HH:mm:ss"),
                Feature.OrderedField);

        JSONArray itemArray = StBeanUtils.makeVoidJsonArray(param);
        JSONArray errorArray = new JSONArray();
        if (itemArray.size() > 0) {
            for (int i = 0; i < itemArray.size(); i++) {
                Long itemid = itemArray.getLong(i);
                saveOperationcostByID(querySession, itemid, errorArray);
            }
        }
        return StBeanUtils.getProcessValueHolder(itemArray, errorArray, StConstant.CON_BILL_ACTION_VOID);
    }

    private void saveOperationcostByID(QuerySession session, Long id, JSONArray errorArray) {
        StCOperationcostDO stCOperationcostDO = stCOperationcostMapper.selectById(id);
        JSONArray tempArray = new JSONArray();
        checkOperationcostStatus(stCOperationcostDO, id, tempArray);
        if (tempArray.size() > 0) {
            errorArray.addAll(tempArray);
            return;
        }

        StBeanUtils.makeModifierField(stCOperationcostDO, session.getUser());//修改信息
        stCOperationcostDO.setModifierename(session.getUser().getEname());//修改人账号
        stCOperationcostDO.setBillStatus(StConstant.CON_BILL_STATUS_03);//作废
        stCOperationcostDO.setIsactive(StConstant.ISACTIVE_N);//作废状态
        stCOperationcostDO.setDelid(Long.valueOf(session.getUser().getId()));//作废人
        stCOperationcostDO.setDelTime(new Date());//作废时间
        stCOperationcostDO.setDelname(session.getUser().getName());//作废人姓名
        stCOperationcostDO.setDelename(session.getUser().getEname());//作废人账号
        if ((stCOperationcostMapper.updateById(stCOperationcostDO)) <= 0) {
            errorArray.add(StBeanUtils.getJsonObjectInfo(id, "方案名称:" + stCOperationcostDO.getEname() + ",作废失败！"));
        }
    }

    private void checkOperationcostStatus(StCOperationcostDO stCOperationcostDO, Long id, JSONArray errorArray) {
        if (stCOperationcostDO == null) {
            errorArray.add(StBeanUtils.getJsonObjectInfo(id, "当前记录已不存在！"));
            return;
        }
        //不是未审核，不允许作废
        if (stCOperationcostDO.getBillStatus() == null
                || !StConstant.CON_BILL_STATUS_01.equals(stCOperationcostDO.getBillStatus())) {
            errorArray.add(StBeanUtils.getJsonObjectInfo(id, "当前记录不是未审核，不允许作废！"));
            return;
        }
    }
}
