package com.jackrain.nea.st.services;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.google.common.collect.Lists;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.st.common.StConstant;
import com.jackrain.nea.st.config.R3OssConfig;
import com.jackrain.nea.st.mapper.StCExpressAreaItemMapper;
import com.jackrain.nea.st.model.result.RegionTreeResult;
import com.jackrain.nea.st.model.result.StErrMsgResult;
import com.jackrain.nea.st.model.table.StCExpressAreaItemDO;
import com.jackrain.nea.st.utils.ExportUtil;
import com.jackrain.nea.st.utils.RegionTreeUtils;
import com.jackrain.nea.st.utils.StBeanUtils;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.face.impl.UserImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @author:huang.zaizai
 * @since: 2019/8/9
 * @create at : 2019/8/9 14:14
 */
@Component
@Slf4j
public class ExpressAreaItemImportService extends CommandAdapter {

    //导入excel,业务校验错误提示返回 错误excel地址
    public static final int IMPORT_ERROR_CODE = 10001;

    @Autowired
    private ExportUtil exportUtil;
    @Autowired
    private RegionTreeQueryService regionTreeQueryService;
    @Autowired
    private R3OssConfig r3OssConfig;
    @Autowired
    private StCExpressAreaItemMapper itemMapper;

    /**
     * @Description 模板下载
     * @date 2019/8/8 20:00
     * @param
     * @return com.jackrain.nea.sys.domain.ValueHolderV14
     */
    public ValueHolderV14 downloadTemp(){
        ValueHolderV14 vh = new ValueHolderV14(ResultCode.SUCCESS, "物流区域明细导入模板下载成功！");
        exportUtil.setEndpoint(r3OssConfig.getEndpoint());
        exportUtil.setAccessKeyId(r3OssConfig.getAccessKeyId());
        exportUtil.setAccessKeySecret(r3OssConfig.getAccessKeySecret());
        exportUtil.setBucketName(r3OssConfig.getBucketName());
        if(StringUtils.isEmpty(r3OssConfig.getTimeout())){
            //如果获取不到apllo配置参数，设置默认过期时间为30分钟
            exportUtil.setTimeout("1800000");
        } else {
            exportUtil.setTimeout(r3OssConfig.getTimeout());
        }
        /**
         *  拼接Excel主表sheet表头字段
         * */
        String mainNames[] = {"省", "市", "区", "排除区域", "是否到达"};
        String mustNames[] = {"省", "市", "区"};
        User user = new UserImpl();
        ((UserImpl) user).setName("");
        List<String> mainList = Lists.newArrayList(mainNames);
        List<String> mustList = Lists.newArrayList(mustNames);
        //生成Excel
        XSSFWorkbook hssfWorkbook = new XSSFWorkbook();
        exportUtil.executeSheet(hssfWorkbook, "物流区域明细数据", "", mainList, mustList,
                Lists.newArrayList(), Lists.newArrayList(), false);
        String putMsg = exportUtil.saveFileAndPutOss(hssfWorkbook, "物流区域明细导入模板",
                user, "OSS-Bucket/EXPORT/StCExpressAreaItem/");
        vh.setData(putMsg);
        return vh;
    }

    /**
     * @Description 导入
     * @date 2019/8/9 15:02
     * @param expressAreaItemList
     * @param user
     * @return com.jackrain.nea.sys.domain.ValueHolderV14
     */
    public ValueHolderV14 importExpressAreaItem(Long objid, List<StCExpressAreaItemDO> expressAreaItemList, User user){
        ValueHolderV14 vh = new ValueHolderV14();
        if(CollectionUtils.isEmpty(expressAreaItemList)){
            vh.setCode(ResultCode.FAIL);
            vh.setMessage("导入失败,上传文件未录入数据!");
            return vh;
        }
        //保存物流明细
        List<String> errMsgList = saveExpressAreaItem(objid, expressAreaItemList, user);
        //3.若有错误信息支持导出
        if(errMsgList.size() > 0){
            List<StErrMsgResult> errExcelList = Lists.newArrayList();
            errMsgList.forEach(errMsg->{
                StErrMsgResult errMsgResult = new StErrMsgResult();
                errMsgResult.setErrMsg(errMsg);
                errExcelList.add(errMsgResult);
            });
            int successNum = expressAreaItemList.size() - errMsgList.size();
            vh.setCode(IMPORT_ERROR_CODE);
            vh.setMessage(String.format("导入成功%d条,失败%d条",successNum,errMsgList.size()));
            String sdd = downloadErrMsg(user, errExcelList);
            vh.setData(sdd);
        }else{
            vh.setCode(ResultCode.SUCCESS);
            vh.setMessage("导入成功");
        }
        return vh;
    }

    /**
     * 下载错误信息
     * @param user
     * @param errExcelList
     * @return 错误信息
     */
    private String downloadErrMsg(User user, List<StErrMsgResult> errExcelList) {
        String columnNames[] = {"错误原因"};
        List<String> columnList = Lists.newArrayList(columnNames);
        String keys[] = {"errMsg"};
        List<String> keyList = Lists.newArrayList(keys);
        exportUtil.setEndpoint(r3OssConfig.getEndpoint());
        exportUtil.setAccessKeyId(r3OssConfig.getAccessKeyId());
        exportUtil.setAccessKeySecret(r3OssConfig.getAccessKeySecret());
        exportUtil.setBucketName(r3OssConfig.getBucketName());
        if(StringUtils.isEmpty(r3OssConfig.getTimeout())){
            //如果获取不到apllo配置参数，设置默认过期时间为30分钟
            exportUtil.setTimeout("1800000");
        } else {
            exportUtil.setTimeout(r3OssConfig.getTimeout());
        }
        Workbook hssfWorkbook = exportUtil.execute("物流区域明细", "物流区域明细",
                columnList, keyList, errExcelList);
        return exportUtil.saveFileAndPutOss(hssfWorkbook, "物流区域明细导入错误信息",
                user,"OSS-Bucket/EXPORT/StCExpressAreaItem/");
    }

    /**
     * 保存物流区域信息
     * @param objid
     * @param expressAreaItemList
     * @return 错误信息
     */
    private List<String> saveExpressAreaItem(Long objid, List<StCExpressAreaItemDO> expressAreaItemList, User user) {
        List<String> errMsgList = Lists.newArrayList();
        List<StCExpressAreaItemDO> itemList = Lists.newArrayList();
        List<RegionTreeResult> treeList = Lists.newArrayList();
        //查询地址
        JSONObject obj = new JSONObject();
        obj.put("regiontype", "PROV,CITY,DIST");
        ValueHolderV14<List<RegionTreeResult>> treeVh = regionTreeQueryService.queryRegionTree(obj);
        if (treeVh.isOK()) {
            treeList = treeVh.getData();
        }
        //1.组装集合
        for(StCExpressAreaItemDO expressAreaItem : expressAreaItemList) {
            try{
                //补足冗余信息
                getImportExpressAreaItem(expressAreaItem, treeList);
                expressAreaItem.setStCExpressAreaId(objid);
                itemList.add(expressAreaItem);
            }catch(Exception e){
                String area = expressAreaItem.getCpCRegionProvinceEname() +
                        expressAreaItem.getCpCRegionCityEname() + expressAreaItem.getCpCRegionAreaEname();
                errMsgList.add(area + e.getMessage());
            }
        }

        Map<String, StCExpressAreaItemDO> itemMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(itemList)) {
            List<StCExpressAreaItemDO> itemOldList = itemMapper.selectList(new QueryWrapper<StCExpressAreaItemDO>()
                    .lambda().eq(StCExpressAreaItemDO::getStCExpressAreaId, objid));
            for (StCExpressAreaItemDO itemOld : itemOldList) {
                String key = itemOld.getCpCRegionProvinceEname() + itemOld.getCpCRegionCityEname() + itemOld.getCpCRegionAreaEname();
                itemMap.put(key, itemOld);
            }
        }
        //2.保存信息
        for(StCExpressAreaItemDO item : itemList){
            try{
                //存在更新 不存在新增
                String key = item.getCpCRegionProvinceEname() + item.getCpCRegionCityEname() + item.getCpCRegionAreaEname();
                if (itemMap.containsKey(key)) {
                    item.setId(itemMap.get(key).getId());
                    StBeanUtils.makeModifierField(item, user);
                    if (itemMapper.updateById(item) < 1) {
                        throw new NDSException(key + "物流区域明细更新失败");
                    }
                } else {
                    item.setId(ModelUtil.getSequence(StConstant.TAB_ST_C_EXPRESS_AREA_ITEM));
                    StBeanUtils.makeCreateField(item, user);
                    if (itemMapper.insert(item) < 1) {
                        throw new NDSException(key + "物流区域明细插入失败");
                    }
                }
            }catch(Exception e){
                errMsgList.add(e.getMessage());
            }
        }
        return errMsgList;
    }

    /**
     * 补足冗余信息
     * @param expressAreaItem
     * @param treeList
     */
    private void getImportExpressAreaItem(StCExpressAreaItemDO expressAreaItem, List<RegionTreeResult> treeList) {
        Long upId = 1L;
        //省设值
        if (!StringUtils.isBlank(expressAreaItem.getCpCRegionProvinceEname())) {
            RegionTreeResult treeResult = RegionTreeUtils.getTreeResultByName(treeList, expressAreaItem.getCpCRegionProvinceEname(), upId);
            if(treeResult == null){
                throw new NDSException(String.format("不存在名称为[%s]的省", expressAreaItem.getCpCRegionProvinceEname()));
            } else {
                upId = treeResult.getId();
                expressAreaItem.setCpCRegionProvinceId(treeResult.getId());
                expressAreaItem.setCpCRegionProvinceEcode(treeResult.getEcode());
            }
        } else {
            throw new NDSException("省不能为空");
        }
        //市设值
        if (!StringUtils.isBlank(expressAreaItem.getCpCRegionCityEname())) {
            RegionTreeResult treeResult = RegionTreeUtils.getTreeResultByName(treeList, expressAreaItem.getCpCRegionCityEname(), upId);
            if(treeResult == null){
                throw new NDSException(String.format("不存在名称为[%s]的市", expressAreaItem.getCpCRegionCityEname()));
            } else {
                upId = treeResult.getId();
                expressAreaItem.setCpCRegionCityId(treeResult.getId());
                expressAreaItem.setCpCRegionCityEcode(treeResult.getEcode());
            }
        } else {
            throw new NDSException("市不能为空");
        }
        //区设值
        if (!StringUtils.isBlank(expressAreaItem.getCpCRegionAreaEname())) {
            RegionTreeResult treeResult = RegionTreeUtils.getTreeResultByName(treeList, expressAreaItem.getCpCRegionAreaEname(), upId);
            if(treeResult == null){
                throw new NDSException(String.format("不存在名称为[%s]的区", expressAreaItem.getCpCRegionAreaEname()));
            } else {
                upId = treeResult.getId();
                expressAreaItem.setCpCRegionAreaId(treeResult.getId());
                expressAreaItem.setCpCRegionAreaEcode(treeResult.getEcode());
            }
        } else {
            throw new NDSException("区不能为空");
        }
        expressAreaItem.setExclusionArea(StringUtils.isBlank(expressAreaItem.getExclusionArea()) ?
                "" : expressAreaItem.getExclusionArea());
        expressAreaItem.setIsArrive("是".equals(expressAreaItem.getIsArrive()) ? "Y" : "N");
    }
}
