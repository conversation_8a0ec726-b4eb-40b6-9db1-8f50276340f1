package com.jackrain.nea.st.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.jackrain.nea.cpext.model.table.CpCPhyWarehouse;
import com.jackrain.nea.cpext.model.table.CpLogistics;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.ext.sequence.observer.SequenceExec;
import com.jackrain.nea.ext.sequence.util.SequenceGenUtil;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.st.common.StConstant;
import com.jackrain.nea.st.mapper.StCCompensateLogisticsMapper;
import com.jackrain.nea.st.mapper.StCCompensateMapper;
import com.jackrain.nea.st.mapper.StCCompensateWarehouseMapper;
import com.jackrain.nea.st.model.request.CompensateRequest;
import com.jackrain.nea.st.model.table.StCCompensateDO;
import com.jackrain.nea.st.model.table.StCCompensateLogisticsDO;
import com.jackrain.nea.st.model.table.StCCompensateWarehouseDO;
import com.jackrain.nea.st.rpc.RpcCpService;
import com.jackrain.nea.st.utils.JsonUtils;
import com.jackrain.nea.st.utils.StBeanUtils;
import com.jackrain.nea.st.utils.ValueHolderUtils;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

/**
 * @Decription 快递赔付方案
 * <AUTHOR>
 * @Date 2019/3/12 10:49
 */
@Component
@Slf4j
@Transactional
public class CompensateSaveService extends CommandAdapter {

    @Autowired
    private StCCompensateMapper stCCompensateMapper;

    @Autowired
    private StCCompensateLogisticsMapper stCCompensateLogisticsMapper;

    @Autowired
    private StCCompensateWarehouseMapper stCCompensateWarehouseMapper;

    @Autowired
    private RpcCpService cpService;

    SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");//时间格式转换

    /**
     * @param querySession
     * @return
     * @Author: 朱宇军
     * @Date 2019/3/12
     */

    @Override
    public ValueHolder execute(QuerySession querySession) throws NDSException {
        DefaultWebEvent event = querySession.getEvent();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(
                event.getParameterValue("param"), "yyyy-MM-dd HH:mm:ss"),
                Feature.OrderedField);
        Long id = param.getLong("objid");
        JSONObject fixColumn = param.getJSONObject("fixcolumn");

        //3.json转换成对象
        CompensateRequest compensateRequest = JsonUtils.jsonParseClass(fixColumn, CompensateRequest.class);
        if (fixColumn != null) {
            if (id != null && id > 0) {
                return updateCompeansate(querySession, id, compensateRequest);
            } else {
                return addCompensate(querySession, compensateRequest);
            }
        }
        throw new NDSException("当前记录已不存在！");
    }

    /**
     * @param session
     * @param compensateRequest
     * @return
     * @Author: 朱宇军
     * @Date 2019/3/12
     */
    private ValueHolder addCompensate(QuerySession session, CompensateRequest compensateRequest) {
        ValueHolder valueHolder = new ValueHolder();
        StCCompensateDO stCCompensateDO = compensateRequest.getStCCompensateDO();
        if (stCCompensateDO.getEndTime() != null) {
            if (stCCompensateDO.getEndTime().before(stCCompensateDO.getBeginTime())) {
                return ValueHolderUtils.getFailValueHolder("结束日期必须大于生效日期");
            }
        }
        if (stCCompensateDO.getBeginTime() != null) {
            if (stCCompensateDO.getBeginTime().before(new Date())) {
                return ValueHolderUtils.getFailValueHolder("生效日期必须大于当前日期");
            }
        }
        if (stCCompensateDO.getCpCPhyWarehouseId() != null) {
            String[] phyWarehouseIds = stCCompensateDO.getCpCPhyWarehouseId().split(",");
            for(String phyWarehouseId : phyWarehouseIds){
                CpCPhyWarehouse phyWarehouse = cpService.getCpCPhyWahouseDoById(Long.valueOf(phyWarehouseId));
                if (phyWarehouse == null) {
                    return ValueHolderUtils.getFailValueHolder("实体仓不存在或者已经失效，不允许保存！");
                }
            }
        }
        stCCompensateDO.setId(ModelUtil.getSequence(StConstant.TAB_ST_C_COMPENSATE));
        stCCompensateDO.setBillStatus(StConstant.CON_BILL_STATUS_01);
        //基本字段值设置
        StBeanUtils.makeCreateField(stCCompensateDO, session.getUser());//创建信息
        JSONArray errorArray = new JSONArray();
        if ((stCCompensateMapper.insert(stCCompensateDO)) > 0) {
            //单据编号
            JSONObject sequence = new JSONObject();
            sequence.put("ST_C_COMPENSATE", "");
            SequenceExec exec = SequenceGenUtil.preGenerateSequence()
                    .add("SEQ_ST_C_COMPENSATE", sequence, stCCompensateDO.getId(), stCCompensateMapper, "updateSequence");
            exec.exec();
            //快递明细保存
            List<StCCompensateLogisticsDO> logisticsDOList = compensateRequest.getStCCompensateLogisticsDO();
            if (logisticsDOList != null && logisticsDOList.size() > 0) {
                for (StCCompensateLogisticsDO stCCompensateLogisticsDO : logisticsDOList) {

                    if (stCCompensateLogisticsDO.getCpCLogisticsId() != null) {
                        CpLogistics cpLogistics = cpService.queryLogisticsById(stCCompensateLogisticsDO.getCpCLogisticsId());
                        if (cpLogistics == null) {
                            return ValueHolderUtils.getFailValueHolder("快递公司不存在或者已经失效，不允许保存！");
                        }
                    }
                    stCCompensateLogisticsDO.setStCLogisticsCompensateId(compensateRequest.getStCCompensateDO().getId());
                    stCCompensateLogisticsDO.setId(ModelUtil.getSequence(StConstant.TAB_ST_C_COMPENSATE_LOGISTICS));
                    StBeanUtils.makeCreateField(stCCompensateLogisticsDO, session.getUser());
                    if (stCCompensateLogisticsMapper.insert(stCCompensateLogisticsDO) < 1) {
                        return ValueHolderUtils.getFailValueHolder("快递公司插入失败");
                    }
                }
            }
            //实物仓明细保存
            generateCompensateWarehouse(stCCompensateDO.getId(), stCCompensateDO.getCpCPhyWarehouseId(), true, session.getUser());
        } else {
            return ValueHolderUtils.getFailValueHolder("保存失败");
        }
        return ValueHolderUtils.getSuccessValueHolderByMessage(stCCompensateDO.getId(), StConstant.TAB_ST_C_COMPENSATE,"保存成功！");
    }

    /**
     * @param session
     * @param id
     * @param compensateRequest
     * @return
     * @Author: 朱宇军
     * @Date 2019/3/12
     */

    private ValueHolder updateCompeansate(QuerySession session, Long id, CompensateRequest compensateRequest) {
        //1.更新前校验
        StCCompensateDO stCCompensateDO = stCCompensateMapper.selectById(id);
        if (stCCompensateDO == null) {
            return ValueHolderUtils.getFailValueHolder("当前记录已不存在！");
        }
        if (!StConstant.CON_BILL_STATUS_01.equals(stCCompensateDO.getBillStatus())) {
            return ValueHolderUtils.getFailValueHolder("当前记录不是未审核，不允许编辑！！");
        }
        if (StConstant.ISACTIVE_N.equals(stCCompensateDO.getIsactive())) {
            return ValueHolderUtils.getFailValueHolder("当前记录已作废，不允许编辑！");
        }
        if (stCCompensateDO.getCpCPhyWarehouseId() != null) {
            String[] phyWarehouseIds = stCCompensateDO.getCpCPhyWarehouseId().split(",");
            for(String phyWarehouseId : phyWarehouseIds){
                CpCPhyWarehouse phyWarehouse = cpService.getCpCPhyWahouseDoById(Long.valueOf(phyWarehouseId));
                if (phyWarehouse == null) {
                    return ValueHolderUtils.getFailValueHolder("实体仓不存在或者已经失效，不允许保存！");
                }
            }
        }
        stCCompensateDO = compensateRequest.getStCCompensateDO();
        if(stCCompensateDO != null){
            if (stCCompensateDO.getEndTime() != null) {
                if (stCCompensateDO.getEndTime().before(stCCompensateDO.getBeginTime())) {
                    return ValueHolderUtils.getFailValueHolder("结束日期必须大于生效日期");
                }
            }
            if (stCCompensateDO.getBeginTime() != null) {
                if (stCCompensateDO.getBeginTime().before(new Date())) {
                    return ValueHolderUtils.getFailValueHolder("生效日期必须大于当前日期");
                }
            }
            //2.主表最后修改信息字段变更
            stCCompensateDO.setId(id);
            StBeanUtils.makeModifierField(stCCompensateDO, session.getUser());
            //3.更新主表信息
            if (stCCompensateMapper.updateById(stCCompensateDO) <= 0) {
                return ValueHolderUtils.getFailValueHolder("快递赔付主表更新失败");
            }
        }

        //3.快递明细数据变更
        List<StCCompensateLogisticsDO> logisticsDOList = compensateRequest.getStCCompensateLogisticsDO();
        if (logisticsDOList != null && logisticsDOList.size() > 0) {
            for (StCCompensateLogisticsDO stCCompensateLogisticsDO : logisticsDOList) {
                //判断 明细 快递公司 不能重复；
                Long cpCLogisticsId = stCCompensateLogisticsDO.getCpCLogisticsId();//快递公司id
                Long logisticsId = stCCompensateLogisticsDO.getId();
                if (cpCLogisticsId != null) {
                    CpLogistics cpLogistics = cpService.queryLogisticsById(cpCLogisticsId);
                    if (cpLogistics == null) {
                       return ValueHolderUtils.getFailValueHolder("快递公司不存在或者已经失效，不允许保存！");
                    }
                }
                //明细修改
                if (logisticsId != null && logisticsId > 0) {
                    stCCompensateLogisticsDO.setStCLogisticsCompensateId(id);
                    StBeanUtils.makeModifierField(stCCompensateLogisticsDO, session.getUser());
                    if (stCCompensateLogisticsMapper.updateById(stCCompensateLogisticsDO) <= 0) {
                        return ValueHolderUtils.getFailValueHolder("快递明细修改失败");
                    }
                } else {
                    //明细创建
                    if(cpCLogisticsId != null && cpCLogisticsId > 0) {
                        List<StCCompensateLogisticsDO>  StCCompensateLogisticsDOCheck =
                                stCCompensateLogisticsMapper.selectStCCompensateLogisticsDOCheckBylogisid(cpCLogisticsId,stCCompensateDO.getId());
                        if(StCCompensateLogisticsDOCheck != null && StCCompensateLogisticsDOCheck.size()>0){
                            stCCompensateLogisticsMapper.deleteById(StCCompensateLogisticsDOCheck.get(0).getId());
                        }
                        stCCompensateLogisticsDO.setStCLogisticsCompensateId(id);
                        stCCompensateLogisticsDO.setId(ModelUtil.getSequence(StConstant.TAB_ST_C_COMPENSATE_LOGISTICS));
                        StBeanUtils.makeCreateField(stCCompensateLogisticsDO, session.getUser());//创建信息
                        if (stCCompensateLogisticsMapper.insert(stCCompensateLogisticsDO) <= 0) {
                            return ValueHolderUtils.getFailValueHolder("快递明细保存失败");
                        }
                    }
                }
            }
        }
        //4.实物仓数据变更
        if(id !=null && StringUtils.isNotEmpty(stCCompensateDO.getCpCPhyWarehouseId())){
            generateCompensateWarehouse(id, stCCompensateDO.getCpCPhyWarehouseId(), false, session.getUser());
        }
        return ValueHolderUtils.getSuccessValueHolderByMessage(id, StConstant.TAB_ST_C_DISTRIBUTION,"保存成功！");
    }

    private void generateCompensateWarehouse(Long id, String cpCPhyWarehouseId, boolean addFlag, User user){
        String[] phyWarehouseIds = cpCPhyWarehouseId.split(",");
        //删除后批量新增
        if(!addFlag){
            stCCompensateWarehouseMapper.deleteByMasterId(id);
        }
        for(String phyWarehouseId : phyWarehouseIds){
            StCCompensateWarehouseDO compensateWarehouse = new StCCompensateWarehouseDO();
            compensateWarehouse.setStCLogisticsCompensateId(id);
            compensateWarehouse.setCpCPhyWarehouseId(Long.valueOf(phyWarehouseId));

            if (compensateWarehouse.getCpCPhyWarehouseId() != null) {
                CpCPhyWarehouse phyWarehouse = cpService.getCpCPhyWahouseDoById(compensateWarehouse.getCpCPhyWarehouseId());
                if (phyWarehouse != null) {
                    compensateWarehouse.setCpCPhyWarehouseEcode(phyWarehouse.getEcode());
                    compensateWarehouse.setCpCPhyWarehouseEname(phyWarehouse.getEname());
                }
            }
            compensateWarehouse.setId(ModelUtil.getSequence(StConstant.TAB_ST_C_COMPENSATE_WAREHOUSE));
            StBeanUtils.makeCreateField(compensateWarehouse, user);//创建信息
            stCCompensateWarehouseMapper.insert(compensateWarehouse);
        }
    }
}
