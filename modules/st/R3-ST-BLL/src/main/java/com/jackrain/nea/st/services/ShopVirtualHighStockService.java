//package com.jackrain.nea.st.services;
//
//import com.alibaba.fastjson.JSONObject;
//import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
//import com.google.common.collect.Lists;
//import com.google.common.collect.Sets;
//import com.jackrain.nea.sg.oms.api.SgBSyncChannelStockCmd;
//import com.jackrain.nea.sg.oms.model.request.SgChannelProductQueryForSTRequest;
//import com.jackrain.nea.sg.oms.model.result.SgChannelProductQueryForSTResult;
//import com.jackrain.nea.sg.oms.model.table.SgBSyncChannelStock;
//import com.jackrain.nea.st.common.StConstant;
//import com.jackrain.nea.st.mapper.StCVirtualHighStockItemMapper;
//import com.jackrain.nea.st.mapper.StCVirtualHighStockMapper;
//import com.jackrain.nea.st.model.table.*;
//import com.jackrain.nea.st.rpc.RpcSgService;
//import com.jackrain.nea.st.utils.ValueHolderUtils;
//import com.jackrain.nea.sys.domain.ValueHolderV14;
//import com.jackrain.nea.util.ValueHolder;
//import com.jackrain.nea.web.face.User;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.commons.collections4.CollectionUtils;
//import org.apache.commons.lang3.StringUtils;
//import org.apache.dubbo.config.annotation.Reference;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.stereotype.Component;
//
//import java.util.ArrayList;
//import java.util.Date;
//import java.util.List;
//import java.util.Set;
//import java.util.stream.Collectors;
//
///**
// * 虚高库存 同步库存
// */
//@Component
//@Slf4j
//public class ShopVirtualHighStockService {
//
//    @Autowired
//    private StCVirtualHighStockMapper stCVirtualHighStockMapper;
//
//    @Autowired
//    private StCVirtualHighStockItemMapper stCVirtualHighStockItemMapper;
//
//    @Reference(group = "sg", version = "1.0")
//    private SgBSyncChannelStockCmd sgBSyncChannelStockCmd;
//
//    @Autowired
//    private RpcSgService rpcSgService;
//
//
//
//    public ValueHolder syncPlatformStock(Long objid, User user) {
//        //查询明细表信息（需要修改前比例是否为Null，以及不为null的情况）
//        StCShopVirtualHighStockDO stCShopVirtualHighStockDO = stCVirtualHighStockMapper.selectById(objid);
//        List<StCShopVirtualHighStockItemDO> itemList = stCVirtualHighStockItemMapper.selectList(new QueryWrapper<StCShopVirtualHighStockItemDO>()
//                .lambda()
//                .eq(StCShopVirtualHighStockItemDO::getStCShopVirtualHighStockId, objid)
//                .eq(StCShopVirtualHighStockItemDO::getIsactive, StConstant.ISACTIVE_Y));
//        //.isNotNull(StCShopVirtualHighStockItemDO::getSkuId));  //需要准确到平台sku
//        if (!CollectionUtils.isEmpty(itemList)) {
//            //调用同步接口,调用成功后需更新修改前比例
//            List<SgBSyncChannelStock> channelStockList = new ArrayList<>();
//            try {
//                channelStockList = this.assembleParam(stCShopVirtualHighStockDO, itemList);
//                if (!CollectionUtils.isEmpty(channelStockList)) {
//                    ValueHolderV14<List<SgBSyncChannelStock>> valueHolderV14 = sgBSyncChannelStockCmd.insert(channelStockList,user);
//                    if (valueHolderV14.isOK()) {
//                        return ValueHolderUtils.getSuccessValueHolder(objid, StConstant.TAB_ST_C_SHOP_VIRTUAL_HIGH_STOCK,"库存同步成功");
//                    }
//                    log.debug(LogUtil.format("【虚高库存 添加同步库存中间表失败】data:{}", JSONObject.toJSON(channelStockList));
//                    return ValueHolderUtils.getFailValueHolder("同步库存失败");
//                }
//            } catch (Exception e) {
//                e.printStackTrace();
//                log.error(LogUtil.format("【虚高库存 添加同步库存中间表失败】data:{}", channelStockList);
//            }
//        }
//        return ValueHolderUtils.getFailValueHolder("暂无需要同步的库存");
//    }
//
//
//    private boolean checkExpire(StCShopVirtualHighStockDO stCShopVirtualHighStockDO) {
//        if (stCShopVirtualHighStockDO != null && stCShopVirtualHighStockDO.getEndTime() != null && stCShopVirtualHighStockDO.getBeginTime() != null) {
//            //当前时间大于开始，小于结束
//            if (stCShopVirtualHighStockDO.getBeginTime().before(new Date()) && new Date().before(stCShopVirtualHighStockDO.getEndTime())) {
//                return true;
//            }
//        }
//        return false;
//    }
//
//
//    private List<SgBSyncChannelStock> assembleParam(StCShopVirtualHighStockDO stCShopVirtualHighStockDO,List<StCShopVirtualHighStockItemDO> itemList) {
//        List<SgBSyncChannelStock> channelStockList = Lists.newArrayList();
//        for (StCShopVirtualHighStockItemDO stockItemDO : itemList) {
//            SgBSyncChannelStock sgBSyncChannelStock = new SgBSyncChannelStock();
//            sgBSyncChannelStock.setBeginTime(stCShopVirtualHighStockDO.getBeginTime());
//            sgBSyncChannelStock.setEndTime(stCShopVirtualHighStockDO.getEndTime());
//            sgBSyncChannelStock.setCpCShopId(stCShopVirtualHighStockDO.getCpCShopId());
//            sgBSyncChannelStock.setCpCShopTitle(stCShopVirtualHighStockDO.getCpCShopTitle());//店铺名称
//            if (!StringUtils.isEmpty(stockItemDO.getNumberId())) {
//                sgBSyncChannelStock.setNumberId(String.valueOf(stockItemDO.getNumberId()));
//            }
//            if (stockItemDO.getPsCProId() != null) {
//                sgBSyncChannelStock.setPsCProId(stockItemDO.getPsCProId());
//            }
//            if (stockItemDO.getPsCSkuId() != null) {
//                sgBSyncChannelStock.setPsCSkuId(stockItemDO.getPsCSkuId());
//            }
//            if(StringUtils.isNotEmpty(stockItemDO.getPsCSkuEcode())){
//                sgBSyncChannelStock.setPsCSkuEcode(stockItemDO.getPsCSkuEcode());
//            }
//            if (!StringUtils.isEmpty(stockItemDO.getSkuId())) {
//                sgBSyncChannelStock.setSkuId(String.valueOf(stockItemDO.getSkuId()));
//            }
//            sgBSyncChannelStock.setStrategyId(stockItemDO.getStCShopVirtualHighStockId());
//            sgBSyncChannelStock.setType(StConstant.VIRTUALHIGH_STRATEGY_TYPE);
//            sgBSyncChannelStock.setStatus(StConstant.NO_SYNC_STATUS);
//            sgBSyncChannelStock.setStrategyItemId(stockItemDO.getId());
//            channelStockList.add(sgBSyncChannelStock);
//        }
//        return channelStockList;
//    }
//
//    private List<SgBSyncChannelStock> assembleParam(Long objid,List<StCShopVirtualHighStockItemDO> itemList) {
//        List<SgBSyncChannelStock> channelStockList = Lists.newArrayList();
//        StCShopVirtualHighStockDO stCShopVirtualHighStockDO = stCVirtualHighStockMapper.selectById(objid);
//        List<StCShopVirtualHighStockItemDO> skuStrategyItemDOS = Lists.newArrayList();
//        List<StCShopVirtualHighStockItemDO> ptSkuStrategyItemDOS = Lists.newArrayList();
//        Set<String> skuEcodes = Sets.newHashSet();
//        for (StCShopVirtualHighStockItemDO highStockItemDO : itemList) {
//            if (highStockItemDO.getSkuId() == null) {
//                if (StringUtils.isNotEmpty(highStockItemDO.getPsCSkuEcode())) {
//                    skuEcodes.add(highStockItemDO.getPsCSkuEcode());
//                }
//                skuStrategyItemDOS.add(highStockItemDO);
//            } else {
//                ptSkuStrategyItemDOS.add(highStockItemDO);
//            }
//        }
//
//        if (CollectionUtils.isNotEmpty(skuStrategyItemDOS)) {
//            SgChannelProductQueryForSTRequest queryForSTRequest = new SgChannelProductQueryForSTRequest();
//            queryForSTRequest.setPsCSkuEcodeList(Lists.newArrayList(skuEcodes));
//            queryForSTRequest.setCpCShopId(stCShopVirtualHighStockDO.getCpCShopId());
//            List<SgChannelProductQueryForSTResult> results = rpcSgService.queryChannelProduct(queryForSTRequest);
//            for (SgChannelProductQueryForSTResult result : results) {
//                SgBSyncChannelStock sgBSyncChannelStock = new SgBSyncChannelStock();
//                sgBSyncChannelStock.setCpCShopId(result.getCpCShopId());
//                sgBSyncChannelStock.setCpCShopTitle(result.getCpCShopTitle()); //设置店铺名称
//                sgBSyncChannelStock.setBeginTime(stCShopVirtualHighStockDO.getBeginTime());
//                sgBSyncChannelStock.setEndTime(stCShopVirtualHighStockDO.getEndTime());
//                sgBSyncChannelStock.setNumberId(result.getNumiid());
//                sgBSyncChannelStock.setPsCProId(result.getPsCProId());
//                sgBSyncChannelStock.setPsCSkuId(result.getPsCSkuId());
//                sgBSyncChannelStock.setPsCSkuEcode(result.getPsCSkuEcode());//条码code
//                sgBSyncChannelStock.setSkuId(result.getSkuId());
//                sgBSyncChannelStock.setStrategyId(objid);
//                sgBSyncChannelStock.setType(StConstant.VIRTUALHIGH_STRATEGY_TYPE);
//                sgBSyncChannelStock.setStatus(StConstant.NO_SYNC_STATUS);
//                channelStockList.add(sgBSyncChannelStock);
//            }
//        }
//        for (StCShopVirtualHighStockItemDO item : ptSkuStrategyItemDOS) {
//            if (item.getStCShopVirtualHighStockId().equals(objid)) {
//                SgBSyncChannelStock sgBSyncChannelStock = new SgBSyncChannelStock();
//                sgBSyncChannelStock.setBeginTime(stCShopVirtualHighStockDO.getBeginTime());
//                sgBSyncChannelStock.setEndTime(stCShopVirtualHighStockDO.getEndTime());
//                sgBSyncChannelStock.setCpCShopId(stCShopVirtualHighStockDO.getCpCShopId());
//                sgBSyncChannelStock.setCpCShopTitle(stCShopVirtualHighStockDO.getCpCShopTitle());//店铺名称
//                if (!StringUtils.isEmpty(item.getNumberId())) {
//                    sgBSyncChannelStock.setNumberId(String.valueOf(item.getNumberId()));
//                }
//                if (item.getPsCProId() != null) {
//                    sgBSyncChannelStock.setPsCProId(item.getPsCProId());
//                }
//                if (item.getPsCSkuId() != null) {
//                    sgBSyncChannelStock.setPsCSkuId(item.getPsCSkuId());
//                    sgBSyncChannelStock.setPsCSkuEcode(item.getPsCSkuEcode());//条码code
//                }
//                if (!StringUtils.isEmpty(item.getSkuId())) {
//                    sgBSyncChannelStock.setSkuId(String.valueOf(item.getSkuId()));
//                }
//                sgBSyncChannelStock.setStrategyId(item.getStCShopVirtualHighStockId());
//                sgBSyncChannelStock.setType(StConstant.VIRTUALHIGH_STRATEGY_TYPE);
//                sgBSyncChannelStock.setStatus(StConstant.NO_SYNC_STATUS);
//                sgBSyncChannelStock.setStrategyItemId(item.getId());
//                channelStockList.add(sgBSyncChannelStock);
//            }
//        }
//        return channelStockList;
//    }
//}
