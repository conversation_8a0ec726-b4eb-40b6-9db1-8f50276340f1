package com.jackrain.nea.st.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.st.common.StConstant;
import com.jackrain.nea.st.mapper.StCDistributionItemMapper;
import com.jackrain.nea.st.mapper.StCDistributionMapper;
import com.jackrain.nea.st.model.table.StCDistributionDO;
import com.jackrain.nea.st.utils.StBeanUtils;
import com.jackrain.nea.st.utils.ValueHolderUtils;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

/**
 * @Descroption 分销代销策略删除
 * <AUTHOR>
 * @Date 2019/3/7 10:55
 */
@Component
@Slf4j
@Transactional
public class DistributionDelService extends CommandAdapter {
    @Autowired
    private StCDistributionMapper mapper;
    @Autowired
    private StCDistributionItemMapper itemMapper;

    @Override
    public ValueHolder execute(QuerySession querySession) throws NDSException {
        DefaultWebEvent event = querySession.getEvent();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(
                event.getParameterValue("param"), "yyyy-MM-dd HH:mm:ss"), Feature.OrderedField);
        String isDel = param.getString("isdelmtable");
        Long objid = param.getLong("objid");

        //判断主表是否存在
        if (mapper.selectById(objid) == null) {
            return ValueHolderUtils.getFailValueHolder("当前记录已不存在!");
        }
        JSONObject tabitem = param.getJSONObject("tabitem");
        JSONArray errorArray = new JSONArray();
        //判断是删除主表还是明细表单独删除
        if (StConstant.FALSE_STR.equals(isDel)) {
            //单独删除明细
            JSONArray itemArray = tabitem.getJSONArray(StConstant.TAB_ST_C_DISTRIBUTION_ITEM);
            if (itemArray != null && itemArray.size() > 0) {
                delItem(itemArray, errorArray);
            }
        } else {
            //删除主表
            delMain(objid, errorArray);
        }
        return StBeanUtils.getExcuteValueHolder(errorArray);
    }

    /**
     * @param itemArray
     * @param errorArray
     * @return void
     * @Descroption 删除商品明细
     * @Author: 洪艺安
     * @Date 2019/3/14
     */
    public void delItem(JSONArray itemArray, JSONArray errorArray) {
        for (int i = 0; i < itemArray.size(); i++) {
            Long itemid = itemArray.getLong(i);
            int deleteCount = itemMapper.deleteById(itemid);
            //小于0表示数据已经被删除
            if (deleteCount <= 0) {
                errorArray.add(StBeanUtils.getJsonObjectInfo(itemid, "商品明细记录已不存在"));
            }
        }
    }

    /**
     * @param mainId
     * @param errorArray
     * @return void
     * @Descroption 删除主表数据
     * @Author: 洪艺安
     * @Date 2019/3/14
     */
    public void delMain(Long mainId, JSONArray errorArray) {
        //1.数据库删除数据
        StCDistributionDO distribution =  mapper.selectById(mainId);
        int deleteCount = mapper.deleteById(mainId);
        if (deleteCount <= 0) {
            errorArray.add(StBeanUtils.getJsonObjectInfo(mainId, "分销代销策略["+distribution.getBillNo()+"]不存在"));
        }
    }

}
