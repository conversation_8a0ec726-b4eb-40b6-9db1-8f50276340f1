package com.jackrain.nea.st.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.st.mapper.StCAllocationStorageCostStrategyMapper;
import com.jackrain.nea.st.model.table.StCAllocationStorageCostStrategy;
import com.jackrain.nea.st.utils.ValueHolderUtils;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Date 2023/8/16 18:06
 * @Description
 */
@Component
@Slf4j
public class StCAllocationStorageCostDelService extends CommandAdapter {

    @Resource
    private StCAllocationStorageCostStrategyMapper mapper;

    @Override
    public ValueHolder execute(QuerySession querySession) throws NDSException {
        DefaultWebEvent event = querySession.getEvent();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(
                event.getParameterValue("param"), "yyyy-MM-dd HH:mm:ss"), Feature.OrderedField);
        //是否删除主表
        boolean delMainFlag = param.getBoolean("isdelmtable");
        Long id = param.getLong("objid");
        //判断主表是否存在
        StCAllocationStorageCostStrategy strategy = mapper.selectById(id);
        if (strategy == null) {
            return ValueHolderUtils.getFailValueHolder("当前记录不存在！");
        }
        if (delMainFlag && Objects.nonNull(id) && id > 0) {
            //主表删除
            if (mapper.deleteById(id) <= 0) {
                return ValueHolderUtils.getFailValueHolder("删除失败！");
            }
        }
        return ValueHolderUtils.getSuccessValueHolder("删除成功！");
    }
}
