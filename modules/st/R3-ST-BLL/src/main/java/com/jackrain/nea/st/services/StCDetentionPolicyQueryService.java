package com.jackrain.nea.st.services;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.jackrain.nea.st.mapper.StCDetentionPolicyItemMapper;
import com.jackrain.nea.st.mapper.StCDetentionPolicyMapper;
import com.jackrain.nea.st.model.request.StDetentionPolicyRequest;
import com.jackrain.nea.st.model.table.StCDetentionPolicy;
import com.jackrain.nea.st.model.table.StCDetentionPolicyItem;
import com.jackrain.nea.utility.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * @program: r3-st
 * @description:
 * @author: liuwj
 * @create: 2021-06-19 15:42
 **/
@Component
@Slf4j
@Transactional
public class StCDetentionPolicyQueryService {


    @Autowired
    private StCDetentionPolicyMapper stCDetentionPolicyMapper;


    @Autowired
    private StCDetentionPolicyItemMapper stCDetentionPolicyItemMapper;
    /**
     * 根据店铺ID查询有效的卡单策略
     * @param shopId
     * @return
     */
    StDetentionPolicyRequest queryStCDetentionPolicyByShopId(Long shopId){
        if(shopId == null){
            return null;
        }
        StDetentionPolicyRequest stDetentionPolicyRequest =new StDetentionPolicyRequest();
        StCDetentionPolicy stCDetentionPolicy =stCDetentionPolicyMapper.queryStCDetentionPolicyByShopId(shopId);
        if (stCDetentionPolicy != null){
            stDetentionPolicyRequest.setStCDetentionPolicy(stCDetentionPolicy);
            List<StCDetentionPolicyItem> stCDetentionPolicyItemList = stCDetentionPolicyItemMapper.selectList(new QueryWrapper<StCDetentionPolicyItem>().lambda().eq(StCDetentionPolicyItem :: getStCDetentionPolicyId,stCDetentionPolicy.getId()));
            if (CollectionUtils.isNotEmpty(stCDetentionPolicyItemList)){
                stDetentionPolicyRequest.setStCDetentionPolicyItem(stCDetentionPolicyItemList);
            }
        }else {
            return null;
        }
        log.info(LogUtil.format("StCDetentionPolicyQueryService.调用st请求卡单策略为{}"), stDetentionPolicyRequest);
        return stDetentionPolicyRequest;
    }
}
