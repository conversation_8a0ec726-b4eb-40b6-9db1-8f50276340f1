package com.jackrain.nea.st.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.burgeon.r3.sg.channel.model.request.product.SgChannelProductQueryRequest;
import com.burgeon.r3.sg.core.model.table.channel.product.SgBChannelProduct;
import com.burgeon.r3.sg.inf.api.oms.product.SgChannelProductQueryCmd;
import com.google.common.base.Throwables;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.cpext.api.CpLogisticsQueryCmd;
import com.jackrain.nea.cpext.model.table.CpLogistics;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.oc.oms.model.resources.OmsRedisKeyResources;
import com.jackrain.nea.ps.api.PsCProdimItemQueryCmd;
import com.jackrain.nea.ps.api.table.PsCProdimItem;
import com.jackrain.nea.psext.api.PsCSkuQueryCmd;
import com.jackrain.nea.psext.api.PscProQueryForEcodeCmd;
import com.jackrain.nea.psext.model.table.PsCPro;
import com.jackrain.nea.psext.model.table.PsCSku;
import com.jackrain.nea.redis.util.RedisOpsUtil;
import com.jackrain.nea.st.common.StConstant;
import com.jackrain.nea.st.mapper.StCAutoCheckAutoTimeMapper;
import com.jackrain.nea.st.mapper.StCAutoCheckExcludeLogisticsMapper;
import com.jackrain.nea.st.mapper.StCAutoCheckExcludeProductMapper;
import com.jackrain.nea.st.mapper.StCAutoCheckMapper;
import com.jackrain.nea.st.mapper.StCAutoCheckProvinceMapper;
import com.jackrain.nea.st.mapper.StCCustomLabelMapper;
import com.jackrain.nea.st.model.enums.AutocheckLogEnum;
import com.jackrain.nea.st.model.table.StCAutoCheckAutoTimeDO;
import com.jackrain.nea.st.model.table.StCAutoCheckDO;
import com.jackrain.nea.st.model.table.StCAutoCheckExcludeLogisticsDO;
import com.jackrain.nea.st.model.table.StCAutoCheckExcludeProductDO;
import com.jackrain.nea.st.model.table.StCAutoCheckProvinceDO;
import com.jackrain.nea.st.model.table.StCCustomLabelDO;
import com.jackrain.nea.st.model.vo.StCAutoCheckVo;
import com.jackrain.nea.st.model.vo.StCAutocheckExcludeProductImpVo;
import com.jackrain.nea.st.utils.JsonUtils;
import com.jackrain.nea.st.utils.RedisHashCommonUtils;
import com.jackrain.nea.st.utils.StBeanUtils;
import com.jackrain.nea.st.utils.ValueHolderUtils;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * @Descroption 订单自动审核策略-保存逻辑
 * <AUTHOR>
 * @Date 2019/3/12 16:53
 */
@Component
@Slf4j
@Transactional
public class AutoCheckSaveService extends CommandAdapter {
    @Autowired
    private StCAutoCheckMapper stCAutoCheckMapper;
    @Autowired
    private StCAutoCheckProvinceMapper stCAutocheckProvinceMapper;
    @Autowired
    private StCAutoCheckExcludeLogisticsMapper stCAutocheckExcludeLogisticsMapper;
    @Autowired
    private RedisOpsUtil<String, String> redisUtil;
    @Autowired
    private StCCustomLabelMapper stCCustomLabelMapper;
    @Autowired
    private RedisHashCommonUtils redisHashCommonUtils;
    @Autowired
    private StCAutoCheckExcludeProductMapper stCAutoCheckExcludeProductMapper;
    @Autowired
    private StCAutoCheckAutoTimeMapper stCAutoCheckAutoTimeMapper;

    @Autowired
    private CommonLogService commonLogService;

    @Reference(version = "1.0", group = "cp-ext")
    private CpLogisticsQueryCmd cpLogisticsQueryCmd;

    @Reference(version = "1.0", group = "ps-ext")
    private PsCSkuQueryCmd psCSkuQueryCmd;

    @Reference(version = "1.0", group = "ps-ext")
    private PscProQueryForEcodeCmd pscProQueryForEcodeCmd;

    @Reference(version = "1.0", group = "sg")
    private SgChannelProductQueryCmd sgChannelProductQueryCmd;

    @Reference(version = "1.0", group = "ps")
    private PsCProdimItemQueryCmd psCProdimItemQueryCmd;

    @Override
    public ValueHolder execute(QuerySession querySession) {
        DefaultWebEvent event = querySession.getEvent();
        User user = querySession.getUser();
        List<String> nullKeyList = new ArrayList<>();
        JSONObject param = JsonUtils.filterNull((JSONObject) event.getParameterValue("param"), nullKeyList);
        if (log.isDebugEnabled()) {
            log.info(LogUtil.format("AutoCheckSaveService.execute.param>>>{}"), param.toString());
        }
        Long id = param.getLong("objid");
        JSONObject fixColumn = param.getJSONObject("fixcolumn");
        if (fixColumn != null) {
            AutoCheckSaveService bean = ApplicationContextHandle.getBean(AutoCheckSaveService.class);
            if (id != null && id > 0) {
                return bean.updateAutoCheck(user, fixColumn, id, false);
            } else {
                return bean.insertAutoCheck(user, fixColumn);
            }
        }
        throw new NDSException("当前记录已不存在！");
    }

    /**
     * 新增逻辑
     *
     * @param user      操作用户信息
     * @param fixColumn 字段信息
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public ValueHolder insertAutoCheck(User user, JSONObject fixColumn) {
        String postfee = fixColumn.getString(StConstant.TAB_ST_C_AUTOCHECK);
        if (StringUtils.isNotEmpty(postfee)) {
            StCAutoCheckDO stCAutoCheckDO = JSON.parseObject(postfee, StCAutoCheckDO.class);
            stCAutoCheckDO.setId(ModelUtil.getSequence(StConstant.TAB_ST_C_AUTOCHECK));
            String labelId = stCAutoCheckDO.getStCCustomLabelId();
            if(StringUtils.isNotBlank(labelId)){
                String[] split = labelId.split(",");
                List<Long> idList = new ArrayList<>();
                for (String s : split){
                    idList.add(Long.parseLong(s));
                }
                List<StCCustomLabelDO> stCCustomLabelDOS = stCCustomLabelMapper.selectList(new QueryWrapper<StCCustomLabelDO>().lambda().in(StCCustomLabelDO::getId, idList));
                List<String> collect = stCCustomLabelDOS.stream().map(StCCustomLabelDO::getEname).collect(Collectors.toList());
                stCAutoCheckDO.setStCCustomLabelEname(StringUtils.join(collect.toArray(),", "));

            }

            //时间验证
            ValueHolder valueHolder = new ValueHolder();
            if (!checkAutocheckDateTime(stCAutoCheckDO, valueHolder)) {
                return valueHolder;
            }
            //校验订单金额及下单时间
            if (checkData(null, valueHolder, fixColumn.getJSONObject(StConstant.TAB_ST_C_AUTOCHECK))) {
                return valueHolder;
            }
            //排除仓库信息
            if(StringUtils.isNotBlank(stCAutoCheckDO.getCpCPhyWarehouseIds())){
                JSONArray jsonArray = JSONObject.parseArray(stCAutoCheckDO.getCpCPhyWarehouseIds());
                List<String> ids = jsonArray.stream().filter(p -> StringUtils.isNotBlank(((JSONObject) p).getString("ID")))
                        .map(p -> ((JSONObject) p).getString("ID")).collect(Collectors.toList());
                List<String> labels = jsonArray.stream().filter(p -> StringUtils.isNotBlank(((JSONObject) p).getString("Label")))
                        .map(p -> ((JSONObject) p).getString("Label")).collect(Collectors.toList());
                if(CollectionUtils.isNotEmpty(ids)){
                    stCAutoCheckDO.setCpCPhyWarehouseIds(String.join(",",ids));
                }
                if(CollectionUtils.isNotEmpty(labels)){
                    stCAutoCheckDO.setCpCPhyWarehouseEnames(String.join(",",labels));
                }
            }
            //排除业务类型
            if(StringUtils.isNotBlank(stCAutoCheckDO.getStCBusinessTypeIds())){
                JSONArray jsonArray = JSONObject.parseArray(stCAutoCheckDO.getStCBusinessTypeIds());
                List<String> ids = jsonArray.stream().filter(p -> StringUtils.isNotBlank(((JSONObject) p).getString("ID")))
                        .map(p -> ((JSONObject) p).getString("ID")).collect(Collectors.toList());
                List<String> labels = jsonArray.stream().filter(p -> StringUtils.isNotBlank(((JSONObject) p).getString("Label")))
                        .map(p -> ((JSONObject) p).getString("Label")).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(ids)) {
                    stCAutoCheckDO.setStCBusinessTypeIds(String.join(",", ids));
                }
                if (CollectionUtils.isNotEmpty(labels)) {
                    stCAutoCheckDO.setStCBusinessTypeEnames(String.join(",", labels));
                }
            }

            //基本字段值设置
            StBeanUtils.makeCreateField(stCAutoCheckDO, user);
            stCAutoCheckDO.setOwnerename(user.getEname());
            stCAutoCheckDO.setModifierename(user.getEname());

            JSONArray errorArray = new JSONArray();
            if ((stCAutoCheckMapper.insert(stCAutoCheckDO)) > 0) {
                //订单自动审核-排除物流公司
                if (stCAutoCheckDO.getCpCLogisticsId() != null) {
                    String ids = stCAutoCheckDO.getCpCLogisticsId();
                    String[] lIds = ids.split(",");
                    saveAutoCheckExcludeLogistics(user, stCAutoCheckDO.getId(), lIds);
                }

                //订单自动审核-发货人所在省
                if (stCAutoCheckDO.getCpCRegionProvinceId() != null) {
                    String ids = stCAutoCheckDO.getCpCRegionProvinceId();
                    String[] lIds = ids.split(",");
                    saveAutoCheckProvince(user, stCAutoCheckDO.getId(), lIds);
                }
                //排除商品
                String productJson = fixColumn.getString(StConstant.TAB_ST_C_AUTOCHECK_EXCLUDE_PRODUCT);
                if(StringUtils.isNotBlank(productJson)){
                    JSON.parseObject(postfee, StCAutoCheckDO.class);
                    StCAutoCheckExcludeProductDO stCAutoCheckExcludeProductDO =
                            JSON.parseObject(productJson, StCAutoCheckExcludeProductDO.class);
                    saveAutoCheckExcludeProduct(user, stCAutoCheckDO.getId(),stCAutoCheckExcludeProductDO);
                }

                //店铺清除rediskey
                delRedisKey(null);
                return StBeanUtils.getExcuteValueHolder(errorArray);
            } else {
                return ValueHolderUtils.getFailValueHolder("保存失败");
            }
        }
        throw new NDSException("当前表" + StConstant.TAB_ST_C_AUTOCHECK + "不存在！");
    }

    /**
     * 保存排除商品
     * @param user
     * @param id
     * @param stCAutoCheckExcludeProductDO
     */
    private void saveAutoCheckExcludeProduct(User user, Long id, StCAutoCheckExcludeProductDO stCAutoCheckExcludeProductDO) {
        //校验重复性
        Integer proLevel = stCAutoCheckExcludeProductDO.getProLevel();
        List<StCAutoCheckExcludeProductDO> existProList = stCAutoCheckExcludeProductMapper.selectList(new QueryWrapper<StCAutoCheckExcludeProductDO>()
                .lambda().eq(StCAutoCheckExcludeProductDO::getStCAutocheckId, id)
                .eq(StCAutoCheckExcludeProductDO::getProLevel, proLevel)
                .eq(proLevel == 1, StCAutoCheckExcludeProductDO::getPsCProdimItemId, stCAutoCheckExcludeProductDO.getPsCProdimItemId())
                .eq(proLevel == 2, StCAutoCheckExcludeProductDO::getPsCProEcode, stCAutoCheckExcludeProductDO.getPsCProEcode())
                .eq(proLevel == 3, StCAutoCheckExcludeProductDO::getPsCSkuEcode, stCAutoCheckExcludeProductDO.getPsCSkuEcode())
                .eq(proLevel == 4, StCAutoCheckExcludeProductDO::getPlatformNumiid, stCAutoCheckExcludeProductDO.getPlatformNumiid())
                .eq(proLevel == 5, StCAutoCheckExcludeProductDO::getPlatformSkuId, stCAutoCheckExcludeProductDO.getPlatformSkuId())
                .ne(stCAutoCheckExcludeProductDO.getId()!=null,StCAutoCheckExcludeProductDO::getId,stCAutoCheckExcludeProductDO.getId())
        );
        if(CollectionUtils.isNotEmpty(existProList)){
            throw new NDSException("排除商品存在重复配置！");
        }

        stCAutoCheckExcludeProductDO.setId(ModelUtil.getSequence(StConstant.TAB_ST_C_AUTOCHECK_EXCLUDE_PRODUCT));//主键
        stCAutoCheckExcludeProductDO.setStCAutocheckId(id);//主表外键
        StBeanUtils.makeCreateField(stCAutoCheckExcludeProductDO, user);
        stCAutoCheckExcludeProductDO.setModifierename(user.getEname());
        stCAutoCheckExcludeProductDO.setOwnerename(user.getEname());
        stCAutoCheckExcludeProductMapper.insert(stCAutoCheckExcludeProductDO);
    }

    /**
     * 修改逻辑
     *
     * @param user      操作用户信息
     * @param fixColumn 数据信息
     * @param id        主键id
     * @return
     */
    public ValueHolder updateAutoCheck(User user, JSONObject fixColumn, Long id, boolean isBatch) {
        ValueHolder valueHolder = new ValueHolder();
        if (stCAutoCheckMapper.selectCount(new LambdaQueryWrapper<StCAutoCheckDO>().eq(StCAutoCheckDO::getId, id)) <= 0) {
            valueHolder.put("code", ResultCode.FAIL);
            valueHolder.put("message", "当前记录已不存在！");
            valueHolder.put("data", id);
            return valueHolder;
        }
        JSONObject jsonObject = fixColumn.getJSONObject(StConstant.TAB_ST_C_AUTOCHECK);

        //校验订单金额及下单时间
        if (checkData(id, valueHolder, jsonObject)) {
            return valueHolder;
        }

        jsonObject.put("ID", id);
        jsonObject.put("MODIFIEDDATE", new Date());
        jsonObject.put("MODIFIERID", user.getId());
        jsonObject.put("MODIFIERENAME", user.getEname());
        jsonObject.put("MODIFIERNAME", user.getName());
        jsonObject.put("tableName", StConstant.TAB_ST_C_AUTOCHECK);
        String labelId = jsonObject.getString("ST_C_CUSTOM_LABEL_ID");
        if (StringUtils.isNotBlank(labelId)){
            String[] split = labelId.split(",");
            List<Long> idList = new ArrayList<>();
            for (String s : split){
                idList.add(Long.parseLong(s));
            }
            List<StCCustomLabelDO> stCCustomLabelDOS = stCCustomLabelMapper.selectList(new QueryWrapper<StCCustomLabelDO>().lambda().in(StCCustomLabelDO::getId, idList));
            List<String> collect = stCCustomLabelDOS.stream().map(StCCustomLabelDO::getEname).collect(Collectors.toList());
            jsonObject.put("ST_C_CUSTOM_LABEL_ENAME", StringUtils.join(collect.toArray(),", "));
        }

        String postfee = fixColumn.getString(StConstant.TAB_ST_C_AUTOCHECK);
        StCAutoCheckDO stCAutoCheckRequest = JSON.parseObject(postfee, StCAutoCheckDO.class);
        //排除仓库信息
        if(StringUtils.isNotBlank(stCAutoCheckRequest.getCpCPhyWarehouseIds())){
            JSONArray jsonArray = JSONObject.parseArray(stCAutoCheckRequest.getCpCPhyWarehouseIds());
            List<String> ids = jsonArray.stream().filter(p -> StringUtils.isNotBlank(((JSONObject) p).getString("ID")))
                    .map(p -> ((JSONObject) p).getString("ID")).collect(Collectors.toList());
            List<String> labels = jsonArray.stream().filter(p -> StringUtils.isNotBlank(((JSONObject) p).getString("Label")))
                    .map(p -> ((JSONObject) p).getString("Label")).collect(Collectors.toList());
            if(CollectionUtils.isNotEmpty(ids)){
                jsonObject.put("CP_C_PHY_WAREHOUSE_IDS",String.join(",",ids));
            }
            if(CollectionUtils.isNotEmpty(labels)){
                jsonObject.put("CP_C_PHY_WAREHOUSE_ENAMES",String.join(",",labels));
            }
        }
        //排除业务类型
        if(StringUtils.isNotBlank(stCAutoCheckRequest.getStCBusinessTypeIds())){
            JSONArray jsonArray = JSONObject.parseArray(stCAutoCheckRequest.getStCBusinessTypeIds());
            List<String> ids = jsonArray.stream().filter(p -> StringUtils.isNotBlank(((JSONObject) p).getString("ID")))
                    .map(p -> ((JSONObject) p).getString("ID")).collect(Collectors.toList());
            List<String> labels = jsonArray.stream().filter(p -> StringUtils.isNotBlank(((JSONObject) p).getString("Label")))
                    .map(p -> ((JSONObject) p).getString("Label")).collect(Collectors.toList());
            if(CollectionUtils.isNotEmpty(ids)){
                jsonObject.put("ST_C_BUSINESS_TYPE_IDS",String.join(",",ids));
            }
            if(CollectionUtils.isNotEmpty(labels)){
                jsonObject.put("ST_C_BUSINESS_TYPE_ENAMES",String.join(",",labels));
            }
        }

        if (stCAutoCheckMapper.updateAtrributes(jsonObject) > 0) {
            //排除商品
            if (isBatch) {
                if (fixColumn.containsKey(StConstant.TAB_ST_C_AUTOCHECK_EXCLUDE_PRODUCT) && fixColumn.getJSONArray(StConstant.TAB_ST_C_AUTOCHECK_EXCLUDE_PRODUCT) != null) {
                    JSONArray productJsonArr = fixColumn.getJSONArray(StConstant.TAB_ST_C_AUTOCHECK_EXCLUDE_PRODUCT);

                    if (CollectionUtils.isNotEmpty(productJsonArr)) {
                        for (Object productJson : productJsonArr) {
                            StCAutoCheckExcludeProductDO stCAutoCheckExcludeProductDO =
                                    JSON.parseObject(JSONObject.toJSONString(productJson), StCAutoCheckExcludeProductDO.class);
                            saveAutoCheckExcludeProduct(user, id, stCAutoCheckExcludeProductDO);
                        }
                    }
                }
            } else {
                if (fixColumn.containsKey(StConstant.TAB_ST_C_AUTOCHECK_EXCLUDE_PRODUCT) && fixColumn.getJSONObject(StConstant.TAB_ST_C_AUTOCHECK_EXCLUDE_PRODUCT) != null) {
                    JSONObject productJson = fixColumn.getJSONObject(StConstant.TAB_ST_C_AUTOCHECK_EXCLUDE_PRODUCT);
                    StCAutoCheckExcludeProductDO stCAutoCheckExcludeProductDO =
                            JSON.parseObject(JSONObject.toJSONString(productJson), StCAutoCheckExcludeProductDO.class);
                    saveAutoCheckExcludeProduct(user, id, stCAutoCheckExcludeProductDO);
                }
            }

            valueHolder.put("code", ResultCode.SUCCESS);
            valueHolder.put("message", "保存成功");
            valueHolder.put("data", id);
            //店铺清除rediskey
            delRedisKey(id);
            StCAutoCheckDO stCAutoCheckDO = stCAutoCheckMapper.selectById(id);
            delRedisKey(stCAutoCheckDO.getCpCShopId());
            //修改之后，保存操作日志
            saveLog(jsonObject,stCAutoCheckDO,user,id);
        }
        return valueHolder;
    }

    /**
     * 校验
     *
     * @param id
     * @param valueHolder
     * @param jsonObject
     * @return
     */
    private static boolean checkData(Long id, ValueHolder valueHolder, JSONObject jsonObject) {
        Long beginTime = jsonObject.getLong("BEGIN_TIME");
        Long endTime = jsonObject.getLong("END_TIME");
        BigDecimal priceDown = jsonObject.getBigDecimal("LIMIT_PRICE_DOWN");
        BigDecimal priceUp = jsonObject.getBigDecimal("LIMIT_PRICE_UP");
        BigDecimal auditPriceUp = jsonObject.getBigDecimal("AUDIT_PRICE_UP");
        BigDecimal auditPriceDown = jsonObject.getBigDecimal("AUDIT_PRICE_DOWN");
        BigDecimal orderDiscountUp = jsonObject.getBigDecimal("ORDER_DISCOUNT_UP");
        BigDecimal orderDiscountDown = jsonObject.getBigDecimal("ORDER_DISCOUNT_DOWN");
        if (beginTime != null) {
            jsonObject.put("BEGIN_TIME", new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date(beginTime)));
        }
        if (endTime != null) {
            jsonObject.put("END_TIME", new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date(endTime)));
        }
        if (beginTime != null && endTime != null && beginTime > endTime) {
            valueHolder.put("code", ResultCode.FAIL);
            valueHolder.put("message", "保存失败，下单时间上限不能早于下限");
            valueHolder.put("data", id);
            return true;
        }
        if (priceDown != null && priceDown.compareTo(BigDecimal.ZERO) < 0) {
            valueHolder.put("code", ResultCode.FAIL);
            valueHolder.put("message", "保存失败，订单金额下限不能小于0(限制条件)");
            valueHolder.put("data", id);
            return true;
        }
        if (auditPriceDown != null && auditPriceDown.compareTo(BigDecimal.ZERO) < 0) {
            valueHolder.put("code", ResultCode.FAIL);
            valueHolder.put("message", "保存失败，订单金额下限不能小于0(审核条件)");
            valueHolder.put("data", id);
            return true;
        }
        if (priceDown != null && priceUp != null && priceDown.compareTo(priceUp) > 0) {
            valueHolder.put("code", ResultCode.FAIL);
            valueHolder.put("message", "保存失败，订单金额上限不能低于下限(限制条件)");
            valueHolder.put("data", id);
            return true;
        }
        if (auditPriceUp != null && auditPriceDown != null && auditPriceDown.compareTo(auditPriceUp) > 0) {
            valueHolder.put("code", ResultCode.FAIL);
            valueHolder.put("message", "保存失败，订单金额上限不能低于下限(审核条件)");
            valueHolder.put("data", id);
            return true;
        }
        if(orderDiscountUp != null && (BigDecimal.ZERO.compareTo(orderDiscountUp)>0 ||BigDecimal.ONE.compareTo(orderDiscountUp)<0)){
            valueHolder.put("code", ResultCode.FAIL);
            valueHolder.put("message", "保存失败，订单折扣上限必须在[0,1]之间(审核条件)");
            valueHolder.put("data", id);
            return true;
        }

        if(orderDiscountDown != null && (BigDecimal.ZERO.compareTo(orderDiscountDown)>0 ||BigDecimal.ONE.compareTo(orderDiscountDown)<0)){
            valueHolder.put("code", ResultCode.FAIL);
            valueHolder.put("message", "保存失败，订单折扣下限必须在[0,1]之间(审核条件)");
            valueHolder.put("data", id);
            return true;
        }

        if(orderDiscountUp!=null && orderDiscountDown !=null && orderDiscountDown.compareTo(orderDiscountUp)>0){
            valueHolder.put("code", ResultCode.FAIL);
            valueHolder.put("message", "保存失败，订单折扣上限不能低于下限(审核条件)");
            valueHolder.put("data", id);
            return true;
        }
        return false;
    }

    /**
     * @param user 登录用户信息
     * @param id   主键id
     * @param ids
     */
    private void saveAutoCheckProvince(User user, Long id, String[] ids) {
        //先删除
        HashMap<String, Object> map = new HashMap<>();
        map.put("ST_C_AUTOCHECK_ID", id);
        int delete = stCAutocheckProvinceMapper.deleteByMap(map);
        if (delete < 0) {
            throw new NDSException("订单自动审核-收货人所在省保存失败！");
        }
        for (String wareId : ids) {
            if (wareId.isEmpty()) {
                continue;
            }
            StCAutoCheckProvinceDO item = new StCAutoCheckProvinceDO();
            item.setId(ModelUtil.getSequence(StConstant.TAB_ST_C_AUTOCHECK_PROVINCE));//主键
            item.setStCAutocheckId(id);//主表外键
            item.setCpCRegionProvinceId(Long.valueOf(wareId));//省份外键
            StBeanUtils.makeCreateField(item, user);
            item.setModifierename(user.getEname());
            item.setOwnerename(user.getEname());
            int insert = stCAutocheckProvinceMapper.insert(item);
            if (insert < 0) {
                throw new NDSException("操作费方案-收货人所在省保存失败！");
            }
        }
    }

    /**
     * @param user
     * @param id
     * @param ids
     * @return void
     * @Descroption 订单自动审核-排除物流公司-保存逻辑
     * @Author: 郑小龙
     * @Date 2019/3/12
     */
    private void saveAutoCheckExcludeLogistics(User user, Long id, String[] ids) {
        //先删除
        HashMap<String, Object> map = new HashMap<>();
        map.put("ST_C_AUTOCHECK_ID", id);
        int delete = stCAutocheckExcludeLogisticsMapper.deleteByMap(map);
        if (delete < 0) {
            throw new NDSException("订单自动审核-排除物流公司明细保存失败！");
        }
        for (String itemId : ids) {
            if (itemId.isEmpty()) {
                continue;
            }
            StCAutoCheckExcludeLogisticsDO item = new StCAutoCheckExcludeLogisticsDO();
            item.setId(ModelUtil.getSequence(StConstant.TAB_ST_C_AUTOCHECK_EXCLUDE_LOGISTICS));//主键
            item.setStCAutocheckId(id);//主表外键
            item.setCpCLogisticsId(Long.valueOf(itemId));//物流外键
            StBeanUtils.makeCreateField(item, user);
            item.setModifierename(user.getEname());//修改人账号
            item.setOwnerename(user.getEname());//创建人账号
            int insert = stCAutocheckExcludeLogisticsMapper.insert(item);
            if (insert < 0) {
                throw new NDSException("订单自动审核-排除物流公司明细保存失败！");
            }
        }
    }

    /**
     * @param stCAutoCheckDO
     * @param valueHolder
     * @return boolean
     * @Descroption 时间验证
     * @Author: 郑小龙
     * @Date 2019/3/26
     */
    private boolean checkAutocheckDateTime(StCAutoCheckDO stCAutoCheckDO, ValueHolder valueHolder) {
        if (stCAutoCheckDO.getEndTime() != null && stCAutoCheckDO.getBeginTime() != null) {
            if (stCAutoCheckDO.getEndTime().before(stCAutoCheckDO.getBeginTime())) {
                valueHolder.put("code", -1);
                valueHolder.put("message", "下单结束日期不能小于下单开始日期！");
                return false;
            }
        }
        return true;
    }

    /**
     * 根据id查询
     *
     * @param id id
     * @return
     */
    public ValueHolder getAutoCheck(Long id) {
        ValueHolder valueHolder = new ValueHolder();
        try {
            StCAutoCheckDO autoCheckDO = stCAutoCheckMapper.selectById(id);
            StCAutoCheckVo vo = new StCAutoCheckVo();
            BeanUtils.copyProperties(autoCheckDO,vo,new String[] { "cpCPhyWarehouseIds", "stCBusinessTypeIds" });

            if(StringUtils.isNotBlank(autoCheckDO.getCpCPhyWarehouseIds())){
                String cpCPhyWarehouseIds = autoCheckDO.getCpCPhyWarehouseIds();
                String cpCPhyWarehouseEnames = autoCheckDO.getCpCPhyWarehouseEnames();
                String[] splitIds = cpCPhyWarehouseIds.split(",");
                String[] splitEnames = cpCPhyWarehouseEnames.split(",");
                List<StCAutoCheckVo.Label> warehouses = new ArrayList<>();
                for(int i=0;i<splitIds.length;i++){
                    StCAutoCheckVo.Label label = new StCAutoCheckVo.Label();
                    label.setId(Long.valueOf(splitIds[i]));
                    label.setLabel(splitEnames[i]);
                    warehouses.add(label);
                }
                vo.setCpCPhyWarehouseIds(warehouses);
            }

            if(StringUtils.isNotBlank(autoCheckDO.getStCBusinessTypeIds())){
                String stCBusinessTypeIds = autoCheckDO.getStCBusinessTypeIds();
                String stCBusinessTypeEnames = autoCheckDO.getStCBusinessTypeEnames();
                String[] splitIds = stCBusinessTypeIds.split(",");
                String[] splitEnames = stCBusinessTypeEnames.split(",");
                List<StCAutoCheckVo.Label> warehouses = new ArrayList<>();
                for(int i=0;i<splitIds.length;i++){
                    StCAutoCheckVo.Label label = new StCAutoCheckVo.Label();
                    label.setId(Long.valueOf(splitIds[i]));
                    label.setLabel(splitEnames[i]);
                    warehouses.add(label);
                }
                vo.setStCBusinessTypeIds(warehouses);
            }
            List<StCAutoCheckExcludeProductDO> stCAutoCheckExcludeProductDOs =
                    stCAutoCheckExcludeProductMapper.selectList(new QueryWrapper<StCAutoCheckExcludeProductDO>()
                            .lambda().eq(StCAutoCheckExcludeProductDO::getStCAutocheckId, id)
                            .eq(StCAutoCheckExcludeProductDO::getIsactive, StConstant.ISACTIVE_Y));
            vo.setExcludeProducts(stCAutoCheckExcludeProductDOs);
            valueHolder.put("code", ResultCode.SUCCESS);
            valueHolder.put("message", "查询成功");
            valueHolder.put("data", vo);
        }catch (Exception e){
            log.error("自动审核策略查询失败！",e);
            valueHolder.put("code", ResultCode.FAIL);
            valueHolder.put("message", e.getMessage());
        }
        return valueHolder;
    }

    public void delRedisKey(Long id) {
        try {
            String redisKey = OmsRedisKeyResources.buildAutoCheckAllListRedisKey();
            if (redisUtil.strRedisTemplate.hasKey(redisKey)) {
                redisUtil.strRedisTemplate.delete(redisKey);
            }
            if (id != null) {
                String idRedisKey = OmsRedisKeyResources.bulidLockStCAutoCheckKey(id);
                if (redisUtil.strRedisTemplate.hasKey(idRedisKey)) {
                    redisUtil.strRedisTemplate.delete(idRedisKey);
                }
            }

        } catch (Exception e) {
            log.error(LogUtil.format("redis保存自动审单策略异常{}"), Throwables.getStackTraceAsString(e));
        }
    }

    public void saveLog(JSONObject json,StCAutoCheckDO stCAutoCheckDO,User user,Long id) {
        log.info(LogUtil.format("======订单自动审核操作日志保存开始啦==========="));
        json.remove("EFFECTIVE_CONDITION");
        log.info(LogUtil.format("=订单自动审核操作日志入参,json:{},stCAutoCheckDO:{}"), json
                , JSON.toJSONString(stCAutoCheckDO));
        try{
            JSONObject jsonObject = JSONObject.parseObject(JSON.toJSONString(stCAutoCheckDO));
            for (String key : json.keySet()){
                Map<Long, CpLogistics> aDataMap = new HashMap<>();
                Map<Long, CpLogistics> bDataMap = new HashMap<>();
                SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                List<String> aLogisticsList = new ArrayList<>();
                List<String> bLogisticsList = new ArrayList<>();
                String aString = json.getString(key);

                if ("CP_C_LOGISTICS_ID".equals(key)){
                    if (!"".equals(aString)){
                        String[] aSplit = aString.split(",");
                        List<Long> aIdList = new ArrayList<>();
                        for (String s : aSplit){
                            aIdList.add(Long.parseLong(s));
                        }
                        ValueHolderV14<Map<Long, CpLogistics>> mapValueHolderV14 = cpLogisticsQueryCmd.queryLogisticsByIds(aIdList);
                        if (mapValueHolderV14.isOK()){
                            aDataMap = mapValueHolderV14.getData();
                            aDataMap.forEach((k,v) ->{
                                aLogisticsList.add(v.getEname());
                            });
                        }
                    }

                    String bString = jsonObject.getString(key);
                    if (StringUtils.isNotBlank(bString)){
                        String[] bSplit = bString.split(",");
                        List<Long> bIdList = new ArrayList<>();
                        for (String s1 : bSplit){
                            bIdList.add(Long.parseLong(s1));
                        }
                        ValueHolderV14<Map<Long, CpLogistics>> mapValueHolderV141 = cpLogisticsQueryCmd.queryLogisticsByIds(bIdList);
                        if (mapValueHolderV141.isOK()){
                            bDataMap = mapValueHolderV141.getData();
                            bDataMap.forEach((k,v) ->{
                                bLogisticsList.add(v.getEname());
                            });
                        }
                    }
                }
                String aDate = String.valueOf(json.get(key));
                log.info(LogUtil.format("订单自动审核策略，aDate:{}"), aDate);
                if (aDate != null){
                    if (StringUtils.isNotEmpty(AutocheckLogEnum.getNameByValue(key))){
                        String bData = jsonObject.get(key) == null ? "" : String.valueOf(jsonObject.get(key));
                        log.info(LogUtil.format("订单自动审核策略1，bData:{}"), bData);
                        //日期转化
                        if (key.equals("BEGIN_TIME") || key.equals("END_TIME")){
                            if (StringUtils.isNotBlank(bData)){
                                Long aLong = Long.valueOf(bData);
                                bData = simpleDateFormat.format(new Date(aLong));
                                log.info(LogUtil.format("订单自动审核策略，bData:{}"), bData);
                            }
                        }
                        if ("LIMIT_PRICE_DOWN".equals(key) || "LIMIT_PRICE_UP".equals(key) || "ORDER_DISCOUNT_DOWN".equals(key) || "ORDER_DISCOUNT_UP".equals(key)){
                            if(Objects.nonNull(json.getBigDecimal(key))){
                                aDate = json.getBigDecimal(key).setScale(4, BigDecimal.ROUND_HALF_UP).toString();
                            }
                        }
                        if (!aDate.equals(bData)){
                            if ("CP_C_LOGISTICS_ID".equals(key)){
                                commonLogService.addchangeLog(user,"ST_C_AUTOCHECK_LOG","ST_C_AUTOCHECK",id, AutocheckLogEnum.getNameByValue(key),StringUtils.strip(bLogisticsList.toString(),"[]") ,StringUtils.strip(aLogisticsList.toString(),"[]"));
                            }else {
                                commonLogService.addchangeLog(user,"ST_C_AUTOCHECK_LOG","ST_C_AUTOCHECK",id, AutocheckLogEnum.getNameByValue(key),bData ,aDate);
                            }
                        }
                    }
                }
            }
        }catch (Exception ex){
            log.error(LogUtil.format("保存操作日志异常{}"), Throwables.getStackTraceAsString(ex));
            throw new NDSException("保存操作日志异常" + ex.getMessage());
        }

    }

    /**
     * 保存自动审核时间
     * @param param
     * @param user
     * @return
     */
    public int saveAutoCheckTime(StCAutoCheckAutoTimeDO param, User user) {
        if(Objects.isNull(param)){
            throw new NDSException("入参不能为空！");
        }
        if(StringUtils.isBlank(param.getStartTime())){
            throw new NDSException("开始时间不能为空！");
        }
        if(StringUtils.isBlank(param.getEndTime())){
            throw new NDSException("结束时间不能为空！");
        }

        //判断所有时间段不重复
        judgeTime(param.getStartTime(),param.getEndTime(),param.getStCAutocheckId());

        Long id = ModelUtil.getSequence("ST_C_AUTOCHECK_AUTO_TIME");
        param.setId(id);
        StBeanUtils.makeCreateField(param, user);
        int i = stCAutoCheckAutoTimeMapper.insert(param);
        //店铺清除rediskey
        delRedisKey(param.getStCAutocheckId());
        StCAutoCheckDO stCAutoCheckDO = stCAutoCheckMapper.selectById(param.getStCAutocheckId());
        delRedisKey(stCAutoCheckDO.getCpCShopId());
        return i;
    }

    /**
     * 判断时间段不重复
     * @param startDateString
     * @param endDateString
     * @param autoCheckId
     */
    private void judgeTime(String startDateString, String endDateString, Long autoCheckId) {
        Date startDate;
        Date endDate;
        try{
            DateFormat df = new SimpleDateFormat("hh:mm");
            startDate = df.parse(startDateString);
            endDate = df.parse(endDateString);
        }catch (Exception e){
            throw new NDSException("请输入正确的时间！");
        }
        if(startDate.getTime()>=endDate.getTime()){
            throw new NDSException("开始时间必须小于结束时间！");
        }
        List<StCAutoCheckAutoTimeDO> stCAutoCheckAutoTimeDOS =
                stCAutoCheckAutoTimeMapper.selectList(new QueryWrapper<StCAutoCheckAutoTimeDO>()
                        .lambda().eq(StCAutoCheckAutoTimeDO::getStCAutocheckId, autoCheckId));
        if(CollectionUtils.isNotEmpty(stCAutoCheckAutoTimeDOS)){
            for(StCAutoCheckAutoTimeDO timeDo:stCAutoCheckAutoTimeDOS ){
                String startTimeStr = timeDo.getStartTime();
                String endTimeStr = timeDo.getEndTime();
                Date startTime;
                Date endTime;
                try{
                    DateFormat df = new SimpleDateFormat("hh:mm");
                    startTime = df.parse(startTimeStr);
                    endTime = df.parse(endTimeStr);
                }catch (Exception e){
                    throw new NDSException("请输入正确的时间！");
                }
                if(startDate.getTime()<endTime.getTime() && startDate.getTime()>=startTime.getTime()
                        ||endDate.getTime()>startTime.getTime() && endDate.getTime()<=endTime.getTime()){
                    throw new NDSException("开始和结束时间之间不能有交叉重复");
                }
            }
        }
    }

    /**
     *
     * @param user
     * @param fixColumn
     * @param id
     * @return
     */
    public int updateAutoCheckTime(User user, JSONObject fixColumn, Long id) {
        String autoTimeJson = fixColumn.getString("ST_C_AUTOCHECK_AUTO_TIME");
        StCAutoCheckAutoTimeDO stCAutoCheckAutoTimeDO = JSON.parseObject(autoTimeJson, StCAutoCheckAutoTimeDO.class);
        stCAutoCheckAutoTimeMapper.delete(new QueryWrapper<StCAutoCheckAutoTimeDO>().lambda().eq(StCAutoCheckAutoTimeDO::getStCAutocheckId,id));
        stCAutoCheckAutoTimeDO.setStCAutocheckId(id);
        //删除redis
        //店铺清除rediskey
        delRedisKey(id);
        StCAutoCheckDO stCAutoCheckDO = stCAutoCheckMapper.selectById(id);
        delRedisKey(stCAutoCheckDO.getCpCShopId());
        return saveAutoCheckTime(stCAutoCheckAutoTimeDO,user);
    }

    /**
     * 批量插入数据
     * @param checkedDataImpVos
     * @param user
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public List<StCAutoCheckExcludeProductDO> batchSaveData(String objid,List<StCAutocheckExcludeProductImpVo> checkedDataImpVos, User user) {
        if(CollectionUtils.isEmpty(checkedDataImpVos)){
            return null;
        }

        Map<String, Long> skuMap = new HashMap<>();
        List<String> skuCodeList = checkedDataImpVos.stream()
                .filter(p -> StringUtils.isNotBlank(p.getPsCSkuEcode()))
                .map(StCAutocheckExcludeProductImpVo::getPsCSkuEcode)
                .collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(skuCodeList)){
            ValueHolderV14<List<PsCSku>> skuV14 = psCSkuQueryCmd.querySKUByEcodeList(skuCodeList);
            List<PsCSku> skuList = skuV14.getData();
            skuMap = skuList.stream().collect(Collectors.toMap(PsCSku::getEcode, PsCSku::getId, (v1, v2) -> v2));
        }
        Map<String, Long> proMap = new HashMap<>();
        for (StCAutocheckExcludeProductImpVo vo : checkedDataImpVos) {
            if (StringUtils.isNotBlank(vo.getPsCProEcode())) {
                PsCPro psCPro = pscProQueryForEcodeCmd.pscProQueryForEcode(vo.getPsCProEcode());
                if(Objects.nonNull(psCPro)){
                    proMap.put(psCPro.getEcode(), psCPro.getId());
                }
            }
        }

        Map<String, Long> skuIdMap = new HashMap<>();
        List<String> platformSkuIds = checkedDataImpVos.stream()
                .filter(p -> StringUtils.isNotBlank(p.getPlatformSkuId()))
                .map(StCAutocheckExcludeProductImpVo::getPlatformSkuId)
                .collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(platformSkuIds)){
            List<Long> skuIds = new ArrayList<>();
            platformSkuIds.forEach(p -> skuIds.add(Long.valueOf(p)));
            SgChannelProductQueryRequest queryRequest = new SgChannelProductQueryRequest();
            queryRequest.setIdList(skuIds);
            ValueHolderV14<List<SgBChannelProduct>> skuIdV14 = sgChannelProductQueryCmd.queryChannelProduct(queryRequest);
            List<SgBChannelProduct> list = skuIdV14.getData();
            for (SgBChannelProduct product : list) {
                skuIdMap.put(product.getSkuId(), product.getId());
            }
        }

        Map<String, Long> proIdMap = new HashMap<>();
        List<String> platformProIds = checkedDataImpVos.stream()
                .filter(p -> StringUtils.isNotBlank(p.getPlatformNumiid()))
                .map(StCAutocheckExcludeProductImpVo::getPlatformNumiid)
                .collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(platformProIds)){
            SgChannelProductQueryRequest queryRequest1 = new SgChannelProductQueryRequest();
            queryRequest1.setNumiidList(platformProIds);
            ValueHolderV14<List<SgBChannelProduct>> proIdV14 = sgChannelProductQueryCmd.queryChannelProduct(queryRequest1);
            List<SgBChannelProduct> prolist = proIdV14.getData();
            for (SgBChannelProduct product : prolist) {
                proIdMap.put(product.getNumiid(), product.getId());
            }
        }

        Map<String, PsCProdimItem> ProdimMap = new HashMap<>();
        //查询品类
        List<String> prodimNameList = checkedDataImpVos.stream().distinct().filter(p->StringUtils.isNotBlank(p.getPsCProdimItemEname()))
                .map(StCAutocheckExcludeProductImpVo::getPsCProdimItemEname).collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(prodimNameList)){
            ValueHolderV14<List<PsCProdimItem>> listValueHolderV14 = psCProdimItemQueryCmd.queryPsCProdimItemListByNames(prodimNameList, 4l);
            List<PsCProdimItem> PsCProdimItemList = listValueHolderV14.getData();
            ProdimMap = PsCProdimItemList.stream().collect(Collectors.toMap(PsCProdimItem::getEname, Function.identity(), (key1, key2) -> key2));
        }

        List<StCAutoCheckExcludeProductDO> inserts = new ArrayList<>();
        String[] ids = objid.split(",");
        List<StCAutoCheckExcludeProductDO> stCAutoCheckExcludeProductDOS = stCAutoCheckExcludeProductMapper.selectList(new QueryWrapper<StCAutoCheckExcludeProductDO>()
                .lambda()
                .in(StCAutoCheckExcludeProductDO::getStCAutocheckId, ids));
        Map<Long, List<StCAutoCheckExcludeProductDO>> productMap =
                stCAutoCheckExcludeProductDOS.stream().collect(Collectors.groupingBy(StCAutoCheckExcludeProductDO::getStCAutocheckId));
        List<StCAutoCheckExcludeProductDO> returnProduct = new ArrayList<>();
        for (StCAutocheckExcludeProductImpVo vo : checkedDataImpVos) {
            if(StringUtils.isNotBlank(vo.getDesc())){
                continue;
            }
            boolean isReturn = true;
            for(String checkId:ids){
                List<StCAutoCheckExcludeProductDO> existProList =productMap.get(Long.valueOf(checkId));
                StCAutoCheckExcludeProductDO productDO = new StCAutoCheckExcludeProductDO();
                productDO.setStCAutocheckId(Long.valueOf(checkId));
                productDO.setProLevel(vo.getProLevel());
                productDO.setPsCProEcode(vo.getPsCProEcode());
                productDO.setPsCProId(proMap.get(vo.getPsCProEcode()));
                productDO.setPsCSkuEcode(vo.getPsCSkuEcode());
                productDO.setPsCSkuId(skuMap.get(vo.getPsCSkuEcode()));
                productDO.setPlatformNumiid(vo.getPlatformNumiid());
                productDO.setSgBChannelProductProId(proIdMap.get(vo.getPlatformNumiid()));
                productDO.setPlatformSkuId(vo.getPlatformSkuId());
                productDO.setSgBChannelProductSkuId(skuIdMap.get(vo.getPlatformSkuId()));
                PsCProdimItem psCProdimItem = ProdimMap.get(vo.getPsCProdimItemEname());
                if(Objects.nonNull(psCProdimItem)){
                    productDO.setPsCProdimItemEcode(psCProdimItem.getEcode());
                    productDO.setPsCProdimItemId(psCProdimItem.getId());
                }
                Long id = ModelUtil.getSequence("ST_C_AUTOCHECK_EXCLUDE_PRODUCT");
                productDO.setId(id);
                StBeanUtils.makeCreateField(productDO, user);

                boolean b = checkRepeat(existProList, productDO);
                if(b){
                    inserts.add(productDO);
                }else{
                    vo.setDesc("[导入数据重复]");
                }

                //店铺清除rediskey
                delRedisKey(Long.valueOf(checkId));
                StCAutoCheckDO stCAutoCheckDO = stCAutoCheckMapper.selectById(Long.valueOf(checkId));
                if(Objects.nonNull(stCAutoCheckDO)){
                    delRedisKey(stCAutoCheckDO.getCpCShopId());
                }

                if(isReturn){
                    returnProduct.add(productDO);
                    isReturn = false;
                }
            }

        }

//        if(CollectionUtils.isNotEmpty(inserts)){
//            stCAutoCheckExcludeProductMapper.batchInsert(inserts);
//        }
        return returnProduct;
    }

    private boolean checkRepeat(List<StCAutoCheckExcludeProductDO> existProList,StCAutoCheckExcludeProductDO productDO){
        Integer proLevel = productDO.getProLevel();
        if(CollectionUtils.isNotEmpty(existProList)){
            List<StCAutoCheckExcludeProductDO> collect = existProList.stream().filter(p -> p.getProLevel().equals(proLevel)).collect(Collectors.toList());
            if(CollectionUtils.isNotEmpty(collect)){
                for(StCAutoCheckExcludeProductDO existProductDo:collect){
                    switch (proLevel){
                        case 1:
                            if(existProductDo.getPsCProdimItemId().equals(productDO.getPsCProdimItemId())){
                                return false;
                            }
                            break;
                        case 2:
                            if(existProductDo.getPsCProId().equals(productDO.getPsCProId())){
                                return false;
                            }
                            break;
                        case 3:
                            if(existProductDo.getPsCSkuId().equals(productDO.getPsCSkuId())){
                                return false;
                            }
                            break;
                        case 4:
                            if(existProductDo.getPlatformNumiid().equals(productDO.getPlatformNumiid())){
                                return false;
                            }
                            break;
                        case 5:
                            if(existProductDo.getPlatformSkuId().equals(productDO.getPlatformSkuId())){
                                return false;
                            }
                            break;
                    }

                }
            }
        }
        return true;
    }
}
