package com.jackrain.nea.st.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.redis.config.CusRedisTemplate;
import com.jackrain.nea.redis.util.RedisOpsUtil;
import com.jackrain.nea.st.annotation.StOperationLog;
import com.jackrain.nea.st.common.StConstant;
import com.jackrain.nea.st.mapper.StCShopLogisticStrategyItemMapper;
import com.jackrain.nea.st.mapper.StCShopLogisticStrategyMapper;
import com.jackrain.nea.st.model.table.StCShopLogisticStrategy;
import com.jackrain.nea.st.model.table.StCShopLogisticStrategyItem;
import com.jackrain.nea.st.utils.StBeanUtils;
import com.jackrain.nea.st.utils.ValueHolderUtils;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.sys.domain.BaseModel;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * Created with IntelliJ IDEA.
 *
 * @Author: jg.zhan
 * @Date: 2022/6/21 11:31
 * @Description: 店铺物流设置删除
 */
@Component
@Slf4j
@Transactional(rollbackFor = Exception.class)
public class StCShopLogisticStrategyDelService extends CommandAdapter {

    @Autowired
    private StCShopLogisticStrategyMapper stCShopLogisticStrategyMapper;

    @Autowired
    private StCShopLogisticStrategyItemMapper stCShopLogisticStrategyItemMapper;

    @Override
    @StOperationLog(operationType = "DEL", mainTableName = "ST_C_SHOP_LOGISTIC_STRATEGY", itemsTableName = "ST_C_SHOP_LOGISTIC_STRATEGY_ITEM")
    public ValueHolder execute(QuerySession session) {
        DefaultWebEvent event = session.getEvent();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(
                event.getParameterValue("param"), "yyyy-MM-dd HH:mm:ss"),
                Feature.OrderedField);
        log.info(LogUtil.format("##店铺物流设置删除##入参：{}"), param);
        Boolean isdelmtable = param.getBoolean("isdelmtable");
        JSONArray errorArray = new JSONArray();
        Long id = param.getLong("objid");

        StCShopLogisticStrategy stCShopLogisticStrategy = stCShopLogisticStrategyMapper.selectById(id);
        if (stCShopLogisticStrategy == null) {
            return ValueHolderUtils.getFailValueHolder("当前记录已不存在！");
        }

        // 清空redis
        CusRedisTemplate<String, String> redisTemplate = RedisOpsUtil.getStrRedisTemplate();
        redisTemplate.delete("st:shop:express:shopId:" + stCShopLogisticStrategy.getCpCShopId());

        if (isdelmtable) {
            List<StCShopLogisticStrategyItem> stCShopLogisticStrategyItemList = stCShopLogisticStrategyItemMapper
                    .selectList(new QueryWrapper<StCShopLogisticStrategyItem>()
                            .lambda().eq(StCShopLogisticStrategyItem::getStCShopLogisticStrategyId, id)
                            .eq(BaseModel::getIsactive, StConstant.ISACTIVE_Y));
            if (CollectionUtils.isNotEmpty(stCShopLogisticStrategyItemList)) {
                int itemRecord =
                        stCShopLogisticStrategyItemMapper.delete(new LambdaQueryWrapper<StCShopLogisticStrategyItem>()
                        .eq(StCShopLogisticStrategyItem::getStCShopLogisticStrategyId, id));
                if (itemRecord < 1) {
                    throw new NDSException("删除明细失败！");
                }
            }
            int record = stCShopLogisticStrategyMapper.deleteById(id);
            if (record <= 0) {
                errorArray.add("删除主表失败");
            }
        } else {
            Map<Long, String> beforeDelObjMap = new HashMap<>();
            JSONObject tabItem = param.getJSONObject("tabitem");
            if (Objects.nonNull(tabItem) && StringUtils.isNotEmpty(tabItem.getString(StConstant.ST_C_SHOP_LOGISTIC_STRATEGY_ITEM))) {
                List<Long> itemIds =
                        JSONObject.parseArray(tabItem.getString(StConstant.ST_C_SHOP_LOGISTIC_STRATEGY_ITEM), Long.class);
                List<StCShopLogisticStrategyItem> items = stCShopLogisticStrategyItemMapper.selectBatchIds(itemIds);
                if (CollectionUtils.isNotEmpty(items)) {
                    for (StCShopLogisticStrategyItem item : items) {
                        beforeDelObjMap.put(item.getId(), JSON.toJSONString(item));
                    }
                }
                stCShopLogisticStrategyItemMapper.deleteBatchIds(itemIds);
                StBeanUtils.makeModifierField(stCShopLogisticStrategy, session.getUser());
                stCShopLogisticStrategyMapper.updateById(stCShopLogisticStrategy);
                session.setAttribute("beforeDelObjMap", beforeDelObjMap);
            }
        }
        redisTemplate.delete("st:shop:express:shopId:" + stCShopLogisticStrategy.getCpCShopId());

        return StBeanUtils.getExcuteValueHolder(errorArray);
    }

}
