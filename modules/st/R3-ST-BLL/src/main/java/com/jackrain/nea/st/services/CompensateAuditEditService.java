package com.jackrain.nea.st.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.st.common.StConstant;
import com.jackrain.nea.st.mapper.StCCompensateLogisticsMapper;
import com.jackrain.nea.st.mapper.StCCompensateMapper;
import com.jackrain.nea.st.mapper.StCCompensateWarehouseMapper;
import com.jackrain.nea.st.model.table.StCCompensateDO;
import com.jackrain.nea.st.model.table.StCCompensateLogisticsDO;
import com.jackrain.nea.st.model.table.StCCompensateWarehouseDO;
import com.jackrain.nea.st.utils.StBeanUtils;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.HashMap;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2019/4/12
 */
@Component
@Slf4j
@Transactional
public class CompensateAuditEditService extends CommandAdapter {

    @Autowired
    private StCCompensateMapper stCCompensateMapper;

    @Autowired
    private StCCompensateLogisticsMapper stCCompensateLogisticsMapper;

    @Autowired
    private StCCompensateWarehouseMapper stCCompensateWarehouseMapper;

    /**
     * 审核:编辑or新增页面
     *
     * @param querySession 参数封装
     * @return 返回状态
     * @throws NDSException 异常信息
     */
    @Override
    public ValueHolder execute(QuerySession querySession) throws NDSException {
        //1.获取传入参数
        DefaultWebEvent event = querySession.getEvent();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(event.getParameterValue("param"),
                "yyyy-MM-dd HH:mm:ss", SerializerFeature.WriteMapNullValue), Feature.OrderedField);
        if (param == null) {
            throw new NDSException("参数为空！");
        }
        HashMap<Long, Object> errMap = new HashMap<Long, Object>();
        //3.生成审核Json数组
        JSONArray auditArray = StBeanUtils.makeAuditJsonArray(param);
        for (int i = 0; i < auditArray.size(); i++) {
            Long id = auditArray.getLong(i);
            try {
                //4.遍历审核方法
                auditCompensate(id, querySession);
            } catch (Exception e) {
                errMap.put(id, e.getMessage());
            }
        }
        //5.返回ValueHolder对象
        return StBeanUtils.getExcuteValueHolder(auditArray.size(), errMap);
    }

    /**
     * @param id
     * @param querySession
     * @return
     * @Author: 田钦华
     * @Date 2019/4/12
     */

    private void auditCompensate(Long id, QuerySession querySession) {
        StCCompensateDO stCCompensateDO = stCCompensateMapper.selectById(id);
        //校验
        checkCompensate(stCCompensateDO);
        //查询校验商品明细是否有数据
        List<StCCompensateLogisticsDO> logisticsDOList = stCCompensateLogisticsMapper.selectLogisticsBySlaverId(id);
        if (logisticsDOList == null || logisticsDOList.size() == 0) {
            throw new NDSException("当前记录快递无明细，不允许审核！");
        }
        //查询校验经销商明细是否有数据
//        List<StCCompensateWarehouseDO> warehouseDOList = stCCompensateWarehouseMapper.selectWarehouseBySlaverId(id);
//        if (warehouseDOList == null || warehouseDOList.size() == 0) {
//            throw new NDSException("当前记录实物仓无明细，不允许审核！");
//        }
        //更新单据状态
        StBeanUtils.makeModifierField(stCCompensateDO, querySession.getUser());
        stCCompensateDO.setBillStatus(StConstant.CON_BILL_STATUS_02);
        setAuditCommonField(stCCompensateDO, querySession.getUser());
        int updateNum = stCCompensateMapper.updateById(stCCompensateDO);
        if (updateNum < 0) {
            throw new NDSException("审核失败！");
        }
    }

    /**
     * @param stCCompensateDO
     * @return
     * @Author: 田钦华
     * @Date 2019/4/12
     */

    private void checkCompensate(StCCompensateDO stCCompensateDO) {
        if (stCCompensateDO == null) {
            throw new NDSException("当前记录已不存在！");
        } else {
            if (StConstant.CON_BILL_STATUS_02.equals(stCCompensateDO.getBillStatus())) {
                throw new NDSException("当前记录已审核，不允许重复审核！");
            } else if (StConstant.CON_BILL_STATUS_03.equals(stCCompensateDO.getBillStatus())) {
                throw new NDSException("当前记录已作废，不允许审核！");
            }
            //判断生效日期是否小于当前日期
//            if (new Date().after(stCCompensateDO.getBeginTime())) {
//                throw new NDSException("生效时间为【" + stCCompensateDO.getBeginTime() + "】，若生效已下载订单将无法计算价格！");
//            }
        }
    }

    /**
     * @param stCCompensateDO
     * @param user
     * @return
     * @Author: 田钦华
     * @Date 2019/4/12
     */

    private void setAuditCommonField(StCCompensateDO stCCompensateDO, User user) {
        stCCompensateDO.setCheckid(Long.valueOf(user.getId()));
        stCCompensateDO.setCheckename(user.getEname());
        stCCompensateDO.setCheckname(user.getName());
        stCCompensateDO.setChecktime(new Date());
    }
}
