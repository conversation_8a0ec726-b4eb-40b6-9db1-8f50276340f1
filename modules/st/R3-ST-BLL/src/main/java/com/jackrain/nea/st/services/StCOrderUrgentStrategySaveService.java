package com.jackrain.nea.st.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.base.Throwables;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.st.common.StConstant;
import com.jackrain.nea.st.mapper.StCOrderUrgentStrategyMapper;
import com.jackrain.nea.st.model.table.StCOrderUrgentStrategyDO;
import com.jackrain.nea.st.utils.JsonUtils;
import com.jackrain.nea.st.utils.StBeanUtils;
import com.jackrain.nea.st.utils.ValueHolderUtils;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @Package com.jackrain.nea.st.services
 * @Description:
 * @date 2020/8/29 23:05
 */
@Component
@Slf4j
@Transactional
public class StCOrderUrgentStrategySaveService extends CommandAdapter {

    @Autowired(required = false)
    private StCOrderUrgentStrategyMapper mapper;

    @Override
    public ValueHolder execute(QuerySession querySession) throws NDSException {
        DefaultWebEvent event = querySession.getEvent();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(
                event.getParameterValue("param"), "yyyy-MM-dd HH:mm:ss", SerializerFeature.WriteMapNullValue),
                Feature.OrderedField);
        Long id = param.getLong("objid");
        JSONObject fixColumn = param.getJSONObject("fixcolumn");
        if (fixColumn != null) {
            if (id != null && id > 0) {
                return update(querySession, fixColumn, id);
            } else {
                return insert(querySession, fixColumn);
            }
        }
        throw new NDSException("当前记录已不存在！");
    }

    private ValueHolder insert(QuerySession session, JSONObject fixColumn) {
        String tab = StConstant.TAB_ST_C_ORDER_URGENT_STRATEGY;
        JSONObject jsonObject = fixColumn.getJSONObject(tab);
        String str = jsonObject.toString();
        if (StringUtils.isNotEmpty(str)) {
            StCOrderUrgentStrategyDO orderUrgentStrategyDO = JSON.parseObject(str, StCOrderUrgentStrategyDO.class);
            orderUrgentStrategyDO.setId(ModelUtil.getSequence(tab));

            //基本字段值设置
            StBeanUtils.makeCreateField(orderUrgentStrategyDO, session.getUser());//创建信息

            List<String> errList = new ArrayList();
            String[] split = orderUrgentStrategyDO.getCpCShopId().split(",");
            for (String shopId : split) {
                LambdaQueryWrapper<StCOrderUrgentStrategyDO> wrapper = new LambdaQueryWrapper<>();
                /*wrapper.and(i->i.like(StCOrderUrgentStrategyDO::getCpCShopId, ","+shopId+",")
                    .or().likeLeft(StCOrderUrgentStrategyDO::getCpCShopId, ","+shopId)
                    .or().likeRight(StCOrderUrgentStrategyDO::getCpCShopId, shopId+",")
                    .or().eq(StCOrderUrgentStrategyDO::getCpCShopId, shopId));*/
                wrapper.apply("FIND_IN_SET({0}, cp_c_shop_id)",shopId);
                wrapper.eq(StCOrderUrgentStrategyDO::getVpCViptypeId, orderUrgentStrategyDO.getVpCViptypeId());
                List<StCOrderUrgentStrategyDO> list = mapper.selectList(wrapper);
                if (list.size() > 0) {
                    errList.add(shopId);
                }
            }
            if (errList.size() > 0) {
                return ValueHolderUtils.getFailValueHolder("【店铺】与【会员等级】已存在，不允许新增。");
            }
            try {
                int insertResult = mapper.insert(orderUrgentStrategyDO);
                if (insertResult > 0) {
                    return ValueHolderUtils.getSuccessValueHolder(orderUrgentStrategyDO.getId(), tab);
                } else {
                    return ValueHolderUtils.getFailValueHolder("保存失败");
                }
            } catch (Exception ex) {
                log.error(LogUtil.format("StCOrderUrgentStrategySaveService.insert Error{}"), Throwables.getStackTraceAsString(ex));
                return ValueHolderUtils.getFailValueHolder("保存异常");
            }
        }
        throw new NDSException("当前表"+tab+"不存在！");
    }

    private ValueHolder update(QuerySession session, JSONObject fixColumn, Long id) {
        String tab = StConstant.TAB_ST_C_ORDER_URGENT_STRATEGY;
        JSONObject jsonObject = fixColumn.getJSONObject(tab);
        StCOrderUrgentStrategyDO orderUrgentStrategyDO = JsonUtils.jsonParseClass(jsonObject, StCOrderUrgentStrategyDO.class);
        StCOrderUrgentStrategyDO existsDO = mapper.selectById(id);
        if (existsDO == null) {
            throw new NDSException("当前记录已不存在！");
        }
        //update基础字段
        if(orderUrgentStrategyDO != null){
            orderUrgentStrategyDO.setId(id);
            StBeanUtils.makeModifierField(orderUrgentStrategyDO, session.getUser());
            try {
                if (mapper.updateById(orderUrgentStrategyDO) > 0) {
                    return ValueHolderUtils.getSuccessValueHolder(id, tab);
                } else {
                    return ValueHolderUtils.getFailValueHolder("保存失败");
                }
            } catch (Exception ex) {
                log.error(LogUtil.format("StCOrderUrgentStrategySaveService.update Error{}"), Throwables.getStackTraceAsString(ex));
                return ValueHolderUtils.getFailValueHolder("保存异常");
            }
        }
        ValueHolder valueHolder = ValueHolderUtils.getSuccessValueHolder(id, tab);
        return valueHolder;
    }
}
