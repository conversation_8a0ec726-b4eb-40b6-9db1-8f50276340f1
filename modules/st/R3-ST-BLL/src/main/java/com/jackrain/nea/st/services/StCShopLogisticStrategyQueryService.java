package com.jackrain.nea.st.services;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.jackrain.nea.st.common.StConstant;
import com.jackrain.nea.st.mapper.StCShopLogisticStrategyItemMapper;
import com.jackrain.nea.st.mapper.StCShopLogisticStrategyMapper;
import com.jackrain.nea.st.model.request.StCShopLogisticStrategyQueryRequest;
import com.jackrain.nea.st.model.table.StCShopLogisticStrategy;
import com.jackrain.nea.st.model.table.StCShopLogisticStrategyItem;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.sys.constants.ResultCode;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * Created with IntelliJ IDEA.
 *
 * @Author: jg.zhan
 * @Date: 2022/6/20 18:31
 * @Description: 店铺物流设置保存
 */
@Component
@Slf4j
@Transactional(rollbackFor = Exception.class)
public class StCShopLogisticStrategyQueryService extends CommandAdapter {

    @Autowired
    private StCShopLogisticStrategyMapper mapper;

    @Autowired
    private StCShopLogisticStrategyItemMapper itemMapper;

    public ValueHolderV14<List<StCShopLogisticStrategyItem>> queryStCShopLogisticStrategy (StCShopLogisticStrategyQueryRequest request) {
        LambdaQueryWrapper<StCShopLogisticStrategy> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(StCShopLogisticStrategy::getCpCShopId, request.getCpCShopId());
        wrapper.eq(StCShopLogisticStrategy::getIsactive, StConstant.ISACTIVE_Y);
        StCShopLogisticStrategy strategy = mapper.selectOne(wrapper);

        if (strategy == null) {
            return new ValueHolderV14<>(ResultCode.FAIL, "未查询到策略");
        }

        LambdaQueryWrapper<StCShopLogisticStrategyItem> itemWrapper = new LambdaQueryWrapper<>();
        itemWrapper.eq(StCShopLogisticStrategyItem::getStCShopLogisticStrategyId, strategy.getId());
        itemWrapper.in(CollectionUtils.isNotEmpty(request.getPsCProIdList()), StCShopLogisticStrategyItem::getPsCProId, request.getPsCProIdList());
        itemWrapper.in(CollectionUtils.isNotEmpty(request.getPsCProdimIdList()), StCShopLogisticStrategyItem::getPsCProdimId, request.getPsCProdimIdList());
        itemWrapper.eq(StCShopLogisticStrategyItem::getIsactive, StConstant.ISACTIVE_Y);

        List<StCShopLogisticStrategyItem> itemList = itemMapper.selectList(itemWrapper);

        return new ValueHolderV14<>(itemList, ResultCode.SUCCESS, "SUCCESS");
    }

}
