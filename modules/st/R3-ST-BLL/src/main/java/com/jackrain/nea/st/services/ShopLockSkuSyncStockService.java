//package com.jackrain.nea.st.services;
//
//import com.alibaba.fastjson.JSON;
//import com.alibaba.fastjson.JSONObject;
//import com.alibaba.fastjson.parser.Feature;
//import com.alibaba.fastjson.serializer.SerializerFeature;
//import com.aliyun.openservices.shade.org.apache.commons.lang3.StringUtils;
//import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
//import com.google.common.collect.Lists;
//import com.google.common.collect.Sets;
//import com.jackrain.nea.exception.NDSException;
//import com.jackrain.nea.sg.oms.api.SgBSyncChannelStockCmd;
//import com.jackrain.nea.sg.oms.api.SgChannelProductQueryCmd;
//import com.jackrain.nea.sg.oms.common.OmsConstantsIF;
//import com.jackrain.nea.sg.oms.model.request.SgChannelProductQueryForSTRequest;
//import com.jackrain.nea.sg.oms.model.result.SgChannelProductQueryForSTResult;
//import com.jackrain.nea.sg.oms.model.table.SgBSyncChannelStock;
//import com.jackrain.nea.st.common.StConstant;
//import com.jackrain.nea.st.mapper.*;
//import com.jackrain.nea.st.model.table.*;
//import com.jackrain.nea.st.rpc.RpcSgService;
//import com.jackrain.nea.st.utils.ValueHolderUtils;
//import com.jackrain.nea.sys.CommandAdapter;
//import com.jackrain.nea.sys.domain.ValueHolderV14;
//import com.jackrain.nea.util.ValueHolder;
//import com.jackrain.nea.web.face.User;
//import com.jackrain.nea.web.query.DefaultWebEvent;
//import com.jackrain.nea.web.query.QuerySession;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.commons.collections4.CollectionUtils;
//import org.apache.dubbo.config.annotation.Reference;
//import org.springframework.beans.BeanUtils;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.stereotype.Component;
//
//import java.util.Date;
//import java.util.List;
//import java.util.Set;
//
///**
// * <AUTHOR> ShiLong
// * @Date: 2020/7/8 9:22 下午
// * @Desc: 2.店铺锁库条码特殊设置同步库存
// */
//@Component
//@Slf4j
//public class ShopLockSkuSyncStockService extends CommandAdapter {
//
//    @Autowired
//    private StCLockSkuStrategyItemMapper itemMapper;
//    @Autowired
//    private StCLockSkuStrategyMapper mainMapper;
//    @Reference(group = "sg", version = "1.0")
//    private SgBSyncChannelStockCmd sgBSyncChannelStockCmd;
//    @Reference(group = "sg", version = "1.0")
//    private SgChannelProductQueryCmd sgChannelProductQueryCmd;
//    @Autowired
//    private RpcSgService rpcSgService;
//
//    /**
//     * 锁库同步后方可同步
//     */
//    @Override
//    public ValueHolder execute(QuerySession session) {
//        DefaultWebEvent event = session.getEvent();
//        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(
//                event.getParameterValue("param"), "yyyy-MM-dd HH:mm:ss", SerializerFeature.WriteMapNullValue),
//                Feature.OrderedField);
//        log.info(LogUtil.format(".店铺锁库条码特殊设置同步库存入参:{}", param);
//        Long id = param.getLong("objid");
//        if (id == null || id < 0) {
//            throw new NDSException("请选择需要同步的库存策略");
//        }
//        this.syncPlatformStock(id, session.getUser());
//        return ValueHolderUtils.getFailValueHolder("暂无需要同步的库存");
//    }
//
//    /**
//     * 锁库结束后方可同步
//     */
//    public ValueHolder syncPlatformStock(Long objid, User user) {
//        //查询明细表信息
//        StCLockSkuStrategyDO stCLockSkuStrategyDO = mainMapper.selectById(objid);
//        List<StCLockSkuStrategyItemDO> itemList = itemMapper.selectList(new QueryWrapper<StCLockSkuStrategyItemDO>()
//                .lambda()
//                .eq(StCLockSkuStrategyItemDO::getStCLockSkuStrategyId, objid)
//                .eq(StCLockSkuStrategyItemDO::getIsactive, StConstant.ISACTIVE_Y));      //明细必须定位到平台skuid
//        if (!CollectionUtils.isEmpty(itemList)) {
//            //调用同步接口
//            try {
//                List<SgBSyncChannelStock> channelStockList = this.assembleParam(stCLockSkuStrategyDO, itemList);
//                if (!CollectionUtils.isEmpty(channelStockList)) {
//                    ValueHolderV14<List<SgBSyncChannelStock>> valueHolderV14 = sgBSyncChannelStockCmd.insert(channelStockList, user);
//                    if (valueHolderV14.isOK()) {
//                        return ValueHolderUtils.getFailValueHolder("同步库存成功");
//                    }
//                }
//                log.debug(LogUtil.format("【店铺锁库条码特殊设置同步库存失败】data:{}", channelStockList);
//                return ValueHolderUtils.getFailValueHolder("同步库存失败");
//            } catch (Exception e) {
//                e.printStackTrace();
//                log.error(LogUtil.format("【.店铺锁库条码特殊设置同步库存失败】data:{}", itemList);
//            }
//        }
//        return ValueHolderUtils.getFailValueHolder("暂无需要同步的库存");
//    }
//
//
//    /**
//     * 0901 修改
//     *
//     * @param stCLockSkuStrategyDO
//     * @param itemList
//     * @return
//     */
//    private List<SgBSyncChannelStock> assembleParam(StCLockSkuStrategyDO stCLockSkuStrategyDO, List<StCLockSkuStrategyItemDO> itemList) {
//        List<SgBSyncChannelStock> channelStockList = Lists.newArrayList();
//
//        for (StCLockSkuStrategyItemDO strategyItemDO : itemList) {
//            if (stCLockSkuStrategyDO.getId().equals(strategyItemDO.getStCLockSkuStrategyId())) {
//                SgBSyncChannelStock sgBSyncChannelStock = new SgBSyncChannelStock();
//                sgBSyncChannelStock.setCpCShopId(stCLockSkuStrategyDO.getCpCShopId());
//                sgBSyncChannelStock.setCpCShopTitle(stCLockSkuStrategyDO.getCpCShopTitle());//设置店铺名称
//                sgBSyncChannelStock.setBeginTime(stCLockSkuStrategyDO.getLockBtime());
//                sgBSyncChannelStock.setEndTime(stCLockSkuStrategyDO.getLockEtime());
//                if (!StringUtils.isEmpty(strategyItemDO.getPtProId())) {
//                    sgBSyncChannelStock.setNumberId(String.valueOf(strategyItemDO.getPtProId()));
//                }
//                if (strategyItemDO.getPsCProId() != null) {
//                    sgBSyncChannelStock.setPsCProId(strategyItemDO.getPsCProId());
//                }
//                if (strategyItemDO.getPsCSkuId() != null) {
//                    sgBSyncChannelStock.setPsCSkuId(strategyItemDO.getPsCSkuId());
//                }
//                if (StringUtils.isNotEmpty(strategyItemDO.getPsCSkuEcode())) {
//                    sgBSyncChannelStock.setPsCSkuEcode(strategyItemDO.getPsCSkuEcode());
//                }
//                if (!StringUtils.isEmpty(strategyItemDO.getPtSkuId())) {
//                    sgBSyncChannelStock.setSkuId(String.valueOf(strategyItemDO.getPtSkuId()));
//                }
//                sgBSyncChannelStock.setStrategyId(stCLockSkuStrategyDO.getId());
//                sgBSyncChannelStock.setStrategyItemId(strategyItemDO.getId());
//                sgBSyncChannelStock.setType(StConstant.LOCK_SKU_STRATEGY_TYPE);
//                sgBSyncChannelStock.setStatus(StConstant.NO_SYNC_STATUS);
//                channelStockList.add(sgBSyncChannelStock);
//            }
//        }
//        return channelStockList;
//    }
//
//    /**
//     * 店铺锁库条码特殊设置参数封装
//     *
//     * @param objid
//     * @param itemList
//     */
//    private List<SgBSyncChannelStock> assembleParam(Long objid, List<StCLockSkuStrategyItemDO> itemList) {
//        StCLockSkuStrategyDO skuStrategyDO = mainMapper.selectById(objid);
//        List<SgBSyncChannelStock> channelStockList = Lists.newArrayList();
//
//        List<StCLockSkuStrategyItemDO> skuStrategyItemDOS = Lists.newArrayList();
//        List<StCLockSkuStrategyItemDO> ptSkuStrategyItemDOS = Lists.newArrayList();
//        Set<String> skuEcodes = Sets.newHashSet();
//
//        for (StCLockSkuStrategyItemDO lockSkuStrategyItemDO : itemList) {
//            if (lockSkuStrategyItemDO.getPtSkuId() == null) {
//                skuStrategyItemDOS.add(lockSkuStrategyItemDO);
//                if (StringUtils.isNotEmpty(lockSkuStrategyItemDO.getPsCSkuEcode())) {
//                    skuEcodes.add(lockSkuStrategyItemDO.getPsCSkuEcode());
//                }
//            } else {
//                ptSkuStrategyItemDOS.add(lockSkuStrategyItemDO);
//            }
//        }
//
//        if (CollectionUtils.isNotEmpty(skuStrategyItemDOS)) {
//            SgChannelProductQueryForSTRequest queryForSTRequest = new SgChannelProductQueryForSTRequest();
//            queryForSTRequest.setPsCSkuEcodeList(Lists.newArrayList(skuEcodes));
//            queryForSTRequest.setCpCShopId(skuStrategyDO.getCpCShopId());
//            List<SgChannelProductQueryForSTResult> results = rpcSgService.queryChannelProduct(queryForSTRequest);
//            for (SgChannelProductQueryForSTResult result : results) {
//                SgBSyncChannelStock sgBSyncChannelStock = new SgBSyncChannelStock();
//                sgBSyncChannelStock.setCpCShopId(skuStrategyDO.getCpCShopId());
//                sgBSyncChannelStock.setCpCShopTitle(skuStrategyDO.getCpCShopTitle());//设置店铺名称
//                sgBSyncChannelStock.setBeginTime(skuStrategyDO.getLockBtime());
//                sgBSyncChannelStock.setEndTime(skuStrategyDO.getLockEtime());
//                sgBSyncChannelStock.setNumberId(result.getNumiid());
//                sgBSyncChannelStock.setPsCProId(result.getPsCProId());
//                sgBSyncChannelStock.setPsCSkuId(result.getPsCSkuId());
//                sgBSyncChannelStock.setPsCSkuEcode(result.getPsCSkuEcode());//条码code
//                sgBSyncChannelStock.setSkuId(result.getSkuId());
//                sgBSyncChannelStock.setStrategyId(objid);
//                sgBSyncChannelStock.setType(StConstant.LOCK_SKU_STRATEGY_TYPE);
//                sgBSyncChannelStock.setStatus(StConstant.NO_SYNC_STATUS);
//                channelStockList.add(sgBSyncChannelStock);
//            }
//        }
//
//        if (CollectionUtils.isNotEmpty(ptSkuStrategyItemDOS)) {
//            for (StCLockSkuStrategyItemDO item : ptSkuStrategyItemDOS) {
//                if (item.getPtSkuId() != null) {
//                    SgBSyncChannelStock sgBSyncChannelStock = new SgBSyncChannelStock();
//                    sgBSyncChannelStock.setCpCShopId(skuStrategyDO.getCpCShopId());
//                    sgBSyncChannelStock.setCpCShopTitle(skuStrategyDO.getCpCShopTitle());//设置店铺名称
//                    sgBSyncChannelStock.setBeginTime(skuStrategyDO.getLockBtime());
//                    sgBSyncChannelStock.setEndTime(skuStrategyDO.getLockEtime());
//                    if (!StringUtils.isEmpty(item.getPtProId())) {
//                        sgBSyncChannelStock.setNumberId(String.valueOf(item.getPtProId()));
//                    }
//                    if (item.getPsCProId() != null) {
//                        sgBSyncChannelStock.setPsCProId(item.getPsCProId());
//                    }
//                    if (item.getPsCSkuId() != null) {
//                        sgBSyncChannelStock.setPsCSkuId(item.getPsCSkuId());
//                        sgBSyncChannelStock.setPsCSkuEcode(item.getPsCSkuEcode());//条码code
//                    }
//                    if (!StringUtils.isEmpty(item.getPtSkuId())) {
//                        sgBSyncChannelStock.setSkuId(String.valueOf(item.getPtSkuId()));
//                    }
//                    sgBSyncChannelStock.setStrategyId(objid);
//                    sgBSyncChannelStock.setType(StConstant.LOCK_SKU_STRATEGY_TYPE);
//                    sgBSyncChannelStock.setStatus(StConstant.NO_SYNC_STATUS);
//                    channelStockList.add(sgBSyncChannelStock);
//                }
//            }
//        }
//
//        return channelStockList;
//    }
//
//
//    /**
//     * 通用方法
//     *
//     * @param objid
//     * @param endTime       延期专用
//     * @param executeStatus 反审核，作废，删除
//     */
//    public void updateSgBSyncChannelStock(Long objid, Date endTime, Integer executeStatus, User user) {
//        log.debug(LogUtil.format("【锁库同步库存】入参objid:{},endTime{},executeStatus{}:", objid, endTime, executeStatus);
//
//        SgBSyncChannelStock sgBSyncChannelStock = new SgBSyncChannelStock();
//        sgBSyncChannelStock.setType(OmsConstantsIF.SHOP_LOCK_SKU_STOCK_TYPE.intValue());
//        sgBSyncChannelStock.setEndTime(endTime);
//        sgBSyncChannelStock.setStatus(executeStatus.intValue());
//        sgBSyncChannelStock.setStrategyId(objid);
//        try {
//            ValueHolderV14<Integer> valueHolderV14 = sgBSyncChannelStockCmd.updateSgBSyncChannelStock(sgBSyncChannelStock, user);
//            if (!valueHolderV14.isOK()) {
//                log.debug(LogUtil.format("【锁库同步库存失败】objid:{},endTime{},executeStatus{}:", objid, endTime, executeStatus);
//            }
//        } catch (Exception e) {
//            e.printStackTrace();
//            log.debug(LogUtil.format("【锁库同步库存失败】objid:{},endTime{},executeStatus{}:", objid, endTime, executeStatus);
//        }
//    }
//}
