package com.jackrain.nea.st.services;

import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.ps.api.table.PsCProdimItem;
import com.jackrain.nea.st.mapper.StCExpressAllocationItemMapper;
import com.jackrain.nea.st.model.request.ExpressAllocationRequest;
import lombok.extern.slf4j.Slf4j;
import org.assertj.core.util.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component
@Slf4j
public class ExpressAllocationCheckService {
    @Autowired
    private StCExpressAllocationItemMapper stCExpressAllocationItemMapper;

    public List<Long> getLogisticInfoByWarehouseId(Long warehouseId) throws NDSException {
        return stCExpressAllocationItemMapper.getLogisticInfoByWarehouseId(warehouseId);

    }

    public Boolean checkLogisticRules(Long warehouseId, Long logisticId) throws NDSException {
        return !stCExpressAllocationItemMapper.listByWareidAndLogisticsid(warehouseId, logisticId).isEmpty();

    }

    public HashMap<String, Object> selectExpressByPhyWarehouse(ExpressAllocationRequest queryRequest) {
        List<HashMap> list = Lists.newArrayList();
        HashMap<String, Object> map = new HashMap();
        if (null == queryRequest || null == queryRequest.getCpCPhyWarehouseId()) {
            if (log.isDebugEnabled()) {
                log.debug(JSONObject.toJSONString(queryRequest));
            }
            return map;
        }
        Page page = PageHelper.startPage(queryRequest.getOffSet(), queryRequest.getLimit());
        list = stCExpressAllocationItemMapper.selectExpressByPhyWarehouse(queryRequest);
        PageInfo pageInfo = new PageInfo<>(page.getResult());
        map.put("data", list);
        map.put("totleCount", pageInfo.getTotal());//总条数
        map.put("totlePages", pageInfo.getPages());//总页数
        return map;


    }
}
