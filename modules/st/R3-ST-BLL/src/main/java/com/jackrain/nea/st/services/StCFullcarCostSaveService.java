package com.jackrain.nea.st.services;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.st.annotation.StOperationLog;
import com.jackrain.nea.st.common.StConstant;
import com.jackrain.nea.st.mapper.StCFullcarCostItemMapper;
import com.jackrain.nea.st.mapper.StCFullcarCostMapper;
import com.jackrain.nea.st.model.enums.CloseStatusEnum;
import com.jackrain.nea.st.model.enums.SubmitStatusEnum;
import com.jackrain.nea.st.model.table.StCFullcarCost;
import com.jackrain.nea.st.model.table.StCFullcarCostItem;
import com.jackrain.nea.st.utils.JsonUtils;
import com.jackrain.nea.st.utils.StBeanUtils;
import com.jackrain.nea.st.utils.ValueHolderUtils;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2022/6/11 21:29
 */
@Component
@Slf4j
@Transactional
public class StCFullcarCostSaveService extends CommandAdapter {

    @Autowired
    private StCFullcarCostMapper stCFullcarCostMapper;

    @Autowired
    private StCFullcarCostItemMapper stCFullcarCostItemMapper;

    @StOperationLog(mainTableName = "ST_C_FULLCAR_COST", itemsTableName = "ST_C_FULLCAR_COST_ITEM")
    @Override
    public ValueHolder execute(QuerySession querySession) throws NDSException {
        ValueHolder valueHolder = new ValueHolder();
        DefaultWebEvent event = querySession.getEvent();
        List<String> nullKeyList = new ArrayList<>();
        JSONObject param = JsonUtils.filterNull((JSONObject) event.getParameterValue("param"), nullKeyList);
        Long id = param.getLong("objid");
        JSONObject fixColumn = param.getJSONObject("fixcolumn");
        if (log.isInfoEnabled()) {
            log.debug("id:{},fixColumn:{}", id, fixColumn);
        }
        if (fixColumn != null) {
            JSONObject mainMap = fixColumn.getJSONObject(StConstant.TAB_ST_C_FULLCAR_COST);
            JSONArray itemMap = fixColumn.getJSONArray(StConstant.TAB_ST_C_FULLCAR_COST_ITEM);
            if (id != null && id < 0) {
                //新增
                valueHolder = saveFunction(mainMap, querySession);
            } else {
                //编辑
                valueHolder = updateFunction(mainMap, itemMap, valueHolder, querySession, id);
            }
        }
        return valueHolder;
    }


    /**
     * 新增操作
     *
     * @param mainMap      主表数据
     * @param querySession 封装数据
     * @return 返回状态
     */
    private ValueHolder saveFunction(JSONObject mainMap, QuerySession querySession) {
        StCFullcarCost stCFullcarCost = JsonUtils.jsonParseClass(mainMap, StCFullcarCost.class);
        ValueHolder holder;
        if (stCFullcarCost != null) {
            stCFullcarCost.setId(ModelUtil.getSequence(StConstant.TAB_ST_C_FULLCAR_COST));
            StBeanUtils.makeCreateField(stCFullcarCost, querySession.getUser());

            reSetDateTimeRange(stCFullcarCost);
            if (stCFullcarCost.getEndDate().getTime() < stCFullcarCost.getStartDate().getTime()) {
                holder = ValueHolderUtils.getFailValueHolder("结束日期不允许小于开始日期");
                return holder;
            }
            if (stCFullcarCostMapper.insert(stCFullcarCost) <= 0) {
                holder = ValueHolderUtils.getFailValueHolder("保存失败！");
                return holder;
            }
        } else {
            holder = ValueHolderUtils.getFailValueHolder("保存失败！");
            return holder;
        }
        holder = ValueHolderUtils.getSuccessValueHolder(stCFullcarCost.getId(), StConstant.TAB_ST_C_FULLCAR_COST);
        return holder;
    }

    private ValueHolder updateFunction(JSONObject mainMap, JSONArray itemMap, ValueHolder holder, QuerySession querySession, Long objid) {

        StCFullcarCost oldMainData = stCFullcarCostMapper.selectById(objid);
        if (oldMainData == null) {
            holder = ValueHolderUtils.getFailValueHolder("当前记录不存在，不允许编辑！");
            return holder;
        }
        if (CloseStatusEnum.CLOSE.getKey().equals(oldMainData.getCloseStatus())) {
            holder = ValueHolderUtils.getFailValueHolder("当前记录已结案，不允许编辑！");
            return holder;
        }
        if (SubmitStatusEnum.SUBMIT.getKey().equals(oldMainData.getStatus())) {
            holder = ValueHolderUtils.getFailValueHolder("当前记录已提交，不允许编辑！");
            return holder;
        }

        //主表更新，objid就是主表ID
        if (mainMap != null && !mainMap.isEmpty()) {
            StCFullcarCost stCFullcarCost = JsonUtils.jsonParseClass(mainMap, StCFullcarCost.class);
            //校验日期是否违法
            if (stCFullcarCost.getStartDate() == null) {
                stCFullcarCost.setStartDate(oldMainData.getStartDate());
            }
            if (stCFullcarCost.getEndDate() == null) {
                stCFullcarCost.setEndDate(oldMainData.getEndDate());
            }
            reSetDateTimeRange(stCFullcarCost);

            if (stCFullcarCost.getEndDate().getTime() < stCFullcarCost.getStartDate().getTime()) {
                holder = ValueHolderUtils.getFailValueHolder("结束日期不允许小于开始日期");
                return holder;
            }

            stCFullcarCost.setId(objid);
            StBeanUtils.makeModifierField(stCFullcarCost, querySession.getUser());

            if (stCFullcarCostMapper.updateById(stCFullcarCost) < 0) {
                holder = ValueHolderUtils.getFailValueHolder("保存失败！");
                return holder;
            }
        }

        //判断子表数据是否存在
        if (itemMap != null && !itemMap.isEmpty()) {
            List<StCFullcarCostItem> stCFullcarCostItemList = JSON.parseObject(itemMap.toJSONString(),
                    new TypeReference<ArrayList<StCFullcarCostItem>>() {
                    });
            if (CollectionUtils.isNotEmpty(stCFullcarCostItemList)) {
                //检查行明细
                checkList(stCFullcarCostItemList, objid);
                for (StCFullcarCostItem stCFullcarCostItem : stCFullcarCostItemList) {
                    Long id = stCFullcarCostItem.getId();
                    if (id < 0) {
                        stCFullcarCostItem.setId(ModelUtil.getSequence(StConstant.TAB_ST_C_FULLCAR_COST_ITEM));
                        stCFullcarCostItem.setFullcarCostId(objid);
                        StBeanUtils.makeCreateField(stCFullcarCostItem, querySession.getUser());

                        if (stCFullcarCostItemMapper.insert(stCFullcarCostItem) < 0) {
                            holder = ValueHolderUtils.getFailValueHolder("保存失败！");
                            return holder;
                        }
                    } else {
                        //修改原有的行信息 id>0
                        if (stCFullcarCostItemMapper.selectById(id) != null) {
                            StBeanUtils.makeModifierField(stCFullcarCostItem, querySession.getUser());
                            if (stCFullcarCostItemMapper.updateById(stCFullcarCostItem) < 0) {
                                holder = ValueHolderUtils.getFailValueHolder("保存失败！");
                                return holder;
                            }
                        } else {
                            holder = ValueHolderUtils.getFailValueHolder("修改的行明细已被删除！");
                            return holder;
                        }
                    }
                }
                StCFullcarCost stCFullcarCost = new StCFullcarCost();
                stCFullcarCost.setId(objid);
                StBeanUtils.makeModifierField(stCFullcarCost, querySession.getUser());
                stCFullcarCostMapper.updateById(stCFullcarCost);
            } else {
                holder = ValueHolderUtils.getFailValueHolder("明细JSON转换失败，保存失败！");
                return holder;
            }
        }

        holder = ValueHolderUtils.getSuccessValueHolder(objid, StConstant.TAB_ST_C_FULLCAR_COST);
        return holder;
    }

    private void checkList(List<StCFullcarCostItem> stCFullcarCostItemList, Long objid) {
        //为新增和修改数据赋值，用于后续比较
        for (StCFullcarCostItem item : stCFullcarCostItemList) {
            if (item.getId() != null && item.getId() < 0) {
                //新增
                if (item.getStartWeight() == null) {
                    item.setStartWeight(new BigDecimal(0));
                }
                if (item.getEndWeight() == null) {
                    item.setEndWeight(new BigDecimal(0));
                }
            } else {
                //编辑
                if (item.getStartWeight() == null || item.getEndWeight() == null || item.getCityId() == null) {
                    StCFullcarCostItem oldOne = stCFullcarCostItemMapper.selectById(item.getId());
                    if (oldOne == null) {
                        throw new NDSException("修改的明细不存在！");
                    }
                    if (item.getCityId() == null) {
                        item.setCityId(oldOne.getCityId());
                    }
                    if (item.getStartWeight() == null) {
                        item.setStartWeight(oldOne.getStartWeight());
                    }
                    if (item.getEndWeight() == null) {
                        item.setEndWeight(oldOne.getEndWeight());
                    }
                }
            }
            if (item.getStartWeight().compareTo(item.getEndWeight()) == 1) {
                throw new NDSException("起始重量不允许大于结束重量！");
            }
        }
        //当编辑多条时，现在集合内判断重量交叉问题
        if (stCFullcarCostItemList.size() > 1) {
            Map<Long, List<StCFullcarCostItem>> listMap = stCFullcarCostItemList.stream().collect(Collectors.groupingBy(StCFullcarCostItem::getCityId));
            Set<Long> cityIds = listMap.keySet();
            for (Long cityId : cityIds) {
                List<StCFullcarCostItem> itemByCityList = listMap.get(cityId);
                if (itemByCityList != null && itemByCityList.size() > 1) {
                    for (int i = 0; i < itemByCityList.size(); i++) {
                        for (int j = i + 1; j < itemByCityList.size(); j++) {
                            StCFullcarCostItem one = itemByCityList.get(i);
                            StCFullcarCostItem two = itemByCityList.get(j);
                            Boolean flag = checkWeight(one.getStartWeight(), one.getEndWeight(), two.getStartWeight(), two.getEndWeight());
                            if (!flag) {
                                throw new NDSException("录入或修改的明细已存在，不允许重复录入！");
                            }
                        }
                    }
                }
            }
        }
        //与库内数据比较
        for (StCFullcarCostItem costItem : stCFullcarCostItemList) {
            List<StCFullcarCostItem> itemList = stCFullcarCostItemMapper.selectList(new LambdaQueryWrapper<StCFullcarCostItem>()
                    .eq(StCFullcarCostItem::getFullcarCostId, objid)
                    .eq(StCFullcarCostItem::getCityId, costItem.getCityId())
                    .ne(StCFullcarCostItem::getId, costItem.getId()));
            if (CollectionUtils.isNotEmpty(itemList)) {
                for (StCFullcarCostItem item : itemList) {
                    Boolean flag = checkWeight(costItem.getStartWeight(), costItem.getEndWeight(), item.getStartWeight(), item.getEndWeight());
                    if (!flag) {
                        throw new NDSException("录入或修改的明细已存在，不允许重复录入！");
                    }
                }
            }
        }

    }

    private Boolean checkWeight(BigDecimal startWeight, BigDecimal endWeight, BigDecimal startWeight1, BigDecimal endWeight1) {
        if (endWeight.compareTo(endWeight1) == 0) {
            return false;
        } else {
            if (endWeight.compareTo(endWeight1) == 1) {
                //endWeight大，当startWeight小于endWeight1时，返回false
                if (startWeight.compareTo(endWeight1) == -1) {
                    return false;
                }
            } else {
                //endWeight1大，当startWeight1小于endWeight时，返回false
                if (startWeight1.compareTo(endWeight) == -1) {
                    return false;
                }
            }
        }
        return true;
    }

    /**
     * 开始日期取当天0点，结束日期取当天23:59:59
     *
     * @param mainObj 主对象
     */
    private void reSetDateTimeRange(StCFullcarCost mainObj) {
        if (Objects.isNull(mainObj)) {
            return;
        }
        if (Objects.nonNull(mainObj.getStartDate())) {
            mainObj.setStartDate(DateUtil.beginOfDay(mainObj.getStartDate()));
        }
        if (Objects.nonNull(mainObj.getEndDate())) {
            /*mainObj.setEndDate(DateUtil.endOfDay(mainObj.getEndDate()));*/
            DateTime dateTime = DateUtil.endOfDay(mainObj.getEndDate());
            dateTime.setTime(dateTime.getTime() - 999);
            mainObj.setEndDate(dateTime);
        }
    }

}
