package com.jackrain.nea.st.validate;

import com.jackrain.nea.cp.entity.CpCOrgChannelEntity;
import com.jackrain.nea.cpext.model.table.CpShop;
import com.jackrain.nea.psext.model.table.PsCPro;
import com.jackrain.nea.psext.model.table.PsCSku;
import com.jackrain.nea.st.model.table.StCProductStrategyDO;
import com.jackrain.nea.st.model.table.StCProductStrategyItemDO;
import com.jackrain.nea.web.face.User;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * 商品特殊设置-导入校验结果对象（用于传输到后续业务逻辑处理）
 *
 * <AUTHOR>
 * @since 2020-08-24
 * create at : 2020-08-24 17:00
 */
@Data
public class ProductStrategyImpCheckResult implements Serializable {
    /**
     * 当前操作用户
     */
    private User user;

    /**
     * 商品特殊设置策略
     */
    private StCProductStrategyDO productStrategyDO;

    /**
     * 店铺Map：key-店铺编码，value-店铺信息
     */
    private Map<String, CpShop> shopMap;

    /**
     * 渠道Map：key-渠道编码，value-渠道信息
     */
    private Map<String, CpCOrgChannelEntity> channelMap;

    /**
     * 商品Map：key-商品编码，value-商品信息
     */
    private Map<String, PsCPro> proMap;

    /**
     * 条码skuMap：key-条码（sku编码），value-sku信息
     */
    private Map<String, PsCSku> skuMap;

    /**
     * 商品下的条码信息：key-条码，value-sku信息
     */
    private Map<String, List<PsCSku>> proSkuMap;

    /**
     * 已存在的策略明细Map（key：[shopId]_[channelId]_[skuId] value：StCProductStrategyItemDO）
     */
    private Map<String, StCProductStrategyItemDO> existsStrategyItemMap;

    /**
     * 校验过后的错误信息列表
     */
    private List<String> errorList;

    /**
     * 返回校验结果是否成功
     *
     * @return 是否成功
     */
    public boolean isOK() {
        return CollectionUtils.isEmpty(errorList);
    }
}
