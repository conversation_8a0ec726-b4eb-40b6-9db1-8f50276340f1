package com.jackrain.nea.st.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.aliyun.openservices.shade.org.apache.commons.lang3.StringUtils;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.st.common.StConstant;
import com.jackrain.nea.st.mapper.StCDetentionPolicyMapper;
import com.jackrain.nea.st.model.table.StCDetentionPolicy;
import com.jackrain.nea.st.model.table.StCDistributionDO;
import com.jackrain.nea.st.utils.RedisCacheUtil;
import com.jackrain.nea.st.utils.RedisConstant;
import com.jackrain.nea.st.utils.StBeanUtils;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.HashMap;

/**
 * @program: r3-st
 * @description: 预售卡单结案
 * @author: liuwj
 * @create: 2021-06-17 19:31
 **/
@Component
@Slf4j
public class StCDetentionPolicyFinshService extends CommandAdapter {

    @Autowired
    private StCDetentionPolicyMapper mapper;

    @Override
    public ValueHolder execute(QuerySession querySession) throws NDSException {
        //1.获取传入参数
        DefaultWebEvent event = querySession.getEvent();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(event.getParameterValue("param"),
                "yyyy-MM-dd HH:mm:ss", SerializerFeature.WriteMapNullValue), Feature.OrderedField);
        if (param == null) {
            throw new NDSException("参数为空！");
        }
        //2.定义错误记录数
        HashMap<Long, Object> errMap = new HashMap<>();
        //3.生成审核Json数组
        JSONArray finishArray = StBeanUtils.makeFinishJsonArray(param);
        for (int i = 0; i < finishArray.size(); i++) {
            Long id = finishArray.getLong(i);
            try {
                //4.遍历结案方法
                finishDetentionPolicy(id, querySession);
            } catch (Exception e) {
                errMap.put(id, e.getMessage());
            }
        }
        //5.返回ValueHolder对象
        return StBeanUtils.getExcuteValueHolder(finishArray.size(), errMap);
    }

    private void finishDetentionPolicy(Long id, QuerySession querySession) {
        StCDetentionPolicy stCDetentionPolicy = mapper.selectById(id);
        //1.主表校验
        checkDetentionPolicy(stCDetentionPolicy);
        //2.更新单据状态
        StBeanUtils.makeModifierField(stCDetentionPolicy, querySession.getUser());
        stCDetentionPolicy.setEstatus(StConstant.CON_BILL_STATUS_04);
        int updateNum = mapper.updateById(stCDetentionPolicy);
        if (updateNum < 0) {
            throw new NDSException("方案:" + stCDetentionPolicy.getName() + "结案失败！");
        }
        Long shopId = StringUtils.isBlank(stCDetentionPolicy.getCpCShopId()) ? null : Long.valueOf(stCDetentionPolicy.getCpCShopId());
        RedisCacheUtil.delete(shopId, RedisConstant.SHOP_DETENTION_ORDER_ST);
    }

    private void checkDetentionPolicy(StCDetentionPolicy stCDetentionPolicy) {
        if (stCDetentionPolicy == null) {
            throw new NDSException("当前记录已不存在！");
        } else {
            if (StConstant.CON_BILL_STATUS_04.equals(stCDetentionPolicy.getEstatus())) {
                throw new NDSException("当前记录已结案，不允许重复结案！");
            } else if (StConstant.CON_BILL_STATUS_03.equals(stCDetentionPolicy.getEstatus())) {
                throw new NDSException("当前记录已作废，不允许结案！");
            }else if(StConstant.CON_BILL_STATUS_01.equals(stCDetentionPolicy.getEstatus())){
                throw new NDSException("当前记录未审核，不允许结案！");
            }
        }
    }
}
