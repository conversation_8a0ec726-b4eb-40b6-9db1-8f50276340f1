package com.jackrain.nea.st.services;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.google.common.base.Throwables;
import com.google.common.collect.Lists;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.st.config.R3OssConfig;
import com.jackrain.nea.st.mapper.StCSendRuleAddressRentMapper;
import com.jackrain.nea.st.mapper.StCSendRuleMapper;
import com.jackrain.nea.st.model.enums.SendRuleEtype;
import com.jackrain.nea.st.model.result.SendRuleAddressRankResult;
import com.jackrain.nea.st.model.result.SendRuleAddressVipResult;
import com.jackrain.nea.st.model.table.StCSendRuleAddressRentDO;
import com.jackrain.nea.st.model.table.StCSendRuleDO;
import com.jackrain.nea.st.model.table.StCSendRuleWarehouseRateDO;
import com.jackrain.nea.st.utils.ExportUtil;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @author:huang.zaizai
 * @since: 2019/9/4
 * @create at : 2019/9/4 8:37
 */
@Component
@Slf4j
public class SendRuleWarehouseExportService extends CommandAdapter {
    @Autowired
    private ExportUtil exportUtil;
    @Autowired
    private SendRuleQueryService queryService;
    @Autowired
    private StCSendRuleAddressRentMapper rentMapper;
    @Autowired
    private R3OssConfig r3OssConfig;
    @Autowired
    private StCSendRuleMapper stCSendRuleMapper;

    public ValueHolderV14 exportSendRuleWarehouseRank(JSONObject obj, User user) {
        ValueHolderV14 vh = new ValueHolderV14(ResultCode.SUCCESS, "仓库优先级明细导出成功！");
        List<SendRuleAddressRankResult> mainExcelList = Lists.newLinkedList();
        Long id = obj.getLong("objid");
        if (id == null) {
            vh.setCode(ResultCode.FAIL);
            vh.setMessage("参数错误");
            return vh;
        }
        StCSendRuleDO stCSendRuleDO = stCSendRuleMapper.selectById(id);
        if (stCSendRuleDO != null && SendRuleEtype.VIP.getEtype().equals(stCSendRuleDO.getEtype())) {
            return exportSendRuleVipResult(obj, user);
        }

        ValueHolderV14<List<SendRuleAddressRankResult>> tableVh = queryService.queryRankResultTable(obj);
        if (tableVh.isOK()) {
            mainExcelList = tableVh.getData();
        }
        exportUtil.setEndpoint(r3OssConfig.getEndpoint());
        exportUtil.setAccessKeyId(r3OssConfig.getAccessKeyId());
        exportUtil.setAccessKeySecret(r3OssConfig.getAccessKeySecret());
        exportUtil.setBucketName(r3OssConfig.getBucketName());
        if (StringUtils.isEmpty(r3OssConfig.getTimeout())) {
            //如果获取不到apllo配置参数，设置默认过期时间为30分钟
            exportUtil.setTimeout("1800000");
        } else {
            exportUtil.setTimeout(r3OssConfig.getTimeout());
        }
        /**
         *  拼接Excel主表sheet表头字段和列表
         * */
        String mainNames[] = {"省"};
        String orderKeys[] = {"cpCRegionProvinceEname"};
        List<String> mainName = Lists.newArrayList(mainNames);
        List<String> mainKey = Lists.newArrayList(orderKeys);

        //获取仓库信息
        List<StCSendRuleAddressRentDO> rentList = rentMapper.selectList(new QueryWrapper<StCSendRuleAddressRentDO>()
                .lambda().eq(StCSendRuleAddressRentDO::getStCSendRuleId, id));
        if (CollectionUtils.isNotEmpty(rentList)) {
            rentList = rentList.stream().sorted(Comparator.comparing(StCSendRuleAddressRentDO::getId))
                    .collect(Collectors.toList());
        }
        int i = 1;
        for (StCSendRuleAddressRentDO rent : rentList) {
            mainName.add(rent.getCpCPhyWarehouseEname());
            mainKey.add("rank" + i);
            i++;
        }
        XSSFWorkbook hssfWorkbook = new XSSFWorkbook();
        exportUtil.executeSheet(hssfWorkbook, "仓库优先级明细", "", mainName,
                Lists.newArrayList(), mainKey, mainExcelList, false);
        String putMsg = exportUtil.saveFileAndPutOss(hssfWorkbook, "仓库优先级明细导出",
                user, "OSS-Bucket/EXPORT/StCSendRuleAddressRank/");
        if (StringUtils.isEmpty(putMsg)) {
            vh.setCode(ResultCode.FAIL);
            vh.setMessage("仓库优先级明细导出失败");
        }
        vh.setData(putMsg);
        return vh;
    }

    public ValueHolderV14 exportSendRuleWarehouseRate(JSONObject obj, User user) {
        ValueHolderV14 vh = new ValueHolderV14(ResultCode.SUCCESS, "仓库比例明细导出成功！");
        List<StCSendRuleWarehouseRateDO> mainExcelList = Lists.newLinkedList();

        ValueHolderV14<List<StCSendRuleWarehouseRateDO>> tableVh = queryService.queryRateResultTable(obj);
        if (tableVh.isOK()) {
            mainExcelList = tableVh.getData();
        }
        exportUtil.setEndpoint(r3OssConfig.getEndpoint());
        exportUtil.setAccessKeyId(r3OssConfig.getAccessKeyId());
        exportUtil.setAccessKeySecret(r3OssConfig.getAccessKeySecret());
        exportUtil.setBucketName(r3OssConfig.getBucketName());
        if (StringUtils.isEmpty(r3OssConfig.getTimeout())) {
            //如果获取不到apllo配置参数，设置默认过期时间为30分钟
            exportUtil.setTimeout("1800000");
        } else {
            exportUtil.setTimeout(r3OssConfig.getTimeout());
        }
        /**
         *  拼接Excel主表sheet表头字段和列表
         * */
        String mainNames[] = {"仓库", "仓库优先级", "发货比例", "发货数量"};
        String orderKeys[] = {"cpCPhyWarehouseEname", "rank", "sendRate", "qtySend"};
        List<String> mainName = Lists.newArrayList(mainNames);
        List<String> mainKey = Lists.newArrayList(orderKeys);

        XSSFWorkbook hssfWorkbook = new XSSFWorkbook();
        exportUtil.executeSheet(hssfWorkbook, "仓库比例明细", "", mainName,
                Lists.newArrayList(), mainKey, mainExcelList, false);
        String putMsg = exportUtil.saveFileAndPutOss(hssfWorkbook, "仓库比例明细导出",
                user, "OSS-Bucket/EXPORT/StCSendRuleWarehouseRate/");
        if (StringUtils.isEmpty(putMsg)) {
            vh.setCode(ResultCode.FAIL);
            vh.setMessage("仓库比例明细导出失败");
        }
        vh.setData(putMsg);
        return vh;
    }

    /**
     * 派单规则唯品会明细导出
     *
     * @param obj
     * @param user
     * @return
     */
    public ValueHolderV14 exportSendRuleVipResult(JSONObject obj, User user) {
        ValueHolderV14 vh = new ValueHolderV14(ResultCode.SUCCESS, "仓库优先级明细导出成功！");
        List<SendRuleAddressVipResult> vipResultList = Lists.newLinkedList();
        ValueHolderV14<List<SendRuleAddressVipResult>> tableVh = queryService.queryVipWarehouseRankResultTable(obj);
        if (tableVh.isOK()) {
            vipResultList = tableVh.getData();
        }
        exportUtil.setEndpoint(r3OssConfig.getEndpoint());
        exportUtil.setAccessKeyId(r3OssConfig.getAccessKeyId());
        exportUtil.setAccessKeySecret(r3OssConfig.getAccessKeySecret());
        exportUtil.setBucketName(r3OssConfig.getBucketName());
        if (StringUtils.isEmpty(r3OssConfig.getTimeout())) {
            //如果获取不到apllo配置参数，设置默认过期时间为30分钟
            exportUtil.setTimeout("1800000");
        } else {
            exportUtil.setTimeout(r3OssConfig.getTimeout());
        }
        /**
         *  拼接Excel主表sheet表头字段和列表
         * */
        String mainNames[] = {"仓库名称"};
        String orderKeys[] = {"cpCPhyWarehouseEname1"};
        List<String> mainName = Lists.newArrayList(mainNames);
        List<String> mainKey = Lists.newArrayList(orderKeys);

        Long id = obj.getLong("objid");

        //获取仓库信息
        List<StCSendRuleAddressRentDO> rentList = rentMapper.selectList(new QueryWrapper<StCSendRuleAddressRentDO>()
                .lambda().eq(StCSendRuleAddressRentDO::getStCSendRuleId, id));
        if (CollectionUtils.isNotEmpty(rentList)) {
            rentList = rentList.stream().sorted(Comparator.comparing(StCSendRuleAddressRentDO::getId))
                    .collect(Collectors.toList());
        }

        List<SendRuleAddressVipResult> mainExcelList = new ArrayList<>();
        for (StCSendRuleAddressRentDO rent : rentList) {
            SendRuleAddressVipResult ruleAddressVipResult = new SendRuleAddressVipResult();
            ruleAddressVipResult.setCpCPhyWarehouseEcode1(rent.getCpCPhyWarehouseEcode());
            ruleAddressVipResult.setCpCPhyWarehouseEname1(rent.getCpCPhyWarehouseEname());
            int index = 1;
            for (SendRuleAddressVipResult vipResult : vipResultList) {
                String data = vipResult.getWarehouseRank();
                JSONArray warehouseRank = JSONArray.parseArray(data);
                for (Object wareRank : warehouseRank) {
                    JSONObject result = JSONObject.parseObject(wareRank.toString());
                    String rank = result.getString("rank");
                    Long warehouseId = result.getLong("warehouseId");
                    if (rent.getCpCPhyWarehouseId().equals(warehouseId)) {
                        try {
                            Method method = SendRuleAddressVipResult.class.getMethod("setRank" + index, new Class[]{String.class});
                            method.invoke(ruleAddressVipResult, rank);
                        } catch (Exception e) {
                            log.error(LogUtil.format("仓库优先级设置失败{}"), Throwables.getStackTraceAsString(e));
                            e.printStackTrace();
                        }
                        index++;
                    }
                }
            }
            mainExcelList.add(ruleAddressVipResult);
        }

        int i = 1;
        for (SendRuleAddressVipResult vipResult : vipResultList) {
            mainName.add(vipResult.getCpCVipcomWahouseWarehouseName());
            mainKey.add("rank" + i);
            i++;
        }

        XSSFWorkbook hssfWorkbook = new XSSFWorkbook();
        exportUtil.executeSheet(hssfWorkbook, "仓库优先级明细", "", mainName,
                Lists.newArrayList(), mainKey, mainExcelList, false);
        String putMsg = exportUtil.saveFileAndPutOss(hssfWorkbook, "仓库优先级明细导出",
                user, "OSS-Bucket/EXPORT/StCSendRuleAddressRank/");
        if (StringUtils.isEmpty(putMsg)) {
            vh.setCode(ResultCode.FAIL);
            vh.setMessage("仓库优先级明细导出失败");
        }
        vh.setData(putMsg);
        return vh;
    }
}
