package com.jackrain.nea.st.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.st.mapper.StCAppointBatchItemMapper;
import com.jackrain.nea.st.mapper.StCAppointBatchMapper;
import com.jackrain.nea.st.model.enums.AppointDimensionEnum;
import com.jackrain.nea.st.model.request.StCCustomLabelRequest;
import com.jackrain.nea.st.model.table.StCAppointBatch;
import com.jackrain.nea.st.model.table.StCAppointBatchItem;
import com.jackrain.nea.st.model.table.StCCustomLabelDO;
import com.jackrain.nea.st.utils.JsonUtils;
import com.jackrain.nea.st.utils.StBeanUtils;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Author: 黄世新
 * @Date: 2022/6/9 下午1:31
 * @Version 1.0
 */
@Slf4j
@Component
public class StAppointBatchSaveService {

    @Autowired
    private StCAppointBatchMapper appointBatchMapper;
    @Autowired
    private StCAppointBatchItemMapper stCAppointBatchItemMapper;
    @Autowired
    private StAppointBatchSaveService stAppointBatchSaveService;


    public ValueHolder appointBatchSave(QuerySession querySession) {
        DefaultWebEvent event = querySession.getEvent();
        List<String> nullKeyList = new ArrayList<>();
        JSONObject param = JsonUtils.filterNull((JSONObject) event.getParameterValue("param"), nullKeyList);
        Long objid = param.getLong("objid");
        User user = querySession.getUser();
        JSONObject fixColumn = param.getJSONObject("fixcolumn");
        JSONObject st_c_appoint_batch = fixColumn.getJSONObject("ST_C_APPOINT_BATCH");
        JSONArray item = fixColumn.getJSONArray("ST_C_APPOINT_BATCH_ITEM");
        StCAppointBatch appointBatch = JsonUtils.jsonParseClass(st_c_appoint_batch, StCAppointBatch.class);
        List<StCAppointBatchItem> stCAppointBatchItems = JSON.parseArray(item.toJSONString(), StCAppointBatchItem.class);
        if (objid < 0) {
            //新增
            saveAppointBatch(appointBatch, stCAppointBatchItems, user);
        } else {

            //更新

        }

        return null;
    }


    private void saveAppointBatch(StCAppointBatch appointBatch, List<StCAppointBatchItem> stCAppointBatchItems, User user){
        this.checkData(appointBatch, stCAppointBatchItems);
        Long id = ModelUtil.getSequence("ST_C_APPOINT_BATCH");
        appointBatch.setId(id);
        StBeanUtils.makeCreateField(appointBatch, user);
        for (StCAppointBatchItem stCAppointBatchItem : stCAppointBatchItems) {
            stCAppointBatchItem.setStCAppointBatchId(id);
            stCAppointBatchItem.setId(ModelUtil.getSequence("ST_C_APPOINT_BATCH_ITEM"));
            StBeanUtils.makeCreateField(stCAppointBatchItem, user);
        }
        stAppointBatchSaveService.checkData(appointBatch, stCAppointBatchItems);

    }

    @Transactional(rollbackFor = Exception.class)
    public void saveData(StCAppointBatch appointBatch, List<StCAppointBatchItem> stCAppointBatchItems){
        appointBatchMapper.insert(appointBatch);
        stCAppointBatchItemMapper.batchInsert(stCAppointBatchItems);
    }

    private void checkData(StCAppointBatch appointBatch,List<StCAppointBatchItem> stCAppointBatchItems) {
        if (appointBatch == null) {
            throw new NDSException("数据为空");
        }
        Date startTime = appointBatch.getStartTime();
        Date endTime = appointBatch.getEndTime();
        if (startTime == null || endTime == null || startTime.getTime() >= endTime.getTime() ) {
            throw new NDSException("日期选择有误,请重新选择");
        }
        if (CollectionUtils.isEmpty(stCAppointBatchItems)) {
            return;
        }
        Map<Integer, List<StCAppointBatchItem>> itemMap = stCAppointBatchItems.stream().collect(Collectors.groupingBy(StCAppointBatchItem::getAppointDimension));
        for (Integer key : itemMap.keySet()) {
            List<StCAppointBatchItem> batchItems = itemMap.get(key);
            List<String> appointContent = batchItems.stream().map(StCAppointBatchItem::getAppointContent).collect(Collectors.toList());
            List<String> list = appointContent.stream().distinct()
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(list)) {
                throw new NDSException("品项:"+AppointDimensionEnum.getMessage(key)+",重复内容:"+list.toString());

            }

        }

    }


}
