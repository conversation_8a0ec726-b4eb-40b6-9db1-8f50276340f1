//package com.jackrain.nea.st.services;
//
//import com.alibaba.fastjson.JSON;
//import com.alibaba.fastjson.JSONObject;
//import com.alibaba.fastjson.parser.Feature;
//import com.alibaba.fastjson.serializer.SerializerFeature;
//import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
//import com.google.common.collect.Lists;
//import com.google.common.collect.Maps;
//import com.jackrain.nea.cp.api.CpCOrgChannelQueryCmd;
//import com.jackrain.nea.cp.entity.CpCOrgChannelEntity;
//import com.jackrain.nea.cp.entity.CpCOrgChannelItemEntity;
//import com.jackrain.nea.cp.result.CpOrgChannelQueryResult;
//import com.jackrain.nea.cpext.model.table.CpCStore;
//import com.jackrain.nea.exception.NDSException;
//import com.jackrain.nea.model.util.ModelUtil;
//import com.jackrain.nea.oc.oms.model.resources.OmsRedisKeyResources;
//import com.jackrain.nea.redis.util.RedisOpsUtil;
//import com.jackrain.nea.st.annotation.StOperationLog;
//import com.jackrain.nea.st.common.StConstant;
//import com.jackrain.nea.st.mapper.StCSyncStockStrategyChannelMapper;
//import com.jackrain.nea.st.mapper.StCSyncStockStrategyMapper;
//import com.jackrain.nea.st.model.request.SyncStockStrategyRequest;
//import com.jackrain.nea.st.model.table.StCSyncStockStrategyChannelDO;
//import com.jackrain.nea.st.model.table.StCSyncStockStrategyDO;
//import com.jackrain.nea.st.rpc.RpcCpService;
//import com.jackrain.nea.st.utils.JsonUtils;
//import com.jackrain.nea.st.utils.RedisCacheUtil;
//import com.jackrain.nea.st.utils.RedisConstant;
//import com.jackrain.nea.st.utils.RegexUtils;
//import com.jackrain.nea.st.utils.StBeanUtils;
//import com.jackrain.nea.st.utils.ValueHolderUtils;
//import com.jackrain.nea.sys.CommandAdapter;
//import com.jackrain.nea.sys.domain.ValueHolderV14;
//import com.jackrain.nea.util.ApplicationContextHandle;
//import com.jackrain.nea.util.ValueHolder;
//import com.jackrain.nea.utils.AssertUtils;
//import com.jackrain.nea.web.query.DefaultWebEvent;
//import com.jackrain.nea.web.query.QuerySession;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.dubbo.config.annotation.Reference;
//import org.springframework.beans.BeanUtils;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.stereotype.Component;
//import org.springframework.transaction.annotation.Transactional;
//import org.springframework.util.CollectionUtils;
//
//import java.math.BigDecimal;
//import java.util.*;
//import java.util.function.Function;
//import java.util.stream.Collectors;
//
///**
// * @Descroption 店铺同步库存新增保存
// * <AUTHOR>
// * @Date 2019/3/10 10:55
// */
//@Component
//@Slf4j
//@Transactional(rollbackFor = {Exception.class})
//public class SyncStockStrategySaveService extends CommandAdapter {
//    @Autowired
//    private StCSyncStockStrategyMapper mapper;
//    @Autowired
//    private StCSyncStockStrategyChannelMapper itemMapper;
//    @Autowired
//    private RpcCpService rpcCpService;
//    @Reference(group = "cp", version = "1.0")
//    private CpCOrgChannelQueryCmd cpCOrgChannelQueryCmd;
////    @Reference(group = "sg", version = "1.0")
////    private SgChannelStorageDefChangeCmd sgChannelStorageDefChangeCmd;
////    @Reference(group = "sg", version = "1.0")
////    private SgChannelStoreChangeCmd sgChannelStoreChangeCmd;
//    @Autowired
//    private RedisOpsUtil redisUtil;
//    @Autowired
//    private SyncStockStrategyChannelService syncStockStrategyChannelService;
//
//    public final static Integer UPDATE_INSERT_BATCH_SIZE = 500;
//
//    @Override
//    public ValueHolder execute(QuerySession session) {
//        DefaultWebEvent event = session.getEvent();
//        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(
//                event.getParameterValue("param"), "yyyy-MM-dd HH:mm:ss", SerializerFeature.WriteMapNullValue),
//                Feature.OrderedField);
//        log.info(LogUtil.format("SyncStockStrategySaveServiceParam:{}", param);
//        Long id = param.getLong("objid");
//        JSONObject fixColumn = param.getJSONObject("fixcolumn");
//        SyncStockStrategySaveService saveService = ApplicationContextHandle.getBean(SyncStockStrategySaveService.class);
//        //获取新增时，复选框选中的值
//        SyncStockStrategyRequest syncStockStrategyRequest = JsonUtils.jsonParseClass(fixColumn, SyncStockStrategyRequest.class);
//        if (fixColumn != null && id != null) {
//            if (id != -1) {
//                return saveService.updateSyncStockStrategy(session, id, syncStockStrategyRequest);
//            } else {
//                return addDistribution(session, id, syncStockStrategyRequest);
//            }
//        }
//        throw new NDSException("传入数据异常！");
//    }
//
//    private ValueHolder addDistribution(QuerySession session, Long id, SyncStockStrategyRequest syncStockStrategyRequest) {
//        StCSyncStockStrategyDO syncStockStrategy = syncStockStrategyRequest.getSyncStockStrategy();
//        //主表数据业务更新校验
//        //checkSyncStockStrategy(syncStockStrategy);
//        syncStockStrategy.setId(ModelUtil.getSequence(StConstant.TAB_ST_C_SYNC_STOCK_STRATEGY));
//        //主表创建信息更新
//        StBeanUtils.makeCreateField(syncStockStrategy, session.getUser());
//        //主表最后信息修改
//        StBeanUtils.makeModifierField(syncStockStrategy, session.getUser());
//        int insertResult = mapper.insert(syncStockStrategy);
//        AssertUtils.cannot(insertResult <= 0, "保存店铺同步库存策略渠道失败");
//        RedisCacheUtil.delete(syncStockStrategy.getCpCShopId(), RedisConstant.SHOP_SYNC_STOCK_STRATEGY);
//        RedisCacheUtil.deleteAll(RedisConstant.SHOP_SYNC_STOCK_STRATEGY_ALL);
//
//        // 更新库存中心-渠道信息表
//        ValueHolderV14<Integer> vh = changeSbGChannelDef(syncStockStrategy);
//        AssertUtils.isTrue(vh.isOK(), vh.getMessage());
//
//        List<StCSyncStockStrategyChannelDO> itemList = syncStockStrategyRequest.getSyncStockStrategyChannelList();
//        if (!CollectionUtils.isEmpty(itemList)) {
//            //存储需要新增的实体仓id，作为校验是否存在相同的id，如果是则不能保存成功
//            Map map = new HashMap<>();
//            Map<Long, CpCStore> storeMap = new HashMap<>();
//            //待插入集合
//            List<StCSyncStockStrategyChannelDO> insertList = Lists.newArrayList();
//            for (StCSyncStockStrategyChannelDO stSyncStockStrategyChannel : itemList) {
//                //新增明细保存渠道逻辑仓
//                addItemUpdateChannelStore(stSyncStockStrategyChannel, syncStockStrategy, map, storeMap, id);
//                //冗余主表店铺信息
//                stSyncStockStrategyChannel.setCpCShopId(syncStockStrategy.getCpCShopId());
//                stSyncStockStrategyChannel.setCpCShopEcode(syncStockStrategy.getCpCShopEcode());
//                stSyncStockStrategyChannel.setCpCShopTitle(syncStockStrategy.getCpCShopTitle());
//                StBeanUtils.makeCreateField(stSyncStockStrategyChannel, session.getUser());
//                insertList.add(stSyncStockStrategyChannel);
//            }
//
//            boolean result = syncStockStrategyChannelService.saveBatch(insertList, UPDATE_INSERT_BATCH_SIZE);
//            AssertUtils.isTrue(result, "保存店铺同步库存策略渠道明细失败！");
//        }
//        return ValueHolderUtils.getSuccessValueHolder(syncStockStrategy.getId(), StConstant.TAB_ST_C_SYNC_STOCK_STRATEGY);
//    }
//
//    /**
//     * @param syncStockStrategy            同步库存策略主表
//     * @param syncStockStrategyChannelItem 同步库存策略子表
//     * @param channelItemList              渠道仓子表
//     * @param map
//     * @param storeMap
//     * @return void
//     * <AUTHOR>
//     * @Description //TODO
//     * @Date 16:20 2020/6/11
//     **/
//    public void updateChannelStore(StCSyncStockStrategyDO syncStockStrategy,
//                                   StCSyncStockStrategyChannelDO syncStockStrategyChannelItem,
//                                   List<CpCOrgChannelItemEntity> channelItemList,
//                                   Map map, Map<Long, CpCStore> storeMap) {
//        for (CpCOrgChannelItemEntity cpCOrgChannelItemEntity : channelItemList) {
//            //逻辑仓id
//            Long storeArrStr = cpCOrgChannelItemEntity.getCpCStoreId();
//            //渠道仓id
//            Long cpCOrgChannelId = cpCOrgChannelItemEntity.getCpCOrgChannelId();
//            //增加校验：新增时不能新增相同的实体仓;返回值如果为true：代表存在已经保存的实体仓
//            if (map != null) {
//                if (checkCSyncStockStrategyItemId(storeArrStr, cpCOrgChannelId, syncStockStrategy.getId())) {
//                    throw new NDSException("该供货仓已存在,请选择其他供货渠道！");
//                }
//                //初始map为null，所以为空；第二次的时候，如果存在第一次的key，value，则代表需要保存的实体仓中存在重复id，则不能保存成功
//                if (map.get(storeArrStr) != null) {
//                    throw new NDSException("存在重复的供货仓,请选择其他供货渠道！");
//                }
//                //将id加入map
//                map.put(storeArrStr, storeArrStr);
//            }
//
//            CpCOrgChannelItemEntity newItem = new CpCOrgChannelItemEntity();
//            BeanUtils.copyProperties(cpCOrgChannelItemEntity, newItem);
//            newItem.setCpCStoreId(storeArrStr);
//            if (storeMap.containsKey(newItem.getCpCStoreId())) {
//                CpCStore cpCStore = storeMap.get(newItem.getCpCStoreId());
//                newItem.setCpCStoreEcode(cpCStore.getEcode());
//                newItem.setCpCStoreEname(cpCStore.getEname());
//            } else {
//                List<Integer> storeIdList = new ArrayList<>();
//                storeIdList.add(newItem.getCpCStoreId().intValue());
//                try {
//                    log.info(LogUtil.format("SyncStockStrategySaveService,RPC参数_storeIdList:{}", storeIdList);
//                    List<CpCStore> storeList = rpcCpService.queryStoreInfoByIds(storeIdList);
//                    if (!CollectionUtils.isEmpty(storeList)) {
//                        CpCStore cpCStore = storeList.get(0);
//                        newItem.setCpCStoreEcode(cpCStore.getEcode());
//                        newItem.setCpCStoreEname(cpCStore.getEname());
//                        storeMap.put(cpCStore.getId(), cpCStore);
//                    }
//                } catch (Exception e) {
//                    throw new NDSException("调用获取店仓RPC接口失败");
//                }
//            }
//
//            //渠道逻辑仓关系表保存
//            saveSbGChannelStore(syncStockStrategy, syncStockStrategyChannelItem, newItem);
//            if (null != syncStockStrategy) {
//                //清除rediskey
//                String redisKey = OmsRedisKeyResources.buildLockSyncStockStrategyItemRedisKey(syncStockStrategy.getCpCShopId());
//                if (redisUtil.objRedisTemplate.hasKey(redisKey)) {
//                    redisUtil.objRedisTemplate.delete(redisKey);
//                }
//                RedisCacheUtil.delete(syncStockStrategy.getCpCShopId(), RedisConstant.SHOP_SYNC_STOCK_STRATEGY);
//                RedisCacheUtil.deleteAll(RedisConstant.SHOP_SYNC_STOCK_STRATEGY_ALL);
//            }
//        }
//    }
//
//    @Transactional(rollbackFor = {Exception.class})
//    @StOperationLog(mainTableName = "ST_C_SYNC_STOCK_STRATEGY", itemsTableName = "ST_C_SYNC_STOCK_STRATEGY_CHANNEL")
//    public ValueHolder updateSyncStockStrategy(QuerySession session, Long id, SyncStockStrategyRequest syncStockStrategyRequest) {
//        StCSyncStockStrategyDO existsSyncStockStrategy = mapper.selectById(id);
//        AssertUtils.notNull(existsSyncStockStrategy, "当前记录已不存在！");
//
//        StCSyncStockStrategyDO syncStockStrategy = syncStockStrategyRequest.getSyncStockStrategy();
//        //主表数据业务更新校验
//        if (syncStockStrategy != null) {
//            //checkSyncStockStrategy(syncStockStrategy);
//            //主表最后修改信息变更
//            syncStockStrategy.setId(id);
//            syncStockStrategy.setShopProperty(syncStockStrategy.getShopProperty() == null ? "" : syncStockStrategy.getShopProperty());
//            StBeanUtils.makeModifierField(syncStockStrategy, session.getUser());
//            //更新店铺同步库存策略主表
//            int result = mapper.updateById(syncStockStrategy);
//            AssertUtils.cannot(result <= 0, "店铺同步库存策略主表更新失败");
//        }
//        if (id > 0) {
//            syncStockStrategy = mapper.selectById(id);
//            if (null != syncStockStrategy) {
//                //清除rediskey
//                String redisKey = RedisConstant.bulidLockStCSyncStockStrategyItemKey(syncStockStrategy.getCpCShopId());
//                if (redisUtil.objRedisTemplate.hasKey(redisKey)) {
//                    redisUtil.objRedisTemplate.delete(redisKey);
//                }
//                RedisCacheUtil.delete(syncStockStrategy.getCpCShopId(), RedisConstant.SHOP_SYNC_STOCK_STRATEGY);
//                RedisCacheUtil.deleteAll(RedisConstant.SHOP_SYNC_STOCK_STRATEGY_ALL);
//            }
//        }
//        // 更新库存中心-渠道信息表
//        ValueHolderV14<Integer> vh = changeSbGChannelDef(syncStockStrategy);
//        AssertUtils.isTrue(vh.isOK(), vh.getMessage());
//
//        //新增子表信息
//        List<StCSyncStockStrategyChannelDO> itemList = syncStockStrategyRequest.getSyncStockStrategyChannelList();
//        if (!CollectionUtils.isEmpty(itemList)) {
//            //存储需要新增的实体仓id，作为校验是否存在相同的id，如果是则不能保存成功
//            Map map = new HashMap<>();
//            Map<Long, CpCStore> storeMap = new HashMap<>();
//
//            List<StCSyncStockStrategyChannelDO> insertList = Lists.newArrayList();
//            List<StCSyncStockStrategyChannelDO> updateList = Lists.newArrayList();
//            // 判断渠道仓优先级有没有相同
//            checkChannelPriorityItem(itemList);
//            List<StCSyncStockStrategyChannelDO> originItems = itemMapper.listChannelStrategyIds(id);
//            // 原优先级map,key 优先级,Val DO
//            Map<Long, StCSyncStockStrategyChannelDO> originChannelMap = originItems.stream()
//                    .collect(Collectors.toMap(StCSyncStockStrategyChannelDO::getId, Function.identity(), (key1, key2) -> key2));
//
//            itemList.forEach(item -> {
//                if (Objects.nonNull(item.getCpCOrgChannelId())) {
//                    Optional<StCSyncStockStrategyChannelDO> first = originItems.stream()
//                            .filter(originItem -> item.getCpCOrgChannelId()
//                                    .equals(originItem.getCpCOrgChannelId()) && id.equals(originItem.getStCSyncStockStrategyId()))
//                            .findFirst();
//                    if (first.isPresent()) {
//                        item.setId(first.get().getId());
//                    }
//                }
//            });
//
//            // 传入优先级map,key 优先级,Val id
//            Map<Long, Integer> newPriorityMap = itemList.stream().filter(item -> item.getChannelPriority() != null)
//                    .collect(Collectors.toMap(StCSyncStockStrategyChannelDO::getId, StCSyncStockStrategyChannelDO::getChannelPriority, (key1, key2) -> key2));
//
//            // 原优先级map,key 优先级,Val id
//            Map<Integer, Long> originPriorityMap = originItems.stream().filter(item -> item.getChannelPriority() != null)
//                    .collect(Collectors.toMap(StCSyncStockStrategyChannelDO::getChannelPriority, StCSyncStockStrategyChannelDO::getId, (key1, key2) -> key2));
//
//            List<Integer> channelPriorityList = originItems.stream()
//                    .map(StCSyncStockStrategyChannelDO::getChannelPriority)
//                    .filter(Objects::nonNull)
//                    .collect(Collectors.toList());
//
//            for (StCSyncStockStrategyChannelDO stSyncStockStrategyChannel : itemList) {
//                //比例、低库存、渠道仓优先级校验
//                checkSyncStockStrategyChannel(stSyncStockStrategyChannel);
//                //明细主键id；若id为null或者为-1则为新增
//                Long itemId = stSyncStockStrategyChannel.getId();
//                //冗余主表店铺信息
//                stSyncStockStrategyChannel.setCpCShopId(syncStockStrategy.getCpCShopId());
//                stSyncStockStrategyChannel.setCpCShopEcode(syncStockStrategy.getCpCShopEcode());
//                stSyncStockStrategyChannel.setCpCShopTitle(syncStockStrategy.getCpCShopTitle());
//                List<StCSyncStockStrategyChannelDO> existChannelDOList = Lists.newArrayList();
//                if (itemId == null || itemId < 0) {
//                    existChannelDOList = itemMapper.selectByChannelId(stSyncStockStrategyChannel.getCpCOrgChannelId(), id);
//                }
//
//                if (itemId != null && itemId > 0 || !CollectionUtils.isEmpty(existChannelDOList)) {
//
//                    StCSyncStockStrategyChannelDO stCSyncStockStrategyChannelDO;
//                    if (itemId != null && itemId > 0) {
//                        stCSyncStockStrategyChannelDO = originChannelMap.get(itemId);
//                    } else {
//                        stCSyncStockStrategyChannelDO = existChannelDOList.get(0);
//                        stSyncStockStrategyChannel.setId(stCSyncStockStrategyChannelDO.getId());
//                    }
//                    if (stCSyncStockStrategyChannelDO == null) {
//                        continue;
//                    }
//
//                    List<Integer> collect = originItems.stream()
//                            .filter(item -> !item.getCpCOrgChannelEcode().equals(stCSyncStockStrategyChannelDO.getCpCOrgChannelEcode()))
//                            .map(StCSyncStockStrategyChannelDO::getChannelPriority)
//                            .filter(Objects::nonNull)
//                            .collect(Collectors.toList());
//
//
//                    Integer channelPriority = stSyncStockStrategyChannel.getChannelPriority();
//                    if (collect.contains(channelPriority) &&
//                            (newPriorityMap.get(originPriorityMap.get(channelPriority)) == null ||
//                                    channelPriority.equals(newPriorityMap.get(originPriorityMap.get(channelPriority))))) {
//                        AssertUtils.logAndThrow("同一个策略内渠道仓优先级不能相等");
//                    }
//
//                    stSyncStockStrategyChannel.setCpCOrgChannelId(stCSyncStockStrategyChannelDO.getCpCOrgChannelId());
//                    stSyncStockStrategyChannel.setCpCOrgChannelEcode(stCSyncStockStrategyChannelDO.getCpCOrgChannelEcode());
//                    stSyncStockStrategyChannel.setCpCOrgChannelEname(stCSyncStockStrategyChannelDO.getCpCOrgChannelEname());
//                    ValueHolder channelValueHolder = cpCOrgChannelQueryCmd.getChannelAndItem(stSyncStockStrategyChannel.getCpCOrgChannelId());
//                    Integer code = (Integer) channelValueHolder.getData().get("code");
//                    if (code.intValue() == 0) {
//                        CpOrgChannelQueryResult cpOrgChannelQueryResult = (CpOrgChannelQueryResult) channelValueHolder.getData().get("data");
//                        List<CpCOrgChannelItemEntity> channelItemList = cpOrgChannelQueryResult.getChannelItemList();
//                        if (!CollectionUtils.isEmpty(channelItemList)) {
//                            updateChannelStore(syncStockStrategy, stSyncStockStrategyChannel, channelItemList, map, storeMap);
//                        }
//                    }
//                    StBeanUtils.makeModifierField(stSyncStockStrategyChannel, session.getUser());
//                    updateList.add(stSyncStockStrategyChannel);
//                } else {
//
//                    if (channelPriorityList.contains(stSyncStockStrategyChannel.getChannelPriority())) {
//                        AssertUtils.logAndThrow("同一个策略内渠道仓优先级不能相等");
//                    }
//
//                    //新增明细保存渠道逻辑仓
//                    addItemUpdateChannelStore(stSyncStockStrategyChannel, syncStockStrategy, map, storeMap, id);
//                    StBeanUtils.makeCreateField(stSyncStockStrategyChannel, session.getUser());
//                    stSyncStockStrategyChannel.setStCSyncStockStrategyId(syncStockStrategy.getId());
//                    insertList.add(stSyncStockStrategyChannel);
//                }
//            }
//            if (!CollectionUtils.isEmpty(updateList)) {
//                boolean b = syncStockStrategyChannelService.updateBatchById(updateList, UPDATE_INSERT_BATCH_SIZE);
//                AssertUtils.isTrue(b, "供货渠道明细更新失败");
//            }
//
//            if (!CollectionUtils.isEmpty(insertList)) {
//                boolean b = syncStockStrategyChannelService.saveBatch(insertList, UPDATE_INSERT_BATCH_SIZE);
//                AssertUtils.isTrue(b, "供货渠道明细插入失败");
//            }
//        }
//        return ValueHolderUtils.getSuccessValueHolder(id, StConstant.TAB_ST_C_SYNC_STOCK_STRATEGY);
//    }
//
//    private static void checkChannelPriorityItem(List<StCSyncStockStrategyChannelDO> itemList) {
//        long distinctCount = itemList.stream()
//                .map(StCSyncStockStrategyChannelDO::getChannelPriority)
//                .filter(Objects::nonNull)
//                .collect(Collectors.toList())
//                .stream().distinct().count();
//
//        long count = itemList.stream()
//                .map(StCSyncStockStrategyChannelDO::getChannelPriority)
//                .filter(Objects::nonNull)
//                .collect(Collectors.toList())
//                .stream().count();
//        if (count != distinctCount) {
//            throw new NDSException("同一个策略内渠道仓优先级不能相等");
//        }
//    }
//
////    public static void main(String[] args) {
////        List<StCSyncStockStrategyChannelDO> itemList = new ArrayList<>();
////        StCSyncStockStrategyChannelDO channelDO = new StCSyncStockStrategyChannelDO();
////        channelDO.setChannelPriority(3);
////
////        StCSyncStockStrategyChannelDO channelDO1 = new StCSyncStockStrategyChannelDO();
////        channelDO1.setChannelPriority(3);
////
////        itemList.add(channelDO);
////        itemList.add(channelDO1);
////
////        checkChannelPriorityItem(itemList);
////    }
//
//    /**
//     * @param stSyncStockStrategyChannel 同步库存策略明细
//     * @param syncStockStrategy          同步库存策略
//     * @param map                        key 逻辑仓id,value 逻辑仓id
//     * @param storeMap                   key 逻辑仓id,value 逻辑仓实体
//     * @param id                         objid
//     * @return void
//     * <AUTHOR>
//     * @Description 新增明细-保存渠道逻辑仓
//     * @Date 2020/11/18 15:10:01
//     **/
//    private void addItemUpdateChannelStore(StCSyncStockStrategyChannelDO stSyncStockStrategyChannel,
//                                           StCSyncStockStrategyDO syncStockStrategy,
//                                           Map map, Map<Long, CpCStore> storeMap, Long id) {
//        List<StCSyncStockStrategyChannelDO> exist = itemMapper.selectByChannelId(stSyncStockStrategyChannel.getCpCOrgChannelId(), id);
//        AssertUtils.cannot(exist != null && exist.size() > 0, "该供货渠道已存在");
//
//        stSyncStockStrategyChannel.setId(ModelUtil.getSequence(StConstant.TAB_ST_C_SYNC_STOCK_STRATEGY_CHANNEL));
//        stSyncStockStrategyChannel.setStCSyncStockStrategyId(syncStockStrategy.getId());
//        ValueHolder channelValueHolder = cpCOrgChannelQueryCmd.getChannelAndItem(stSyncStockStrategyChannel.getCpCOrgChannelId());
//        Integer code = (Integer) channelValueHolder.getData().get("code");
//        if (code.intValue() == 0) {
//            CpOrgChannelQueryResult cpOrgChannelQueryResult = (CpOrgChannelQueryResult) channelValueHolder.getData().get("data");
//            CpCOrgChannelEntity channel = cpOrgChannelQueryResult.getChannel();
//            if (channel != null) {
//                stSyncStockStrategyChannel.setCpCOrgChannelId(channel.getId());
//                stSyncStockStrategyChannel.setCpCOrgChannelEname(channel.getEname());
//                stSyncStockStrategyChannel.setCpCOrgChannelEcode(channel.getEcode());
//            }
//            List<CpCOrgChannelItemEntity> channelItemList = cpOrgChannelQueryResult.getChannelItemList();
//            if (!CollectionUtils.isEmpty(channelItemList)) {
//                updateChannelStore(syncStockStrategy, stSyncStockStrategyChannel, channelItemList, map, storeMap);
//            }
//        }
//    }
////
////    /**
////     * @param syncStockStrategy            店铺同步库存策略主表
////     * @param syncStockStrategyChannelItem 店铺同步库存策略子表
////     * @param item                         渠道仓子表
////     * @return void
////     * <AUTHOR>
////     * @Description 渠道逻辑仓关系表保存
////     * @Date 16:38 2020/6/11
////     **/
////    private void saveSbGChannelStore(StCSyncStockStrategyDO syncStockStrategy,
////                                     StCSyncStockStrategyChannelDO syncStockStrategyChannelItem,
////                                     CpCOrgChannelItemEntity item) {
////        SgChannelStoreChangeRequest storeChange = new SgChannelStoreChangeRequest();
////        storeChange.setCpCShopId(syncStockStrategy.getCpCShopId());
////        storeChange.setCpCShopEcode(syncStockStrategy.getCpCShopEcode());
////        storeChange.setCpCShopTitle(syncStockStrategy.getCpCShopTitle());
////        storeChange.setCpCStoreId(item.getCpCStoreId());
////        storeChange.setCpCStoreEcode(item.getCpCStoreEcode());
////        storeChange.setCpCStoreEname(item.getCpCStoreEname());
////        storeChange.setPriority(item.getSupplyPriority());
////        storeChange.setRate(syncStockStrategyChannelItem.getStockRate());
////        if (syncStockStrategyChannelItem.getLowStock() != null) {
////            storeChange.setLowStock(syncStockStrategyChannelItem.getLowStock());
////        }
////        storeChange.setIsSend(StConstant.ISACTIVE_Y.equals(item.getIsactive()) ? 1 : 0);
////        storeChange.setCpCChannelId(syncStockStrategyChannelItem.getCpCOrgChannelId());
////        storeChange.setCpCChannelEcode(syncStockStrategyChannelItem.getCpCOrgChannelEcode());
////        storeChange.setCpCChannelEname(syncStockStrategyChannelItem.getCpCOrgChannelEname());
////        try {
////            log.info(LogUtil.format("SyncStockStrategySaveService,RPC参数_storeChange:{}", storeChange);
////            sgChannelStoreChangeCmd.saveSbGChannelStore(storeChange);
////        } catch (Exception e) {
////            log.error(LogUtil.format("调用更新渠道逻辑仓关系RPC接口失败：" + e.getMessage(), e);
////            throw new NDSException("调用更新渠道逻辑仓关系RPC接口失败：" + e.getMessage());
////        }
////    }
//
//    private void checkSyncStockStrategyChannel(StCSyncStockStrategyChannelDO stCSyncStockStrategyChannelDO) {
//        String errMessage = "";
//        BigDecimal stockRate = stCSyncStockStrategyChannelDO.getStockRate();
//        if (stockRate != null && (stockRate.compareTo(BigDecimal.ZERO) < 0 ||
//                stockRate.compareTo(BigDecimal.valueOf(100)) > 0)) {
//            errMessage = "库存比例区间必须为：0~100!";
//        }
//        BigDecimal lowStock = stCSyncStockStrategyChannelDO.getLowStock();
//        if (lowStock != null && lowStock.compareTo(BigDecimal.ZERO) < 0) {
//            errMessage = "低库存数不能为负数!";
//        }
//
//        Integer channelPriority = stCSyncStockStrategyChannelDO.getChannelPriority();
//        if (channelPriority != null && !RegexUtils.isPureDigital(String.valueOf(channelPriority))) {
//            errMessage = "渠道仓优先级必须为正整数!";
//        }
//        if (!errMessage.isEmpty()) {
//            throw new NDSException(errMessage);
//        }
//    }
//
//    /**
//     * @param cpCStoreId      实体仓id
//     * @param cpCOrgChannelId 渠道仓id
//     * @param id              主表的关联id
//     * @return boolean
//     * <AUTHOR>
//     * @Description 校验新增的实体仓不能重复新增
//     * @Date 16:30 2020/6/11
//     **/
//    private boolean checkCSyncStockStrategyItemId(Long cpCStoreId, Long cpCOrgChannelId, Long id) {
//        //根据实体仓id查询数据库，若能查询到数据，代表已存在申请的记录，则不能重复申请同样的实体仓
//        //2020-0701 有可能是修改，将自生去除
//        List<CpCOrgChannelItemEntity> cpCOrgChannelItemEntities = itemMapper.selectstCChannelItemDOBySyncStockId(cpCStoreId, cpCOrgChannelId, id);
//        if (!CollectionUtils.isEmpty(cpCOrgChannelItemEntities)) {
//            return true;
//        }
//        return false;
//    }
////
////    //更新库存中心-渠道信息表
////    private ValueHolderV14<Integer> changeSbGChannelDef(StCSyncStockStrategyDO syncStockStrategy) {
////
////        SgChannelStorageDefChangeRequest channelDefRequest = new SgChannelStorageDefChangeRequest();
////        channelDefRequest.setId(syncStockStrategy.getCpCShopId());
////        //channelDefRequest.setStockScale(syncStockStrategy.getStockRate());
////        //channelDefRequest.setLowStock(syncStockStrategy.getLowStock() != null ? BigDecimal.valueOf(syncStockStrategy.getLowStock()) : null);
////        channelDefRequest.setIsSyncStock(syncStockStrategy.getIsSyncStock());
////        channelDefRequest.setIsAdd(false);
////        log.info(LogUtil.format("SyncStockStrategySaveService,RPC参数_channelDefRequest:{}", channelDefRequest);
////        return sgChannelStorageDefChangeCmd.changeSbGChannelDef(channelDefRequest);
////    }
//}