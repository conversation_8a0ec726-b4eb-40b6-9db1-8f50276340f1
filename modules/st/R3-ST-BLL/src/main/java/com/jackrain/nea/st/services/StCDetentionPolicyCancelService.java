package com.jackrain.nea.st.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.redis.util.RedisOpsUtil;
import com.jackrain.nea.st.common.StConstant;
import com.jackrain.nea.st.mapper.StCDetentionPolicyMapper;
import com.jackrain.nea.st.model.request.StDetentionPolicyRequest;
import com.jackrain.nea.st.model.table.StCDetentionPolicy;
import com.jackrain.nea.st.utils.RedisCacheUtil;
import com.jackrain.nea.st.utils.StBeanUtils;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;

/**
 * @program: r3-st
 * @description: 预售卡单反审
 * @author: liuwj
 * @create: 2021-06-17 18:13
 **/
@Component
@Slf4j
public class StCDetentionPolicyCancelService  extends CommandAdapter {
    @Autowired
    private StCDetentionPolicyMapper mapper;

    @Override
    public ValueHolder execute(QuerySession querySession) throws NDSException {
        //1.获取传入参数
        DefaultWebEvent event = querySession.getEvent();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(event.getParameterValue("param"),
                "yyyy-MM-dd HH:mm:ss", SerializerFeature.WriteMapNullValue), Feature.OrderedField);
        if (param == null) {
            throw new NDSException("参数为空！");
        }
        log.debug(LogUtil.format("Start StCDetentionPolicyCancelService.execute. ReceiveParams: {}"),
                param.toJSONString());
        //2.定义错误记录数
        HashMap<Long, Object> errMap = new HashMap();
        //3.生成审核Json数组
        JSONArray cancleAuditArray = StBeanUtils.makeUnAuditJsonArray(param);
        for (int i = 0; i < cancleAuditArray.size(); i++) {
            Long id = cancleAuditArray.getLong(i);
            try {
                //4.遍历反审核方法
                cancleAudit(id, querySession);
            } catch (Exception e) {
                errMap.put(id, e.getMessage());
            }
        }
        //5.返回ValueHolder对象
        return StBeanUtils.getExcuteValueHolder(cancleAuditArray.size(), errMap);
    }

    public  void cancleAudit(Long id,QuerySession querySession){
        StCDetentionPolicy stCDetentionPolicy = mapper.selectById(id);
        //主表校验
        checkExpress(stCDetentionPolicy);
        //更新单据状态
        StBeanUtils.makeModifierField(stCDetentionPolicy, querySession.getUser());
        stCDetentionPolicy.setModifierename(querySession.getUser().getEname());
        stCDetentionPolicy.setEstatus(StConstant.CON_BILL_STATUS_01);
        int updateNum = mapper.updateById(stCDetentionPolicy);
        if (updateNum < 0) {
            throw new NDSException("物流方案:" + stCDetentionPolicy.getName() + "反审核失败！");
        }

        /**
         * 反审核删除对应redis
         */
        String cpCShopId = stCDetentionPolicy.getCpCShopId();
        String key = StConstant.SHOP_DETENTION_ORDER_ST + cpCShopId;
        RedisCacheUtil.deleteAll(key);
    }
    private void checkExpress(StCDetentionPolicy stCDetentionPolicy) {
        if (stCDetentionPolicy == null) {
            throw new NDSException("当前记录已不存在！");
        } else {
            if (StConstant.CON_BILL_STATUS_01.equals(stCDetentionPolicy.getEstatus())) {
                throw new NDSException("当前记录未审核，不允许做反审核！");
            } else if (StConstant.CON_BILL_STATUS_04.equals(stCDetentionPolicy.getEstatus())) {
                throw new NDSException("当前记录已结案，不允许做反审核！");
            } else if (StConstant.CON_BILL_STATUS_03.equals(stCDetentionPolicy.getEstatus())) {
                throw new NDSException("当前记录已作废，不允许做反审核！");
            }
        }
    }
}
