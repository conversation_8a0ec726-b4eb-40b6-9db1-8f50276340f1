package com.jackrain.nea.st.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.redis.util.RedisOpsUtil;
import com.jackrain.nea.st.common.StConstant;
import com.jackrain.nea.st.mapper.StCOrderLabelLogMapper;
import com.jackrain.nea.st.mapper.StCOrderLabelMapper;
import com.jackrain.nea.st.mapper.StCOrderLabelShopItemMapper;
import com.jackrain.nea.st.model.table.StCOrderLabelDO;
import com.jackrain.nea.st.model.table.StCOrderLabelLogDO;
import com.jackrain.nea.st.model.table.StCOrderLabelShopItemDO;
import com.jackrain.nea.st.utils.RedisConstant;
import com.jackrain.nea.st.utils.StBeanUtils;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;

/**
 * @ClassName : StCOrderLabelCancelService  
 * @Description :
 * <AUTHOR>  YCH
 * @Date: 2021-11-23 17:08  
 */
@Component
@Slf4j
public class StCOrderLabelCancelService extends CommandAdapter {

    @Autowired
    private StCOrderLabelMapper stCOrderLabelMapper;
    @Autowired
    private StCOrderLabelLogMapper stCOrderLabelLogMapper;
    @Autowired
    private StCOrderLabelShopItemMapper stCOrderLabelShopItemMapper;

    @Autowired
    private RedisOpsUtil<String, String> redisUtil;

    @Override
    public ValueHolder execute(QuerySession querySession) throws NDSException {
        //1.获取传入参数
        DefaultWebEvent event = querySession.getEvent();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(event.getParameterValue("param"),
                "yyyy-MM-dd HH:mm:ss", SerializerFeature.WriteMapNullValue), Feature.OrderedField);
        if (param == null) {
            throw new NDSException("参数为空！");
        }
        log.debug(LogUtil.format("Start StCOrderLabelCancelService.execute. ReceiveParams: {}"), param.toJSONString());
        //2.定义错误记录数
        HashMap<Long, Object> errMap = new HashMap();
        //3.生成审核Json数组
        JSONArray cancleAuditArray = StBeanUtils.makeUnAuditJsonArray(param);
        for (int i = 0; i < cancleAuditArray.size(); i++) {
            Long id = cancleAuditArray.getLong(i);
            try {
                //4.遍历反审核方法
                cancleAudit(id, querySession);
                saveLog(id, querySession);
            } catch (Exception e) {
                errMap.put(id, e.getMessage());
            }
        }
        //5.返回ValueHolder对象
        return StBeanUtils.getExcuteValueHolder(cancleAuditArray.size(), errMap);
    }

    public  void cancleAudit(Long id,QuerySession querySession){
        StCOrderLabelDO stCOrderLabelDO = stCOrderLabelMapper.selectById(id);
        //主表校验
        checkExpress(stCOrderLabelDO);
        //更新单据状态
        StBeanUtils.makeModifierField(stCOrderLabelDO, querySession.getUser());
        stCOrderLabelDO.setModifierename(querySession.getUser().getEname());
        stCOrderLabelDO.setStatus(StConstant.CON_BILL_STATUS_01);
        int updateNum = stCOrderLabelMapper.updateById(stCOrderLabelDO);
        if (updateNum < 0) {
            throw new NDSException("反审核失败！");
        }
        List<StCOrderLabelShopItemDO> stCOrderLabelShopItemDOS = stCOrderLabelShopItemMapper.selectStCOrderLabelShopItemList(id);
        if (CollectionUtils.isNotEmpty(stCOrderLabelShopItemDOS)) {
            for (StCOrderLabelShopItemDO stCOrderLabelShopItemDO : stCOrderLabelShopItemDOS) {
                redisUtil.strRedisTemplate.delete(RedisConstant.ST_ORDER_LABEL + stCOrderLabelShopItemDO.getCpCShopId());
            }
        }
    }

    private void saveLog(Long id, QuerySession querySession) {
        StCOrderLabelLogDO stCOrderLabelLogDO = new StCOrderLabelLogDO();
        stCOrderLabelLogDO.setId(ModelUtil.getSequence(StConstant.TAB_ST_C_ORDER_LABEL_LOG));
        stCOrderLabelLogDO.setStCOrderLabelId(id);
        stCOrderLabelLogDO.setModcontent("反审核");
        stCOrderLabelLogDO.setBmod("已审核");
        stCOrderLabelLogDO.setAmod("待审核");
        StBeanUtils.makeCreateField(stCOrderLabelLogDO, querySession.getUser());
        stCOrderLabelLogMapper.insert(stCOrderLabelLogDO);
    }

    private void checkExpress(StCOrderLabelDO stCOrderLabelDO) {
        if (stCOrderLabelDO == null) {
            throw new NDSException("当前记录已不存在！");
        } else {
            if (!StConstant.CON_BILL_STATUS_02.equals(stCOrderLabelDO.getStatus())) {
                throw new NDSException("仅可对已审核的单据进行审核操作！反审核失败！");
            }
        }
    }
}
