package com.jackrain.nea.st.services;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.base.Throwables;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.st.mapper.StCDewuWarehouseConfigMapper;
import com.jackrain.nea.st.model.table.StCDewuWarehouseConfig;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.utility.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

/**
 * 得物仓库配置表查询服务
 */
@Component
@Slf4j
@Transactional(readOnly = true)
public class StCDewuWarehouseConfigQueryService extends CommandAdapter {

    @Autowired
    private StCDewuWarehouseConfigMapper stCDewuWarehouseConfigMapper;

    /**
     * 根据仓库编码查询得物仓库配置
     *
     * @param warehouseCode 仓库编码
     * @return 得物仓库配置列表
     */
    public List<StCDewuWarehouseConfig> queryByWarehouseCode(String warehouseCode) {
        log.debug(LogUtil.format("开始根据仓库编码查询得物仓库配置，仓库编码: {}"), warehouseCode);

        try {
            if (StringUtils.isBlank(warehouseCode)) {
                log.error("仓库编码不能为空");
                return new ArrayList<>();
            }

            // 构建查询条件
            LambdaQueryWrapper<StCDewuWarehouseConfig> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(StCDewuWarehouseConfig::getWarehouseCode, warehouseCode);
            queryWrapper.eq(StCDewuWarehouseConfig::getIsactive, "Y");
            queryWrapper.orderByDesc(StCDewuWarehouseConfig::getCreationdate);

            // 执行查询
            List<StCDewuWarehouseConfig> configList = stCDewuWarehouseConfigMapper.selectList(queryWrapper);

            log.debug(LogUtil.format("根据仓库编码查询得物仓库配置成功，仓库编码: {}, 结果数量: {}"),
                    warehouseCode, configList != null ? configList.size() : 0);

            return configList != null ? configList : new ArrayList<>();
        } catch (Exception ex) {
            log.error(LogUtil.format("根据仓库编码查询得物仓库配置异常，仓库编码: {}, 异常: {}"),
                    warehouseCode, Throwables.getStackTraceAsString(ex));
            throw new NDSException("查询得物仓库配置异常: " + ex.getMessage());
        }
    }
}
