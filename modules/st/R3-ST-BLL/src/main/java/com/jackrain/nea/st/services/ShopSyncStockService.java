//package com.jackrain.nea.st.services;
//
//import com.alibaba.fastjson.JSON;
//import com.alibaba.fastjson.JSONObject;
//import com.alibaba.fastjson.parser.Feature;
//import com.alibaba.fastjson.serializer.SerializerFeature;
//import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
//import com.google.common.collect.Lists;
//import com.jackrain.nea.exception.NDSException;
//import com.jackrain.nea.sg.oms.api.SgBSyncChannelStockCmd;
//import com.jackrain.nea.sg.oms.api.SgChannelProductQueryCmd;
//import com.jackrain.nea.sg.oms.common.OmsConstantsIF;
//import com.jackrain.nea.sg.oms.model.request.SgChannelProductQueryForSTRequest;
//import com.jackrain.nea.sg.oms.model.result.SgChannelProductQueryForSTResult;
//import com.jackrain.nea.sg.oms.model.table.SgBSyncChannelStock;
//import com.jackrain.nea.st.common.StConstant;
//import com.jackrain.nea.st.mapper.StCSyncPlatformStockMapper;
//import com.jackrain.nea.st.mapper.StCSyncStockStrategyChannelMapper;
//import com.jackrain.nea.st.mapper.StCSyncStockStrategyMapper;
//import com.jackrain.nea.st.model.table.StCSyncStockStrategyChannelDO;
//import com.jackrain.nea.st.model.table.StCSyncStockStrategyDO;
//import com.jackrain.nea.st.utils.RedisCacheUtil;
//import com.jackrain.nea.st.utils.RedisConstant;
//import com.jackrain.nea.st.utils.ValueHolderUtils;
//import com.jackrain.nea.sys.CommandAdapter;
//import com.jackrain.nea.sys.domain.ValueHolderV14;
//import com.jackrain.nea.util.ApplicationContextHandle;
//import com.jackrain.nea.util.ValueHolder;
//import com.jackrain.nea.web.face.User;
//import com.jackrain.nea.web.query.DefaultWebEvent;
//import com.jackrain.nea.web.query.QuerySession;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.dubbo.config.annotation.Reference;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.stereotype.Component;
//import org.springframework.transaction.annotation.Transactional;
//import org.springframework.util.CollectionUtils;
//import org.springframework.util.StringUtils;
//
//import java.util.List;
//import java.util.stream.Collectors;
//
///**
// * <AUTHOR> ShiLong
// * @Date: 2020/7/8 9:22 下午
// * @Desc: 店铺同步平台库存
// */
//@Component
//@Slf4j
//public class ShopSyncStockService extends CommandAdapter {
//
//    @Autowired
//    private StCSyncStockStrategyChannelMapper itemMapper;
//    @Autowired
//    private StCSyncPlatformStockMapper stockMapper;
//    @Reference(group = "sg", version = "1.0")
//    private SgBSyncChannelStockCmd sgBSyncChannelStockCmd;
//    @Autowired
//    private StCSyncStockStrategyMapper mainMapper;
//    @Reference(group = "sg", version = "1.0")
//    private SgChannelProductQueryCmd sgChannelProductQueryCmd;
//
//    /**
//     * 点击同步按钮时，需判断修改前比例是否为null，如果为Null代表需要同步；
//     * 其次还需要判断不为null情况下，当前修改比例与修改前比例是否相同，如果相同则不需要同步，否则同步
//     * 点击同步后，需要将修改前比例更新一下
//     */
//    @Override
//    public ValueHolder execute(QuerySession session) {
//        DefaultWebEvent event = session.getEvent();
//        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(
//                event.getParameterValue("param"), "yyyy-MM-dd HH:mm:ss", SerializerFeature.WriteMapNullValue),
//                Feature.OrderedField);
//        log.info(LogUtil.format("店铺同步平台库存入参:{}", param);
//        Long id = param.getLong("objid");
//        if (id == null || id < 0) {
//            throw new NDSException("请选择需要同步的库存策略");
//        }
//        ShopSyncStockService bean = ApplicationContextHandle.getBean(ShopSyncStockService.class);
//        ValueHolder holder = bean.syncPlatformStock(id, session.getUser());
//        return holder;
//    }
//
//    /**
//     * 点击同步按钮时，需判断修改前比例是否为null，如果为Null代表需要同步；
//     * 其次还需要判断不为null情况下，当前修改比例与修改前比例是否相同，如果相同则不需要同步，否则同步
//     * 点击同步后，需要将修改前比例更新一下
//     */
//    @Transactional(rollbackFor = Exception.class)
//    public ValueHolder syncPlatformStock(Long objid, User user) {
//        //查询明细表信息（需要修改前比例是否为Null，以及不为null的情况）
//        StCSyncStockStrategyDO main = mainMapper.selectById(objid);
//        List<StCSyncStockStrategyChannelDO> itemList = itemMapper.selectList(new QueryWrapper<StCSyncStockStrategyChannelDO>()
//                .lambda()
//                .eq(StCSyncStockStrategyChannelDO::getStCSyncStockStrategyId, objid)
//                .eq(StCSyncStockStrategyChannelDO::getIsactive, StConstant.ISACTIVE_Y));
//        if (!CollectionUtils.isEmpty(itemList)) {
//            //收集修改前比例为null的比例(代表需要同步)
//            //收集修改前比例不为Null且与当前修改比例不一致的明细(代表需要同步)
//            List<StCSyncStockStrategyChannelDO> rateList = itemList.stream().filter(e ->
//                    e.getBeforeUpdateStockRate() == null
//                            || (e.getBeforeUpdateStockRate() != null
//                            && !e.getStockRate().equals(e.getBeforeUpdateStockRate())))
//                    .collect(Collectors.toList());
//            //调用同步接口,调用成功后需更新修改前比例
//            try {
//                if (rateList.size() != 0) {
//                    //需先查询sku相关信息
//                    List<SgBSyncChannelStock> channelStockList = this.assembleParam(main, rateList);
//                    //为空则不需同步
//                    if (!CollectionUtils.isEmpty(channelStockList)) {
//                        ValueHolderV14<List<SgBSyncChannelStock>> valueHolderV14 = sgBSyncChannelStockCmd.insert(channelStockList, user);
//                        if (valueHolderV14.isOK()) {
//                            int Result = stockMapper.updateByIds(rateList);
//                            if (Result > 0) {
//                                return ValueHolderUtils.getSuccessValueHolder(objid, StConstant.TAB_ST_C_SYNC_STOCK_STRATEGY_CHANNEL, "库存同步成功");
//                            } else {
//                                log.debug(LogUtil.format("库存同步中间表添加成功,明细表修改before_update失败" + rateList.toString());
//                                return ValueHolderUtils.getFailValueHolder("同步库存失败");
//                            }
//                        }
//                        log.debug(LogUtil.format("【店铺同步平台库存失败】data:{}", JSONObject.toJSON(rateList));
//                        return ValueHolderUtils.getFailValueHolder("同步库存失败");
//                    } else {
//                        return ValueHolderUtils.getFailValueHolder("店铺暂无sku信息，无需同步");
//                    }
//                }
//            } catch (Exception e) {
//                e.printStackTrace();
//                log.error(LogUtil.format("【店铺同步平台库存失败】data:{}", rateList);
//            }
//            RedisCacheUtil.delete(main.getCpCShopId(), RedisConstant.SHOP_SYNC_STOCK_STRATEGY);
//            RedisCacheUtil.deleteAll(RedisConstant.SHOP_SYNC_STOCK_STRATEGY_ALL);
//        }
//        return ValueHolderUtils.getFailValueHolder("暂无需要同步的库存策略");
//    }
//
//    private List<SgBSyncChannelStock> assembleParam(StCSyncStockStrategyDO main, List<StCSyncStockStrategyChannelDO> itemList) {
//        List<SgBSyncChannelStock> channelStockList = Lists.newArrayList();
//        for (StCSyncStockStrategyChannelDO strategyChannelDO : itemList) {
//            if (main.getId().equals(strategyChannelDO.getStCSyncStockStrategyId())) {
//                SgBSyncChannelStock sgBSyncChannelStock = new SgBSyncChannelStock();
//                sgBSyncChannelStock.setCpCOrgChannelId(strategyChannelDO.getCpCOrgChannelId());
//                sgBSyncChannelStock.setCpCShopId(main.getCpCShopId());
//                sgBSyncChannelStock.setCpCShopTitle(main.getCpCShopTitle());
//                sgBSyncChannelStock.setStrategyId(main.getId());
//                sgBSyncChannelStock.setType(StConstant.SYNC_STOCK_STRATEGY_TYPE);
//                sgBSyncChannelStock.setStatus(StConstant.NO_SYNC_STATUS);
//                channelStockList.add(sgBSyncChannelStock);
//            }
//        }
//        return channelStockList;
//    }
//
//
//    private List<SgBSyncChannelStock> assembleParam(Long objid, List<StCSyncStockStrategyChannelDO> itemList) {
//        StCSyncStockStrategyDO main = mainMapper.selectById(objid);
//        SgChannelProductQueryForSTRequest queryForSTResult = new SgChannelProductQueryForSTRequest();
//        List<Long> shopIds = Lists.newArrayList();
//        shopIds.add(main.getCpCShopId());
//        queryForSTResult.setCpCShopIdList(shopIds);
//        //根据店铺查询平台店铺商品表获取Sku相关信息
//        List<SgChannelProductQueryForSTResult> queryResult = sgChannelProductQueryCmd.queryChannelProductForST(queryForSTResult);
//        List<SgBSyncChannelStock> channelStockList = Lists.newArrayList();
//        for (SgChannelProductQueryForSTResult result : queryResult) {
//            for (StCSyncStockStrategyChannelDO item : itemList) {
//                if (item.getStCSyncStockStrategyId().equals(main.getId())) {
//                    SgBSyncChannelStock sgBSyncChannelStock = new SgBSyncChannelStock();
//                    sgBSyncChannelStock.setCpCOrgChannelId(item.getCpCOrgChannelId());
//                    sgBSyncChannelStock.setCpCShopId(main.getCpCShopId());
//                    sgBSyncChannelStock.setCpCShopTitle(main.getCpCShopTitle());
//                    if (!StringUtils.isEmpty(result.getNumiid())) {
//                        sgBSyncChannelStock.setNumberId(result.getNumiid());
//                    }
//                    if (result.getPsCProId() != null) {
//                        sgBSyncChannelStock.setPsCProId(result.getPsCProId());
//                    }
//                    if (result.getPsCSkuId() != null) {
//                        sgBSyncChannelStock.setPsCSkuId(result.getPsCSkuId());
//                        sgBSyncChannelStock.setPsCSkuEcode(result.getPsCSkuEcode());
//                    }
//                    if (!StringUtils.isEmpty(result.getSkuId())) {
//                        sgBSyncChannelStock.setSkuId(result.getSkuId());
//                    }
//                    sgBSyncChannelStock.setStrategyId(main.getId());
//                    sgBSyncChannelStock.setType(StConstant.SYNC_STOCK_STRATEGY_TYPE);
//                    sgBSyncChannelStock.setStatus(StConstant.NO_SYNC_STATUS);
//                    channelStockList.add(sgBSyncChannelStock);
//                }
//            }
//        }
//        return channelStockList;
//    }
//}
