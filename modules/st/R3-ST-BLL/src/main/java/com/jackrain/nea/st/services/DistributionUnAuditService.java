package com.jackrain.nea.st.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.st.common.StConstant;
import com.jackrain.nea.st.mapper.StCDistributionMapper;
import com.jackrain.nea.st.model.table.StCDistributionDO;
import com.jackrain.nea.st.utils.StBeanUtils;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import java.util.HashMap;

/**
 * @Descroption 分销代销策略新增反审核
 * <AUTHOR>
 * @Date 2019/3/12 10:55
 */
@Component
@Slf4j
public class DistributionUnAuditService extends CommandAdapter {
    @Autowired
    private StCDistributionMapper mapper;

    @Override
    public ValueHolder execute(QuerySession querySession) throws NDSException {
        //1.获取传入参数
        DefaultWebEvent event = querySession.getEvent();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(event.getParameterValue("param"),
                "yyyy-MM-dd HH:mm:ss", SerializerFeature.WriteMapNullValue), Feature.OrderedField);
        if (param == null) {
            throw new NDSException("参数为空！");
        }
        //2.定义错误记录数
        HashMap<Long, Object> errMap = new HashMap<>();
        //3.生成审核Json数组
        JSONArray unAuditArray = StBeanUtils.makeUnAuditJsonArray(param);
        for (int i = 0; i < unAuditArray.size(); i++) {
            Long id = unAuditArray.getLong(i);
            try {
                //4.遍历反审核方法
                unAuditDistribution(id, querySession);
            } catch (Exception e) {
                errMap.put(id, e.getMessage());
            }
        }
        //5.返回ValueHolder对象
        return StBeanUtils.getExcuteValueHolder(unAuditArray.size(), errMap);
    }

    private void unAuditDistribution(Long id, QuerySession querySession) {
        StCDistributionDO distribution = mapper.selectById(id);
        //1.主表校验
        checkDistribution(distribution);
        //2.更新单据状态
        StBeanUtils.makeModifierField(distribution, querySession.getUser());
        distribution.setBillStatus(StConstant.CON_BILL_STATUS_01);
        clearAuditCommonField(distribution);
        int updateNum = mapper.updateById(distribution);
        if (updateNum < 0) {
            throw new NDSException("方案:" + distribution.getEname() + "反审核失败！");
        }
    }

    private void checkDistribution(StCDistributionDO distribution) {
        if (distribution == null) {
            throw new NDSException("当前记录已不存在！");
        } else {
            if (StConstant.CON_BILL_STATUS_01.equals(distribution.getBillStatus())) {
                throw new NDSException("当前记录未审核，不允许做反审核！");
            } else if (StConstant.CON_BILL_STATUS_04.equals(distribution.getBillStatus())) {
                throw new NDSException("当前记录已结案，不允许做反审核！");
            } else if (StConstant.CON_BILL_STATUS_03.equals(distribution.getBillStatus())) {
                throw new NDSException("当前记录已作废，不允许做反审核！");
            }
        }
    }

    private void clearAuditCommonField(StCDistributionDO distribution) {
        distribution.setCheckid(null);
        distribution.setCheckename(null);
        distribution.setCheckname(null);
        distribution.setChecktime(null);
    }
}
