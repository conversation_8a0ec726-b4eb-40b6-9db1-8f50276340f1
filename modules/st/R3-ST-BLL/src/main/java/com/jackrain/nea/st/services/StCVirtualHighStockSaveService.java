//package com.jackrain.nea.st.services;
//
//import com.alibaba.fastjson.JSON;
//import com.alibaba.fastjson.JSONArray;
//import com.alibaba.fastjson.JSONObject;
//import com.alibaba.fastjson.TypeReference;
//import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
//import com.google.common.collect.Lists;
//import com.jackrain.nea.cpext.model.table.CpShop;
//import com.jackrain.nea.exception.NDSException;
//import com.jackrain.nea.ext.sequence.util.SequenceGenUtil;
//import com.jackrain.nea.model.util.ModelUtil;
//import com.jackrain.nea.psext.model.table.PsCPro;
//import com.jackrain.nea.psext.model.table.PsCSku;
//import com.jackrain.nea.sg.oms.api.SgChannelProductQueryCmd;
//import com.jackrain.nea.sg.oms.model.request.SgChannelProductQueryForSTRequest;
//import com.jackrain.nea.sg.oms.model.result.SgChannelProductQueryForSTResult;
//import com.jackrain.nea.st.common.StConstant;
//import com.jackrain.nea.st.mapper.StCVirtualHighStockItemMapper;
//import com.jackrain.nea.st.mapper.StCVirtualHighStockMapper;
//import com.jackrain.nea.st.model.table.StCShopVirtualHighStockDO;
//import com.jackrain.nea.st.model.table.StCShopVirtualHighStockItemDO;
//import com.jackrain.nea.st.rpc.RpcCpService;
//import com.jackrain.nea.st.rpc.RpcPsService;
//import com.jackrain.nea.st.utils.DatasToEsUtils;
//import com.jackrain.nea.st.utils.JsonUtils;
//import com.jackrain.nea.st.utils.StBeanUtils;
//import com.jackrain.nea.st.utils.ValueHolderUtils;
//import com.jackrain.nea.sys.CommandAdapter;
//import com.jackrain.nea.util.ApplicationContextHandle;
//import com.jackrain.nea.util.ValueHolder;
//import com.jackrain.nea.web.query.DefaultWebEvent;
//import com.jackrain.nea.web.query.QuerySession;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.commons.collections4.CollectionUtils;
//import org.apache.commons.lang3.StringUtils;
//import org.apache.dubbo.config.annotation.Reference;
//import org.springframework.beans.BeanUtils;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.stereotype.Component;
//import org.springframework.transaction.annotation.Transactional;
//
//import java.io.IOException;
//import java.math.BigDecimal;
//import java.util.*;
//import java.util.function.Function;
//import java.util.stream.Collectors;
//
///**
// * <AUTHOR> ShiLong
// * @Date: 2020/6/22 2:54 下午
// * @Desc: 虚高库存业务代码
// */
//@Component
//@Slf4j
//public class StCVirtualHighStockSaveService extends CommandAdapter {
//
//    @Autowired
//    private StCVirtualHighStockMapper stCVirtualHighStockMapper;
//    @Autowired
//    private StCVirtualHighStockItemService stockItemService;
//    @Autowired
//    private StCVirtualHighStockItemMapper itemMapper;
//    @Reference(group = "sg",version = "1.0")
//    private SgChannelProductQueryCmd sgChannelProductQueryCmd;
//    @Autowired
//    private RpcCpService rpcCpService;
//    @Autowired
//    private RpcPsService rpcPsService;
//
//    @Autowired
//    private GeneralStrategyCheckService generalStrategyCheckService;
//
//    /**
//     * 新增和修改
//     * @param querySession 参数封装
//     * @return 返回状态
//     * @throws NDSException 异常信息
//     */
//    @Transactional(rollbackFor = Exception.class)
//    @Override
//    public ValueHolder execute(QuerySession querySession) throws NDSException {
//        ValueHolder valueHolder = new ValueHolder();
//        DefaultWebEvent event = querySession.getEvent();
//        JSONObject param = JsonUtils.filterNull((JSONObject) event.getParameterValue("param"), null);
//        Long id = param.getLong("objid");
//        JSONObject fixColumn = param.getJSONObject("fixcolumn");
//        JSONObject headMap = fixColumn.getJSONObject(StConstant.TAB_ST_C_SHOP_VIRTUAL_HIGH_STOCK);
//        JSONArray itemMap = fixColumn.getJSONArray(StConstant.TAB_ST_C_SHOP_VIRTUAL_HIGH_STOCK_ITEM);
//        if (log.isDebugEnabled()) {
//            log.debug(LogUtil.format("shopItemVirtualHighStockMapParam:{}",param.toJSONString());
//            log.debug(LogUtil.format("shopItemVirtualHighStockMap:{}", headMap);
//        }
//        if (id != null && id < 0) {
//            valueHolder = saveProcess(headMap, itemMap, valueHolder, querySession, id);
//        } else {
//            valueHolder = updateProcess(headMap, itemMap, valueHolder, querySession, id);
//        }
//        return valueHolder;
//    }
//
//    /**
//     * 新增操作
//     * @param headMap                主表数据
//     * @param itemMap                子表数据
//     * @param valueHolder            响应数据
//     * @param querySession           封装数据
//     * @param id                     id
//     * @return 返回状态
//     */
//    @Transactional(rollbackFor = Exception.class)
//    public ValueHolder saveProcess(JSONObject headMap, JSONArray itemMap, ValueHolder valueHolder, QuerySession querySession, Long id) {
//        StCShopVirtualHighStockDO stCShopItemVirtualHighStockDO = JsonUtils.jsonParseClass(headMap, StCShopVirtualHighStockDO.class);
//        if (log.isDebugEnabled()) {
//            log.debug(LogUtil.format("【虚高库存添加明细表入参】:");
//        }
//        //状态数据检查
//        checkStatus(stCShopItemVirtualHighStockDO, id, valueHolder);
//
//        //新增主表数据
//        if (stCShopItemVirtualHighStockDO != null) {
//            saveVirtualHigh(querySession, stCShopItemVirtualHighStockDO);
//        } else {
//            log.debug(LogUtil.format("【虚高库存新增主表失败】:{}",JSONObject.toJSON(stCShopItemVirtualHighStockDO));
//            throw new NDSException("保存失败！");
//        }
//
//        // 明细
//        if (!saveItemProcess(querySession, stCShopItemVirtualHighStockDO, itemMap)) {
//            return valueHolder;
//        } else {
//            StCShopVirtualHighStockDO stCShopItemVirtualHighStockNewDO = new StCShopVirtualHighStockDO();
//            stCShopItemVirtualHighStockNewDO.setId(stCShopItemVirtualHighStockDO.getId());
//            StBeanUtils.makeModifierField(stCShopItemVirtualHighStockNewDO, querySession.getUser());
//            BeanUtils.copyProperties(stCShopItemVirtualHighStockDO,stCShopItemVirtualHighStockNewDO);
//            stCVirtualHighStockMapper.updateById(stCShopItemVirtualHighStockNewDO);
//            //推送es
//            try {
//                DatasToEsUtils.insertVirtualHighStockEsData(stCShopItemVirtualHighStockNewDO,null,StConstant.TAB_ST_C_SHOP_VIRTUAL_HIGH_STOCK);
//            } catch (IOException e) {
//                log.debug(LogUtil.format("店铺商品虚高库存明细表推数据到ES失败：" + e.toString());
//            }
//        }
//        valueHolder = ValueHolderUtils.getSuccessValueHolder(stCShopItemVirtualHighStockDO.getId(), StConstant.TAB_ST_C_SHOP_VIRTUAL_HIGH_STOCK);
//        return valueHolder;
//    }
//
//    /**
//     * <ul>
//     *     <li>
//     *         保存主表数据
//     *     </li>
//     * </ul>
//     *
//     * @param querySession                  {@link QuerySession}
//     * @param stCShopItemVirtualHighStockDO 主表
//     */
//    private void saveVirtualHigh(QuerySession querySession, StCShopVirtualHighStockDO stCShopItemVirtualHighStockDO) {
//        if (stCShopItemVirtualHighStockDO.getCpCShopId() != null) {
//            CpShop cpShop = rpcCpService.selectCpCShopById(stCShopItemVirtualHighStockDO.getCpCShopId());
//            if (cpShop != null) {
//                stCShopItemVirtualHighStockDO.setCpCShopEcode(cpShop.getEcode());
//                stCShopItemVirtualHighStockDO.setCpCShopTitle(cpShop.getCpCShopTitle());
//            }
//        }
//        stCShopItemVirtualHighStockDO.setId(ModelUtil.getSequence(StConstant.TAB_ST_C_SHOP_VIRTUAL_HIGH_STOCK));
//        // 方案ID
//        JSONObject sequence = new JSONObject();
//        String seqProductStrategy = SequenceGenUtil.generateSquence("SEQ_VIRTUAL_HIGH_STOCK", sequence, querySession.getUser().getLocale(), false);
//        stCShopItemVirtualHighStockDO.setPlan(seqProductStrategy);
//        stCShopItemVirtualHighStockDO.setIsactive(StConstant.ISACTIVE_Y);
//        stCShopItemVirtualHighStockDO.setVirtualHighFailureValue(BigDecimal.ZERO);
//        StBeanUtils.makeCreateField(stCShopItemVirtualHighStockDO, querySession.getUser());
//        stCShopItemVirtualHighStockDO.setState(StConstant.CON_BILL_STATUS_01);
//        if (stCVirtualHighStockMapper.insert(stCShopItemVirtualHighStockDO) < 0) {
//            log.debug(LogUtil.format("【虚高库存新增主表失败】:{}", JSONObject.toJSON(stCShopItemVirtualHighStockDO));
//            throw new NDSException("保存失败！");
//        }
//    }
//
//    /**
//     * 更新
//     * @param headMap
//     * @param itemMap
//     * @param valueHolder
//     * @param querySession
//     * @param id
//     * @return
//     */
//    @Transactional(rollbackFor = Exception.class)
//    public ValueHolder updateProcess(JSONObject headMap, JSONArray itemMap, ValueHolder valueHolder, QuerySession querySession, Long id) {
//        //主表更新，objid就是主表ID
//        if (headMap != null && !headMap.isEmpty()) {
//            StCShopVirtualHighStockDO stCShopItemVirtualHighStockDO = JSON.parseObject(headMap.toJSONString(),
//                    new TypeReference<StCShopVirtualHighStockDO>() {
//                    });
//            //状态数据检查
//            checkStatus(stCShopItemVirtualHighStockDO, id, valueHolder);
//
//            StBeanUtils.makeModifierField(stCShopItemVirtualHighStockDO, querySession.getUser());
//            stCShopItemVirtualHighStockDO.setId(id);
//            if (stCVirtualHighStockMapper.updateById(stCShopItemVirtualHighStockDO) < 0) {
//                log.debug(LogUtil.format("【虚高库存更新主表失败】:{}",JSONObject.toJSON(stCShopItemVirtualHighStockDO));
//                throw new NDSException("保存失败！");
//            }
//        }
//
//        //判断子表数据是否存在
//        if (itemMap != null && !itemMap.isEmpty()) {
//            StCShopVirtualHighStockDO stCShopItemVirtualHighStockDO = stCVirtualHighStockMapper.selectById(id);
//            //状态数据检查
//            checkStatus(stCShopItemVirtualHighStockDO, id, valueHolder);
//
//            if (!saveItemProcess(querySession, stCShopItemVirtualHighStockDO, itemMap)) {
//                return valueHolder;
//            } else {
//                StCShopVirtualHighStockDO stCShopItemVirtualHighStockNewDO = new StCShopVirtualHighStockDO();
//                stCShopItemVirtualHighStockNewDO.setId(stCShopItemVirtualHighStockDO.getId());
//                BeanUtils.copyProperties(stCShopItemVirtualHighStockDO,stCShopItemVirtualHighStockNewDO);
//                StBeanUtils.makeModifierField(stCShopItemVirtualHighStockNewDO, querySession.getUser());
//                stCVirtualHighStockMapper.updateById(stCShopItemVirtualHighStockNewDO);
//                //推送数据到es
//                this.pushDataToEs(stCShopItemVirtualHighStockDO.getId());
//            }
//        }
//        valueHolder = ValueHolderUtils.getSuccessValueHolder(id, StConstant.TAB_ST_C_SHOP_VIRTUAL_HIGH_STOCK);
//        return valueHolder;
//    }
//
//
//    private void checkStatus(StCShopVirtualHighStockDO stCShopItemVirtualHighStockDO, Long objid, ValueHolder valueHolder) {
//        if (objid > 0) {
//            StCShopVirtualHighStockDO stCShopItemVirtualHighStockOldDO = stCVirtualHighStockMapper.selectById(objid);
//            if (stCShopItemVirtualHighStockOldDO != null) {
//                int state = stCShopItemVirtualHighStockOldDO.getState();
//                if (state != 1) {
//                    throw new NDSException("单据处于未审核状态才能进行编辑！");
//                }
//            } else {
//                throw new NDSException("数据不存在！");
//            }
//            if (stCShopItemVirtualHighStockOldDO.getEndTime() == null) {
//                stCShopItemVirtualHighStockOldDO.setEndTime(stCShopItemVirtualHighStockOldDO.getEndTime());
//            }
//            if (stCShopItemVirtualHighStockOldDO.getBeginTime() == null) {
//                stCShopItemVirtualHighStockOldDO.setBeginTime(stCShopItemVirtualHighStockOldDO.getBeginTime());
//            }
//        }
//
//        /**
//         * 保存前进行的判断
//         */
//        if (stCShopItemVirtualHighStockDO != null) {
//            if (stCShopItemVirtualHighStockDO.getEndTime() != null && stCShopItemVirtualHighStockDO.getBeginTime() != null) {
//                if (stCShopItemVirtualHighStockDO.getEndTime().before(stCShopItemVirtualHighStockDO.getBeginTime())) {
//                    throw new NDSException("方案的生效结束日期不能小于生效开始日期！");
//                }
//                if (stCShopItemVirtualHighStockDO.getEndTime().before(new Date())) {
//                    throw new NDSException("方案的结束日期不能小于当前日期！");
//                }
//            }
//        }
//    }
//
//
//    /**
//     * 子表新增
//     */
//    @Transactional(rollbackFor = Exception.class)
//    public boolean saveItemProcess(QuerySession session,
//                                   StCShopVirtualHighStockDO stCShopItemVirtualHighStockDO,
//                                   JSONArray itemAllMap) {
//        // 旧数据取得，防止重复
//        List<StCShopVirtualHighStockItemDO> itemOldList = itemMapper.selectList(new QueryWrapper<StCShopVirtualHighStockItemDO>()
//                .lambda().eq(StCShopVirtualHighStockItemDO::getStCShopVirtualHighStockId, stCShopItemVirtualHighStockDO.getId()));
//        Map<Object, StCShopVirtualHighStockItemDO> itemOldMap = new HashMap<>();
//        // 数据防止重复
//        Map<String, StCShopVirtualHighStockItemDO> uniqueMap = new HashMap<>();
//        for (StCShopVirtualHighStockItemDO itemDO : itemOldList) {
//            String str = itemDO.getPsCSkuId() + "," + itemDO.getPsCProId() + "," + itemDO.getSkuId() + "," + itemDO.getNumberId();
//            itemOldMap.put(str, itemDO);
//        }
//        if (itemAllMap != null) {
//            List<StCShopVirtualHighStockItemDO> stCVirtualHighStockItemDOList = JSON.parseObject(itemAllMap.toJSONString(),
//                    new TypeReference<ArrayList<StCShopVirtualHighStockItemDO>>() {
//                    });
//            if (!CollectionUtils.isEmpty(stCVirtualHighStockItemDOList)) {
//                List<Long> collect = stCVirtualHighStockItemDOList.stream().filter((ItemDO) -> ItemDO.getId() != -1).map(StCShopVirtualHighStockItemDO::getId).collect(Collectors.toList());
//                Map<Long, StCShopVirtualHighStockItemDO> ItemMap = new HashMap<>();
//                if(collect.size()>0){
//                    List<StCShopVirtualHighStockItemDO> itemDOS = itemMapper.selectBatchIds(collect);
//                    ItemMap = itemDOS.stream().collect(Collectors.toMap(StCShopVirtualHighStockItemDO::getId, Function.identity()));
//                }
//
//                for (StCShopVirtualHighStockItemDO stCVirtualHighStockItemDO : stCVirtualHighStockItemDOList) {
//                    Long itemId = stCVirtualHighStockItemDO.getId();
//                    if (itemId> 0) {
//                        //基本字段值设置
//                        StCShopVirtualHighStockItemDO stCShopVirtualHighStockItemDO = ItemMap.get(itemId);
//                        if (stCShopVirtualHighStockItemDO == null) {
//                            throw new NDSException("此明细不存在！");
//                        }
//                        if (stCVirtualHighStockItemDO.getExpireValue() != null) {
//                            stCShopVirtualHighStockItemDO.setExpireValue(stCVirtualHighStockItemDO.getExpireValue());
//                        }
//                        if (stCVirtualHighStockItemDO.getVirtualHighValue() != null) {
//                            stCShopVirtualHighStockItemDO.setVirtualHighValue(stCVirtualHighStockItemDO.getVirtualHighValue());
//                        }
//                        //冗余主表字段
//                        stCShopVirtualHighStockItemDO.setPlanName(stCShopItemVirtualHighStockDO.getPlan());
//                        stCShopVirtualHighStockItemDO.setStatus(stCShopItemVirtualHighStockDO.getState());
//                        stCShopVirtualHighStockItemDO.setBeginTime(stCShopItemVirtualHighStockDO.getBeginTime());
//                        stCShopVirtualHighStockItemDO.setEndTime(stCShopItemVirtualHighStockDO.getEndTime());
//                        stCShopVirtualHighStockItemDO.setCpCShopId(stCShopItemVirtualHighStockDO.getCpCShopId());
//                        stCShopVirtualHighStockItemDO.setCpCShopEcode(stCShopItemVirtualHighStockDO.getCpCShopEcode());
//                        stCShopVirtualHighStockItemDO.setCpCShopTitle(stCShopItemVirtualHighStockDO.getCpCShopTitle());
//                        stCShopVirtualHighStockItemDO.setMainCreationdate(stCShopItemVirtualHighStockDO.getCreationdate());
//                        StBeanUtils.makeModifierField(stCShopVirtualHighStockItemDO, session.getUser());
//                        int updateResult = itemMapper.updateById(stCShopVirtualHighStockItemDO);
//                        if (updateResult <= 0) {
//                            log.debug(LogUtil.format("【虚高库存更新子表失败】:{}", JSONObject.toJSON(stCVirtualHighStockItemDO));
//                            throw new NDSException("保存失败！");
//                        }
//
//                    } else {
//                        StCVirtualHighStockSaveService bean = ApplicationContextHandle.getBean(StCVirtualHighStockSaveService.class);
//                        if (!bean.insterItemProcess(session, stCShopItemVirtualHighStockDO, itemOldMap, uniqueMap, stCVirtualHighStockItemDO)) {
//                            log.debug(LogUtil.format("【虚高库存新新子表失败】:{}", JSONObject.toJSON(stCVirtualHighStockItemDO));
//                            throw new NDSException("保存失败！");
//                        }
//                    }
//                }
//                //推送数据到es
//                this.pushDataToEs(stCShopItemVirtualHighStockDO.getId());
//            } else {
//                log.debug(LogUtil.format("【虚高库存新增子表失败】:{}", JSONObject.toJSON(itemAllMap));
//                throw new NDSException("保存失败！");
//            }
//        }
//        return true;
//    }
//
//    /**
//     * 新增子表数据
//     * @param session
//     * @param stCShopItemVirtualHighStockDO
//     * @param itemOldMap
//     * @param uniqueMap
//     * @param stCVirtualHighStockItemDO
//     * @return
//     */
//    public boolean insterItemProcess(QuerySession session,
//                                     StCShopVirtualHighStockDO stCShopItemVirtualHighStockDO,
//                                     Map<Object, StCShopVirtualHighStockItemDO> itemOldMap,
//                                     Map<String, StCShopVirtualHighStockItemDO> uniqueMap,
//                                     StCShopVirtualHighStockItemDO stCVirtualHighStockItemDO) {
//        //虚高失效值和虚高库存必填
//        if ((stCVirtualHighStockItemDO.getVirtualHighValue() == null && stCVirtualHighStockItemDO.getExpireValue() == null)) {
//            throw new NDSException("虚高失效值和虚高库存不能为空！");
//        }
//        Long cpCShopId = stCShopItemVirtualHighStockDO.getCpCShopId();
//        // 明细数据关联查询
//        List<SgChannelProductQueryForSTResult> results = generalStrategyCheckService.checkParam(
//                StConstant.TAB_ST_C_SHOP_VIRTUAL_HIGH_STOCK,cpCShopId,stCVirtualHighStockItemDO);
//        //明细参数检验
//        this.checkParam(cpCShopId, stCVirtualHighStockItemDO, results);
//        //明细数据组装
//        List<StCShopVirtualHighStockItemDO> itemDOList = this.findByCondition(stCVirtualHighStockItemDO, results);
//
//        List<StCShopVirtualHighStockItemDO> itemDoUpdateList = new ArrayList<>();
//        List<StCShopVirtualHighStockItemDO> itemDoInsertList = new ArrayList<>();
//        for (StCShopVirtualHighStockItemDO itemDO : itemDOList) {
//            String str = itemDO.getPsCSkuId()+","+itemDO.getPsCProId()+","+itemDO.getSkuId()+","+itemDO.getNumberId();
//            //去除插入或修改的数据重复
//            if (uniqueMap.containsKey(str)) {
//                continue;
//            } else {
//                uniqueMap.put(str, itemDO);
//            }
//            //判断是否已经存在，否则插入
//            if (itemOldMap.containsKey(str)) {
//                //基本字段值设置
//                StBeanUtils.makeModifierField(stCVirtualHighStockItemDO, session.getUser());
//                stCVirtualHighStockItemDO.setStCShopVirtualHighStockId(stCShopItemVirtualHighStockDO.getId());
//                stCVirtualHighStockItemDO.setPsCProId(itemDO.getPsCProId());
//                stCVirtualHighStockItemDO.setPsCSkuEcode(itemDO.getPsCSkuEcode());
//                stCVirtualHighStockItemDO.setPsCSkuId(itemDO.getPsCSkuId());
//                stCVirtualHighStockItemDO.setPsCProEcode(itemDO.getPsCProEcode());
//                stCVirtualHighStockItemDO.setId(itemOldMap.get(str).getId());
//                //冗余主表字段
//                stCVirtualHighStockItemDO.setPlanName(stCShopItemVirtualHighStockDO.getPlan());
//                stCVirtualHighStockItemDO.setStatus(stCShopItemVirtualHighStockDO.getState());
//                stCVirtualHighStockItemDO.setBeginTime(stCShopItemVirtualHighStockDO.getBeginTime());
//                stCVirtualHighStockItemDO.setEndTime(stCShopItemVirtualHighStockDO.getEndTime());
//                stCVirtualHighStockItemDO.setCpCShopId(stCShopItemVirtualHighStockDO.getCpCShopId());
//                stCVirtualHighStockItemDO.setCpCShopEcode(stCShopItemVirtualHighStockDO.getCpCShopEcode());
//                stCVirtualHighStockItemDO.setCpCShopTitle(stCShopItemVirtualHighStockDO.getCpCShopTitle());
//                stCVirtualHighStockItemDO.setMainCreationdate(stCShopItemVirtualHighStockDO.getCreationdate());
//                itemDoUpdateList.add(stCVirtualHighStockItemDO);
//            } else {
//                //基本字段值设置
//                StBeanUtils.makeCreateField(itemDO, session.getUser());
//                itemDO.setId(ModelUtil.getSequence(StConstant.TAB_ST_C_SHOP_VIRTUAL_HIGH_STOCK_ITEM));
//                itemDO.setStCShopVirtualHighStockId(stCShopItemVirtualHighStockDO.getId());
//                itemDO.setExpireValue(stCVirtualHighStockItemDO.getExpireValue());
//                itemDO.setVirtualHighValue(stCVirtualHighStockItemDO.getVirtualHighValue());
//                //冗余主表字段
//                itemDO.setPlanName(stCShopItemVirtualHighStockDO.getPlan());
//                itemDO.setStatus(stCShopItemVirtualHighStockDO.getState());
//                itemDO.setBeginTime(stCShopItemVirtualHighStockDO.getBeginTime());
//                itemDO.setEndTime(stCShopItemVirtualHighStockDO.getEndTime());
//                itemDO.setCpCShopId(stCShopItemVirtualHighStockDO.getCpCShopId());
//                itemDO.setCpCShopEcode(stCShopItemVirtualHighStockDO.getCpCShopEcode());
//                itemDO.setCpCShopTitle(stCShopItemVirtualHighStockDO.getCpCShopTitle());
//                itemDO.setMainCreationdate(stCShopItemVirtualHighStockDO.getCreationdate());
//                itemDoInsertList.add(itemDO);
//            }
//        }
//        StCVirtualHighStockSaveService bean =
//                ApplicationContextHandle.getBean(StCVirtualHighStockSaveService.class);
//        bean.batchSaveOrUpdate(itemDoUpdateList, itemDoInsertList);
//        return true;
//    }
//
//    @Transactional(rollbackFor = Exception.class)
//    public void batchSaveOrUpdate(List<StCShopVirtualHighStockItemDO> itemDoUpdateList,
//                                  List<StCShopVirtualHighStockItemDO> itemDoInsertList) {
//        List<StCShopVirtualHighStockItemDO> allList = new ArrayList<>();
//        try {
//            if (CollectionUtils.isNotEmpty(itemDoInsertList)) {
//                stockItemService.saveBatch(itemDoInsertList, 500);
//                allList.addAll(itemDoInsertList);
//            }
//
//            if (CollectionUtils.isNotEmpty(itemDoUpdateList)) {
//                stockItemService.updateBatchById(itemDoUpdateList, 500);
//                allList.addAll(itemDoUpdateList);
//            }
//        } catch (Exception e){
//            throw new NDSException("批量插入更新异常", e);
//        }
//    }
//
//    /**
//     *  校验条码 和平台条码id 是否唯一
//     * @return
//     */
//    private boolean checkProSku(Long strategyId,Long prsku,String ptSku) {
//        List<StCShopVirtualHighStockItemDO> itemDOS = itemMapper.selectByPsSkuIdAndPtSkuId(strategyId, ptSku, prsku);
//        if (!org.apache.commons.collections.CollectionUtils.isEmpty(itemDOS)) {
//            return true;
//        }
//        return false;
//    }
//    private List<SgChannelProductQueryForSTResult> getResultByCondition(Long psCSkuId,Long psCProId,
//                                                                        String psCSkuEcode,String psCProEcode,
//                                                                        String skuId,String numberId,
//                                                                        Long cpCShopId) {
//        //获取查询结果
//        List<SgChannelProductQueryForSTResult> sgChannelProductQueryForSTResults =
//                sgChannelProductQueryCmd.queryChannelProductForST(this.assembleSgRequest(psCSkuId,psCProId,psCSkuEcode,psCProEcode,
//                        StringUtils.isBlank(skuId) ? null:skuId,
//                        StringUtils.isBlank(numberId) ? null:numberId,
//                        cpCShopId));
//        return sgChannelProductQueryForSTResults;
//    }
//
//    /**
//     * 根据录入的商品id 条码 和平台id 和平台条码 校验数据
//     * @param stCShopVirtualHighStockItemDO
//     * @param sgChannelProductQueryForSTResults
//     * @return
//     */
//    private List<StCShopVirtualHighStockItemDO> findByCondition(StCShopVirtualHighStockItemDO stCShopVirtualHighStockItemDO,
//                                                                List<SgChannelProductQueryForSTResult> sgChannelProductQueryForSTResults) {
//        Long psCSkuId = stCShopVirtualHighStockItemDO.getPsCSkuId();
//        Long psCProId = stCShopVirtualHighStockItemDO.getPsCProId();
//        String psCSkuEcode = stCShopVirtualHighStockItemDO.getPsCSkuEcode();
//        String psCProEcode = stCShopVirtualHighStockItemDO.getPsCProEcode();
//        String skuId = stCShopVirtualHighStockItemDO.getSkuId();
//        String numberId = stCShopVirtualHighStockItemDO.getNumberId();
//        StCShopVirtualHighStockItemDO itemDO = null;
//        List<StCShopVirtualHighStockItemDO> itemDOList = Lists.newArrayList();
//        for(SgChannelProductQueryForSTResult e : sgChannelProductQueryForSTResults) {
//            if (StringUtils.isEmpty(skuId) && StringUtils.isEmpty(numberId)) {
//                if (StringUtils.isNotBlank(psCProEcode)) {
//                    e.setPsCProEcode(psCProEcode);
//                }
//                if (StringUtils.isNotBlank(psCSkuEcode)) {
//                    e.setPsCSkuEcode(psCSkuEcode);
//                }
//                if (psCSkuId != null) {
//                    e.setPsCSkuId(psCSkuId);
//                }
//                if (psCProId != null) {
//                    e.setPsCProId(psCProId);
//                }
//            }
//            itemDO = this.assembleItemDo(e.getPsCSkuId(),e.getPsCProId(),e.getPsCSkuEcode(),e.getPsCProEcode(),e.getSkuId(),e.getNumiid());
//            itemDOList.add(itemDO);
//        }
//        return itemDOList;
//    }
//
//    /**
//     * <AUTHOR>
//     * @Description 虚高库存明细参数校验
//     * @Date 15:08 2020/12/25
//     * @param cpCShopId
//     * @param stCVirtualHighStockItemDO
//     * @param sgChannelProductQueryForSTResults
//     * @return com.jackrain.nea.util.ValueHolder
//     **/
//    public void checkParam(Long cpCShopId, StCShopVirtualHighStockItemDO stCVirtualHighStockItemDO,
//                                  List<SgChannelProductQueryForSTResult> sgChannelProductQueryForSTResults) {
//        Long psCSkuId = stCVirtualHighStockItemDO.getPsCSkuId();
//        Long psCProId = stCVirtualHighStockItemDO.getPsCProId();
//        String skuId = stCVirtualHighStockItemDO.getSkuId();
//        String numberId = stCVirtualHighStockItemDO.getNumberId();
//        if (CollectionUtils.isEmpty(sgChannelProductQueryForSTResults)) {
//            if (psCSkuId != null) {
//                PsCSku psCSku = rpcPsService.getSkuById(psCSkuId);
//                if (psCSku != null) {
//                    throw new NDSException("未查询到条码为:" + psCSku.getEcode() + "的数据");
//                }
//                throw new NDSException("未查询到该条码id为:" + psCSkuId + "的数据");
//            } else if (psCProId != null) {
//                PsCPro psCPro = rpcPsService.queryProByID(psCProId);
//                if (psCPro != null) {
//                    throw new NDSException("未查询到商品编码为:" + psCPro.getEcode() + "的数据");
//                }
//                throw new NDSException("未查询到未查询到该商品id为:" + psCProId + "的数据");
//            } else if (StringUtils.isNotBlank(skuId)) {
//                throw new NDSException("未查询到平台条码id为:" + skuId + "的数据");
//            } else if (StringUtils.isNotBlank(numberId)) {
//                throw new NDSException("未查询到平台商品id为:" + numberId + "的数据");
//            } else if (cpCShopId != null) {
//                throw new NDSException("未查询到相关记录");
//            }
//        }
//    }
//
//    /**
//     * 封装返回参数
//     * @return
//     */
//    private StCShopVirtualHighStockItemDO assembleItemDo(Long psCSkuId, Long psCProId,
//                                                         String psCSkuEcode, String psCProEcode,
//                                                         String skuId, String numberId) {
//        StCShopVirtualHighStockItemDO itemDO = new StCShopVirtualHighStockItemDO();
//        //条码id
//        itemDO.setPsCSkuId(psCSkuId);
//        //条码
//        itemDO.setPsCSkuEcode(psCSkuEcode);
//        //商品编码
//        itemDO.setPsCProEcode(psCProEcode);
//        //商品id
//        itemDO.setPsCProId(psCProId);
//        itemDO.setNumberId(numberId);
//        itemDO.setSkuId(skuId);
//        return itemDO;
//    }
//
//    /**
//     * 组装请求参数
//     * @param psCSkuId
//     * @param psCProId
//     * @param psCSkuEcode
//     * @param psCProEcode
//     * @return
//     */
//    private SgChannelProductQueryForSTRequest assembleSgRequest(Long psCSkuId,Long psCProId,
//                                                                String psCSkuEcode,String psCProEcode,
//                                                                String skuId,String numberId,
//                                                                Long cpCShopId) {
//        SgChannelProductQueryForSTRequest request = new SgChannelProductQueryForSTRequest();
//        List<Long> cpCShopIds = Lists.newArrayList();
//        List<Long> psCProIds = Lists.newArrayList();
//        List<Long> psCSkuIds = Lists.newArrayList();
//        List<String> psCProEcodes = Lists.newArrayList();
//        List<String> psCSkuEcodes = Lists.newArrayList();
//        List<String> skuIds = Lists.newArrayList();
//        List<String> numberIds = Lists.newArrayList();
//        cpCShopIds.add(cpCShopId);
//        psCProIds.add(psCProId);
//        psCSkuIds.add(psCSkuId);
//        psCProEcodes.add(psCProEcode);
//        psCSkuEcodes.add(psCSkuEcode);
//        skuIds.add(skuId);
//        numberIds.add(numberId);
//        if (!CollectionUtils.isEmpty(cpCShopIds) && cpCShopId != null) {
//            request.setCpCShopIdList(cpCShopIds);
//        }
//        if (!CollectionUtils.isEmpty(psCProIds) && psCProId != null) {
//            request.setPsCProIdList(psCProIds);
//        }
//        if (!CollectionUtils.isEmpty(psCSkuIds) && psCSkuId != null) {
//            request.setPsCSkuIdList(psCSkuIds);
//        }
//        if (!CollectionUtils.isEmpty(psCProEcodes) && StringUtils.isNotBlank(psCProEcode)) {
//            request.setPsCProEcodeList(psCProEcodes);
//        }
//        if (!CollectionUtils.isEmpty(psCSkuEcodes) && StringUtils.isNotBlank(psCSkuEcode)) {
//            request.setPsCSkuEcodeList(psCSkuEcodes);
//        }
//        if (!CollectionUtils.isEmpty(skuIds) && StringUtils.isNotBlank(skuId)) {
//            request.setPtSkuIdList(skuIds);
//        }
//        if (!CollectionUtils.isEmpty(numberIds) && StringUtils.isNotBlank(numberId)) {
//            request.setPtProIdList(numberIds);
//        }
//        return request;
//    }
//
//    /**
//     * 推送数据到es
//     * @param objid
//     */
//    private void pushDataToEs(Long objid) {
//        //推送ES数据
//        try {
//            //做更新的需要先查询更新后数据库的实体在推ES
//            List<StCShopVirtualHighStockItemDO> itemDOList = itemMapper.selectList(new QueryWrapper<StCShopVirtualHighStockItemDO>().lambda()
//                    .eq(StCShopVirtualHighStockItemDO::getStCShopVirtualHighStockId, objid));
//            StCShopVirtualHighStockDO virtualHighStockDO = stCVirtualHighStockMapper.selectById(objid);
//            DatasToEsUtils.batchInsertVirtualHighStockEsData(virtualHighStockDO,itemDOList,
//                    StConstant.TAB_ST_C_SHOP_VIRTUAL_HIGH_STOCK);
//            if (log.isDebugEnabled()) {
//                log.debug(LogUtil.format("店铺商品虚高库存明细表推送es成功：");
//            }
//        } catch (Exception ex) {
//            log.debug(LogUtil.format("店铺商品虚高库存明细表推数据到ES失败：" + ex.toString());
//        }
//    }
//}
