package com.jackrain.nea.st.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.st.common.StConstant;
import com.jackrain.nea.st.mapper.StCSellOwngoodsMapper;
import com.jackrain.nea.st.model.table.StCSellOwngoodsDO;
import com.jackrain.nea.st.utils.StBeanUtils;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;

/**
 * <AUTHOR>
 * @Description 经销商自有商品反审核业务类
 * @Date 2019/3/12
 **/
@Component
@Slf4j
@Transactional
public class SellOwnGoodsReverseAuditService extends CommandAdapter {

    @Autowired
    private StCSellOwngoodsMapper stCSellOwngoodsMapper;

    /**
     * @return com.jackrain.nea.util.ValueHolder
     * <AUTHOR>
     * @Description 经销商自有商品反审核方法
     * @Date 2019/3/12
     * @Param [session]
     **/
    public ValueHolder reverseAuditSellOwnGoods(QuerySession session) {

        DefaultWebEvent event = session.getEvent();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(
                event.getParameterValue("param"), "yyyy-MM-dd HH:mm:ss", SerializerFeature.WriteMapNullValue),
                Feature.OrderedField);
        log.debug(LogUtil.format("请求JSON: ") + param.toString());

        //生成反审核Json数组
        JSONArray auditArray = StBeanUtils.makeUnAuditJsonArray(param);
        HashMap<Long, Object> errorMap = new HashMap<Long, Object>();
        ValueHolder valueHolder = new ValueHolder();

        for (int i = 0; i < auditArray.size(); i++) {
            Long id = Long.valueOf(auditArray.get(i).toString());
            try {
                //反审核验证
                unAuditCheck(id);

                JSONObject jsonObject = new JSONObject();
                StBeanUtils.makeModifierField(jsonObject, session.getUser());
                StBeanUtils.makeCancelField(jsonObject);//反审-审核置空信息
                //BILL_STATUS    1:未审核 2:已审核 3:已作废 4:已结案
                jsonObject.put("ID", id);
                jsonObject.put("BILL_STATUS", StConstant.CON_BILL_STATUS_01);

                //更新单据状态
                int count = stCSellOwngoodsMapper.updateAtrributes(jsonObject);
                if (count < 0) {
                    throw new Exception();
                }
            } catch (Exception e) {
                errorMap.put(id, e.getMessage());
            }
        }

        valueHolder = StBeanUtils.getExcuteValueHolder(auditArray.size(),errorMap);
        return valueHolder;
    }

    /**
     * @return com.alibaba.fastjson.JSONObject
     * <AUTHOR>
     * @Description 反审核验证
     * @Date 2019/3/11
     * @Param [objid]
     **/
    private JSONObject unAuditCheck(Long objid) throws Exception {
        //主表记录不存在
        StCSellOwngoodsDO stCSellOwngoodsDO = stCSellOwngoodsMapper.selectById(objid);
        if (stCSellOwngoodsDO == null) {
            throw new Exception("当前记录已不存在！");
        } else {
            if (StConstant.CON_BILL_STATUS_01.equals(stCSellOwngoodsDO.getBillStatus())) {
                throw new NDSException("当前记录未审核，不允许做反审核！");
            } else if (StConstant.CON_BILL_STATUS_04.equals(stCSellOwngoodsDO.getBillStatus())) {
                throw new NDSException("当前记录已结案，不允许做反审核！");
            } else if (StConstant.CON_BILL_STATUS_03.equals(stCSellOwngoodsDO.getBillStatus())) {
                throw new NDSException("当前记录已作废，不允许做反审核！");
            }
        }
        return null;
    }
}
