package com.jackrain.nea.st.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.st.common.StConstant;
import com.jackrain.nea.st.mapper.StCDistributionItemMapper;
import com.jackrain.nea.st.mapper.StCDistributionMapper;
import com.jackrain.nea.st.model.table.StCDistributionDO;
import com.jackrain.nea.st.model.table.StCDistributionItemDO;
import com.jackrain.nea.st.utils.StBeanUtils;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import java.math.BigDecimal;
import java.util.Date;
import java.util.HashMap;
import java.util.List;

/**
 * @Descroption 分销代销策略审核
 * <AUTHOR>
 * @Date 2019/3/7 10:55
 */
@Component
@Slf4j
public class DistributionAuditService extends CommandAdapter {
    @Autowired
    private StCDistributionMapper mapper;
    @Autowired
    private StCDistributionItemMapper itemMapper;
    @Override
    public ValueHolder execute(QuerySession querySession) throws NDSException {
        //1.获取传入参数
        DefaultWebEvent event = querySession.getEvent();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(event.getParameterValue("param"),
                "yyyy-MM-dd HH:mm:ss", SerializerFeature.WriteMapNullValue), Feature.OrderedField);
        if (param == null) {
            throw new NDSException("参数为空！");
        }
        if(log.isDebugEnabled()){
            log.debug(LogUtil.format("Start DistributionAuditService.execute. ReceiveParams: {}"),
                    param.toJSONString());
        }
        //2.定义错误记录数
        HashMap<Long, Object> errMap = new HashMap<>();
        //3.生成审核Json数组
        JSONArray auditArray = StBeanUtils.makeAuditJsonArray(param);
        for (int i = 0; i < auditArray.size(); i++) {
            Long id = auditArray.getLong(i);
            try {
                //4.遍历审核方法
                auditDistribution(id, querySession);
            } catch (Exception e) {
                errMap.put(id, e.getMessage());
            }
        }
        //5.返回ValueHolder对象
        return StBeanUtils.getExcuteValueHolder(auditArray.size(), errMap);
    }

    private void auditDistribution(Long id, QuerySession querySession) {
        StCDistributionDO distribution = mapper.selectById(id);
        List<StCDistributionItemDO> itemList = itemMapper.selectItemByMainId(id);
        //1.数据校验
        checkDistribution(distribution);
        //查询校验商品明细是否有数据及是否存在结算价为0
        checkDistributionItem(itemList);
        //2.更新单据状态
        StBeanUtils.makeModifierField(distribution, querySession.getUser());
        distribution.setBillStatus(StConstant.CON_BILL_STATUS_02);
        setAuditCommonField(distribution, querySession.getUser());
        int updateNum = mapper.updateById(distribution);
        if (updateNum < 0) {
            throw new NDSException("方案:" + distribution.getEname() + "审核失败！");
        }
    }

    private void checkDistribution(StCDistributionDO distribution) {
        if (distribution == null) {
            throw new NDSException("当前记录已不存在！");
        } else {
            if (StConstant.CON_BILL_STATUS_02.equals(distribution.getBillStatus())) {
                throw new NDSException("当前记录已审核，不允许重复审核！");
            }else if(StConstant.CON_BILL_STATUS_03.equals(distribution.getBillStatus())){
                throw new NDSException("当前记录已作废，不允许审核！");
            }
        }
    }

    private void checkDistributionItem(List<StCDistributionItemDO> itemList) {
        if (itemList.size() <= 0) {
            throw new NDSException("当前商品明细记录无明细，不允许审核！");
        }
        for(StCDistributionItemDO stCDistributionItemDO : itemList){
            if(stCDistributionItemDO.getSettlementprice() != null ){
                if(stCDistributionItemDO.getSettlementprice().compareTo(BigDecimal.ZERO) == 0){
                    throw new NDSException("存在结算价为0，不允许审核！");
                }
            }
        }
    }
    private void setAuditCommonField(StCDistributionDO distribution, User user) {
        distribution.setCheckid(Long.valueOf(user.getId()));
        distribution.setCheckename(user.getEname());
        distribution.setCheckname(user.getName());
        distribution.setChecktime(new Date());
    }

}
