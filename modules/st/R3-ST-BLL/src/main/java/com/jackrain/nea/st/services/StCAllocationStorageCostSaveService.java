package com.jackrain.nea.st.services;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.st.common.StConstant;
import com.jackrain.nea.st.mapper.StCAllocationStorageCostStrategyMapper;
import com.jackrain.nea.st.model.table.StCAllocationStorageCostStrategy;
import com.jackrain.nea.st.utils.JsonUtils;
import com.jackrain.nea.st.utils.StBeanUtils;
import com.jackrain.nea.st.utils.ValueHolderUtils;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2023/8/16 17:37
 * @Description
 */
@Component
@Slf4j
public class StCAllocationStorageCostSaveService extends CommandAdapter {

    @Resource
    private StCAllocationStorageCostStrategyMapper mapper;

    @Override
    public ValueHolder execute(QuerySession querySession) throws NDSException {
        ValueHolder valueHolder = new ValueHolder();
        DefaultWebEvent event = querySession.getEvent();

        List<String> nullKeyList = new ArrayList<>();
        JSONObject param = JsonUtils.filterNull((JSONObject) event.getParameterValue("param"), nullKeyList);
        log.info(LogUtil.format("StCAllocationStorageCostSaveService.execute,param:{}",
                "StCAllocationStorageCostSaveService#execute"), param.toJSONString());
        if (param != null) {
            Long id = param.getLong("objid");
            JSONObject fixColumn = param.getJSONObject("fixcolumn");
            if (fixColumn != null) {
                JSONObject mainMap = fixColumn.getJSONObject(StConstant.TAB_ST_C_ALLOCATION_STORAGE_COST_STRATEGY);
                if (id != null && id < 0) {
                    //新增
                    valueHolder = saveFunction(mainMap, querySession);
                } else {
                    //编辑
                    valueHolder = updateFunction(mainMap, querySession, id);
                }
            }
        } else {
            throw new NDSException("参数为空！");
        }
        return valueHolder;
    }

    /**
     * 新增操作
     *
     * @param mainMap      主表数据
     * @param querySession 封装数据
     * @return 返回状态
     */
    private ValueHolder saveFunction(JSONObject mainMap, QuerySession querySession) {

        StCAllocationStorageCostStrategy allocationStorageCostStrategy =
                JsonUtils.jsonParseClass(mainMap, StCAllocationStorageCostStrategy.class);
        ValueHolder holder;
        if (allocationStorageCostStrategy != null) {
            Integer count = mapper.selectCount(new LambdaQueryWrapper<StCAllocationStorageCostStrategy>()
                    .eq(StCAllocationStorageCostStrategy::getFactory, allocationStorageCostStrategy.getFactory())
                    .eq(StCAllocationStorageCostStrategy::getCpCPhyWarehouseId, allocationStorageCostStrategy.getCpCPhyWarehouseId()));
            if (count > 0) {
                holder = ValueHolderUtils.getFailValueHolder("数据重复！");
                return holder;
            }
            allocationStorageCostStrategy.setId(ModelUtil.getSequence(StConstant.TAB_ST_C_ALLOCATION_STORAGE_COST_STRATEGY));
            StBeanUtils.makeCreateField(allocationStorageCostStrategy, querySession.getUser());
            if (mapper.insert(allocationStorageCostStrategy) <= 0) {
                holder = ValueHolderUtils.getFailValueHolder("保存失败！");
                return holder;
            }
        } else {
            holder = ValueHolderUtils.getFailValueHolder("保存失败！");
            return holder;
        }
        holder = ValueHolderUtils.getSuccessValueHolder(allocationStorageCostStrategy.getId(), StConstant.TAB_ST_C_ALLOCATION_STORAGE_COST_STRATEGY);
        return holder;
    }

    private ValueHolder updateFunction(JSONObject mainMap, QuerySession querySession, Long objid) {
        StCAllocationStorageCostStrategy oldMainData = mapper.selectById(objid);
        ValueHolder holder;
        if (oldMainData == null) {
            holder = ValueHolderUtils.getFailValueHolder("当前记录不存在，不允许编辑！");
            return holder;
        }
        //主表更新，objid就是主表ID
        if (mainMap != null && !mainMap.isEmpty()) {
            StCAllocationStorageCostStrategy allocationStorageCostStrategy =
                    JsonUtils.jsonParseClass(mainMap, StCAllocationStorageCostStrategy.class);
            if (allocationStorageCostStrategy.getFactory() == null) {
                allocationStorageCostStrategy.setFactory(oldMainData.getFactory());
            }
            if (allocationStorageCostStrategy.getCpCPhyWarehouseId() == null) {
                allocationStorageCostStrategy.setCpCPhyWarehouseId(oldMainData.getCpCPhyWarehouseId());
            }
            Integer count = mapper.selectCount(new LambdaQueryWrapper<StCAllocationStorageCostStrategy>()
                    .eq(StCAllocationStorageCostStrategy::getFactory, allocationStorageCostStrategy.getFactory())
                    .eq(StCAllocationStorageCostStrategy::getCpCPhyWarehouseId, allocationStorageCostStrategy.getCpCPhyWarehouseId())
                    .ne(StCAllocationStorageCostStrategy::getId, objid));
            if (count > 0) {
                holder = ValueHolderUtils.getFailValueHolder("数据重复！");
                return holder;
            }

            allocationStorageCostStrategy.setId(objid);
            StBeanUtils.makeModifierField(allocationStorageCostStrategy, querySession.getUser());
            if (mapper.updateById(allocationStorageCostStrategy) < 0) {
                holder = ValueHolderUtils.getFailValueHolder("保存失败！");
                return holder;
            }
        }
        holder = ValueHolderUtils.getSuccessValueHolder(objid, StConstant.TAB_ST_C_ALLOCATION_STORAGE_COST_STRATEGY);
        return holder;
    }
}
