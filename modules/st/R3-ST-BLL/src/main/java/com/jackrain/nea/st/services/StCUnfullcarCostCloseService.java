package com.jackrain.nea.st.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.jackrain.nea.oc.oms.model.enums.YesNoEnum;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.st.annotation.StOperationLog;
import com.jackrain.nea.st.mapper.StCUnfullcarCostItemMapper;
import com.jackrain.nea.st.mapper.StCUnfullcarCostMapper;
import com.jackrain.nea.st.model.enums.CloseStatusEnum;
import com.jackrain.nea.st.model.enums.SubmitStatusEnum;
import com.jackrain.nea.st.model.table.StCUnfullcarCost;
import com.jackrain.nea.st.model.common.StRedisConstant;
import com.jackrain.nea.st.utils.RedisCacheUtil;
import com.jackrain.nea.st.utils.StBeanUtils;
import com.jackrain.nea.st.utils.ValueHolderUtils;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @Date 2022/6/11 13:26
 */
@Component
@Slf4j
public class StCUnfullcarCostCloseService extends CommandAdapter {

    @Autowired
    private StCUnfullcarCostMapper stCUnfullcarCostMapper;

    @Autowired
    private StCUnfullcarCostItemMapper stCUnfullcarCostItemMapper;

    @StOperationLog(operationType = "FINISH", mainTableName = "ST_C_UNFULLCAR_COST", itemsTableName = "ST_C_UNFULLCAR_COST_ITEM")
    @Override
    public ValueHolder execute(QuerySession querySession) throws NDSException {
        DefaultWebEvent event = querySession.getEvent();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(
                event.getParameterValue("param"), "yyyy-MM-dd HH:mm:ss"), Feature.OrderedField);
        if (param == null) {
            throw new NDSException("参数为空！");
        }
        JSONArray voidArray = StBeanUtils.makeUnAuditJsonArray(param);
        if (voidArray.size() == 1) {
            voidAction(voidArray.getLong(0),querySession);
        }else {
            int success = 0;
            int fail = 0;
            for (int i = 0; i < voidArray.size(); i++) {
                Long id = voidArray.getLong(i);
                try {
                    voidAction(id,querySession);
                    success++;
                } catch (Exception e) {
                    fail++;
                }
            }
            if (success != voidArray.size()) {
                return ValueHolderUtils.getFailValueHolder("零担费用设置结案成功" + success + "条,失败" + fail + "条");
            }
        }
        return ValueHolderUtils.getSuccessValueHolder("结案成功");
    }

    private void voidAction(Long id, QuerySession querySession) {
        User user = querySession.getUser();
        StCUnfullcarCost unfullcarCost = stCUnfullcarCostMapper.selectById(id);
        //判断主表是否存在
        if (unfullcarCost == null){
            throw new NDSException("当前记录不存在！");
        }
        if (!SubmitStatusEnum.SUBMIT.getKey().equals(unfullcarCost.getStatus())){
            throw new NDSException("当前单据状态，不允许结案！");
        }
        if (!CloseStatusEnum.NO_CLOSE.getKey().equals(unfullcarCost.getCloseStatus())){
            throw new NDSException("当前单据状态，不允许结案！");
        }
        StCUnfullcarCost newCost = new StCUnfullcarCost();
        newCost.setId(unfullcarCost.getId());
        newCost.setCloseStatus(CloseStatusEnum.CLOSE.getKey());
        newCost.setCloseUserId(Long.valueOf(user.getId()));
        newCost.setCloseTime(LocalDateTime.now());
        newCost.setIsactive(YesNoEnum.N.getKey());
        StBeanUtils.makeModifierField(newCost, querySession.getUser());
        int update = stCUnfullcarCostMapper.updateById(newCost);
        if (update <= 0) {
            throw new NDSException("结案失败！");
        }

        // 清除零担报价关系缓存
        String redisKey = StRedisConstant.buildUnfullcarCostRelationKey(unfullcarCost.getCpCPhyWarehouseId(), unfullcarCost.getCpCLogisticsId());
        RedisCacheUtil.deleteAll(redisKey);
        log.info("清除零担报价关系缓存成功，key={}", redisKey);

        // 清除根据ID查询的分布式缓存
        String idCacheKey = StRedisConstant.buildUnfullcarCostRelationByIdKey(unfullcarCost.getId());
        RedisCacheUtil.deleteAll(idCacheKey);
        log.info("清除零担报价ID缓存成功，key={}", idCacheKey);
    }

}
