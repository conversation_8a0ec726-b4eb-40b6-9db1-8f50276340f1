package com.jackrain.nea.st.services;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.st.mapper.StCSyncSkustockStrategyMapper;
import com.jackrain.nea.st.model.common.StEsConstants;
import com.jackrain.nea.st.model.request.SyncSkuStockItemRequest;
import com.jackrain.nea.st.model.result.SyncSkuStockItemResult;
import com.jackrain.nea.st.model.result.VirtualHighStockItemResult;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.utility.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2020/8/26
 * create at : 2020/8/26 1:56
 */
@Slf4j
@Component
public class StCSyncSkustockQueryService {

    @Autowired
    private StCSyncSkustockStrategyMapper syncSkustockStrategyMapper;

    public ValueHolderV14<List<SyncSkuStockItemResult>> selectCurrentStock(SyncSkuStockItemRequest syncSkuStockItemRequest) {
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("Start StCSyncSkustockQueryService selectCurrentStock Receive Params:{};"),
                    JSONObject.toJSON(syncSkuStockItemRequest));
        }
        ValueHolderV14<List<SyncSkuStockItemResult>> valueHolder14 = new ValueHolderV14<>();
        valueHolder14.setCode(StEsConstants.SUCCESS);
        valueHolder14.setMessage("成功");
        if (StringUtils.isBlank(syncSkuStockItemRequest.getShopId())) {
            valueHolder14.setCode(StEsConstants.FAIL);
            valueHolder14.setMessage("店铺不允许为空");
            return valueHolder14;
        }
        //已审核且没有作废,且在有效期内，多个策略需取最新时间，如果出现创建时间相同，则取id最大的，前提是需要skuID存在当前策略
       List<SyncSkuStockItemResult> syncSkuStockItemResults = syncSkustockStrategyMapper.selectByParam(syncSkuStockItemRequest.getSkuId(),
                syncSkuStockItemRequest.getPsCSkuId(),syncSkuStockItemRequest.getExpireTime(), syncSkuStockItemRequest.getShopId());
        if (CollectionUtils.isNotEmpty(syncSkuStockItemResults)) {
            valueHolder14.setData(syncSkuStockItemResults);
        }
        return valueHolder14;
    }
}
