package com.jackrain.nea.st.validate;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.tableService.MainTableRecord;
import com.jackrain.nea.tableService.TableServiceContext;
import com.jackrain.nea.tableService.validate.BaseValidator;
import com.jackrain.nea.utils.AssertUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Locale;

/**
 * Description： 策略明细保存的校验
 * Author: RESET
 * Date: Created in 2020/7/1 23:03
 * Modified By:
 */
@Slf4j
@Component
public class StCLiveCastItemAddValidate extends BaseValidator {

    @Override
    public void validate(TableServiceContext tableServiceContext, MainTableRecord mainTableRecord) {
        validateInsert(tableServiceContext, mainTableRecord);
    }

    /**
     * 插入的校验
     * @param context
     * @param mainTableRecord
     */
    private void validateInsert(TableServiceContext context, MainTableRecord mainTableRecord) {
        Locale locale = context.getLocale();
        // 保存提取数据
        JSONObject data = mainTableRecord.getMainData().getCommitData();

        AssertUtils.notNull(data.get(StCLiveConsts.RULE_TYPE), "规则类型不能为空！", locale);
        AssertUtils.notNull(data.get(StCLiveConsts.RULE_CONTEXT), "规则内容不能为空！", locale);
    }

}
