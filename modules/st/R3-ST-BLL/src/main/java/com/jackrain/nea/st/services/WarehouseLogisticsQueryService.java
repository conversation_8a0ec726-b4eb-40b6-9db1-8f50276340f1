package com.jackrain.nea.st.services;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.google.common.base.Throwables;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.cpext.model.table.CpLogistics;
import com.jackrain.nea.psext.api.utils.JsonUtils;
import com.jackrain.nea.st.mapper.StCWarehouseLogisticsItemMapper;
import com.jackrain.nea.st.mapper.StCWarehouseLogisticsMapper;
import com.jackrain.nea.st.mapper.StCWarehouseLogisticsRankMapper;
import com.jackrain.nea.st.model.request.WarehouseLogisticsRankRequest;
import com.jackrain.nea.st.model.result.*;
import com.jackrain.nea.st.model.table.StCWarehouseLogisticsDO;
import com.jackrain.nea.st.model.table.StCWarehouseLogisticsItemDO;
import com.jackrain.nea.st.model.table.StCWarehouseLogisticsRankDO;
import com.jackrain.nea.st.rpc.RpcCpService;
import com.jackrain.nea.st.utils.RegionTreeUtils;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.utility.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.assertj.core.util.Lists;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @author: huang.zaizai
 * @since: 2019/08/13
 * create at : 2019/7/24 09:00
 */
@Slf4j
@Component
public class WarehouseLogisticsQueryService {
    @Autowired
    private RegionTreeQueryService regionTreeQueryService;
    @Autowired
    private StCWarehouseLogisticsMapper mapper;
    @Autowired
    private StCWarehouseLogisticsItemMapper itemMapper;
    @Autowired
    private StCWarehouseLogisticsRankMapper rankMapper;
    @Autowired
    private RpcCpService rpcCpService;
    /**
     * 查询省市区树
     * @param obj
     * @return ValueHolderV14
     */
    public ValueHolderV14 queryWarehouseLogisticsTree(JSONObject obj) {
        ValueHolderV14<WarehouseLogisticsTreeResult> vh = new ValueHolderV14<>();
        vh.setCode(ResultCode.SUCCESS);
        if (null == obj || !obj.containsKey("objid")) {
            vh.setCode(ResultCode.FAIL);
            vh.setMessage("参数为空");
            return vh;
        }
        WarehouseLogisticsTreeResult result = new WarehouseLogisticsTreeResult();
        Long id = obj.getLong("objid");
        if (id > 0) {
            StCWarehouseLogisticsDO warehouseLogistics = mapper.selectById(id);
            result.setWarehouseLogistics(warehouseLogistics);

            //获取物流公司信息
            List<StCWarehouseLogisticsItemDO> itemList = itemMapper.selectList(new QueryWrapper<StCWarehouseLogisticsItemDO>()
                    .lambda().eq(StCWarehouseLogisticsItemDO::getStCWarehouseLogisticsId, id));
            if (CollectionUtils.isNotEmpty(itemList)) {
                itemList = itemList.stream().sorted(Comparator.comparing(StCWarehouseLogisticsItemDO::getId))
                        .collect(Collectors.toList());
                result.setWarehouseLogisticsItems(itemList);
            } else {
                result.setWarehouseLogisticsItems(Lists.newArrayList());
            }
        }
        //查询地址
        obj.put("regiontype", "PROV,CITY");
        ValueHolderV14<List<RegionTreeResult>> treeVh = regionTreeQueryService.queryRegionTree(obj);
        if (treeVh.isOK()) {
            Map<Long, StCWarehouseLogisticsRankDO> rankMap = new HashMap<>();
            if (id > 0) {
                List<StCWarehouseLogisticsRankDO> rankList = rankMapper.selectList(new QueryWrapper<StCWarehouseLogisticsRankDO>()
                        .lambda().eq(StCWarehouseLogisticsRankDO::getStCWarehouseLogisticsId, id));
                for (StCWarehouseLogisticsRankDO rank : rankList) {
                    rankMap.put(rank.getCpCRegionCityId(), rank);
                }
            }
            //构造树
            List<RegionTreeResult> resultList = treeVh.getData();
            if (rankMap.size() > 0) {
                changeTreeChecked(resultList, rankMap);
            }
            result.setWarehouseLogisticsTree(resultList);
        } else {
            vh.setCode(ResultCode.FAIL);
            vh.setMessage("省市区获取失败！");
            return vh;
        }
        vh.setData(result);
        return vh;
    }

    /**
     * 构建树
     * @param resultList
     * @param rankMap
     * @return Boolean
     */
    private Boolean changeTreeChecked(List<RegionTreeResult> resultList,
                                    Map<Long, StCWarehouseLogisticsRankDO> rankMap) {
        Boolean checkedFlg = true;
        for (RegionTreeResult result : resultList) {
            if (rankMap.containsKey(result.getId())) {
                result.setChecked(true);
                if (CollectionUtils.isNotEmpty(result.getChildren())) {
                    changeTreeChecked(result.getChildren(), rankMap);
                }
            } else {
                if (CollectionUtils.isNotEmpty(result.getChildren())) {
                    result.setChecked(changeTreeChecked(result.getChildren(), rankMap));
                }
            }
            if (!result.getChecked()) {
                checkedFlg = false;
            }
        }
        return checkedFlg;
    }

    /**
     * 查询仓库物流优先级明细表
     * @param obj
     * @return ValueHolderV14
     */
    public ValueHolderV14 queryRankResultTable(JSONObject obj) {
        ValueHolderV14<List<WarehouseLogisticsRankResult>> vh = new ValueHolderV14<>();
        vh.setCode(ResultCode.SUCCESS);
        if (null == obj || !obj.containsKey("objid")) {
            vh.setCode(ResultCode.FAIL);
            vh.setMessage("参数为空");
            return vh;
        }
        if (!obj.containsKey("treeNode")) {
            return vh;
        }

        //查询地址
        obj.put("regiontype", "PROV,CITY");
        ValueHolderV14<List<RegionTreeResult>> treeVh = regionTreeQueryService.queryRegionTree(obj);
        if (treeVh.isOK()) {
            //查询明细
            Long id = obj.getLong("objid");
            String treeNode = JSONArray.toJSONString(obj.getJSONArray("treeNode"));
            List<RegionTreeResult> treeList = treeVh.getData();
            List<RegionTreeResult> treeNodeList = JsonUtils.jsonToList(RegionTreeResult.class, treeNode);

            String citylevel = obj.getString("cityleave");
            if (citylevel != null && "PROV".equals(citylevel)) {
                List<Long> upIdList = Lists.newArrayList();
                for (RegionTreeResult regionTreeResult : treeNodeList) {
                    RegionTreeResult regionTree = RegionTreeUtils.getTreeResult(treeList, regionTreeResult.getId());
                    if ("CITY".equals(regionTree.getRegiontype()) && !upIdList.contains(regionTree.getUpId())) {
                        upIdList.add(regionTree.getUpId());
                    }
                }
                treeNodeList.clear();
                for (Long upId : upIdList) {
                    List<RegionTreeResult> childrenList = RegionTreeUtils.getTreeChildrenResult(treeVh.getData(), upId);
                    if (CollectionUtils.isNotEmpty(childrenList)) {
                        treeNodeList.addAll(childrenList);
                    }
                }
            }
            //构造仓库物流优先级明细
            List<StCWarehouseLogisticsRankDO> warehouseLogisticsRankList = buildRankDOList(id, treeList, treeNodeList);
            if (CollectionUtils.isNotEmpty(warehouseLogisticsRankList)) {
                //转换成画面使用rankResult
                List<WarehouseLogisticsRankResult> rankResultList = copyToRankResultList(id, warehouseLogisticsRankList, citylevel);
                vh.setData(rankResultList);
            }
        } else {
            vh.setCode(ResultCode.FAIL);
            vh.setMessage("仓库物流优先级明细获取失败！");
            return vh;
        }
        return vh;
    }

    /**
     * 构造仓库物流优先级明细
     * @param id
     * @param treeList
     * @param treeNodeList
     * @return List<StCWarehouseLogisticsRankDO>
     */
    private List<StCWarehouseLogisticsRankDO> buildRankDOList(Long id, List<RegionTreeResult> treeList,
                                                                List<RegionTreeResult> treeNodeList) {
        List<StCWarehouseLogisticsRankDO> warehouseLogisticsRankList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(treeNodeList)) {
            treeNodeList = treeNodeList.stream().sorted(Comparator.comparing(RegionTreeResult::getId))
                    .collect(Collectors.toList());
            Map<Long, StCWarehouseLogisticsRankDO> rankMap = getRankMap(id, treeNodeList);

            for (RegionTreeResult node : treeNodeList) {
                if (rankMap.containsKey(node.getId())) {
                    StCWarehouseLogisticsRankDO rank = rankMap.get(node.getId());
                    warehouseLogisticsRankList.add(rank);
                } else {
                    RegionTreeResult treeResult = RegionTreeUtils.getTreeResult(treeList, node.getId());
                    if (treeResult != null) {
                        //设置值
                        StCWarehouseLogisticsRankDO rank = new StCWarehouseLogisticsRankDO();
                        rank.setId(-1L);
                        //市
                        rank.setCpCRegionCityId(treeResult.getId());
                        rank.setCpCRegionCityEcode(treeResult.getEcode());
                        rank.setCpCRegionCityEname(treeResult.getTitle());
                        treeResult = RegionTreeUtils.getTreeResult(treeList, treeResult.getUpId());
                        if (treeResult != null) {
                            //省
                            rank.setCpCRegionProvinceId(treeResult.getId());
                            rank.setCpCRegionProvinceEcode(treeResult.getEcode());
                            rank.setCpCRegionProvinceEname(treeResult.getTitle());
                        }
                        warehouseLogisticsRankList.add(rank);
                    }
                }
            }
        }
        return warehouseLogisticsRankList;
    }

    /**
     * 获取明细MAP
     * @param id
     * @param treeNodeList
     * @return Map<Long, StCWarehouseLogisticsRankDO>
     */
    private Map<Long, StCWarehouseLogisticsRankDO> getRankMap(Long id, List<RegionTreeResult> treeNodeList) {
        Map<Long, StCWarehouseLogisticsRankDO> rankMap = new HashMap<>();
        if (id > 0) {
            Map<String, List<Long>> treeNodeMap = new HashMap<>();
            for (RegionTreeResult regionTree : treeNodeList) {
                List<Long> idList = new ArrayList<>();
                if (treeNodeMap.containsKey(regionTree.getRegiontype())) {
                    idList = treeNodeMap.get(regionTree.getRegiontype());
                }
                idList.add(regionTree.getId());
                treeNodeMap.put(regionTree.getRegiontype(), idList);
            }
            List<StCWarehouseLogisticsRankDO> rankAllList = rankMapper.selectList(new QueryWrapper<StCWarehouseLogisticsRankDO>()
                    .lambda().eq(StCWarehouseLogisticsRankDO::getStCWarehouseLogisticsId, id));
            for (String key : treeNodeMap.keySet()) {
                List<StCWarehouseLogisticsRankDO> rankList = rankAllList.stream().filter(rank ->
                        treeNodeMap.get(key).contains(rank.getCpCRegionCityId())).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(rankList)) {
                    for (StCWarehouseLogisticsRankDO rank : rankList) {
                        rankMap.put(rank.getCpCRegionCityId(), rank);
                    }
                }
            }
        }
        return rankMap;
    }

    /**
     * 查询物流公司信息
     * @param obj
     * @return ValueHolderV14
     */
    public ValueHolderV14 queryLogisticsInfo(JSONObject obj) {
        ValueHolderV14<WarehouseLogisticsInfoResult> vh = new ValueHolderV14<>();
        vh.setCode(ResultCode.SUCCESS);
        if (null == obj || !obj.containsKey("objid")) {
            vh.setCode(ResultCode.FAIL);
            vh.setMessage("参数为空");
            return vh;
        }
        WarehouseLogisticsInfoResult result = new WarehouseLogisticsInfoResult();
        Long id = obj.getLong("objid");
        if (id > 0) {
            //获取物流公司信息
            List<StCWarehouseLogisticsItemDO> itemList = itemMapper.selectList(new QueryWrapper<StCWarehouseLogisticsItemDO>()
                    .lambda().eq(StCWarehouseLogisticsItemDO::getStCWarehouseLogisticsId, id));
            itemList = itemList.stream().sorted(Comparator.comparing(StCWarehouseLogisticsItemDO::getId))
                    .collect(Collectors.toList());
            result.setWarehouseLogisticsItems(itemList);
        }
        //查询地址
        List<CpLogistics> cpLogisticsList = rpcCpService.queryLogisticsByLike(obj.getString("logisticsInfo"));
        if (CollectionUtils.isNotEmpty(cpLogisticsList)) {
            result.setCpLogisticsList(cpLogisticsList);
        }
        if (result.getWarehouseLogisticsItems() == null) {
            result.setWarehouseLogisticsItems(Lists.newArrayList());
        }
        vh.setData(result);
        return vh;
    }


    /**
     * 根据省市查询仓库物流优先级
     * @param rankRequest
     * @return ValueHolderV14
     */
    public ValueHolderV14<StCWarehouseLogisticsRankDO> queryLogisticsRankInfo(WarehouseLogisticsRankRequest rankRequest) {
        ValueHolderV14<StCWarehouseLogisticsRankDO> vh = new ValueHolderV14<>();
        vh.setCode(ResultCode.SUCCESS);
        //查询地址
        List<StCWarehouseLogisticsRankDO> rankList = rankMapper.queryLogisticsRankInfo(rankRequest.getCpCPhyWarehouseId()
                                                                            , rankRequest.getCpCRegionProvinceId()
                                                                            , rankRequest.getCpCRegionCityId());
        if (CollectionUtils.isNotEmpty(rankList)) {
            vh.setData(rankList.get(0));
        } else {
            vh.setData(null);
        }
        return vh;
    }

    /**
     * 转换成画面使用rankResult
     * @param id
     * @param id
     * @param warehouseLogisticsRankList
     * @param citylevel
     * @return List<WarehouseLogisticsRankResult>
     */
    private List<WarehouseLogisticsRankResult> copyToRankResultList(Long id, List<StCWarehouseLogisticsRankDO> warehouseLogisticsRankList,
                                                                    String citylevel) {
        List<WarehouseLogisticsRankResult> rankResultList = Lists.newArrayList();
        //获取物流公司信息
        List<StCWarehouseLogisticsItemDO> itemList = itemMapper.selectList(new QueryWrapper<StCWarehouseLogisticsItemDO>()
                .lambda().eq(StCWarehouseLogisticsItemDO::getStCWarehouseLogisticsId, id));
        itemList = itemList.stream().sorted(Comparator.comparing(StCWarehouseLogisticsItemDO::getId))
                .collect(Collectors.toList());

        Map<Long, WarehouseLogisticsRankResult> provRankMap = new HashMap<>();
        Map<String, Boolean> provDiffRankMap = new HashMap<>();
        for (StCWarehouseLogisticsRankDO rank : warehouseLogisticsRankList) {
            WarehouseLogisticsRankResult rankResult = new WarehouseLogisticsRankResult();
            BeanUtils.copyProperties(rank, rankResult);

            //获取仓库物流优先级集合
            Map<Long, LogisticsRankResult> logisticsRankMap = new HashMap<>();
            if (!StringUtils.isBlank(rank.getLogisticsRank())) {
                List<LogisticsRankResult> logisticsRankList = JsonUtils.jsonToList(LogisticsRankResult.class, rank.getLogisticsRank());
                if (CollectionUtils.isNotEmpty(logisticsRankList)) {
                    logisticsRankMap = logisticsRankList.stream().collect(
                            Collectors.toMap(LogisticsRankResult::getLogisticsId, Function.identity()));
                }
            }

            WarehouseLogisticsRankResult provRankResult = null;
            if (citylevel != null && "PROV".equals(citylevel) &&
                    provRankMap.containsKey(rankResult.getCpCRegionProvinceId())) {
                provRankResult = provRankMap.get(rankResult.getCpCRegionProvinceId());
            }
            int i = 1;
            List<LogisticsRankResult> logisticsRankNewList = Lists.newArrayList();
            for (StCWarehouseLogisticsItemDO item : itemList) {
                LogisticsRankResult logisticsRankNew = new LogisticsRankResult();
                logisticsRankNew.setLogisticsId(item.getCpCLogisticsId());
                logisticsRankNew.setLogisticsEcode(item.getCpCLogisticsEcode());
                logisticsRankNew.setLogisticsEname(item.getCpCLogisticsEname());
                try {
                    Method method = WarehouseLogisticsRankResult.class.getMethod("setRank" + i, new Class[]{String.class});
                    Method idMethod = WarehouseLogisticsRankResult.class.getMethod("setCpCLogisticsId" + i, new Class[]{Long.class});
                    Method ecodeMethod = WarehouseLogisticsRankResult.class.getMethod("setCpCLogisticsEcode" + i, new Class[]{String.class});
                    Method enameMethod = WarehouseLogisticsRankResult.class.getMethod("setCpCLogisticsEname" + i, new Class[]{String.class});

                    idMethod.invoke(rankResult, item.getCpCLogisticsId());
                    ecodeMethod.invoke(rankResult, item.getCpCLogisticsEcode());
                    enameMethod.invoke(rankResult, item.getCpCLogisticsEname());
                    if (logisticsRankMap.containsKey(item.getCpCLogisticsId())) {
                        LogisticsRankResult logisticsRank = logisticsRankMap.get(item.getCpCLogisticsId());
                        method.invoke(rankResult, logisticsRank.getRank());
                        logisticsRankNew.setRank(logisticsRank.getRank());
                    } else {
                        method.invoke(rankResult, "");
                        logisticsRankNew.setRank("");
                    }
                    if (provRankResult != null) {
                        Method provMethod = WarehouseLogisticsRankResult.class.getMethod("getRank" + i);
                        String provRank = (String) provMethod.invoke(provRankResult);
                        if (!logisticsRankNew.getRank().equals(provRank)) {
                            method.invoke(rankResult, "");
                            logisticsRankNew.setRank("");
                            provDiffRankMap.put(rank.getCpCRegionProvinceId() + "," + item.getCpCLogisticsId(), true);
                        }
                    }
                } catch (Exception ex) {
                    log.debug(LogUtil.format("仓库物流优先级设置失败：{}"), Throwables.getStackTraceAsString(ex));
                }
                logisticsRankNew.setLogisticsIdField("CP_C_LOGISTICS_ID" + i);
                logisticsRankNew.setLogisticsEcodeField("CP_C_LOGISTICS_ECODE" + i);
                logisticsRankNew.setLogisticsEnameField("CP_C_LOGISTICS_ENAME" + i);
                logisticsRankNew.setRankField("RANK" + i);

                if (provDiffRankMap.containsKey(rank.getCpCRegionProvinceId() + "," + item.getCpCLogisticsId()) &&
                        provDiffRankMap.get(rank.getCpCRegionProvinceId() + "," + item.getCpCLogisticsId())) {
                    logisticsRankNew.setProvDiffRank("市优先级不一致");
                }
                logisticsRankNewList.add(logisticsRankNew);
                i++;
            }
            rankResult.setLogisticsRank(JSONArray.toJSONString(logisticsRankNewList));
            rankResultList.add(rankResult);

            if (citylevel != null && "PROV".equals(citylevel)) {
                rankResult.setCpCRegionCityId(null);
                rankResult.setCpCRegionCityEcode(null);
                rankResult.setCpCRegionCityEname(null);
                rankResult.setCitylevel(citylevel);
                provRankMap.put(rankResult.getCpCRegionProvinceId(), rankResult);
            }
        }

        if (citylevel != null && "PROV".equals(citylevel)) {
            rankResultList.clear();
            rankResultList = provRankMap.values().stream().collect(Collectors.toList());
        }
        return rankResultList;
    }

    /**
     * 模糊地区查询仓库优先级明细表
     * @param obj
     * @return ValueHolderV14
     */
    public ValueHolderV14 queryLikeRankResultTable(JSONObject obj) {
        ValueHolderV14<WarehouseLogisticsTreeLikeResult> vh = new ValueHolderV14<>();
        vh.setCode(ResultCode.SUCCESS);
        if (null == obj || !obj.containsKey("objid")) {
            vh.setCode(ResultCode.FAIL);
            vh.setMessage("参数为空");
            return vh;
        }
        if (!obj.containsKey("treeLikeKey")) {
            return vh;
        }
        //查询地址
        obj.put("regiontype", "PROV,CITY");
        ValueHolderV14<List<RegionTreeResult>> treeVh = regionTreeQueryService.queryRegionTree(obj);
        WarehouseLogisticsTreeLikeResult likeResult = new WarehouseLogisticsTreeLikeResult();
        likeResult.setWarehouseLogisticsRanks(Lists.newArrayList());
        if (treeVh.isOK()) {
            //查询明细
            Long id = obj.getLong("objid");
            List<RegionTreeResult> treeList = treeVh.getData();

            List<RegionTreeResult> treeNodeList = Lists.newArrayList();
            RegionTreeUtils.changeTreeLikeChecked(treeList, obj.getString("treeLikeKey"), false, treeNodeList);
            likeResult.setWarehouseLogisticsTree(treeList);

            List<RegionTreeResult> treeNodeCityList = Lists.newArrayList();
            for (RegionTreeResult regionTreeResult : treeNodeList) {
                if (!("PROV").equals(regionTreeResult.getRegiontype())) {
                    treeNodeCityList.add(regionTreeResult);
                }
            }
            //构造仓库物流优先级明细
            List<StCWarehouseLogisticsRankDO> warehouseLogisticsRankList = buildRankDOList(id, treeList, treeNodeList);
            if (CollectionUtils.isNotEmpty(warehouseLogisticsRankList)) {
                //转换成画面使用rankResult
                List<WarehouseLogisticsRankResult> rankResultList = copyToRankResultList(id, warehouseLogisticsRankList,
                        "CITY");
                likeResult.setWarehouseLogisticsRanks(rankResultList);
            }
        } else {
            vh.setCode(ResultCode.FAIL);
            vh.setMessage("仓库物流优先级明细获取失败！");
            return vh;
        }
        vh.setData(likeResult);
        return vh;
    }
}
