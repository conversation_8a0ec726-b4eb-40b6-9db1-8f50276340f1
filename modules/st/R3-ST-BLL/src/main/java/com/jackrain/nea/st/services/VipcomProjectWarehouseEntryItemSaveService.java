package com.jackrain.nea.st.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.jackrain.nea.cpext.model.table.CpCPhyWarehouse;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.st.common.StConstant;
import com.jackrain.nea.st.mapper.StCVipcomProjectWhEntryItemMapper;
import com.jackrain.nea.st.model.table.StCVipcomProjectItemDO;
import com.jackrain.nea.st.model.table.StCVipcomProjectWhEntryItem;
import com.jackrain.nea.st.rpc.RpcCpService;
import com.jackrain.nea.st.utils.JsonUtils;
import com.jackrain.nea.st.utils.StBeanUtils;
import com.jackrain.nea.st.utils.ValueHolderUtils;
import com.jackrain.nea.st.validate.StParamConstants;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import java.text.DateFormat;
import java.text.ParseException;
import java.text.ParsePosition;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.LinkedList;
import java.util.List;
import java.util.Locale;

/**
 * 档期日程规划入库单明细保存
 * <AUTHOR>
 * @Date 2021/05/25/17:10
 */
@Component
@Slf4j
@Transactional
public class VipcomProjectWarehouseEntryItemSaveService extends CommandAdapter {

    @Autowired
    private StCVipcomProjectWhEntryItemMapper stCVipcomProjectWhEntryItemMapper;
    @Autowired
    private RpcCpService cpService;

    @Override
    public ValueHolder execute(QuerySession session) {
        DefaultWebEvent event = session.getEvent();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(
                event.getParameterValue("param"), "yyyy-MM-dd HH:mm:ss", SerializerFeature.WriteMapNullValue),
                Feature.OrderedField);
        log.debug(LogUtil.format("Start VipcomProjectWarehouseEntryItemSaveService.execute. ReceiveParams: {}"),
                param.toJSONString());
        Long masterId = param.getLong("ST_C_VIPCOM_PROJECT_ID");
        JSONObject jsonObject = param.getJSONObject("WAREHOUSE_ENTRY_ITEM");
        if(jsonObject == null){
            throw new NDSException("入库单明细接口传入数据为空！");
        }
        Long id = jsonObject.getLong("ID");
        StCVipcomProjectWhEntryItem stCVipcomProjectWhEntryItem = JsonUtils.jsonParseClass(jsonObject, StCVipcomProjectWhEntryItem.class);
        if (stCVipcomProjectWhEntryItem == null) {
            throw new NDSException("数据异常！");
        }

        if(!ObjectUtils.isEmpty(stCVipcomProjectWhEntryItem.getIsAirEmbargo()) &&
                stCVipcomProjectWhEntryItem.getIsAirEmbargo().equals(1)){
            stCVipcomProjectWhEntryItem.setDeliveryWay("1");
        }

        log.debug("Start VipcomProjectWarehouseEntryItemSaveService.execute.line.66 : {}", id);
        if (id != null) {
            if (id != -1L) {
                return updateWarehouseEntryItem(session, stCVipcomProjectWhEntryItem);
            } else {
                return insertWarehouseEntryItem(masterId,stCVipcomProjectWhEntryItem,session);
            }
        }else{
            throw new NDSException("入库单明细接口传入数据id为空！");
        }
    }


    /**
     * 档期日程规划入库单明细更新
     * @param session
     * @param item
     * @return com.jackrain.nea.util.ValueHolder
     * @Author: hly
     * @Date 2021/05/26
     */
    private ValueHolder updateWarehouseEntryItem(QuerySession session,StCVipcomProjectWhEntryItem item) {
        log.debug(LogUtil.format("Start VipcomProjectWarehouseEntryItemSaveService.updateWarehouseEntryItem.line.88 " +
                "item: {}"), item);
        //checkVipcomProjectItemByFilter(item, "update");
        StBeanUtils.makeModifierField(item, session.getUser());
        int update = stCVipcomProjectWhEntryItemMapper.updateById(item);
        if (update < 0) {
            throw new NDSException("更新失败！");
        }
        return ValueHolderUtils.getSuccessValueHolder("更新成功！");
    }

    /**
     * 档期日程规划入库单明细新增
     * @param masterId
     * @param item
     * @param session
     * @return com.jackrain.nea.util.ValueHolder
     * @Author: hly
     * @Date 2021/05/26
     */
    private ValueHolder insertWarehouseEntryItem(long masterId, StCVipcomProjectWhEntryItem item, QuerySession session) {
        log.debug(LogUtil.format("Start VipcomProjectWarehouseEntryItemSaveService.insertWarehouseEntryItem.line.114 " +
                "item: {}"), item);
        Long id = ModelUtil.getSequence(StConstant.TAB_ST_C_VIPCOM_PROJECT_WH_ENTRY_ITEM);
        item.setId(id);
        //主键
        item.setStCVipcomProjectId(masterId);
        StBeanUtils.makeCreateField(item, session.getUser());
        checkVipcomProjectItemByFilter(item);
        int insert = stCVipcomProjectWhEntryItemMapper.insert(item);
        if (insert < 0) {
            throw new NDSException("保存失败！");
        }
        return ValueHolderUtils.getSuccessValueHolder("新增成功！");
    }

    private void checkVipcomProjectItemByFilter(StCVipcomProjectWhEntryItem item) {
        //发货间隔 1：当日 2：次日
        String sendInterval = item.getSendInterval();
        //到货间隔
        String arrivalInterval = item.getArrivalInterval();

        if (StringUtils.isEmpty(sendInterval)|| StringUtils.isEmpty(arrivalInterval)){
            throw new NDSException("发货间隔和到货间隔都不能为空！");
        }
        DateFormat fmt =new SimpleDateFormat("HH:mm:ss");
        try {
            Date arrivaltime =   new SimpleDateFormat("HH:mm:ss").parse(item.getArrivaltime(),new ParsePosition( 0 ));
            Date sendTime = new SimpleDateFormat("HH:mm:ss").parse(item.getSendtime(),new ParsePosition( 0 ));
            if(sendInterval.equals(arrivalInterval)){
                //时间判断
                if (arrivaltime != null && sendTime != null) {
                    if (arrivaltime.before(sendTime)) {
                        throw new NDSException("到货时间需大于发货时间，请重新录入！");
                    }
                }
            }
            if (Integer.parseInt(sendInterval) > Integer.parseInt(arrivalInterval)){
                throw new NDSException("到货时间需大于发货时间，请重新录入！");
            }
        } catch (Exception e) {
            throw new NDSException(e.getMessage());
        }

    }

}
