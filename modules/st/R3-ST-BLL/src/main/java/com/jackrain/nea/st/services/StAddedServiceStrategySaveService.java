package com.jackrain.nea.st.services;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.st.annotation.StOperationLog;
import com.jackrain.nea.st.common.StConstant;
import com.jackrain.nea.st.mapper.StAddedServiceStrategyDetailMapper;
import com.jackrain.nea.st.mapper.StAddedServiceStrategyMapper;
import com.jackrain.nea.st.model.request.AddedServiceStrategyRequest;
import com.jackrain.nea.st.model.table.StAddedServiceStrategyDO;
import com.jackrain.nea.st.model.table.StAddedServiceStrategyDetailDO;
import com.jackrain.nea.st.utils.JsonUtils;
import com.jackrain.nea.st.utils.StBeanUtils;
import com.jackrain.nea.st.utils.ValueHolderUtils;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @program: ryytn-st-v3.0
 * @description: 增值服务策略保存
 * @author: haiyang
 * @create: 2023-10-20 16:41
 **/
@Component
@Slf4j
@Transactional
public class StAddedServiceStrategySaveService extends CommandAdapter {


    @Autowired
    private StAddedServiceStrategyMapper addedServiceStrategyMapper;

    @Autowired
    private StAddedServiceStrategyDetailMapper addedServiceStrategyDetailMapper;

    @Override
    @StOperationLog(mainTableName = "ST_ADDED_SERVICE_STRATEGY", itemsTableName = "ST_ADDED_SERVICE_STRATEGY_DETAIL")
    public ValueHolder execute(QuerySession session) throws NDSException {
        DefaultWebEvent event = session.getEvent();
        List<String> nullKeyList = new ArrayList<>();
        JSONObject param = JsonUtils.filterNull((JSONObject) event.getParameterValue("param"), nullKeyList);

        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("StAddedServiceStrategySaveService.param=") + param.toJSONString());
        }
        Long id = param.getLong("objid");
        JSONObject fixColumn = param.getJSONObject("fixcolumn");
        if (fixColumn != null) {
            AddedServiceStrategyRequest strategyCmdRequest = JsonUtils.jsonParseClass(fixColumn, AddedServiceStrategyRequest.class);
            if (id != null && id > 0) {
                return updateAddedServiceStrategy(strategyCmdRequest, session, id, nullKeyList);
            } else {
                return saveAddedServiceStrategy(strategyCmdRequest, session, id);
            }
        }
        throw new NDSException("当前记录已不存在！");

    }

    private ValueHolder updateAddedServiceStrategy(AddedServiceStrategyRequest strategyCmdRequest, QuerySession session, Long id, List<String> nullKeyList) {
        ValueHolder holder = new ValueHolder();
        StAddedServiceStrategyDO stAddedServiceStrategyDO = strategyCmdRequest.getStAddedServiceStrategyDO();
        if (null != stAddedServiceStrategyDO) {
            String addedStrategyName = stAddedServiceStrategyDO.getAddedStrategyName();
            StAddedServiceStrategyDO strategyDO = addedServiceStrategyMapper.selectById(id);
            if (null == strategyDO) {
                return ValueHolderUtils.getFailValueHolder("修改的头信息已被删除！");
            }
            Long cpCPhyWarehouseId = strategyDO.getCpCPhyWarehouseId();
            int i = addedServiceStrategyMapper.selectByCpCPhyWarehouseIdAndStrategyName(cpCPhyWarehouseId, addedStrategyName);
            if (i > 0) {
                return ValueHolderUtils.getFailValueHolder("当前实体仓下存在同名策略名称，请检查");
            }
            stAddedServiceStrategyDO.setId(id);
            StBeanUtils.makeModifierField(stAddedServiceStrategyDO, session.getUser());
            int i1 = addedServiceStrategyMapper.updateById(stAddedServiceStrategyDO);
            if (i1 < 0) {
                holder = ValueHolderUtils.getFailValueHolder("保存失败！");
                return holder;
            }
        }
        List<StAddedServiceStrategyDetailDO> strategyDetailDOList = strategyCmdRequest.getStAddedServiceStrategyDetailDOList();
        if (CollectionUtils.isNotEmpty(strategyDetailDOList)) {
            checkStrategyTypeExist(id, strategyDetailDOList);
            List<StAddedServiceStrategyDetailDO> insertDetails = strategyDetailDOList.stream().filter(e -> e.getId() < 0).collect(Collectors.toList());
            List<StAddedServiceStrategyDetailDO> updateDetails = strategyDetailDOList.stream().filter(e -> e.getId() > 0).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(insertDetails)) {
                for (StAddedServiceStrategyDetailDO item : insertDetails) {
                    item.setId(ModelUtil.getSequence(StConstant.ST_ADDED_SERVICE_STRATEGY_DETAIL));
                    item.setAddedStrategyId(id);
                    StBeanUtils.makeCreateField(item, session.getUser());

                    if (addedServiceStrategyDetailMapper.insert(item) < 0) {
                        holder = ValueHolderUtils.getFailValueHolder("保存失败！");
                        return holder;
                    }
                }
            }
            if (CollectionUtils.isNotEmpty(updateDetails)) {
                for (StAddedServiceStrategyDetailDO item: updateDetails) {
                    if (addedServiceStrategyDetailMapper.selectById(item.getId()) != null) {
                        StBeanUtils.makeModifierField(item, session.getUser());
                        if (addedServiceStrategyDetailMapper.updateById(item) < 0) {
                            holder = ValueHolderUtils.getFailValueHolder("保存失败！");
                            return holder;
                        }
                    } else {
                        holder = ValueHolderUtils.getFailValueHolder("修改的行明细已被删除！");
                        return holder;
                    }
                }
            }
        }
        return ValueHolderUtils.getSuccessValueHolder(id, StConstant.ST_ADDED_SERVICE_STRATEGY);
    }


    private ValueHolder saveAddedServiceStrategy(AddedServiceStrategyRequest strategyCmdRequest, QuerySession session, Long id) {
        StAddedServiceStrategyDO stAddedServiceStrategyDO = strategyCmdRequest.getStAddedServiceStrategyDO();
        List<StAddedServiceStrategyDetailDO> strategyDetailDOList = strategyCmdRequest.getStAddedServiceStrategyDetailDOList();
        stAddedServiceStrategyDO.setId(ModelUtil.getSequence(StConstant.ST_ADDED_SERVICE_STRATEGY));
        StBeanUtils.makeCreateField(stAddedServiceStrategyDO, session.getUser());
        checkStrategyExist(stAddedServiceStrategyDO);

        int insertCount = addedServiceStrategyMapper.insert(stAddedServiceStrategyDO);
        if (insertCount < 0) {
            return ValueHolderUtils.getFailValueHolder("增值服务策略主表保存失败");
        }

        // 子表新增
        if (CollectionUtils.isNotEmpty(strategyDetailDOList)) {
            strategyDetailDOList.forEach(x -> {
                x.setId(ModelUtil.getSequence(StConstant.ST_ADDED_SERVICE_STRATEGY_DETAIL));
                StBeanUtils.makeCreateField(x, session.getUser());
            });
            addedServiceStrategyDetailMapper.batchInsert(strategyDetailDOList);
        }
        return ValueHolderUtils.getSuccessValueHolder(stAddedServiceStrategyDO.getId(), StConstant.ST_ADDED_SERVICE_STRATEGY);
    }

    private void checkStrategyExist(StAddedServiceStrategyDO stAddedServiceStrategyDO) {
        String addedStrategyCode = stAddedServiceStrategyDO.getAddedStrategyCode();
        Long cpCPhyWarehouseId = stAddedServiceStrategyDO.getCpCPhyWarehouseId();
        int i = addedServiceStrategyMapper.selectByCpCPhyWarehouseId(cpCPhyWarehouseId);
        if (i > 0) {
            throw new NDSException("该逻辑仓已存在对应策略");
        }
        int i1 = addedServiceStrategyMapper.selectByStrategyCode(addedStrategyCode);
        if (i1 > 0) {
            throw new NDSException("该编码已存在，请重新输入");
        }
    }

    private void checkStrategyTypeExist(Long addedStrategyId, List<StAddedServiceStrategyDetailDO> strategyDetailDOList) {
        List<Long> typeList = strategyDetailDOList.stream().map(StAddedServiceStrategyDetailDO::getAddedTypeDocId).filter(Objects::nonNull).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(typeList)) {
            Map<Long, List<Long>> typeGroupMap = typeList.stream().collect(Collectors.groupingBy(e -> e));
            for (Map.Entry<Long, List<Long>> entry : typeGroupMap.entrySet()) {
                List<Long> value = entry.getValue();
                if (value.size() > 1) {
                    throw new NDSException("存在相同策略类型，请检查");
                }
            }
            String types = typeList.stream().map(Object::toString).collect(Collectors.joining(","));
            List<StAddedServiceStrategyDetailDO> existRecords = addedServiceStrategyDetailMapper.selectByStrategyIdAndTypeDocIds(addedStrategyId, types);
            if (CollectionUtils.isNotEmpty(existRecords)) {
                throw new NDSException("已配置相同类型策略，请勿重复添加");
            }
        }
    }
}
