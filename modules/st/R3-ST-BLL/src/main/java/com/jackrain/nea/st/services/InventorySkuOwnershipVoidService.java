package com.jackrain.nea.st.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.st.common.StConstant;
import com.jackrain.nea.st.mapper.StCInventorySkuOwnershipMapper;
import com.jackrain.nea.st.model.table.StCInventorySkuOwnershipDO;
import com.jackrain.nea.st.utils.StBeanUtils;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.Date;
import java.util.HashMap;

/**
 * <AUTHOR>
 * @Date 2019/9/23 10:02
 */
@Component
@Slf4j
public class InventorySkuOwnershipVoidService extends CommandAdapter {
    @Autowired
    private StCInventorySkuOwnershipMapper mapper;

    @Override
    public ValueHolder execute(QuerySession session) throws NDSException {
        DefaultWebEvent event = session.getEvent();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(
                event.getParameterValue("param"), "yyyy-MM-dd HH:mm:ss", SerializerFeature.WriteMapNullValue),
                Feature.OrderedField);
        HashMap<Long, Object> errMap = new HashMap<Long, Object>();
        //生成Json数组
        JSONArray voidArray = StBeanUtils.makeVoidJsonArray(param);
        //列表批量
        if (!CollectionUtils.isEmpty(voidArray)) {
            for (int i = 0; i < voidArray.size(); i++) {
                // 遍历 jsonarray 数组，把每一个对象转成 json 对象
                Long id = Long.valueOf(voidArray.get(i).toString());
                try {
                    voidProcess(id, session);
                } catch (Exception ex) {
                    errMap.put(id, ex.getMessage());
                }
            }
        }
        //返回日志
        return StBeanUtils.getExcuteValueHolder(voidArray.size(), errMap);
    }

    /**
     * 方案作废
     *
     * @param id
     * @param session
     * @return com.jackrain.nea.util.ValueHolder
     */
    private void voidProcess(long id, QuerySession session) {
        //验证
        checkProcess(id);
        //作废
        StCInventorySkuOwnershipDO inventorySkuOwnership = new StCInventorySkuOwnershipDO();
        inventorySkuOwnership.setId(id);
        inventorySkuOwnership.setIsactive(StConstant.ISACTIVE_N);//作废状态
        inventorySkuOwnership.setDelname(session.getUser().getName());//作废人用户名
        inventorySkuOwnership.setDelerId(Long.valueOf(session.getUser().getId()));//作废人
        inventorySkuOwnership.setDelename(session.getUser().getEname());//作废人姓名
        inventorySkuOwnership.setDelTime(new Date());//作废时间
        inventorySkuOwnership.setEstatus(StConstant.CON_BILL_STATUS_03);

        StBeanUtils.makeModifierField(inventorySkuOwnership, session.getUser());
        int update = mapper.updateById(inventorySkuOwnership);
        if (update < 0) {
            throw new NDSException("作废失败！");
        }
    }

    private void checkProcess(long id) {
        StCInventorySkuOwnershipDO inventorySkuOwnership = mapper.selectById(id);
        if (inventorySkuOwnership == null) {
            throw new NDSException("当前记录已不存在！");
        } else {
            if (inventorySkuOwnership.getIsactive().equals(StConstant.ISACTIVE_N)) {
                throw new NDSException("当前记录已作废，不允许重复作废！");
            }
            if (inventorySkuOwnership.getEstatus().equals(StConstant.CON_BILL_STATUS_02)) {
                throw new NDSException("当前记录已审核，不允许作废！");
            }
            if (inventorySkuOwnership.getEstatus().equals(StConstant.CON_BILL_STATUS_03)) {
                throw new NDSException("当前记录已作废，不允许作废！");
            }
            if (inventorySkuOwnership.getEstatus().equals(StConstant.CON_BILL_STATUS_04)) {
                throw new NDSException("当前记录已结案，不允许作废！");
            }
        }
    }

}
