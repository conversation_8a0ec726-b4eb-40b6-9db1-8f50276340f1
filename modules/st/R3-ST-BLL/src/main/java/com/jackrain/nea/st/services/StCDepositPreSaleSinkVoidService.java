package com.jackrain.nea.st.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.st.common.StConstant;
import com.jackrain.nea.st.mapper.StCDepositPreSaleSinkMapper;
import com.jackrain.nea.st.model.table.StCDepositPreSaleSinkDO;
import com.jackrain.nea.st.utils.StBeanUtils;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;

/**
 * @ClassName : StCDepositPreSaleSinkVoidService  
 * @Description : 预下沉策略作废
 * <AUTHOR>  YCH
 * @Date: 2021-09-23 19:22  
 */
@Component
@Slf4j
public class StCDepositPreSaleSinkVoidService extends CommandAdapter {
    @Autowired
    private StCDepositPreSaleSinkMapper mapper;

    @Override
    public ValueHolder execute(QuerySession querySession) throws NDSException {
        //1.获取传入参数
        DefaultWebEvent event = querySession.getEvent();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(
                event.getParameterValue("param"), "yyyy-MM-dd HH:mm:ss"),
                Feature.OrderedField);
        if (param == null) {
            throw new NDSException("参数为空！");
        }
        log.debug(LogUtil.format("Start StCDepositPreSaleSinkVoidService.execute. ReceiveParams: {}"),
                param.toJSONString());
        //2.定义错误记录数
        HashMap<Long, Object> errMap = new HashMap();
        //3.生成审核Json数组
        JSONArray voidArray = StBeanUtils.makeVoidJsonArray(param);
        for (int i = 0; i < voidArray.size(); i++) {
            Long id = voidArray.getLong(i);
            try {
                //4.遍历作废方法
                voidAction(id, querySession);
            } catch (Exception e) {
                errMap.put(id, e.getMessage());
            }
        }
        //5.返回ValueHolder对象
        return StBeanUtils.getExcuteValueHolder(voidArray.size(), errMap);
    }


    /**
     * @param id
     * @param querySession
     * @return void
     * @Descroption 检查并更新作废状态
     * @Author: 黄火县
     * @Date 2019/3/12
     */
    public void voidAction(Long id, QuerySession querySession) {
        StCDepositPreSaleSinkDO stCDepositPreSaleSinkDO = mapper.selectById(id);
        //主表校验
        checkAction(stCDepositPreSaleSinkDO);
        stCDepositPreSaleSinkDO.setEstatus(StConstant.HOLD_ORDER_STATUS_03);
        stCDepositPreSaleSinkDO.setIsactive(StConstant.ISACTIVE_N);
        //更新作废状态
        StBeanUtils.makeModifierField(stCDepositPreSaleSinkDO, querySession.getUser());
        int updateNum = mapper.updateById(stCDepositPreSaleSinkDO);
        if (updateNum < 0) {
            throw new NDSException("作废失败！");
        }
    }

    private void checkAction(StCDepositPreSaleSinkDO stCDepositPreSaleSinkDO) {
        if (stCDepositPreSaleSinkDO == null) {
            throw new NDSException("当前记录已不存在！");
        } else {
            if (StConstant.ISACTIVE_N.equals(stCDepositPreSaleSinkDO.getIsactive())) {
                throw new NDSException("当前记录已作废，不允许重复作废！");
            }
            if (!StConstant.CON_BILL_STATUS_01.equals(stCDepositPreSaleSinkDO.getEstatus())) {
                throw new NDSException("当前记录非未审核状态，不允许作废！");
            }
        }
    }
}
