package com.jackrain.nea.st.services;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.st.common.StConstant;
import com.jackrain.nea.st.mapper.StCFullcarCostItemMapper;
import com.jackrain.nea.st.mapper.StCFullcarCostMapper;
import com.jackrain.nea.st.model.enums.CloseStatusEnum;
import com.jackrain.nea.st.model.enums.SubmitStatusEnum;
import com.jackrain.nea.st.model.request.StCFullcarCostDetailQueryRequest;
import com.jackrain.nea.st.model.request.StCFullcarCostQueryRequest;
import com.jackrain.nea.st.model.request.StCFullcarCostRelationQueryRequest;
import com.jackrain.nea.st.model.result.StCFullcarCostDetailQueryResult;
import com.jackrain.nea.st.model.result.StCFullcarCostQueryResult;
import com.jackrain.nea.st.model.result.StCFullcarCostRelation;
import com.jackrain.nea.st.model.table.StCFullcarCost;
import com.jackrain.nea.st.model.table.StCFullcarCostItem;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.jackrain.nea.oc.oms.model.enums.YesNoEnum;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @ClassName StCFullcarCostQueryService
 * @Description 整车报价
 * <AUTHOR>
 * @Date 2024/4/8 15:18
 * @Version 1.0
 */
@Component
@Slf4j
public class StCFullcarCostQueryService {
    @Autowired
    private StCFullcarCostMapper stCFullcarCostMapper;
    @Autowired
    private StCFullcarCostItemMapper stCFullcarCostItemMapper;

    public ValueHolderV14<StCFullcarCostQueryResult> queryFullcarCost(StCFullcarCostQueryRequest request) {
        ValueHolderV14<StCFullcarCostQueryResult> result = new ValueHolderV14<>(ResultCode.SUCCESS, "SUCCESS");


        if (request == null || request.getRegionProvinceId() == null || request.getRegionCityId() == null || request.getTotalWeight() == null) {

            result.setCode(ResultCode.FAIL);
            result.setMessage("查询整车报价设置参数为空");
            return result;
        }

        Date date = new Date();
        List<StCFullcarCost> fullcarCostList = stCFullcarCostMapper.selectList(new QueryWrapper<StCFullcarCost>()
                .lambda()
                .eq(StCFullcarCost::getStatus, SubmitStatusEnum.SUBMIT.getKey())
                .eq(StCFullcarCost::getCloseStatus, CloseStatusEnum.NO_CLOSE.getKey())
                .eq(StCFullcarCost::getIsactive, StConstant.ISACTIVE_Y)
                .le(StCFullcarCost::getStartDate, date)
                .ge(StCFullcarCost::getEndDate, date));

        if (CollectionUtils.isEmpty(fullcarCostList)) {
            result.setData(getEmptyResult());
            return result;
        }

        List<Long> mainTableIdList = fullcarCostList.stream().map(StCFullcarCost::getId).collect(Collectors.toList());

        List<StCFullcarCostItem> unfullcarCostItemList = stCFullcarCostItemMapper.selectList(new QueryWrapper<StCFullcarCostItem>()
                .lambda()
                .in(StCFullcarCostItem::getFullcarCostId, mainTableIdList)
                .eq(StCFullcarCostItem::getProvinceId, request.getRegionProvinceId())
                .eq(StCFullcarCostItem::getCityId, request.getRegionCityId())
                .lt(StCFullcarCostItem::getStartWeight, request.getTotalWeight())
                .ge(StCFullcarCostItem::getEndWeight, request.getTotalWeight())
                .eq(StCFullcarCostItem::getIsactive, StConstant.ISACTIVE_Y));

        if (CollectionUtils.isEmpty(unfullcarCostItemList)) {
            result.setData(getEmptyResult());
            return result;
        }
        Set<Long> existMainTableIdSet = unfullcarCostItemList.stream().map(StCFullcarCostItem::getFullcarCostId).collect(Collectors.toSet());
        fullcarCostList = fullcarCostList.stream().filter(o -> existMainTableIdSet.contains(o.getId())).collect(Collectors.toList());

        StCFullcarCostQueryResult resultModel = new StCFullcarCostQueryResult();
        resultModel.setFullcarCostList(fullcarCostList);
        resultModel.setFullcarCostItemList(unfullcarCostItemList);

        result.setData(resultModel);
        return result;
    }

    private StCFullcarCostQueryResult getEmptyResult() {
        StCFullcarCostQueryResult fullcarCostQueryResult = new StCFullcarCostQueryResult();
        fullcarCostQueryResult.setFullcarCostList(new ArrayList<>());
        fullcarCostQueryResult.setFullcarCostItemList(new ArrayList<>());
        return fullcarCostQueryResult;
    }

    /**
     * 根据报价ID和重量查询整车报价明细
     *
     * @param request 查询请求
     * @return 查询结果
     */
    public ValueHolderV14<StCFullcarCostDetailQueryResult> queryFullcarCostDetail(StCFullcarCostDetailQueryRequest request) {
        if (log.isDebugEnabled()) {
            log.debug("StCFullcarCostQueryService.queryFullcarCostDetail, request: {}", JSONObject.toJSONString(request));
        }

        ValueHolderV14<StCFullcarCostDetailQueryResult> result = new ValueHolderV14<>(ResultCode.SUCCESS, "SUCCESS");

        // 参数校验
        if (request == null || request.getFullcarCostId() == null || request.getWeight() == null) {
            result.setCode(ResultCode.FAIL);
            result.setMessage("查询整车报价明细参数不完整");
            return result;
        }

        StCFullcarCostDetailQueryResult queryResult = new StCFullcarCostDetailQueryResult();

        try {
            // 查询整车报价设置是否存在
            StCFullcarCost fullcarCost = stCFullcarCostMapper.selectById(request.getFullcarCostId());
            if (fullcarCost == null) {
                log.debug("未找到整车报价设置，ID: {}", request.getFullcarCostId());
                result.setData(queryResult);
                return result;
            }

            // 构建查询条件
            QueryWrapper<StCFullcarCostItem> itemWrapper = new QueryWrapper<>();
            itemWrapper.lambda()
                    .eq(StCFullcarCostItem::getIsactive, StConstant.ISACTIVE_Y)
                    .eq(StCFullcarCostItem::getFullcarCostId, request.getFullcarCostId())
                    .le(StCFullcarCostItem::getStartWeight, request.getWeight())
                    .ge(StCFullcarCostItem::getEndWeight, request.getWeight());

            // 如果指定了省份，则添加条件
            if (request.getProvinceId() != null) {
                itemWrapper.lambda().eq(StCFullcarCostItem::getProvinceId, request.getProvinceId());
            }

            // 如果指定了城市，则添加条件
            if (request.getCityId() != null) {
                itemWrapper.lambda().eq(StCFullcarCostItem::getCityId, request.getCityId());
            }

            List<StCFullcarCostItem> fullcarCostItemList = stCFullcarCostItemMapper.selectList(itemWrapper);

            // 设置查询结果
            queryResult.setFullcarCostItemList(fullcarCostItemList);

            result.setData(queryResult);

            return result;
        } catch (Exception e) {
            log.error("查询整车报价明细异常", e);
            result.setCode(ResultCode.FAIL);
            result.setMessage("查询整车报价明细异常: " + e.getMessage());
            return result;
        }
    }

    /**
     * 根据实体仓和物流公司查询整车报价关系
     *
     * @param request 查询请求，包含实体仓ID和物流公司ID
     * @return 整车报价关系列表，包含主表信息和明细列表
     */
    public List<StCFullcarCostRelation> queryFullcarCostRelation(StCFullcarCostRelationQueryRequest request) {
        if (request == null || request.getCpCPhyWarehouseId() == null || request.getCpCLogisticsId() == null) {
            log.error("实体仓ID和物流公司ID不能为空!");
            return new ArrayList<>();
        }
        List<StCFullcarCostRelation> relations = new ArrayList<>();
        List<StCFullcarCost> fullcarCostList = stCFullcarCostMapper.selectList(
                new LambdaQueryWrapper<StCFullcarCost>()
                        .eq(StCFullcarCost::getCpCPhyWarehouseId, request.getCpCPhyWarehouseId())
                        .eq(StCFullcarCost::getCpCLogisticsId, request.getCpCLogisticsId())
                        .eq(StCFullcarCost::getStatus, SubmitStatusEnum.SUBMIT.getKey())
                        .eq(StCFullcarCost::getCloseStatus, CloseStatusEnum.NO_CLOSE.getKey())
                        .ge(StCFullcarCost::getEndDate, new Date())
                        .eq(StCFullcarCost::getIsactive, YesNoEnum.Y.getKey()));
        if (CollectionUtils.isEmpty(fullcarCostList)) {
            return new ArrayList<>();
        }
        List<Long> fullcarCostIds = fullcarCostList.stream().map(StCFullcarCost::getId).collect(Collectors.toList());
        List<StCFullcarCostItem> fullcarCostItemList = stCFullcarCostItemMapper.selectList(new LambdaQueryWrapper<StCFullcarCostItem>()
                .in(StCFullcarCostItem::getFullcarCostId, fullcarCostIds)
                .eq(StCFullcarCostItem::getIsactive, YesNoEnum.Y.getKey()));
        if (CollectionUtils.isEmpty(fullcarCostItemList)) {
            log.error("整车报价明细为空!");
            return new ArrayList<>();
        }
        Map<Long, List<StCFullcarCostItem>> itemGroupMap =
                fullcarCostItemList.stream().collect(Collectors.groupingBy(StCFullcarCostItem::getFullcarCostId));
        for (StCFullcarCost fullcarCost : fullcarCostList) {
            StCFullcarCostRelation relation = new StCFullcarCostRelation();
            relations.add(relation);
            relation.setStCFullcarCost(fullcarCost);
            relation.setStCFullcarCostItemList(itemGroupMap.get(fullcarCost.getId()));
        }
        return relations;
    }

    /**
     * 根据主表ID查询整车报价关系
     *
     * @param id 整车报价主表ID
     * @return 整车报价关系对象，包含主表信息和明细列表
     */
    public StCFullcarCostRelation queryFullcarCostRelationById(Long id) {
        if (id == null) {
            log.error("主表ID不能为空!");
            return null;
        }

        // 根据ID查询主表信息
        StCFullcarCost fullcarCost = stCFullcarCostMapper.selectById(id);
        if (fullcarCost == null) {
            log.error("未找到ID为{}的整车报价设置!", id);
            return null;
        }

        // 查询对应的明细信息
        List<StCFullcarCostItem> fullcarCostItemList = stCFullcarCostItemMapper.selectList(
                new LambdaQueryWrapper<StCFullcarCostItem>()
                        .eq(StCFullcarCostItem::getFullcarCostId, id)
                        .eq(StCFullcarCostItem::getIsactive, YesNoEnum.Y.getKey())
        );

        // 构建返回对象
        StCFullcarCostRelation relation = new StCFullcarCostRelation();
        relation.setStCFullcarCost(fullcarCost);
        relation.setStCFullcarCostItemList(fullcarCostItemList);

        return relation;
    }
}
