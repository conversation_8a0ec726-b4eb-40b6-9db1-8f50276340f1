package com.jackrain.nea.st.services;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.model.enums.YesNoEnum;
import com.jackrain.nea.oc.oms.model.resources.OmsRedisKeyResources;
import com.jackrain.nea.redis.util.RedisOpsUtil;
import com.jackrain.nea.st.mapper.*;
import com.jackrain.nea.st.model.result.EwayBillResult;
import com.jackrain.nea.st.model.result.StCAutoCheckResult;
import com.jackrain.nea.st.model.table.*;
import com.jackrain.nea.st.utils.RedisConstant;
import com.jackrain.nea.st.utils.RedisHashCommonUtils;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.utility.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @program: r3-st
 * @author: Lijp
 * @create: 2019-07-16 14:02
 */
@Component
@Slf4j
public class StrategyCenterService {
    @Autowired
    StCScalpingMapper stCScalpingMapper;

    @Autowired
    StCScalpingLogisticsMapper stCScalpingLogisticsMapper;

    @Autowired
    StCScalpingReplaceProMapper stCScalpingReplaceProMapper;

    @Autowired
    StCEwaybillMapper stCEwaybillMapper;

    @Autowired
    StCEwaybillShopMapper stCEwaybillShopMapper;

    @Autowired
    StCEwaybillLogisticsMapper stCEwaybillLogisticsMapper;

    @Autowired
    StCShopStrategyMapper stCShopStrategyMapper;

    @Autowired
    StCAutoCheckMapper stCAutocheckMapper;

    @Autowired
    StCInventoryOwnershipMapper stCInventoryOwnershipMapper;

    @Autowired
    private RedisHashCommonUtils redisHashCommonUtils;

    public static final int isForceSend = 1;//是否强制发货 1 强制发货

    @Autowired
    private RedisOpsUtil<String, String> redisUtil;

    @Autowired
    private StCAutoCheckAutoTimeMapper autoCheckAutoTimeMapper;

    /**
     * 根据店铺id查询该店铺下的所有刷单策略
     *
     * @param shopId
     * @return
     */
    public StCScalpingDO queryScalpingList(Long shopId, String keyWord, Date payTime) {
        log.debug(LogUtil.format("入参shopId：" + shopId + "备注：" + keyWord + "付款时间：" + payTime));
        if (null == shopId || StringUtils.isEmpty(keyWord) || null == payTime) {
            return null;
        }
        StCScalpingDO stCScalpingDO = stCScalpingMapper.queryScalpingByshopId(shopId, keyWord, payTime);//主表明细
        return stCScalpingDO;
    }

    /**
     * 根据店铺id 物流公司id 查询电子面单信息
     *
     * @param shopId
     * @param logiscId
     * @return
     */
    public ValueHolderV14<EwayBillResult> queryEwayBillByshopId(Long shopId, Long logiscId) {
        log.debug(LogUtil.format("查询电子面单信息接口入参shopId:" + shopId + "物流信息logiscId:" + logiscId));
        ValueHolderV14<EwayBillResult> result = new ValueHolderV14<>();
        if (null == shopId || null == logiscId) {
            result.setCode(ResultCode.FAIL);
            result.setMessage("店铺id/物流公司ID为空");
        }
        //根据店铺id查询电子面单策略id
        List<StCEwaybillShopDO> stCEwaybillShopDOList = stCEwaybillShopMapper.queryShopList(shopId);
        if (CollectionUtils.isEmpty(stCEwaybillShopDOList)) {
            result.setCode(ResultCode.FAIL);
            result.setMessage("根据店铺id获取不到店铺信息");
            return result;
        }
        List<Long> ids = stCEwaybillShopDOList.stream().map(StCEwaybillShopDO::getStCEwaybillId).collect(Collectors.toList());
        List<StCEwaybillDO> stCEwaybillDOList = stCEwaybillMapper.queryEwayBillList(ids);
        if (CollectionUtils.isEmpty(stCEwaybillDOList)) {
            result.setCode(ResultCode.FAIL);
            result.setMessage("获取不到电子面单策略信息");
            return result;
        }

        QueryWrapper<StCEwaybillLogisticsDO> queryWrapper = new QueryWrapper();
        queryWrapper.eq("st_c_ewaybill_id", stCEwaybillDOList.get(0).getId());
        queryWrapper.eq("cp_c_logistics_id", logiscId);
        List<StCEwaybillLogisticsDO> stCEwaybillLogisticsDOList = stCEwaybillLogisticsMapper.selectList(queryWrapper);
        if (CollectionUtils.isEmpty(stCEwaybillLogisticsDOList)) {
            result.setCode(ResultCode.FAIL);
            result.setMessage("该物流公司获取不到电子面单策略信息");
            return result;
        }
        EwayBillResult ewayBillResult = new EwayBillResult();
        ewayBillResult.setStCEwaybillDO(stCEwaybillDOList.get(0));
        ewayBillResult.setStCEwaybillLogisticsDO(stCEwaybillLogisticsDOList.get(0));
        result.setCode(ResultCode.SUCCESS);
        result.setData(ewayBillResult);
        result.setMessage("获取成功");
        return result;
    }

    /**
     * 根据店铺id 查询店铺是否可以进行强制平台发货
     *
     * @param shopId
     * @return
     */
    public ValueHolderV14<List<StCShopStrategyDO>> querySendShopById(Long shopId) {
        ValueHolderV14<List<StCShopStrategyDO>> result = new ValueHolderV14<>();
        if (null == shopId) {
            result.setCode(ResultCode.FAIL);
            result.setMessage("未获取到店铺id");
            return result;
        }
        QueryWrapper<StCShopStrategyDO> queryWrapper = new QueryWrapper();
        queryWrapper.eq("CP_C_SHOP_ID", shopId);
        queryWrapper.eq("IS_FORCE_SEND", isForceSend);
        List<StCShopStrategyDO> stCShopStrategyDOList = stCShopStrategyMapper.selectList(queryWrapper);
        result.setCode(ResultCode.SUCCESS);
        result.setData(stCShopStrategyDOList);
        return result;
    }

    /**
     * 查询所有自动审单策略
     */
    public ValueHolderV14<List<StCAutoCheckDO>> queryAllList() {
        ValueHolderV14<List<StCAutoCheckDO>> result = new ValueHolderV14<>();

        List<StCAutoCheckDO> stCAutoCheckDOList = null;
        try {
            String redisKey = OmsRedisKeyResources.buildAutoCheckAllListRedisKey();

            QueryWrapper<StCAutoCheckDO> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("ISACTIVE", "Y");
            stCAutoCheckDOList = stCAutocheckMapper.selectList(queryWrapper);
            if (CollectionUtils.isEmpty(stCAutoCheckDOList)) {
                log.error(LogUtil.format("数据库中没有自动审核店铺策略：redisKey=", redisKey));
                result.setCode(ResultCode.FAIL);
                result.setMessage("数据库中没有自动审核店铺策略");
                return result;

            }

            result.setCode(ResultCode.SUCCESS);
            result.setData(stCAutoCheckDOList);

        } catch (Exception e) {
            result.setCode(ResultCode.FAIL);
            result.setMessage(e.getMessage());
        }
        return result;
    }

    public ValueHolderV14<List<StCAutoCheckResult>> queryAUtoCheckAllListAndItem() {

        ValueHolderV14<List<StCAutoCheckResult>> result = new ValueHolderV14<>();


        try {

            QueryWrapper<StCAutoCheckDO> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("ISACTIVE", "Y");
            List<StCAutoCheckDO> stCAutoCheckDOList = stCAutocheckMapper.selectList(queryWrapper);

            if (CollectionUtils.isEmpty(stCAutoCheckDOList)) {
                result.setCode(ResultCode.FAIL);
                result.setMessage("数据库中没有自动审核店铺策略");
                return result;

            }
            List<Long> collect = stCAutoCheckDOList.stream().map(StCAutoCheckDO::getId).collect(Collectors.toList());

            List<StCAutoCheckAutoTimeDO> stCAutoCheckAutoTimeDOS =
                    autoCheckAutoTimeMapper.selectList(new QueryWrapper<StCAutoCheckAutoTimeDO>()
                            .lambda()
                            .in(StCAutoCheckAutoTimeDO::getStCAutocheckId, collect)
                            .eq(StCAutoCheckAutoTimeDO::getIsactive, YesNoEnum.Y.getKey()));

            Map<Long, List<StCAutoCheckAutoTimeDO>> collect1 = stCAutoCheckAutoTimeDOS.stream().collect(Collectors.groupingBy(StCAutoCheckAutoTimeDO::getStCAutocheckId));

            List<StCAutoCheckResult> autoCheckResultList = new ArrayList<>();
            for (StCAutoCheckDO stCAutoCheckDO : stCAutoCheckDOList) {
                StCAutoCheckResult stCAutoCheckResult = new StCAutoCheckResult();
                stCAutoCheckResult.setStCAutoCheckDO(stCAutoCheckDO);
                stCAutoCheckResult.setAutoTimes(collect1.get(stCAutoCheckDO.getId()));
                autoCheckResultList.add(stCAutoCheckResult);
            }

            result.setCode(ResultCode.SUCCESS);
            result.setData(autoCheckResultList);

        } catch (Exception e) {
            result.setCode(ResultCode.FAIL);
            result.setMessage(e.getMessage());
        }

        return result;
    }


    //private Set<Long> getRedisAllShopIds(String key) {
    //    Set<Long> result = null;
    //
    //    try {
    //        if (redisUtil.strRedisTemplate.hasKey(key)) {
    //            Set<String> checkShopIds = redisUtil.strRedisTemplate.opsForSet().members(key);
    //            if (CollectionUtils.isNotEmpty(checkShopIds)) {
    //
    //                result = checkShopIds.stream().filter(shopId -> org.apache.commons.lang3.StringUtils.isNoneBlank(shopId)).map(shopId -> Long.parseLong(shopId)).collect(Collectors.toSet());
    //                return result;
    //            }
    //        }
    //    } catch (Exception e) {
    //        throw new NDSException("redis查询所有店铺ID异常," + e.getMessage());
    //    }
    //
    //    return null;
    //}

    //private void setRedisAllShopIds(String key, Set<Long> value) {
    //
    //    if (StringUtils.isBlank(key) || CollectionUtils.isEmpty(value)) {
    //        return;
    //    }
    //    String[] valueArray = value.stream().filter(shopId -> shopId != null)
    //            .map(shopId -> shopId.toString()).collect(Collectors.toSet())
    //            .stream().toArray(String[]::new);
    //
    //    setRedisAllShopIds(key, valueArray);
    //}

    //private void setRedisAllShopIds(String key, String[] value) {
    //    if (StringUtils.isBlank(key) || value == null || value.length == 0) {
    //        return;
    //    }
    //
    //    try {
    //        //存放在redis中
    //        if (redisUtil.strRedisTemplate.hasKey(key)) {
    //            redisUtil.strRedisTemplate.delete(key);
    //        }
    //        redisUtil.strRedisTemplate.opsForSet().add(key, value);
    //    } catch (Exception e) {
    //        throw new NDSException("redis保存所有自动审单策略异常," + e.getMessage());
    //    }
    //}

    //public ValueHolderV14<Set<Long>> queryAutoCheckAllShopIds() {
    //
    //    ValueHolderV14<Set<Long>> result = new ValueHolderV14<>();
    //    String redisKey = OmsRedisKeyResources.buildAutoCheckAllListRedisKey();
    //    Set<Long> resultSet = null;
    //
    //    try {
    //        Set<Long> checkShopIds = getRedisAllShopIds(redisKey);
    //        if (CollectionUtils.isNotEmpty(checkShopIds)) {
    //            resultSet = checkShopIds;
    //            result.setCode(ResultCode.SUCCESS);
    //            result.setData(resultSet);
    //            return result;
    //        }
    //    } catch (Exception e) {
    //        log.error(LogUtil.format("{}.redis查询所有自动审单策略异常", this.getClass().getName(), e);
    //    }
    //
    //    Map<Long, StCAutoCheckDO> map = queryDbAutoCheckAllShopIds();
    //    if (MapUtils.isEmpty(map)) {
    //        result.setCode(ResultCode.FAIL);
    //        result.setMessage("未查到开启审核的店铺");
    //        return result;
    //    }
    //    resultSet = map.keySet();
    //    try {
    //        //存放在redis中
    //        this.setRedisAllShopIds(redisKey, resultSet);
    //    } catch (Exception e) {
    //        log.error(LogUtil.format("{}.redis保存所有自动审单策略异常", this.getClass().getName(), e);
    //    }
    //    result.setCode(ResultCode.SUCCESS);
    //    result.setData(resultSet);
    //
    //    return result;
    //}

    /**
     * 查询所有店铺审核策略
     *
     * @return
     */
    //public ValueHolderV14<Map<Long, StCAutoCheckDO>> queryAutoCheckAll() {
    //
    //    ValueHolderV14<Map<Long, StCAutoCheckDO>> result = new ValueHolderV14<>();
    //
    //    ValueHolderV14<Set<Long>> allShopIds = this.queryAutoCheckAllShopIds();
    //    if (allShopIds == null || !allShopIds.isOK()) {
    //        result.setCode(ResultCode.FAIL);
    //        result.setMessage("未查到开启审核的店铺");
    //        return result;
    //    }
    //    Set<Long> autoCheckAllShopIds = allShopIds.getData();
    //
    //    Map<Long, StCAutoCheckDO> resultMap = new HashMap<>(autoCheckAllShopIds.size());
    //    List<Long> notExistRedisIds = new ArrayList<>();
    //
    //    try {
    //        try {
    //            for (Long shopId : autoCheckAllShopIds) {
    //                StCAutoCheckDO stCAutoCheckDO = redisHashCommonUtils.hgetall(
    //                        OmsRedisKeyResources.bulidLockStCAutoCheckKey(shopId), StCAutoCheckDO.class);
    //                if (stCAutoCheckDO == null) {
    //                    notExistRedisIds.add(shopId);
    //                    continue;
    //                }
    //                resultMap.put(shopId, stCAutoCheckDO);
    //            }
    //            if (MapUtils.isNotEmpty(resultMap)) {
    //                result.setCode(ResultCode.SUCCESS);
    //                result.setData(resultMap);
    //                return result;
    //            }
    //        } catch (Exception e) {
    //            log.error(LogUtil.format("{}.redis查询所有自动审单策略异常", this.getClass().getName(), e);
    //        }
    //
    //        Map<Long, StCAutoCheckDO> map = queryDbAutoCheckAllShopIds();
    //        if (MapUtils.isEmpty(map)) {
    //            return null;
    //        }
    //
    //        resultMap = map;
    //
    //        try {
    //            this.setRedisAllShopIds(OmsRedisKeyResources.buildAutoCheckAllListRedisKey(), resultMap.keySet());
    //            //存放在redis中
    //            redisHashCommonUtils.hset(RedisConstant.bulidLockStCAutoCheckKey(), result);
    //        } catch (Exception e) {
    //            log.error(LogUtil.format("{}.redis保存所有自动审单策略异常", this.getClass().getName(), e);
    //        }
    //        result.setCode(ResultCode.SUCCESS);
    //        result.setData(resultMap);
    //
    //    } catch (NDSException e) {
    //        log.error(LogUtil.format("queryAUtoCheckAllList查询所有店铺审核策略异常", e);
    //    }
    //    return result;
    //}

    /**
     * 查询单个店铺审核策略
     * @param shopId
     * @return
     */
    //public ValueHolderV14<StCAutoCheckDO> queryAUtoCheckByShopId(Long shopId) {
    //
    //    ValueHolderV14<StCAutoCheckDO> result = new ValueHolderV14<>();
    //
    //    if (shopId == null || shopId == 0L){
    //        result.setCode(ResultCode.FAIL);
    //        result.setMessage("请传入店铺ID");
    //        return result;
    //    }
    //
    //    try {
    //        StCAutoCheckDO stCAutoCheckDO = redisHashCommonUtils.hgetall(
    //                OmsRedisKeyResources.bulidLockStCAutoCheckKey(shopId), StCAutoCheckDO.class);
    //        if (stCAutoCheckDO != null) {
    //            result.setCode(ResultCode.SUCCESS);
    //            result.setData(stCAutoCheckDO);
    //            return result;
    //        }
    //    } catch (Exception e) {
    //        log.error(LogUtil.format("{}.redis查询自动审单策略异常", this.getClass().getName(), e);
    //    }
    //
    //    StCAutoCheckDO stCAutoCheckDO = stCAutocheckMapper.queryOcStCAutocheck(shopId);
    //    if (stCAutoCheckDO == null){
    //        result.setCode(ResultCode.FAIL);
    //        result.setMessage("未查到店铺[" + shopId + "]");
    //        return result;
    //    }
    //
    //    try {
    //        //存放在redis中
    //        redisHashCommonUtils.hset(RedisConstant.bulidLockStCAutoCheckKey(shopId), stCAutoCheckDO);
    //    } catch (Exception e) {
    //        log.error(LogUtil.format("{}.redis保存所有自动审单策略异常", this.getClass().getName(), e);
    //    }
    //
    //    result.setCode(ResultCode.SUCCESS);
    //    result.setData(stCAutoCheckDO);
    //
    //    return result;
    //}

    //private Map<Long, StCAutoCheckDO> queryDbAutoCheckAllShopIds() {
    //    QueryWrapper<StCAutoCheckDO> queryWrapper = new QueryWrapper<>();
    //    queryWrapper.eq("ISACTIVE", "Y");
    //    queryWrapper.eq("IS_AUTOCHECK_ORDER", "Y");
    //    List<StCAutoCheckDO> stCAutoCheckDOS = stCAutocheckMapper.selectList(queryWrapper);
    //    if (CollectionUtils.isEmpty(stCAutoCheckDOS)) {
    //        return null;
    //    }
    //
    //    Map<Long, StCAutoCheckDO> map =
    //            stCAutoCheckDOS.stream().filter(item -> item.getCpCShopId() != null)
    //                    .collect(Collectors.toMap(StCAutoCheckDO::getCpCShopId, item -> item));
    //
    //    return map;
    //}
}
