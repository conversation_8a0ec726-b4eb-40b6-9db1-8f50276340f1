package com.jackrain.nea.st.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.google.common.base.Throwables;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.st.common.StConstant;
import com.jackrain.nea.st.mapper.StCVirtualHighStockItemMapper;
import com.jackrain.nea.st.mapper.StCVirtualHighStockMapper;
import com.jackrain.nea.st.model.table.StCShopVirtualHighStockDO;
import com.jackrain.nea.st.model.table.StCShopVirtualHighStockItemDO;
import com.jackrain.nea.st.utils.DatasToEsUtils;
import com.jackrain.nea.st.utils.ListUtils;
import com.jackrain.nea.st.utils.StBeanUtils;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.util.DateUtil;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.io.IOException;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;


/**
 * <AUTHOR> ShiLong
 * @Date: 2020/6/22 10:26 下午
 * @Desc: 延期逻辑（店铺商品虚高库存）
 */
@Component
@Slf4j
public class StCVirtualHighStockDelayService extends CommandAdapter {
    @Autowired
    private StCVirtualHighStockMapper stCVirtualHighStockMapper;

    @Autowired
    private StCVirtualHighStockItemMapper stCVirtualHighStockItemMapper;

//    @Autowired
//    private ShopUniversalStockService shopUniversalStockService;

    public ValueHolder execute(QuerySession querySession, String newEndTime) throws NDSException {
        DefaultWebEvent event = querySession.getEvent();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(
                event.getParameterValue("param"), "yyyy-MM-dd HH:mm:ss"),
                Feature.OrderedField);
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("执行StCVirtualHighStockDelayService:{}"), param.toJSONString());
        }
        ValueHolder valueHolder = new ValueHolder();
        int errRecord = 0;
        HashMap<Long, Object> errMap = new HashMap<>();
        //生成数组
        JSONArray auditArray = StBeanUtils.makeDelayJsonArray(param);
        //列表批量
        if (!CollectionUtils.isEmpty(auditArray)) {
            StCVirtualHighStockDelayService bean =
                    ApplicationContextHandle.getBean(StCVirtualHighStockDelayService.class);
            for (int i = 0; i < auditArray.size(); i++) {
                //遍历 jsonarray 数组，把每一个对象转成 json 对象
                Long id = Long.valueOf(auditArray.get(i).toString());
                try {
                    bean.updateAuditState(querySession, id, newEndTime, valueHolder);
                } catch (Exception ex) {
                    errRecord++;
                    errMap.put(id, ex.getMessage());
                }
            }
        }
        //返回日志
        return StBeanUtils.getExcuteValueHolder(auditArray.size(), errMap);
    }

    @Transactional(rollbackFor = Exception.class)
    public void updateAuditState(QuerySession session, Long id, String newEndTime, ValueHolder valueHolder) {
        StCShopVirtualHighStockDO result = stCVirtualHighStockMapper.selectById(id);
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("执行updateAuditState:id:{},newEndTime:{},valueHolder:{}", id), newEndTime,
                    valueHolder.toJSONObject());
        }
        valueHolder = checkAutoCheckStatus(result, newEndTime, valueHolder);
        if (!valueHolder.isOK() && !valueHolder.getData().get("code").equals(-2)) {
            return;
        }
        //修改信息
        StBeanUtils.makeModifierField(result, session.getUser());
        //延迟
        result.setState(Integer.valueOf(StConstant.CON_BILL_STATUS_02));
        //延迟生效结束时间
        result.setEndTime(DateUtil.stringToDate(newEndTime));
        int update = stCVirtualHighStockMapper.updateById(result);
        if (update > 0) {
            //推送ES数据
//            ValueHolderV14<Integer> v14 = shopUniversalStockService.updateSgBSyncChannelStock(id, result.getEndTime(), session.getUser());
//            log.debug(LogUtil.format("商品虚高 延期 修改同步库存中间表数据 策略id：" + id + " 结果：" + JSONObject.toJSONString(v14));
            try {
                //做更新的需要先查询更新后数据库的实体在推ES
                QueryWrapper<StCShopVirtualHighStockItemDO> wrapper = updateVirtualHighItems(id, result);
                QueryWrapper<StCShopVirtualHighStockItemDO> query = new QueryWrapper<StCShopVirtualHighStockItemDO>()
                        .eq("ST_C_SHOP_VIRTUAL_HIGH_STOCK_ID", id);
                List<StCShopVirtualHighStockItemDO> itemList = ListUtils.batchQueryByCondition(query, stCVirtualHighStockItemMapper, 1000);
                if (log.isDebugEnabled()) {
                    log.debug(LogUtil.format("店铺商品虚高库存推送es start：") + result);
                }
                DatasToEsUtils.insertVirtualHighStockEsData(result, null, StConstant.TAB_ST_C_SHOP_VIRTUAL_HIGH_STOCK);
                if (CollectionUtils.isNotEmpty(itemList)) {
                    DatasToEsUtils.insertVirtualHighStockEsData(result, itemList, StConstant.TAB_ST_C_SHOP_VIRTUAL_HIGH_STOCK_ITEM);
                }
                if (log.isDebugEnabled()) {
                    log.debug(LogUtil.format("店铺商品虚高库存推送es成功："));
                }
            } catch (Exception ex) {
                log.debug(LogUtil.format("店铺商品虚高库存推数据到ES失败：{}"), Throwables.getStackTraceAsString(ex));
            }
        }
    }

    private ValueHolder checkAutoCheckStatus(StCShopVirtualHighStockDO stCAutocheckDO, String newEndTime, ValueHolder valueHolder) {
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("【虚高库存延期参数校验】stCAutocheckDO:{},newEndTime:{},valueHolder:{}"),
                    JSONObject.toJSON(stCAutocheckDO), newEndTime, valueHolder.toJSONObject());
        }
        if (stCAutocheckDO == null) {
            valueHolder.put("code", -1);
            valueHolder.put("message", Resources.getMessage("记录不存在！", new Locale("zh", "CN")));
            return valueHolder;
        }
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("【虚高库存延期记录延期】isResult:{}"),
                    stCAutocheckDO.getIsactive().equals(StConstant.ISACTIVE_N));
        }
        //记录已作废，不允许延期
        if (stCAutocheckDO.getIsactive().equals(StConstant.ISACTIVE_N)) {
            throw new NDSException("方案已作废，不允许延期！");
        }
        if (stCAutocheckDO.getState().equals(StConstant.CON_BILL_STATUS_04)) {
            throw new NDSException("方案已结案，不允许延期！");
        }
        if (!stCAutocheckDO.getState().equals(StConstant.CON_BILL_STATUS_02)) {
            throw new NDSException("方案状态不是已审核，不允许延期！");
        }
        if (DateUtil.stringToDate(newEndTime) != null && stCAutocheckDO.getBeginTime() != null) {
            if (DateUtil.stringToDate(newEndTime).before(stCAutocheckDO.getEndTime())) {
                throw new NDSException("生效结束时间不能早于原来生效结束时间！");
            }
        }
        valueHolder.put("code", 0);
        valueHolder.put("message", "成功");
        return valueHolder;
    }

    /**
     * 批量延期结束时间
     *
     * @return
     */
    public ValueHolder batchDelayEndTime(QuerySession querySession) {
        ValueHolder valueHolder = new ValueHolder();
        valueHolder.put("code", 0);
        valueHolder.put("message", "成功");
        DefaultWebEvent event = querySession.getEvent();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(
                event.getParameterValue("param"), "yyyy-MM-dd HH:mm:ss"),
                Feature.OrderedField);
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("【执行虚高库存延期】param:{}"), param.toJSONString());
        }
        JSONArray itemArray;
        HashMap<Long, Object> errMap = new HashMap<>(16);
        int size = 0;
        try {
            itemArray = StBeanUtils.makeVoidJsonArray(param);
            if (itemArray != null) {
                size = itemArray.size();
            }
            String newEndTime = (String) param.get("newEndTime");
            if (size > 0) {
                StCVirtualHighStockDelayService bean =
                        ApplicationContextHandle.getBean(StCVirtualHighStockDelayService.class);
                for (int i = 0; i < size; i++) {
                    Long itemId = itemArray.getLong(i);
                    try {
                        bean.updateDelayState(querySession.getUser(), itemId, newEndTime, valueHolder);
                    } catch (Exception e) {
                        errMap.put(itemId, e.getMessage());
                    }
                }
            }
        } catch (Exception e) {
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("【执行虚高库存延期异常】{}"), Throwables.getStackTraceAsString(e));
            }
        }
        return StBeanUtils.getExcuteValueHolder(size, errMap);
    }

    /**
     * <ul>
     *     <li>
     *         修改虚高库存延期
     *     </li>
     * </ul>
     *
     * @param user        用户
     * @param id          主表id
     * @param newEndTime  新结束时间
     * @param valueHolder 结果
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateDelayState(User user, Long id, String newEndTime, ValueHolder valueHolder) {
        StCShopVirtualHighStockDO result = stCVirtualHighStockMapper.selectById(id);
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("【执行虚高库存延期状态更新入参】result:{}"), JSONObject.toJSON(result));
        }
        checkAutoCheckStatus(result, newEndTime, valueHolder);
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("【执行虚高库存延期状态更新校验入参结果】valueHolder:{}"), JSONObject.toJSON(valueHolder));
        }
        // 双非短路与判断改成正向判断
        // 修改信息
        if (valueHolder.isOK() || "-2".equals(valueHolder.get("code"))) {
            StBeanUtils.makeModifierField(result, user);
            //延迟
            result.setState(StConstant.CON_BILL_STATUS_02);
            //延迟生效结束时间
            Date endTime = DateUtil.stringToDate(newEndTime);
            result.setEndTime(endTime);
            int update = stCVirtualHighStockMapper.updateById(result);
            if (update > 0) {
                //推送ES数据
//                ValueHolderV14<Integer> v14 = shopUniversalStockService.updateSgBSyncChannelStock(id, endTime, user);
//                log.debug(LogUtil.format("商品虚高 延期 修改同步库存中间表数据 策略id：" + id + " 结果：" + JSONObject.toJSONString(v14));
                try {
                    //做更新的需要先查询更新后数据库的实体在推ES
                    QueryWrapper<StCShopVirtualHighStockItemDO> wrapper = updateVirtualHighItems(id, result);
                    pushVirtualHighItemsToEle(result, wrapper, stCVirtualHighStockItemMapper);
                } catch (Exception ex) {
                    log.debug(LogUtil.format("店铺商品虚高库存推数据到ES失败：{}"), Throwables.getStackTraceAsString(ex));
                }
            }
        }
    }

    /**
     * <ul>
     *     <li>
     *         修改虚高库存明细表
     *     </li>
     * </ul>
     *
     * @param id     主表id
     * @param result 主表信息
     * @return 条件
     */
    private QueryWrapper<StCShopVirtualHighStockItemDO> updateVirtualHighItems(Long id, StCShopVirtualHighStockDO result) {
        StCShopVirtualHighStockItemDO item = new StCShopVirtualHighStockItemDO();
        item.setStatus(result.getState());
        item.setEndTime(result.getEndTime());
        QueryWrapper<StCShopVirtualHighStockItemDO> wrapper = new QueryWrapper<>();
        wrapper.eq("st_c_shop_virtual_high_stock_id", id);
        stCVirtualHighStockItemMapper.update(item, wrapper);
        return wrapper;
    }

    /**
     * <ul>
     *     <li>
     *         推数据
     *     </li>
     * </ul>
     *
     * @param result                     主表
     * @param wrapper                    明细mapper
     * @param virtualHighStockItemMapper 主表mapper
     * @throws IOException exception
     */
    static void pushVirtualHighItemsToEle(StCShopVirtualHighStockDO result, QueryWrapper<StCShopVirtualHighStockItemDO> wrapper, StCVirtualHighStockItemMapper virtualHighStockItemMapper) throws IOException {
        List<StCShopVirtualHighStockItemDO> itemList = ListUtils.batchQueryByCondition(wrapper, virtualHighStockItemMapper, 1000);
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("店铺商品虚高库存推送es start：") + result);
        }
        DatasToEsUtils.insertVirtualHighStockEsData(result, null, StConstant.TAB_ST_C_SHOP_VIRTUAL_HIGH_STOCK);
        if (CollectionUtils.isNotEmpty(itemList)) {
            DatasToEsUtils.insertVirtualHighStockEsData(result, itemList, StConstant.TAB_ST_C_SHOP_VIRTUAL_HIGH_STOCK_ITEM);
        }
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("店铺商品虚高库存推送es成功："));
        }
    }
}