package com.jackrain.nea.st.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.google.common.base.Throwables;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.model.resources.OmsRedisKeyResources;
import com.jackrain.nea.redis.util.RedisOpsUtil;
import com.jackrain.nea.st.mapper.StCVipcomCooperationNoMapper;
import com.jackrain.nea.st.utils.StBeanUtils;
import com.jackrain.nea.st.utils.ValueHolderUtils;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;

/**
 * description：合单策略删除
 *
 * <AUTHOR>
 * @date 2021/5/13
 */
@Component
@Slf4j
@Transactional
public class VipcomCooperationNoDeleteService extends CommandAdapter {

    @Autowired
    private RedisOpsUtil redisUtil;

    @Autowired
    private StCVipcomCooperationNoMapper stCCooperationNoMapper;


    @Override
    public ValueHolder execute(QuerySession querySession) throws NDSException {
        DefaultWebEvent event = querySession.getEvent();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(
                event.getParameterValue("param"), "yyyy-MM-dd HH:mm:ss"),
                Feature.OrderedField);
        if (param == null) {
            return ValueHolderUtils.getFailValueHolder("参数为空!");
        }
        //生成数组
        JSONArray voidArray = StBeanUtils.makeVoidJsonArray(param);
        if (CollectionUtils.isEmpty(voidArray)) {
            return ValueHolderUtils.getFailValueHolder("请选择数据");
        }
        int errRecord = 0;
        HashMap<Long, Object> errMap = new HashMap<>();
        //列表批量
        for (int i = 0; i < voidArray.size(); i++) {
            Long id = voidArray.getLong(i);
            try {
                stCCooperationNoMapper.deleteById(id);
            } catch (Exception ex) {
                errRecord++;
                errMap.put(id, ex.getMessage());
            }
        }
        //返回日志
        return StBeanUtils.getExcuteValueHolder(voidArray.size(), errMap);
    }

    private void delRedisKey() {
        try {
            String redisKey = OmsRedisKeyResources.buildAutoMergeAllListRedisKey();
            if (redisUtil.strRedisTemplate.hasKey(redisKey)) {
                redisUtil.strRedisTemplate.delete(redisKey);
            }
        } catch (Exception e) {
            log.error(LogUtil.format("redis删除自动合单策略异常{}"), Throwables.getStackTraceAsString(e));
        }
    }
}
