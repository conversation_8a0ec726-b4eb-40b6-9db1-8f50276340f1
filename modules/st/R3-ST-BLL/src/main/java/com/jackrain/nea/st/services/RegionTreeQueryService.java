package com.jackrain.nea.st.services;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.cpext.model.table.CpCRegion;
import com.jackrain.nea.st.model.result.RegionTreeResult;
import com.jackrain.nea.st.rpc.RpcCpService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @author: huang.zaizai
 * @since: 2019/08/07
 * create at : 2019/7/24 09:00
 */
@Slf4j
@Component
public class RegionTreeQueryService {
    @Autowired
    private RpcCpService rpcCpService;

    /**
     * 查询省市区树
     * @param obj
     * @return ValueHolderV14
     */
    public ValueHolderV14 queryRegionTree(JSONObject obj) {
        ValueHolderV14<List<RegionTreeResult>> vh = new ValueHolderV14<>();
        vh.setCode(ResultCode.SUCCESS);
        if (null == obj || !obj.containsKey("regiontype")) {
            vh.setCode(ResultCode.FAIL);
            vh.setMessage("参数为空");
            return vh;
        }
        //查询地址
        String regiontype = obj.getString("regiontype");
        String[] regiontypes = regiontype.split(",");
        List<String> regiontypeList= new ArrayList<>(Arrays.asList(regiontypes));
        List<CpCRegion> regionList = rpcCpService.queryAllRegion(regiontypeList);
        if (regionList != null) {
            regionList = regionList.stream().sorted(Comparator.comparing(CpCRegion::getId))
                        .collect(Collectors.toList());
            //构造树
            List<RegionTreeResult> resultList = new ArrayList<>();
            buildRegionTree(resultList, regionList, 1L);

            vh.setData(resultList);
        } else {
            vh.setCode(ResultCode.FAIL);
            vh.setMessage("省市区获取失败！");
            return vh;
        }
        return vh;
    }

    /**
     * 构建树
     * @param resultList
     * @param regionList
     * @param upId
     * @return List<RegionTreeResult>
     */
    private List<RegionTreeResult> buildRegionTree(List<RegionTreeResult> resultList,
                                                  List<CpCRegion> regionList,
                                                  Long upId) {
        for (CpCRegion region : regionList) {
            if (upId.equals(region.getCUpId())) {
                RegionTreeResult result = new RegionTreeResult();
                result.setId(region.getId());
                result.setEcode(region.getEcode());
                result.setTitle(region.getEname());
                result.setUpId(region.getCUpId());
                result.setRegiontype(region.getRegiontype());
                result.setChecked(false);
                //判断是后还拥有下层数据
                List<CpCRegion> regionCounts = regionList.stream().filter(r -> r.getCUpId().equals(result.getId()))
                        .collect(Collectors.toList());
                List<RegionTreeResult> childrenResultList = new ArrayList<>();
                if (CollectionUtils.isNotEmpty(regionCounts)) {
                    buildRegionTree(childrenResultList, regionList, result.getId());
                }
                result.setChildren(childrenResultList);
                resultList.add(result);
            }
        }
        return resultList;
    }
}
