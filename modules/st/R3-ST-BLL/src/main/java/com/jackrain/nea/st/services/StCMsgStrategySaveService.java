package com.jackrain.nea.st.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.google.common.base.Throwables;
import com.jackrain.nea.cpext.model.table.CpShop;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.st.common.StConstant;
import com.jackrain.nea.st.mapper.StCMsgMapper;
import com.jackrain.nea.st.model.table.StcMsgDO;
import com.jackrain.nea.st.rpc.RpcCpService;
import com.jackrain.nea.st.utils.JsonUtils;
import com.jackrain.nea.st.utils.StBeanUtils;
import com.jackrain.nea.st.utils.ValueHolderUtils;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@Component
@Slf4j
@Transactional
public class StCMsgStrategySaveService extends CommandAdapter {

    @Autowired
    private StCMsgMapper stCMsgMapper;

    @Autowired
    private RpcCpService rpcCpService;

    public ValueHolderV14 saveContent(QuerySession session) {
        ValueHolderV14<String> stringValueHolderV14 = new ValueHolderV14<>();
        DefaultWebEvent event = session.getEvent();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(
                event.getParameterValue("param"), "yyyy-MM-dd HH:mm:ss", SerializerFeature.WriteMapNullValue),
                Feature.OrderedField);
        log.debug(LogUtil.format("解析规则保存Json：") + param.toJSONString());
        Long id = param.getLong("objid");//获取objid参数
        String content = param.getString("content");//获取配置短信内容
        StcMsgDO stcMsgDO = new StcMsgDO();
        stcMsgDO.setId(id);
        stcMsgDO.setTemplateContent(content);
        try {
            if (stCMsgMapper.updateById(stcMsgDO) > 0) {
                stringValueHolderV14.setCode(0);
                stringValueHolderV14.setMessage("保存成功");
            } else {
                stringValueHolderV14.setCode(-1);
                stringValueHolderV14.setMessage("保存失败");
            }
        } catch (Exception ex) {
            log.error(LogUtil.format("StCMsgStrategySaveService.saveContent Error：{}"),
                    Throwables.getStackTraceAsString(ex));
            stringValueHolderV14.setMessage("保存异常,e=" + ex.getMessage());
        }
        return stringValueHolderV14;
    }


    @Override
    public ValueHolder execute(QuerySession session) {
        DefaultWebEvent event = session.getEvent();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(
                event.getParameterValue("param"), "yyyy-MM-dd HH:mm:ss", SerializerFeature.WriteMapNullValue),
                Feature.OrderedField);

        Long id = param.getLong("objid");
        JSONObject fixColumn = param.getJSONObject("fixcolumn");
        if (fixColumn != null && id != null) {
            if (id != -1) {
                return update(session, fixColumn, id);
            } else {
                return add(session, fixColumn);
            }
        }
        throw new NDSException("当前记录已不存在！");
    }

    private ValueHolder update(QuerySession session, JSONObject fixColumn, Long id) {
        JSONObject jsonObject = fixColumn.getJSONObject(StConstant.TAB_ST_C_MSG_STRATEGY);
        StcMsgDO stcMsgDO = JsonUtils.jsonParseClass(jsonObject, StcMsgDO.class);
        StcMsgDO existsStcMsgDO = stCMsgMapper.selectById(id);
        if (existsStcMsgDO == null) {
            throw new NDSException("当前记录已不存在！");
        }
        if (stcMsgDO != null) {
            //补充框架不回传的值（需要用来校验的值）
            beanCopy(fixColumn, existsStcMsgDO, stcMsgDO);
            //update基础字段
            stcMsgDO.setId(id);
            StBeanUtils.makeModifierField(stcMsgDO, session.getUser());

            //不允许修改成无名件（无名件只能新增）
            if (!StConstant.MSG_TASK_NODE_04.equals(existsStcMsgDO.getTaskNode()) && StConstant.MSG_TASK_NODE_04.equals(stcMsgDO.getTaskNode())) {
                return ValueHolderUtils.getFailValueHolder("不能直接修改成无名件策略,请新增无名件策略");
            }
            //无名件的策略类型不允许修改
            if (StConstant.MSG_TASK_NODE_04.equals(existsStcMsgDO.getTaskNode()) && StringUtils.isNotEmpty(stcMsgDO.getTaskNode()) && !StConstant.MSG_TASK_NODE_04.equals(stcMsgDO.getTaskNode())) {
                return ValueHolderUtils.getFailValueHolder("无名件不允许修改类型，请先删除已有策略");
            }
            if (StConstant.MSG_TASK_NODE_04.equals(existsStcMsgDO.getTaskNode()) && StringUtils.isNotEmpty(stcMsgDO.getAdviceType()) && !StConstant.MSG_ADVICE_TYPE_RETURN.equals(stcMsgDO.getAdviceType())) {
                return ValueHolderUtils.getFailValueHolder("无名件不允许修改通知类型，请先删除已有策略");
            }
            //非无名件店铺不能为空
            if (!StConstant.MSG_TASK_NODE_04.equals(stcMsgDO.getTaskNode()) && StringUtils.isEmpty(stcMsgDO.getCpCShopId())) {
                return ValueHolderUtils.getFailValueHolder("非无名件完成入库必须选择店铺");
            }
            //无名件不需要店铺信息
            if (StConstant.MSG_TASK_NODE_04.equals(existsStcMsgDO.getTaskNode())) {
                stcMsgDO.setCpCShopId(null);
            } else {
                //校验
                ArrayList<String> shopFailList = checkUpdateParam(stcMsgDO, existsStcMsgDO);
                if (CollectionUtils.isNotEmpty(shopFailList)) {
                    return ValueHolderUtils.getFailValueHolder(StringUtils.join(shopFailList, ",") + "店铺已配置短信策略，不可重复配置");
                }
            }
            try {
                setShopTitle(stcMsgDO);
                if (stCMsgMapper.updateById(stcMsgDO) > 0) {
                    return ValueHolderUtils.getSuccessValueHolder(id, StConstant.TAB_ST_C_MSG_STRATEGY);
                } else {
                    return ValueHolderUtils.getFailValueHolder("保存失败");
                }
            } catch (Exception ex) {
                log.error(LogUtil.format("StCMsgStrategySaveService.update Error：{}"),
                        Throwables.getStackTraceAsString(ex));
                return ValueHolderUtils.getFailValueHolder("保存异常");
            }
        }
        return ValueHolderUtils.getSuccessValueHolder(id, StConstant.TAB_ST_C_MSG_STRATEGY);
    }

    private ValueHolder add(QuerySession session, JSONObject fixColumn) {
        JSONObject jsonObject = fixColumn.getJSONObject(StConstant.TAB_ST_C_MSG_STRATEGY);
        String string = jsonObject.toString();
        if (StringUtils.isNotEmpty(string)) {
            StcMsgDO stcMsgDO = JsonUtils.jsonParseClass(jsonObject, StcMsgDO.class);

            //校验当【任务节点】为【无名件完成入库】不填店铺,其他的必须填店铺
            if (!StConstant.MSG_TASK_NODE_04.equals(stcMsgDO.getTaskNode()) && StringUtils.isEmpty(stcMsgDO.getCpCShopId())) {
                return ValueHolderUtils.getFailValueHolder("非无名件完成入库必须选择店铺");
            } else if (StConstant.MSG_TASK_NODE_04.equals(stcMsgDO.getTaskNode())) {
                String taskNode = stcMsgDO.getTaskNode();
                String advicType = stcMsgDO.getAdviceType();
                //店铺为空，且是无名件的需要判断唯一性
                stcMsgDO.setCpCShopId(null);
                List<StcMsgDO> stcMsgDOS = stCMsgMapper.selectByParam(null, null, null, advicType, taskNode);
                if (stcMsgDOS.size() > 0) {
                    return ValueHolderUtils.getFailValueHolder("无名件策略已存在!");
                }
            }

            //校验
            ArrayList<String> shopFailList = checkAddParam(stcMsgDO);
            if (shopFailList.size() > 0) {
                return ValueHolderUtils.getFailValueHolder(StringUtils.join(shopFailList, ",") + "店铺已配置短信策略，不可重复配置");
            }

            List<StcMsgDO> stcMsgDOS = stCMsgMapper.selectByParam(null, stcMsgDO.getMsgName(), null, null, null);
            if (stcMsgDOS.size() > 0) {
                return ValueHolderUtils.getFailValueHolder("短信策略名已存在!");
            }


            stcMsgDO.setId(ModelUtil.getSequence(StConstant.TAB_ST_C_MSG_STRATEGY));
            StBeanUtils.makeCreateField(stcMsgDO, session.getUser());

            try {
                setShopTitle(stcMsgDO);
                int insertResult = stCMsgMapper.insert(stcMsgDO);
                if (insertResult > 0) {
                    return ValueHolderUtils.getSuccessValueHolder(stcMsgDO.getId(), StConstant.TAB_ST_C_MSG_STRATEGY);
                } else {
                    return ValueHolderUtils.getFailValueHolder("保存失败");
                }
            } catch (Exception ex) {
                log.error(LogUtil.format("StCMsgStrategySaveService.add Error：{}"),
                        Throwables.getStackTraceAsString(ex));
                return ValueHolderUtils.getFailValueHolder("保存异常");
            }
        }
        throw new NDSException("当前表st_c_msg_strategy不存在！");
    }

    private ArrayList<String> checkAddParam(StcMsgDO stcMsgDO) {

        //校验,同一个店铺只能有一个同类型短信策略
        ArrayList<String> shopFailList = new ArrayList<>();
        String msgName = stcMsgDO.getMsgName();
        String cpCShopId = stcMsgDO.getCpCShopId(); //这里逻辑需要先判空
        String taskNode = stcMsgDO.getTaskNode();
        String advicType = stcMsgDO.getAdviceType();

        List<String> splitShopId = new ArrayList<>();
        if (StringUtils.isNotEmpty(cpCShopId)) {
            splitShopId = Arrays.asList(cpCShopId.split(","));
        }

        for (String shopId : splitShopId) {
            List<StcMsgDO> stcMsgDOS = stCMsgMapper.selectByParam(Long.valueOf(shopId), msgName, null, advicType, taskNode);
            if (stcMsgDOS.size() > 0) {
                shopFailList.add(shopId);
            }
        }
        return shopFailList;
    }

    private ArrayList<String> checkUpdateParam(StcMsgDO stcMsgDO, StcMsgDO stcMsgDOBef) {

        //不允许修改字段
        stcMsgDO.setMsgName(null);

        //校验,同一个店铺只能有一个同类型短信策略
        ArrayList<String> shopFailList = new ArrayList<>();
        Long id = stcMsgDO.getId();
        String cpCShopId = stcMsgDO.getCpCShopId();
        if (StringUtils.isEmpty(cpCShopId)) {
            return shopFailList;
        }
        String taskNode = stcMsgDO.getTaskNode();
        String advicType = stcMsgDO.getAdviceType();

        List<String> splitShopId = new ArrayList<>();
        //如果修改(只针对新增)了店铺id筛选,那么需要判断新增的店铺有没有配置其他类型策略
        if (stcMsgDOBef.getAdviceType().equals(advicType) && stcMsgDOBef.getTaskNode().equals(taskNode)) {
            String cpCShopIdBef = stcMsgDOBef.getCpCShopId();
            if (cpCShopIdBef.equals(cpCShopId)) {
                return null;
            } else {
                List<String> idBef = Arrays.asList(stcMsgDOBef.getCpCShopId().split(","));
                List<String> idAf = Arrays.asList(cpCShopId.split(","));
                idAf.removeAll(idBef);
                splitShopId = idAf;

            }
        }
        for (String shopId : splitShopId) {
            List<StcMsgDO> stcMsgDOS = stCMsgMapper.selectByParam(Long.valueOf(shopId), null, null, advicType, taskNode);
            if (stcMsgDOS.size() > 0) {
                shopFailList.add(shopId);
            }
        }
        return shopFailList;
    }

    private void setShopTitle(StcMsgDO stcMsgDO) {
        String cpCShopId = stcMsgDO.getCpCShopId();
        if (StringUtils.isEmpty(cpCShopId)) {
            return;
        }
        String[] split = cpCShopId.split(",");
        List<Long> collect = Arrays.stream(split).map(Long::parseLong).collect(Collectors.toList());
        List<CpShop> cpShops = rpcCpService.queryShopByIds(collect);
        String shopTitles = cpShops.stream().map(CpShop::getCpCShopTitle).collect(Collectors.joining(","));
        stcMsgDO.setCpCShopTitle(shopTitles);
    }

    private void beanCopy(JSONObject fixColumn, StcMsgDO existsStcMsgDO, StcMsgDO stcMsgDO) {
        if (!fixColumn.containsKey("CP_C_SHOP_ID")) {
            stcMsgDO.setCpCShopId(existsStcMsgDO.getCpCShopId());
        } else if (!fixColumn.containsKey("ADVICE_TYPE")) {
            stcMsgDO.setAdviceType(existsStcMsgDO.getAdviceType());
        } else if (!fixColumn.containsKey("TASK_NODE")) {
            stcMsgDO.setTaskNode(existsStcMsgDO.getTaskNode());
        } else if (!fixColumn.containsKey("ISACTIVE")) {
            stcMsgDO.setIsactive(existsStcMsgDO.getIsactive());
        }
    }

}
