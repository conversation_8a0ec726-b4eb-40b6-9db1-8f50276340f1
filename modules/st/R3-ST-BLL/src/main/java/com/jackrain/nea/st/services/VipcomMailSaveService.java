package com.jackrain.nea.st.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.google.common.base.Throwables;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.st.common.StConstant;
import com.jackrain.nea.st.mapper.StCVipcomMailMapper;
import com.jackrain.nea.st.model.request.VipcomProjectRequest;
import com.jackrain.nea.st.model.table.StCResolveRuleDO;
import com.jackrain.nea.st.model.table.StCVipcomMailDO;
import com.jackrain.nea.st.utils.JsonUtils;
import com.jackrain.nea.st.utils.StBeanUtils;
import com.jackrain.nea.st.utils.ValueHolderUtils;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.sql.Timestamp;
import java.util.List;
import java.util.regex.Pattern;

/**
 * 唯品会通知邮件保存接口
 *
 * <AUTHOR>
 * @Date 2019/3/11 13:44
 */
@Component
@Slf4j
@Transactional
public class VipcomMailSaveService extends CommandAdapter {

    @Autowired
    private StCVipcomMailMapper stCVipcomMailMapper;

    /**
     * @param session
     * @return
     * @Author: 朱宇军
     * @Date 2019/3/11
     */

    public ValueHolder execute(QuerySession session) {
        DefaultWebEvent event = session.getEvent();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(
                event.getParameterValue("param"), "yyyy-MM-dd HH:mm:ss", SerializerFeature.WriteMapNullValue),
                Feature.OrderedField);
        if (log.isDebugEnabled()){
            log.debug(LogUtil.format("VipcomMailSaveService.param:{}"), JSON.toJSONString(param));
        }
        Long id = param.getLong("objid");
        JSONObject fixColumn = param.getJSONObject("fixcolumn");
        if (fixColumn != null && id != null) {
            if (id != -1) {
                return updateVipcomMail(session, fixColumn, id);
            } else {
                return addVipcomMail(session, fixColumn);
            }
        }
        throw new NDSException("当前记录已不存在！");
    }

    /**
     * @param session
     * @param fixColumn
     * @param id
     * @return
     * @Author: 朱宇军
     * @Date 2019/3/7
     */

    private ValueHolder updateVipcomMail(QuerySession session, JSONObject fixColumn, Long id) {
        JSONObject jsonObject = fixColumn.getJSONObject(StConstant.TAB_ST_C_VIPCOM_MAIL);
        StCVipcomMailDO stCVipcomMailDO = JsonUtils.jsonParseClass(jsonObject, StCVipcomMailDO.class);
        if (log.isDebugEnabled()){
            log.debug(LogUtil.format("VipcomMailSaveService.updateVipcomMail 转换对象"));
        }
        StCVipcomMailDO existsVipcomMail = stCVipcomMailMapper.selectById(id);
        if (existsVipcomMail == null) {
            throw new NDSException("当前记录已不存在！");
        }
        //update基础字段
        if(stCVipcomMailDO != null){
            stCVipcomMailDO.setId(id);
            StBeanUtils.makeModifierField(stCVipcomMailDO, session.getUser());
            //校验收件人和抄送人
            if (null != stCVipcomMailDO.getReceiveMail()){
                boolean flag = this.checkCol(stCVipcomMailDO.getReceiveMail());
                if (flag){
                    return ValueHolderUtils.getFailValueHolder("收件人字段存在非法字符，请检查！");
                }
            }
            if (null != stCVipcomMailDO.getCopyMail()){
                boolean flag = this.checkCol(stCVipcomMailDO.getCopyMail());
                if (flag){
                    return ValueHolderUtils.getFailValueHolder("抄送人字段存在非法字符，请检查！");
                }
            }

            //校验通知简称不能重复
            if(stCVipcomMailDO.getEname()!=null){
                List<StCVipcomMailDO> stCVipcomMailDOList = stCVipcomMailMapper.selectByEnameId(stCVipcomMailDO.getEname(), id);
                if(stCVipcomMailDOList != null && stCVipcomMailDOList.size()>0){
                    return ValueHolderUtils.getFailValueHolder("通知简称重复，不能更新");
                }
            }
            try {
                if (stCVipcomMailMapper.updateById(stCVipcomMailDO) > 0) {
                    return ValueHolderUtils.getSuccessValueHolder(id, StConstant.TAB_ST_C_VIPCOM_MAIL);
                } else {
                    return ValueHolderUtils.getFailValueHolder("保存失败");
                }
            } catch (Exception ex) {
                log.error(LogUtil.format("ResolveRuleSaveService.updateResolveRule Error{}"),
                        Throwables.getStackTraceAsString(ex));
                return ValueHolderUtils.getFailValueHolder("保存异常");
            }
        }
        ValueHolder valueHolder = ValueHolderUtils.getSuccessValueHolder(id, StConstant.TAB_ST_C_VIPCOM_MAIL);
        return valueHolder;
    }

    /**
     * @param session
     * @param fixColumn
     * @return
     * @Author: 朱宇军
     * @Date 2019/3/7
     */

    private ValueHolder addVipcomMail(QuerySession session, JSONObject fixColumn) {
        JSONObject jsonObject = fixColumn.getJSONObject(StConstant.TAB_ST_C_VIPCOM_MAIL);
        String string = jsonObject.toString();
        if (StringUtils.isNotEmpty(string)) {
            StCVipcomMailDO stCVipcomMailDO = JsonUtils.jsonParseClass(jsonObject, StCVipcomMailDO.class);
            stCVipcomMailDO.setId(ModelUtil.getSequence(StConstant.TAB_ST_C_VIPCOM_MAIL));
            StBeanUtils.makeCreateField(stCVipcomMailDO, session.getUser());
            //校验收件人和抄送人
            if (null != stCVipcomMailDO.getReceiveMail()){
                boolean flag = this.checkCol(stCVipcomMailDO.getReceiveMail());
                if (flag){
                    return ValueHolderUtils.getFailValueHolder("收件人字段存在非法字符，请检查！");
                }
            }
            if (null != stCVipcomMailDO.getCopyMail()){
                boolean flag = this.checkCol(stCVipcomMailDO.getCopyMail());
                if (flag){
                    return ValueHolderUtils.getFailValueHolder("抄送人字段存在非法字符，请检查！");
                }
            }

            //校验通知简称不能重复
            if(stCVipcomMailDO.getEname() != null){
                List<StCVipcomMailDO> stCVipcomMailDOList = stCVipcomMailMapper.selectByEname(stCVipcomMailDO.getEname());
                if(stCVipcomMailDOList != null && stCVipcomMailDOList.size() == 1){
                    return ValueHolderUtils.getFailValueHolder("通知简称重复");
                }
            }
            try {
                int insertResult = stCVipcomMailMapper.insert(stCVipcomMailDO);
                if (insertResult > 0) {
                    return ValueHolderUtils.getSuccessValueHolder(stCVipcomMailDO.getId(), StConstant.TAB_ST_C_VIPCOM_MAIL);
                } else {
                    return ValueHolderUtils.getFailValueHolder("保存失败");
                }
            } catch (Exception ex) {
                log.error(LogUtil.format("VipcomMailSaveService.addVipcomMail Error：{}"),
                        Throwables.getStackTraceAsString(ex));
                return ValueHolderUtils.getFailValueHolder("保存异常");
            }
        }
        throw new NDSException("当前表ST_C_VIPCOM_MAIL不存在！");
    }
    /**
     * <AUTHOR>
     * @Description 验证字符串是否有特殊字符（除中文逗号，句号，@符号外的特殊字符）
     * @Date  2019-6-18
     * @Param [mailStr]
     * @return boolean
    **/
    private boolean checkCol(String mailStr){
        String regEx ="[`~!#$%^&*()+=|{}':;'\\\\[\\\\]<>/?~！#￥%……&*（）——+|{}【】‘；：”“’。，、？]";
        Pattern pattern = Pattern.compile(regEx);
        return pattern.matcher(mailStr).find();
    }
}
