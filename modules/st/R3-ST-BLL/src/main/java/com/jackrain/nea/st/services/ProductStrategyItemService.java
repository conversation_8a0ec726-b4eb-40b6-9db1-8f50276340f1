package com.jackrain.nea.st.services;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.st.common.StConstant;
import com.jackrain.nea.st.mapper.StCProductStrategyItemMapper;
import com.jackrain.nea.st.model.request.ProductStrategyItemRequest;
import com.jackrain.nea.st.model.table.StCProductStrategyItemDO;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.utility.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;

/**
 * 商品特殊比例设置-明细service
 *
 * <AUTHOR>
 * @since 2020-08-24
 * create at : 2020-08-24 14:10
 */
@Slf4j
@Component
public class ProductStrategyItemService extends ServiceImpl<StCProductStrategyItemMapper, StCProductStrategyItemDO> {

    @Autowired
    private StCProductStrategyItemMapper itemMapper;

    /**
     * 根据策略ID查询明细
     *
     * @param strategyId 策略ID
     * @return 明细列表
     */
    public List<StCProductStrategyItemDO> queryItemListByStrategyId(Long strategyId) {
        LambdaQueryWrapper<StCProductStrategyItemDO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(StCProductStrategyItemDO::getStCProductStrategyId, strategyId);
        return itemMapper.selectList(wrapper);
    }

    /**
     * <AUTHOR>
     * @Description 查询有效店铺商品特殊设置明细
     * @Date 16:07 2020/12/18
     * @param request
     * @return com.jackrain.nea.sys.domain.ValueHolderV14<com.jackrain.nea.st.model.table.StCProductStrategyItemDO>
     **/
    public ValueHolderV14<List<StCProductStrategyItemDO>> queryProductStrategyItemValid(ProductStrategyItemRequest request) {
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("【查询有效店铺商品特殊设置明细入参】{}"), JSONObject.toJSON(request));
        }
        Date now = new Date();
        ValueHolderV14<List<StCProductStrategyItemDO>> valueHolder14 = new ValueHolderV14();
        valueHolder14.setCode(ResultCode.SUCCESS);
        valueHolder14.setMessage("成功");
        String errorMes = checkProductStrategyItemRequest(request);
        if (StringUtils.isNotEmpty(errorMes)) {
            valueHolder14.setCode(ResultCode.FAIL);
            valueHolder14.setMessage(errorMes);
            return valueHolder14;
        }
        LambdaQueryWrapper<StCProductStrategyItemDO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(StCProductStrategyItemDO::getCpCShopId, request.getCpCShopId());
        wrapper.eq(StCProductStrategyItemDO::getStatus, StConstant.CON_BILL_STATUS_02);
        wrapper.le(StCProductStrategyItemDO::getBeginTime, request.getExpireTime());
        wrapper.ge(StCProductStrategyItemDO::getEndTime, request.getExpireTime());
        wrapper.and(queryWrapper -> queryWrapper
                .eq(StCProductStrategyItemDO::getPsCSkuId, request.getPsCSkuId()).or()
                .eq(StCProductStrategyItemDO::getPtSkuId, request.getPtSkuId()).or()
                .eq(StCProductStrategyItemDO::getPtProId, request.getPtProId())
        );
        List<StCProductStrategyItemDO> itemList = itemMapper.selectList(wrapper);
        if (CollectionUtils.isNotEmpty(itemList)) {
            itemList = itemList.stream().filter(obj -> {
                // 1. 中台sku不一致的过滤
                if(obj.getPsCSkuId() != null && !obj.getPsCSkuId().equals(request.getPsCSkuId())){
                    return false;
                }
                //2. 存在平台skuid，但是和渠道库存缓存池中不一致的过滤
                if (StringUtils.isNotEmpty(obj.getPtSkuId()) && !StringUtils.equals(obj.getPtSkuId(), request.getPtSkuId())) {
                    return false;
                }
                //3.平台skuid为空且平台商品id有值，但是和渠道库存缓存池中不一致的过滤
                if (StringUtils.isNotEmpty(obj.getPtProId()) && !StringUtils.equals(obj.getPtProId(), request.getPtProId())) {
                    return false;
                }
                return true;
            }).collect(Collectors.toList());
        }
        if (CollectionUtils.isNotEmpty(itemList)) {
            itemList.sort(Comparator
                    // 主表时间降序排序
                    .comparing(StCProductStrategyItemDO::getMainCreationdate).reversed()
                    // 粒度越细排在前面
                    .thenComparing((k1,k2) -> {
                        int a = 0,b = 0;
                        if (StringUtils.isNotEmpty(k1.getPtSkuId())) {
                            a = 3;
                        } else if (StringUtils.isNotEmpty(k1.getPtProId())) {
                            a = 2;
                        } else if (k1.getPsCSkuId() != null) {
                            a = 1;
                        }
                        if (StringUtils.isNotEmpty(k2.getPtSkuId())) {
                            b = 3;
                        } else if (StringUtils.isNotEmpty(k2.getPtProId())) {
                            b = 2;
                        } else if (k2.getPsCSkuId() != null) {
                            b = 1;
                        }
                        return b - a;
                    }));
            // 根据cpCOrgChannelId去重，取第一个
            itemList = itemList.stream().filter(distinctByKey(StCProductStrategyItemDO::getCpCOrgChannelId)).collect(Collectors.toList());
            valueHolder14.setData(itemList);
        }
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("【查询有效店铺商品特殊设置明细出参】{}"), JSONObject.toJSON(itemList));
        }
        return valueHolder14;
    }

    /**
     * <AUTHOR>
     * @Description 入参校验
     * @Date 17:21 2020/12/18
     * @param request
     * @return java.lang.String
     **/
    private String checkProductStrategyItemRequest(ProductStrategyItemRequest request) {
        if (request.getCpCShopId() == null) {
            return "店铺不允许为空";
        }
        if (request.getExpireTime() == null) {
            return "当前时间不允许为空";
        }
        if (request.getPsCSkuId() == null) {
            return "中台sku不允许为空";
        }
        if (StringUtils.isEmpty(request.getPtProId())) {
            return "平台商品不允许为空";
        }
        if (StringUtils.isEmpty(request.getPtSkuId())) {
            return "平台sku不允许为空";
        }
        return null;
    }
    private static <T> Predicate<T> distinctByKey(Function<? super T, Object> keyExtractor) {
        Map<Object, Boolean> seen = new ConcurrentHashMap<>();
        return t -> seen.putIfAbsent(keyExtractor.apply(t), Boolean.TRUE) == null;
    }
}
