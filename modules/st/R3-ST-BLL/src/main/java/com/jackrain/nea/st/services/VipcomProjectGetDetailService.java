package com.jackrain.nea.st.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.st.mapper.StCVipcomProjectMapper;
import com.jackrain.nea.st.mapper.StCVipcomProjectPickorderItemMapper;
import com.jackrain.nea.st.mapper.StCVipcomProjectWhEntryItemMapper;
import com.jackrain.nea.st.model.table.StCVipcomProjectDO;
import com.jackrain.nea.st.model.table.StCVipcomProjectPickorderItem;
import com.jackrain.nea.st.model.table.StCVipcomProjectWhEntryItem;
import com.jackrain.nea.st.utils.JsonUtils;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.List;

/**
 * @Descroption 获取档期日程规划详情
 * <AUTHOR>
 * @Date 2021/05/27 11:20
 */
@Component
@Slf4j
@Transactional
public class VipcomProjectGetDetailService extends CommandAdapter {
    @Autowired
    private StCVipcomProjectMapper projectMapper;
    @Autowired
    private StCVipcomProjectPickorderItemMapper stCVipcomProjectPickorderItemMapper;
    @Autowired
    private StCVipcomProjectWhEntryItemMapper stCVipcomProjectWhEntryItemMapper;

    @Override
    public ValueHolder execute(QuerySession querySession) throws NDSException {
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("Start VipcomProjectDelService.QuerySession=") + querySession.toString() + ";");
        }
        DefaultWebEvent event = querySession.getEvent();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(
                event.getParameterValue("param"), "yyyy-MM-dd HH:mm:ss"),
                Feature.OrderedField);
        Long masterId = param.getLong("ID");
        ValueHolder valueHolder = new ValueHolder();
            //1. 查询主表
            StCVipcomProjectDO project = projectMapper.selectById(masterId);
            if (project != null) {
                JSONObject resultJson = new JSONObject();
                String projectJson = JsonUtils.toJsonString(project);
                resultJson.put("VIPCOM_PROJECT_LIST",projectJson);
                //2.查询拣货单明细
                List<StCVipcomProjectPickorderItem> pickorderItemList = stCVipcomProjectPickorderItemMapper.queryPickorderItemByMasterId(masterId);
                if(CollectionUtils.isNotEmpty(pickorderItemList)){
                    String pickorderItemJson = JsonUtils.toJsonString(pickorderItemList);
                    resultJson.put("VIPCOM_PROJECT_PICK_LIST",pickorderItemJson);
                }
                //3.查询入库单明细
                List<StCVipcomProjectWhEntryItem> warehouseEntryItemList = stCVipcomProjectWhEntryItemMapper.queryPickorderItemByMasterId(masterId);
                if(CollectionUtils.isNotEmpty(warehouseEntryItemList)){
                    String warehouseEntryItemJson = JsonUtils.toJsonString(warehouseEntryItemList);
                    resultJson.put("VIPCOM_PROJECT_STORE_IN_LIST",warehouseEntryItemJson);
                }
                HashMap<String, Object> map = new HashMap<>();
                map.put("code", 0);
                map.put("message", "");
                map.put("data", resultJson);
                valueHolder.setData(map);
                return valueHolder;
            }else{
                throw new NDSException("当前记录已不存在！");
            }
    }


}
