package com.jackrain.nea.st.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.google.common.base.Throwables;
import com.jackrain.nea.oc.oms.model.resources.OmsRedisKeyResources;
import com.jackrain.nea.redis.util.RedisOpsUtil;
import com.jackrain.nea.st.mapper.StCMergeOrderMapper;
import com.jackrain.nea.st.model.table.StCMergeOrderDO;
import com.jackrain.nea.st.utils.StBeanUtils;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.HashMap;

@Component
@Slf4j
@Transactional
public class MergeOrderVoidService extends CommandAdapter {

    @Autowired
    private RedisOpsUtil redisUtil;

    @Autowired
    private StCMergeOrderMapper stCMergeOrderMapper;

    public ValueHolder voidMergeOrder(QuerySession session) {
        DefaultWebEvent event = session.getEvent();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(
                event.getParameterValue("param"), "yyyy-MM-dd HH:mm:ss", SerializerFeature.WriteMapNullValue),
                Feature.OrderedField);
        if (log.isInfoEnabled()) {
            log.debug(LogUtil.format("{}:请求JSON ") + param.toString());
        }
        //生成作废Json数组
        JSONArray auditArray = StBeanUtils.makeAuditJsonArray(param);
        HashMap<Long, Object> errorMap = new HashMap<Long, Object>();
        ValueHolder valueHolder = new ValueHolder();

        for (int i = 0; i < auditArray.size(); i++) {
            Long id = Long.valueOf(auditArray.get(i).toString());
            try {
                //作废验证
                voidCheck(id);
                StCMergeOrderDO stCMergeOrderDO = new StCMergeOrderDO();
                stCMergeOrderDO.setId(id);
                stCMergeOrderDO.setIsactive("N");//作废
                makeVoidField(stCMergeOrderDO, session.getUser());
                //更新
                int count = stCMergeOrderMapper.updateById(stCMergeOrderDO);
                if (count < 0) {
                    throw new Exception();
                }
            } catch (Exception e) {
                errorMap.put(id, e.getMessage());
            }
        }
        //店铺清除rediskey
        delRedisKey();
        //失败成功信息
        StBeanUtils.getExcuteValueHolder(auditArray.size(), errorMap);
        return valueHolder;
    }

    /**
     * @return com.alibaba.fastjson.JSONObject
     * <AUTHOR>
     * @Description 作废验证
     * @Date 2019/3/12
     * @Param [objid]
     **/
    private JSONObject voidCheck(Long objid) throws Exception {

        //记录不存在
        StCMergeOrderDO stCMergeOrderDO = stCMergeOrderMapper.selectById(objid);
        if (stCMergeOrderDO == null) {
            JSONObject errJo = new JSONObject();
            errJo.put("objid", objid);
            errJo.put("message", "当前记录已不存在！");
            log.debug(LogUtil.format("当前记录已不存在！") + objid);
            throw new Exception("当前记录已不存在！");

        }

        //已作废
        StCMergeOrderDO stCMergeOrderDO1 = stCMergeOrderMapper.selectByIdAndIsactive(objid, "N");
        if (stCMergeOrderDO1 != null) {
            JSONObject errJo = new JSONObject();
            errJo.put("objid", objid);
            errJo.put("message", "当前记录已作废，不允许重复作废！");
            log.debug(LogUtil.format("当前记录已作废，不允许重复作废！objid=", objid));
            throw new Exception("当前记录已作废，不允许重复作废！");
        }
        return null;
    }

    /**
     * @return void
     * <AUTHOR>
     * @Description 作废人bean组装
     * @Date 2019/3/12
     * @Param [stCMergeOrderDO, user]
     **/
    public static void makeVoidField(StCMergeOrderDO stCMergeOrderDO, User user) {
        stCMergeOrderDO.setDelid(Long.valueOf(user.getId()));//作废人ID
        stCMergeOrderDO.setDelname(user.getName());
        stCMergeOrderDO.setDelename(user.getEname());//作废人账号
        stCMergeOrderDO.setDelTime(new Date());
        stCMergeOrderDO.setModifierid(Long.valueOf(user.getId()));//修改人Id
        stCMergeOrderDO.setModifiername(user.getName());
        stCMergeOrderDO.setModifierename(user.getEname());
        stCMergeOrderDO.setModifieddate(new Date());
    }

    private void delRedisKey() {
        try {
            String redisKey = OmsRedisKeyResources.buildAutoMergeAllListRedisKey();
            if (redisUtil.strRedisTemplate.hasKey(redisKey)) {
                redisUtil.strRedisTemplate.delete(redisKey);
            }
        } catch (Exception e) {
            log.error(LogUtil.format("redis保存自动合单策略异常{}"), Throwables.getStackTraceAsString(e));
        }
    }
}
