package com.jackrain.nea.st.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.cpext.model.table.CpShop;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.st.common.StConstant;
import com.jackrain.nea.st.mapper.StCAutoInvoiceMapper;
import com.jackrain.nea.st.mapper.StCAutoInvoiceShopMapper;
import com.jackrain.nea.st.model.table.StCAutoInvoiceDO;
import com.jackrain.nea.st.model.table.StCAutoInvoiceShopDO;
import com.jackrain.nea.st.rpc.RpcCpService;
import com.jackrain.nea.st.utils.JsonUtils;
import com.jackrain.nea.st.utils.StBeanUtils;
import com.jackrain.nea.st.utils.ValueHolderUtils;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * @Descroption 自动开票策略新增保存
 * <AUTHOR>
 * @Date 2019/9/2 10:55
 */
@Component
@Slf4j
@Transactional
public class AutoInvoiceSaveService extends CommandAdapter {
    @Autowired
    private StCAutoInvoiceMapper mapper;
    @Autowired
    private StCAutoInvoiceShopMapper shopMapper;
    @Autowired
    private RpcCpService rpcCpService;

    public ValueHolder execute(QuerySession session) {
        //获取传入参数
        DefaultWebEvent event = session.getEvent();
        JSONObject param = JsonUtils.filterNull((JSONObject) event.getParameterValue("param"), null);

        Long id = param.getLong("objid");
        JSONObject fixColumn = param.getJSONObject("fixcolumn");
        //json转换成对象
        String autoInvoice = fixColumn.getString(StConstant.TAB_ST_C_AUTO_INVOICE);
        //新增或变更
        if (autoInvoice != null && id != null) {
            StCAutoInvoiceDO stCAutoInvoice = JSON.parseObject(autoInvoice, StCAutoInvoiceDO.class);
            if (id != -1) {
                return updateAutoInvoice(session, id, stCAutoInvoice);
            } else {
                return addAutoInvoice(session, stCAutoInvoice);
            }
        }
        throw new NDSException("传入数据异常！");
    }

    /**
     * @Descroption 新增
     * @param session
     * @param stCAutoInvoice
     * @return com.jackrain.nea.util.ValueHolder
     */
    private ValueHolder addAutoInvoice(QuerySession session, StCAutoInvoiceDO stCAutoInvoice) {
        //主表数据业务更新校验
        ValueHolder holder = checkAutoInvoice(stCAutoInvoice);
        if (holder != null) {
            return holder;
        }
        //主表id及创建修改时间赋值
        stCAutoInvoice.setId(ModelUtil.getSequence(StConstant.TAB_ST_C_AUTO_INVOICE));
        stCAutoInvoice.setSameInvoiceInfoWord("InvoiceCompany,InvoiceType,HeaderName,ReceiverMobile,ReceiverAddress");
        stCAutoInvoice.setSameSourceCodeWord("SourceCode");
        if (!"Y".equals(stCAutoInvoice.getIsAutoMergeInvoice())) {
            stCAutoInvoice.setIsUnauditInvoiceEstatus("Y");
            stCAutoInvoice.setIsSameInvoiceShop("Y");
            stCAutoInvoice.setIsSameInvoiceInfo("Y");
            stCAutoInvoice.setIsSameSourceCode("Y");
        }
        //主表最后信息修改
        StBeanUtils.makeCreateField(stCAutoInvoice, session.getUser());
        //3.主表数据保存
        int insertResult = mapper.insert(stCAutoInvoice);
        if (insertResult <= 0) {
            return ValueHolderUtils.getFailValueHolder("保存失败");
        }
        //插入商品明细信息
        generateShop(stCAutoInvoice,true, session.getUser());
        return ValueHolderUtils.getSuccessValueHolder(stCAutoInvoice.getId(), StConstant.TAB_ST_C_AUTO_INVOICE);
    }

    /**
     * @Descroption 更新
     * @param session
     * @param id
     * @param stCAutoInvoice
     * @return com.jackrain.nea.util.ValueHolder
     */
    private ValueHolder updateAutoInvoice(QuerySession session, Long id, StCAutoInvoiceDO stCAutoInvoice)  {
        //主表最后修改信息字段变更
        stCAutoInvoice.setId(id);
        ValueHolder holder = checkAutoInvoice(stCAutoInvoice);
        if (holder != null) {
            return holder;
        }
        StBeanUtils.makeModifierField(stCAutoInvoice, session.getUser());
        //更新主表信息
        if (mapper.updateById(stCAutoInvoice) <= 0) {
            return ValueHolderUtils.getFailValueHolder("更新失败");
        }
        //插入商品明细信息(存在店铺信息修改的同步更改明细表)
        if(stCAutoInvoice != null && StringUtils.isNotEmpty(stCAutoInvoice.getCpCShopId())){
            generateShop(stCAutoInvoice,false, session.getUser());
        }
        return ValueHolderUtils.getSuccessValueHolder(id, StConstant.TAB_ST_C_AUTO_INVOICE);
    }

    private ValueHolder checkAutoInvoice(StCAutoInvoiceDO stCAutoInvoice) {

        StCAutoInvoiceDO stCAutoInvoiceOld = null;
        if (stCAutoInvoice.getId() != null && stCAutoInvoice.getId() > 0) {
            stCAutoInvoiceOld = mapper.selectById(stCAutoInvoice.getId());
            if (stCAutoInvoiceOld == null) {
                return ValueHolderUtils.getFailValueHolder("当前记录已不存在！");
            }
        }

        if (StringUtils.isNotEmpty(stCAutoInvoice.getCpCShopId())) {
            List<StCAutoInvoiceShopDO> shopList = shopMapper.selectShopListByShopIds(stCAutoInvoice.getCpCShopId());
            for (StCAutoInvoiceShopDO shop : shopList) {
                if (!shop.getStCAutoInvoiceNoticeId().equals(stCAutoInvoice.getId())) {
                    return ValueHolderUtils.getFailValueHolder("店铺已经存在其他方案中，不允许重复设置自动开票策略！");
                }
            }
        }

        String limitAmtFlg = stCAutoInvoice.getIsLimitMaximumAmount();
        if (limitAmtFlg == null && stCAutoInvoiceOld != null) {
            limitAmtFlg = stCAutoInvoice.getIsLimitMaximumAmount();
        }
        if (StConstant.ISACTIVE_Y.equals(limitAmtFlg) && stCAutoInvoice.getMaximumAmount() == null) {
            return ValueHolderUtils.getFailValueHolder("开票最大金额不可以为空！");
        }
        return null;
    }

    private void generateShop(StCAutoInvoiceDO stCAutoInvoice, boolean addFlag, User user){
        String cpCShopIdStr = stCAutoInvoice.getCpCShopId();
        String[] cpCShopIds = cpCShopIdStr.split(",");
        //删除后批量新增
        if(!addFlag){
            shopMapper.delShopByMainId(stCAutoInvoice.getId());
        }
        for(String shopId : cpCShopIds){
            StCAutoInvoiceShopDO autoInvoiceShop = new StCAutoInvoiceShopDO();
            autoInvoiceShop.setId(ModelUtil.getSequence(StConstant.TAB_ST_C_AUTO_INVOICE_SHOP));
            autoInvoiceShop.setCpCShopId(Long.valueOf(shopId));
            autoInvoiceShop.setStCAutoInvoiceNoticeId(stCAutoInvoice.getId());
            CpShop cpShop = rpcCpService.selectCpCShopById(autoInvoiceShop.getCpCShopId());
            if (cpShop != null) {
                autoInvoiceShop.setCpCShopEcode(cpShop.getEcode());
                autoInvoiceShop.setCpCShopTitle(cpShop.getCpCShopTitle());
            }
            StBeanUtils.makeCreateField(autoInvoiceShop, user);
            shopMapper.insert(autoInvoiceShop);
        }
    }
}
