package com.jackrain.nea.st.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.google.common.base.Throwables;
import com.jackrain.nea.cpext.model.table.CpCPhyWarehouse;
import com.jackrain.nea.cpext.model.table.CpLogistics;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.st.mapper.StCWarehouseLogisticsItemMapper;
import com.jackrain.nea.st.mapper.StCWarehouseLogisticsMapper;
import com.jackrain.nea.st.mapper.StCWarehouseLogisticsRankMapper;
import com.jackrain.nea.st.model.request.WarehouseLogisticsRequest;
import com.jackrain.nea.st.model.result.LogisticsRankResult;
import com.jackrain.nea.st.model.result.RegionTreeResult;
import com.jackrain.nea.st.model.result.WarehouseLogisticsRankResult;
import com.jackrain.nea.st.model.table.StCWarehouseLogisticsDO;
import com.jackrain.nea.st.model.table.StCWarehouseLogisticsItemDO;
import com.jackrain.nea.st.model.table.StCWarehouseLogisticsRankDO;
import com.jackrain.nea.st.rpc.RpcCpService;
import com.jackrain.nea.st.utils.JsonUtils;
import com.jackrain.nea.st.utils.RegionTreeUtils;
import com.jackrain.nea.st.utils.StBeanUtils;
import com.jackrain.nea.st.utils.ValueHolderUtils;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.assertj.core.util.Lists;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.lang.reflect.Method;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Bll层-新增保存业务逻辑
 *
 * <AUTHOR> 黄超
 * @since : 2019-08-15
 * create at : 2019-08-15 10:00
 */
@Component
@Slf4j
@Transactional
public class WarehouseLogisticsSaveService extends CommandAdapter {
    @Autowired
    private StCWarehouseLogisticsMapper stCMainMapper;
    @Autowired
    private StCWarehouseLogisticsItemMapper stCItemMapper;
    @Autowired
    private StCWarehouseLogisticsRankMapper stCRankMapper;
    @Autowired
    private RpcCpService rpcCpService;
    @Autowired
    private RegionTreeQueryService regionTreeQueryService;

    private String strTableMain = "ST_C_WAREHOUSE_LOGISTICS";
    private String strTableList = "ST_C_WAREHOUSE_LOGISTICS_ITEM";
    private String strTableRank = "ST_C_WAREHOUSE_LOGISTICS_RANK";

    /**
     * 新增和修改
     *
     * @param querySession 参数封装
     * @return 返回状态
     * @throws NDSException 异常信息
     */
    @Override
    public ValueHolder execute(QuerySession querySession) throws NDSException {
        ValueHolder valueHolder = new ValueHolder();
        DefaultWebEvent event = querySession.getEvent();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(
                event.getParameterValue("param"), "yyyy-MM-dd HH:mm:ss"),
                Feature.OrderedField);

        if (param != null) {
            Long id = param.getLong("objid");
            JSONObject fixColumn = param.getJSONObject("fixcolumn");
            if (fixColumn != null) {
                WarehouseLogisticsRequest request = JsonUtils.jsonParseClass(fixColumn, WarehouseLogisticsRequest.class);

                if (request == null) {
                    throw new NDSException("数据异常！");
                }
                if (id != null && id < 0) {
                    valueHolder = saveFunction(request, valueHolder, querySession, id);
                } else {
                    valueHolder = updateFunction(request, valueHolder, querySession, id);
                }
            }
        } else {
            throw new NDSException("参数为空！");
        }
        return valueHolder;
    }

    /**
     * 新增操作
     *
     * @param request      数据
     * @param holder       响应数据
     * @param querySession 封装数据
     * @param objid        id
     * @return 返回状态
     */
    private ValueHolder saveFunction(WarehouseLogisticsRequest request,
                                     ValueHolder holder,
                                     QuerySession querySession, Long objid) {
        StCWarehouseLogisticsDO stCWarehouseLogisticsDO = request.getWarehouseLogistics();
        if (null != stCWarehouseLogisticsDO) {
            //状态数据检查
            if (!checkStatus(stCWarehouseLogisticsDO, objid, holder)) {
                return holder;
            }
            // 取得仓库信息
            if (stCWarehouseLogisticsDO.getCpCPhyWarehouseId() != null) {
                CpCPhyWarehouse cpCPhyWarehouse = rpcCpService.getCpCPhyWahouseDoById(stCWarehouseLogisticsDO.getCpCPhyWarehouseId());
                if (cpCPhyWarehouse != null) {
                    stCWarehouseLogisticsDO.setCpCPhyWarehouseEcode(cpCPhyWarehouse.getEcode());
                    stCWarehouseLogisticsDO.setCpCPhyWarehouseEname(cpCPhyWarehouse.getEname());
                }
            }
            stCWarehouseLogisticsDO.setId(ModelUtil.getSequence(strTableMain));
            StBeanUtils.makeCreateField(stCWarehouseLogisticsDO, querySession.getUser());

            if (stCMainMapper.insert(stCWarehouseLogisticsDO) <= 0) {
                holder = ValueHolderUtils.getFailValueHolder("保存失败！");
                return holder;
            }
        }

        //更新子表信息
        List<StCWarehouseLogisticsItemDO> stCWarehouseLogisticsItemDOList = request.getItemList();
        if (stCWarehouseLogisticsItemDOList != null && null != stCWarehouseLogisticsDO) {
            //检查行明细
            if (!checkList(stCWarehouseLogisticsItemDOList, objid, holder)) {
                return holder;
            }
            if (!saveItemList(querySession, stCWarehouseLogisticsDO.getId(), stCWarehouseLogisticsItemDOList, holder)) {
                return holder;
            }
        }
        //仓库物流优先级明细
        List<WarehouseLogisticsRankResult> warehouseLogisticsRankResultList = request.getRankResultList();
        if (warehouseLogisticsRankResultList != null
                && null != stCWarehouseLogisticsDO
                && !saveRankList(querySession, stCWarehouseLogisticsDO.getId(), warehouseLogisticsRankResultList, holder)) {
            return holder;
        }
        if (null != stCWarehouseLogisticsDO) {
            objid = stCWarehouseLogisticsDO.getId();
        }
        holder = ValueHolderUtils.getSuccessValueHolder(objid, strTableMain);
        return holder;
    }

    /**
     * 更新操作
     *
     * @param request      数据
     * @param holder       响应数据
     * @param querySession 封装数据
     * @param objid        主表id
     * @return 返回状态
     */
    private ValueHolder updateFunction(WarehouseLogisticsRequest request,
                                       ValueHolder holder,
                                       QuerySession querySession,
                                       Long objid) {
        //主表更新，objid就是主表ID
        StCWarehouseLogisticsDO stCWarehouseLogisticsDO = request.getWarehouseLogistics();
        if (stCWarehouseLogisticsDO != null) {
            if (!checkStatus(stCWarehouseLogisticsDO, objid, holder)) {
                return holder;
            }
            stCWarehouseLogisticsDO.setId(objid);
            StBeanUtils.makeModifierField(stCWarehouseLogisticsDO, querySession.getUser());

            if (stCMainMapper.updateById(stCWarehouseLogisticsDO) < 0) {
                holder = ValueHolderUtils.getFailValueHolder("保存失败！");
                return holder;
            }
        }

        //更新子表信息
        List<StCWarehouseLogisticsItemDO> stCWarehouseLogisticsItemDOList = request.getItemList();
        if (stCWarehouseLogisticsItemDOList != null) {
            //检查行明细
            if (!checkList(stCWarehouseLogisticsItemDOList, objid, holder)) {
                return holder;
            }
            if (!saveItemList(querySession, objid, stCWarehouseLogisticsItemDOList, holder)) {
                return holder;
            }
        }
        //仓库物流优先级明细
        List<WarehouseLogisticsRankResult> warehouseLogisticsRankResultList = request.getRankResultList();
        if (warehouseLogisticsRankResultList != null) {
            if (!saveRankList(querySession, objid, warehouseLogisticsRankResultList, holder)) {
                return holder;
            }
        }
        holder = ValueHolderUtils.getSuccessValueHolder(objid, strTableMain);
        return holder;
    }

    private boolean saveItemList(QuerySession querySession, Long objid,
                                 List<StCWarehouseLogisticsItemDO> stCWarehouseLogisticsItemDOList, ValueHolder valueHolder) {
        for (StCWarehouseLogisticsItemDO stCWarehouseLogisticsItemDO : stCWarehouseLogisticsItemDOList) {
            Long id = stCWarehouseLogisticsItemDO.getId();
            // 取得物流公司信息
            if (stCWarehouseLogisticsItemDO.getCpCLogisticsId() != null) {
                CpLogistics cpLogistics = rpcCpService.queryLogisticsById(stCWarehouseLogisticsItemDO.getCpCLogisticsId());
                if (cpLogistics != null) {
                    stCWarehouseLogisticsItemDO.setCpCLogisticsEcode(cpLogistics.getEcode());
                    stCWarehouseLogisticsItemDO.setCpCLogisticsEname(cpLogistics.getEname());
                }
            }
            if (id < 0) {
                stCWarehouseLogisticsItemDO.setId(ModelUtil.getSequence(strTableList));
                stCWarehouseLogisticsItemDO.setStCWarehouseLogisticsId(objid);
                StBeanUtils.makeCreateField(stCWarehouseLogisticsItemDO, querySession.getUser());
                if (stCItemMapper.insert(stCWarehouseLogisticsItemDO) < 0) {
                    valueHolder.put("code", -1);
                    valueHolder.put("message", "保存失败！");
                    return false;
                }
            } else {
                //修改原有的行信息 id>0
                if (stCItemMapper.selectById(id) != null) {
                    StBeanUtils.makeModifierField(stCWarehouseLogisticsItemDO, querySession.getUser());
                    if (stCItemMapper.updateById(stCWarehouseLogisticsItemDO) < 0) {
                        valueHolder.put("code", -1);
                        valueHolder.put("message", "保存失败！");
                        return false;
                    }
                } else {
                    valueHolder.put("code", -1);
                    valueHolder.put("message", "修改的行明细已被删除！");
                    return false;
                }
            }
        }
        return true;
    }

    private boolean saveRankList(QuerySession querySession, Long objid,
                                 List<WarehouseLogisticsRankResult> warehouseLogisticsRankResultList, ValueHolder valueHolder) {
        //获取物流公司信息
        List<StCWarehouseLogisticsItemDO> itemList = stCItemMapper.selectList(new QueryWrapper<StCWarehouseLogisticsItemDO>()
                .lambda().eq(StCWarehouseLogisticsItemDO::getStCWarehouseLogisticsId, objid));
        if (CollectionUtils.isNotEmpty(itemList)) {
            itemList = itemList.stream().sorted(Comparator.comparing(StCWarehouseLogisticsItemDO::getId))
                    .collect(Collectors.toList());
        }
        JSONObject obj = new JSONObject();
        obj.put("regiontype", "PROV,CITY");
        ValueHolderV14<List<RegionTreeResult>> treeVh = regionTreeQueryService.queryRegionTree(obj);

        List<WarehouseLogisticsRankResult> rankResultAllList = Lists.newArrayList();
        Map<Long, StCWarehouseLogisticsRankDO> rankOldMap = new HashMap<>();
        for (WarehouseLogisticsRankResult rankResult : warehouseLogisticsRankResultList) {
            if (treeVh.isOK() && rankResult.getCitylevel() != null && "PROV".equals(rankResult.getCitylevel())) {
                List<StCWarehouseLogisticsRankDO> rankOldList = stCRankMapper.selectList(new QueryWrapper<StCWarehouseLogisticsRankDO>()
                        .lambda().eq(StCWarehouseLogisticsRankDO::getStCWarehouseLogisticsId, objid)
                        .eq(StCWarehouseLogisticsRankDO::getCpCRegionProvinceId, rankResult.getCpCRegionProvinceId()));
                if (CollectionUtils.isNotEmpty(rankOldList)) {
                    rankOldMap.putAll(rankOldList.stream().collect(Collectors.toMap(StCWarehouseLogisticsRankDO::getCpCRegionCityId, Function.identity())));
                }

                List<RegionTreeResult> childrenList = RegionTreeUtils.getTreeChildrenResult(treeVh.getData(), rankResult.getCpCRegionProvinceId());
                if (CollectionUtils.isNotEmpty(childrenList)) {
                    for (RegionTreeResult treeChildren : childrenList) {
                        WarehouseLogisticsRankResult childrenRank = new WarehouseLogisticsRankResult();
                        BeanUtils.copyProperties(rankResult, childrenRank);

                        if (rankOldMap.containsKey(treeChildren.getId())) {
                            childrenRank.setId(rankOldMap.get(treeChildren.getId()).getId());
                        } else {
                            childrenRank.setId(-1L);
                        }
                        childrenRank.setCpCRegionCityId(treeChildren.getId());
                        childrenRank.setCpCRegionCityEcode(treeChildren.getEcode());
                        childrenRank.setCpCRegionCityEname(treeChildren.getTitle());

                        rankResultAllList.add(childrenRank);
                    }
                }
            } else {
                rankResultAllList.add(rankResult);
            }
        }

        for (WarehouseLogisticsRankResult rankResult : rankResultAllList) {

            Long id = rankResult.getId();
            StCWarehouseLogisticsRankDO rank = new StCWarehouseLogisticsRankDO();
            BeanUtils.copyProperties(rankResult, rank);

            Map<Long, LogisticsRankResult> logisticsRankOldMap = new HashMap<>();
            if (rankOldMap.containsKey(rankResult.getCpCRegionCityId())) {
                List<LogisticsRankResult> logisticsRankList = JsonUtils.jsonToList(LogisticsRankResult.class, rankResult.getLogisticsRank());
                List<LogisticsRankResult> logisticsRankOldList = JsonUtils.jsonToList(LogisticsRankResult.class,
                        rankOldMap.get(rankResult.getCpCRegionCityId()).getLogisticsRank());

                for (LogisticsRankResult logisticsRank : logisticsRankList) {
                    if (StringUtils.isNotBlank(logisticsRank.getProvDiffRank())) {
                        for (LogisticsRankResult logisticsRankOld : logisticsRankOldList) {
                            if (logisticsRank.getLogisticsId().equals(logisticsRankOld.getLogisticsId())) {
                                logisticsRankOldMap.put(logisticsRankOld.getLogisticsId(), logisticsRankOld);
                                break;
                            }
                        }
                    }
                }
            }

            int i = 1;
            List<LogisticsRankResult> logisticsRankNewList = Lists.newArrayList();
            List<String> rankAllList = Lists.newArrayList();
            for (StCWarehouseLogisticsItemDO item : itemList) {
                LogisticsRankResult logisticsRankNew = new LogisticsRankResult();
                logisticsRankNew.setLogisticsId(item.getCpCLogisticsId());
                try {
                    Method method = WarehouseLogisticsRankResult.class.getMethod("getRank" + i);
                    logisticsRankNew.setRank((String) method.invoke(rankResult));
                } catch (Exception ex) {
                    logisticsRankNew.setRank("");
                    log.debug(LogUtil.format("仓库物流优先级获取失败：{}"), Throwables.getStackTraceAsString(ex));
                }

                if (StringUtils.isBlank(logisticsRankNew.getRank()) &&
                        logisticsRankOldMap.containsKey(logisticsRankNew.getLogisticsId())) {
                    logisticsRankNew.setRank(logisticsRankOldMap.get(logisticsRankNew.getLogisticsId()).getRank());
                }
                i++;
                if (logisticsRankNew.getRank() != null && !"".equals(logisticsRankNew.getRank())) {
                    if (rankAllList.contains(logisticsRankNew.getRank())) {
                        valueHolder.put("code", -1);
                        valueHolder.put("message", rank.getCpCRegionProvinceEname() +
                                rank.getCpCRegionCityEname() + "仓库物流优先级重复！");
                        return false;
                    } else {
                        rankAllList.add(logisticsRankNew.getRank());
                    }
                    logisticsRankNewList.add(logisticsRankNew);
                }
            }

            boolean saveFlg = false;
            for (LogisticsRankResult logisticsRankResult : logisticsRankNewList) {
                if (StringUtils.isNotBlank(logisticsRankResult.getRank())) {
                    saveFlg = true;
                }
            }

            rank.setLogisticsRank(JSONArray.toJSONString(logisticsRankNewList));
            if (id < 0) {
                if (saveFlg) {
                    rank.setId(ModelUtil.getSequence(strTableRank));
                    rank.setStCWarehouseLogisticsId(objid);

                    StBeanUtils.makeCreateField(rank, querySession.getUser());
                    if (stCRankMapper.insert(rank) < 0) {
                        valueHolder.put("code", -1);
                        valueHolder.put("message", rank.getCpCRegionProvinceEname() +
                                rank.getCpCRegionCityEname() + "保存失败！");
                        return false;
                    }
                }
            } else {
                if (saveFlg) {
                    //修改原有的行信息 id>0
                    if (stCRankMapper.selectById(id) != null) {
                        StBeanUtils.makeModifierField(rank, querySession.getUser());
                        if (stCRankMapper.updateById(rank) < 0) {
                            valueHolder.put("code", -1);
                            valueHolder.put("message", "保存失败！");
                            return false;
                        }
                    } else {
                        valueHolder.put("code", -1);
                        valueHolder.put("message", "修改的行明细已被删除！");
                        return false;
                    }
                } else {
                    if (stCRankMapper.deleteById(rank) < 0) {
                        valueHolder.put("code", -1);
                        valueHolder.put("message", "保存失败！");
                        return false;
                    }
                }
            }
        }
        return true;
    }

    private boolean checkStatus(StCWarehouseLogisticsDO stCWarehouseLogisticsDO, Long objId, ValueHolder valueHolder) {
        if (stCWarehouseLogisticsDO.getCpCPhyWarehouseId() != null) {
            if (stCMainMapper.listByWareid(objId, stCWarehouseLogisticsDO.getCpCPhyWarehouseId()) > 0) {
                valueHolder.put("code", -1);
                valueHolder.put("message", "该仓库物流优先级方案已存在，不能重复！");
                return false;
            }
        }

        if (objId > 0) {
            StCWarehouseLogisticsDO stCWarehouseLogisticsDO1 = stCMainMapper.selectById(objId);
            if (stCWarehouseLogisticsDO1 != null) {
                String strActive = stCWarehouseLogisticsDO1.getIsactive();
                if ("N".equals(strActive)) {
                    valueHolder.put("code", -1);
                    valueHolder.put("message", "单据处于作废状态不能编辑！");
                    return false;
                }
            } else {
                valueHolder.put("code", -1);
                valueHolder.put("message", "数据已不存在！");
                return false;
            }
        }
        return true;
    }

    private boolean checkList(List<StCWarehouseLogisticsItemDO> itemDOList, Long objid, ValueHolder valueHolder) {
        Map<String, String> modifyMap = new HashMap();
        Map<String, String> oldMap = new HashMap<String, String>();
        String strKey = "";
        String strValue = "";

        for (StCWarehouseLogisticsItemDO stCWarehouseLogisticsItemDO : itemDOList) {

            //判断新增或者修改的物流公司是否重复
            if (stCWarehouseLogisticsItemDO.getCpCLogisticsId() != null) {
                strKey = stCWarehouseLogisticsItemDO.getCpCLogisticsId().toString();
                strValue = stCWarehouseLogisticsItemDO.getId().toString();

                if (modifyMap.isEmpty()) {
                    modifyMap.put(strKey, strValue);
                } else {
                    if (modifyMap.containsKey(strKey)) {
                        valueHolder.put("code", -1);
                        valueHolder.put("message", "物流公司不能重复！");
                        return false;
                    } else {
                        modifyMap.put(strKey, strValue);
                    }
                }
            }
        }

        //objid = -1为新增的时候忽略后续判断
        if (objid < 0) {
            return true;
        }

        //读取未修改的行
        List<StCWarehouseLogisticsItemDO> warehouseLogisticsItemList = stCItemMapper.listByMainid(objid);
        if (warehouseLogisticsItemList != null) {
            for (StCWarehouseLogisticsItemDO stCDO : warehouseLogisticsItemList) {
                if (stCDO.getId() != null && stCDO.getCpCLogisticsId() != null) {
                    strKey = stCDO.getCpCLogisticsId().toString();
                    strValue = stCDO.getId().toString();
                    //判断如果是修改物流公司的话，则从oldMap去除这行
                    if (!modifyMap.containsValue(strValue)) {
                        oldMap.put(strKey, strValue);
                    }
                }
            }
        }
        if (!oldMap.isEmpty() && !modifyMap.isEmpty()) {
            //判断modifyMap的key值 是否存在于oldMap的key值
            for (String key : modifyMap.keySet()) {
                if (oldMap.containsKey(key)) {
                    valueHolder.put("code", -1);
                    valueHolder.put("message", "物流公司不能重复！");
                    return false;
                }
            }
        }
        return true;
    }
}
