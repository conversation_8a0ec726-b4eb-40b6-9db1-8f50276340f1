package com.jackrain.nea.st.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.openservices.shade.org.apache.commons.lang3.StringUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.burgeon.r3.sg.channel.model.request.product.SgChannelProductQueryRequest;
import com.burgeon.r3.sg.core.common.SgConstants;
import com.burgeon.r3.sg.core.model.table.channel.product.SgBChannelProduct;
import com.burgeon.r3.sg.inf.api.oms.product.SgChannelProductQueryCmd;
import com.github.pagehelper.util.StringUtil;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.psext.api.SkuLikeQueryCmd;
import com.jackrain.nea.psext.model.table.PsCPro;
import com.jackrain.nea.psext.model.table.PsCSku;
import com.jackrain.nea.st.common.StConstant;
import com.jackrain.nea.st.mapper.*;
import com.jackrain.nea.st.model.request.StCOrderLabelRequest;
import com.jackrain.nea.st.model.table.*;
import com.jackrain.nea.st.utils.JsonUtils;
import com.jackrain.nea.st.utils.StBeanUtils;
import com.jackrain.nea.st.utils.ValueHolderUtils;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @ClassName : StCOrderLabelSaveService
 * @Description : 订单打标策略保存服务
 * <AUTHOR>  YCH
 * @Date: 2021-11-23 10:35
 */
@Component
@Slf4j
@Transactional
public class StCOrderLabelSaveService extends CommandAdapter {

    @Autowired
    private StCOrderLabelMapper stCOrderLabelMapper;

    @Autowired
    private StCOrderLabelItemMapper stCOrderLabelItemMapper;

    @Autowired
    private StCOrderLabelShopItemMapper shopItemMapper;

    @Autowired
    private StCOrderLabelLogMapper stCOrderLabelLogMapper;

    @Autowired
    private StCCustomLabelMapper stCCustomLabelMapper;

    @Reference(group = "sg", version = "1.0")
    private SgChannelProductQueryCmd sgChannelProductQueryCmd;

    @Reference(group = "ps-ext", version = "1.0")
    private SkuLikeQueryCmd skuLikeQueryCmd;

    @Override
    public ValueHolder execute(QuerySession session) {
        DefaultWebEvent event = session.getEvent();
        JSONObject param = JsonUtils.filterNull((JSONObject) event.getParameterValue("param"), new ArrayList<>());
        Long id = param.getLong("objid");
        JSONObject fixColumn = param.getJSONObject("fixcolumn");
        log.info(LogUtil.format("querySession:{}", "StCOrderLabelSaveService.execute"), JSON.toJSONString(fixColumn));
        StCOrderLabelRequest stCHoldOrderRequest = JsonUtils.jsonParseClass(fixColumn, StCOrderLabelRequest.class);
        if (fixColumn != null) {
            if (id != null && id > 0L) {
                return this.updateStCOrderLabel(session, stCHoldOrderRequest, id);
            } else {
                return this.insertStCOrderLabel(session, stCHoldOrderRequest);
            }
        }
        throw new NDSException("当前记录已不存在！");
    }

    private ValueHolder updateStCOrderLabel(QuerySession session, StCOrderLabelRequest stCHoldOrderRequest, Long id) {
        //主表修改
        StCOrderLabelDO oldMain = stCOrderLabelMapper.selectById(id);
        if (oldMain == null) {
            return ValueHolderUtils.getFailValueHolder("记录不存在！");
        }

        StCOrderLabelDO newMain = stCHoldOrderRequest.getStCOrderLabelDO();
        if (newMain != null) {
            //时间判断
            Date beginTime = newMain.getBeginTime() == null ? oldMain.getBeginTime() : newMain.getBeginTime();
            Date endTime = newMain.getEndTime() == null ? oldMain.getEndTime() : newMain.getEndTime();
            if (beginTime.before(new Date())) {
                return ValueHolderUtils.getFailValueHolder("开始时间必须大于当前时间！");
            }
            if (endTime.before(beginTime)) {
                return ValueHolderUtils.getFailValueHolder("结束日期不能小于开始日期！");
            }
            newMain.setId(id);
            if (StringUtil.isNotEmpty(newMain.getStCCustomLabelId())) {
                StCCustomLabelDO customLabelDOS = stCCustomLabelMapper.selectById(newMain.getStCCustomLabelId());
                newMain.setStCCustomLabelEname(customLabelDOS.getEname());
            }
            StBeanUtils.makeModifierField(newMain, session.getUser());
            if (stCOrderLabelMapper.updateById(newMain) < 0) {
                return ValueHolderUtils.getFailValueHolder("更新失败！");
            }
        } else {
            StBeanUtils.makeModifierField(oldMain, session.getUser());
            stCOrderLabelMapper.updateById(oldMain);
        }

        //新增明细
        List<StCOrderLabelItemDO> oldItemList = new ArrayList<>();
        List<StCOrderLabelItemDO> insertList = new ArrayList<>();

        List<StCOrderLabelItemDO> stCOrderLabelStrategyItemDOList = stCHoldOrderRequest.getStCOrderLabelStrategyItemDOList();
        log.info("StCOrderLabelSaveService.updateStCOrderLabel param stCOrderLabelStrategyItemDOList:{}", JSON.toJSONString(stCOrderLabelStrategyItemDOList));
        if (CollectionUtils.isNotEmpty(stCOrderLabelStrategyItemDOList)) {
            LambdaQueryWrapper<StCOrderLabelItemDO> itemWrapper = new LambdaQueryWrapper<>();
            itemWrapper.eq(StCOrderLabelItemDO::getStCOrderLabelId, id);
            itemWrapper.eq(StCOrderLabelItemDO::getIsactive, StConstant.ISACTIVE_Y);
            oldItemList.addAll(stCOrderLabelItemMapper.selectList(itemWrapper));

            stCOrderLabelStrategyItemDOList.forEach(i -> {
                i.setStCOrderLabelId(id);
                i.setType(1);

                if (i.getId() == null || i.getId() < 1L) {
                    long count = oldItemList.stream().filter(x -> x.getCompareType().equals(i.getCompareType())
                            && x.getRulesRecognition().equals(i.getRulesRecognition())
                            && x.getContent().equals(i.getContent())).count();

                    if (count > 0) {
                        throw new NDSException("已存在相同明细！");
                    }
                    i.setId(ModelUtil.getSequence(StConstant.TAB_ST_C_ORDER_LABEL_ITEM));
                    StBeanUtils.makeCreateField(i, session.getUser());
                    insertList.add(i);
                } else {
                    List<StCOrderLabelItemDO> collect = oldItemList.stream().filter(x -> x.getId().equals(i.getId())).collect(Collectors.toList());
                    if (CollectionUtils.isEmpty(collect)) {
                        throw new NDSException("明细已不存在！");
                    }
                    StCOrderLabelItemDO oldItem = collect.get(0);
                    if (StringUtils.isNotEmpty(i.getContent())) {
                        oldItem.setContent(i.getContent());
                    }
                    if (i.getCompareType() != null) {
                        oldItem.setCompareType(i.getCompareType());
                    }
                    if (StringUtils.isNotEmpty(i.getRulesRecognition())) {
                        oldItem.setRulesRecognition(i.getRulesRecognition());
                    }

                    long count = oldItemList.stream().filter(x -> x.getCompareType().equals(oldItem.getCompareType())
                            && x.getRulesRecognition().equals(oldItem.getRulesRecognition())
                            && x.getContent().equals(oldItem.getContent())
                            && !x.getId().equals(oldItem.getId())).count();

                    if (count > 0) {
                        throw new NDSException("已存在相同明细！");
                    }
                    StBeanUtils.makeModifierField(oldItem, session.getUser());
                    stCOrderLabelItemMapper.updateById(oldItem);
                }
            });
        }

        //新增明细
        List<StCOrderLabelItemDO> stCOrderLabelItemDOList = stCHoldOrderRequest.getStCOrderLabelItemDOList();
        log.info("StCOrderLabelSaveService.updateStCOrderLabel param stCOrderLabelItemDOList:{}", JSON.toJSONString(stCOrderLabelItemDOList));
        if (CollectionUtils.isNotEmpty(stCOrderLabelItemDOList)) {
            if (CollectionUtils.isEmpty(oldItemList)) {
                LambdaQueryWrapper<StCOrderLabelItemDO> itemWrapper = new LambdaQueryWrapper<>();
                itemWrapper.eq(StCOrderLabelItemDO::getStCOrderLabelId, id);
                itemWrapper.eq(StCOrderLabelItemDO::getIsactive, StConstant.ISACTIVE_Y);
                oldItemList.addAll(stCOrderLabelItemMapper.selectList(itemWrapper));
            }
            stCOrderLabelItemDOList.forEach(i -> {
                i.setStCOrderLabelId(id);
                i.setType(2);
                i.setCompareType(2);
                if (i.getId() == null || i.getId() < 1L) {
                    long count = oldItemList.stream().filter(x -> x.getRulesRecognition().equals(i.getRulesRecognition())
                            && x.getContent().equals(i.getContent())).count();

                    if (count > 0) {
                        throw new NDSException("已存在相同明细！");
                    }
                    checkItem(i.getRulesRecognition(), i.getContent());
                    i.setId(ModelUtil.getSequence(StConstant.TAB_ST_C_ORDER_LABEL_ITEM));
                    StBeanUtils.makeCreateField(i, session.getUser());
                    insertList.add(i);
                } else {
                    List<StCOrderLabelItemDO> collect = oldItemList.stream().filter(x -> x.getId().equals(i.getId())).collect(Collectors.toList());
                    if (CollectionUtils.isEmpty(collect)) {
                        throw new NDSException("明细已不存在！");
                    }
                    StCOrderLabelItemDO oldItem = collect.get(0);
                    if (StringUtils.isNotEmpty(i.getContent())) {
                        oldItem.setContent(i.getContent());
                    }
                    if (i.getCompareType() != null) {
                        oldItem.setCompareType(i.getCompareType());
                    }
                    if (StringUtils.isNotEmpty(i.getRulesRecognition())) {
                        oldItem.setRulesRecognition(i.getRulesRecognition());
                    }

                    checkItem(oldItem.getRulesRecognition(), oldItem.getContent());

                    long count = oldItemList.stream().filter(x -> x.getRulesRecognition().equals(oldItem.getRulesRecognition())
                            && x.getContent().equals(oldItem.getContent())
                            && !x.getId().equals(oldItem.getId())).count();

                    if (count > 0) {
                        throw new NDSException("已存在相同明细！");
                    }
                    StBeanUtils.makeModifierField(oldItem, session.getUser());
                    stCOrderLabelItemMapper.updateById(oldItem);
                }
            });
        }
        if (CollectionUtils.isNotEmpty(insertList)) {
            stCOrderLabelItemMapper.batchInsert(insertList);
        }

        List<StCOrderLabelShopItemDO> shopItemList = stCHoldOrderRequest.getStCOrderLabelShopItemDOList();

        if (CollectionUtils.isNotEmpty(shopItemList)) {
            List<StCOrderLabelShopItemDO> shopInsertList = new ArrayList<>();

            Map<Long, List<StCOrderLabelShopItemDO>> checkMap = shopItemList.stream().collect(Collectors.groupingBy(StCOrderLabelShopItemDO::getCpCShopId));
            for (List<StCOrderLabelShopItemDO> checkValue : checkMap.values()) {
                if (checkValue.size() > 1) {
                    return ValueHolderUtils.getFailValueHolder("已存在相同店铺！");
                }
            }

            LambdaQueryWrapper<StCOrderLabelShopItemDO> shopItemWrapper = new LambdaQueryWrapper<>();
            shopItemWrapper.eq(StCOrderLabelShopItemDO::getStCOrderLabelId, id);
            shopItemWrapper.eq(StCOrderLabelShopItemDO::getIsactive, StConstant.ISACTIVE_Y);
            List<StCOrderLabelShopItemDO> alreadyShopList = shopItemMapper.selectList(shopItemWrapper);
            List<Long> shopIdList = alreadyShopList.stream().map(StCOrderLabelShopItemDO::getCpCShopId).collect(Collectors.toList());

            for (StCOrderLabelShopItemDO x : shopItemList) {
                if (shopIdList.contains(x.getCpCShopId())) {
                    return ValueHolderUtils.getFailValueHolder("已存在相同店铺！");
                }
                x.setStCOrderLabelId(id);
                x.setId(ModelUtil.getSequence(StConstant.TAB_ST_C_ORDER_LABEL_SHOP_ITEM));
                StBeanUtils.makeCreateField(x, session.getUser());
                shopInsertList.add(x);
            }
            shopItemMapper.batchInsert(shopInsertList);
        }

        //添加操作日志
        List<StCOrderLabelLogDO> stCOrderLabelLogDOList = new ArrayList<>();
        if (newMain != null) {
            if (StringUtil.isNotEmpty(newMain.getTimeType())) {
                if (!newMain.getTimeType().equals(oldMain.getTimeType())) {
                    StCOrderLabelLogDO stCOrderLabelLogDO = new StCOrderLabelLogDO();
                    stCOrderLabelLogDO.setId(ModelUtil.getSequence(StConstant.TAB_ST_C_ORDER_LABEL_LOG));
                    stCOrderLabelLogDO.setStCOrderLabelId(id);
                    stCOrderLabelLogDO.setModcontent("时间类型");
                    if ("1".equals(oldMain.getTimeType())) {
                        stCOrderLabelLogDO.setBmod("下单时间");
                        stCOrderLabelLogDO.setAmod("付款时间");
                    } else {
                        stCOrderLabelLogDO.setBmod("付款时间");
                        stCOrderLabelLogDO.setAmod("下单时间");
                    }
                    StBeanUtils.makeModifierField(stCOrderLabelLogDO, session.getUser());
                    stCOrderLabelLogDOList.add(stCOrderLabelLogDO);
                }
            }
            if (newMain.getBeginTime() != null) {
                if (newMain.getBeginTime() != oldMain.getBeginTime()) {
                    StCOrderLabelLogDO stCOrderLabelLogDO = new StCOrderLabelLogDO();
                    stCOrderLabelLogDO.setId(ModelUtil.getSequence(StConstant.TAB_ST_C_ORDER_LABEL_LOG));
                    stCOrderLabelLogDO.setStCOrderLabelId(id);
                    stCOrderLabelLogDO.setModcontent("开始时间");
                    stCOrderLabelLogDO.setBmod(dateBuild(oldMain.getBeginTime()));
                    stCOrderLabelLogDO.setAmod(dateBuild(newMain.getBeginTime()));
                    StBeanUtils.makeModifierField(stCOrderLabelLogDO, session.getUser());
                    stCOrderLabelLogDOList.add(stCOrderLabelLogDO);
                }
            }
            if (newMain.getEndTime() != null) {
                if (newMain.getEndTime() != oldMain.getEndTime()) {
                    StCOrderLabelLogDO stCOrderLabelLogDO = new StCOrderLabelLogDO();
                    stCOrderLabelLogDO.setId(ModelUtil.getSequence(StConstant.TAB_ST_C_ORDER_LABEL_LOG));
                    stCOrderLabelLogDO.setStCOrderLabelId(id);
                    stCOrderLabelLogDO.setModcontent("结束时间");
                    stCOrderLabelLogDO.setBmod(dateBuild(oldMain.getEndTime()));
                    stCOrderLabelLogDO.setAmod(dateBuild(newMain.getEndTime()));
                    StBeanUtils.makeModifierField(stCOrderLabelLogDO, session.getUser());
                    stCOrderLabelLogDOList.add(stCOrderLabelLogDO);
                }
            }
            if (StringUtil.isNotEmpty(newMain.getRemake())) {
                if (!newMain.getRemake().equals(oldMain.getRemake())) {
                    StCOrderLabelLogDO stCOrderLabelLogDO = new StCOrderLabelLogDO();
                    stCOrderLabelLogDO.setId(ModelUtil.getSequence(StConstant.TAB_ST_C_ORDER_LABEL_LOG));
                    stCOrderLabelLogDO.setStCOrderLabelId(id);
                    stCOrderLabelLogDO.setModcontent("备注");
                    stCOrderLabelLogDO.setBmod(oldMain.getRemake());
                    stCOrderLabelLogDO.setAmod(newMain.getRemake());
                    StBeanUtils.makeModifierField(stCOrderLabelLogDO, session.getUser());
                    stCOrderLabelLogDOList.add(stCOrderLabelLogDO);
                }
            }
            if (StringUtil.isNotEmpty(newMain.getStCCustomLabelId())) {
                if (!newMain.getStCCustomLabelId().equals(oldMain.getStCCustomLabelId())) {
                    StCOrderLabelLogDO stCOrderLabelLogDO = new StCOrderLabelLogDO();
                    stCOrderLabelLogDO.setId(ModelUtil.getSequence(StConstant.TAB_ST_C_ORDER_LABEL_LOG));
                    stCOrderLabelLogDO.setStCOrderLabelId(id);
                    stCOrderLabelLogDO.setModcontent("自定义标签");
                    stCOrderLabelLogDO.setBmod(oldMain.getStCCustomLabelEname());
                    stCOrderLabelLogDO.setAmod(newMain.getStCCustomLabelEname());
                    StBeanUtils.makeModifierField(stCOrderLabelLogDO, session.getUser());
                    stCOrderLabelLogDOList.add(stCOrderLabelLogDO);
                }
            }
        }
        if (CollectionUtils.isNotEmpty(stCOrderLabelLogDOList)) {
            stCOrderLabelLogMapper.batchInsert(stCOrderLabelLogDOList);
        }
        return ValueHolderUtils.getSuccessValueHolder(id, StConstant.TAB_ST_C_ORDER_LABEL, "修改成功");
    }

    private ValueHolder insertStCOrderLabel(QuerySession session, StCOrderLabelRequest stCOrderLabelRequest) {

        StCOrderLabelDO stCOrderLabelDO = stCOrderLabelRequest.getStCOrderLabelDO();
        //1.1 判断名称是否已存在
        boolean checkTitle = checkTitle(stCOrderLabelDO.getTitle());
        if (checkTitle) {
            return ValueHolderUtils.getFailValueHolder("当前标题已存在！");
        }
        long id = ModelUtil.getSequence(StConstant.TAB_ST_C_ORDER_LABEL);
        stCOrderLabelDO.setId(id);
        Date now = new Date();
        if (stCOrderLabelDO.getBeginTime().compareTo(now) < 0) {
            return ValueHolderUtils.getFailValueHolder("开始时间必须大于当前时间！");
        }
        if (stCOrderLabelDO.getBeginTime().compareTo(stCOrderLabelDO.getEndTime()) > 0) {
            return ValueHolderUtils.getFailValueHolder("结束日期不能小于开始日期！");
        }
        StCCustomLabelDO stCCustomLabelDOS = stCCustomLabelMapper.selectById(stCOrderLabelDO.getStCCustomLabelId());
        stCOrderLabelDO.setStCCustomLabelEname(stCCustomLabelDOS.getEname());
        //1.2 插入
        stCOrderLabelDO.setStatus(StConstant.HOLD_ORDER_STATUS_01);
        StBeanUtils.makeCreateField(stCOrderLabelDO, session.getUser());
        int insertResult = stCOrderLabelMapper.insert(stCOrderLabelDO);
        if (insertResult < 0) {
            return ValueHolderUtils.getFailValueHolder("保存失败！");
        }
        // 新增策略明细
        List<StCOrderLabelItemDO> stCOrderLabelStrategyItemDOList = stCOrderLabelRequest.getStCOrderLabelStrategyItemDOList();
        if (CollectionUtils.isNotEmpty(stCOrderLabelStrategyItemDOList)) {
            // 校验重复
            Map<String, List<StCOrderLabelItemDO>> checkMap = stCOrderLabelStrategyItemDOList.stream().collect(Collectors.groupingBy(x -> x.getRulesRecognition() + SgConstants.SG_CONNECTOR_MARKS_6 + x.getCompareType() + SgConstants.SG_CONNECTOR_MARKS_6 + x.getContent()));
            for (List<StCOrderLabelItemDO> checkValue : checkMap.values()) {
                if (checkValue.size() > 1) {
                    return ValueHolderUtils.getFailValueHolder("存在相同明细！");
                }
            }
            List<StCOrderLabelItemDO> insert = new ArrayList<>();
            stCOrderLabelStrategyItemDOList.forEach(i -> {
                i.setStCOrderLabelId(id);
                i.setType(1);
                i.setId(ModelUtil.getSequence(StConstant.TAB_ST_C_ORDER_LABEL_ITEM));
                StBeanUtils.makeCreateField(i, session.getUser());
                insert.add(i);
            });
            stCOrderLabelItemMapper.batchInsert(insert);
        }

        //新增商品明细
        List<StCOrderLabelItemDO> stCOrderLabelItemDOList = stCOrderLabelRequest.getStCOrderLabelItemDOList();
        if (CollectionUtils.isNotEmpty(stCOrderLabelItemDOList)) {
            Map<String, List<StCOrderLabelItemDO>> checkMap = stCOrderLabelItemDOList.stream().collect(Collectors.groupingBy(x -> x.getRulesRecognition() + SgConstants.SG_CONNECTOR_MARKS_6 + x.getContent()));
            for (List<StCOrderLabelItemDO> checkValue : checkMap.values()) {
                if (checkValue.size() > 1) {
                    return ValueHolderUtils.getFailValueHolder("存在相同明细！");
                }
            }
            List<StCOrderLabelItemDO> insterList = new ArrayList<>();
            stCOrderLabelItemDOList.forEach(i -> {
                checkItem(i.getRulesRecognition(), i.getContent());
                i.setStCOrderLabelId(id);
                i.setType(2);
                i.setCompareType(2);
                i.setId(ModelUtil.getSequence(StConstant.TAB_ST_C_ORDER_LABEL_ITEM));
                StBeanUtils.makeCreateField(i, session.getUser());
                insterList.add(i);
            });
            stCOrderLabelItemMapper.batchInsert(insterList);
        }
        List<StCOrderLabelShopItemDO> shopItemList = stCOrderLabelRequest.getStCOrderLabelShopItemDOList();
        if (CollectionUtils.isNotEmpty(shopItemList)) {
            Map<Long, List<StCOrderLabelShopItemDO>> checkMap = shopItemList.stream().collect(Collectors.groupingBy(StCOrderLabelShopItemDO::getCpCShopId));
            for (List<StCOrderLabelShopItemDO> checkValue : checkMap.values()) {
                if (checkValue.size() > 1) {
                    return ValueHolderUtils.getFailValueHolder("存在相同店铺！");
                }
            }
            shopItemList.forEach(x -> {
                x.setId(ModelUtil.getSequence(StConstant.TAB_ST_C_ORDER_LABEL_SHOP_ITEM));
                x.setStCOrderLabelId(id);
                StBeanUtils.makeCreateField(x, session.getUser());
            });
            shopItemMapper.batchInsert(shopItemList);
        }
        return ValueHolderUtils.getSuccessValueHolder(id, StConstant.TAB_ST_C_ORDER_LABEL, "保存成功");
    }

    private boolean checkTitle(String title) {
        LambdaQueryWrapper<StCOrderLabelDO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(StCOrderLabelDO::getTitle, title);
        wrapper.eq(StCOrderLabelDO::getIsactive, StConstant.ISACTIVE_Y);
        return stCOrderLabelMapper.selectCount(wrapper) > 0;
    }

    /**
     * description:识别规则合识别内容只存在一个  flag  true=修改
     */
    private void checkItem(String rulesRecognition, String content) {
        if (StConstant.ST_C_ORDER_LABEL_ITEM_RULES_RECOGNITION_PTSKU.equals(rulesRecognition)) {
            SgChannelProductQueryRequest request = new SgChannelProductQueryRequest();
            List<String> list = new ArrayList<>();
            list.add(content);
            request.setSkuIdList(list);
            ValueHolderV14<List<SgBChannelProduct>> holderV14 = sgChannelProductQueryCmd.queryChannelProduct(request);

            if (!holderV14.isOK() || CollectionUtils.isEmpty(holderV14.getData())) {
                throw new NDSException("平台条码在平台店铺商品表中不存在！");
            }
        }
        if (StConstant.ST_C_ORDER_LABEL_ITEM_RULES_RECOGNITION_TPID.equals(rulesRecognition)) {
            SgChannelProductQueryRequest request = new SgChannelProductQueryRequest();
            List<String> list = new ArrayList<>();
            list.add(content);
            request.setNumiidList(list);
            ValueHolderV14<List<SgBChannelProduct>> holderV14 = sgChannelProductQueryCmd.queryChannelProduct(request);

            if (!holderV14.isOK() || CollectionUtils.isEmpty(holderV14.getData())) {
                throw new NDSException("平台商品在平台店铺商品表中不存在！");
            }
        }
        if (StConstant.ST_C_ORDER_LABEL_ITEM_RULES_RECOGNITION_MSKU.equals(rulesRecognition)) {
            List<String> list = new ArrayList<>();
            list.add(content);
            List<PsCSku> psCPros = skuLikeQueryCmd.querySkuByEcode(list);
            if (CollectionUtils.isEmpty(psCPros)) {
                throw new NDSException("条码在条码档案中不存在！");
            }
        }
        if (StConstant.ST_C_ORDER_LABEL_ITEM_RULES_RECOGNITION_MPRO.equals(rulesRecognition)) {
            List<String> list = new ArrayList<>();
            list.add(content);
            List<PsCPro> psCPros = skuLikeQueryCmd.queryProByEcode(list);
            if (CollectionUtils.isEmpty(psCPros)) {
                throw new NDSException("商品在商品档案中不存在！");
            }
        }
    }

    public String dateBuild(Date date) {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        return simpleDateFormat.format(date);
    }

}
