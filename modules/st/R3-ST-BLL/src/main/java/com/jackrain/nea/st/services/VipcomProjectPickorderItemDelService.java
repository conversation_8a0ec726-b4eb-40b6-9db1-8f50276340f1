package com.jackrain.nea.st.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.st.mapper.StCVipcomProjectPickorderItemMapper;
import com.jackrain.nea.st.utils.ValueHolderUtils;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

/**
 * @Descroption 档期日程规划拣货单明细-删除
 * <AUTHOR>
 * @Date 2021/05/24 15:50
 */
@Component
@Slf4j
@Transactional
public class VipcomProjectPickorderItemDelService extends CommandAdapter {
    @Autowired
    private StCVipcomProjectPickorderItemMapper stCVipcomProjectPickorderItemMapper;

    @Override
    public ValueHolder execute(QuerySession querySession) throws NDSException {
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("Start VipcomProjectPickorderItemDelService.QuerySession=") + querySession.toString() + ";");
        }
        DefaultWebEvent event = querySession.getEvent();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(
                event.getParameterValue("param"), "yyyy-MM-dd HH:mm:ss"),
                Feature.OrderedField);
        Long  masterId = param.getLong("ST_C_VIPCOM_PROJECT_ID");
        Long itemidId = param.getLong("IDS");

        if(itemidId.intValue() == -1){
            //根据主表ID删除所有明细
            stCVipcomProjectPickorderItemMapper.deletePickorderItemByMasterId(masterId);
            return ValueHolderUtils.custom(0,"明细清除成功",null);
        }else{
            int result = stCVipcomProjectPickorderItemMapper.deletePickorderItemById(itemidId);
            if(result == 0){
                return ValueHolderUtils.custom(-1,"删除失败",null);
            }else{
                return ValueHolderUtils.custom(0,"删除成功",null);
            }
        }
    }


}
