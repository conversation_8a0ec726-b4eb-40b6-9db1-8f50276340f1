package com.jackrain.nea.st.services;

import com.alibaba.fastjson.JSON;
import com.google.common.base.Throwables;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.st.mapper.StCVipcomAscriptionMapper;
import com.jackrain.nea.st.mapper.StCVipcomProjectItemMapper;
import com.jackrain.nea.st.mapper.StCVipcomProjectMapper;
import com.jackrain.nea.st.mapper.StCVipcomProjectPickorderItemMapper;
import com.jackrain.nea.st.mapper.StCVipcomProjectWhEntryItemMapper;
import com.jackrain.nea.st.model.request.VipProjectItemAscriptionRequest;
import com.jackrain.nea.st.model.request.VipcomProjectAndItemRequest;
import com.jackrain.nea.st.model.request.VipcomProjectNewRequest;
import com.jackrain.nea.st.model.request.VipcomProjectRequest;
import com.jackrain.nea.st.model.table.StCVipcomAscriptionDO;
import com.jackrain.nea.st.model.table.StCVipcomProjectDO;
import com.jackrain.nea.st.model.table.StCVipcomProjectItemDO;
import com.jackrain.nea.st.model.table.StCVipcomProjectItemExtDO;
import com.jackrain.nea.st.model.table.StCVipcomProjectPickorderItem;
import com.jackrain.nea.st.model.table.StCVipcomProjectWhEntryItem;
import com.jackrain.nea.st.utils.JsonUtils;
import com.jackrain.nea.st.utils.ValueHolderUtils;
import com.jackrain.nea.st.validate.StParamConstants;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.DateUtil;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utility.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.time.FastDateFormat;
import org.assertj.core.util.Lists;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalTime;
import java.util.*;
import java.util.function.Predicate;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description 获取日程配置信息
 **/
@Component
@Slf4j
public class VipcomProjectPoService extends CommandAdapter {

    @Autowired
    private StCVipcomProjectMapper stCVipcomProjectMapper;
    @Autowired
    private StCVipcomProjectPickorderItemMapper stCVipcomProjectPickorderItemMapper;
    @Autowired
    private StCVipcomProjectItemMapper stCVipcomProjectItemMapper;
    @Autowired
    private StCVipcomAscriptionMapper stCVipcomAscriptionMapper;

    @Autowired
    private StCVipcomProjectWhEntryItemMapper stCVipcomProjectWhEntryItemMapper;

    /**
     * 获取有效档期日程规划
     *
     * @param shopId 店铺ID
     * @return StCVipcomProjectDO
     */
    public ValueHolder selectVipcomProjectByShopId(Long shopId) {
        ValueHolder valueHolder = new ValueHolder();
        List<StCVipcomProjectDO> stCVipcomProjectDOList
                = stCVipcomProjectMapper.selectProjectByShopId(shopId, new Date());
        HashMap<String, Object> map = new HashMap<>();
        if (CollectionUtils.isNotEmpty(stCVipcomProjectDOList)) {
            String json = JsonUtils.toJsonString(stCVipcomProjectDOList.get(0));
            map.put("code", 0);
            map.put("message", "");
            map.put("data", json);
            valueHolder.setData(map);
        } else {
            map.put("code", -1);
            map.put("message", "无符合的日程信息！");
            valueHolder.setData(map);
        }
        return valueHolder;
    }

    /**
     * @return com.jackrain.nea.util.ValueHolder
     * <AUTHOR>
     * @Description 获取匹配的日程规划信息;根据店铺查询档期日程规划主表和拣货单明细、入库单明细
     * 如果传入店铺id为空，则代表查询全部
     * @ModifiedDate 2021-06-11
     **/
    public ValueHolder getPoInfo(Long shopId) {

        Date curDate = new Date();

        DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String s = dateFormat.format(curDate);
        List<StCVipcomProjectDO> stCVipcomProjectDOList = stCVipcomProjectMapper.selectByCurrentDate(s);
        log.debug("VipcomProjectPoService.getPoInfo 获取匹配的日程规划信息 data：{}", JSON.toJSONString(stCVipcomProjectDOList));
        //按照店铺筛选分组日程
        Map<Long, List<StCVipcomProjectDO>> mapShop = stCVipcomProjectDOList.stream()
                .collect(Collectors.groupingBy(StCVipcomProjectDO::getCpCShopId));

        //排序取店铺最高优先级
        List<StCVipcomProjectDO> resultShop = new ArrayList<>();
        for (Map.Entry<Long, List<StCVipcomProjectDO>> listSet : mapShop.entrySet()) {
            //优先级升序，修改时间降序排序（优先级值越小，代表优先级越高）
            Optional<StCVipcomProjectDO> o = listSet.getValue().stream().min(Comparator.comparing(StCVipcomProjectDO::getRank));
            //传入店铺信息则只取当前店铺的信息
            if (null != shopId && !listSet.getKey().equals(shopId)) {
                continue;
            }
            o.ifPresent(resultShop::add);
        }
        log.debug("VipcomProjectPoService.getPoInfo 获取匹配的日程规划信息 data1：{}", JSON.toJSONString(resultShop));
        //查询子表
        if (CollectionUtils.isEmpty(resultShop)) {
            return ValueHolderUtils.getFailValueHolder("无日程信息！");
        }
        List<VipcomProjectNewRequest> vipcomProjectRequestList = new ArrayList<>();
        for (StCVipcomProjectDO stDo : resultShop) {
            Map<String, Object> map = new HashMap<>(16);
            map.put("st_c_vipcom_project_id", stDo.getId());
            List<StCVipcomProjectPickorderItem> stCVipcomProjectPickorderItemList = stCVipcomProjectPickorderItemMapper.queryPickorderItemByMasterId(stDo.getId());
            log.debug("VipcomProjectPoService.getPoInfo 获取匹配的日程规划信息 data3-1：{}",
                    JSON.toJSONString(stCVipcomProjectPickorderItemList));
            if (CollectionUtils.isEmpty(stCVipcomProjectPickorderItemList)) {
                continue;
            }
            List<StCVipcomProjectWhEntryItem> stCVipcomProjectWhEntryItems = stCVipcomProjectWhEntryItemMapper.queryPickorderItemByMasterId(stDo.getId());
            log.debug("VipcomProjectPoService.getPoInfo 获取匹配的日程规划信息 data3-2：{}",
                    JSON.toJSONString(stCVipcomProjectWhEntryItems));
            VipcomProjectNewRequest vipcomProjectNewRequest = new VipcomProjectNewRequest();
            vipcomProjectNewRequest.setStCVipcomProject(stDo);
            vipcomProjectNewRequest.setStCVipcomProjectPickorderItemList(stCVipcomProjectPickorderItemList);
            vipcomProjectNewRequest.setStCVipcomProjectWhEntryItemList(stCVipcomProjectWhEntryItems);
            vipcomProjectRequestList.add(vipcomProjectNewRequest);
        }
        log.debug("VipcomProjectPoService.getPoInfo 获取匹配的日程规划信息 data3-3：{}", JSON.toJSONString(vipcomProjectRequestList));
        ValueHolder valueHolder = new ValueHolder();
        HashMap<String, Object> map = new HashMap<>();
        if (CollectionUtils.isNotEmpty(vipcomProjectRequestList)) {
            String json = JsonUtils.toJsonString(vipcomProjectRequestList);
            map.put("code", 0);
            map.put("message", "");
            map.put("data", json);
            valueHolder.setData(map);
        } else {
            map.put("code", -1);
            map.put("message", "无符合的日程信息！!!");
            valueHolder.setData(map);
        }
        return valueHolder;
    }

    /**
     * description：根据JIT配货单的“店铺”、“发货实体仓”在档期日程规划中查找出距离当前时间往后最近的一条规划明细
     *
     * <AUTHOR>
     * @date 2021/6/23
     */
    public VipcomProjectNewRequest getVipcomProjectAndItemRequest(Long shopId, Long cpCPhyWarehouseId) {
        VipcomProjectNewRequest request = new VipcomProjectNewRequest();

        try {
            List<StCVipcomProjectDO> stCVipcomProjectDOList = stCVipcomProjectMapper.selectProjectByShopId(shopId
                    , new Date());
            if (!stCVipcomProjectDOList.isEmpty()) {
                //优先级升序，修改时间降序排序（优先级值越小，代表优先级越高）
                Optional<StCVipcomProjectDO> o = stCVipcomProjectDOList.stream().min(Comparator.comparing(StCVipcomProjectDO::getRank));
                // 同一个店铺只有一个日程规划，故取第一个
                StCVipcomProjectDO stCVipcomProjectDO = o.get();
                request.setStCVipcomProject(stCVipcomProjectDO);
                List<StCVipcomProjectPickorderItem> stCVipcomProjectPickorderItemList =
                        stCVipcomProjectPickorderItemMapper.queryPickorderItemByMasterId(stCVipcomProjectDO.getId());
                // 获取日程规划明细
                request.setStCVipcomProjectPickorderItemList(stCVipcomProjectPickorderItemList);
                List<StCVipcomProjectWhEntryItem> stCVipcomProjectWhEntryItems = stCVipcomProjectWhEntryItemMapper.queryPickorderItemByMasterId(stCVipcomProjectDO.getId());
                //根据仓库和结单时间(离当前时间往后最近的)
                StCVipcomProjectWhEntryItem matchItem = this.filterVipcomProjectWhEntryItems(cpCPhyWarehouseId.toString(), stCVipcomProjectWhEntryItems);
                request.setStCVipcomProjectWhEntryItemList(Lists.newArrayList(matchItem));

            }
        } catch (Exception e) {
            log.error(LogUtil.format("getVipcomProjectAndItemRequest error{}"), Throwables.getStackTraceAsString(e));
        }
        return request;
    }


    private VipcomProjectAndItemRequest getVipcomProjectAndItemRequestByShopId(Long shopId, Date curDate
            , String warehouseCode
            , Long cpCPhyWarehouseId) {
        VipcomProjectAndItemRequest request = new VipcomProjectAndItemRequest();

        try {
            List<StCVipcomProjectDO> stCVipcomProjectDOList = stCVipcomProjectMapper.selectProjectByShopId(shopId
                    , curDate);
            if (stCVipcomProjectDOList.size() >= 1) {
                // 同一个店铺只有一个日程规划，故取第一个
                StCVipcomProjectDO stCVipcomProjectDO = stCVipcomProjectDOList.get(0);
                request.setStCVipcomProject(stCVipcomProjectDO);
                // 获取日程规划明细
                List<StCVipcomProjectItemDO> stCVipcomProjectItemDOS = stCVipcomProjectItemMapper
                        .listStCVipcomProjectItemOrderByDownLoadTime(stCVipcomProjectDO.getId());
                List<StCVipcomProjectItemDO> filterVipProjectItems = getMatchPhyWareHouseAndWareHouseCode(
                        warehouseCode
                        , cpCPhyWarehouseId
                        , stCVipcomProjectItemDOS);
                if (CollectionUtils.isNotEmpty(filterVipProjectItems)) {
                    List<StCVipcomProjectItemExtDO> stCVipcomProjectItemExtDOS = filterVipProjectItems.stream()
                            .map(this::buildProjectItemExt).collect(Collectors.toList());
                    request.setStCVipcomProjectItemList(stCVipcomProjectItemExtDOS);
                }
            }
        } catch (Exception e) {
            log.error(LogUtil.format("getVipcomProjectAndItemRequestByShopId error{}"), Throwables.getStackTraceAsString(e));
        }
        return request;
    }

    private StCVipcomProjectItemExtDO buildProjectItemExt(StCVipcomProjectItemDO itemDO) {
        StCVipcomProjectItemExtDO itemExtDO = new StCVipcomProjectItemExtDO();
        BeanUtils.copyProperties(itemDO, itemExtDO);
        StCVipcomAscriptionDO stCVipcomAscriptionDO = stCVipcomAscriptionMapper
                .selectById(itemDO.getStCVipcomAscriptionId());
        itemExtDO.setStCVipcomAscriptionDO(stCVipcomAscriptionDO);
        return itemExtDO;
    }

    /**
     * 获取店铺当前时段最合适的档期
     *
     * @param shopId
     * @return
     */
    public VipcomProjectAndItemRequest getOneShopVipProjectInfo(Long shopId, String warehouseCode
            , Long cpCPhyWarehouseId) {
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("getOneShopVipProjectInfo begin：shopId/warehouseCode/cpCPhyWarehouseId="
                    , shopId, warehouseCode, cpCPhyWarehouseId));
        }
        Date curDate = new Date();
        VipcomProjectAndItemRequest vipcomProjectAndItemRequest = getVipcomProjectAndItemRequestByShopId(shopId
                , curDate, warehouseCode, cpCPhyWarehouseId);
        return vipcomProjectAndItemRequest;
    }

    /**
     * 获取店铺当前时段最合适的档期
     *
     * @param shopId
     * @return
     */
    public ValueHolder listVipProjectAscription(Long shopId, String warehouseCode, Long cpCPhyWarehouseId
            , String downTimeStr) {
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("listVipProjectAscription begin：shopId/warehouseCode/cpCPhyWarehouseId" +
                    "/downTimeStr=", shopId, warehouseCode, cpCPhyWarehouseId, downTimeStr));
        }
        Date curDate = new Date();
        VipcomProjectAndItemRequest vipcomProjectAndItemRequest = getVipcomProjectAndItemRequestByShopId(shopId
                , curDate, warehouseCode, cpCPhyWarehouseId);
        try {
            List<StCVipcomProjectItemExtDO> filterVipProjectItems = vipcomProjectAndItemRequest
                    .getStCVipcomProjectItemList();
            if (CollectionUtils.isNotEmpty(filterVipProjectItems)) {
                // 根据档期下单时间 获取日程规划明细及归属
                StCVipcomProjectItemExtDO itemDO = getVipProjectItemWithInTenMinutes(filterVipProjectItems, downTimeStr);
                if (itemDO == null) {
                    if (log.isDebugEnabled()) {
                        log.debug(LogUtil.format("listVipProjectAscription.getVipProjectItemWithInTenMinutes return " +
                                "is empty"));
                    }
                    return ValueHolderUtils.getFailValueHolder("根据下单时间获取档期日程规划明细为空");
                }
                String currentTime = DateUtil.format(new Date(), "HH:mm:ss").substring(0, 4);
                List<StCVipcomProjectItemExtDO> stCVipcomProjectItemExtDOS = filterVipProjectItems.stream()
                        .filter(getStCVipcomProjectItemExtDOPredicate(currentTime, itemDO.getDownloadTime()))
                        .collect(Collectors.toList());
                List<VipProjectItemAscriptionRequest> andAscriptionVos = stCVipcomProjectItemExtDOS.stream()
                        .map(obj -> {
                            VipProjectItemAscriptionRequest andAscriptionVo = new VipProjectItemAscriptionRequest();
                            andAscriptionVo.setId(obj.getStCVipcomAscriptionDO().getId());
                            andAscriptionVo.setECode(obj.getStCVipcomAscriptionDO().getEcode());
                            andAscriptionVo.setEName(obj.getStCVipcomAscriptionDO().getEname());
                            andAscriptionVo.setSelected(0);
                            if (itemDO.getDownloadTime().equals(obj.getDownloadTime())) {
                                andAscriptionVo.setSelected(1);
                            }
                            int deliveryMethodType = Integer.parseInt(obj.getDeliveryMethod());
                            andAscriptionVo.setDeliveryMethod(deliveryMethodType);
                            String deliveryMethodName = deliveryMethodType == 2 ? "空运" : "汽运";
                            andAscriptionVo.setDeliveryMethodName(deliveryMethodName);
                            andAscriptionVo.setDownloadTime(obj.getDownloadTime());
                            return andAscriptionVo;
                        }).collect(Collectors.toList());
                if (log.isDebugEnabled()) {
                    log.debug(LogUtil.format("success.end：result:{}，shopId/warehouseCode" +
                                    "/cpCPhyWarehouseId/downTimeStr=", shopId
                            , warehouseCode, cpCPhyWarehouseId, downTimeStr), JSON.toJSONString(andAscriptionVos));
                }
                return ValueHolderUtils.custom(ResultCode.SUCCESS, "获取日程归属信息成功", andAscriptionVos);
            } else {
                if (log.isDebugEnabled()) {
                    log.debug(LogUtil.format("listVipProjectAscription.getVipcomProjectAndItemRequestByShopId return " +
                            "is empty"));
                }
                return ValueHolderUtils.getFailValueHolder("没有符合条件的档期日程规划明细");
            }
        } catch (Exception e) {
            log.error(LogUtil.format("listVipProjectAscription.error{}"), Throwables.getStackTraceAsString(e));
        }
        return ValueHolderUtils.getFailValueHolder("获取日程归属信息失败");
    }

    /**
     * 获取店铺当前时段最合适的档期
     *
     * @param shopId
     * @return
     */
    public ValueHolder listVipProjectAscriptionWithNoDefault(Long shopId, String warehouseCode, Long cpCPhyWarehouseId
            , String downTimeStr) {
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("begin：shopId/warehouseCode" +
                    "/cpCPhyWarehouseId/downTimeStr=", shopId, warehouseCode, cpCPhyWarehouseId, downTimeStr));
        }
        Date curDate = new Date();
        VipcomProjectAndItemRequest vipcomProjectAndItemRequest = getVipcomProjectAndItemRequestByShopId(shopId
                , curDate, warehouseCode, cpCPhyWarehouseId);
        try {
            List<StCVipcomProjectItemExtDO> filterVipProjectItems = vipcomProjectAndItemRequest
                    .getStCVipcomProjectItemList();
            if (CollectionUtils.isNotEmpty(filterVipProjectItems)) {
                // 根据档期下单时间 获取日程规划明细及归属
                StCVipcomProjectItemExtDO itemDO = getVipProjectItemWithInTenMinutes(filterVipProjectItems, downTimeStr);
                if (itemDO == null) {
                    if (log.isDebugEnabled()) {
                        log.debug(LogUtil.format("failure：shopId/warehouseCode" +
                                "/cpCPhyWarehouseId/downTimeStr=", shopId, warehouseCode, cpCPhyWarehouseId, downTimeStr));
                    }
                    return ValueHolderUtils.getFailValueHolder("获取当前时间的日程归属信息失败");
                }
                List<StCVipcomProjectItemExtDO> stCVipcomProjectItemExtDOS = new ArrayList<>();
                stCVipcomProjectItemExtDOS.add(itemDO);
                vipcomProjectAndItemRequest.setStCVipcomProjectItemList(stCVipcomProjectItemExtDOS);
                if (log.isDebugEnabled()) {
                    log.debug(LogUtil.format("result：success.vipcomProjectAndItemRequest={}," +
                                    "shopId/warehouseCode/cpCPhyWarehouseId/downTimeStr=", shopId, warehouseCode
                            , cpCPhyWarehouseId, downTimeStr), JSON.toJSONString(vipcomProjectAndItemRequest));
                }
                return ValueHolderUtils.custom(ResultCode.SUCCESS, "获取当前时间的日程归属信息成功"
                        , vipcomProjectAndItemRequest);
            }
        } catch (Exception e) {
            log.error(LogUtil.format("error：{},shopId/warehouseCode/cpCPhyWarehouseId/downTimeStr=", shopId
                    , warehouseCode, cpCPhyWarehouseId, downTimeStr), Throwables.getStackTraceAsString(e));
        }
        return ValueHolderUtils.getFailValueHolder("获取当前时间的日程归属信息失败");
    }

    private Predicate<StCVipcomProjectItemExtDO> getStCVipcomProjectItemExtDOPredicate(String currentTime
            , String defaultDownTime) {
        return obj -> obj.getDownloadTime().equals(defaultDownTime) ||
                (StringUtils.isNotBlank(obj.getDownloadTime())
                        && obj.getDownloadTime().substring(0, 4).compareTo(currentTime) >= 0);
    }

    private List<StCVipcomProjectItemDO> getMatchPhyWareHouseAndWareHouseCode(String warehouseCode
            , Long cpCPhyWarehouseId
            , List<StCVipcomProjectItemDO> stCVipcomProjectItemDOS) {
        List<StCVipcomProjectItemDO> filterVipProjectItems = new ArrayList<>();
        for (StCVipcomProjectItemDO itemDO : stCVipcomProjectItemDOS) {
            boolean matchPhyWareHouseId = true;
            if (cpCPhyWarehouseId != null) {
                matchPhyWareHouseId = isMatch(itemDO.getCpCPhyWarehouseId()
                        , cpCPhyWarehouseId.toString());
            }
            boolean matchWarehouseCode = isMatch(itemDO.getCpCOrigEcode(), warehouseCode);
            if (matchPhyWareHouseId && matchWarehouseCode) {
                filterVipProjectItems.add(itemDO);
            }
        }
        return filterVipProjectItems;
    }

    /**
     * 过滤日程规划入库单明细
     *
     * @param tempCpPhyWarehouseId
     * @param whEntryItemList
     * @return StCVipcomProjectWhEntryItem
     */
    private StCVipcomProjectWhEntryItem filterVipcomProjectWhEntryItems(String tempCpPhyWarehouseId, List<StCVipcomProjectWhEntryItem> whEntryItemList) {
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("过滤日程规划入库单明细开始"));
        }
        List<StCVipcomProjectWhEntryItem> filterItem = new ArrayList<>(whEntryItemList.size());
        List<StCVipcomProjectWhEntryItem> lastItem = new ArrayList<>(whEntryItemList.size());
        LocalTime now = LocalTime.now();
        for (StCVipcomProjectWhEntryItem item : whEntryItemList) {
            String warehouseId = item.getCpCPhyWarehouseId();
            if (org.apache.commons.lang3.StringUtils.isEmpty(warehouseId) ||
                    !warehouseId.contains(tempCpPhyWarehouseId) ||
                    org.apache.commons.lang3.StringUtils.isEmpty(item.getStatementTime())) {
                continue;
            }
            //获取的仓库为","分割的仓库id拼接
            String[] warehouseIdArr = warehouseId.split(",");
            for (String str : warehouseIdArr) {
                if (str.equalsIgnoreCase(tempCpPhyWarehouseId)) {
                    if (now.isBefore(LocalTime.parse(item.getStatementTime()))) {
                        filterItem.add(item);
                    }else {
                        lastItem.add(item);
                    }
                }
            }
        }

        //没有满足当前配货单明细仓库相关的规划
        if(CollectionUtils.isEmpty(filterItem) && CollectionUtils.isEmpty(lastItem)){
            return null;
        }

        //日程规划升序排序
        filterItem.sort(Comparator.comparing(StCVipcomProjectWhEntryItem::getStatementTime));
        lastItem.sort(Comparator.comparing(StCVipcomProjectWhEntryItem::getStatementTime));
        //多条数据需根据结单时间进行排序 获取当前时间往后的第一条规划,追至次日
        StCVipcomProjectWhEntryItem next;

        int intervel = 0;
        if (CollectionUtils.isEmpty(filterItem)) {
            next = lastItem.get(0);
            intervel = 1;
        }else if(CollectionUtils.isEmpty(lastItem)){
            next = filterItem.get(0);
        }else {
            next = filterItem.get(0);
        }

        StCVipcomProjectWhEntryItem result = new StCVipcomProjectWhEntryItem();
        BeanUtils.copyProperties(next,result);

        result.setSendInterval(String.valueOf(Integer.valueOf(result.getSendInterval()) + intervel));
        if (org.apache.commons.lang3.StringUtils.isNotBlank(result.getArrivaltime())) {
            result.setArrivalInterval(String.valueOf(Integer.valueOf(result.getArrivalInterval()) + intervel));
        }

        //转换发货时间、到货时间
//        String deliveryTime = parseVipTime(result.getSendtime(),
//                StParamConstants.TWO.equals(result.getSendInterval()) ? intervel ++ : intervel);
//        if (deliveryTime == null) {
//            log.error(" 预计发货时间deliveryTime转换失败！");
//            return null;
//        }
//        result.setSendtime(deliveryTime);

//        if (org.apache.commons.lang3.StringUtils.isNotBlank(result.getArrivaltime())) {
//            String arrivalTime = parseVipTime(result.getArrivaltime(),
//                    StParamConstants.TWO.equals(result.getArrivalInterval()) ? intervel + 1 : intervel);
//            if (arrivalTime == null) {
//                log.error(" 预计到货时间arrivalTime转换失败！");
//                return null;
//            }
//            result.setArrivaltime(arrivalTime);
//        }

        return result;

    }

    /**
     * 获取到货发货时间
     *
     * @param time 时分秒
     * @param days 间隔天数
     */
    private String parseVipTime(String time, int days) {
        Date d = new Date();
        try {
            SimpleDateFormat format1 = new SimpleDateFormat("yyyy-MM-dd");
            SimpleDateFormat format2 = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            String curDate = format1.format(d);
            String dateTime = curDate + " " + time;
            Date newDate = format2.parse(dateTime);
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(newDate);
            calendar.add(Calendar.DAY_OF_MONTH, days);//+1今天的时间加四天
            Date date = calendar.getTime();
            return format2.format(date);
        } catch (ParseException e) {
            log.error(" 创建唯品会入库单，【parseVipTime】获取到货时间异常！！！", e);
        }
        return null;
    }

    /**
     * 是否包含 如 "2,1" 包含 "1" true
     *
     * @param splits
     * @param str
     * @return
     */
    private boolean isMatch(String splits, String str) {
        try {
            if (StringUtils.isBlank(splits)) {
                return false;
            }
            String[] strings = splits.split(",");
            for (String obj : strings) {
                if (obj.equals(str)) {
                    return true;
                }
            }
        } catch (Exception e) {
            log.error(LogUtil.format("isMatch.error：{},splits/str=", splits, str), Throwables.getStackTraceAsString(e));
        }
        return false;
    }

    /**
     * 获取十分钟类的日程规划明细
     *
     * @param sourceList
     * @param downTimeStr
     */
    private StCVipcomProjectItemExtDO getVipProjectItemWithInTenMinutes(List<StCVipcomProjectItemExtDO> sourceList
            , String downTimeStr) {
        if (CollectionUtils.isEmpty(sourceList) || StringUtils.isBlank(downTimeStr) || downTimeStr.length() < 4) {
            return null;
        }
        for (StCVipcomProjectItemExtDO itemExtDO : sourceList) {
            if (isWithInTenMinutes(itemExtDO.getDownloadTime(), downTimeStr)) {
                return itemExtDO;
            }
        }
        return null;
    }

    private boolean isWithInTenMinutes(String orginTime, String currentTime) {
        if (StringUtils.isBlank(orginTime) || StringUtils.isBlank(currentTime)
                || orginTime.length() < 4 || currentTime.length() < 4) {
            return false;
        }
        String originSubstring = orginTime.substring(0, 4);
        String currentSubstring = currentTime.substring(0, 4);
        if (originSubstring.equals(currentSubstring)) {
            return true;
        }
        return false;
    }

    /**
     * 处理小时 如
     * 1、00 为 "0"
     * 1、01 为 "1"
     * 1、10 为 "10"
     *
     * @param hourStr
     * @return
     */
    private String getSubHourStr(String hourStr) {
        if (StringUtils.isBlank(hourStr)) {
            return null;
        }
        if ("00".equals(hourStr)) {
            //0点特殊处理
            return "0";
        } else {
            //截取的小时小于10点会有0
            return hourStr.replaceAll("^(0+)", "");
        }
    }

    public ValueHolderV14<VipcomProjectRequest> getVipcomProjectAndItem(Long shopId, Long ascriptionId, String downTimeStr) {
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("getVipcomProjectAndItem.param：shopId/ascriptionId/downTimeStr=",
                    shopId, ascriptionId, downTimeStr));
        }

        ValueHolderV14<VipcomProjectRequest> vh = new ValueHolderV14<>();

        try {
            FastDateFormat fastDateFormat = FastDateFormat.getInstance("yyyy-MM-dd HH:mm:ss");
            Date downDate = fastDateFormat.parse(downTimeStr);

            List<StCVipcomProjectDO> vipcomProjectDOS = stCVipcomProjectMapper.selectProjectByShopId(shopId, downDate);

            if (CollectionUtils.isEmpty(vipcomProjectDOS)) {
                vh.setCode(ResultCode.FAIL);
                vh.setMessage("根据店铺ID加下载时间没有获取到档期日程规划！");
                return vh;
            }

            VipcomProjectRequest request = new VipcomProjectRequest();

            StCVipcomProjectDO vipcomProjectDO = vipcomProjectDOS.get(0);
            List<StCVipcomProjectItemDO> vipcomProjectItemDOS =
                    stCVipcomProjectItemMapper.selectListByAscriptionId(vipcomProjectDO.getId(), ascriptionId);
            if (CollectionUtils.isEmpty(vipcomProjectDOS)) {
                if (log.isDebugEnabled()) {
                    log.debug(LogUtil.format("根据档期日程规划ID和归属ID没有获取到明细，规划ID/归属ID=", vipcomProjectDO.getId(),
                            ascriptionId));
                }
                vh.setCode(ResultCode.FAIL);
                vh.setMessage("根据档期日程规划ID和归属ID没有获取到明细");
                return vh;
            }
            request.setStCVipcomProject(vipcomProjectDO);
            //request.setStCVipcomProjectItemList(vipcomProjectItemDOS);

            vh.setCode(ResultCode.SUCCESS);
            vh.setMessage("success");
            vh.setData(request);
        } catch (ParseException e) {
            log.error(LogUtil.format("获取档期日程规划信息异常{}"), Throwables.getStackTraceAsString(e));
            vh.setCode(ResultCode.FAIL);
            vh.setMessage("获取档期日程规划信息异常：" + e.getMessage());
        }

        return vh;
    }

}
