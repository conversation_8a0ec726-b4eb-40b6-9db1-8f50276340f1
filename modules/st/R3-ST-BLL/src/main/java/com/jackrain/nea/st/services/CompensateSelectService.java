package com.jackrain.nea.st.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.st.mapper.StCCompensateLogisticsMapper;
import com.jackrain.nea.st.mapper.StCCompensateMapper;
import com.jackrain.nea.st.mapper.StCCompensateWarehouseMapper;
import com.jackrain.nea.st.model.table.StCCompensateDO;
import com.jackrain.nea.st.model.table.StCCompensateLogisticsDO;
import com.jackrain.nea.st.model.table.StCCompensateWarehouseDO;
import com.jackrain.nea.st.utils.ValueHolderUtils;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.List;
import java.util.Locale;

/**
 * <AUTHOR>
 * @Date 2019/4/18 15:00
 * 快递赔付编辑页数据查询接口（定制）
 */
@Component
@Slf4j
@Transactional
public class CompensateSelectService extends CommandAdapter {

    @Autowired
    private StCCompensateMapper stCCompensateMapper;

    @Autowired
    private StCCompensateLogisticsMapper stCCompensateLogisticsMapper;

    @Autowired
    private StCCompensateWarehouseMapper stCCompensateWarehouseMapper;
    /**
     * @param querySession
     * @return
     * <AUTHOR>
     * @Date 2019/4/18 15:00
     */

    @Override
    public ValueHolder execute(QuerySession querySession) throws NDSException {
        ValueHolder result = new ValueHolder();
        DefaultWebEvent event = querySession.getEvent();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(
                event.getParameterValue("param"), "yyyy-MM-dd HH:mm:ss"), Feature.OrderedField);
        Long objid = param.getLong("objid");
        //查询主表跟明细
        if(objid== null || objid <= 0){
            return ValueHolderUtils.getFailValueHolder("查询失败，请确认是否存在这条记录！");
        }
        result = selectCompensate(objid);
        return result;
    }

    /**
     * @param id 快递主表id
     * @return
     * @Author: 朱宇军
     * @Date 2019/3/12
     */
    private ValueHolder selectCompensate(Long id) {
        ValueHolder result = new ValueHolder();
        //主表根据id查询数据
        StCCompensateDO stCCompensateDO =  stCCompensateMapper.selectById(id);
        //明细表根据外键id查询对应明细数据
        List<StCCompensateLogisticsDO> stCCompensateLogisticsDOList = stCCompensateLogisticsMapper.selectLogisticsBySlaverId(id);
        List<StCCompensateWarehouseDO> stCCompensateWarehouseDOList = stCCompensateWarehouseMapper.selectWarehouseBySlaverId(id);
        String cpCPhyWarehouseEname = "";
        for (StCCompensateWarehouseDO compensateWarehouseDO : stCCompensateWarehouseDOList) {
            cpCPhyWarehouseEname = cpCPhyWarehouseEname + "," + compensateWarehouseDO.getCpCPhyWarehouseEname();
        }
        if (!StringUtils.isBlank(cpCPhyWarehouseEname)) {
            cpCPhyWarehouseEname = StringUtils.substring(cpCPhyWarehouseEname, 1, cpCPhyWarehouseEname.length());
        }
        //返回格式组装容器
        HashMap map = new HashMap();
        if(stCCompensateDO != null ){
            //组装主表和明细表数据返回格式
            stCCompensateDO.setCpCPhyWarehouseEname(cpCPhyWarehouseEname);
            map.put("ST_C_COMPENSATE",stCCompensateDO);
            map.put("ST_C_COMPENSATE_LOGISTICS",stCCompensateLogisticsDOList);
            map.put("code", ResultCode.SUCCESS);
            map.put("message", Resources.getMessage("查询成功！", new Locale("zh", "CN")));
            result.setData(map);
        }else{
            map.put("code", -2);
            map.put("message", Resources.getMessage("查询失败，请确认是否存在这条记录！", new Locale("zh", "CN")));
            result.setData(map);
        }
        return result;
    }

}
