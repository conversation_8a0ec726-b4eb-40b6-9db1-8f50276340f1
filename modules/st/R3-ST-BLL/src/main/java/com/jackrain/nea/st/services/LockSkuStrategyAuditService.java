package com.jackrain.nea.st.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.google.common.base.Throwables;
import com.google.common.collect.Lists;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.st.common.StConstant;
import com.jackrain.nea.st.mapper.StCLockSkuStrategyItemMapper;
import com.jackrain.nea.st.mapper.StCLockSkuStrategyMapper;
import com.jackrain.nea.st.model.table.StCLockSkuStrategyDO;
import com.jackrain.nea.st.model.table.StCLockSkuStrategyItemDO;
import com.jackrain.nea.st.utils.DatasToEsUtils;
import com.jackrain.nea.st.utils.ListUtils;
import com.jackrain.nea.st.utils.StBeanUtils;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

@Component
@Slf4j
@Transactional
public class LockSkuStrategyAuditService extends CommandAdapter {
    @Autowired
    private StCLockSkuStrategyMapper stCLockSkuStrategyMapper;

    @Autowired
    private StCLockSkuStrategyItemMapper stCLockSkuStrategyItemMapper;

//    @Reference(group = "sg", version = "1.0")
//    private SgLockSkuStrategyBasicCmd skuStrategyBasicCmd;
//
//    @Autowired
//    private ShopLockSkuSyncStockService skuSyncStockService;


    /**
     * 审核
     *
     * @param querySession 参数封装
     * @return 返回状态
     * @throws NDSException 异常信息
     */
    @Override
    public ValueHolder execute(QuerySession querySession) throws NDSException {
        ValueHolder valueHolder = new ValueHolder();
        DefaultWebEvent event = querySession.getEvent();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(
                event.getParameterValue("param"), "yyyy-MM-dd HH:mm:ss"),
                Feature.OrderedField);

        if (param != null) {
            JSONArray itemArray = StBeanUtils.makeAuditJsonArray(param);
            valueHolder = auditLockSkuStrategy(itemArray, valueHolder, querySession);
        } else {
            throw new NDSException("参数为空！");
        }
        return valueHolder;
    }

    private JSONObject checkStatus(StCLockSkuStrategyDO stCLockSkuStrategyDO, Long objId, JSONObject errJo) {
        int iStatus = stCLockSkuStrategyDO.getEstatus();
        if (iStatus != StConstant.CON_BILL_STATUS_01) {
            errJo.put("objid", objId);
            errJo.put("message", "单据处于未审核状态才能进行审核！");
            return errJo;
        }

        if (stCLockSkuStrategyDO.getLockEtime().compareTo(new Date()) <= 0) {
            errJo.put("objid", objId);
            errJo.put("message", "结束时间小于或等于当前系统时间，不允许审核！");
            return errJo;
        }

        int count = stCLockSkuStrategyItemMapper.selectCountByItemId(objId);
        if (count <= 0) {
            errJo.put("objid", objId);
            errJo.put("message", "当前方案明细没有记录，不允许审核！");
            return errJo;
        }
        return null;
    }

    private ValueHolder auditLockSkuStrategy(JSONArray itemArray, ValueHolder valueHolder, QuerySession querySession) {
        //审核记录前，先判断是否存在
        int size = itemArray.size();
        if (size > 0) {
            List<StCLockSkuStrategyDO> cLockSkuStrategyList = stCLockSkuStrategyMapper.
                    selectBatchIds(JSONObject.parseArray(itemArray.toJSONString(), Long.class));
            JSONArray errorArray = new JSONArray();
            // 同步策略到PG库
            List<Long> ids = Lists.newArrayListWithExpectedSize(size);
            for (StCLockSkuStrategyDO cLockSkuStrategyDO : cLockSkuStrategyList) {
                JSONObject errJo = new JSONObject();

                /* if (stCLockSkuStrategyDO != null) {*/
                Long objId = cLockSkuStrategyDO.getId();
                JSONObject errorJson = checkStatus(cLockSkuStrategyDO, objId, errJo);
                if (errorJson != null) {
                    errorArray.add(errorJson);
                } else {
                    StBeanUtils.makeModifierField(cLockSkuStrategyDO, querySession.getUser());
                    //BILL_STATUS    1:未审核 2:已审核 3:已作废 4:已结案
                    cLockSkuStrategyDO.setEstatus(StConstant.CON_BILL_STATUS_02);
                    //审核人 审核时间 审核人姓名 审核人账号
                    cLockSkuStrategyDO.setCheckid(Long.valueOf(querySession.getUser().getId()));
                    cLockSkuStrategyDO.setChecktime(new Date());
                    cLockSkuStrategyDO.setCheckname(querySession.getUser().getName());
                    cLockSkuStrategyDO.setCheckename(querySession.getUser().getEname());
                    int iResult = stCLockSkuStrategyMapper.updateById(cLockSkuStrategyDO);
                    if (iResult < 0) {
                        errJo.put("objid", objId);
                        errJo.put("message", "审核失败！");
                        errorArray.add(errJo);
                    } else {
                        ids.add(objId);
                        //推送ES数据
                        try {
                            //做更新的需要先查询更新后数据库的实体在推ES
                            cLockSkuStrategyDO = stCLockSkuStrategyMapper.selectById(objId);
                            StCLockSkuStrategyItemDO item = new StCLockSkuStrategyItemDO();
                            item.setStatus(StConstant.CON_BILL_STATUS_02);
                            item.setPlanName(cLockSkuStrategyDO.getEname());
                            item.setBeginTime(cLockSkuStrategyDO.getLockBtime());
                            item.setEndTime(cLockSkuStrategyDO.getLockEtime());
                            item.setCpCShopId(cLockSkuStrategyDO.getCpCShopId());
                            item.setCpCShopEcode(cLockSkuStrategyDO.getCpCShopEcode());
                            item.setCpCShopTitle(cLockSkuStrategyDO.getCpCShopTitle());
                            item.setMainCreationdate(cLockSkuStrategyDO.getCreationdate());
                            QueryWrapper<StCLockSkuStrategyItemDO> wrapper = new QueryWrapper<>();
                            wrapper.eq("st_c_lock_sku_strategy_id", objId);
                            stCLockSkuStrategyItemMapper.update(item, wrapper);
                            List<StCLockSkuStrategyItemDO> itemList = ListUtils.batchQueryByCondition(wrapper, stCLockSkuStrategyItemMapper, 1000);
                            DatasToEsUtils.insertLoclSkuEsData(cLockSkuStrategyDO, null, StConstant.TAB_ST_C_LOCK_SKU_STRATEGY);
                            if (CollectionUtils.isNotEmpty(itemList)) {
                                DatasToEsUtils.insertLoclSkuEsData(cLockSkuStrategyDO, itemList, StConstant.TAB_ST_C_LOCK_SKU_STRATEGY_ITEM);
                            }
                        } catch (Exception ex) {
                            log.debug(LogUtil.format("店铺锁库条码特殊设置策略主表审核推数据到ES失败：{}"),
                                    Throwables.getStackTraceAsString(ex));
                        }
                    }
                }

        }
        // 修改状态为审核
//        skuStrategyBasicCmd.status(ids, StConstant.CON_BILL_STATUS_02);
        valueHolder = StBeanUtils.getProcessValueHolder(itemArray, errorArray, "审核");
        return valueHolder;
    }
        return valueHolder;
}
}
