package com.jackrain.nea.st.validate;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.st.common.StConstant;
import com.jackrain.nea.tableService.MainTableRecord;
import com.jackrain.nea.tableService.TableServiceContext;
import com.jackrain.nea.tableService.validate.BaseValidator;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.utils.AssertUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * Description： 作废校验
 * Author: RESET
 * Date: Created in 2020/7/1 19:49
 * Modified By:
 */
@Slf4j
@Component
public class StCLiveCastVoidValidate extends BaseValidator {

    @Override
    public void validate(TableServiceContext tableServiceContext, MainTableRecord mainTableRecord) {
        log.info(LogUtil.format("live.validate.mainTableRecord.getCommitData:{}")
                , JSON.toJSONString(mainTableRecord.getCommitData()));
        log.info(LogUtil.format("live.validate.mainTableRecord.getOrignalData:{}")
                , JSON.toJSONString(mainTableRecord.getOrignalData()));
        log.info(LogUtil.format("live.validate.mainTableRecord.getId:{}")
                , JSON.toJSONString(mainTableRecord.getId()));

        // 基础校验
        validateVoidBase(tableServiceContext, mainTableRecord);
        // 校验状态
        checkStatus(tableServiceContext, mainTableRecord);
        // 设置状态
        JSONObject data = mainTableRecord.getMainData().getCommitData();

        if (Objects.nonNull(data)) {
            data.put(StCLiveConsts.STRATEGY_STATUS, StConstant.LIVE_STRATEGY_STATUS_VOID);
        }
    }

    /**
     * 当前状态校验
     * @param tableServiceContext
     * @param mainTableRecord
     */
    private void checkStatus(TableServiceContext tableServiceContext, MainTableRecord mainTableRecord) {
        JSONObject data = mainTableRecord.getMainData().getOrignalData();

        if (Objects.nonNull(data)) {
            // 当前状态
            Integer status = data.getInteger(StCLiveConsts.STRATEGY_STATUS);
            AssertUtils.cannot(StConstant.LIVE_STRATEGY_STATUS_VOID.equals(status), "当前已经是作废状态！", tableServiceContext.getLocale());
        }
    }

    /**
     * 作废的校验
     * @param context
     * @param row
     */
    private void validateVoidBase(TableServiceContext context, MainTableRecord row) {
        Long objId = row.getId();
        if (objId == null) {
            throw new NDSException(Resources.getMessage("请先选择需要作废的记录！", context.getLocale(), new Object[0]));
        } else {
            JSONObject data = row.getMainData().getOrignalData();
            if (data == null) {
                throw new NDSException(Resources.getMessage("当前记录已不存在！", context.getLocale(), new Object[0]));
            } else {
                log.debug(LogUtil.format("void data====>") + data.toJSONString());
                Integer status = data.getInteger("STATUS");
                String isactive = data.getString("ISACTIVE");
                if ("N".equals(isactive)) {
                    throw new NDSException(Resources.getMessage("当前记录已作废,不允许重复作废！", context.getLocale(), new Object[0]));
                } else if (status != null && (status == 2 || status == 3)) {
                    throw new NDSException(Resources.getMessage("当前记录不是未审核状态,不允许作废！", context.getLocale(), new Object[0]));
                }
            }
        }
    }

}
