package com.jackrain.nea.st.services;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.jackrain.nea.st.mapper.StCDepositPreSaleSinkItemMapper;
import com.jackrain.nea.st.mapper.StCDepositPreSaleSinkLogisticsMapper;
import com.jackrain.nea.st.mapper.StCDepositPreSaleSinkMapper;
import com.jackrain.nea.st.model.request.StCDepositPreSaleSinkRequest;
import com.jackrain.nea.st.model.table.StCDepositPreSaleSinkDO;
import com.jackrain.nea.st.model.table.StCDepositPreSaleSinkItemDO;
import com.jackrain.nea.st.model.table.StCDepositPreSaleSinkLogisticsDO;
import com.jackrain.nea.st.model.table.StCDepositPreSaleSinkLogisticsDO;
import com.jackrain.nea.utility.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

@Component
@Slf4j
public class StCDepositPreSaleSinkQueryService {
    @Autowired
    private StCDepositPreSaleSinkMapper stCDepositPreSaleSinkMapper;

    @Autowired
    private StCDepositPreSaleSinkItemMapper stCDepositPreSaleSinkItemMapper;

    @Autowired
    private StCDepositPreSaleSinkLogisticsMapper stCDepositPreSaleSinkLogisticsMapper;


    /**
     * description: 查询定金预售满足的仓库
     * @Author:  liuwenjin
     * @Date 2021/9/24 11:26 上午
     */
    public List<StCDepositPreSaleSinkRequest> queryDepositPreSaleSink(Long cpCPhyWarehouse) {
        log.info(LogUtil.format("StCDepositPreSaleSinkQueryService.queryDepositPreSaleSink.param :{}"),cpCPhyWarehouse);
        if (cpCPhyWarehouse == null){
            log.info(LogUtil.format("StCDepositPreSaleSinkQueryService.queryDepositPreSaleSink.cpCPhyWarehouse 为空"));
            return null;
        }
        List<StCDepositPreSaleSinkRequest> stCDepositPreSaleSinkRequestList = new ArrayList<>();
        List<StCDepositPreSaleSinkDO> stCDepositPreSaleSinkDOList = stCDepositPreSaleSinkMapper.selectByWarehouseId(cpCPhyWarehouse.toString());
        if (CollectionUtils.isNotEmpty(stCDepositPreSaleSinkDOList)){
            for (StCDepositPreSaleSinkDO stCDepositPreSaleSinkDO : stCDepositPreSaleSinkDOList) {
                StCDepositPreSaleSinkRequest stCDepositPreSaleSinkRequest =new StCDepositPreSaleSinkRequest();
                stCDepositPreSaleSinkRequest.setStCDepositPreSaleSinkDO(stCDepositPreSaleSinkDO);
                List<StCDepositPreSaleSinkItemDO> stCDepositPreSaleSinkItemDOList = stCDepositPreSaleSinkItemMapper.selectList(new QueryWrapper<StCDepositPreSaleSinkItemDO>().lambda()
                        .eq(StCDepositPreSaleSinkItemDO :: getStCDepositPreSaleSinkId,stCDepositPreSaleSinkDO.getId())
                        .eq(StCDepositPreSaleSinkItemDO :: getIsactive,"Y"));
                if (CollectionUtils.isNotEmpty(stCDepositPreSaleSinkItemDOList)){
                    stCDepositPreSaleSinkRequest.setStCDepositPreSaleSinkItemDOList(stCDepositPreSaleSinkItemDOList);
                }
                List<StCDepositPreSaleSinkLogisticsDO> stCDepositPreSaleSinkLogisticsDOList = stCDepositPreSaleSinkLogisticsMapper.selectList(new QueryWrapper<StCDepositPreSaleSinkLogisticsDO>().lambda()
                        .eq(StCDepositPreSaleSinkLogisticsDO :: getStCDepositPreSaleSinkId,stCDepositPreSaleSinkDO.getId())
                        .eq(StCDepositPreSaleSinkLogisticsDO :: getIsactive,"Y"));
                if (CollectionUtils.isNotEmpty(stCDepositPreSaleSinkLogisticsDOList)){
                    stCDepositPreSaleSinkRequest.setStCDepositPreSaleSinkLogisticsDOList(stCDepositPreSaleSinkLogisticsDOList);
                }
                stCDepositPreSaleSinkRequestList.add(stCDepositPreSaleSinkRequest);
            }
            return stCDepositPreSaleSinkRequestList;
        }
        log.info(LogUtil.format("StCDepositPreSaleSinkQueryService.queryDepositPreSaleSink.返回数据为: {}"),
                JSON.toJSONString(stCDepositPreSaleSinkRequestList));
       return null;
    }
}
