package com.jackrain.nea.st.services;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.st.mapper.StCAllocationStorageCostStrategyMapper;
import com.jackrain.nea.st.model.table.StCAllocationStorageCostStrategy;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2023/8/16 18:33
 * @Description
 */
@Component
@Slf4j
public class StCAllocationStorageCostQueryService {

    @Resource
    private StCAllocationStorageCostStrategyMapper mapper;

    public ValueHolderV14<List<StCAllocationStorageCostStrategy>> queryByWarehouseIds(List<Long> warehouseIds) {
        ValueHolderV14<List<StCAllocationStorageCostStrategy>> holderV14 = new ValueHolderV14<>(ResultCode.SUCCESS, "success");
        if (CollectionUtils.isEmpty(warehouseIds)) {
            holderV14.setCode(ResultCode.FAIL);
            holderV14.setMessage("仓库为空！");
            return holderV14;
        }
        List<StCAllocationStorageCostStrategy> strategyList =
                mapper.selectList(new LambdaQueryWrapper<StCAllocationStorageCostStrategy>()
                        .in(StCAllocationStorageCostStrategy::getCpCPhyWarehouseId, warehouseIds));
        holderV14.setData(strategyList);
        return holderV14;
    }
}
