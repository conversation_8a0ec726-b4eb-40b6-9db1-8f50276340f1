package com.jackrain.nea.st.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.google.common.base.Throwables;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.st.mapper.StCDewuWarehouseConfigMapper;
import com.jackrain.nea.st.model.table.StCDewuWarehouseConfig;
import com.jackrain.nea.st.utils.ValueHolderUtils;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

/**
 * 得物仓库配置表删除服务
 */
@Component
@Slf4j
@Transactional
public class StCDewuWarehouseConfigDeleteService extends CommandAdapter {

    @Autowired
    private StCDewuWarehouseConfigMapper stCDewuWarehouseConfigMapper;

    /**
     * 物理删除得物仓库配置
     *
     * @param querySession 查询会话
     * @return 删除结果
     */
    @Override
    public ValueHolder execute(QuerySession querySession) {
        log.debug(LogUtil.format("开始物理删除得物仓库配置"));

        DefaultWebEvent event = querySession.getEvent();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(event.getParameterValue("param"),
                "yyyy-MM-dd HH:mm:ss", SerializerFeature.WriteMapNullValue), Feature.OrderedField);

        if (param == null) {
            return ValueHolderUtils.getFailValueHolder("参数为空！");
        }
        Long id = param.getLong("objid");
        if (id == null) {
            return ValueHolderUtils.getFailValueHolder("删除失败，ID不能为空");
        }

        log.debug(LogUtil.format("物理删除得物仓库配置，ID: {}"), id);

        try {
            // 先查询记录是否存在
            StCDewuWarehouseConfig config = stCDewuWarehouseConfigMapper.selectById(id);
            if (config == null) {
                return ValueHolderUtils.getFailValueHolder("删除失败，记录不存在");
            }

            // 执行物理删除
            int count = stCDewuWarehouseConfigMapper.deleteById(id);
            if (count > 0) {
                log.debug(LogUtil.format("物理删除得物仓库配置成功，ID: {}"), id);
                return ValueHolderUtils.getSuccessValueHolder("删除成功");
            } else {
                log.error(LogUtil.format("物理删除得物仓库配置失败，ID: {}"), id);
                return ValueHolderUtils.getFailValueHolder("删除失败");
            }
        } catch (Exception ex) {
            log.error(LogUtil.format("物理删除得物仓库配置异常，ID: {}, 异常: {}"), id, Throwables.getStackTraceAsString(ex));
            throw new NDSException("删除得物仓库配置异常: " + ex.getMessage());
        }
    }
}
