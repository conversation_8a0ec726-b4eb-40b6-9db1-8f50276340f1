package com.jackrain.nea.st.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.google.common.base.Throwables;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.st.common.StConstant;
import com.jackrain.nea.st.mapper.StCRefundOrderStrategyMapper;
import com.jackrain.nea.st.model.request.RefundOrderStrategyRequest;
import com.jackrain.nea.st.model.table.StCRefundOrderStrategyDO;
import com.jackrain.nea.st.model.table.StCVipcomProjectDO;
import com.jackrain.nea.st.utils.JsonUtils;
import com.jackrain.nea.st.utils.StBeanUtils;
import com.jackrain.nea.st.utils.ValueHolderUtils;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2019/3/8 11:06
 */
@Component
@Slf4j
@Transactional
public class RefundOrderSaveService extends CommandAdapter {
    @Autowired
    private StCRefundOrderStrategyMapper refundMapper;

    public ValueHolder execute(QuerySession session) throws NDSException {
        DefaultWebEvent event = session.getEvent();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(
                event.getParameterValue("param"), "yyyy-MM-dd HH:mm:ss", SerializerFeature.WriteMapNullValue),
                Feature.OrderedField);
        if (param == null) {
            throw new NDSException("参数为空！");
        }
        log.debug(LogUtil.format("Start DistributionAuditService.execute. ReceiveParams: {}"), param.toJSONString());
        Long id = param.getLong("objid");
        JSONObject fixColumn = param.getJSONObject("fixcolumn");
        RefundOrderStrategyRequest refundorderRequest = JsonUtils.jsonParseClass(fixColumn, RefundOrderStrategyRequest.class);
        if (fixColumn != null && id != null) {
            if (id != -1) {
                return updateRefundOrderStrategy(session, id, refundorderRequest);
            } else {
                return insertRefundOrderStrategy(session, refundorderRequest);
            }
        }
        throw new NDSException("当前记录已不存在！");
    }

    /**
     * 日程计划更新
     *
     * @param session
     * @param id
     * @param refundOrderStrategyRequest
     * @return com.jackrain.nea.util.ValueHolder
     * @Author: 陈秀楼
     * @Date 2019/3/8
     */
    private ValueHolder updateRefundOrderStrategy(QuerySession session, Long id, RefundOrderStrategyRequest refundOrderStrategyRequest) {
        try {
            StCRefundOrderStrategyDO refundOrder = refundOrderStrategyRequest.getStCRefundOrderStrategy();
            if (refundOrder == null) {
                log.error(LogUtil.format("RefundOrderStrategySaveService.updateRefundOrderStrategy Error"), "实体为空");
                return ValueHolderUtils.getFailValueHolder("更新失败");
            }
            ValueHolder check = checkRefundOrderByFilter(-id,refundOrder, "update");
            if (check != null) {
                return check;
            }

            //1. 数据处理
            refundOrder.setId(id);
            StBeanUtils.makeModifierField(refundOrder, session.getUser());
            if (refundMapper.updateById(refundOrder) > 0) {
                return ValueHolderUtils.getSuccessValueHolder(id, StConstant.TAB_ST_C_REFUND_ORDER_STRATEGY);
            } else {
                return ValueHolderUtils.getFailValueHolder("更新失败");
            }
        } catch (Exception ex) {
            log.error(LogUtil.format("RefundOrderStrategySaveService.updateRefundOrderStrategy Error{}"), Throwables.getStackTraceAsString(ex));
            return ValueHolderUtils.getFailValueHolder("保存异常");
        }
    }

    /**
     * 退货审核策略 插入
     *
     * @param session
     * @param refundOrderStrategyRequest
     * @return com.jackrain.nea.util.ValueHolder
     * @Author: 陈秀楼
     * @Date 2019/3/8
     */
    private ValueHolder insertRefundOrderStrategy(QuerySession session, RefundOrderStrategyRequest refundOrderStrategyRequest) {
        try {
            StCRefundOrderStrategyDO refundOrder = refundOrderStrategyRequest.getStCRefundOrderStrategy();
            if (refundOrder == null) {
                log.error(LogUtil.format("RefundOrderStrategySaveService.insertRefundOrderStrategy Error"), "实体为空");
                return ValueHolderUtils.getFailValueHolder("保存失败！");
            }
            //1. 判断记录是否已存在
            ValueHolder check = checkRefundOrderByFilter(-1L,refundOrder, "insert");
            if (check != null) {
                return check;
            }
            //2. 插入操作
            long Id = ModelUtil.getSequence(StConstant.TAB_ST_C_REFUND_ORDER_STRATEGY);
            refundOrder.setId(Id);
            //基本字段值设置
            StBeanUtils.makeCreateField(refundOrder, session.getUser());
            int insertResult = refundMapper.insert(refundOrder);
            if (insertResult > 0) {
                return ValueHolderUtils.getSuccessValueHolder(refundOrder.getId(), StConstant.TAB_ST_C_REFUND_ORDER_STRATEGY);
            } else {
                return ValueHolderUtils.getFailValueHolder("保存失败！");
            }
        } catch (Exception ex) {
            log.error(LogUtil.format("RefundOrderStrategySaveService.insertRefundOrderStrategy Error{}"), Throwables.getStackTraceAsString(ex));
            return ValueHolderUtils.getFailValueHolder("保存异常！");
        }
    }

    /**
     * 检查数据
     *
     * @param Id
     * @param refundOrder
     * @param action
     * @return boolean
     * @Author: 陈秀楼
     * @Date 2019/3/11
     */
    private ValueHolder checkRefundOrderByFilter(Long Id, StCRefundOrderStrategyDO refundOrder, String action) {
        String shopId = String.valueOf(refundOrder.getCpCShopId());
        Date beginTime = refundOrder.getBeginTime();
        Date endTime = refundOrder.getEndTime();
        //时间判断
        if (beginTime != null && endTime != null) {
            if (endTime.compareTo(beginTime) < 0) {
                return ValueHolderUtils.getFailValueHolder("结束日期不能小于开始日期！");
            }
        }
        switch (action) {
            case "insert":
                //店铺判断
                Map<String, String> result = new HashMap();
                HashMap<String, Object> map = new HashMap<>();
                map.put("cp_c_shop_id", shopId);
                map.put("isactive",StConstant.ISACTIVE_N);
                List<StCRefundOrderStrategyDO> refundOrders = refundMapper.selectByMap(map);
                if (!refundOrders.isEmpty()) {
                    return ValueHolderUtils.getFailValueHolder("当前店铺已存在！");
                }
//                //状态判断
//                for (int i = 0; i < refundOrders.size(); i++) {
//                    StCRefundOrderStrategyDO refund = refundOrders.get(i);
//                    if (refundOrder.getIsactive().equals(StConstant.ISACTIVE_N)) {
//                        return ValueHolderUtils.getFailValueHolder("当前记录已作废，不允许操作！");
//                    }
//                }
                break;
            case "update":
                StCRefundOrderStrategyDO existsRefundOrder = refundMapper.selectById(Id);
                if (existsRefundOrder == null) {
                    return ValueHolderUtils.getFailValueHolder("当前记录已不存在！");
                }
                break;
        }
        return null;
    }

}
