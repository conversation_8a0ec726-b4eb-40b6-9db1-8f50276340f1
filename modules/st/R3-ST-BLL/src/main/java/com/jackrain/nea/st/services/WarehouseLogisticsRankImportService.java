package com.jackrain.nea.st.services;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.google.common.base.Throwables;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.st.config.R3OssConfig;
import com.jackrain.nea.st.mapper.StCWarehouseLogisticsItemMapper;
import com.jackrain.nea.st.mapper.StCWarehouseLogisticsRankMapper;
import com.jackrain.nea.st.model.result.LogisticsRankResult;
import com.jackrain.nea.st.model.result.RegionTreeResult;
import com.jackrain.nea.st.model.result.StErrMsgResult;
import com.jackrain.nea.st.model.result.WarehouseLogisticsRankResult;
import com.jackrain.nea.st.model.table.StCWarehouseLogisticsItemDO;
import com.jackrain.nea.st.model.table.StCWarehouseLogisticsRankDO;
import com.jackrain.nea.st.utils.ExportUtil;
import com.jackrain.nea.st.utils.RegionTreeUtils;
import com.jackrain.nea.st.utils.StBeanUtils;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @author:huang.zaizai
 * @since: 2019/8/15
 * @create at : 2019/8/15 14:14
 */
@Component
@Slf4j
public class WarehouseLogisticsRankImportService extends CommandAdapter {

    //导入excel,业务校验错误提示返回 错误excel地址
    public static final int IMPORT_ERROR_CODE = 10001;

    @Autowired
    private ExportUtil exportUtil;
    @Autowired
    private RegionTreeQueryService regionTreeQueryService;
    @Autowired
    private R3OssConfig r3OssConfig;
    @Autowired
    private StCWarehouseLogisticsItemMapper itemMapper;
    @Autowired
    private StCWarehouseLogisticsRankMapper rankMapper;

    /**
     * @Description 模板下载
     * @date 2019/8/15 20:00
     * @param
     * @return com.jackrain.nea.sys.domain.ValueHolderV14
     */
    public ValueHolderV14 downloadTemp(JSONObject obj, User user){
        ValueHolderV14 vh = new ValueHolderV14(ResultCode.SUCCESS, "仓库物流优先级明细导入模板下载成功！");
        exportUtil.setEndpoint(r3OssConfig.getEndpoint());
        exportUtil.setAccessKeyId(r3OssConfig.getAccessKeyId());
        exportUtil.setAccessKeySecret(r3OssConfig.getAccessKeySecret());
        exportUtil.setBucketName(r3OssConfig.getBucketName());
        if(StringUtils.isEmpty(r3OssConfig.getTimeout())){
            //如果获取不到apllo配置参数，设置默认过期时间为30分钟
            exportUtil.setTimeout("1800000");
        } else {
            exportUtil.setTimeout(r3OssConfig.getTimeout());
        }
        /**
         *  拼接Excel主表sheet表头字段
         * */
        String mainNames[] = {"省", "市"};
        String mustNames[] = {"省"};
        List<String> mainList = Lists.newArrayList(mainNames);
        List<String> mustList = Lists.newArrayList(mustNames);

        Long id = obj.getLong("objid");
        //获取物流公司信息
        List<StCWarehouseLogisticsItemDO> itemList = itemMapper.selectList(new QueryWrapper<StCWarehouseLogisticsItemDO>()
                .lambda().eq(StCWarehouseLogisticsItemDO::getStCWarehouseLogisticsId, id));
        if (CollectionUtils.isNotEmpty(itemList)) {
            itemList = itemList.stream().sorted(Comparator.comparing(StCWarehouseLogisticsItemDO::getId))
                    .collect(Collectors.toList());
        }
        for (StCWarehouseLogisticsItemDO item : itemList) {
            mainList.add(item.getCpCLogisticsEname());
        }
        //生成Excel
        XSSFWorkbook hssfWorkbook = new XSSFWorkbook();
        exportUtil.executeSheet(hssfWorkbook, "仓库物流优先级明细数据", "", mainList, mustList,
                Lists.newArrayList(), Lists.newArrayList(), false);
        String putMsg = exportUtil.saveFileAndPutOss(hssfWorkbook, "仓库物流优先级明细导入模板",
                user, "OSS-Bucket/EXPORT/StCWarehouseLogisticsRank/");
        vh.setData(putMsg);
        return vh;
    }

    /**
     * @Description 导入
     * @date 2019/8/15 15:02
     * @param rankResultList
     * @param user
     * @return com.jackrain.nea.sys.domain.ValueHolderV14
     */
    public ValueHolderV14 importWarehouseLogisticsRank(Long objid, List<WarehouseLogisticsRankResult> rankResultList, User user){
        ValueHolderV14 vh = new ValueHolderV14();
        if(CollectionUtils.isEmpty(rankResultList)){
            vh.setCode(ResultCode.FAIL);
            vh.setMessage("导入失败,上传文件未录入数据!");
            return vh;
        }
        //保存物流明细
        List<String> errMsgList = saveWarehouseLogisticsRank(objid, rankResultList, user);
        //3.若有错误信息支持导出
        if(errMsgList.size() > 0){
            List<StErrMsgResult> errExcelList = Lists.newArrayList();
            errMsgList.forEach(errMsg->{
                StErrMsgResult errMsgResult = new StErrMsgResult();
                errMsgResult.setErrMsg(errMsg);
                errExcelList.add(errMsgResult);
            });
            int successNum = rankResultList.size() - errMsgList.size();
            vh.setCode(IMPORT_ERROR_CODE);
            vh.setMessage(String.format("导入成功%d条,失败%d条",successNum,errMsgList.size()));
            String sdd = downloadErrMsg(user, errExcelList);
            vh.setData(sdd);
        }else{
            vh.setCode(ResultCode.SUCCESS);
            vh.setMessage("导入成功");
        }
        return vh;
    }

    /**
     * 下载错误信息
     * @param user
     * @param errExcelList
     * @return 错误信息
     */
    private String downloadErrMsg(User user, List<StErrMsgResult> errExcelList) {
        String columnNames[] = {"错误原因"};
        List<String> columnList = Lists.newArrayList(columnNames);
        String keys[] = {"errMsg"};
        List<String> keyList = Lists.newArrayList(keys);
        exportUtil.setEndpoint(r3OssConfig.getEndpoint());
        exportUtil.setAccessKeyId(r3OssConfig.getAccessKeyId());
        exportUtil.setAccessKeySecret(r3OssConfig.getAccessKeySecret());
        exportUtil.setBucketName(r3OssConfig.getBucketName());
        if(StringUtils.isEmpty(r3OssConfig.getTimeout())){
            //如果获取不到apllo配置参数，设置默认过期时间为30分钟
            exportUtil.setTimeout("1800000");
        } else {
            exportUtil.setTimeout(r3OssConfig.getTimeout());
        }
        Workbook hssfWorkbook = exportUtil.execute("仓库物流优先级明细", "仓库物流优先级明细",
                columnList, keyList, errExcelList);
        return exportUtil.saveFileAndPutOss(hssfWorkbook, "仓库物流优先级明细导入错误信息",
                user,"OSS-Bucket/EXPORT/StCWarehouseLogisticsRank/");
    }

    /**
     * 保存仓库物流优先级信息
     * @param objid
     * @param rankResultList
     * @return 错误信息
     */
    private List<String> saveWarehouseLogisticsRank(Long objid, List<WarehouseLogisticsRankResult> rankResultList, User user) {
        List<String> errMsgList = Lists.newArrayList();
        List<StCWarehouseLogisticsRankDO> rankProvList = Lists.newArrayList();
        List<StCWarehouseLogisticsRankDO> rankCityList = Lists.newArrayList();
        List<RegionTreeResult> treeList = Lists.newArrayList();
        //查询地址
        JSONObject obj = new JSONObject();
        obj.put("regiontype", "PROV,CITY");
        ValueHolderV14<List<RegionTreeResult>> treeVh = regionTreeQueryService.queryRegionTree(obj);
        if (treeVh.isOK()) {
            treeList = treeVh.getData();
        }
        //1.组装集合
        //获取物流公司信息
        List<StCWarehouseLogisticsItemDO> itemList = itemMapper.selectList(new QueryWrapper<StCWarehouseLogisticsItemDO>()
                .lambda().eq(StCWarehouseLogisticsItemDO::getStCWarehouseLogisticsId, objid));
        if (CollectionUtils.isNotEmpty(itemList)) {
            itemList = itemList.stream().sorted(Comparator.comparing(StCWarehouseLogisticsItemDO::getId))
                    .collect(Collectors.toList());
        }
        for(WarehouseLogisticsRankResult rankResult : rankResultList) {
            try{
                //补足冗余信息
                StCWarehouseLogisticsRankDO rank = getImportWarehouseLogisticsRank(rankResult, treeList, itemList);
                rank.setStCWarehouseLogisticsId(objid);
                if (rank.getCpCRegionCityId() == null) {
                    rankProvList.add(rank);
                } else {
                    rankCityList.add(rank);
                }
            }catch(Exception e){
                String area = rankResult.getCpCRegionProvinceEname() + rankResult.getCpCRegionCityEname();
                errMsgList.add(area + e.getMessage());
            }
        }

        //开始重新拼装需要入库的完整数据
        Map<String, StCWarehouseLogisticsRankDO> rankCityMap = Maps.newHashMap();
        for (StCWarehouseLogisticsRankDO rankProv : rankProvList) {
            //取省下对应的市
            List<RegionTreeResult> childrenRankList = RegionTreeUtils.getTreeChildrenResult(treeList, rankProv.getCpCRegionProvinceId());
            for (RegionTreeResult treeResult : childrenRankList) {
                StCWarehouseLogisticsRankDO newRank = new StCWarehouseLogisticsRankDO();
                BeanUtils.copyProperties(rankProv, newRank);
                newRank.setCpCRegionCityId(treeResult.getId());
                newRank.setCpCRegionCityEcode(treeResult.getEcode());
                newRank.setCpCRegionCityEname(treeResult.getTitle());

                String area = newRank.getCpCRegionProvinceEname() + newRank.getCpCRegionCityEname();
                rankCityMap.put(area, newRank);
            }
        }
        //用导入数据中完整数据进行覆盖
        for (StCWarehouseLogisticsRankDO rankCity : rankCityList) {
            String area = rankCity.getCpCRegionProvinceEname() + rankCity.getCpCRegionCityEname();
            rankCityMap.put(area, rankCity);
        }

        List<StCWarehouseLogisticsRankDO> rankList = rankCityMap.values().stream().collect(Collectors.toList());
        Map<String, StCWarehouseLogisticsRankDO> rankMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(rankList)) {
            List<StCWarehouseLogisticsRankDO> rankOldList = rankMapper.selectList(new QueryWrapper<StCWarehouseLogisticsRankDO>()
                    .lambda().eq(StCWarehouseLogisticsRankDO::getStCWarehouseLogisticsId, objid));
            for (StCWarehouseLogisticsRankDO rankOld : rankOldList) {
                String key = rankOld.getCpCRegionProvinceEname() + rankOld.getCpCRegionCityEname();
                rankMap.put(key, rankOld);
            }
        }
        //2.保存信息
        for(StCWarehouseLogisticsRankDO rank : rankList){
            try{
                //存在更新 不存在新增
                String key = rank.getCpCRegionProvinceEname() + rank.getCpCRegionCityEname();
                if (rankMap.containsKey(key)) {
                    rank.setId(rankMap.get(key).getId());
                    StBeanUtils.makeModifierField(rank, user);
                    if (rankMapper.updateById(rank) < 1) {
                        throw new NDSException(key + "仓库物流优先级明细更新失败");
                    }
                } else {
                    rank.setId(ModelUtil.getSequence("ST_C_WAREHOUSE_LOGISTICS_RANK"));
                    StBeanUtils.makeCreateField(rank, user);
                    if (rankMapper.insert(rank) < 1) {
                        throw new NDSException(key + "仓库物流优先级明细插入失败");
                    }
                }
            }catch(Exception e){
                errMsgList.add(e.getMessage());
            }
        }
        return errMsgList;
    }

    /**
     * 补足冗余信息
     * @param rankResult
     * @param treeList
     * @param itemList
     */
    private StCWarehouseLogisticsRankDO getImportWarehouseLogisticsRank(WarehouseLogisticsRankResult rankResult,
                                                                      List<RegionTreeResult> treeList,
                                                                      List<StCWarehouseLogisticsItemDO> itemList) {
        StCWarehouseLogisticsRankDO rank = new StCWarehouseLogisticsRankDO();
        Long upId = 1L;
        //省设值
        if (!StringUtils.isBlank(rankResult.getCpCRegionProvinceEname())) {
            RegionTreeResult treeResult = RegionTreeUtils.getTreeResultByName(treeList, rankResult.getCpCRegionProvinceEname(), upId);
            if(treeResult == null){
                throw new NDSException(String.format("不存在名称为[%s]的省", rankResult.getCpCRegionProvinceEname()));
            } else {
                upId = treeResult.getId();
                rank.setCpCRegionProvinceId(treeResult.getId());
                rank.setCpCRegionProvinceEcode(treeResult.getEcode());
                rank.setCpCRegionProvinceEname(treeResult.getTitle());
            }
        } else {
            throw new NDSException("省不能为空");
        }
        //市设值
        if (!StringUtils.isBlank(rankResult.getCpCRegionCityEname())) {
            RegionTreeResult treeResult = RegionTreeUtils.getTreeResultByName(treeList, rankResult.getCpCRegionCityEname(), upId);
            if(treeResult == null){
                throw new NDSException(String.format("不存在名称为[%s]的市", rankResult.getCpCRegionCityEname()));
            } else {
                upId = treeResult.getId();
                rank.setCpCRegionCityId(treeResult.getId());
                rank.setCpCRegionCityEcode(treeResult.getEcode());
                rank.setCpCRegionCityEname(treeResult.getTitle());
            }
        }

        List<LogisticsRankResult> logisticsRankNewList = Lists.newArrayList();
        List<String> rankAllList = Lists.newArrayList();
        int i = 1;
        for (StCWarehouseLogisticsItemDO item : itemList) {
            LogisticsRankResult logisticsRankNew = new LogisticsRankResult();
            logisticsRankNew.setLogisticsId(item.getCpCLogisticsId());
            try {
                Method method = WarehouseLogisticsRankResult.class.getMethod("getRank" + i);
                logisticsRankNew.setRank((String) method.invoke(rankResult));
            } catch (Exception ex) {
                logisticsRankNew.setRank("");
                log.debug(LogUtil.format("仓库物流优先级获取失败：{}"), Throwables.getStackTraceAsString(ex));
            }
            i++;
            if (logisticsRankNew.getRank() != null && !"".equals(logisticsRankNew.getRank())) {
                if (rankAllList.contains(logisticsRankNew.getRank())) {
                    throw new NDSException("仓库物流优先级重复！");
                } else {
                    rankAllList.add(logisticsRankNew.getRank());
                }
                logisticsRankNewList.add(logisticsRankNew);
            }
        }
        rank.setLogisticsRank(JSONArray.toJSONString(logisticsRankNewList));
        return rank;
    }
}
