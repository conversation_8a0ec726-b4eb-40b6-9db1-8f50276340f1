package com.jackrain.nea.st.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.st.common.StConstant;
import com.jackrain.nea.st.mapper.StCPickUpTimeMapper;
import com.jackrain.nea.st.model.table.StCPickUpTimeDO;
import com.jackrain.nea.st.utils.StBeanUtils;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.HashMap;

/**
 * @Descroption 上门取件时间策略作废
 * <AUTHOR>
 * @Date 2020/5/12 15:55
 */
@Component
@Slf4j
public class PickUpTimeVoidService extends CommandAdapter {
    @Autowired
    private StCPickUpTimeMapper mapper;

    @Override
    public ValueHolder execute(QuerySession querySession) throws NDSException {
        //1.获取传入参数
        DefaultWebEvent event = querySession.getEvent();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(event.getParameterValue("param"),
                "yyyy-MM-dd HH:mm:ss", SerializerFeature.WriteMapNullValue), Feature.OrderedField);
        if (param == null) {
            throw new NDSException("参数为空！");
        }
        //2.定义错误记录数
        HashMap<Long, Object> errMap = new HashMap();
        //3.生成审核Json数组
        JSONArray voidArray = StBeanUtils.makeVoidJsonArray(param);
        for (int i = 0; i < voidArray.size(); i++) {
            Long id = voidArray.getLong(i);
            try {
                //4.遍历作废方法
                voidPickUpTime(id, querySession);
            } catch (Exception e) {
                errMap.put(id, e.getMessage());
            }
        }
        //5.返回ValueHolder对象
        return StBeanUtils.getExcuteValueHolder(voidArray.size(), errMap);
    }

    /**
     * @param id
     * @param querySession
     * @return void
     * @Descroption 检查并更新作废状态
     */
    public void voidPickUpTime(Long id, QuerySession querySession) {
        StCPickUpTimeDO pickUpTime = mapper.selectById(id);
        checkPickUpTime(pickUpTime);
        //更新作废状态
        StBeanUtils.makeModifierField(pickUpTime, querySession.getUser());
        pickUpTime.setIsactive(StConstant.ISACTIVE_N);//作废
        setVoidCommonField(pickUpTime, querySession.getUser());
        int updateNum = mapper.updateById(pickUpTime);
        if (updateNum < 0) {
            throw new NDSException("作废失败！");
        }
    }

    private void checkPickUpTime(StCPickUpTimeDO pickUpTime) {
        if (pickUpTime == null) {
            throw new NDSException("当前记录已不存在！");
        } else {
            if (StConstant.ISACTIVE_N.equals(pickUpTime.getIsactive())) {
                throw new NDSException("当前记录已作废，不允许重复作废！");
            }
        }
    }

    /**
     * @param pickUpTime
     * @param user
     * @return void
     * @Descroption 设置作废公共字段
     */
    private void setVoidCommonField(StCPickUpTimeDO pickUpTime, User user) {
        pickUpTime.setDelid(Long.valueOf(user.getId()));
        pickUpTime.setDelename(user.getEname());
        pickUpTime.setDelname(user.getName());
        pickUpTime.setDelTime(new Date());
    }
}
