package com.jackrain.nea.st.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.st.mapper.StCDistributionItemMapper;
import com.jackrain.nea.st.mapper.StCDistributionMapper;
import com.jackrain.nea.st.model.result.DistributionResult;
import com.jackrain.nea.st.model.table.StCDistributionDO;
import com.jackrain.nea.st.model.table.StCDistributionItemDO;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @description 获取分销代销信息
 * @author:洪艺安
 * @since: 2019/7/18
 * @create at : 2019/7/18 15:37
 */
@Component
@Slf4j
public class DistributionQueryService  extends CommandAdapter {
    @Autowired
    private StCDistributionMapper mainmMapper;
    @Autowired
    private StCDistributionItemMapper itemMapper;
    @Override
    public ValueHolder execute(QuerySession querySession) throws NDSException {
        ValueHolder valueHolder = new ValueHolder();
        DistributionResult queryResult = new DistributionResult();
        DefaultWebEvent event = querySession.getEvent();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(
                event.getParameterValue("param"), "yyyy-MM-dd HH:mm:ss"),
                Feature.OrderedField);
        Long objid = param.getLong("objid");
        if (objid != null && objid > 0) {
            StCDistributionDO stCDistribution = mainmMapper.selectById(objid);
            List<StCDistributionItemDO> itemList  = itemMapper.selectItemByMainId(objid);
            queryResult.setStCDistribution(stCDistribution);
            queryResult.setStCDistributionItemList(itemList);
            valueHolder.put("code", 0);
            valueHolder.put("message", "查询成功");
            valueHolder.put("data", queryResult);
        }else{
            valueHolder.put("code", -1);
            valueHolder.put("message", "查询失败");
        }
        return valueHolder;
    }
}
