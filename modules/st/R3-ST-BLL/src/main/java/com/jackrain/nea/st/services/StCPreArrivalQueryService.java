package com.jackrain.nea.st.services;

import com.jackrain.nea.st.common.StConstant;
import com.jackrain.nea.st.mapper.StCPreArrivalItemMapper;
import com.jackrain.nea.st.mapper.StCPreArrivalMapper;
import com.jackrain.nea.st.model.result.StCPreArrivalResult;
import com.jackrain.nea.st.model.table.StCPreArrivalDO;
import com.jackrain.nea.st.model.table.StCPreArrivalItemDO;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description 预到货策略查询
 * @Date 2020/06/10
 * @Copyright 2019-2020
 */
@Component
@Slf4j
@Transactional
public class StCPreArrivalQueryService extends CommandAdapter {

    @Autowired
    private StCPreArrivalMapper stCPreArrivalMapper;

    @Autowired
    private StCPreArrivalItemMapper stCPreArrivalItemMapper;

    /**
     * @return com.jackrain.nea.util.ValueHolder
     * <AUTHOR>
     * @Description 预到货策略查询
     * @Date  2020/06/14
     **/
    public ValueHolderV14<List<StCPreArrivalDO>> queryStCPreArrival(){

        ValueHolderV14<List<StCPreArrivalDO>> result = new ValueHolderV14<>();
        Map<String,Object> param = new Hashtable<>();
        param.put("pre_arrival_status",StConstant.PRE_ARRIVAL_STATUS_02);
        List<StCPreArrivalDO> list = stCPreArrivalMapper.selectByMap(param);
        if(CollectionUtils.isEmpty(list)){
            return null;
        }
        Date date = new Date();
        List<StCPreArrivalDO> resultList = list.stream()
                .filter(c->c.getEndTime().after(date) && c.getBeginTime().before(date))
                .collect(Collectors.toList());
        result.setData(resultList);
        return result;
    }


    /**
     * @return com.jackrain.nea.util.ValueHolder
     * <AUTHOR>
     * @Description 预到货策略明细查询
     * @Date  2020/06/14
     **/
    public ValueHolderV14<StCPreArrivalResult> queryStCPreArrivalItems(Long preArrivalId){

        ValueHolderV14<StCPreArrivalResult> result = new ValueHolderV14<>();
        StCPreArrivalDO dto = stCPreArrivalMapper.selectById(preArrivalId);
        Date date = new Date();
        if(dto.getBeginTime().after(date) || dto.getEndTime().before(date) || !StConstant.PRE_ARRIVAL_STATUS_02.equals(dto.getPreArrivalStatus())){
            return null;
        }
        Map<String,Object> param = new HashMap<>();
        param.put("st_c_pre_arrival_id",preArrivalId);
        List<StCPreArrivalItemDO> items = stCPreArrivalItemMapper.selectByMap(param);
        StCPreArrivalResult stCPreArrivalResult = new StCPreArrivalResult();
        stCPreArrivalResult.setStCPreArrivalDO(dto);
        stCPreArrivalResult.setItemDOList(items);
        result.setData(stCPreArrivalResult);
        return result;
    }

}
