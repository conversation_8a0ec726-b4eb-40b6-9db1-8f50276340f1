package com.jackrain.nea.st.services;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.cpext.model.table.CpShop;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.psext.model.table.PsCSku;
import com.jackrain.nea.st.annotation.StOperationLog;
import com.jackrain.nea.st.mapper.StPreorderFieldStrategyMapper;
import com.jackrain.nea.st.mapper.StPreorderItemStrategyMapper;
import com.jackrain.nea.st.mapper.StPreorderModelStrategyMapper;
import com.jackrain.nea.st.model.enums.SubmitStatusEnum;
import com.jackrain.nea.st.model.request.StCPreorderModelStrategyRequest;
import com.jackrain.nea.st.model.result.StCPreorderModelStrategyResult;
import com.jackrain.nea.st.model.table.StCPreorderFieldStrategyDO;
import com.jackrain.nea.st.model.table.StCPreorderItemStrategyDO;
import com.jackrain.nea.st.model.table.StCPreorderModelStrategyDO;
import com.jackrain.nea.st.rpc.RpcCpService;
import com.jackrain.nea.st.rpc.RpcPsService;
import com.jackrain.nea.st.utils.JsonUtils;
import com.jackrain.nea.st.utils.ValueHolderUtils;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @ClassName StCPreorderModelStrategyService
 * @Description 订单预导入模板
 * <AUTHOR>
 * @Date 2022/12/22 11:18
 * @Version 1.0
 */
@Component
@Slf4j
public class StCPreorderModelStrategyService {

    @Autowired
    private StPreorderModelStrategyMapper modelStrategyMapper;
    @Autowired
    private StPreorderFieldStrategyMapper fieldStrategyMapper;
    @Autowired
    private StPreorderItemStrategyMapper itemStrategyMapper;
    @Autowired
    private ApplicationContext applicationContext;
    @Autowired
    private RpcCpService rpcCpService;
    @Autowired
    private RpcPsService rpcPsService;

    public ValueHolder saveOrUpdate(QuerySession session) {
        DefaultWebEvent event = session.getEvent();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(
                        event.getParameterValue("param"), "yyyy-MM-dd HH:mm:ss", SerializerFeature.WriteMapNullValue),
                Feature.OrderedField);
        if (log.isDebugEnabled()) {
            log.debug("Start Update model strategy Receive Params:{}", param);
        }
        User user = session.getUser();
        Long id = param.getLong("objid");
        JSONObject fixColumn = param.getJSONObject("fixcolumn");
        JSONObject before = param.getJSONObject("beforevalue");
        JSONObject after = param.getJSONObject("aftervalue");

        if (fixColumn != null && id != null) {
            if (id < 0) {
                return addModelStrategy(fixColumn, user);
            } else {
                return updateModelStrategy(fixColumn, user, id, before, after);
            }
        }
        throw new NDSException("当前记录已不存在！");
    }

    @StOperationLog(operationType = "DEL", mainTableName = "ST_C_PREORDER_MODEL_STRATEGY", itemsTableName = "ST_C_PREORDER_FIELD_STRATEGY,ST_C_PREORDER_ITEM_STRATEGY")
    public ValueHolder delModelStrategy(QuerySession session) {
        ValueHolder valueHolder = new ValueHolder();
        valueHolder.put("code", ResultCode.SUCCESS);
        valueHolder.put("message", "success");

        DefaultWebEvent event = session.getEvent();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(
                event.getParameterValue("param"), "yyyy-MM-dd HH:mm:ss", SerializerFeature.WriteMapNullValue),
                Feature.OrderedField);
        Long id = param.getLong("objid");
        String table = param.getString("table");
        Boolean isdelmtable = param.getBoolean("isdelmtable");
        StCPreorderModelStrategyDO oldModelStrategyDO = modelStrategyMapper.selectById(id);
        if (ObjectUtil.equal(oldModelStrategyDO.getSubmitStatus(), SubmitStatusEnum.SUBMIT.getKey())) {
            valueHolder.put("code", ResultCode.FAIL);
            valueHolder.put("message", "模板已提交 不允许删除");
            return valueHolder;
        }
        if (isdelmtable) {
            // 主表删除
            if (ObjectUtil.notEqual("ST_C_PREORDER_MODEL_STRATEGY", table)) {
                valueHolder.put("code", ResultCode.FAIL);
                valueHolder.put("message", "表错误");
                return valueHolder;
            }
            log.info("StCPreorderModelStrategyService delModelStrategy param:{}", param);
            applicationContext.getBean(StCPreorderModelStrategyService.class).delByModelId(id);
        } else {
            Map<Long, String> beforeDelObjMap = new HashMap<>();
            JSONObject data = param.getJSONObject("tabitem");
            JSONArray array = data.getJSONArray("ST_C_PREORDER_FIELD_STRATEGY");
            if (CollectionUtils.isNotEmpty(array)) {
                List<Long> ids = JSONArray.parseArray(JSONObject.toJSONString(array), Long.class);
                if (CollectionUtils.isNotEmpty(ids)) {
                    List<StCPreorderFieldStrategyDO> items = fieldStrategyMapper.selectBatchIds(ids);
                    if (CollectionUtils.isNotEmpty(items)) {
                        for (StCPreorderFieldStrategyDO item : items) {
                            beforeDelObjMap.put(item.getId(), JSON.toJSONString(item));
                        }
                    }
                }
                for (Object o : array) {
                    delByFieldId(Long.valueOf(o.toString()));
                }
            }
            JSONArray arrayItem = data.getJSONArray("ST_C_PREORDER_ITEM_STRATEGY");
            if (CollectionUtils.isNotEmpty(arrayItem)) {
                List<Long> ids = JSONArray.parseArray(JSONObject.toJSONString(arrayItem), Long.class);
                if (CollectionUtils.isNotEmpty(ids)) {
                    List<StCPreorderItemStrategyDO> items = itemStrategyMapper.selectBatchIds(ids);
                    if (CollectionUtils.isNotEmpty(items)) {
                        for (StCPreorderItemStrategyDO item : items) {
                            beforeDelObjMap.put(item.getId(), JSON.toJSONString(item));
                        }
                    }
                }
                for (Object o : arrayItem) {
                    delByItemId(Long.valueOf(o.toString()));
                }
            }
            session.setAttribute("beforeDelObjMap", beforeDelObjMap);
        }

        return valueHolder;
    }

    public ValueHolder unSubmitModelStrategy(QuerySession session) {
        DefaultWebEvent event = session.getEvent();
        ValueHolder valueHolder = new ValueHolder();
        valueHolder.put("code", ResultCode.SUCCESS);
        valueHolder.put("message", "success");
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(
                        event.getParameterValue("param"), "yyyy-MM-dd HH:mm:ss", SerializerFeature.WriteMapNullValue),
                Feature.OrderedField);
        JSONArray jsonArray = param.getJSONArray("ids");
        for (Object o : jsonArray) {
            Long id = Long.valueOf(o.toString());
            StCPreorderModelStrategyDO modelStrategyDO = modelStrategyMapper.selectById(id);
            if (ObjectUtil.isNull(modelStrategyDO)) {
                valueHolder.put("code", ResultCode.FAIL);
                valueHolder.put("message", "模板记录已不存在");
                return valueHolder;
            }
            StCPreorderModelStrategyDO updateModelStrategyDO = new StCPreorderModelStrategyDO();
            updateModelStrategyDO.setId(modelStrategyDO.getId());
            updateModelStrategyDO.setSubmitStatus(SubmitStatusEnum.NO_SUBMIT.getKey());
            updateModelStrategyDO.setModifieddate(new Date());
            modelStrategyMapper.updateById(updateModelStrategyDO);
        }

        return valueHolder;
    }


    public ValueHolder submitModelStrategy(QuerySession session) {
        DefaultWebEvent event = session.getEvent();
        ValueHolder valueHolder = new ValueHolder();
        valueHolder.put("code", ResultCode.SUCCESS);
        valueHolder.put("message", "success");
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(
                        event.getParameterValue("param"), "yyyy-MM-dd HH:mm:ss", SerializerFeature.WriteMapNullValue),
                Feature.OrderedField);
        JSONArray jsonArray = param.getJSONArray("ids");
        for (Object o : jsonArray) {
            Long id = Long.valueOf(o.toString());
            StCPreorderModelStrategyDO modelStrategyDO = modelStrategyMapper.selectById(id);
            if (ObjectUtil.isNull(modelStrategyDO)) {
                valueHolder.put("code", ResultCode.FAIL);
                valueHolder.put("message", "模板记录已不存在");
                return valueHolder;
            }

            // 校验同店铺是否已有提交状态的记录
            StCPreorderModelStrategyDO existSubmittedModel = modelStrategyMapper.getByShopIdAndSubmitStatus(modelStrategyDO.getCpCShopId());
            if (ObjectUtil.isNotNull(existSubmittedModel) && !existSubmittedModel.getId().equals(id)) {
                valueHolder.put("code", ResultCode.FAIL);
                valueHolder.put("message", "该店铺已审核策略存在");
                return valueHolder;
            }

            List<StCPreorderFieldStrategyDO> fieldStrategyDOList = fieldStrategyMapper.getFieldStrategyDOListByModelId(id);
            if (CollectionUtils.isEmpty(fieldStrategyDOList)) {
                valueHolder.put("code", ResultCode.FAIL);
                valueHolder.put("message", "该模板未维护字段");
                return valueHolder;
            }
            List<String> standardField = fieldStrategyDOList.stream().map(StCPreorderFieldStrategyDO::getStandardField).collect(Collectors.toList());
            StringBuilder sb = new StringBuilder();
            // 需要校验是否填了必填字段。必填字段为  下单店铺 平台单号 收货人 收货人手机 收货人地址 商品SKU编码 数量 成交单价
//            if (!standardField.contains("下单店铺")) {
//                sb.append("下单店铺 ");
//            }
            if (!standardField.contains("平台单号")) {
                sb.append("平台单号 ");
            }
            if (!standardField.contains("收货人")) {
                sb.append("收货人 ");
            }
            if (!standardField.contains("收货人手机")) {
                sb.append("收货人手机 ");
            }
            if (!standardField.contains("收货人地址")) {
                sb.append("收货人地址 ");
            }
//            if (!standardField.contains("商品SKU编码")) {
//                sb.append("商品SKU编码 ");
//            }
            if (!standardField.contains("数量")) {
                sb.append("数量 ");
            }
//            if (!standardField.contains("成交单价")) {
//                sb.append("成交单价 ");
//            }
            if (!standardField.contains("导入内容")) {
                sb.append("导入内容 ");
            }
            if (StringUtils.isNotEmpty(sb.toString())) {
                sb.append(" 等必填字段未填写,请先填写后再提交");
                valueHolder.put("code", ResultCode.FAIL);
                valueHolder.put("message", sb.toString());
                return valueHolder;
            }

            List<StCPreorderItemStrategyDO> itemStrategyDOList = itemStrategyMapper.getItemStrategyDOListByModelId(id);
            if (CollectionUtils.isNotEmpty(itemStrategyDOList)) {
                List<String> itemNameList = itemStrategyDOList.stream().map(StCPreorderItemStrategyDO::getItemName).distinct().collect(Collectors.toList());
                if (itemNameList.size() != itemStrategyDOList.size()) {
                    valueHolder.put("code", ResultCode.FAIL);
                    valueHolder.put("message", "商品名称存在重复");
                    return valueHolder;
                }
            }
            StCPreorderModelStrategyDO updateModelStrategy = new StCPreorderModelStrategyDO();
            updateModelStrategy.setId(modelStrategyDO.getId());
            updateModelStrategy.setSubmitStatus(SubmitStatusEnum.SUBMIT.getKey());
            updateModelStrategy.setModifieddate(new Date());
            modelStrategyMapper.updateById(updateModelStrategy);
        }

        return valueHolder;
    }

    @Transactional(rollbackFor = Exception.class)
    public void delByModelId(Long modelId) {
        modelStrategyMapper.deleteById(modelId);
        fieldStrategyMapper.delByModelId(modelId);
        itemStrategyMapper.delByModelId(modelId);
    }

    public void delByFieldId(Long fieldId) {
        fieldStrategyMapper.deleteById(fieldId);
    }

    public void delByItemId(Long fieldId) {
        itemStrategyMapper.deleteById(fieldId);
    }

    private ValueHolder addModelStrategy(JSONObject fixColumn, User user) {
        ValueHolder valueHolder = new ValueHolder();
        valueHolder.put("code", ResultCode.SUCCESS);
        valueHolder.put("message", "success");
        StCPreorderModelStrategyRequest modelStrategyRequest = JsonUtils.jsonParseClass(fixColumn, StCPreorderModelStrategyRequest.class);

        StCPreorderModelStrategyDO strategyDO = modelStrategyRequest.getModelStrategyDO();
        // 名称与编码都不能重复
        String code = strategyDO.getCode();
        String name = strategyDO.getName();
        Long shopId = strategyDO.getCpCShopId();
        StCPreorderModelStrategyDO modelStrategyDO;
        modelStrategyDO = modelStrategyMapper.getByCode(code);
        if (ObjectUtil.isNotNull(modelStrategyDO)) {
            valueHolder.put("code", ResultCode.FAIL);
            valueHolder.put("message", "模板编码重复,请重新填写");
            return valueHolder;
        }
        modelStrategyDO = modelStrategyMapper.getByName(name);
        if (ObjectUtil.isNotNull(modelStrategyDO)) {
            valueHolder.put("code", ResultCode.FAIL);
            valueHolder.put("message", "模板名称重复,请重新填写");
            return valueHolder;
        }
        List<CpShop> cpShopList = rpcCpService.queryShopByIds(Collections.singletonList(shopId));
        if (CollectionUtils.isEmpty(cpShopList)) {
            valueHolder.put("code", ResultCode.FAIL);
            valueHolder.put("message", "店铺不存在");
            return valueHolder;
        }
        CpShop cpShop = cpShopList.get(0);
        // 构建id 以及创建人等基础信息
        Long id = ModelUtil.getSequence("ST_C_PREORDER_MODEL_STRATEGY");
        strategyDO.setId(id);
        strategyDO.setCpCShopEcode(cpShop.getEcode());
        strategyDO.setAdClientId((long) user.getClientId());
        strategyDO.setAdOrgId((long) user.getOrgId());
        strategyDO.setCreationdate(new Date());
        strategyDO.setModifieddate(new Date());
        strategyDO.setOwnerid(Long.valueOf(user.getId()));
        strategyDO.setOwnername(user.getName());
        strategyDO.setModifierid(Long.valueOf(user.getId()));
        strategyDO.setModifiername(user.getName());
        strategyDO.setIsactive("Y");
        strategyDO.setSubmitStatus(SubmitStatusEnum.NO_SUBMIT.getKey());

        List<StCPreorderFieldStrategyDO> fieldStrategyDOList = modelStrategyRequest.getFieldStrategyDOS();
        if (CollectionUtils.isNotEmpty(fieldStrategyDOList)) {
            for (StCPreorderFieldStrategyDO fieldStrategyDO : fieldStrategyDOList) {
                // 准备新增
                checkFieldStrategy(user, valueHolder, id, fieldStrategyDO);
                if (!valueHolder.isOK()) {
                    return valueHolder;
                }
            }
        }

        List<StCPreorderItemStrategyDO> itemStrategyDOS = modelStrategyRequest.getItemStrategyDOS();
        if (CollectionUtils.isNotEmpty(itemStrategyDOS)) {
            for (StCPreorderItemStrategyDO itemStrategyDO : itemStrategyDOS) {
                // 校验商品明细配置
                checkItemStrategyForAdd(itemStrategyDO, id, valueHolder, user);
                if (!valueHolder.isOK()) {
                    return valueHolder;
                }
            }
        }

        applicationContext.getBean(StCPreorderModelStrategyService.class).add(strategyDO, fieldStrategyDOList, itemStrategyDOS);
        // 新增字段
        return ValueHolderUtils.getSuccessValueHolder(id, "ST_C_PREORDER_MODEL_STRATEGY");
    }

    // fixme
    private ValueHolder checkItemStrategyForAdd(StCPreorderItemStrategyDO itemStrategyDO, Long id, ValueHolder valueHolder, User user) {
        valueHolder.put("code", ResultCode.SUCCESS);
        valueHolder.put("message", "success");
        itemStrategyDO.setPreorderModelStrategyId(id);
        PsCSku psCSku = rpcPsService.getSkuById(itemStrategyDO.getSkuId());
        itemStrategyDO.setSkuEcode(psCSku.getEcode());
        Long itemStrategyId = ModelUtil.getSequence("ST_C_PREORDER_ITEM_STRATEGY");
        itemStrategyDO.setId(itemStrategyId);
        itemStrategyDO.setPreorderModelStrategyId(id);
        itemStrategyDO.setAdClientId((long) user.getClientId());
        itemStrategyDO.setAdOrgId((long) user.getOrgId());
        itemStrategyDO.setCreationdate(new Date());
        itemStrategyDO.setModifieddate(new Date());
        itemStrategyDO.setOwnerid(Long.valueOf(user.getId()));
        itemStrategyDO.setOwnername(user.getName());
        itemStrategyDO.setModifierid(Long.valueOf(user.getId()));
        itemStrategyDO.setModifiername(user.getName());
        itemStrategyDO.setIsactive("Y");
        List<StCPreorderItemStrategyDO> itemStrategyDOList = itemStrategyMapper.getItemStrategyDOListByModelId(id);
        if (CollectionUtils.isEmpty(itemStrategyDOList)) {
            return valueHolder;
        }
        // 根据商品名称进行校验
        List<StCPreorderItemStrategyDO> stCPreorderItemStrategyDOS = itemStrategyMapper.getItemStrategyDOListByItemName(itemStrategyDO.getItemName());
        if (CollectionUtils.isNotEmpty(stCPreorderItemStrategyDOS)) {
            valueHolder.put("code", ResultCode.FAIL);
            valueHolder.put("message", "商品名称重复");
            return valueHolder;
        }
        return valueHolder;
    }

    private ValueHolder checkFieldStrategy(User user, ValueHolder valueHolder, Long id, StCPreorderFieldStrategyDO fieldStrategyDO) {
        valueHolder.put("code", ResultCode.SUCCESS);
        valueHolder.put("message", "success");
        Long fieldStrategyId = ModelUtil.getSequence("ST_C_PREORDER_FIELD_STRATEGY");
        fieldStrategyDO.setId(fieldStrategyId);
        fieldStrategyDO.setPreorderModelStrategyId(id);
        fieldStrategyDO.setAdClientId((long) user.getClientId());
        fieldStrategyDO.setAdOrgId((long) user.getOrgId());
        fieldStrategyDO.setCreationdate(new Date());
        fieldStrategyDO.setModifieddate(new Date());
        fieldStrategyDO.setOwnerid(Long.valueOf(user.getId()));
        fieldStrategyDO.setOwnername(user.getName());
        fieldStrategyDO.setModifierid(Long.valueOf(user.getId()));
        fieldStrategyDO.setModifiername(user.getName());
        fieldStrategyDO.setIsactive("Y");
        // 判断标准列名或者自定义列名是否已经在模板中存在
        List<StCPreorderFieldStrategyDO> alreadyFieldStrategyList = fieldStrategyMapper.getFieldStrategyDOListByModelId(id);
        if (CollectionUtils.isNotEmpty(alreadyFieldStrategyList)) {
            List<String> standardFieldStrategy = alreadyFieldStrategyList.stream().map(StCPreorderFieldStrategyDO::getStandardField).collect(Collectors.toList());
            List<String> customizeFieldStrategy = alreadyFieldStrategyList.stream().map(StCPreorderFieldStrategyDO::getCustomizeField).collect(Collectors.toList());
            if (standardFieldStrategy.contains(fieldStrategyDO.getStandardField())) {
                valueHolder.put("code", ResultCode.FAIL);
                valueHolder.put("message", "标准列名已存在");
                return valueHolder;
            }
            if (customizeFieldStrategy.contains(fieldStrategyDO.getCustomizeField())) {
                valueHolder.put("code", ResultCode.FAIL);
                valueHolder.put("message", "自定义列名已存在");
                return valueHolder;
            }
        }
        return valueHolder;
    }

    @Transactional(rollbackFor = Exception.class)
    public void add(StCPreorderModelStrategyDO modelStrategyDO, List<StCPreorderFieldStrategyDO> fieldStrategyDOList, List<StCPreorderItemStrategyDO> itemStrategyDOList) {
        modelStrategyMapper.insert(modelStrategyDO);
        if (CollectionUtils.isNotEmpty(fieldStrategyDOList)) {
            fieldStrategyMapper.batchInsert(fieldStrategyDOList);
        }
        if (CollectionUtils.isNotEmpty(itemStrategyDOList)) {
            itemStrategyMapper.batchInsert(itemStrategyDOList);
        }
    }

    private ValueHolder updateModelStrategy(JSONObject fixColumn, User user, Long id, JSONObject before, JSONObject after) {
        ValueHolder valueHolder = new ValueHolder();
        valueHolder.put("code", ResultCode.SUCCESS);
        valueHolder.put("message", "success");
        log.info("updateModelStrategy, param,fixColumn:{}", fixColumn.toJSONString());
        StCPreorderModelStrategyRequest modelStrategyRequest = JsonUtils.jsonParseClass(fixColumn, StCPreorderModelStrategyRequest.class);
        StCPreorderModelStrategyRequest beforeRequest = JsonUtils.jsonParseClass(before, StCPreorderModelStrategyRequest.class);
        StCPreorderModelStrategyRequest afterRequest = JsonUtils.jsonParseClass(after, StCPreorderModelStrategyRequest.class);

        StCPreorderModelStrategyDO modelStrategyDO = modelStrategyRequest.getModelStrategyDO();
        List<StCPreorderFieldStrategyDO> fieldStrategyDOList = modelStrategyRequest.getFieldStrategyDOS();
        List<StCPreorderItemStrategyDO> itemStrategyDOS = modelStrategyRequest.getItemStrategyDOS();
        log.info("updateModelStrategy, param,fieldStrategyDOList:{}", JSONUtil.toJsonStr(fieldStrategyDOList));
        log.info("updateModelStrategy, param,itemStrategyDOS:{}", JSONUtil.toJsonStr(itemStrategyDOS));
        StCPreorderModelStrategyDO oldModelStrategyDO = modelStrategyMapper.selectById(id);
        if (ObjectUtil.equal(oldModelStrategyDO.getSubmitStatus(), SubmitStatusEnum.SUBMIT.getKey())) {
            valueHolder.put("code", ResultCode.FAIL);
            valueHolder.put("message", "模板已提交 不允许修改");
            return valueHolder;
        }
        if (ObjectUtil.equal("standard", oldModelStrategyDO.getCode())) {
            valueHolder.put("code", ResultCode.FAIL);
            valueHolder.put("message", "标准模板 不允许修改");
            return valueHolder;
        }
        // 判断是否修改编码或者名称
        if (ObjectUtil.isNotNull(modelStrategyDO) && StringUtils.isNotEmpty(modelStrategyDO.getCode()) && ObjectUtil.notEqual(modelStrategyDO.getCode(), oldModelStrategyDO.getCode())) {
            // 校验是否有重复
            StCPreorderModelStrategyDO exist = modelStrategyMapper.getByCode(modelStrategyDO.getCode());
            if (ObjectUtil.isNotNull(exist)) {
                valueHolder.put("code", ResultCode.FAIL);
                valueHolder.put("message", "模板编码已存在");
                return valueHolder;
            }
        }

        // 判断是否修改编码或者名称
        if (ObjectUtil.isNotNull(modelStrategyDO) && StringUtils.isNotEmpty(modelStrategyDO.getName()) && ObjectUtil.notEqual(modelStrategyDO.getName(), oldModelStrategyDO.getName())) {
            // 校验是否有重复
            StCPreorderModelStrategyDO exist = modelStrategyMapper.getByName(modelStrategyDO.getName());
            if (ObjectUtil.isNotNull(exist)) {
                valueHolder.put("code", ResultCode.FAIL);
                valueHolder.put("message", "模板名称已存在");
                return valueHolder;
            }
        }


        // 模板不支持修改。只支持字段的修改
        List<StCPreorderFieldStrategyDO> addFieldStrategyDOList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(fieldStrategyDOList)) {
            for (StCPreorderFieldStrategyDO fieldStrategyDO : fieldStrategyDOList) {
                if (fieldStrategyDO.getId() == -1L) {
                    // 执行新增
                    valueHolder = checkFieldStrategy(user, valueHolder, id, fieldStrategyDO);
                    if (!valueHolder.isOK()) {
                        return valueHolder;
                    }
                    addFieldStrategyDOList.add(fieldStrategyDO);
                } else {
                    StCPreorderFieldStrategyDO fieldStrategyDO1 =
                            fieldStrategyMapper.getFieldStrategyDOListByCustomizeField(fieldStrategyDO.getCustomizeField(), id);
                    if (ObjectUtil.isNotNull(fieldStrategyDO1) && fieldStrategyDO1.getId() != fieldStrategyDO.getId()) {
                        valueHolder.put("code", ResultCode.FAIL);
                        valueHolder.put("message", "自定义列名不能重复");
                        return valueHolder;
                    }
                    StCPreorderFieldStrategyDO updateFieldStrategyDO = new StCPreorderFieldStrategyDO();
                    updateFieldStrategyDO.setId(fieldStrategyDO.getId());
                    updateFieldStrategyDO.setCustomizeField(fieldStrategyDO.getCustomizeField());
                    updateFieldStrategyDO.setModifieddate(new Date());
                    updateFieldStrategyDO.setModifierid(Long.valueOf(user.getId()));
                    updateFieldStrategyDO.setModifiername(user.getName());
                    fieldStrategyMapper.updateById(updateFieldStrategyDO);
                }
            }
            if (CollectionUtils.isNotEmpty(addFieldStrategyDOList)) {
                fieldStrategyMapper.batchInsert(addFieldStrategyDOList);
            }
        }

        if (CollectionUtils.isNotEmpty(itemStrategyDOS)) {
            List<StCPreorderItemStrategyDO> addItemStrategyDOList = new ArrayList<>();
            Map<Long, StCPreorderItemStrategyDO> beforeItemStrategyMap = new HashMap<>();
            Map<Long, StCPreorderItemStrategyDO> afterItemStrategyMap = new HashMap<>();
            if (ObjectUtil.isNotNull(beforeRequest)) {
                List<StCPreorderItemStrategyDO> beforeItemStrategyList = beforeRequest.getItemStrategyDOS();
                if (CollectionUtils.isNotEmpty(beforeItemStrategyList)) {
                    beforeItemStrategyMap = beforeItemStrategyList.stream().collect(Collectors.toMap(StCPreorderItemStrategyDO::getId, Function.identity()));
                }
            }
            if (ObjectUtil.isNotNull(afterRequest)) {
                List<StCPreorderItemStrategyDO> afterItemStrategyList = afterRequest.getItemStrategyDOS();
                if (CollectionUtils.isNotEmpty(afterItemStrategyList)) {
                    afterItemStrategyMap = afterItemStrategyList.stream().collect(Collectors.toMap(StCPreorderItemStrategyDO::getId, Function.identity()));
                }
            }
            for (StCPreorderItemStrategyDO itemStrategyDO : itemStrategyDOS) {
                if (itemStrategyDO.getId() == -1L) {
                    checkItemStrategyForAdd(itemStrategyDO, id, valueHolder, user);
                    if (!valueHolder.isOK()) {
                        return valueHolder;
                    }
                    addItemStrategyDOList.add(itemStrategyDO);
                } else {
                    // 需要校验
                    if (StringUtils.isNotEmpty(itemStrategyDO.getItemName())) {
                        // 不为空代表修改了 需要判断现在数据库中是否已存在
                        List<StCPreorderItemStrategyDO> itemStrategyDOList = itemStrategyMapper.getItemStrategyDOListByItemName(itemStrategyDO.getItemName());
                        if (CollectionUtils.isNotEmpty(itemStrategyDOList)) {
                            valueHolder.put("code", ResultCode.FAIL);
                            valueHolder.put("message", "商品名称重复");
                            return valueHolder;
                        }
                    }
                    StCPreorderItemStrategyDO beforeItemStrategyDO = beforeItemStrategyMap.get(itemStrategyDO.getId());
                    StCPreorderItemStrategyDO afterItemStrategyDO = afterItemStrategyMap.get(itemStrategyDO.getId());

                    StCPreorderItemStrategyDO updateItemStrategyDO = new StCPreorderItemStrategyDO();
                    updateItemStrategyDO.setId(itemStrategyDO.getId());
                    updateItemStrategyDO.setItemName(itemStrategyDO.getItemName());
                    updateItemStrategyDO.setSkuEcode(itemStrategyDO.getSkuEcode());
                    updateItemStrategyDO.setSkuId(itemStrategyDO.getSkuId());
                    updateItemStrategyDO.setPriceActual(itemStrategyDO.getPriceActual());
                    updateItemStrategyDO.setModifieddate(new Date());
                    itemStrategyMapper.updateById(updateItemStrategyDO);
                    // 清空priceActual
                    if (ObjectUtil.isNotNull(beforeItemStrategyDO) && ObjectUtil.isNotNull(afterItemStrategyDO) &&
                            ObjectUtil.isNotNull(beforeItemStrategyDO.getPriceActual()) && ObjectUtil.isNull(afterItemStrategyDO.getPriceActual())) {
                        itemStrategyMapper.updatePriceActual(null, afterItemStrategyDO.getId());
                    }
                }
            }
            if (CollectionUtils.isNotEmpty(addItemStrategyDOList)) {
                log.info("updateModelStrategy, param,addItemStrategyDOList:{}", JSONUtil.toJsonStr(addItemStrategyDOList));
                itemStrategyMapper.batchInsert(addItemStrategyDOList);
            }
        }

        return valueHolder;
    }

    public ValueHolderV14<List<StCPreorderFieldStrategyDO>> getFieldStrategyByModelCode(String modelCode) {
        StCPreorderModelStrategyDO modelStrategyDO = modelStrategyMapper.getByCode(modelCode);
        ValueHolderV14<List<StCPreorderFieldStrategyDO>> valueHolderV14 = new ValueHolderV14(new ArrayList<>(), ResultCode.SUCCESS, "success");
        if (ObjectUtil.isNull(modelStrategyDO)) {
            valueHolderV14.setCode(ResultCode.FAIL);
            valueHolderV14.setMessage("模板记录已不存在");
            return valueHolderV14;
        }
        Long modelId = modelStrategyDO.getId();
        List<StCPreorderFieldStrategyDO> fieldStrategyDOList = fieldStrategyMapper.getFieldStrategyDOListByModelId(modelId);
        if (CollectionUtils.isEmpty(fieldStrategyDOList)) {
            valueHolderV14.setCode(ResultCode.FAIL);
            valueHolderV14.setMessage("模板未维护字段");
            return valueHolderV14;
        }
        valueHolderV14.setData(fieldStrategyDOList);
        return valueHolderV14;
    }

    public ValueHolderV14<List<StCPreorderItemStrategyDO>> getItemStrategyByModelCode(String modelCode) {
        StCPreorderModelStrategyDO modelStrategyDO = modelStrategyMapper.getByCode(modelCode);
        ValueHolderV14<List<StCPreorderItemStrategyDO>> valueHolderV14 = new ValueHolderV14(new ArrayList<>(), ResultCode.SUCCESS, "success");
        if (ObjectUtil.isNull(modelStrategyDO)) {
            valueHolderV14.setCode(ResultCode.FAIL);
            valueHolderV14.setMessage("模板记录已不存在");
            return valueHolderV14;
        }
        Long modelId = modelStrategyDO.getId();
        List<StCPreorderItemStrategyDO> itemStrategyDOList = itemStrategyMapper.getItemStrategyDOListByModelId(modelId);
        valueHolderV14.setData(itemStrategyDOList);
        return valueHolderV14;
    }

    public ValueHolderV14<List<StCPreorderModelStrategyResult>> getAllModel() {
        ValueHolderV14<List<StCPreorderModelStrategyResult>> valueHolderV14 = new ValueHolderV14(new ArrayList<>(), ResultCode.SUCCESS, "success");
        List<StCPreorderModelStrategyDO> modelStrategyDOList = modelStrategyMapper.getAllModel();
        if (CollectionUtils.isEmpty(modelStrategyDOList)) {
            return valueHolderV14;
        }
        List<StCPreorderModelStrategyResult> modelStrategyResultList = new ArrayList<>();
        for (StCPreorderModelStrategyDO modelStrategyDO : modelStrategyDOList) {
            StCPreorderModelStrategyResult modelStrategyResult = new StCPreorderModelStrategyResult();
            modelStrategyResult.setCode(modelStrategyDO.getCode());
            modelStrategyResult.setName(modelStrategyDO.getName());
            modelStrategyResultList.add(modelStrategyResult);
        }
        valueHolderV14.setData(modelStrategyResultList);
        return valueHolderV14;
    }

    public ValueHolderV14 getByModelCode(String modelCode) {
        ValueHolderV14 valueHolderV14 = new ValueHolderV14();
        valueHolderV14.setCode(ResultCode.SUCCESS);

        StCPreorderModelStrategyDO byCode = modelStrategyMapper.getSubmitByCode(modelCode);
        if (ObjectUtil.isNull(byCode)) {
            valueHolderV14.setCode(ResultCode.FAIL);
            valueHolderV14.setMessage("模板记录不存在");
            return valueHolderV14;
        }
        valueHolderV14.setData(byCode);
        return valueHolderV14;
    }
}
