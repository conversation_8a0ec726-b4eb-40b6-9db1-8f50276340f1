package com.jackrain.nea.st.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.burgeon.r3.sg.basic.api.SgLockStockBasicCmd;
import com.google.common.base.Throwables;
import com.google.common.collect.Lists;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.st.common.StConstant;
import com.jackrain.nea.st.mapper.StCLockStockStrategyItemMapper;
import com.jackrain.nea.st.mapper.StCLockStockStrategyMapper;
import com.jackrain.nea.st.model.table.StCLockStockStrategyDO;
import com.jackrain.nea.st.model.table.StCLockStockStrategyItemDO;
import com.jackrain.nea.st.utils.DatasToEsUtils;
import com.jackrain.nea.st.utils.ListUtils;
import com.jackrain.nea.st.utils.StBeanUtils;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.HashMap;
import java.util.List;

/**
 * @Descroption 店铺策略锁库设置结案
 * @Author: 汪聿森
 * @Date: 2019/3/26 19:59
 */
@Component
@Slf4j
public class LockStockFinishService extends CommandAdapter {
    @Autowired
    private StCLockStockStrategyMapper mapper;

    @Autowired
    private StCLockStockStrategyItemMapper stCLockStockStrategyItemMapper;

    @Reference(group = "sg", version = "1.0")
    private SgLockStockBasicCmd sgLockStockBasicCmd;

//    @Autowired
//    private ShopUniversalStockService shopUniversalStockService;

    @Override
    public ValueHolder execute(QuerySession querySession) throws NDSException {
        //1.获取传入参数
        DefaultWebEvent event = querySession.getEvent();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(event.getParameterValue("param"),
                "yyyy-MM-dd HH:mm:ss", SerializerFeature.WriteMapNullValue), Feature.OrderedField);
        if (param == null) {
            throw new NDSException("参数为空！");
        }
        log.debug(LogUtil.format("Start ExpressFinishService.execute. ReceiveParams: {}"), param.toJSONString());
        //2.定义错误记录数
        HashMap<Long, Object> errMap = new HashMap();
        //3.生成审核Json数组
        JSONArray finishArray = StBeanUtils.makeAuditJsonArray(param);
        List<Long> ids = Lists.newArrayListWithExpectedSize(finishArray.size());

        if (finishArray.size() > 0) {
            List<StCLockStockStrategyDO> lockStockStrategyDOS = mapper.
                    selectBatchIds(JSONObject.parseArray(finishArray.toJSONString(), Long.class));

            for (StCLockStockStrategyDO cLockStockStrategyDO : lockStockStrategyDOS) {
                try {
                    //4.遍历结案方法
                    finishLockStock(cLockStockStrategyDO, querySession, ids);
                } catch (Exception e) {
                    errMap.put(cLockStockStrategyDO.getId(), e.getMessage());
                }
            }
        }
        // 结案同步至PG
        sgLockStockBasicCmd.status(ids, StConstant.CON_BILL_STATUS_04);
        //5.返回ValueHolder对象
        return StBeanUtils.getExcuteValueHolder(finishArray.size(), errMap);
    }

    public void finishLockStock(StCLockStockStrategyDO stCLockStockStrategyDO, QuerySession querySession, List<Long> ids) {

        Long id = stCLockStockStrategyDO.getId();
        //校验
        checkExpress(stCLockStockStrategyDO);
        //更新单据状态
        StBeanUtils.makeModifierField(stCLockStockStrategyDO, querySession.getUser());
        stCLockStockStrategyDO.setEstatus(StConstant.CON_BILL_STATUS_04);
        setFinishCommonField(stCLockStockStrategyDO, querySession.getUser());
        int updateNum = mapper.updateById(stCLockStockStrategyDO);
        if (updateNum < 0) {
            throw new NDSException("锁库方案:" + stCLockStockStrategyDO.getPlanName() + "结案失败！");
        } else {
            //结案后删除 清除同步库存中间表数据
//            ValueHolder v14 = shopUniversalStockService.deleteSgBSyncChannelStock(StConstant.LOCK_STOCK_TYPE.longValue(), id);
//            log.debug(LogUtil.format("锁库结案 删除同步库存中间表数据 策略id：" + id + " 结果：" + JSONObject.toJSONString(v14));
            ids.add(id);
            try {
                //做更新的需要先查询更新后数据库的实体在推ES
                stCLockStockStrategyDO = mapper.selectById(id);
                StCLockStockStrategyItemDO item = new StCLockStockStrategyItemDO();
                item.setStatus(StConstant.CON_BILL_STATUS_04);
                item.setPlanName(stCLockStockStrategyDO.getPlanName());
                item.setRank(stCLockStockStrategyDO.getRank());
                item.setMainCreationdate(stCLockStockStrategyDO.getCreationdate());
                QueryWrapper<StCLockStockStrategyItemDO> wrapper = new QueryWrapper<>();
                wrapper.eq("st_c_lock_stock_strategy_id", id);
                stCLockStockStrategyItemMapper.update(item, wrapper);
                List<StCLockStockStrategyItemDO> itemList = ListUtils.batchQueryByCondition(wrapper, stCLockStockStrategyItemMapper, 1000);
                DatasToEsUtils.insertLoclStockEsData(stCLockStockStrategyDO, null, StConstant.TAB_ST_C_LOCK_STOCK_STRATEGY);
                if (CollectionUtils.isNotEmpty(itemList)) {
                    DatasToEsUtils.insertLoclStockEsData(stCLockStockStrategyDO, itemList, StConstant.TAB_ST_C_LOCK_STOCK_STRATEGY_ITEM);
                }
            } catch (Exception ex) {
                log.debug(LogUtil.format("店铺锁库策略结案推数据到ES失败：") + ex);
            }
        }
    }

    /**
     * <AUTHOR>
     * @Description 批量结案锁库策略
     * @Date 14:20 2020/12/18
     * @param ids
     * @param user
     * @return com.jackrain.nea.sys.domain.ValueHolderV14
     **/
    @Transactional(rollbackFor = Exception.class)
    public ValueHolderV14 batchFinishLockStock(List<Long> ids, User user) {
        ValueHolderV14 valueHolder = new ValueHolderV14();
        if (CollectionUtils.isEmpty(ids)) {
            valueHolder.setCode(ResultCode.FAIL);
            valueHolder.setMessage("传入结案参数列表为空");
            return valueHolder;
        }
        try{
            /**1. 批量修改主表数据*/
            StCLockStockStrategyDO main = new StCLockStockStrategyDO();
            main.setEstatus(StConstant.CON_BILL_STATUS_04);
            setFinishCommonField(main, user);
            StBeanUtils.makeModifierField(main,user);
            QueryWrapper<StCLockStockStrategyDO> wrapper = new QueryWrapper<>();
            wrapper.in("id", ids);
            mapper.update(main, wrapper);

            /**2. 批量修改明细数据*/
            StCLockStockStrategyItemDO item = new StCLockStockStrategyItemDO();
            item.setStatus(StConstant.CON_BILL_STATUS_04);
            StBeanUtils.makeModifierField(item,user);
            QueryWrapper<StCLockStockStrategyItemDO> itemWrapper = new QueryWrapper<>();
            itemWrapper.in("st_c_lock_stock_strategy_id", ids);
            stCLockStockStrategyItemMapper.update(item, itemWrapper);

            /**3. 批量更新es*/
            List<StCLockStockStrategyDO> lockStockStrategyDOS = mapper.selectList(wrapper);
            for (StCLockStockStrategyDO lockStockStrategyDO : lockStockStrategyDOS) {
                DatasToEsUtils.insertLoclStockEsData(lockStockStrategyDO, null, StConstant.TAB_ST_C_LOCK_STOCK_STRATEGY);

                QueryWrapper<StCLockStockStrategyItemDO> esWrapper = new QueryWrapper<>();
                esWrapper.eq("st_c_lock_stock_strategy_id", lockStockStrategyDO.getId());
                List<StCLockStockStrategyItemDO> itemList = ListUtils.batchQueryByCondition(esWrapper, stCLockStockStrategyItemMapper, 1000);
                if (CollectionUtils.isNotEmpty(itemList)) {
                    DatasToEsUtils.insertLoclStockEsData(lockStockStrategyDO, itemList, StConstant.TAB_ST_C_LOCK_STOCK_STRATEGY_ITEM);
                }
            }
            valueHolder.setCode(ResultCode.SUCCESS);
        } catch (Exception e) {
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("【执行批量锁库策略结案异常】{}"), Throwables.getStackTraceAsString(e));
            }
            valueHolder.setCode(ResultCode.FAIL);
            valueHolder.setMessage(e.getMessage());
        }
        return valueHolder;
    }

    private void checkExpress(StCLockStockStrategyDO stCLockStockStrategyDO) {
        if (stCLockStockStrategyDO == null) {
            throw new NDSException("当前记录已不存在！");
        } else {
            if (StConstant.CON_BILL_STATUS_01.equals(stCLockStockStrategyDO.getEstatus())) {
                throw new NDSException("当前记录未审核，不允许结案！");
            } else if (StConstant.CON_BILL_STATUS_04.equals(stCLockStockStrategyDO.getEstatus())) {
                throw new NDSException("当前记录已结案，不允许重复结案！");
            } else if (StConstant.CON_BILL_STATUS_03.equals(stCLockStockStrategyDO.getEstatus())) {
                throw new NDSException("当前记录已作废，不允许结案！");
            }
        }
    }

    private void setFinishCommonField(StCLockStockStrategyDO stCLockStockStrategyDO, User user) {
        stCLockStockStrategyDO.setFinishid(Long.valueOf(user.getId()));
        stCLockStockStrategyDO.setFinishename(user.getEname());
        stCLockStockStrategyDO.setFinishname(user.getName());
        stCLockStockStrategyDO.setFinishtime(new Date());
    }
}
