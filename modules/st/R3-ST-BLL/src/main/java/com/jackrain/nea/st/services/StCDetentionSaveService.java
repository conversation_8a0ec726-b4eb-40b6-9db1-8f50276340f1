package com.jackrain.nea.st.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.burgeon.r3.sg.channel.model.request.product.SgChannelProductQueryRequest;
import com.burgeon.r3.sg.core.model.table.channel.product.SgBChannelProduct;
import com.burgeon.r3.sg.inf.api.oms.product.SgChannelProductQueryCmd;
import com.jackrain.nea.common.ReferenceUtil;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.psext.api.SkuLikeQueryCmd;
import com.jackrain.nea.psext.model.table.PsCPro;
import com.jackrain.nea.psext.model.table.PsCSku;
import com.jackrain.nea.st.common.StConstant;
import com.jackrain.nea.st.mapper.StCDetentionPolicyItemMapper;
import com.jackrain.nea.st.mapper.StCDetentionPolicyMapper;
import com.jackrain.nea.st.model.request.StDetentionPolicyRequest;
import com.jackrain.nea.st.model.table.StCDetentionPolicy;
import com.jackrain.nea.st.model.table.StCDetentionPolicyItem;
import com.jackrain.nea.st.rpc.RpcPsService;
import com.jackrain.nea.st.utils.*;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.utils.AssertUtils;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 */
@Component
@Slf4j
@Transactional
public class StCDetentionSaveService extends CommandAdapter {

    @Autowired
    private StCDetentionPolicyMapper StCDetentionPolicyMapper;

    @Autowired
    private StCDetentionPolicyItemMapper stCDetentionPolicyItemMapper;

    @Autowired
    private RpcPsService rpcPsService;

    @Reference(group = "sg", version = "1.0")
    private SgChannelProductQueryCmd sgChannelProductQueryCmd;

    @Override
    @Transactional(rollbackFor = {Exception.class})
    public ValueHolder execute(QuerySession session) {
        DefaultWebEvent event = session.getEvent();
        List<String> nullKeyList = new ArrayList<>();
        JSONObject param = JsonUtils.filterNull((JSONObject) event.getParameterValue("param"), nullKeyList);
        if (param != null) {
            Long id = param.getLong("objid");
            JSONObject fixColumn = param.getJSONObject("fixcolumn");
            log.info(LogUtil.format(" StCDetentionSaveService.execute querySession:{}"), session);
            StDetentionPolicyRequest stCHoldOrderRequest = JsonUtils.jsonParseClass(fixColumn, StDetentionPolicyRequest.class);
            if (fixColumn != null && id != null) {
                if (id != -1) {
                    return this.update(session, stCHoldOrderRequest, id);
                } else {
                    return this.insert(session, stCHoldOrderRequest);
                }
            }
        }
        throw new NDSException("当前记录已不存在！");
    }

    /**
     * 插入卡单策略
     *
     * @param session
     * @param stDetentionPolicyRequest
     * @return
     */
    private ValueHolder insert(QuerySession session, StDetentionPolicyRequest stDetentionPolicyRequest) {

        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format(" Detention insert request params: {}"),
                    JSON.toJSONString(stDetentionPolicyRequest));
        }
        long id = 0;
        //1.HOLD单策略表
        StCDetentionPolicy stCDetentionPolicy = stDetentionPolicyRequest.getStCDetentionPolicy();
        if (stCDetentionPolicy != null) {
            //1.1 判断名称是否已存在
            ValueHolder check = check(-1L, stDetentionPolicyRequest, "insert");
            if (check != null) {
                return check;
            }
            if (stCDetentionPolicy.getCpCShopId() == null) {
                return ValueHolderUtils.getFailValueHolder("卡单店铺不能为空");
            }
            if (stCDetentionPolicy.getPolicyTimeType() == null) {
                return ValueHolderUtils.getFailValueHolder("时间类型不能为空");
            }
            Date beginTime = stCDetentionPolicy.getStartTime();
            Date endTime = stCDetentionPolicy.getEndTime();
            if (beginTime == null || endTime == null) {
                return ValueHolderUtils.getFailValueHolder("开始时间结束时间不能为空");
            }
            //时间判断
            if (beginTime != null && endTime != null) {
                if (endTime.compareTo(beginTime) < 0) {
                    return ValueHolderUtils.getFailValueHolder("结束日期不能小于开始日期！");
                }
            }
            //1.2 插入
            id = ModelUtil.getSequence(StConstant.TAB_ST_C_DETENTION_POLICY);
            stCDetentionPolicy.setId(id);
            stCDetentionPolicy.setEstatus(StConstant.DETENTION_POLICY_STATUS_01);
            StBeanUtils.makeCreateField(stCDetentionPolicy, session.getUser());
            int insertResult = StCDetentionPolicyMapper.insert(stCDetentionPolicy);
            if (insertResult < 0) {
                return ValueHolderUtils.getFailValueHolder("保存失败！");
            }
        }
        //新增明细
        List<StCDetentionPolicyItem> stCDetentionPolicyItems = stDetentionPolicyRequest.getStCDetentionPolicyItem();
        log.info(LogUtil.format(" StCDetentionSaveService.insert param StCDetentionPolicyItems:{}"),
                stCDetentionPolicyItems);
        if (CollectionUtils.isNotEmpty(stCDetentionPolicyItems)) {

            //校验明细
            checkItems(stCDetentionPolicyItems);

            List<StCDetentionPolicyItem> updateList = new ArrayList<>();
            List<StCDetentionPolicyItem> insterList = new ArrayList<>();
            long detentionId = id;
            stCDetentionPolicyItems.forEach(i -> {
                i.setStCDetentionPolicyId(detentionId);
                if (Objects.nonNull(i.getId()) && i.getId() > 0) {
                    StBeanUtils.makeModifierField(i, session.getUser());
                    updateList.add(i);
                } else {
                    i.setId(ModelUtil.getSequence(StConstant.TAB_ST_C_DETENTION_POLICY_ITEM));
                    StBeanUtils.makeCreateField(i, session.getUser());
                    insterList.add(i);
                }
            });
            Boolean flag = detentionPolicyItemSave(updateList, insterList);
            if (!flag) {
                return ValueHolderUtils.getFailValueHolder("保存明细失败！");
            }
        }
        // 删除Redis缓存
        String shopId = stCDetentionPolicy.getCpCShopId() == null ? null : stCDetentionPolicy.getCpCShopId();
        String[] shopIds = shopId.split(",");
        for (String shop :
                shopIds) {
            RedisCacheUtil.delete(Long.parseLong(shop), RedisConstant.SHOP_DETENTION_ORDER_ST);
        }
        return ValueHolderUtils.getSuccessValueHolder(id, StConstant.TAB_ST_C_DETENTION_POLICY, "");
    }


    /**
     * <AUTHOR>
     * @Date 15:38 2021/4/22
     * @Description 明细新增修改 或者即新增又修改
     */
    private Boolean detentionPolicyItemSave(List<StCDetentionPolicyItem> updateList, List<StCDetentionPolicyItem> insterList) {
        log.info(LogUtil.format(" StCDetentionSaveService.detentionPolicyItemSave param updateList:{},insterList:{}")
                , updateList, insterList);
        int n = 0;
        if (CollectionUtils.isNotEmpty(updateList)) {
            for (StCDetentionPolicyItem stCDetentionPolicyItem : updateList) {
                //if (checkDetentionPolicyItem(stCDetentionPolicyItem)){
                n += stCDetentionPolicyItemMapper.updateById(stCDetentionPolicyItem);
               /* }else {
                    throw new NDSException("已存在相同数据，【识别规则】:"+parsFieldByRule(stCDetentionPolicyItem.getParsingContext())+"");
                }*/
            }

        }
        if (CollectionUtils.isNotEmpty(insterList)) {
            for (StCDetentionPolicyItem stCDetentionPolicyItem : insterList) {
                //if (checkDetentionPolicyItem(stCDetentionPolicyItem)){
                n += stCDetentionPolicyItemMapper.insert(stCDetentionPolicyItem);
                /*}else {
                    throw new NDSException("已存在相同数据，【识别规则】:"+parsFieldByRule(stCDetentionPolicyItem.getParsingContext())+"");
                }*/
            }

        }
        if (n > 0) {
            return true;
        }
        return false;
    }

    /**
     * <AUTHOR>
     * @Date 18:04 2021/4/22
     * @Description 翻译识别规则
     */
    private String parsFieldByRule(String key) {
        return StConstant.DETENTION_RULES_POLICY_TYPE.get(key);
    }

    /**
     * <AUTHOR>
     * @Date 16:11 2021/4/22
     * @Description 校验明细 【识别规则】以及【识别内容】不允许同时相同，否则提示
     */
    private boolean checkDetentionPolicyItem(StCDetentionPolicyItem stCDetentionPolicyItem) {
        if (Objects.nonNull(stCDetentionPolicyItem) && Objects.nonNull(stCDetentionPolicyItem.getStCDetentionPolicyId()) && Objects.nonNull(stCDetentionPolicyItem.getParsingRuleType())
                && Objects.nonNull(stCDetentionPolicyItem.getParsingContext())) {
            int n = stCDetentionPolicyItemMapper.selectCount(new QueryWrapper<StCDetentionPolicyItem>().lambda()
                    .eq(StCDetentionPolicyItem::getStCDetentionPolicyId, stCDetentionPolicyItem.getStCDetentionPolicyId())
                    .eq(StCDetentionPolicyItem::getParsingRuleType, stCDetentionPolicyItem.getParsingRuleType())
                    .eq(StCDetentionPolicyItem::getParsingContext, stCDetentionPolicyItem.getParsingContext()));
            if (n > 0) {
                return false;
            }
        }
        return true;
    }

    private ValueHolder update(QuerySession session, StDetentionPolicyRequest stDetentionPolicyRequest, Long id) {
        StCDetentionPolicy stCDetentionPolicy = stDetentionPolicyRequest.getStCDetentionPolicy();
        if (stCDetentionPolicy != null) {
            ValueHolder holder = check(id, stDetentionPolicyRequest, "update");
            if (holder != null) {
                return holder;
            }
            //1.订单HOLD单策略主表处理
            stCDetentionPolicy.setId(id);
            StBeanUtils.makeModifierField(stCDetentionPolicy, session.getUser());
            if (StCDetentionPolicyMapper.updateById(stCDetentionPolicy) < 0) {
                return ValueHolderUtils.getFailValueHolder("更新失败！");
            }
            stCDetentionPolicy = StCDetentionPolicyMapper.selectById(id);
        }
        //修改明细
        List<StCDetentionPolicyItem> stCDetentionPolicyItem = stDetentionPolicyRequest.getStCDetentionPolicyItem();
        log.info(LogUtil.format(" StDetentionSaveService update Detention rule. param stDetentionPolicyItemDOList:{}"), stCDetentionPolicyItem);
        if (CollectionUtils.isNotEmpty(stCDetentionPolicyItem)) {

            //校验明细
            checkItems(stCDetentionPolicyItem);

            List<StCDetentionPolicyItem> updateList = new ArrayList<>();
            List<StCDetentionPolicyItem> insterList = new ArrayList<>();
            stCDetentionPolicyItem.forEach(i -> {
                i.setStCDetentionPolicyId(id);
                if (Objects.nonNull(i.getId()) && i.getId() > 0) {
                    StBeanUtils.makeModifierField(i, session.getUser());
                    updateList.add(i);
                } else {
                    i.setId(ModelUtil.getSequence(StConstant.TAB_ST_C_DETENTION_POLICY_ITEM));
                    StBeanUtils.makeCreateField(i, session.getUser());
                    insterList.add(i);
                }
            });
            Boolean flag = detentionPolicyItemSave(updateList, insterList);
            if (!flag) {
                return ValueHolderUtils.getFailValueHolder("保存明细失败！");
            }
        }
        //主表不跟新会导致空指针
        if (stCDetentionPolicy == null) {
            stCDetentionPolicy = StCDetentionPolicyMapper.selectById(id);
        }
        // 删除Redis缓存
        String shopId = stCDetentionPolicy.getCpCShopId() == null ? null : stCDetentionPolicy.getCpCShopId();
        String[] shopIds = shopId.split(",");
        for (String shop :
                shopIds) {
            RedisCacheUtil.delete(Long.parseLong(shop), RedisConstant.SHOP_DETENTION_ORDER_ST);
        }
        return ValueHolderUtils.getSuccessValueHolder(id, StConstant.TAB_ST_C_DETENTION_POLICY, "");
    }


    private ValueHolder check(Long id, StDetentionPolicyRequest stDetentionPolicyRequest, String action) {
        StCDetentionPolicy stCDetentionPolicy = stDetentionPolicyRequest.getStCDetentionPolicy();
        log.info(LogUtil.format(" 验证DetentionPolicy单表单数据:{}"), stCDetentionPolicy);
        Date beginTime = stCDetentionPolicy.getStartTime();
        Date endTime = stCDetentionPolicy.getEndTime();
        String ename = stCDetentionPolicy.getName();
        String policyTimeType = stCDetentionPolicy.getPolicyTimeType();
        StringBuffer sb = new StringBuffer();

        StCDetentionPolicy policy = StCDetentionPolicyMapper.selectById(id);
        beginTime = beginTime == null ? policy.getStartTime() : beginTime;
        endTime = endTime == null ? policy.getEndTime() : endTime;

        //时间判断
        if (beginTime != null && endTime != null) {

            if (!beginTime.after(new Date())) {
                sb.append("开始时间必须大于当前系统时间！");
            }
            if (!endTime.after(new Date())) {
                sb.append("结束时间必须大于当前系统时间！");
            }
            if (!endTime.after(beginTime)) {
                sb.append("结束时间必须大于开始时间！");
            }
        }
        if (org.apache.commons.lang3.StringUtils.isNotBlank(sb)) {
            throw new NDSException(sb.toString());
        }
        if (StConstant.MINUS_ONE.equals(id)) {
            if (beginTime.compareTo(new Date()) < 0) {
                return ValueHolderUtils.getFailValueHolder("开始时间不能小于当前时间！");
            }
        }

        // 1:指定时点释放 2:固定时长后释放',
        if (Integer.valueOf(1).equals(policyTimeType)) {
            if (stCDetentionPolicy.getReeleaseTime() == null) {
                return ValueHolderUtils.getFailValueHolder("释放时间不能为空");
            }
        } else if (Integer.valueOf(2).equals(policyTimeType)) {
            if (stCDetentionPolicy.getDuration() == null) {
                return ValueHolderUtils.getFailValueHolder("固定时长不能为空");
            }
            if (stCDetentionPolicy.getReleaseTimeUnit() == null) {
                return ValueHolderUtils.getFailValueHolder("单位不能为空");
            }
        }
        return null;
    }

    private boolean checkEnameRepeat(String name, Long id) {
        QueryWrapper<StCDetentionPolicy> wrapper = new QueryWrapper<>();
        wrapper.eq("name", name);
        wrapper.eq("ISACTIVE", "Y");
        wrapper.ne("estatus", StConstant.SKUSTOCK_STATUS_03);
        wrapper.ne("estatus", StConstant.SKUSTOCK_STATUS_04);
        if (id > 0) {
            wrapper.ne("id", id);
        }
        return !CollectionUtils.isEmpty(StCDetentionPolicyMapper.selectList(wrapper));
    }

    private void checkEstatus(StCDetentionPolicy stCDetentionPolicy
            , String isStrategyTime) {
        if (stCDetentionPolicy == null) {
            throw new NDSException("当前记录已不存在！");
        }
        if ("Y".equals(isStrategyTime)) {
            if (!StConstant.CON_BILL_STATUS_02.equals(stCDetentionPolicy.getEstatus())) {
                throw new NDSException("只能修改已审核方案的结束时间！");
            }
        } else {
            if (StConstant.CON_BILL_STATUS_02.equals(stCDetentionPolicy.getEstatus())) {
                throw new NDSException("当前记录已审核，不允许修改！");
            } else if (StConstant.CON_BILL_STATUS_03.equals(stCDetentionPolicy.getEstatus())) {
                throw new NDSException("当前记录已作废，不允许修改！");
            } else if (StConstant.CON_BILL_STATUS_04.equals(stCDetentionPolicy.getEstatus())) {
                throw new NDSException("当前记录已结案，不允许修改！");
            }
        }

    }

    private void checkItems(List<StCDetentionPolicyItem> stCDetentionPolicyItems) {

        //识别规则如果是商品编码或者条码时，检验存不存在该商品或者条码
        List<String> proEcodeList = new ArrayList<>();
        List<String> skuEcodeList = new ArrayList<>();
        List<String> proIdList = new ArrayList<>();
        List<String> skuIdList = new ArrayList<>();

        //处理数据
        List<Long> ids = stCDetentionPolicyItems.stream().filter(x -> x.getId() > 0).map(StCDetentionPolicyItem::getId).collect(Collectors.toList());

        if (CollectionUtils.isNotEmpty(ids)) {
            List<StCDetentionPolicyItem> policyItems = stCDetentionPolicyItemMapper.selectBatchIds(ids);
            if (CollectionUtils.isNotEmpty(policyItems)) {

                Map<Long, StCDetentionPolicyItem> itemMap = policyItems.stream().collect(Collectors.toMap(StCDetentionPolicyItem::getId, v -> v,
                        (v1, v2) -> v1));

                stCDetentionPolicyItems.forEach(s -> {
                    StCDetentionPolicyItem stCDetentionPolicyItem = itemMap.get(s.getId());
                    if (stCDetentionPolicyItem != null) {
                        s.setParsingRuleType(stCDetentionPolicyItem.getParsingRuleType());
                    }
                });
            }
        }
        //开始校验
        stCDetentionPolicyItems.forEach(s -> {
            if (StConstant.DETENTION_RULES_POLICY_TYPE_MPRO.equalsIgnoreCase(s.getParsingRuleType())) {
                proEcodeList.add(s.getParsingContext());
            }

            if (StConstant.DETENTION_RULES_POLICY_TYPE_MSKU.equalsIgnoreCase(s.getParsingRuleType())) {
                skuEcodeList.add(s.getParsingContext());
            }

            if (StConstant.DETENTION_RULES_POLICY_TYPE_TPID.equalsIgnoreCase(s.getParsingRuleType())) {
                proIdList.add(s.getParsingContext());
            }

            if (StConstant.DETENTION_RULES_POLICY_TYPE_PTSKU.equalsIgnoreCase(s.getParsingRuleType())) {

                skuIdList.add(s.getParsingContext());
            }
        });

        Boolean aBoolean = checkProSkuEcode(proEcodeList, skuEcodeList, proIdList, skuIdList);
        if (!aBoolean) {
            AssertUtils.logAndThrow("当前商品在【商品款号】和【商品SKU】中不存在，新增失败!");
        }
    }

    private Boolean checkProSkuEcode(List<String> proEcodeList, List<String> skuEcodeList, List<String> proIds, List<String> skuIds) {

        SkuLikeQueryCmd skuLikeQueryCmd =
                (SkuLikeQueryCmd) ReferenceUtil.refer(ApplicationContextHandle.getApplicationContext(),
                        SkuLikeQueryCmd.class.getName(), "ps-ext", "1.0");
        Boolean proEcodeFlag = true;
        Boolean skuEcodeFlag = true;
        Boolean proIdflag = true;
        Boolean skuIdflag = true;
        //判断商品编码是否存在
        if (CollectionUtils.isNotEmpty(proEcodeList)) {
            List<PsCPro> psCPros = skuLikeQueryCmd.queryProByEcode(proEcodeList);
            if (CollectionUtils.isNotEmpty(psCPros)) {
                proEcodeFlag = true;
            } else {
                proEcodeFlag = false;
            }
        }
        //判断SKU编码是否存在
        if (CollectionUtils.isNotEmpty(skuEcodeList)) {
            List<PsCSku> psCSkus = skuLikeQueryCmd.querySkuByEcode(skuEcodeList);
            if (CollectionUtils.isNotEmpty(psCSkus)) {
                skuEcodeFlag = true;
            } else {
                skuEcodeFlag = false;
            }

        }
        //判断商品ID是否存在
        if (CollectionUtils.isNotEmpty(proIds)) {
            SgChannelProductQueryRequest request = new SgChannelProductQueryRequest();
            request.setNumiidList(proIds);
            ValueHolderV14<List<SgBChannelProduct>> holderV14 = sgChannelProductQueryCmd.queryChannelProduct(request);
            if (holderV14.isOK()) {
                List<SgBChannelProduct> data = holderV14.getData();
                if (CollectionUtils.isNotEmpty(data)) {
                    proIdflag = true;
                } else {
                    proIdflag = false;
                }
            }
        }
        //判断编码ID是否存在
        if (CollectionUtils.isNotEmpty(skuIds)) {
            SgChannelProductQueryRequest request = new SgChannelProductQueryRequest();
            request.setSkuIdList(skuIds);
            ValueHolderV14<List<SgBChannelProduct>> holderV14 = sgChannelProductQueryCmd.queryChannelProduct(request);
            if (holderV14.isOK()) {
                List<SgBChannelProduct> data = holderV14.getData();
                if (CollectionUtils.isNotEmpty(data)) {
                    skuIdflag = true;
                } else {
                    skuIdflag = false;
                }
            }

        }
        if (proEcodeFlag && skuEcodeFlag && proIdflag && skuIdflag) {
            return true;
        } else {
            return false;
        }
    }

}
