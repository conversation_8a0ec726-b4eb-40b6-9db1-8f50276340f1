package com.jackrain.nea.st.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.github.pagehelper.util.StringUtil;
import com.jackrain.nea.cpext.model.table.CpCustomer;
import com.jackrain.nea.es.util.ElasticSearchUtil;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.ext.sequence.util.SequenceGenUtil;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.st.common.EsConstant;
import com.jackrain.nea.st.common.StConstant;
import com.jackrain.nea.st.mapper.StCDistributionCustomerMapper;
import com.jackrain.nea.st.mapper.StCDistributionItemMapper;
import com.jackrain.nea.st.mapper.StCDistributionMapper;
import com.jackrain.nea.st.model.componentJsonModel.PopCheckboxJson;
import com.jackrain.nea.st.model.componentJsonModel.PopCheckboxListsJson;
import com.jackrain.nea.st.model.componentJsonModel.PopCheckboxResultJson;
import com.jackrain.nea.st.model.componentJsonModel.PopCheckboxValueJson;
import com.jackrain.nea.st.model.table.StCDistributionCustomerDO;
import com.jackrain.nea.st.model.table.StCDistributionDO;
import com.jackrain.nea.st.model.table.StCDistributionItemDO;
import com.jackrain.nea.st.rpc.RpcCpService;
import com.jackrain.nea.st.utils.JsonUtils;
import com.jackrain.nea.st.utils.StBeanUtils;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.beanutils.ConvertUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

/**
 * @Descroption 分销代销方案复制
 * <AUTHOR>
 * @Date 2019/5/7 14:04
 */
@Component
@Slf4j
@Transactional(rollbackFor = {Exception.class})
public class DistributionCopyService{
    @Autowired
    private StCDistributionMapper mapper;
    @Autowired
    private StCDistributionItemMapper itemMapper;
    @Autowired
    private StCDistributionCustomerMapper customerMapper;
    @Autowired
    private RpcCpService rpcCpService;
    @Autowired
    private DistributionUtilService distributionUtilService;

    public ValueHolderV14<List<StCDistributionDO>> execute(QuerySession querySession) throws NDSException {
        //1.获取传入参数
        DefaultWebEvent event = querySession.getEvent();
        JSONObject param = JSON.parseObject(JSON.toJSONString(event.getParameterValue("param"), SerializerFeature.WriteMapNullValue), Feature.OrderedField);
        ValueHolderV14<List<StCDistributionDO>> valueHolderV14 = new ValueHolderV14<>();
        String errMessage = "";
        if (param == null) {
            valueHolderV14.setCode(-1);
            valueHolderV14.setMessage("参数为空！");
            return valueHolderV14;
        }
        if(log.isDebugEnabled()){
            log.debug(LogUtil.format("Start DistributionCopyService.execute. ReceiveParams: {}"), param.toJSONString());
        }
        String customerIdStr = param.getString("CP_C_CUSTOMER_ID");
        JSONArray idsArray = param.getJSONArray("IDS");
        if(StringUtil.isEmpty(customerIdStr)){
            valueHolderV14.setCode(-1);
            valueHolderV14.setMessage("未选中经销商！");
            return valueHolderV14;
        }
        if(idsArray != null){
            for(int i=0; i< idsArray.size(); i++){
                long distributionId = idsArray.getLong(i);
                StCDistributionDO mainDO = mapper.selectById(distributionId);
                List<StCDistributionItemDO> itemList = itemMapper.selectItemByMainId(distributionId);
                //主表数据复制
                StCDistributionDO copyMainDO = new StCDistributionDO();
                if(mainDO != null){
                    try {
                        //1.业务信息复制
                        BeanUtils.copyProperties(mainDO,copyMainDO);
                        //2.经销商信息替换且分销代销方案审核状态设置为未审核
                        copyMainDO.setBillStatus(StConstant.CON_BILL_STATUS_01);
                        //extends SubBaseModel(copyMainDO,customerIdStr);
                        copyMainDO.setCpCCustomerIdSet(customerIdStr);
                        //3.主表id及创建修改时间赋值
                        copyMainDO.setId(ModelUtil.getSequence(StConstant.TAB_ST_C_DISTRIBUTION));
                        //主表创建信息更新
                        StBeanUtils.makeCreateField(copyMainDO, querySession.getUser());
                        copyMainDO.setOwnerename(querySession.getUser().getEname());
                        copyMainDO.setModifierename(querySession.getUser().getEname());
                        //单据编号生成更新
                        JSONObject sequence = new JSONObject();
                        String billNo = SequenceGenUtil.generateSquence("SEQ_ST_C_DISTRIBUTION",sequence,querySession.getUser().getLocale(),false);
                        copyMainDO.setBillNo(billNo);
                        //4.主表数据保存
                        int insertResult = mapper.insert(copyMainDO);
                        if (insertResult <= 0) {
                            throw new NDSException("方案["+mainDO.getBillNo()+"]复制插入主表数据失败!");
                        }
                        //5.单据编号生成更新
//                        JSONObject sequence = new JSONObject();
//                        sequence.put(StConstant.TAB_ST_C_DISTRIBUTION, "");
//                        SequenceExec seqGenerate = SequenceGenUtil.preGenerateSequence()
//                                .add("SEQ_ST_C_DISTRIBUTION", sequence,
//                                        copyMainDO.getId(), mapper, "updateDistributionSequence");
//                        seqGenerate.exec();
                        //6.拆分保存经销商信息
                        String[] customerIdArr = customerIdStr.split(",");
                        List<StCDistributionCustomerDO> customerList = insertCustomerItem(querySession, copyMainDO.getId(), customerIdArr);
                        //7.推送数据至ES
                        try{
                            ElasticSearchUtil.indexDocument(EsConstant.ST_C_DISTRIBUTION_INDEX, EsConstant.ST_C_DISTRIBUTION_TYPE, copyMainDO, copyMainDO.getId());
                            for(StCDistributionCustomerDO customerDO : customerList){
                                ElasticSearchUtil.indexDocument(EsConstant.ST_C_DISTRIBUTION_INDEX, EsConstant.ST_C_DISTRIBUTION_CUSTOMER_TYPE, customerDO, customerDO.getId(),copyMainDO.getId());
                            }
                        }catch(IOException e){
                            throw new Exception("复制分销代销方案["+mainDO.getBillNo()+"]数据推送ES失败！");
                        }
                    } catch (Exception e) {
                        errMessage = e.getMessage();
                        break;
                    }
                }
                //商品明细复制
                if(!CollectionUtils.isEmpty(itemList)){
                    for(StCDistributionItemDO item : itemList){
                        StCDistributionItemDO copyItemDO = new StCDistributionItemDO();
                        try {
                            BeanUtils.copyProperties(item,copyItemDO);
                            copyItemDO.setId(ModelUtil.getSequence(StConstant.TAB_ST_C_DISTRIBUTION_ITEM));
                            copyItemDO.setStCDistributionId(copyMainDO.getId());
                            StBeanUtils.makeCreateField(copyItemDO, querySession.getUser());
                            copyItemDO.setOwnerename(querySession.getUser().getEname());
                            copyItemDO.setModifierename(querySession.getUser().getEname());
                            copyItemDO.setStCDistributionId(copyMainDO.getId());
                            if (itemMapper.insert(copyItemDO) < 1) {
                                throw new NDSException("方案["+mainDO.getBillNo()+"]复制商品明细插入失败");
                            }
                        } catch (Exception e) {
                            errMessage = e.getMessage();
                            break;
                        }
                    }
                }

            }
        }
        if(StringUtil.isNotEmpty(errMessage)){
            valueHolderV14.setCode(-1);
            valueHolderV14.setMessage(errMessage);
        }else{
            valueHolderV14.setCode(0);
            valueHolderV14.setMessage("方案复制成功！");
        }
        return valueHolderV14;
    }
    /**
     * @Descroption 设置转换后的经销商ID
     * @Author: 洪艺安
     * @Date 2019/5/7
     * @param mainnDO
     * @param customerId
     * @return  void
     */
    private void setCustomerId(StCDistributionDO mainDO, String customerId){
        String customerJson = getCustomerJson(customerId);
        mainDO.setCpCCustomerIdSet(customerJson);
    }
    /**
     * @Descroption 转换经销商ID为json格式
     * @Author: 洪艺安
     * @Date 2019/5/7
     * @param customerId
     * @return  java.lang.String
     */
    private String getCustomerJson(String customerId){
        String[] customerStrArr = customerId.split(",");
        String[] commonArr = {};
        long[] customerLongArr = (long[])ConvertUtils.convert(customerStrArr,long.class);
        PopCheckboxJson popCheckboxJson = new PopCheckboxJson();
        popCheckboxJson.setTotal(customerStrArr.length);
        popCheckboxJson.setIdArray(commonArr);
        //设置PopCheckboxValueJson
        PopCheckboxValueJson popCheckboxValueJson = new PopCheckboxValueJson();
        popCheckboxValueJson.setTablename("CP_C_CUSTOMER");
        popCheckboxValueJson.setCondition(commonArr);
        popCheckboxValueJson.setNotin(commonArr);
        popCheckboxValueJson.setExclude(commonArr);
        popCheckboxValueJson.setGlobal("");
        popCheckboxValueJson.setIn(customerLongArr);
        popCheckboxJson.setPopCheckboxValue(popCheckboxValueJson);
        //设置PopCheckboxListsJson
        PopCheckboxListsJson popCheckboxListsJson = new PopCheckboxListsJson();
        List<PopCheckboxResultJson> resultList = new ArrayList();
        for(int i=0; i<customerLongArr.length; i++){
            long screen = customerLongArr[i];
            long[] screenArr = {screen};
            PopCheckboxResultJson popCheckboxResultJson = new  PopCheckboxResultJson();
            CpCustomer customerDO = rpcCpService.getStoreCustomerById(screen);
            if(customerDO != null){
                popCheckboxResultJson.setScreenString(customerDO.getEcode());
            }
            popCheckboxResultJson.setScreen(screen);
            popCheckboxResultJson.setExclude(false);
            popCheckboxResultJson.setIdList(screenArr);
            resultList.add(popCheckboxResultJson);
        }
        popCheckboxListsJson.setPopCheckboxResultJson(resultList);
        popCheckboxJson.setPopCheckboxLists(popCheckboxListsJson);
        return JsonUtils.toJsonString(popCheckboxJson);
    }

    /**
     * @Descroption 插入经销商明细表
     * @Author: 洪艺安
     * @Date 2019/5/8
     * @param session
     * @param mainId
     * @param customerIds
     * @return  java.util.List<com.jackrain.nea.st.model.table.StCDistributionCustomerDO>
     */
    private List<StCDistributionCustomerDO> insertCustomerItem(QuerySession session, Long mainId, String[] customerIds) {
        List<StCDistributionCustomerDO> customerList = new ArrayList<>();
        for (int i=0; i< customerIds.length; i++) {
            long customerIdLong = Long.valueOf(customerIds[i]);
            StCDistributionCustomerDO stCDistributionCustomer = new StCDistributionCustomerDO();
            stCDistributionCustomer.setId(ModelUtil.getSequence(StConstant.TAB_ST_C_DISTRIBUTION_CUSTOMER));
            stCDistributionCustomer.setStCDistributionId(mainId);
            stCDistributionCustomer.setCpCCustomerId(customerIdLong);
            StBeanUtils.makeCreateField(stCDistributionCustomer, session.getUser());
            stCDistributionCustomer.setOwnerename(session.getUser().getEname());
            stCDistributionCustomer.setModifierename(session.getUser().getEname());
            customerList.add(stCDistributionCustomer);
            if (customerMapper.insert(stCDistributionCustomer) < 1) {
                throw new NDSException("分销代销策略经销商明细插入失败！");
            }
        }
        return customerList;
    }
}
