package com.jackrain.nea.st.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.st.mapper.StCCustomLabelMapper;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * @ClassName : StCCustomLabelDeleteService  
 * @Description : 
 * <AUTHOR>  YCH
 * @Date: 2021-11-29 11:16  
 */
@Component
@Slf4j
public class StCCustomLabelDeleteService extends CommandAdapter {
    @Autowired
    private StCCustomLabelMapper stCCustomLabelMapper;
    @Override
    public ValueHolder execute(QuerySession session){
        DefaultWebEvent event = session.getEvent();
        JSONObject param = JSON.parseObject(
                JSON.toJSONStringWithDateFormat(event.getParameterValue("param"), "yyyy-MM-dd HH:mm:ss"),
                Feature.OrderedField);
        ValueHolder valueHolder = new ValueHolder();
        boolean delMainFlag = param.getBoolean("isdelmtable");
        Long id = param.getLong("objid");
        if (delMainFlag) {
            // 删除主表
            if (Objects.nonNull(id) && id > 0) {
                //主表删除写在这里
                if (stCCustomLabelMapper.deleteById(id)>0){
                    valueHolder.put("code", ResultCode.SUCCESS);
                    valueHolder.put("message", "删除成功！");
                    return valueHolder;
                }
            }
        }

        throw new NDSException("当前记录已不存在！");
    }
}
