package com.jackrain.nea.st.services;

import com.jackrain.nea.st.mapper.StCShopStrategyMapper;
import com.jackrain.nea.st.model.table.StCShopStrategyDO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 查询默认仓库
 */
@Component
@Slf4j
public class ShopStrategyDefaultStoreQueryService {
    @Autowired
    private StCShopStrategyMapper stCShopStrategyMapper;

    /**
     * 查询默认仓库
     * @param shopId
     * @return
     */
    public List<StCShopStrategyDO> selectDefaultStore (Long shopId){
        return stCShopStrategyMapper.selectDefaultStore(shopId);
    }

}
