package com.jackrain.nea.st.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.google.common.base.Throwables;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.st.common.StConstant;
import com.jackrain.nea.st.mapper.StCVirtualHighStockItemMapper;
import com.jackrain.nea.st.mapper.StCVirtualHighStockMapper;
import com.jackrain.nea.st.model.table.StCShopVirtualHighStockDO;
import com.jackrain.nea.st.model.table.StCShopVirtualHighStockItemDO;
import com.jackrain.nea.st.utils.StBeanUtils;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.HashMap;

/**
 * <AUTHOR> ShiLong
 * @Date: 2020/6/22 10:26 下午
 * @Desc: 反审核逻辑（店铺商品虚高库存）
 */
@Component
@Slf4j
public class StCVirtualHighStockReverseAuditService extends CommandAdapter {
    @Autowired
    private StCVirtualHighStockMapper stCVirtualHighStockMapper;

    @Autowired
    private StCVirtualHighStockItemMapper stCVirtualHighStockItemMapper;

//    @Autowired
//    private ShopUniversalStockService shopUniversalStockService;

    @Override
    public ValueHolder execute(QuerySession querySession) throws NDSException {
        DefaultWebEvent event = querySession.getEvent();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(
                event.getParameterValue("param"), "yyyy-MM-dd HH:mm:ss"),
                Feature.OrderedField);
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("入参StCVirtualHighStockReverseAuditService中param:param:{}"), param.toJSONString());
        }
        ValueHolder valueHolder = new ValueHolder();
        HashMap<Long, Object> errMap = new HashMap<>();
        //生成数组
        JSONArray auditArray = makeVoidJsonArray(param);
        //列表批量
        StCVirtualHighStockReverseAuditService bean =
                ApplicationContextHandle.getBean(StCVirtualHighStockReverseAuditService.class);
        if (!CollectionUtils.isEmpty(auditArray)) {
            for (int i = 0; i < auditArray.size(); i++) {
                //遍历 jsonarray 数组，把每一个对象转成 json 对象
                Long id = Long.valueOf(auditArray.get(i).toString());
                try {
                    bean.updateAuditState(querySession,id,valueHolder);
                } catch (Exception ex) {
                    errMap.put(id, ex.getMessage());
                }
            }
        }
        //返回日志
        return StBeanUtils.getExcuteValueHolder(auditArray.size(),errMap);
    }

    @Transactional(rollbackFor = Exception.class)
    public void updateAuditState(QuerySession session, Long id, ValueHolder valueHolder) {
        StCShopVirtualHighStockDO result = stCVirtualHighStockMapper.selectById(id);
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("【反审核状态更新】result:{}"), result.toString());
        }
        checkAutocheckStatus(result, valueHolder);
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("【反审核状态更新,获取验参结果】result:{},str:{}"),valueHolder.toJSONObject(),
                    valueHolder.toString());
        }
        if (!valueHolder.isOK()) {
            return;
        }
        try {
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("【反审核状态更新,获取session用户】str:{}"), JSONObject.toJSON(session.getUser()));
            }
            //修改信息
            JSONObject jsonObject = new JSONObject();
            StBeanUtils.makeModifierField(jsonObject, session.getUser());
            StBeanUtils.makeCancelField(jsonObject, session.getUser());
            //反审-审核置空信息
            StBeanUtils.makeCancelField(jsonObject);
            result.setState(StConstant.CON_BILL_STATUS_01);
            result.setReverseAuditId(Long.valueOf(session.getUser().getId()));
            //反审核时间
            result.setReverseAuditTime(new Date());
            //反审核姓名
            result.setReverseAuditName(session.getUser().getName());
            result.setSyncStartMark(0);
            result.setSyncEndMark(0);
            result.setSyncCompleteMark(0);

            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("【反审核状态更新,即将更新】result:{}"),JSONObject.toJSON(result));
            }
            int effectedRows = stCVirtualHighStockMapper.updateById(result);
            if (effectedRows > 0) {
                if (log.isDebugEnabled()) {
                    log.debug(LogUtil.format("【反审核状态更新,获取更新结果】effectedRows:{}"),effectedRows);
                }
//                ValueHolder holder = shopUniversalStockService.deleteSgBSyncChannelStock(StConstant.VIRTUALHIGH_STRATEGY_TYPE.longValue(), id);
//                log.debug(LogUtil.format("商品虚高 反审核 删除同步库存中间表数据 策略id："+id+" 结果："+JSONObject.toJSONString(holder));
//                //推送ES数据
                try {
                    //做更新的需要先查询更新后数据库的实体在推ES
                    StCShopVirtualHighStockItemDO item = new StCShopVirtualHighStockItemDO();
                    item.setStatus(StConstant.CON_BILL_STATUS_01);
                    QueryWrapper<StCShopVirtualHighStockItemDO> wrapper = new QueryWrapper<>();
                    wrapper.eq("st_c_shop_virtual_high_stock_id", id);
                    stCVirtualHighStockItemMapper.update(item, wrapper);
                    StCVirtualHighStockDelayService.pushVirtualHighItemsToEle(result, wrapper, stCVirtualHighStockItemMapper);
                } catch (Exception ex) {
                    log.error(LogUtil.format("店铺商品虚高库存推数据到ES失败：{}"), Throwables.getStackTraceAsString(ex));
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void checkAutocheckStatus(StCShopVirtualHighStockDO stCAutocheckDO, ValueHolder valueHolder) {
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("【执行反审核参数校验】stCAutocheckDO:{},valueHolder:{}")
                    ,JSONObject.toJSON(stCAutocheckDO)
                    ,valueHolder.toJSONObject());
        }
        if (stCAutocheckDO == null) {
            throw new NDSException("请选择需要反审核的方案！");
        }
        //记录已作废，不允许作废
        if (stCAutocheckDO.getIsactive().equals(StConstant.ISACTIVE_N)) {
            throw new NDSException("该方案已作废，不允许反审核！");
        }
        if (!stCAutocheckDO.getState().equals(StConstant.CON_BILL_STATUS_02)) {
            throw new NDSException("方案状态不是已审核，不允许反审核！");
        }

        valueHolder.put("code", 0);
        valueHolder.put("message","成功");
    }

    public static JSONArray makeVoidJsonArray(JSONObject param) {
        Long objid = param.getLong("objid");
        JSONArray auditJsonArray = param.getJSONArray("objids");
        if (auditJsonArray == null) {
            auditJsonArray = param.getJSONArray("ids");
            if (auditJsonArray == null) {
                auditJsonArray = new JSONArray();
            }
        }
        if (objid == null && auditJsonArray.size() <= 0) {
            throw new NDSException("请至少选择1条记录！");
        }
        //非空单对象加入json数组
        if (objid != null && objid > 0) {
            auditJsonArray = new JSONArray();
            auditJsonArray.add(objid.toString());
        }
        return auditJsonArray;
    }

}
