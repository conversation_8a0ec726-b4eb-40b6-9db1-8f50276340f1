package com.jackrain.nea.st.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.google.common.base.Throwables;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.st.common.StConstant;
import com.jackrain.nea.st.mapper.StCVirtualHighStockItemMapper;
import com.jackrain.nea.st.mapper.StCVirtualHighStockMapper;
import com.jackrain.nea.st.model.table.StCShopVirtualHighStockDO;
import com.jackrain.nea.st.model.table.StCShopVirtualHighStockItemDO;
import com.jackrain.nea.st.utils.StBeanUtils;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.utils.AssertUtils;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.HashMap;


/**
 * <AUTHOR> ShiLong
 * @Date: 2020/6/22 10:26 下午
 * @Desc: 审核逻辑（店铺商品虚高库存）
 */
@Component
@Slf4j
public class StCVirtualHighStockAuditService extends CommandAdapter {
    @Autowired
    private StCVirtualHighStockMapper stCVirtualHighStockMapper;

    @Autowired
    private StCVirtualHighStockItemMapper stCVirtualHighStockItemMapper;

    @Override
    public ValueHolder execute(QuerySession querySession) throws NDSException {
        DefaultWebEvent event = querySession.getEvent();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(
                event.getParameterValue("param"), "yyyy-MM-dd HH:mm:ss"),
                Feature.OrderedField);
        ValueHolder valueHolder = new ValueHolder();
        HashMap<Long, Object> errMap = new HashMap<>();
        //生成数组
        JSONArray auditArray = StBeanUtils.makeAuditJsonArray(param);
        //列表批量
        if (!CollectionUtils.isEmpty(auditArray)) {
            StCVirtualHighStockAuditService bean =
                    ApplicationContextHandle.getBean(StCVirtualHighStockAuditService.class);
            for (int i = 0; i < auditArray.size(); i++) {
                Long id = Long.valueOf(auditArray.get(i).toString());
                try {
                    bean.updateAuditState(querySession, id, valueHolder);
                } catch (Exception ex) {
                    errMap.put(id, ex.getMessage());
                }
            }
        }
        //返回日志
        return StBeanUtils.getExcuteValueHolder(auditArray.size(), errMap);
    }

    @Transactional(rollbackFor = Exception.class)
    public void updateAuditState(QuerySession session, Long id, ValueHolder valueHolder) {
        StCShopVirtualHighStockDO result = stCVirtualHighStockMapper.selectById(id);
        valueHolder = checkAutocheckStatus(result, valueHolder);
        if (!valueHolder.isOK()) {
            return;
        }
        //修改信息
        StBeanUtils.makeModifierField(result, session.getUser());
        result.setState(StConstant.CON_BILL_STATUS_02);
        //审核人相关信息
        result.setAuditId(Long.valueOf(session.getUser().getId()));
        result.setAuditTime(new Date());
        result.setAuditName(session.getUser().getName());
        int update = stCVirtualHighStockMapper.updateById(result);
        if (update > 0) {
            try {
                StCShopVirtualHighStockItemDO item = new StCShopVirtualHighStockItemDO();
                item.setStatus(StConstant.CON_BILL_STATUS_02);
                item.setPlanName(result.getPlan());
                item.setBeginTime(result.getBeginTime());
                item.setEndTime(result.getEndTime());
                item.setCpCShopId(result.getCpCShopId());
                item.setCpCShopEcode(result.getCpCShopEcode());
                item.setCpCShopTitle(result.getCpCShopTitle());
                item.setMainCreationdate(result.getCreationdate());
                QueryWrapper<StCShopVirtualHighStockItemDO> wrapper = new QueryWrapper<>();
                wrapper.eq("st_c_shop_virtual_high_stock_id", id);
                stCVirtualHighStockItemMapper.update(item, wrapper);
                StCVirtualHighStockDelayService.pushVirtualHighItemsToEle(result, wrapper, stCVirtualHighStockItemMapper);
            } catch (Exception ex) {
                log.error(LogUtil.format("店铺商品虚高库存推数据到ES失败：{}"), Throwables.getStackTraceAsString(ex));
            }

        }
    }

    private ValueHolder checkAutocheckStatus(StCShopVirtualHighStockDO stCAutocheckDO, ValueHolder valueHolder) {
        if (stCAutocheckDO == null) {
            throw new NDSException("请选择需要审核的方案!");
        }
        if (stCAutocheckDO.getIsactive().equals(StConstant.ISACTIVE_N)) {
            throw new NDSException("该方案已作废，不允许审核！");
        }
        if (!stCAutocheckDO.getState().equals(StConstant.CON_BILL_STATUS_01)) {
            throw new NDSException("方案状态不是未审核，不允许审核！");
        }

        //方案没有明细记录
        int itemCount = stCVirtualHighStockItemMapper.selectCountByItemId(stCAutocheckDO.getId());
        AssertUtils.cannot(itemCount <= 0, "当前方案明细没有记录，不允许审核！");

        //结束时间小于或等于当前系统时间
        AssertUtils.cannot(stCAutocheckDO.getEndTime().compareTo(new Date()) <= 0, "结束时间小于或等于当前系统时间，不允许审核");

        valueHolder.put("code", 0);
        valueHolder.put("message", "成功");
        return valueHolder;
    }


}
