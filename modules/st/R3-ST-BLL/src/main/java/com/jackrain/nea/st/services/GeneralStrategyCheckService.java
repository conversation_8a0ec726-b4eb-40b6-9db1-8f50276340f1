//package com.jackrain.nea.st.services;
//
//
//import com.jackrain.nea.cpext.model.table.CpShop;
//import com.jackrain.nea.data.basic.model.request.ProInfoQueryRequest;
//import com.jackrain.nea.data.basic.model.request.SkuInfoQueryRequest;
//import com.jackrain.nea.data.basic.services.BasicPsQueryService;
//import com.jackrain.nea.exception.NDSException;
//import com.jackrain.nea.ps.api.result.PsCProSkuResult;
//import com.jackrain.nea.ps.api.result.PsSkuResult;
//import com.jackrain.nea.ps.api.table.PsCPro;
//import com.jackrain.nea.ps.api.table.PsCSkuExt;
//import com.jackrain.nea.sg.oms.model.request.SgChannelProductQueryForSTRequest;
//import com.jackrain.nea.sg.oms.model.result.SgChannelProductQueryForSTResult;
//import com.jackrain.nea.st.common.StConstant;
//import com.jackrain.nea.st.model.table.StCLockSkuStrategyItemDO;
//import com.jackrain.nea.st.model.table.StCProductStrategyItemDO;
//import com.jackrain.nea.st.model.table.StCShopVirtualHighStockItemDO;
//import com.jackrain.nea.st.rpc.RpcCpService;
//import com.jackrain.nea.st.rpc.RpcSgService;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.commons.lang3.StringUtils;
//import org.apache.dubbo.common.utils.CollectionUtils;
//import org.springframework.beans.BeanUtils;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.stereotype.Component;
//
//import java.util.*;
//import java.util.stream.Collectors;
//
//
///**
// * 通用策略校验()
// */
//@Component
//@Slf4j
//public class GeneralStrategyCheckService {
//
//    @Autowired
//    private RpcSgService rpcSgService;
//
//    @Autowired
//    private BasicPsQueryService basicPsQueryService;
//
//    @Autowired
//    private RpcCpService rpcCpService;
//
//
//
//    List<SgChannelProductQueryForSTResult> checkParam(String tableName,Long cpCShopId,Object ItemDOList){
//
//        List<SgChannelProductQueryForSTResult> results = new ArrayList<>();
//
//        if(!StringUtils.isEmpty(tableName)){
//            if(StConstant.TAB_ST_C_SHOP_VIRTUAL_HIGH_STOCK.equals(tableName)){
//                StCShopVirtualHighStockItemDO itemDOS = (StCShopVirtualHighStockItemDO) ItemDOList;
//
//                return check(cpCShopId,
//                        itemDOS.getPsCProId() != null ? itemDOS.getPsCProId() : null,
//                        itemDOS.getPsCSkuId() != null ? itemDOS.getPsCSkuId() : null,
//                        StringUtils.isEmpty(itemDOS.getSkuId())  ?null:itemDOS.getSkuId(),
//                        StringUtils.isEmpty(itemDOS.getNumberId()) ? null:itemDOS.getNumberId());
//            }else if(StConstant.TAB_ST_C_PRODUCT_STRATEGY.equals(tableName)){
//                StCProductStrategyItemDO itemDO = (StCProductStrategyItemDO) ItemDOList;
//
//                return check(itemDO.getCpCShopId(),
//                        itemDO.getPsCProId() != null ? itemDO.getPsCProId() : null,
//                        itemDO.getPsCSkuId() != null ? itemDO.getPsCSkuId() : null,
//                        StringUtils.isEmpty(itemDO.getPtSkuId()) ? null : itemDO.getPtSkuId(),
//                        StringUtils.isEmpty(itemDO.getPtProId()) ? null : itemDO.getPtProId());
//            }else if(StConstant.TAB_ST_C_LOCK_SKU_STRATEGY.equals(tableName)){
//                List<StCLockSkuStrategyItemDO> itemDOS = (List<StCLockSkuStrategyItemDO>) ItemDOList;
//
//                for (StCLockSkuStrategyItemDO itemDO : itemDOS) {
//                    List<SgChannelProductQueryForSTResult> queryForSTResults = check(cpCShopId,
//                            itemDO.getPsCProId() != null ? itemDO.getPsCProId() : null,
//                            itemDO.getPsCSkuId() != null ? itemDO.getPsCSkuId() : null,
//                            StringUtils.isEmpty(itemDO.getPtSkuId()) ? null : itemDO.getPtSkuId(),
//                            StringUtils.isEmpty(itemDO.getPtProId()) ? null : itemDO.getPtProId());
//                    results.addAll(queryForSTResults);
//                }
//            }
//        }
//        return results;
//    }
//
//    /**
//     *
//     * @param cpCShopId  店铺id
//     * @param psCProId  商品id
//     * @param psCSkuId  条码id
//     * @param ptskuId   平台条码id
//     * @param ptProId   平台商品id
//     * @return
//     */
//    private List<SgChannelProductQueryForSTResult> check(Long cpCShopId,Long psCProId,Long psCSkuId,String ptskuId,String ptProId){
//        if(psCProId==null && psCSkuId==null && StringUtils.isEmpty(ptskuId) && StringUtils.isEmpty(ptProId)){
//            throw new NDSException("商品信息需要填写至少一项");
//        }
//        List<SgChannelProductQueryForSTResult> results ;
//        if(!StringUtils.isEmpty(ptProId)||!StringUtils.isEmpty(ptskuId)){
//            /** 1 平台商品id，平台条码id至少有一个存在时,平台验证验证*/
//            results = selectByPt(cpCShopId,ptskuId,ptProId,psCSkuId,psCProId);
//        }else{
//            /** 2 平台商品id，平台条码id都为空时,使用商品、条码验证*/
//            results = selectByPc(cpCShopId,psCProId,psCSkuId);
//        }
//        return results;
//    }
//
//    //商品编码
//    //条码
//    private List<SgChannelProductQueryForSTResult> selectByPc(Long cpCShopId,Long psCProId,Long psCSkuId) {
//        List<SgChannelProductQueryForSTResult> results = new ArrayList<>();
//        List<PsCPro> pros = new ArrayList<>();
//        HashMap<Long, PsCProSkuResult> skuInfo = new HashMap<>();
//        if (psCProId != null) {
//            ProInfoQueryRequest proInfoQueryRequest = new ProInfoQueryRequest();
//            proInfoQueryRequest.setProIdList(Arrays.asList(psCProId));
//            pros = basicPsQueryService.getProInfo(proInfoQueryRequest);
//        }
//        if (psCSkuId != null) {
//            SkuInfoQueryRequest skuQueryRequest = new SkuInfoQueryRequest();
//            skuQueryRequest.setSkuIdList(Arrays.asList(psCSkuId));
//            skuInfo = basicPsQueryService.getSkuInfo(skuQueryRequest);
//        }
//        CpShop shop = null;
//        if (cpCShopId != null) {
//            List<CpShop> cpShops = rpcCpService.queryShopByIds(Arrays.asList(cpCShopId));
//            if (CollectionUtils.isNotEmpty(cpShops)) {
//                shop = cpShops.get(0);
//            }
//        }
//        if (CollectionUtils.isEmpty(pros) && psCProId != null) {
//            throw new NDSException("没有找到对应的商品编码");
//        }
//        if (skuInfo.size() == 0 && psCSkuId != null) {
//            throw new NDSException("没有找到对应的条码");
//        }
//        if (psCSkuId != null) {
//            SgChannelProductQueryForSTResult result = new SgChannelProductQueryForSTResult();
//            PsCProSkuResult psCProSkuResult = skuInfo.get(psCSkuId);
//            if(psCProId != null&&psCProSkuResult.getPsCProId().longValue()!=psCProId){
//                throw new NDSException("商品编码和条码不对应");
//            }
//            result.setPsCProId(psCProSkuResult.getPsCProId());
//            result.setPsCProEname(psCProSkuResult.getPsCProEname());
//            result.setPsCProEcode(psCProSkuResult.getPsCProEcode());
//            result.setPsCSkuId(psCProSkuResult.getId());
//            result.setPsCSkuEcode(psCProSkuResult.getSkuEcode());
//            result.setCpCShopId(cpCShopId);
//            result.setPsCSpec1Ecode(psCProSkuResult.getClrsEcode());
//            result.setPsCSpec1Ename(psCProSkuResult.getClrsEname());
//            result.setPsCSpec2Ecode(psCProSkuResult.getSizesEcode());
//            result.setPsCSpec2Ename(psCProSkuResult.getSizesEname());
//            results.add(result);
//        } else {
//            //条码为空 返回对应商品所有条码
//            PsSkuResult skuInfoByProId = basicPsQueryService.getSkuInfoByProId(psCProId);
//            List<PsCSkuExt> proSkus = skuInfoByProId.getSkuList();
//            proSkus.forEach(skus -> {
//                SgChannelProductQueryForSTResult result = new SgChannelProductQueryForSTResult();
//                result.setPsCProId(skus.getPsCProId());
//                result.setPsCProEcode(skus.getProEcode());
//                result.setPsCProEname(skus.getProEname());
//                result.setPsCSkuId(skus.getId());
//                result.setPsCSkuEcode(skus.getEcode());
//                result.setCpCShopId(cpCShopId);
//                result.setPsCSpec1Ecode(skus.getColorEcode());
//                result.setPsCSpec1Ename(skus.getColorName());
//                result.setPsCSpec2Ecode(skus.getSizeEcode());
//                result.setPsCSpec2Ename(skus.getSizeName());
//                result.setGbcode(skus.getGbcode());
//                results.add(result);
//            });
//        }
//        //补充店铺名称信息
//        if (shop != null && CollectionUtils.isNotEmpty(results) ) {
//            for (SgChannelProductQueryForSTResult obj : results) {
//                obj.setCpCShopTitle(shop.getCpCShopTitle());
//            }
//        }
//        return results;
//    }
//
//
//    private List<SgChannelProductQueryForSTResult> selectByPt(Long cpCShopId,String ptskuId,String ptProId,Long psCSkuId, Long psCProId){
//        //平台验证
//        SgChannelProductQueryForSTRequest queryForSTRequest = new SgChannelProductQueryForSTRequest();
//        List<String> ptskulist = Arrays.asList(ptskuId);
//        List<String> ptprolist = Arrays.asList(ptProId);
//        queryForSTRequest.setCpCShopId(cpCShopId);
//        if (ptskuId!=null) {
//            queryForSTRequest.setPtSkuIdList(ptskulist);
//        } else if (ptProId!=null) {
//            queryForSTRequest.setPtProIdList(ptprolist);
//        }
//        Integer count = rpcSgService.queryChannelProductCount(queryForSTRequest);
//        if (count.intValue()==0) {
//            return new ArrayList<>();
//        }
//        List<SgChannelProductQueryForSTResult> list = rpcSgService.queryChannelProduct(queryForSTRequest);
//        //录入明细时，如果只有平台商品id则按平台商品id级别录入不再冗余 平台sku_id
//        if (CollectionUtils.isNotEmpty(list)) {
//            for (SgChannelProductQueryForSTResult item : list) {
//                Long shopId = item.getCpCShopId();
//                String cpCShopTitle = item.getCpCShopTitle();
//                String skuId = item.getSkuId();
//                String numiid = item.getNumiid();
//                SgChannelProductQueryForSTResult result = new SgChannelProductQueryForSTResult();
//                /** 1. 中台条码、中台商品都为空时 */
//                BeanUtils.copyProperties(result, item);
//                item.setCpCShopId(shopId);
//                item.setCpCShopTitle(cpCShopTitle);
//                if(StringUtils.isNotEmpty(ptskuId)) {
//                    item.setSkuId(skuId);
//                } else if (StringUtils.isNotEmpty(ptProId)) {
//                    item.setNumiid(numiid);
//                }
//            }
//            //平台条码置空后去重
//            Map<String, SgChannelProductQueryForSTResult> resultMap = list.stream().collect(Collectors.toMap(a ->
//                    new StringBuffer()
//                            .append(a.getCpCShopId()).append(":")
//                            .append(a.getPsCSkuId()).append(":")
//                            .append(a.getPsCProId()).append(":")
//                            .append(a.getSkuId()).append(":")
//                            .append(a.getNumiid()).toString(), b -> b, (k1, k2) -> k1));
//            list = resultMap.values().stream().collect(Collectors.toList());
//        }
//        return list;
//    }
//
//}
