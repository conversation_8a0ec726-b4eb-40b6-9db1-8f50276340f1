package com.jackrain.nea.st.services;/**
 * <AUTHOR>
 * @create 2021/11/24 10:41
 */

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.st.common.StConstant;
import com.jackrain.nea.st.mapper.StCOrderLabelItemMapper;
import com.jackrain.nea.st.mapper.StCOrderLabelLogMapper;
import com.jackrain.nea.st.mapper.StCOrderLabelMapper;
import com.jackrain.nea.st.mapper.StCOrderLabelShopItemMapper;
import com.jackrain.nea.st.model.table.StCOrderLabelDO;
import com.jackrain.nea.st.utils.StBeanUtils;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Objects;

/**
 * @ClassName : StCOrderLabelDeleteService  
 * @Description : 
 * <AUTHOR>  YCH
 * @Date: 2021-11-24 10:41  
 */
@Service
public class StCOrderLabelDeleteService extends CommandAdapter {

    @Autowired
    private StCOrderLabelMapper stCOrderLabelMapper;

    @Autowired
    private StCOrderLabelItemMapper stCOrderLabelItemMapper;
    @Autowired
    private StCOrderLabelShopItemMapper shopItemMapper;

    @Autowired
    private StCOrderLabelLogMapper stCOrderLabelLogMapper;

    @Override
    public ValueHolder execute(QuerySession session){
        DefaultWebEvent event = session.getEvent();
        JSONObject param = JSON.parseObject(
                JSON.toJSONStringWithDateFormat(event.getParameterValue("param"), "yyyy-MM-dd HH:mm:ss"),
                Feature.OrderedField);

        boolean delMainFlag = param.getBoolean("isdelmtable");//是否删除主表
        Long id = param.getLong("objid");

        if (delMainFlag) {
            // 删除主表
            if (Objects.nonNull(id) && id > 0) {
                //主表删除写在这里
            }
        } else {
            // 删除明细
            JSONObject tabitem = param.getJSONObject("tabitem");
            if (!CollectionUtils.isEmpty(tabitem)) {
                JSONArray itemArray = tabitem.getJSONArray(StConstant.TAB_ST_C_ORDER_LABEL_ITEM);
                JSONArray errorArray = new JSONArray();
                if (!CollectionUtils.isEmpty(itemArray)) {
                    deleteItemByID(itemArray, errorArray);
                }
                JSONArray strategyItemArray = tabitem.getJSONArray(StConstant.TAB_ST_C_ORDER_LABEL_STRATEGY_ITEM);
                if (!CollectionUtils.isEmpty(strategyItemArray)) {
                    deleteItemByID(strategyItemArray, errorArray);
                }
                JSONArray shopItemArray = tabitem.getJSONArray(StConstant.TAB_ST_C_ORDER_LABEL_SHOP_ITEM);
                if (!CollectionUtils.isEmpty(shopItemArray)) {
                    deleteShopItemByID(shopItemArray, errorArray);
                }

                //修改主表信息
                updateHoldOrder(id,session);
                return StBeanUtils.getExcuteValueHolder(errorArray);
            }
        }

        throw new NDSException("当前记录已不存在！");
    }

    private void updateHoldOrder(Long id, QuerySession session) {
        StCOrderLabelDO stCOrderLabelDO =new StCOrderLabelDO();
        stCOrderLabelDO.setId(id);
        StBeanUtils.makeModifierField(stCOrderLabelDO,session.getUser());
        int n = stCOrderLabelMapper.updateById(stCOrderLabelDO);
        if (n<=0){
            throw new NDSException("修改主表的信息失败！");
        }
    }

    private void deleteItemByID(JSONArray itemArray, JSONArray errorArray) {
        for (int i = 0; i < itemArray.size(); i++) {
            Long itemid = itemArray.getLong(i);
            if ((stCOrderLabelItemMapper.deleteById(itemid)) <= 0) {
                errorArray.add(StBeanUtils.getJsonObjectInfo(itemid, "删除明细失败"));
            }
        }
    }
    private void deleteShopItemByID(JSONArray itemArray, JSONArray errorArray) {
        for (int i = 0; i < itemArray.size(); i++) {
            Long itemid = itemArray.getLong(i);
            if ((shopItemMapper.deleteById(itemid)) <= 0) {
                errorArray.add(StBeanUtils.getJsonObjectInfo(itemid, "删除明细失败"));
            }
        }
    }
}
