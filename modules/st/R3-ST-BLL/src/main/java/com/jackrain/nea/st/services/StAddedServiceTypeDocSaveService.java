package com.jackrain.nea.st.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.st.annotation.StOperationLog;
import com.jackrain.nea.st.common.StConstant;
import com.jackrain.nea.st.mapper.StAddedServiceTypeDocMapper;
import com.jackrain.nea.st.model.table.StAddedServiceTypeDocDO;
import com.jackrain.nea.st.utils.JsonUtils;
import com.jackrain.nea.st.utils.StBeanUtils;
import com.jackrain.nea.st.utils.ValueHolderUtils;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

/**
 * @program: ryytn-st-v3.0
 * @description: 增值服务策略类型档案保存
 * @author: haiyang
 * @create: 2023-10-20 14:46
 **/
@Component
@Slf4j
@Transactional
public class StAddedServiceTypeDocSaveService extends CommandAdapter {

    @Autowired
    private StAddedServiceTypeDocMapper stAddedServiceTypeDocMapper;

    @StOperationLog(mainTableName = "ST_ADDED_SERVICE_TYPE_DOC")
    public ValueHolder execute(QuerySession session) throws NDSException {
        DefaultWebEvent event = session.getEvent();
        List<String> nullKeyList = new ArrayList<>();
        JSONObject param = JsonUtils.filterNull((JSONObject) event.getParameterValue("param"), nullKeyList);

        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("StAddedServiceTypeDocSaveService.param=") + param.toJSONString());
        }
        Long id = param.getLong("objid");
        JSONObject fixColumn = param.getJSONObject("fixcolumn");
        if (fixColumn != null) {
            if (id != null && id > 0) {
                return updateAddedServiceTypeDoc(session, fixColumn, id);
            } else {
                return addAddedServiceTypeDoc(session, fixColumn);
            }
        }
        throw new NDSException("当前记录已不存在！");
    }

    private ValueHolder addAddedServiceTypeDoc(QuerySession session, JSONObject fixColumn) {
        String columnString = fixColumn.getString(StConstant.ST_ADDED_SERVICE_TYPE_DOC);
        if (StringUtils.isNotEmpty(columnString)) {
            StAddedServiceTypeDocDO addedServiceTypeDocDO = JSON.parseObject(columnString, StAddedServiceTypeDocDO.class);
            checkAddedServiceDocExist(addedServiceTypeDocDO);
            addedServiceTypeDocDO.setId(ModelUtil.getSequence(StConstant.ST_ADDED_SERVICE_TYPE_DOC));

            //基本字段值设置
            StBeanUtils.makeCreateField(addedServiceTypeDocDO, session.getUser());//创建修改等信息
            addedServiceTypeDocDO.setModifierename(session.getUser().getEname());//修改人账号
            addedServiceTypeDocDO.setOwnerename(session.getUser().getEname());//创建人账号

            if ((stAddedServiceTypeDocMapper.insert(addedServiceTypeDocDO)) > 0) {
                return ValueHolderUtils.getSuccessValueHolder(addedServiceTypeDocDO.getId(), StConstant.ST_ADDED_SERVICE_TYPE_DOC);
            } else {
                return ValueHolderUtils.getFailValueHolder("保存失败");
            }
        }
        throw new NDSException("当前表" + StConstant.ST_ADDED_SERVICE_TYPE_DOC + "不存在！");

    }



    private ValueHolder updateAddedServiceTypeDoc(QuerySession session, JSONObject fixColumn, Long id) {
        ValueHolder valueHolder = new ValueHolder();
        if (!checkExistById(id, valueHolder)) {
            return valueHolder;
        }
        String columnString = fixColumn.getString(StConstant.ST_ADDED_SERVICE_TYPE_DOC);
        if (StringUtils.isNotEmpty(columnString)) {
            log.info("updateAddedServiceTypeDoc.columnString: {}", columnString);
            StAddedServiceTypeDocDO addedServiceTypeDocDO = JSON.parseObject(columnString, StAddedServiceTypeDocDO.class);
            addedServiceTypeDocDO.setId(id);
            checkAddedServiceDocExist(addedServiceTypeDocDO);
            StBeanUtils.makeModifierField(addedServiceTypeDocDO, session.getUser());//基本字段值设置-修改信息
            addedServiceTypeDocDO.setModifierename(session.getUser().getEname());//修改人账号
            int i = stAddedServiceTypeDocMapper.updateById(addedServiceTypeDocDO);
            if (i < 0) {
                log.debug(LogUtil.format("更新增值服务策略类型档案失败！"));
                return ValueHolderUtils.getFailValueHolder("保存失败");
            }
        }
        return ValueHolderUtils.getSuccessValueHolder(id, StConstant.ST_ADDED_SERVICE_TYPE_DOC);

    }

    private boolean checkExistById(Long id, ValueHolder valueHolder) {
        StAddedServiceTypeDocDO addedServiceTypeDocDO = stAddedServiceTypeDocMapper.selectById(id);
        if (null == addedServiceTypeDocDO) {
            valueHolder.put("code", -1);
            valueHolder.put("message", "当前记录已不存在！");
            return false;
        }
        return true;
    }

    private void checkAddedServiceDocExist(StAddedServiceTypeDocDO addedServiceTypeDocDO) {
        String errMessage = "";
        String addedTypeCode = addedServiceTypeDocDO.getAddedTypeCode();
        String addedTypeName = addedServiceTypeDocDO.getAddedTypeName();
        if (StringUtils.isNotEmpty(addedTypeCode)) {
            StAddedServiceTypeDocDO docDO = stAddedServiceTypeDocMapper.selectByTypeCode(addedTypeCode);
            if (null != docDO) {
                errMessage = "增值服务编码" + addedTypeCode + "已存在!";
                throw new NDSException(errMessage);
            }

        }
        if (StringUtils.isNotEmpty(addedTypeName)) {
            StAddedServiceTypeDocDO docDO = stAddedServiceTypeDocMapper.selectByTypeName(addedTypeName);
            if (null != docDO) {
                errMessage = "增值服务名称" + addedTypeName + "已存在!";
                throw new NDSException(errMessage);
            }
        }
    }

}
