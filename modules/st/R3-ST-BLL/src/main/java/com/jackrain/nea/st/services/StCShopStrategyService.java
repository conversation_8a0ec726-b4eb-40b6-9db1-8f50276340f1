package com.jackrain.nea.st.services;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.burgeon.r3.sg.core.enums.YesNoEnum;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.cpext.model.table.CpCLogistics;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.st.common.StConstant;
import com.jackrain.nea.st.mapper.StCShopStrategyLogisticsItemMapper;
import com.jackrain.nea.st.mapper.StCShopStrategyMapper;
import com.jackrain.nea.st.model.enums.OperationTypeEnum;
import com.jackrain.nea.st.model.enums.StCShopStrategyLogisticsRuleEnum;
import com.jackrain.nea.st.model.enums.StCShopStrategyLogisticsTypeEnum;
import com.jackrain.nea.st.model.table.StCOperationLogDO;
import com.jackrain.nea.st.model.table.StCShopStrategyDO;
import com.jackrain.nea.st.model.table.StCShopStrategyLogisticsItem;
import com.jackrain.nea.st.request.StCShopStrategyLogisticsSaveRequest;
import com.jackrain.nea.st.utils.StBeanUtils;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
@Transactional
public class StCShopStrategyService {

    @Autowired
    private StCShopStrategyMapper mapper;

    @Autowired
    private StCShopStrategyLogisticsItemMapper itemMapper;

    @Resource
    private LogCommonService logCommonService;

    @Transactional(rollbackFor = Exception.class)
    public ValueHolderV14 save(StCShopStrategyLogisticsSaveRequest request) {
        ValueHolderV14 v14 = new ValueHolderV14(ResultCode.SUCCESS, "保存成功");
        //参数校验
        checkParam(request, v14);
        if (!v14.isOK()) {
            return v14;
        }
        List<StCOperationLogDO> operationLogList = new ArrayList<>();
        //构建主表更新日志
        mainTableAddLog(request, operationLogList);
        //更新策略主表
        mapper.batchUpdateByIds(request);
        //如果为不做要求的类型就删除子表
        if (StCShopStrategyLogisticsTypeEnum.STATUS_0.getCode().equals(request.getLogisticsType())) {
            //删除并记录日志
            List<StCShopStrategyLogisticsItem> itemList = itemMapper.selectList(new LambdaQueryWrapper<StCShopStrategyLogisticsItem>()
                    .in(StCShopStrategyLogisticsItem::getStCShopStrategyId, request.getObjids()));
            deleteItemAndAddlog(request, operationLogList, itemList);
        }
        //按照更新规则更新策略子表
        List<StCShopStrategyLogisticsItem> insertItemList = new ArrayList<>();
        if (StCShopStrategyLogisticsRuleEnum.STATUS_0.getCode().equals(request.getRule())) {
            //追加 先查询明细，确保追加的数据不会重复
            List<StCShopStrategyLogisticsItem> logisticsItems =
                    itemMapper.selectList(new LambdaQueryWrapper<StCShopStrategyLogisticsItem>()
                            .in(StCShopStrategyLogisticsItem::getStCShopStrategyId, request.getObjids())
                            .in(StCShopStrategyLogisticsItem::getCpCLogisticsId, request.getLogisticsList()));
            Map<String, StCShopStrategyLogisticsItem> logisticsItemMap = new HashMap<>();
            if (CollectionUtils.isNotEmpty(logisticsItems)) {
                logisticsItemMap = logisticsItems.stream()
                        .collect(Collectors.toMap(a -> a.getStCShopStrategyId() + "&" + a.getCpCLogisticsId(), a -> a));
            }
            //查询物流公司
            Map<Long, CpCLogistics> logisticsMap = queryCpCLogistics(request);
            for (Long objId : request.getObjids()) {
                for (Long logisticsId : request.getLogisticsList()) {
                    if (logisticsItemMap.get(objId + "&" + logisticsId) != null) {
                        continue;
                    }
                    buildItemModel(request, logisticsMap, insertItemList, objId, logisticsId);
                }
            }
            //增加新增日志
            addItemAndAddLog(request, operationLogList, insertItemList);
        } else if (StCShopStrategyLogisticsRuleEnum.STATUS_1.getCode().equals(request.getRule())) {
            //删除并记录删除日志
            List<StCShopStrategyLogisticsItem> itemList = itemMapper.selectList(new LambdaQueryWrapper<StCShopStrategyLogisticsItem>()
                    .in(StCShopStrategyLogisticsItem::getStCShopStrategyId, request.getObjids())
                    .in(StCShopStrategyLogisticsItem::getCpCLogisticsId, request.getLogisticsList()));
            deleteItemAndAddlog(request, operationLogList, itemList);
        } else if (StCShopStrategyLogisticsRuleEnum.STATUS_2.getCode().equals(request.getRule())) {
            //覆盖
            //删除并记录日志
            List<StCShopStrategyLogisticsItem> itemList = itemMapper.selectList(new LambdaQueryWrapper<StCShopStrategyLogisticsItem>()
                    .in(StCShopStrategyLogisticsItem::getStCShopStrategyId, request.getObjids()));
            deleteItemAndAddlog(request, operationLogList, itemList);
            //查询物流公司
            Map<Long, CpCLogistics> logisticsMap = queryCpCLogistics(request);
            for (Long objId : request.getObjids()) {
                for (Long logisticsId : request.getLogisticsList()) {
                    buildItemModel(request, logisticsMap, insertItemList, objId, logisticsId);
                }
            }
            //增加新增日志
            addItemAndAddLog(request, operationLogList, insertItemList);
        }

        if (CollectionUtils.isNotEmpty(insertItemList)) {
            itemMapper.batchInsert(insertItemList);
        }
        if (CollectionUtils.isNotEmpty(operationLogList)) {
            logCommonService.batchInsertLog(operationLogList);
        }
        return v14;
    }

    private void addItemAndAddLog(StCShopStrategyLogisticsSaveRequest request, List<StCOperationLogDO> operationLogList, List<StCShopStrategyLogisticsItem> insertItemList) {
        if (CollectionUtils.isNotEmpty(insertItemList)) {
            for (StCShopStrategyLogisticsItem logisticsItem : insertItemList) {
                StCOperationLogDO operationLog = logCommonService.getOperationLog("ST_C_SHOP_STRATEGY",
                        OperationTypeEnum.ADD.getOperationValue(), logisticsItem.getStCShopStrategyId(),
                        "店铺策略-物流公司明细", "物流公司", null,
                        logisticsItem.getCpCLogisticsEname(), request.getUser());
                operationLogList.add(operationLog);
            }
        }
    }

    private void deleteItemAndAddlog(StCShopStrategyLogisticsSaveRequest request, List<StCOperationLogDO> operationLogList, List<StCShopStrategyLogisticsItem> itemList) {
        if (CollectionUtils.isNotEmpty(itemList)) {
            Set<Long> itemIds = itemList.stream().map(StCShopStrategyLogisticsItem::getId).collect(Collectors.toSet());
            itemMapper.deleteBatchIds(itemIds);
            //增加删除日志
            for (StCShopStrategyLogisticsItem logisticsItem : itemList) {
                String beforValue = "[" + logisticsItem.getCpCLogisticsEname() + "],["
                        + (YesNoEnum.Y.getKey().equals(logisticsItem.getIsactive()) ? "是" : "否") + "]";
                StCOperationLogDO operationLog = logCommonService.getOperationLog("ST_C_SHOP_STRATEGY",
                        OperationTypeEnum.DEL.getOperationValue(), logisticsItem.getStCShopStrategyId(),
                        "店铺策略-物流公司明细", "删除店铺策略-物流公司明细", beforValue,
                        null, request.getUser());
                operationLogList.add(operationLog);
            }
        }
    }

    private void mainTableAddLog(StCShopStrategyLogisticsSaveRequest request, List<StCOperationLogDO> operationLogList) {
        List<StCShopStrategyDO> strategyList = mapper.selectBatchIds(request.getObjids());
        if (CollectionUtils.isNotEmpty(strategyList)) {
            for (StCShopStrategyDO strategy : strategyList) {
                if (!ObjectUtil.equals(String.valueOf(strategy.getLogisticsType()), request.getLogisticsType())) {
                    StCOperationLogDO operationLog = logCommonService.getOperationLog("ST_C_SHOP_STRATEGY",
                            OperationTypeEnum.MOD.getOperationValue(), strategy.getId(), "店铺策略", "发货物流公司",
                            StCShopStrategyLogisticsTypeEnum.getByCode(String.valueOf(strategy.getLogisticsType())).getDesc(),
                            StCShopStrategyLogisticsTypeEnum.getByCode(request.getLogisticsType()).getDesc(), request.getUser());
                    operationLogList.add(operationLog);
                }
            }
        }
    }

    /**
     * 查询物流公司
     *
     * @param request
     * @return
     */
    private Map<Long, CpCLogistics> queryCpCLogistics(StCShopStrategyLogisticsSaveRequest request) {
        Map<Long, CpCLogistics> logisticsMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(request.getLogisticsList())) {
            List<CpCLogistics> logisticsList = mapper.queryLogistics(request);
            logisticsMap = logisticsList.stream().collect(Collectors.toMap(CpCLogistics::getId, k -> k));
        }
        return logisticsMap;
    }

    /**
     * 参数校验
     *
     * @param request
     * @param v14
     */
    private void checkParam(StCShopStrategyLogisticsSaveRequest request, ValueHolderV14 v14) {
        if (request == null || CollectionUtils.isEmpty(request.getObjids())) {
            v14.setCode(ResultCode.FAIL);
            v14.setMessage("请求参数不能为空");
            return;
        }
        if (request.getUser() == null) {
            v14.setCode(ResultCode.FAIL);
            v14.setMessage("用户不能为空");
            return;
        }
        if (!StCShopStrategyLogisticsTypeEnum.STATUS_0.getCode().equals(request.getLogisticsType())
                && request.getRule() == null) {
            v14.setCode(ResultCode.FAIL);
            v14.setMessage("请选择物流公司明细更新规则！");
            return;
        }
        if (!StCShopStrategyLogisticsTypeEnum.STATUS_0.getCode().equals(request.getLogisticsType())
                && CollectionUtils.isEmpty(request.getLogisticsList())) {
            v14.setCode(ResultCode.FAIL);
            v14.setMessage("请添加物流公司！");
        }
    }

    /**
     * 构建明细表参数
     *
     * @param request
     * @param logisticsMap
     * @param insertItemList
     * @param objId
     * @param logisticsId
     */
    private void buildItemModel(StCShopStrategyLogisticsSaveRequest request,
                                Map<Long, CpCLogistics> logisticsMap,
                                List<StCShopStrategyLogisticsItem> insertItemList,
                                Long objId, Long logisticsId) {
        CpCLogistics logistics = logisticsMap.get(logisticsId);
        if (logistics != null) {
            StCShopStrategyLogisticsItem item = new StCShopStrategyLogisticsItem();
            item.setId(ModelUtil.getSequence(StConstant.TAB_ST_C_SHOP_STRATEGY_LOGISTICS_ITEM));
            item.setStCShopStrategyId(objId);
            item.setCpCLogisticsId(logisticsId);
            item.setCpCLogisticsEcode(logistics.getEcode());
            item.setCpCLogisticsEname(logistics.getEname());
            StBeanUtils.makeCreateField(item, request.getUser());
            item.setOwnerename(request.getUser().getEname());
            item.setModifierename(request.getUser().getEname());
            item.setIsactive(StConstant.ISACTIVE_Y);
            insertItemList.add(item);
        }
    }

    public List<CpCLogistics> queryAllLogistics() {
        return mapper.queryAllLogistics();
    }
}
