package com.jackrain.nea.st.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.st.common.StConstant;
import com.jackrain.nea.st.mapper.StCVipUrgentStrategyMapper;
import com.jackrain.nea.st.model.table.StCVipUrgentStrategy;
import com.jackrain.nea.st.utils.StBeanUtils;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;

/**
 * <AUTHOR> zhuxing
 * @Date : 2021-10-24 16:11
 * @Description : 唯品会补货加急策略 - 作废服务
 **/
@Component
@Slf4j
@Transactional(rollbackFor = {Exception.class})
public class StCVipUrgentStrategyVoidService extends CommandAdapter {
    @Autowired
    private StCVipUrgentStrategyMapper stCVipUrgentStrategyMapper;

    @Override
    public ValueHolder execute(QuerySession querySession) throws NDSException {
        DefaultWebEvent event = querySession.getEvent();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(
                event.getParameterValue("param"), "yyyy-MM-dd HH:mm:ss"),
                Feature.OrderedField);

        JSONArray itemArray = StBeanUtils.makeVoidJsonArray(param);
        JSONArray errorArray = new JSONArray();
        if (itemArray.size() > 0) {
            for (int i = 0; i < itemArray.size(); i++) {
                Long itemid = itemArray.getLong(i);
                voidUrgentStrategy(querySession, itemid, errorArray);
            }
        }
        return StBeanUtils.getProcessValueHolder(itemArray, errorArray, StConstant.CON_BILL_ACTION_VOID);
    }

    public void voidUrgentStrategy(QuerySession querySession,Long id,JSONArray errorArray){
        try{
            //查询该记录
            StCVipUrgentStrategy stCVipUrgentStrategy = stCVipUrgentStrategyMapper.selectById(id);
            if(stCVipUrgentStrategy == null){
                throw new NDSException("当前记录已不存在！");
            }
            String isactive = stCVipUrgentStrategy.getIsactive();
            if(!StConstant.ISACTIVE_Y.equals(isactive)){
                throw new NDSException("当前记录已作废，不允许作废！");
            }
            //作废
            User user = querySession.getUser();
            StCVipUrgentStrategy updateStrategy = new StCVipUrgentStrategy();
            updateStrategy.setId(id);
            updateStrategy.setIsactive(StConstant.ISACTIVE_N);
            updateStrategy.setDelerId(Long.valueOf(user.getId()));
            updateStrategy.setDelerName(user.getName());
            updateStrategy.setDelerEname(user.getEname());
            updateStrategy.setDelerDate(new Date());
            stCVipUrgentStrategyMapper.updateById(updateStrategy);
        }catch (Exception e){
            log.error("唯品会补货加急策略 ID:{}作废失败",id);
            String errorMsg = e.getMessage();
            errorArray.add(StBeanUtils.getJsonObjectInfo(id, errorMsg));
        }
    }
}
