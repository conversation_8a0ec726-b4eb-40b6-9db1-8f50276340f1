//package com.jackrain.nea.st.services;
//
//import com.alibaba.fastjson.JSONArray;
//import com.alibaba.fastjson.JSONObject;
//import com.jackrain.nea.cpext.model.table.CpShop;
//import com.jackrain.nea.resource.SystemUserResource;
//import com.jackrain.nea.sg.oms.api.SgBChannelStorageBufferCmd;
//import com.jackrain.nea.sg.oms.api.SgChannelProductCalcAndSyncCmd;
//import com.jackrain.nea.sg.oms.common.enums.ChannelStoragePoolTypeEnum;
//import com.jackrain.nea.sg.oms.model.request.SgChannelStorageBufferBatchSaveRequest;
//import com.jackrain.nea.sg.oms.model.request.SgChannelStorageBufferSaveRequest;
//import com.jackrain.nea.sg.oms.model.tableExtend.SgBChannelStorageBufferExtend;
//import com.jackrain.nea.st.mapper.*;
//import com.jackrain.nea.st.model.enums.StrategySyncMarkEnum;
//import com.jackrain.nea.st.model.table.*;
//import com.jackrain.nea.st.rpc.RpcCpService;
//import com.jackrain.nea.sys.domain.ValueHolderV14;
//import com.jackrain.nea.util.ValueHolder;
//import com.jackrain.nea.web.face.User;
//import com.jackrain.nea.web.query.DefaultWebEvent;
//import com.jackrain.nea.web.query.QuerySession;
//import com.jackrain.nea.web.query.QuerySessionImpl;
//import org.apache.commons.collections.CollectionUtils;
//import org.apache.commons.collections.MapUtils;
//import org.apache.commons.lang.StringUtils;
//import org.apache.curator.shaded.com.google.common.collect.Maps;
//import org.apache.dubbo.config.annotation.Reference;
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.beans.factory.annotation.Value;
//import org.springframework.stereotype.Service;
//import org.springframework.transaction.annotation.Transactional;
//
//import javax.annotation.Resource;
//import java.util.List;
//import java.util.Map;
//import java.util.function.Function;
//import java.util.stream.Collectors;
//
///**
// * <ul>
// *     <li>
// *         扫描 店铺商品特殊设置、店铺商品虚高库存设置、店铺锁库设置、店铺锁库条码特殊设置
// *         并且生效时间开始后将策略对应的明细插入到库存计算缓存池
// *     </li>
// *     <li>
// *         这一层只负责查询数据,并且简单处理一下skuId,插入到缓存池, 并不参与缓存池的计算工作
// *     </li>
// * </ul>
// * 2020/10/30 15:51
// * r3-st - com.jackrain.nea.st.services
// *
// * <AUTHOR>
// */
//@Service
//public class StrategiesScanLayerService {
//
//    private static final Logger logger = LoggerFactory.getLogger(StrategiesScanLayerService.class);
//
//    @Resource
//    private StCProductStrategyMapper stCProductStrategyMapper;
//
//    @Resource
//    private StCProductStrategyItemMapper stCProductStrategyItemMapper;
//
//    @Resource
//    private StCLockSkuStrategyMapper stCLockSkuStrategyMapper;
//
//    @Resource
//    private StCLockSkuStrategyItemMapper stCLockSkuStrategyItemMapper;
//
//    @Resource
//    private StCLockStockStrategyItemMapper stCLockStockStrategyItemMapper;
//
//    @Resource
//    private StCVirtualHighStockMapper stCVirtualHighStockMapper;
//
//    @Resource
//    private StCVirtualHighStockItemMapper stCVirtualHighStockItemMapper;
//
//    @Reference(version = "1.0", group = "sg")
//    private SgChannelProductCalcAndSyncCmd sgChannelProductCalcAndSyncCmd;
//
//    @Reference(group = "sg", version = "1.0")
//    private SgBChannelStorageBufferCmd sgBChannelStorageBufferCmd;
//
//    @Value("${st.strategy.scan.size:10000}")
//    private int pageSize;
//
//    @Autowired
//    private RpcCpService rpcCpService;
//
//    @Autowired
//    private ProductStrategyCloseService productStrategyCloseService;
//
//    @Autowired
//    private StCVirtualHighStockCloseService stCVirtualHighStockCloseService;
//
//    @Autowired
//    private LockSkuStrategyFinishService lockSkuStrategyFinishService;
//
//    /**
//     * <ul>
//     *     <li>
//     *         </p>策略开始时应该执行的逻辑
//     *     </li>
//     *     <li>
//     *         </p>定时任务扫描店铺特殊策略和店铺虚高库存策略，开始执行状态为未执行，状态等于已审核，且当前时间在策略开始时间和结束时间之间（包含开始时间）时
//     *         </p>将店铺+SKU或者店铺+平台条码ID查询平台店铺商品表插入库存计算缓存池重新计算。
//     *     </li>
//     * </ul>
//     */
//    @Transactional(rollbackFor = Exception.class)
//    public void strategyBegin() {
//
//        Map<Long, CpShop> shopMap = Maps.newHashMap();
//
//        User root = SystemUserResource.getRootUser();
//
//        //批次号
//        String batchno = (System.currentTimeMillis() + "" + (int) (Math.random() * 9000 + 1000));
//
//        // 循环查找符合条件渠道商品特殊设置的记录
//        List<StCProductStrategyDO> productStrategyList = stCProductStrategyMapper.selectActiveStrategy();
//        logger.info("开始标策略扫商品特殊设置条数为:{}", productStrategyList.size());
//        if (CollectionUtils.isNotEmpty(productStrategyList)) {
//
//            shopMap = rpcCpService.queryAllShopIsY().stream().collect(Collectors.toMap(CpShop::getId, Function.identity()));
//
//            List<Long> productStrategyIds = productStrategyList.parallelStream().map(StCProductStrategyDO::getId).collect(Collectors.toList());
//            productStrategyList.forEach(o -> {
//                if (StringUtils.isEmpty(o.getPlanName())) o.setPlanName("null");
//            });
//            Map<Long, String> idMapName = productStrategyList.stream().collect(Collectors.toMap(StCProductStrategyDO::getId, StCProductStrategyDO::getPlanName, (x, y) -> x));
//            // 1,标记开始标为执行
//            stCProductStrategyMapper.updateSyncStartMarkByIds(StrategySyncMarkEnum.BEGINNING.getCode(), productStrategyIds);
//            channelProductLegacy(productStrategyIds, idMapName, shopMap, root, batchno, StrategySyncMarkEnum.BEGINNING);
//        }
//
//        // 2.1 查询符合条件的虚高库存
//        List<StCShopVirtualHighStockDO> virtualHighStockList = stCVirtualHighStockMapper.selectActiveStrategy();
//        logger.info("开始标策略扫商品虚高库存条数为:{}", virtualHighStockList.size());
//        if (CollectionUtils.isNotEmpty(virtualHighStockList)) {
//
//            if (MapUtils.isEmpty(shopMap)) {
//                shopMap = rpcCpService.queryAllShopIsY().stream().collect(Collectors.toMap(CpShop::getId, Function.identity()));
//            }
//
//            // 2.2获取所有虚高库存的主键
//            List<Long> idList = virtualHighStockList.parallelStream().map(StCShopVirtualHighStockDO::getId).collect(Collectors.toList());
//            // 2.2.1 标记所有策略为已执行
//            stCVirtualHighStockMapper.updateSyncStartMarkByIds(StrategySyncMarkEnum.BEGINNING.getCode(), idList);
//            // 2.3 抽出map(id, 虚高库存策略)
//            Map<Long, StCShopVirtualHighStockDO> idMapValue = virtualHighStockList.parallelStream().collect(Collectors.toMap(StCShopVirtualHighStockDO::getId, Function.identity(), (x, y) -> x));
//            // 虚高库存设置策略处理
//            virtualHighStockLegacy(idList, idMapValue, shopMap, root, batchno, StrategySyncMarkEnum.BEGINNING);
//        }
//
//    }
//
//    /**
//     * <ul>
//     *     <li>
//     *         策略结束时执行的逻辑
//     *     </li>
//     *     <li>
//     *         定时任务扫描店铺特殊策略和店铺虚高库存策略、店铺锁库策略、店铺锁库条码策略，结束执行状态为未执行，状态等于已审核，且当前时间在结束时间之后（包含结束时间）时
//     *         将店铺+SKU或者店铺+平台条码ID查询平台店铺商品表插入库存计算缓存池重新计算。
//     *     </li>
//     * </ul>
//     */
//    @Transactional(rollbackFor = Exception.class)
//    public void strategyEnd() {
//
//        Map<Long, CpShop> shopMap = Maps.newHashMap();
//
//        User root = SystemUserResource.getRootUser();
//
//        //批次号
//        String batchno = (System.currentTimeMillis() + "" + (int) (Math.random() * 9000 + 1000));
//
//        // 1 店铺商品特殊设置
//        List<StCProductStrategyDO> productStrategyList = stCProductStrategyMapper.selectEndActiveStrategy();
//        logger.info("结束标策略扫商品特殊设置条数为:{}", productStrategyList.size());
//        if (CollectionUtils.isNotEmpty(productStrategyList)) {
//
//            shopMap = rpcCpService.queryAllShopIsY().stream().collect(Collectors.toMap(CpShop::getId, Function.identity()));
//            productStrategyList.forEach(o -> {
//                if (StringUtils.isEmpty(o.getPlanName())) o.setPlanName("null");
//            });
//            Map<Long, String> idMapName = productStrategyList.parallelStream().collect(Collectors.toMap(StCProductStrategyDO::getId, StCProductStrategyDO::getPlanName, (x, y) -> x));
//            List<Long> productStrategyIds = productStrategyList.parallelStream().map(StCProductStrategyDO::getId).collect(Collectors.toList());
//            stCProductStrategyMapper.updateSyncEndAndStartAndCompleteMarkByIds(StrategySyncMarkEnum.BEGINNING.getCode(), productStrategyIds);
//            channelProductLegacy(productStrategyIds, idMapName, shopMap, root, batchno, StrategySyncMarkEnum.END);
//            productStrategyCloseService.batchCloseProductStrategy(productStrategyIds, root);
//        }
//
//        // 2 虚高库存
//        List<StCShopVirtualHighStockDO> virtualHighStockList = stCVirtualHighStockMapper.selectEndActiveStrategy();
//        logger.info("结束标策略扫商品虚高库存条数为:{}", virtualHighStockList.size());
//        if (CollectionUtils.isNotEmpty(virtualHighStockList)) {
//
//            if (MapUtils.isEmpty(shopMap)) {
//                shopMap = rpcCpService.queryAllShopIsY().stream().collect(Collectors.toMap(CpShop::getId, Function.identity()));
//            }
//
//            List<Long> idList = virtualHighStockList.parallelStream().map(StCShopVirtualHighStockDO::getId).collect(Collectors.toList());
//            stCVirtualHighStockMapper.updateSyncEndCompleteStartMarkByIds(StrategySyncMarkEnum.BEGINNING.getCode(), idList);
//            Map<Long, StCShopVirtualHighStockDO> idMapValue = virtualHighStockList.parallelStream().collect(Collectors.toMap(StCShopVirtualHighStockDO::getId, Function.identity(), (x, y) -> x));
//            virtualHighStockLegacy(idList, idMapValue, shopMap, root, batchno, StrategySyncMarkEnum.END);
//            /**批量更新状态为结案 */
//            stCVirtualHighStockCloseService.batchCloseStCVirtualHighStock(idList, root);
//        }
//
//        // 3 店铺锁库条码设置
//        List<StCLockSkuStrategyDO> list = stCLockSkuStrategyMapper.selectEndActiveStrategy();
//        logger.info("结束标策略扫锁库条码设置条数为:{}", list.size());
//        if (CollectionUtils.isNotEmpty(list)) {
//
//            if (MapUtils.isEmpty(shopMap)) {
//                shopMap = rpcCpService.queryAllShopIsY().stream().collect(Collectors.toMap(CpShop::getId, Function.identity()));
//            }
//
//            List<Long> lockSkuIdList = list.parallelStream().map(StCLockSkuStrategyDO::getId).collect(Collectors.toList());
//            stCLockSkuStrategyMapper.updateSyncEndCompleteMarkByIds(StrategySyncMarkEnum.BEGINNING.getCode(), lockSkuIdList);
//            Map<Long, StCLockSkuStrategyDO> idMapLockSku = list.parallelStream().collect(Collectors.toMap(StCLockSkuStrategyDO::getId, Function.identity(), (x, y) -> x));
//            stockLockSkuLegacy(lockSkuIdList, idMapLockSku, shopMap, root, batchno, StrategySyncMarkEnum.END);
//            /**批量更新状态为结案 */
//            lockSkuStrategyFinishService.batchFinishSkuStrategy(lockSkuIdList, root);
//        }
//
//        // 4 店铺锁库策略
//        List<StCLockStockStrategyItemDO> lockStockStrategyItemList = stCLockStockStrategyItemMapper.selectEndActiveStrategyItem();
//        logger.info("结束标策略扫店铺锁库策略条数为:{}", lockStockStrategyItemList.size());
//        if (CollectionUtils.isNotEmpty(lockStockStrategyItemList)) {
//
//            List<Long> lockStockItemIdList = lockStockStrategyItemList.parallelStream().map(StCLockStockStrategyItemDO::getId).collect(Collectors.toList());
//            List<String> planNameList = lockStockStrategyItemList.parallelStream().map(StCLockStockStrategyItemDO::getPlanName).distinct().collect(Collectors.toList());
//            stCLockStockStrategyItemMapper.updateSyncEndCompleteMarkByIds(StrategySyncMarkEnum.BEGINNING.getCode(), lockStockItemIdList);
//            List<Long> shopIdList = lockStockStrategyItemList.parallelStream().map(StCLockStockStrategyItemDO::getCpCShopId).collect(Collectors.toList());
//            stockLockLegacy(shopIdList, planNameList, StrategySyncMarkEnum.END);
//        }
//    }
//
//    /**
//     * <ul>
//     *     <li>
//     *         策略完成是需要处理的逻辑
//     *     </li>
//     * </ul>
//     */
//    @Transactional(rollbackFor = Exception.class)
//    public void strategyComplete() {
//
//        Map<Long, CpShop> shopMap = Maps.newHashMap();
//
//        User root = SystemUserResource.getRootUser();
//
//        //批次号
//        String batchno = (System.currentTimeMillis() + "" + (int) (Math.random() * 9000 + 1000));
//
//        // 1 店铺商品特殊设置
//        List<StCProductStrategyDO> productStrategyList = stCProductStrategyMapper.selectCompleteActiveStrategy();
//        logger.info("完结标策略扫描特殊商品设置策略条数为:{}", productStrategyList.size());
//        if (CollectionUtils.isNotEmpty(productStrategyList)) {
//
//            shopMap = rpcCpService.queryAllShopIsY().stream().collect(Collectors.toMap(CpShop::getId, Function.identity()));
//            productStrategyList.forEach(o -> {
//                if (StringUtils.isEmpty(o.getPlanName())) o.setPlanName("null");
//            });
//            Map<Long, String> idMapName = productStrategyList.parallelStream().collect(Collectors.toMap(StCProductStrategyDO::getId, StCProductStrategyDO::getPlanName, (x, y) -> x));
//            List<Long> productStrategyIds = productStrategyList.parallelStream().map(StCProductStrategyDO::getId).collect(Collectors.toList());
//            stCProductStrategyMapper.updateSyncStartAndCompleteMarkByIds(StrategySyncMarkEnum.BEGINNING.getCode(), productStrategyIds);
//            channelProductLegacy(productStrategyIds, idMapName, shopMap, root, batchno, StrategySyncMarkEnum.COMPLETE);
//        }
//
//        // 2 虚高库存
//        List<StCShopVirtualHighStockDO> virtualHighStockList = stCVirtualHighStockMapper.selectCompleteActiveStrategy();
//        logger.info("完结标策略扫描虚高库存策略条数为:{}", virtualHighStockList.size());
//        if (CollectionUtils.isNotEmpty(virtualHighStockList)) {
//
//            if (MapUtils.isEmpty(shopMap)) {
//                shopMap = rpcCpService.queryAllShopIsY().stream().collect(Collectors.toMap(CpShop::getId, Function.identity()));
//            }
//
//            List<Long> idList = virtualHighStockList.parallelStream().map(StCShopVirtualHighStockDO::getId).collect(Collectors.toList());
//            stCVirtualHighStockMapper.updateSyncCompleteStartMarkByIds(StrategySyncMarkEnum.BEGINNING.getCode(), idList);
//            Map<Long, StCShopVirtualHighStockDO> idMapValue = virtualHighStockList.parallelStream().collect(Collectors.toMap(StCShopVirtualHighStockDO::getId, Function.identity(), (x, y) -> x));
//            virtualHighStockLegacy(idList, idMapValue, shopMap, root, batchno, StrategySyncMarkEnum.COMPLETE);
//        }
//
//        // 3 店铺锁库条码设置
//        List<StCLockSkuStrategyDO> list = stCLockSkuStrategyMapper.selectCompleteActiveStrategy();
//        logger.info("完结标策略扫描店铺锁库条码设置策略条数为:{}", list.size());
//        if (CollectionUtils.isNotEmpty(list)) {
//
//            if (MapUtils.isEmpty(shopMap)) {
//                shopMap = rpcCpService.queryAllShopIsY().stream().collect(Collectors.toMap(CpShop::getId, Function.identity()));
//            }
//
//            List<Long> lockSkuIdList = list.parallelStream().map(StCLockSkuStrategyDO::getId).collect(Collectors.toList());
//            stCLockSkuStrategyMapper.updateSyncStartCompleteMarkByIds(StrategySyncMarkEnum.BEGINNING.getCode(), lockSkuIdList);
//            Map<Long, StCLockSkuStrategyDO> idMapLockSku = list.parallelStream().collect(Collectors.toMap(StCLockSkuStrategyDO::getId, Function.identity(), (x, y) -> x));
//            stockLockSkuLegacy(lockSkuIdList, idMapLockSku, shopMap, root, batchno, StrategySyncMarkEnum.COMPLETE);
//        }
//
//        // 4 店铺锁库策略
//        List<StCLockStockStrategyItemDO> lockStockStrategyItemList = stCLockStockStrategyItemMapper.selectCompleteActiveStrategyItem();
//        logger.info("完结标策略扫描店铺锁库策略策略条数为:{}", lockStockStrategyItemList.size());
//        if (CollectionUtils.isNotEmpty(lockStockStrategyItemList)) {
//            List<Long> lockStockItemIdList = lockStockStrategyItemList.parallelStream().map(StCLockStockStrategyItemDO::getId).collect(Collectors.toList());
//            List<String> planNameList = lockStockStrategyItemList.parallelStream().map(StCLockStockStrategyItemDO::getPlanName).distinct().collect(Collectors.toList());
//            stCLockStockStrategyItemMapper.updateSyncCompleteMarkByIds(StrategySyncMarkEnum.BEGINNING.getCode(), lockStockItemIdList);
//            List<Long> shopIdList = lockStockStrategyItemList.parallelStream().map(StCLockStockStrategyItemDO::getCpCShopId).collect(Collectors.toList());
//            stockLockLegacy(shopIdList, planNameList, StrategySyncMarkEnum.COMPLETE);
//        }
//    }
//
//    /**
//     * 店铺锁库策略
//     *
//     * @param shopIdList   店铺列表
//     * @param planNameList
//     * @param syncMarkEnum
//     */
//    private void stockLockLegacy(List<Long> shopIdList, List<String> planNameList, StrategySyncMarkEnum syncMarkEnum) {
//        List<Integer> cpcShopIdList = shopIdList.parallelStream().map(Long::intValue).collect(Collectors.toList());
//        QuerySession querySession = new QuerySessionImpl();
//        DefaultWebEvent event = new DefaultWebEvent("stockLockLegacy", new JSONObject());
//        JSONObject jsonObject = new JSONObject();
//        JSONArray array = new JSONArray();
//        JSONArray planNameArray = new JSONArray();
//        if (CollectionUtils.isNotEmpty(planNameList)) {
//            planNameArray.addAll(planNameList);
//        }
//        array.addAll(cpcShopIdList);
//        jsonObject.put("cpCShopIdList", array);
//        jsonObject.put("operate", 1);
//        jsonObject.put("sourceno", String.format("店铺锁库策略[%s]任务触发库存同步,店铺ids:[%s],方案ID:[%s]", syncMarkEnum.getDesc(), array, planNameArray));
//        //库存同步 插入到库存策略计算缓存池
//        jsonObject.put("poolType", ChannelStoragePoolTypeEnum.SYNC_POOL_ALL.getValue());
//        event.put("param", jsonObject);
//        querySession.setEvent(event);
//        try {
//            ValueHolder execute = sgChannelProductCalcAndSyncCmd.execute(querySession);
//            logger.info("手动处理店铺锁库:{}", execute);
//        } catch (Exception e) {
//            logger.error("插入库存计算缓存池错误", e);
//        }
//    }
//
//    /**
//     * 店铺锁库条码策略
//     */
//    private void stockLockSkuLegacy(List<Long> lockSkuIdList, Map<Long, StCLockSkuStrategyDO> idMapLockSku, Map<Long, CpShop> shopMap, User root, String batchno, StrategySyncMarkEnum syncMarkEnum) {
//        int offset = 0;
//        while (true) {
//            // 1.1,分页查询锁库策略中的明细
//            List<StCLockSkuStrategyItemDO> lockSkuStrategyItemList = stCLockSkuStrategyItemMapper.selectByStrategyIds(lockSkuIdList, offset, pageSize);
//            offset = (offset + 1) * pageSize;
//
//            // 1.2,判断本次获取到的数据条数
//            int size = lockSkuStrategyItemList.size();
//
//            // 1.3,本次查询到结果为0跳出当前循环
//            if (size == 0) {
//                break;
//            }
//
//            // 1.4,组件缓存池对象
//            List<SgChannelStorageBufferSaveRequest> channelStorageBuffers = lockSkuStrategyItemList.parallelStream().map(x -> {
//                StCLockSkuStrategyDO lockSkuStrategy = idMapLockSku.get(x.getStCLockSkuStrategyId());
//
//                SgChannelStorageBufferSaveRequest storageBufferSaveRequest = new SgChannelStorageBufferSaveRequest();
//
//                Long shopId = x.getCpCShopId();
//                String shopTitle = x.getCpCShopTitle();
//                CpShop shop = shopMap.get(x.getCpCShopId());
//                if (shop != null) {
//                    shopTitle = shop.getCpCShopTitle();
//                    //设置平台单号
//                    storageBufferSaveRequest.setCpCPlatformId(shop.getCpCPlatformId().intValue());
//                }
//
//                storageBufferSaveRequest.setCpCShopId(lockSkuStrategy.getCpCShopId());
//                storageBufferSaveRequest.setWareType(0);
//                storageBufferSaveRequest.setSkuId(x.getPtSkuId());
//                storageBufferSaveRequest.setPtProId(x.getPtProId());
//                storageBufferSaveRequest.setCpCShopTitle(shopTitle);
//                storageBufferSaveRequest.setPsCSkuId(x.getPsCSkuId());
//                storageBufferSaveRequest.setPsCSkuEcode(x.getPsCSkuEcode());
//                storageBufferSaveRequest.setDealStatus(SgBChannelStorageBufferExtend.DealStatusEnum.UN_DEAL.getCode());
//                storageBufferSaveRequest.setPsCProId(x.getPsCProId());
//                storageBufferSaveRequest.setSourceNo(String.format("店铺锁库条码特殊设置策略[%s]任务触发库存同步,店铺:[%s],方案ID:[%S]", syncMarkEnum.getDesc(), lockSkuStrategy.getCpCShopTitle(), lockSkuStrategy.getEname()));
//
//                return storageBufferSaveRequest;
//            }).collect(Collectors.toList());
//
//            // 1.6 插入库存计算缓存池
//            try {
//
//                SgChannelStorageBufferBatchSaveRequest batchSaveRequest = new SgChannelStorageBufferBatchSaveRequest();
//                batchSaveRequest.setBufferSaveRequestList(channelStorageBuffers);
//                batchSaveRequest.setBatchno(batchno);
//                batchSaveRequest.setUser(root);
//
//                ValueHolderV14<Integer> integerValueHolderV14 = sgBChannelStorageBufferCmd.saveDataToChannelStorageBuffer(batchSaveRequest);
//                logger.info("插入缓存池接口调用:{}", integerValueHolderV14);
//
//            } catch (Exception e) {
//                logger.error("插入库存计算缓存池错误", e);
//            }
//
//            // 1.7 本次查询如果结果集条数小于分页条数,证明当前页为最后一页,则循环结束.
//            if (size < pageSize) {
//                break;
//            }
//        }
//    }
//
//    /**
//     * 处理关于渠道特殊商品设置
//     *
//     * @param productStrategyIds 主键Ids
//     * @param idMapName          主键id对应的策略主表
//     * @param shopMap
//     * @param root
//     * @param batchno
//     * @param syncMarkEnum
//     */
//    private void channelProductLegacy(List<Long> productStrategyIds, Map<Long, String> idMapName, Map<Long, CpShop> shopMap, User root, String batchno, StrategySyncMarkEnum syncMarkEnum) {
//        int offset = 0;
//        while (true) {
//            // 1.1,分页查询特殊商品设置的明细
//            List<StCProductStrategyItemDO> strategyItemList = stCProductStrategyItemMapper.selectBytStCProductStrategyIds(productStrategyIds, offset, pageSize);
//            offset = (offset + 1) * pageSize;
//
//            // 1.2,判断本次获取到的数据条数
//            int size = strategyItemList.size();
//
//            // 1.3,本次查询到结果为0跳出当前循环
//            if (size == 0) {
//                break;
//            }
//
//            // 1.4,组件缓存池对象
//            List<SgChannelStorageBufferSaveRequest> channelStorageBuffers = strategyItemList.parallelStream().map(x -> {
//                SgChannelStorageBufferSaveRequest storageBufferSaveRequest = new SgChannelStorageBufferSaveRequest();
//
//                Long shopId = x.getCpCShopId();
//                String shopTitle = x.getCpCShopTitle();
//                CpShop shop = shopMap.get(x.getCpCShopId());
//                if (shop != null) {
//                    shopTitle = shop.getCpCShopTitle();
//                    //设置平台单号
//                    storageBufferSaveRequest.setCpCPlatformId(shop.getCpCPlatformId().intValue());
//                }
//
//                storageBufferSaveRequest.setWareType(0);
//                storageBufferSaveRequest.setCpCShopId(shopId);
//                storageBufferSaveRequest.setCpCShopTitle(shopTitle);
//                storageBufferSaveRequest.setSkuId(x.getPtSkuId());
//                storageBufferSaveRequest.setPtProId(x.getPtProId());
//                storageBufferSaveRequest.setPsCProId(x.getPsCProId());
//                storageBufferSaveRequest.setPsCSkuId(x.getPsCSkuId());
//                storageBufferSaveRequest.setDealStatus(SgBChannelStorageBufferExtend.DealStatusEnum.UN_DEAL.getCode());
//                storageBufferSaveRequest.setPsCSkuEcode(x.getPsCSkuEcode());
//                storageBufferSaveRequest.setSourceNo(String.format("店铺商品特殊设置:[%s]任务[%s]触发库存同步", idMapName.get(x.getStCProductStrategyId()), syncMarkEnum.getDesc()));
//
//                return storageBufferSaveRequest;
//            }).collect(Collectors.toList());
//
//
//            // 1.6 插入库存计算缓存池
//            try {
//
//                SgChannelStorageBufferBatchSaveRequest batchSaveRequest = new SgChannelStorageBufferBatchSaveRequest();
//                batchSaveRequest.setBufferSaveRequestList(channelStorageBuffers);
//                batchSaveRequest.setBatchno(batchno);
//                batchSaveRequest.setUser(root);
//
//                ValueHolderV14<Integer> integerValueHolderV14 = sgBChannelStorageBufferCmd.saveDataToChannelStorageBuffer(batchSaveRequest);
//                logger.info("插入缓存池接口调用:{}", integerValueHolderV14);
//
//            } catch (Exception e) {
//                logger.error("插入库存计算缓存池错误", e);
//            }
//
//            // 1.7 本次查询如果结果集条数小于分页条数,证明当前页为最后一页,则循环结束.
//            if (size < pageSize) {
//                break;
//            }
//        }
//    }
//
//    /**
//     * <Ul>
//     * <li>
//     * 处理虚高库存的策略
//     * </li>
//     * </Ul>
//     */
//    private void virtualHighStockLegacy(List<Long> idList, Map<Long, StCShopVirtualHighStockDO> idMapValue, Map<Long, CpShop> shopMap, User root, String batchno, StrategySyncMarkEnum syncMarkEnum) {
//        int offset = 0;
//        while (true) {
//            // 2.4,分页查询虚高策略中的明细
//            List<StCShopVirtualHighStockItemDO> virtualHighStockItemDOList = stCVirtualHighStockItemMapper.selectByStrategyIds(idList, offset, pageSize);
//            offset = (offset + 1) * pageSize;
//
//            // 2.5,判断本次获取到的数据条数
//            int size = virtualHighStockItemDOList.size();
//
//            // 2.6,本次查询到结果为0跳出当前循环
//            if (size == 0) {
//                break;
//            }
//
//            // 2.7,组件缓存池对象
//            List<SgChannelStorageBufferSaveRequest> channelStorageBuffers = virtualHighStockItemDOList.parallelStream().map(x -> {
//                SgChannelStorageBufferSaveRequest storageBufferSaveRequest = new SgChannelStorageBufferSaveRequest();
//
//                StCShopVirtualHighStockDO shopVirtualHighStockDO = idMapValue.get(x.getStCShopVirtualHighStockId());
//                Long shopId = shopVirtualHighStockDO.getCpCShopId();
//                String shopTitle = shopVirtualHighStockDO.getCpCShopTitle();
//                CpShop shop = shopMap.get(x.getCpCShopId());
//                if (shop != null) {
//                    shopTitle = shop.getCpCShopTitle();
//                    //设置平台单号
//                    storageBufferSaveRequest.setCpCPlatformId(shop.getCpCPlatformId().intValue());
//                }
//
//                storageBufferSaveRequest.setWareType(0);
//                storageBufferSaveRequest.setCpCShopId(shopId);
//                storageBufferSaveRequest.setCpCShopTitle(shopTitle);
//                storageBufferSaveRequest.setSkuId(x.getSkuId());
//                storageBufferSaveRequest.setPtProId(x.getNumberId());
//                storageBufferSaveRequest.setPsCProId(x.getPsCProId());
//                storageBufferSaveRequest.setPsCSkuId(x.getPsCSkuId());
//                storageBufferSaveRequest.setPsCSkuEcode(x.getPsCSkuEcode());
//                storageBufferSaveRequest.setDealStatus(SgBChannelStorageBufferExtend.DealStatusEnum.UN_DEAL.getCode());
//                storageBufferSaveRequest.setSourceNo(String.format("店铺商品虚高库存设置:[%s]任务[%s]触发库存同步", shopVirtualHighStockDO.getPlan(), syncMarkEnum.getDesc()));
//
//                return storageBufferSaveRequest;
//            }).collect(Collectors.toList());
//
//
//            // 2.9 插入库存计算缓存池
//            try {
//
//                SgChannelStorageBufferBatchSaveRequest batchSaveRequest = new SgChannelStorageBufferBatchSaveRequest();
//                batchSaveRequest.setBufferSaveRequestList(channelStorageBuffers);
//                batchSaveRequest.setBatchno(batchno);
//                batchSaveRequest.setUser(root);
//
//                ValueHolderV14<Integer> integerValueHolderV14 = sgBChannelStorageBufferCmd.saveDataToChannelStorageBuffer(batchSaveRequest);
//                logger.info("插入缓存池接口调用:{}", integerValueHolderV14);
//
//            } catch (Exception e) {
//                logger.error("插入库存计算缓存池错误", e);
//            }
//
//            // 2.10 本次查询如果结果集条数小于分页条数,证明当前页为最后一页,则循环结束.
//            if (size < pageSize) {
//                logger.debug("循环结束break");
//                break;
//            }
//        }
//    }
//
//}
