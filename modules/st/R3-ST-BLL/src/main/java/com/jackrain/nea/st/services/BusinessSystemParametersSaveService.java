package com.jackrain.nea.st.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.google.common.base.Throwables;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.psext.model.table.PsCSku;
import com.jackrain.nea.redis.util.RedisOpsUtil;
import com.jackrain.nea.st.common.StConstant;
import com.jackrain.nea.st.mapper.BusinessSystemParametersLogMapper;
import com.jackrain.nea.st.mapper.BusinessSystemParametersSaveMapper;
import com.jackrain.nea.st.model.table.BusinessSystemParametersLogDO;
import com.jackrain.nea.st.model.table.CpCBusinessSystemParametersDO;
import com.jackrain.nea.st.rpc.RpcPsService;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * @description: 业务系统参数
 * @author: wangkuan
 * @date: 2020-11-04
 */
@Component
@Slf4j
public class BusinessSystemParametersSaveService extends CommandAdapter {

    @Autowired
    BusinessSystemParametersLogMapper businessSystemParametersLogMapper;

    @Autowired
    private BusinessSystemParametersSaveMapper businessSystemParametersSaveMapper;

    @Autowired
    private RedisOpsUtil<String, String> redisUtil;

    @Autowired
    private RpcPsService rpcPsService;

    @Override
    public ValueHolder execute(QuerySession querySession) throws NDSException {

        log.debug(LogUtil.format("start修改业务系统参数"));
        DefaultWebEvent event = querySession.getEvent();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(event.getParameterValue("param"),
                "yyyy-MM-dd HH:mm:ss", SerializerFeature.WriteMapNullValue), Feature.OrderedField);
        if (param == null) {
            throw new NDSException("参数为空！");
        }
        if (log.isDebugEnabled()) {

            log.debug(LogUtil.format("Start BusinessSystemParametersSaveService.execute.ReceiveParams: {}"),
                    JSONObject.toJSONString(param, SerializerFeature.WriteMapNullValue));
        }
        ValueHolder vh = new ValueHolder();
        User user = querySession.getUser();
        try {

            //id
            Long id = param.getLong("objid");
            //获取实体
            JSONObject object = param.getJSONObject("fixcolumn");
            String key = object.keySet().iterator().next();
            object = object.getJSONObject(key);
            if (object.size() == 0) {
                vh.put("code", ResultCode.SUCCESS);
                vh.put("message", "保存成功");
                return vh;
            }
            String beforevalue = param.getJSONObject("beforevalue")
                    .getJSONObject("CP_C_BUSINESS_SYSTEM_PARAMETERS").getString("PARAM_VALUE");
            String aftervalue = param.getJSONObject("aftervalue")
                    .getJSONObject("CP_C_BUSINESS_SYSTEM_PARAMETERS").getString("PARAM_VALUE");
            CpCBusinessSystemParametersDO businessSystemParameters = businessSystemParametersSaveMapper.selectById(id);
            String code = businessSystemParameters.getParamCode();
            String type = businessSystemParameters.getParamType();

            if (checkType(vh, aftervalue, type, code)) {
                return vh;
            }

            //修改
            insertBusinessSystemParameters(businessSystemParameters,aftervalue, user);

            //插入结算日志
            insertBusinessSystemParametersLog(id, beforevalue, aftervalue, user);

            //更新redis
            redisUtil.strRedisTemplate.opsForValue().set("business_system:" + businessSystemParameters.getParamCode(), aftervalue);
            log.debug(LogUtil.format("更新redis: {}"), aftervalue);
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("objid", id);
            jsonObject.put("tablename", "CP_C_BUSINESS_SYSTEM_PARAMETERS");
            vh.put("data", jsonObject);
            vh.put("code", ResultCode.SUCCESS);
            vh.put("message", "保存成功");
        } catch (Exception e) {
            log.error("error：{}", Throwables.getStackTraceAsString(e));
            vh.put("code", ResultCode.FAIL);
            vh.put("message", e.getMessage());
        }
        return vh;
    }

    private void insertBusinessSystemParameters(CpCBusinessSystemParametersDO businessSystemParameters,String aftervalue, User user) {
        businessSystemParameters.setAdClientId(37L);
        businessSystemParameters.setAdOrgId(27L);
        businessSystemParameters.setModifieddate(new Date());
        businessSystemParameters.setModifierid(Long.valueOf(user.getId()));
        businessSystemParameters.setModifiername(user.getEname());
        businessSystemParameters.setParamValue(aftervalue);
        log.debug(LogUtil.format("businessSystemParameters") + businessSystemParameters.toString());
        businessSystemParametersSaveMapper.updateById(businessSystemParameters);
    }

    private void insertBusinessSystemParametersLog(Long id, String beforevalue, String aftervalue, User user) {
        BusinessSystemParametersLogDO businessSystemParametersLogDO = new BusinessSystemParametersLogDO();
        businessSystemParametersLogDO.setBeforeModification(beforevalue);
        businessSystemParametersLogDO.setAfterModification(aftervalue);
        businessSystemParametersLogDO.setModcontent("PARAM_VALUE");
        businessSystemParametersLogDO.setModifieddate(new Date());
        businessSystemParametersLogDO.setModifierid(Long.valueOf(user.getId()));
        businessSystemParametersLogDO.setModifiername(user.getEname());
        businessSystemParametersLogDO.setOutId(id);
        businessSystemParametersLogDO.setIsactive("Y");
        businessSystemParametersLogDO.setAdClientId(37L);
        businessSystemParametersLogDO.setAdOrgId(27L);
        businessSystemParametersLogMapper.insert(businessSystemParametersLogDO);
    }

    private boolean checkType(ValueHolder vh, String aftervalue, String type, String code) {
        if ("boolean".equals(type)) {
            if (!"是".equals(aftervalue) && !"否".equals(aftervalue)) {
                vh.put("code", ResultCode.FAIL);
                vh.put("message", "请输入是或者否");
                return true;
            }
        } else if ("date".equals(type)) {
            SimpleDateFormat format = new SimpleDateFormat("yyyy/MM/dd HH:mm:ss");
            try {
                format.setLenient(false);
                format.parse(aftervalue);
            } catch (ParseException e) {
                vh.put("code", ResultCode.FAIL);
                vh.put("message", "请输入指定日期格式（yyyy/MM/dd HH:mm:ss）");
                return true;
            }

        } else if ("number".equals(type)) {
            if (!aftervalue.matches("^[-\\+]?[\\d]*$")) {
                vh.put("code", ResultCode.FAIL);
                vh.put("message", "请输入数字");
                return true;
            }
        } else if (StConstant.IP_B_VIP_RETURN_ORDER.equals(code)) {
            String[] vals = aftervalue.split("\\|");
            if (StringUtils.isBlank(vals[0]) || vals.length < StConstant.IP_B_VIP_RETURN_ORDER_VALUE) {
                vh.put("code", ResultCode.FAIL);
                vh.put("message", "参数值必须要用竖线间隔，且竖线前后必须有值！");
                return true;
            }
            List<String> skuCds = new ArrayList<>();
            skuCds.add(vals[1]);
            List<PsCSku> psCSkus = rpcPsService.querySkuByEcodes(skuCds);
            if (psCSkus == null) {
                vh.put("code", ResultCode.FAIL);
                vh.put("message", "参数值必须要用竖线间隔，且竖线后必须设置有效的商品条码！");
                return true;
            }
        }
        return false;
    }

}

