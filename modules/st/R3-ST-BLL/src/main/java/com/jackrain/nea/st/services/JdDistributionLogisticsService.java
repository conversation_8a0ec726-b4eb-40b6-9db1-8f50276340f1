package com.jackrain.nea.st.services;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.cpext.model.table.CpLogistics;
import com.jackrain.nea.cpext.model.table.CpShop;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.ip.api.jingdong.JdDistributionLogisticsGetCmd;
import com.jackrain.nea.ip.model.result.JdDistributionLogisticsGetResp;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.st.mapper.JdDistributionLogisticsMapper;
import com.jackrain.nea.st.mapper.JdDistributionMapper;
import com.jackrain.nea.st.model.table.JdDistribution;
import com.jackrain.nea.st.model.table.JdDistributionLogistics;
import com.jackrain.nea.st.rpc.RpcCpService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @ClassName JdDistributionLogisticsService
 * @Description 京东分销商物流公司
 * <AUTHOR>
 * @Date 2022/12/10 14:51
 * @Version 1.0
 */
@Component
@Slf4j
public class JdDistributionLogisticsService {

    @Autowired
    private JdDistributionMapper jdDistributionMapper;
    @Reference(group = "ip", version = "1.4.0")
    private JdDistributionLogisticsGetCmd logisticsGetCmd;
    @Autowired
    private JdDistributionLogisticsMapper logisticsMapper;
    @Autowired
    private RpcCpService rpcCpService;
    @Autowired
    private ApplicationContext applicationContext;

    public ValueHolder refreshLogistics(QuerySession session) {
        ValueHolder valueHolder = new ValueHolder();
        valueHolder.put("code", ResultCode.SUCCESS);
        valueHolder.put("message", "success");
        User user = session.getUser();
        DefaultWebEvent event = session.getEvent();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(
                        event.getParameterValue("param"), "yyyy-MM-dd HH:mm:ss", SerializerFeature.WriteMapNullValue),
                Feature.OrderedField);
        if (log.isDebugEnabled()) {
            log.debug("start refresh jd distribution Receive Params:{}", param);
        }
        JSONArray objids = param.getJSONArray("ids");
        List<Long> ids = new ArrayList<>();
        List<JdDistribution> jdDistributionList;
        if (CollectionUtils.isNotEmpty(objids)) {
            for (Object o : objids) {
                ids.add(Long.valueOf(String.valueOf(o)));
            }
            jdDistributionList = jdDistributionMapper.queryByIds(ids);
        } else {
            jdDistributionList = jdDistributionMapper.queryAllJdDistribution();
        }
        if (CollectionUtils.isEmpty(jdDistributionList)) {
            return valueHolder;
        }
        StringBuilder sb = new StringBuilder();
        int success = 0;
        int error = 0;
        for (JdDistribution jdDistribution : jdDistributionList) {
            try {
                String distributionId = jdDistribution.getCpCDistributionId();
                Long shopId = jdDistribution.getCpCShopId();
                CpShop cpShop = rpcCpService.selectCpCShopById(shopId);
                if (ObjectUtil.isNull(cpShop)) {
                    sb.append(jdDistribution.getCpCShopId() + ":店铺已不存在.");
                    error += 1;
                    continue;
                }
                ValueHolderV14 valueHolderV14 = logisticsGetCmd.getLogistics(distributionId, cpShop.getCpCPlatformId().toString(), cpShop.getSellerNick());
                if (!valueHolderV14.isOK()) {
                    sb.append(cpShop.getSellerNick() + "获取物流信息异常");
                    error += 1;
                    continue;
                }
                List<JdDistributionLogisticsGetResp> logisticsGetRespList = (List<JdDistributionLogisticsGetResp>) valueHolderV14.getData();
                // 构建京东分销商物流数据
                if (CollectionUtils.isEmpty(logisticsGetRespList)) {
                    // 将京东分销商物流公司数据清除掉
                    logisticsMapper.deleteByDistributionId(jdDistribution.getId());
                } else {
                    List<JdDistributionLogistics> logisticsList = new ArrayList<>();
                    for (JdDistributionLogisticsGetResp resp : logisticsGetRespList) {
                        // 根据平台物流公司编码获取物流公司
                        List<CpLogistics> cpLogisticsList = rpcCpService.queryByLogisticsEcode(cpShop.getCpCPlatformId(), resp.getCompany_id());
                        if (CollectionUtils.isEmpty(cpLogisticsList)) {
                            sb.append(" 平台物流档案表无数据:" + resp.getCompany_id() + "," + resp.getCompany_name() + " ");
                            error += 1;
                            continue;
                        }
                        for (CpLogistics cpLogistics : cpLogisticsList) {
                            JdDistributionLogistics jdDistributionLogistics = new JdDistributionLogistics();
                            Long id = ModelUtil.getSequence("JD_DISTRIBUTION_LOGISTICS");
                            jdDistributionLogistics.setId(id);
                            jdDistributionLogistics.setAdClientId((long) user.getClientId());
                            jdDistributionLogistics.setAdOrgId((long) user.getOrgId());
                            jdDistributionLogistics.setCreationdate(new Date());
                            jdDistributionLogistics.setModifieddate(new Date());
                            jdDistributionLogistics.setOwnerid(Long.valueOf(user.getId()));
                            jdDistributionLogistics.setOwnername(user.getName());
                            jdDistributionLogistics.setModifierid(Long.valueOf(user.getId()));
                            jdDistributionLogistics.setModifiername(user.getEname());
                            jdDistributionLogistics.setIsactive("Y");
                            jdDistributionLogistics.setCpCDistributionId(distributionId);
                            jdDistributionLogistics.setPlatformLogisticsCode(resp.getCompany_id());
                            jdDistributionLogistics.setPlatformLogisticsName(resp.getCompany_name());
                            jdDistributionLogistics.setLogisticsCode(cpLogistics.getEcode());
                            jdDistributionLogistics.setLogisticsName(cpLogistics.getEname());
                            jdDistributionLogistics.setLogisticsId(cpLogistics.getId());
                            jdDistributionLogistics.setJdDistributionId(jdDistribution.getId());
                            logisticsList.add(jdDistributionLogistics);
                            success += 1;
                        }
                    }
                    applicationContext.getBean(JdDistributionLogisticsService.class).initLogistics(jdDistribution.getId(), logisticsList);
                }
            } catch (Exception e) {
                valueHolder.put("code", ResultCode.FAIL);
                valueHolder.put("message", e.getMessage());
            }
        }
        if (StringUtils.isNotEmpty(sb.toString())) {
            valueHolder.put("code", ResultCode.SUCCESS);
            valueHolder.put("message", "成功" + success + "条数据, 失败" + error + "条数据,失败原因" + sb.toString());
        }
        return valueHolder;
    }

    public ValueHolderV14<List<Long>> getLogisticsIdsByDistributionId(Long shopId, String jdDistributionId) {
        ValueHolderV14 valueHolderV14 = new ValueHolderV14<>(ResultCode.SUCCESS, "success");
        if (StringUtils.isEmpty(jdDistributionId)) {
            throw new NDSException("参数不能为空");
        }
        JdDistribution jdDistribution = jdDistributionMapper.getByDistributionId(shopId, jdDistributionId);
        if (ObjectUtil.isNull(jdDistribution)) {
            throw new NDSException("分销商记录已不存在");
        }
        List<JdDistributionLogistics> jdDistributionLogisticsList = logisticsMapper.getByDistributionId(jdDistribution.getId());
        if (CollectionUtils.isEmpty(jdDistributionLogisticsList)) {
            return valueHolderV14;
        }
        List<Long> logisticsIds = jdDistributionLogisticsList.stream().map(JdDistributionLogistics::getLogisticsId).collect(Collectors.toList());
        valueHolderV14.setData(logisticsIds);
        return valueHolderV14;
    }

    @Transactional(rollbackFor = Exception.class)
    public void initLogistics(Long distributionId, List<JdDistributionLogistics> logisticsList) {
        logisticsMapper.deleteByDistributionId(distributionId);
        logisticsMapper.batchInsert(logisticsList);
    }
}
