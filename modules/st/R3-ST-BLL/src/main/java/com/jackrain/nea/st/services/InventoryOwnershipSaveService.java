package com.jackrain.nea.st.services;

import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.util.StringUtil;
import com.jackrain.nea.cpext.model.table.CpShop;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.st.common.StConstant;
import com.jackrain.nea.st.mapper.StCInventoryOwnershipItemMapper;
import com.jackrain.nea.st.mapper.StCInventoryOwnershipMapper;
import com.jackrain.nea.st.mapper.StCProMaterieltypeMapper;
import com.jackrain.nea.st.model.table.StCInventoryOwnershipDO;
import com.jackrain.nea.st.model.table.StCInventoryOwnershipItemDO;
import com.jackrain.nea.st.model.table.StCProMaterieltypeDO;
import com.jackrain.nea.st.rpc.RpcCpService;
import com.jackrain.nea.st.utils.JsonUtils;
import com.jackrain.nea.st.utils.StBeanUtils;
import com.jackrain.nea.st.utils.ValueHolderUtils;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.stream.Collectors;

import static com.jackrain.nea.oc.oms.model.relation.OcBOrderExtend.CP_C_SHOP_ID;

/**
 * <AUTHOR>
 * @Date 2019/8/6 16:00
 */
@Component
@Slf4j
@Transactional
public class InventoryOwnershipSaveService extends CommandAdapter {
    @Autowired
    private StCInventoryOwnershipMapper mapper;
    @Autowired
    private StCInventoryOwnershipItemMapper itemMapper;
    @Autowired
    private StCProMaterieltypeMapper materieltypeMapper;
    @Autowired
    private RpcCpService rpcCpService;

    @Override
    public ValueHolder execute(QuerySession session) {
        DefaultWebEvent event = session.getEvent();
        JSONObject param = JsonUtils.filterNull((JSONObject) event.getParameterValue("param"), null);
        if (param != null) {
            Long id = param.getLong("objid");
            JSONObject fixColumn = param.getJSONObject("fixcolumn");
            JSONObject inventoryOwnershipMap = null;
            inventoryOwnershipMap = param.getJSONObject(StConstant.TAB_ST_C_INVENTORY_OWNERSHIP);
            if(fixColumn != null) {
                inventoryOwnershipMap = fixColumn.getJSONObject(StConstant.TAB_ST_C_INVENTORY_OWNERSHIP);
            }
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("inventoryOwnershipMap") + inventoryOwnershipMap);
            }
            StCInventoryOwnershipDO inventoryOwnership = JsonUtils.jsonParseClass(inventoryOwnershipMap, StCInventoryOwnershipDO.class);
            if (inventoryOwnership != null && id != null) {
                inventoryOwnership.setId(id);
                return saveInventoryOwnership(session, inventoryOwnership,inventoryOwnershipMap);
            } else {
                return ValueHolderUtils.getSuccessValueHolder(id, StConstant.TAB_ST_C_INVENTORY_OWNERSHIP, "");
            }
        }
        throw new NDSException("当前记录已不存在！");
    }

    /**
     * 保存
     *
     * @param session
     * @param inventoryOwnership
     * @return com.jackrain.nea.util.ValueHolder
     */
    private ValueHolder saveInventoryOwnership(QuerySession session, StCInventoryOwnershipDO inventoryOwnership,JSONObject inventoryOwnershipMap) {
        //主表
        long id = inventoryOwnership.getId();
        //1.1 判断【品牌】+【物料类型】是否已存在
        ValueHolder check = checkByFilter(inventoryOwnership);
        if (check != null) {
            return check;
        }
        //CP_C_SHOP_ID传递为多个,分割
        String str = inventoryOwnershipMap.get(CP_C_SHOP_ID).toString();
        List<Long> shopIds = Arrays.stream(str.split(","))
                .map(s -> Long.parseLong(s.trim()))
                .collect(Collectors.toList());
        //补足店铺冗余字段
        if (!CollectionUtils.isEmpty(shopIds)) {
            if (inventoryOwnership.getId() > 0) {
                StBeanUtils.makeModifierField(inventoryOwnership, session.getUser());
                if (mapper.updateById(inventoryOwnership) < 0) {
                    return ValueHolderUtils.getFailValueHolder("更新失败！");
                }
            } else {
                //插入
                id = ModelUtil.getSequence(StConstant.TAB_ST_C_INVENTORY_OWNERSHIP);
                inventoryOwnership.setId(id);
                StBeanUtils.makeCreateField(inventoryOwnership, session.getUser());
                int insertResult = mapper.insert(inventoryOwnership);
                if (insertResult < 0) {
                    return ValueHolderUtils.getFailValueHolder("保存失败！");
                }
            }
            //物料类型明细
            if (inventoryOwnership.getMaterialType() != null) {
                this.insertMaterialType(session, id, inventoryOwnership.getMaterialType(), shopIds);
            }
        }
        return ValueHolderUtils.getSuccessValueHolder(id, StConstant.TAB_ST_C_INVENTORY_OWNERSHIP, "");
    }

    /**
     * 物料类型 插入
     * @param id
     * @param session
     * @param materialTypeId
     * @return com.jackrain.nea.util.ValueHolder
     */
    private void insertMaterialType(QuerySession session, Long id, String materialTypeId,List<Long> shopIds) {
        //先删除
        HashMap<String, Object> map = new HashMap<>();
        map.put("st_c_inventory_ownership_id", id);
        itemMapper.deleteByMap(map);
        //新增
        for (Long shopId : shopIds) {
            StCInventoryOwnershipItemDO item = new StCInventoryOwnershipItemDO();
            item.setId(ModelUtil.getSequence(StConstant.TAB_ST_C_INVENTORY_OWNERSHIP_ITEM));//主键
            item.setStCInventoryOwnershipId(id);//外键
            //根据
            item.setMaterialType(Long.valueOf(materialTypeId));
            StCProMaterieltypeDO proMaterieltype = materieltypeMapper.selectById(item.getMaterialType());
            if (proMaterieltype != null) {
                item.setMaterialTypeCode(proMaterieltype.getEcode());
                item.setMaterialTypeName(proMaterieltype.getEname());
            }
            StBeanUtils.makeCreateField(item, session.getUser());
            CpShop cpShop = rpcCpService.selectCpCShopById(shopId);
            if (cpShop != null) {
                item.setCpCShopId(shopId);
                item.setCpCShopEcode(cpShop.getEcode());
                item.setCpCShopTitle(cpShop.getCpCShopTitle());
            }
            int insert = itemMapper.insert(item);
            if (insert < 0) {
                throw new NDSException("物料类型明细插入失败！");
            }
        }
    }

    /**
     * @param inventoryOwnership
     * @return com.jackrain.nea.util.ValueHolder
     */
    private ValueHolder checkByFilter(StCInventoryOwnershipDO inventoryOwnership) {
        if (inventoryOwnership.getId() > 0 && inventoryOwnership.getMaterialType() != null) {
            StCInventoryOwnershipDO inventoryOwnershipOld = mapper.selectById(inventoryOwnership.getId());
            if (inventoryOwnershipOld == null) {
                return ValueHolderUtils.getFailValueHolder("当前记录已不存在！");
            } else {
                if (StConstant.ISACTIVE_N.equals(inventoryOwnershipOld.getIsactive())) {
                    return ValueHolderUtils.getFailValueHolder("当前记录已作废，不允许编辑！！");
                }
            }
            //重复性验证
            if (inventoryOwnership.getBrand() != null && StringUtil.isNotEmpty(inventoryOwnership.getMaterialType())) {
                List<Long> ids = mapper.selectListByMaterialTypes(inventoryOwnership.getBrand(), inventoryOwnership.getMaterialType(), null);
                for (Long id : ids) {
                    if (!id.equals(inventoryOwnership.getId())) {
                        return ValueHolderUtils.getFailValueHolder("当前方案已存在，不可保存！");
                    }
                }
            }
        }
        return null;
    }
}
