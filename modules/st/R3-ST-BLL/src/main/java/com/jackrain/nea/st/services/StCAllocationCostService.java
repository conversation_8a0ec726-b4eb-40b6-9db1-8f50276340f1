package com.jackrain.nea.st.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.jackrain.nea.cpext.utils.ValueHolderUtils;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.oc.oms.model.enums.YesNoEnum;
import com.jackrain.nea.st.mapper.StCAllocationCostItemMapper;
import com.jackrain.nea.st.mapper.StCAllocationCostMapper;
import com.jackrain.nea.st.model.enums.CommStatusEnum;
import com.jackrain.nea.st.model.request.StCAllocationCostQueryRequest;
import com.jackrain.nea.st.model.result.StCAllocationCostQueryResult;
import com.jackrain.nea.st.model.table.StCAllocationCost;
import com.jackrain.nea.st.model.table.StCAllocationCostItem;
import com.jackrain.nea.st.utils.StBeanUtils;
import com.jackrain.nea.util.DateUtil;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.text.ParseException;
import java.util.*;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 * @Date 2022/6/10 10:35
 */
@Service
@Slf4j
public class StCAllocationCostService {
    @Autowired
    private StCAllocationCostMapper stCAllocationCostMapper;
    @Autowired
    private StCAllocationCostItemMapper stCAllocationCostItemMapper;


    @Transactional(rollbackFor = Exception.class)
    public Long save(JSONObject param, User user) {

        JSONObject fixColumn = param.getJSONObject("fixcolumn");
        Long objid = param.getLong("objid");
        String tableName = param.getString("table");
        JSONObject jsonObject = fixColumn.getJSONObject(tableName);

        if (Objects.nonNull(jsonObject)) {
            StCAllocationCost stCAllocationCost
                    = JSONObject.parseObject(jsonObject.toJSONString(), StCAllocationCost.class);
            //新增
            if (Objects.isNull(objid) || objid < 0) {
                checkInsertParam(stCAllocationCost);
                Long id = ModelUtil.getSequence(tableName.toLowerCase());
                stCAllocationCost.setId(id);
                StBeanUtils.makeCreateField(stCAllocationCost, user);
                stCAllocationCostMapper.insert(stCAllocationCost);
                objid = id;
            } else {
                //更新
                JSONObject afterValue = param.getJSONObject("aftervalue");
                JSONObject jsonObjectAfter = afterValue.getJSONObject(tableName);
                stCAllocationCost
                        = JSONObject.parseObject(jsonObjectAfter.toJSONString(), StCAllocationCost.class);
                checkUpdateParam(objid,stCAllocationCost);
                stCAllocationCost.setId(objid);
                StBeanUtils.makeModifierField(stCAllocationCost, user);
                if (CommStatusEnum.YES.desc().equals(stCAllocationCost.getIsactive())) {
                    stCAllocationCost.setIsactive(CommStatusEnum.YES.charVal());
                }
                if (CommStatusEnum.NO.desc().equals(stCAllocationCost.getIsactive())) {
                    stCAllocationCost.setIsactive(CommStatusEnum.NO.charVal());
                }
                stCAllocationCostMapper.updateById(stCAllocationCost);
            }
        }

        //子表更新
        String subTableName = "ST_C_ALLOCATION_COST_ITEM";
        JSONArray jsonObjectItems = fixColumn.getJSONArray(subTableName);
        if (CollectionUtils.isNotEmpty(jsonObjectItems)) {
            List<StCAllocationCostItem> insertList = new ArrayList<>();
            List<StCAllocationCostItem> updateList = new ArrayList<>();
            List<StCAllocationCostItem> stCAllocationCostItems = JSONArray.parseArray(jsonObjectItems.toJSONString(), StCAllocationCostItem.class);
            Long finalObjid = objid;
            stCAllocationCostItems.forEach(p -> {
                if (p.getId() == null || p.getId() < 0) {
                    checkInsertItemParam(finalObjid,p);
                    p.setStCAllocationCostId(finalObjid);
                    Long id = ModelUtil.getSequence(subTableName.toLowerCase());
                    p.setId(id);
                    //设置默认值
                    if(Objects.isNull(p.getArrivalDays())){
                        p.setArrivalDays(0);
                    }
                    if(Objects.isNull(p.getDeliveryFee())){
                        p.setDeliveryFee(BigDecimal.ZERO);
                    }
                    if(Objects.isNull(p.getFreight())){
                        p.setFreight(BigDecimal.ZERO);
                    }
                    if(Objects.isNull(p.getPremium())){
                        p.setPremium(BigDecimal.ZERO);
                    }
                    if(Objects.isNull(p.getUnloadingFee())){
                        p.setUnloadingFee(BigDecimal.ZERO);
                    }
                    if(Objects.isNull(p.getOtherFee())){
                        p.setOtherFee(BigDecimal.ZERO);
                    }
                    StBeanUtils.makeCreateField(p, user);
                    insertList.add(p);
                } else {
                    checkUpdateItemParam(finalObjid,p);
                    StBeanUtils.makeModifierField(p, user);
                    updateList.add(p);
                }

            });
            if (CollectionUtils.isNotEmpty(insertList)) {
                stCAllocationCostItemMapper.batchInsert(insertList);
            }
            if (CollectionUtils.isNotEmpty(updateList)) {
                for (StCAllocationCostItem item : updateList) {
                    stCAllocationCostItemMapper.updateById(item);
                }
            }
            //更新主表修改时间
            if(CollectionUtils.isNotEmpty(insertList) || CollectionUtils.isNotEmpty(updateList)){
                StCAllocationCost po = new StCAllocationCost();
                po.setId(finalObjid);
                StBeanUtils.makeModifierField(po, user);
                stCAllocationCostMapper.updateById(po);
            }
        }
        return objid;
    }

    /**
     * 校验有效性
     * @param stCAllocationCost
     */
    private void checkUpdateParam(Long objid,StCAllocationCost stCAllocationCost) {
        if(Objects.nonNull(stCAllocationCost.getStartDate()) || Objects.nonNull(stCAllocationCost.getEndDate())){
            StCAllocationCost existPo = stCAllocationCostMapper.selectById(objid);
            Date startDate = Objects.isNull(stCAllocationCost.getStartDate())?existPo.getStartDate():stCAllocationCost.getStartDate();
            Date endDate = Objects.isNull(stCAllocationCost.getEndDate())?existPo.getEndDate():stCAllocationCost.getEndDate();
            if(startDate.getTime()>endDate.getTime()){
                throw new NDSException("开始时间晚于结束时间，不允许！");
            }
            String s = DateUtil.format(new Date(), "yyyy-MM-dd");
            Date date = null;
            try {
                date = DateUtils.parseDate(s, "yyyy-MM-dd");
            } catch (ParseException e) {
                e.printStackTrace();
            }
            if(endDate.getTime()<date.getTime()){
                throw new NDSException("结束时间不能早于系统当前时间，不允许！");
            }
        }
    }

    /**
     * 检查子表有效性
     * @param p
     */
    private void checkUpdateItemParam(Long objid,StCAllocationCostItem p) {
        StCAllocationCostItem stCAllocationCostItem = stCAllocationCostItemMapper.selectById(p.getId());

        if(Objects.nonNull(p.getStartWeight()) && p.getStartWeight().compareTo(BigDecimal.ZERO)<0){
            throw new NDSException("起始重量必须为正数且最多两位小数！");
        }
        if(Objects.nonNull(p.getEndWeight()) && p.getEndWeight().compareTo(BigDecimal.ZERO)<0){
            throw new NDSException("结束重量必须为正数且最多两位小数！");
        }
        if(Objects.nonNull(p.getStartWeight()) || Objects.nonNull(p.getEndWeight())){
            BigDecimal startWeight = Objects.isNull(p.getStartWeight())?stCAllocationCostItem.getStartWeight():p.getStartWeight();
            BigDecimal endWeight = Objects.isNull(p.getEndWeight())?stCAllocationCostItem.getEndWeight():p.getEndWeight();
            if(startWeight.compareTo(endWeight)>=0){
                throw new NDSException("结束重量必须大于起始重量！");
            }
        }
        if(Objects.nonNull(p.getTrunkFreight()) && p.getTrunkFreight().compareTo(BigDecimal.ZERO)<0){
            throw new NDSException("干线运费必须为正数且最多两位小数！");
        }
        if(Objects.nonNull(p.getDeliveryFee()) && p.getDeliveryFee().compareTo(BigDecimal.ZERO)<0){
            throw new NDSException("提货费必须为正数且最多两位小数！");
        }
        if(Objects.nonNull(p.getFreight()) && p.getFreight().compareTo(BigDecimal.ZERO)<0){
            throw new NDSException("送货费必须为正数且最多两位小数！");
        }
        if(Objects.nonNull(p.getPremium()) && p.getPremium().compareTo(BigDecimal.ZERO)<0){
            throw new NDSException("保费必须为正数且最多两位小数！");
        }
        if(Objects.nonNull(p.getUnloadingFee()) && p.getUnloadingFee().compareTo(BigDecimal.ZERO)<0){
            throw new NDSException("卸货费必须为正数且最多两位小数！");
        }
        if(Objects.nonNull(p.getOtherFee()) && p.getOtherFee().compareTo(BigDecimal.ZERO)<0){
            throw new NDSException("其他费用必须为正数且最多两位小数！");
        }

        //检查重量重复校验
        //如果修改了仓库、起始重量、结束重量 需要校验重复性 修改是否可用也需要校验重复性
        if(Objects.nonNull(p.getCpCPhyWarehouseId()) || Objects.nonNull(p.getStartWeight()) || Objects.nonNull(p.getEndWeight()) || Objects.nonNull(p.getIsactive())){
            Long warehouseId = Objects.isNull(p.getCpCPhyWarehouseId())?stCAllocationCostItem.getCpCPhyWarehouseId():p.getCpCPhyWarehouseId();
            BigDecimal startWeight = Objects.isNull(p.getStartWeight())?stCAllocationCostItem.getStartWeight():p.getStartWeight();
            BigDecimal endWeight = Objects.isNull(p.getEndWeight())?stCAllocationCostItem.getEndWeight():p.getEndWeight();
            List<StCAllocationCostItem> stCAllocationCostItems = stCAllocationCostItemMapper.selectList(new QueryWrapper<StCAllocationCostItem>().lambda()
                    .eq(StCAllocationCostItem::getStCAllocationCostId, objid)
                    .eq(StCAllocationCostItem::getCpCPhyWarehouseId, warehouseId)
                    .lt(StCAllocationCostItem::getStartWeight, endWeight)
                    .gt(StCAllocationCostItem::getEndWeight, startWeight)
                    .ne(StCAllocationCostItem::getId, p.getId())
                    .eq(StCAllocationCostItem::getIsactive,YesNoEnum.Y.getKey()));
            if(CollectionUtils.isNotEmpty(stCAllocationCostItems)){
                throw new NDSException("录入的明细重量区间不允许交叉！");
            }
        }
    }

    /**
     * 校验子表数据有效性
     * @param stCAllocationCostItem
     */
    private void checkInsertItemParam(Long objid,StCAllocationCostItem stCAllocationCostItem) {
        if(Objects.isNull(stCAllocationCostItem)){
            throw new NDSException("子表数据不能为空！");
        }
        if(Objects.isNull(stCAllocationCostItem.getCpCPhyWarehouseId())){
            throw new NDSException("仓库不能为空！");
        }
        if(Objects.nonNull(stCAllocationCostItem.getArrivalDays()) && stCAllocationCostItem.getArrivalDays()<0){
            throw new NDSException("到货天数必须为正整数！");
        }
        if(Objects.isNull(stCAllocationCostItem.getStartWeight())){
            throw new NDSException("请填写起始重量！");
        }
        if(stCAllocationCostItem.getStartWeight().compareTo(BigDecimal.ZERO)<0){
            throw new NDSException("起始重量必须为正数且最多两位小数！");
        }
        if(Objects.isNull(stCAllocationCostItem.getEndWeight())){
            throw new NDSException("请填写结束重量！");
        }
        if(stCAllocationCostItem.getEndWeight().compareTo(BigDecimal.ZERO)<0){
            throw new NDSException("结束重量必须为正数且最多两位小数！");
        }
        if(stCAllocationCostItem.getStartWeight().compareTo(stCAllocationCostItem.getEndWeight())>=0){
            throw new NDSException("结束重量必须大于起始重量！");
        }

        if(Objects.isNull(stCAllocationCostItem.getTrunkFreight())){
            throw new NDSException("请填写干线运费！");
        }
        if(stCAllocationCostItem.getTrunkFreight().compareTo(BigDecimal.ZERO)<0){
            throw new NDSException("干线运费必须为正数且最多两位小数！");
        }
        if(Objects.nonNull(stCAllocationCostItem.getDeliveryFee()) && stCAllocationCostItem.getDeliveryFee().compareTo(BigDecimal.ZERO)<0){
            throw new NDSException("提货费必须为正数且最多两位小数！");
        }
        if(Objects.nonNull(stCAllocationCostItem.getFreight()) && stCAllocationCostItem.getFreight().compareTo(BigDecimal.ZERO)<0){
            throw new NDSException("送货费必须为正数且最多两位小数！");
        }
        if(Objects.nonNull(stCAllocationCostItem.getPremium()) && stCAllocationCostItem.getPremium().compareTo(BigDecimal.ZERO)<0){
            throw new NDSException("保费必须为正数且最多两位小数！");
        }
        if(Objects.nonNull(stCAllocationCostItem.getUnloadingFee()) && stCAllocationCostItem.getUnloadingFee().compareTo(BigDecimal.ZERO)<0){
            throw new NDSException("卸货费必须为正数且最多两位小数！");
        }
        if(Objects.nonNull(stCAllocationCostItem.getOtherFee()) && stCAllocationCostItem.getOtherFee().compareTo(BigDecimal.ZERO)<0){
            throw new NDSException("其他费用必须为正数且最多两位小数！");
        }
        //重量重复校验
        List<StCAllocationCostItem> stCAllocationCostItems = stCAllocationCostItemMapper.selectList(new QueryWrapper<StCAllocationCostItem>().lambda()
                .eq(StCAllocationCostItem::getStCAllocationCostId, objid)
                .eq(StCAllocationCostItem::getCpCPhyWarehouseId, stCAllocationCostItem.getCpCPhyWarehouseId())
                .lt(StCAllocationCostItem::getStartWeight, stCAllocationCostItem.getEndWeight())
                .gt(StCAllocationCostItem::getEndWeight, stCAllocationCostItem.getStartWeight())
                .eq(StCAllocationCostItem::getIsactive,YesNoEnum.Y.getKey()));
        if(CollectionUtils.isNotEmpty(stCAllocationCostItems)){
            throw new NDSException("录入的明细重量区间不允许交叉！");
        }
    }

    /**
     * 校验主表数据有效性
     * @param stCAllocationCost
     */
    private void checkInsertParam(StCAllocationCost stCAllocationCost) {
        if (Objects.isNull(stCAllocationCost)) {
            throw new NDSException("入参不能为空！");
        }
        if(Objects.isNull(stCAllocationCost.getCpCLogisticsId())){
            throw new NDSException("请选择物流公司!");
        }
        if(Objects.isNull(stCAllocationCost.getStartDate())){
            throw new NDSException("请填写开始时间");
        }
        if(Objects.isNull(stCAllocationCost.getEndDate())){
            throw new NDSException("请填写结束时间");
        }
        if(stCAllocationCost.getStartDate().getTime()>stCAllocationCost.getEndDate().getTime()){
            throw new NDSException("开始时间晚于结束时间，不允许！");
        }
        String s = DateUtil.format(new Date(), "yyyy-MM-dd");
        Date date = null;
        try {
            date = DateUtils.parseDate(s, "yyyy-MM-dd");
        } catch (ParseException e) {
            e.printStackTrace();
        }
        if(stCAllocationCost.getEndDate().getTime()<date.getTime()){
            throw new NDSException("结束时间不能早于系统当前时间，不允许！");
        }
    }

    /**
     * 匹配查询调拨报价
     * @param request
     * @return
     */
    public StCAllocationCostQueryResult queryAllocationCostByCondition(StCAllocationCostQueryRequest request) {
        if(Objects.isNull(request)){
            throw new NDSException("入参不能为空！");
        }
        if(CollectionUtils.isEmpty(request.getWarehouseIdList())){
            throw new NDSException("实体仓ID不能为空！");
        }
        if(Objects.isNull(request.getTotalWeight())){
            throw new NDSException("重量不能为空！");
        }
        StCAllocationCostQueryResult result = new StCAllocationCostQueryResult();
        List<StCAllocationCostItem> stCAllocationCostItems = stCAllocationCostItemMapper.selectList(new QueryWrapper<StCAllocationCostItem>().lambda()
                .in(StCAllocationCostItem::getCpCPhyWarehouseId, request.getWarehouseIdList())
                .lt(StCAllocationCostItem::getStartWeight, request.getTotalWeight())
                .ge(StCAllocationCostItem::getEndWeight, request.getTotalWeight())
                .eq(StCAllocationCostItem::getIsactive, YesNoEnum.Y.getKey()));
        if(CollectionUtils.isNotEmpty(stCAllocationCostItems)){
            List<Long> allocationCostIds
                    = stCAllocationCostItems.stream().map(StCAllocationCostItem::getStCAllocationCostId).distinct().collect(Collectors.toList());
            String today = DateUtil.format(new Date(),"yyyy-MM-dd");
            List<StCAllocationCost> stCAllocationCosts = stCAllocationCostMapper.selectList(new QueryWrapper<StCAllocationCost>().lambda()
                    .in(StCAllocationCost::getId, allocationCostIds)
                    .le(StCAllocationCost::getStartDate,today)
                    .ge(StCAllocationCost::getEndDate, today)
                    .eq(StCAllocationCost::getStatus, 2)
                    .eq(StCAllocationCost::getCloseStatus, 0).eq(StCAllocationCost::getIsactive, YesNoEnum.Y.getKey()));
            if(CollectionUtils.isNotEmpty(stCAllocationCosts)){
                List<Long> mainTableIds = stCAllocationCosts.stream().map(StCAllocationCost::getId).collect(Collectors.toList());
                //根据主表结果过滤子表
                List<StCAllocationCostItem> stCAllocationCostItemResult
                        = stCAllocationCostItems.stream().filter(p -> mainTableIds.contains(p.getStCAllocationCostId())).collect(Collectors.toList());
                result.setStCAllocationCostList(stCAllocationCosts);
                result.setStCAllocationCostItemList(stCAllocationCostItemResult);
            }
        }
        return result;
    }

    /**
     * 结案
     * @param param
     */
    public ValueHolder end(JSONObject param, User user) {
        JSONArray failArray = new JSONArray();
        JSONArray ids = param.getJSONArray("ids");
        int success = 0;
        for(Object id:ids){
            try {
                Long objid = Long.parseLong((String) id);
                StCAllocationCost stCAllocationCost = stCAllocationCostMapper.selectById(objid);
                if (Objects.isNull(stCAllocationCost)) {
                    throw new NDSException("当前记录不存在！");
                }
                if (stCAllocationCost.getStatus() != 2) {
                    throw new NDSException("当前单据状态，不允许结案！");
                }
                if (stCAllocationCost.getCloseStatus() != 0) {
                    throw new NDSException("当前单据状态，不允许结案！");
                }

                StCAllocationCost stCAllocationCostPo = new StCAllocationCost();
                stCAllocationCostPo.setId(objid);
                stCAllocationCostPo.setCloseStatus(1);
                stCAllocationCostPo.setCloseTime(new Date());
                stCAllocationCostPo.setCloseUserId(new Long(user.getId()));
                StBeanUtils.makeModifierField(stCAllocationCostPo, user);
                stCAllocationCostMapper.updateById(stCAllocationCostPo);
                success++;
            }catch(Exception e){
                log.error(LogUtil.format("结案失败："+id,"调拨报价设置"),e);
                JSONObject fail = new JSONObject();
                fail.put("objid",id);
                fail.put("message",e.getMessage());
                failArray.add(fail);
            }
        }
        if(org.springframework.util.CollectionUtils.isEmpty(failArray)){
            return ValueHolderUtils.success("结案成功！");
        }else{
            String message = String.format("提交成功记录数：%s，提交失败记录数：%s",success,ids.size()-success);
            return ValueHolderUtils.fail(message,failArray);
        }
    }

    /**
     * 删除
     *
     * @param param
     * @param querySession
     */
    public void delete(JSONObject param, QuerySession querySession) {
        JSONObject tabItem = param.getJSONObject("tabitem");
        Long objid = param.getLong("objid");
        StCAllocationCost stCAllocationCost = stCAllocationCostMapper.selectById(objid);
        if (Objects.isNull(stCAllocationCost)) {
            throw new NDSException("当前记录不存在！");
        }
        if (stCAllocationCost.getStatus() != 1) {
            throw new NDSException("当前单据状态，不允许删除！");
        }

        //判断是不是只删除子表
        if (Objects.nonNull(tabItem) && CollectionUtils.isNotEmpty(tabItem.getJSONArray("ST_C_ALLOCATION_COST_ITEM"))) {
            JSONArray itemIds = tabItem.getJSONArray("ST_C_ALLOCATION_COST_ITEM");
            List<Long> itemIdList = JSONArray.parseArray(itemIds.toJSONString(), Long.class);
            List<StCAllocationCostItem> itemList = stCAllocationCostItemMapper.selectBatchIds(itemIdList);
            if (CollectionUtils.isNotEmpty(itemList)) {
                Map<Long, String> beforeDelObjMap = new HashMap<>();
                for (StCAllocationCostItem item : itemList) {
                    beforeDelObjMap.put(item.getId(), JSON.toJSONString(item));
                }
                querySession.setAttribute("beforeDelObjMap", beforeDelObjMap);
            }
            stCAllocationCostItemMapper.deleteBatchIds(itemIdList);
        } else {
            //删除主表
            stCAllocationCostMapper.deleteById(objid);
            //删除关联的全部子表
            QueryWrapper<StCAllocationCostItem> queryWrapper = new QueryWrapper<>();
            queryWrapper.lambda().eq(StCAllocationCostItem::getStCAllocationCostId, objid);
            stCAllocationCostItemMapper.delete(queryWrapper);
        }
    }

    /**
     * 审核
     * @param param
     * @param user
     */
    public void submit(JSONObject param, User user) {
        Long objid = param.getLong("objid");
        StCAllocationCost stCAllocationCost = stCAllocationCostMapper.selectById(objid);
        if(Objects.isNull(stCAllocationCost)){
            throw new NDSException("当前记录不存在！");
        }
        if(stCAllocationCost.getStatus()!=1){
            throw new NDSException("当前单据状态，不允许审核！");
        }
        List<StCAllocationCostItem> stCAllocationCostItems
                = stCAllocationCostItemMapper.selectList(new QueryWrapper<StCAllocationCostItem>().lambda().eq(StCAllocationCostItem::getStCAllocationCostId, objid));
        if(CollectionUtils.isEmpty(stCAllocationCostItems)){
            throw new NDSException("请维护明细！");
        }
        //时间交叉重复校验
        List<StCAllocationCost> stCAllocationCosts = stCAllocationCostMapper.selectList(new QueryWrapper<StCAllocationCost>().lambda().eq(StCAllocationCost::getCpCLogisticsId, stCAllocationCost.getCpCLogisticsId())
                .le(StCAllocationCost::getStartDate, stCAllocationCost.getEndDate())
                .ge(StCAllocationCost::getEndDate, stCAllocationCost.getStartDate())
                .ne(StCAllocationCost::getEndDate, stCAllocationCost.getId())
                .eq(StCAllocationCost::getStatus, 2)
                .eq(StCAllocationCost::getCloseStatus, 0)
                .eq(StCAllocationCost::getIsactive, YesNoEnum.Y.getKey()));
        if(CollectionUtils.isNotEmpty(stCAllocationCosts)){
            throw new NDSException("同物流公司的时间区间不能重叠！");
        }

        StCAllocationCost stCAllocationCostPo = new StCAllocationCost();
        stCAllocationCostPo.setId(objid);
        stCAllocationCostPo.setStatus(2);
        stCAllocationCostPo.setSubmitTime(new Date());
        stCAllocationCostPo.setSubmitUserId(new Long(user.getId()));
        StBeanUtils.makeModifierField(stCAllocationCostPo, user);
        stCAllocationCostMapper.updateById(stCAllocationCostPo);
    }
}
