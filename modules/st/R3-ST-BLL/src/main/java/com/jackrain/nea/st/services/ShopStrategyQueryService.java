package com.jackrain.nea.st.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.model.enums.IsActiveEnum;
import com.jackrain.nea.oc.oms.model.enums.YesNoEnum;
import com.jackrain.nea.redis.util.RedisOpsUtil;
import com.jackrain.nea.st.common.StConstant;
import com.jackrain.nea.st.mapper.*;
import com.jackrain.nea.st.model.result.MergeOrderStrategyResult;
import com.jackrain.nea.st.model.result.StCPriceResult;
import com.jackrain.nea.st.model.result.StCShopStrategyLogisticsItemResult;
import com.jackrain.nea.st.model.table.*;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.utils.ValueHolderV14Utils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @description: 店铺策略服务接口
 * @author: 郑小龙
 * @date: 2019-04-03 16:15
 */
@Component
@Slf4j
public class ShopStrategyQueryService {
    @Autowired
    private StCShopStrategyMapper stCShopStrategyMapper;

    @Autowired
    private StCShopStrategyItemMapper stCShopStrategyItemMapper;

    @Autowired
    private StCShopStrategyLogisticsItemMapper logisticsItemMapper;

    @Autowired
    private StCPriceMapper priceMapper;

    @Autowired
    private StCPriceItemMapper stCPriceItemMapper;

    @Autowired
    private StCPriceExcludeItemMapper stCPriceExcludeItemMapper;

    @Autowired
    private StCAutoCheckMapper stCAutocheckMapper;

    @Autowired
    private StCAutoCheckExcludeProductMapper stCAutoCheckExcludeProductMapper;

    @Autowired
    private StCAutoCheckAutoTimeMapper stCAutoCheckAutoTimeMapper;

    @Autowired
    private StCMergeOrderMapper stCMergeOrderMapper;

    @Autowired
    private StCMergeCategoryLimitItemMapper mergeCategoryLimitItemMapper;

    @Autowired
    private StCAutoInvoiceMapper stCAutoInvoiceMapper;

    @Autowired
    private RedisOpsUtil<String, String> redisUtil;

    @Autowired
    private SplitOrderReasonService splitOrderReasonService;

    public ValueHolderV14<StCShopStrategyDO> queryShopStrategyById(Long id) throws NDSException {
        ValueHolderV14<StCShopStrategyDO> valueHolderV14 = new ValueHolderV14<>();
        HashMap<String, Object> map = new HashMap<>();
        map.put("cp_c_shop_id", id);
        List<StCShopStrategyDO> stCShopStrategyDOList = stCShopStrategyMapper.selectByMap(map);
        valueHolderV14.setCode(-1);
        valueHolderV14.setMessage("查询失败，无数据记录！");
        if (!CollectionUtils.isEmpty(stCShopStrategyDOList)) {
            valueHolderV14.setCode(0);
            valueHolderV14.setMessage("查询成功！");
            valueHolderV14.setData(stCShopStrategyDOList.get(0));
        }
        return valueHolderV14;
    }

    public StCShopStrategyDO selectOcStCShopStrategyByCpCshopId(Long id) {
        log.debug(LogUtil.format("ShopStrategyQueryService.selectOcStCShopStrategyByCpCshopId入参:", id));
        QueryWrapper<StCShopStrategyDO> wrapper = new QueryWrapper<>();
        wrapper.eq("cp_c_shop_id", id).eq("isactive", "Y");
        StCShopStrategyDO stCShopStrategyDO = stCShopStrategyMapper.selectOne(wrapper);
        log.debug(LogUtil.format("ShopStrategyQueryService.selectOcStCShopStrategyByCpCshopId返回:") + JSONObject.toJSONString(stCShopStrategyDO));
        return stCShopStrategyDO;
    }

    public List<String> selectDiffpriceskuList(Long shopId, List<String> skuEcodeList) {
        log.debug(LogUtil.multiFormat(LogUtil.format("ShopStrategyQueryService" +
                ".selectDiffpriceskuList入参：shopId/skuEcodeList=", shopId), skuEcodeList));
        List<String> priceSkuList = stCShopStrategyMapper.queryDiffenPriceSku(shopId, skuEcodeList);
        log.debug(LogUtil.format("ShopStrategyQueryService.selectDiffpriceskuList返回:") + JSONObject.toJSONString(priceSkuList));
        return priceSkuList;
    }

    public List<StCShopStrategyItemDO> queryShopStrategyItem(Long shopId) {
        log.debug(LogUtil.format("ShopStrategyQueryService.queryShopStrategyItem入参：shopId=", shopId));
        List<StCShopStrategyItemDO> itemList = stCShopStrategyItemMapper.queryShopStrategyItem(shopId);
        log.debug(LogUtil.format("ShopStrategyQueryService.queryShopStrategyItem返回:") + JSONObject.toJSONString(itemList));
        return itemList;
    }

    /**
     * 根据店铺查找店铺价格方案
     *
     * @param shopId 店铺Id
     * @return List<Long>
     */
    public List<Long> queryPriceList(Long shopId) {
        log.debug(LogUtil.format("ShopStrategyQueryService.queryPriceList入参：shopId=", shopId));
        List<Long> priceList = priceMapper.queryPriceList(new Date(), shopId);
        log.debug(LogUtil.format("ShopStrategyQueryService.queryPriceList返回:") + JSONObject.toJSONString(priceList));
        return priceList;
    }

    /**
     * 统计商品价格策略明细是否存在
     *
     * @param stCPriceList 价格list
     * @param proId        商品Id
     * @return int
     */
    public List<BigDecimal> queryPriceTotal(List<Long> stCPriceList, Long proId) {
        log.debug(LogUtil.multiFormat(LogUtil.format("ShopStrategyQueryService.queryPriceTotal入参：proId/stCPriceList="
                ,  proId), stCPriceList));
        List<BigDecimal> priceTotal = priceMapper.queryPriceTotal(stCPriceList, proId);
        log.debug(LogUtil.format("ShopStrategyQueryService.queryPriceTotal返回:") + JSONObject.toJSONString(priceTotal));
        return priceTotal;
    }

    /**
     * 根据店铺查询店铺自动审核策略
     *
     * @param shopId 店铺Id
     * @return StCAutocheckDO
     */
    public StCAutoCheckDO queryOcStCAutocheck(Long shopId) {
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("ShopStrategyQueryService.queryOcStCAutocheck入参：shopId=", shopId));
        }
        StCAutoCheckDO stCAutocheckDO = null;
        stCAutocheckDO = stCAutocheckMapper.queryOcStCAutocheck(shopId);
        if (stCAutocheckDO == null) {
            log.error(LogUtil.format("数据库中没有自动审核店铺策略,ShopId=", shopId));
            return null;
        }
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("ShopStrategyQueryService.queryOcStCAutocheck返回:{}"),
                    JSON.toJSONString(stCAutocheckDO));
        }

        return stCAutocheckDO;
    }

    /**
     * 查询自动审核策略排除商品
     * @param checkId
     * @return
     */
    public List<StCAutoCheckExcludeProductDO> queryOcStCAutoCheckExcludeProduct(Long checkId) {
        List<StCAutoCheckExcludeProductDO> stCAutoCheckExcludeProductDOS =
                stCAutoCheckExcludeProductMapper.selectList(new QueryWrapper<StCAutoCheckExcludeProductDO>()
                .lambda().eq(StCAutoCheckExcludeProductDO::getStCAutocheckId, checkId)
                .eq(StCAutoCheckExcludeProductDO::getIsactive, YesNoEnum.Y.getKey()));
        return stCAutoCheckExcludeProductDOS;
    }

    /**
     * 查询自动审核策略时间
     * @param checkId
     * @return
     */
    public List<StCAutoCheckAutoTimeDO> queryOcStCAutoCheckAutoTime(Long checkId) {
        List<StCAutoCheckAutoTimeDO> stCAutoCheckAutoTimeDOS =
                stCAutoCheckAutoTimeMapper.selectList(new QueryWrapper<StCAutoCheckAutoTimeDO>()
                        .lambda().eq(StCAutoCheckAutoTimeDO::getStCAutocheckId, checkId)
                        .eq(StCAutoCheckAutoTimeDO::getIsactive, YesNoEnum.Y.getKey()));
        return stCAutoCheckAutoTimeDOS;
    }

    /**
     * 根据店铺查询店铺自动开票策略
     *
     * @param shopId 店铺Id
     * @return StCAutoInvoiceDO
     */
    public StCAutoInvoiceDO queryStCAutoInvoice(Long shopId) {
        log.debug(LogUtil.format("ShopStrategyQueryService.queryStCAutoInvoiceDO入参：shopId=", shopId));
        StCAutoInvoiceDO stCAutoInvoice = stCAutoInvoiceMapper.queryStCAutoInvoice(shopId);
        log.debug(LogUtil.format("ShopStrategyQueryService.queryStCAutoInvoiceDO返回:") + JSONObject.toJSONString(stCAutoInvoice));
        return stCAutoInvoice;
    }

    /**
     * 查询所有自动开票策略
     *
     * @return List<StCAutoInvoiceDO>
     */
    public List<StCAutoInvoiceDO> queryAllAutoInvoice() {
        List<StCAutoInvoiceDO> stCAutoInvoiceList = stCAutoInvoiceMapper.selectList(new QueryWrapper<StCAutoInvoiceDO>()
                .eq("ISACTIVE", StConstant.ISACTIVE_Y));
        return stCAutoInvoiceList;
    }

    /**
     * 根据店铺查询订单退单自动审核策略
     *
     * @param shopId 店铺Id
     * @return boolean
     */
    public boolean isExitsRefundOrderStrategy(Long shopId) {
        log.debug(LogUtil.format("ShopStrategyQueryService.isExitsRefundOrderStrategy入参：shopId=", shopId));
        int count = stCShopStrategyMapper.queryStCShopStrategyAutoAudit(shopId);
        boolean flg = false;
        if (count > 0) {
            flg = true;
        }
        log.debug(LogUtil.format("ShopStrategyQueryService.isExitsRefundOrderStrategy返回：", flg));
        return flg;
    }

    /**
     * 传入店铺和sku,判断该sku是否为补差价条码
     *
     * @param shopId   店铺Id
     * @param skuEcode 条码Ecode
     * @return boolean
     */
    public boolean isDifferenPriceSku(Long shopId, String skuEcode) {
        log.debug(LogUtil.format("ShopStrategyQueryService.isDifferenPriceSku入参：shopId/skuEcode="
                , shopId, skuEcode));
        Long skuTotal = stCShopStrategyMapper.differPriceSku(shopId, skuEcode);
        boolean flg = true;
        if (skuTotal == null || skuTotal.longValue() == 0) {
            flg = false;
        }
        log.debug(LogUtil.format("ShopStrategyQueryService.isDifferenPriceSku返回:", flg));
        return flg;
    }

    /**
     * 查询店铺的默认发货仓库
     *
     * @param shopId 店铺Id
     * @return Long
     */
    public Long queryDefaultWarehouse(Long shopId) {
        log.debug(LogUtil.format("ShopStrategyQueryService.queryDefaultWarehouse入参：shopId=", shopId));
        StCShopStrategyDO shopStrategy = stCShopStrategyMapper.selectOcStCShopStrategy(shopId);
        if (shopStrategy == null) {
            shopStrategy = new StCShopStrategyDO();
        }
        Long storeId = shopStrategy.getDefaultStoreId();
        log.debug(LogUtil.format("ShopStrategyQueryService.queryDefaultWarehouse返回：storeId=", storeId));
        return storeId;
    }

    /**
     * description：斯凯奇项目合单策略-新增品类限制需求 增加品类限制明细查询返回
     *
     * <AUTHOR>
     * @date 2021/5/13
     */
    public List<MergeOrderStrategyResult> queryAllMergeOrderInfo() {
        List<StCMergeOrderDO> mergeOrderDOS = this.queryAllMergeOrder();
        if (CollectionUtils.isNotEmpty(mergeOrderDOS)) {
            Set<Long> mergeOrderIds = mergeOrderDOS.stream().filter(x -> IsActiveEnum.Y.getKey().equalsIgnoreCase(x.getCategoryLimit())).map(StCMergeOrderDO::getId).collect(Collectors.toSet());
            Map<Long, List<StCMergeCategoryLimitItemDO>> itemMap = new HashMap<>(mergeOrderIds.size());
            if (CollectionUtils.isNotEmpty(mergeOrderIds)) {
                List<StCMergeCategoryLimitItemDO> stCMergeCategoryLimitItemDOS = mergeCategoryLimitItemMapper.selectList(new LambdaQueryWrapper<StCMergeCategoryLimitItemDO>()
                        .in(StCMergeCategoryLimitItemDO::getStCMergeOrderId, mergeOrderIds)
                        .eq(StCMergeCategoryLimitItemDO::getIsactive, IsActiveEnum.Y.getKey()));
                if (CollectionUtils.isNotEmpty(stCMergeCategoryLimitItemDOS)) {
                    itemMap = stCMergeCategoryLimitItemDOS.stream().collect(Collectors.groupingBy(StCMergeCategoryLimitItemDO::getStCMergeOrderId));
                }
            }
            List<MergeOrderStrategyResult> resultList = new ArrayList<>(mergeOrderDOS.size());
            for (StCMergeOrderDO mergeOrderDO : mergeOrderDOS) {
                MergeOrderStrategyResult result = new MergeOrderStrategyResult();
                result.setMergeOrderDO(mergeOrderDO);
                List<StCMergeCategoryLimitItemDO> itemList = itemMap.get(mergeOrderDO.getId());
                if (CollectionUtils.isNotEmpty(itemList)) {
                    result.setMergeCategoryLimitItemList(itemList);
                }
                resultList.add(result);
            }
            return resultList;
        }
        return null;
    }
    /**
     * 查询订单自动合并策略
     *
     * @return List<StCMergeOrderDO>
     */
    public List<StCMergeOrderDO> queryAllMergeOrder() {
        List<StCMergeOrderDO> stCMergeOrderList = stCMergeOrderMapper.queryAllMergeOrder();
        if (log.isInfoEnabled()) {
            log.debug(LogUtil.format("ShopStrategyQueryService.queryAllMergeOrder返回:{}"),
                    JSONObject.toJSONString(stCMergeOrderList));
        }

        if (CollectionUtils.isEmpty(stCMergeOrderList)) {
            log.error(LogUtil.format("数据库中没有自动合单店铺策略"));
            return null;
        }

        /**
         * redis保存拆单type
         */
        for (StCMergeOrderDO stCMergeOrderDO : stCMergeOrderList) {
            String mergeSplitOrderType = stCMergeOrderDO.getMergeSplitOrderType();
            if (StringUtils.isBlank(mergeSplitOrderType)) {
                continue;
            }
            /**
             * 根据主键ID查询type
             */
            List<Long> idList = Arrays.stream(mergeSplitOrderType.split(","))
                    .filter(StringUtils::isNotBlank).map(Long::valueOf).collect(Collectors.toList());
            List<StCOrderSplitMergeType> stCOrderSplitMergeType = splitOrderReasonService.getStCOrderSplitMergeType(idList);
            List<Integer> typeList = stCOrderSplitMergeType.stream().map(
                    StCOrderSplitMergeType::getType).collect(Collectors.toList());
            String join = StringUtils.join(typeList, ",");

            stCMergeOrderDO.setMergeSplitOrderType(join);
        }

        return stCMergeOrderList;
    }

    /**
     * 查询所有店铺策略
     *
     * @return List<StCShopStrategyDO>
     */
    public List<StCShopStrategyDO> queryAllShopStrategy() {
        QueryWrapper<StCShopStrategyDO> wrapper = new QueryWrapper<>();
        wrapper.eq("isactive", "Y");
        List<StCShopStrategyDO> stCShopStrategyList = stCShopStrategyMapper.selectList(wrapper);
        log.debug(LogUtil.format("ShopStrategyQueryService.queryAllShopStrategy返回:") + JSONObject.toJSONString(stCShopStrategyList));
        return stCShopStrategyList;
    }

    /**
     * 查询启用下载订单的店铺策略查询
     *
     * @return List<StCShopStrategyDO>
     */
    public List<StCShopStrategyDO> queryOrderLoadShopStrategy() {
        QueryWrapper<StCShopStrategyDO> wrapper = new QueryWrapper<>();
        wrapper.eq("isactive", "Y");
        wrapper.eq("is_order_load", "Y");
        List<StCShopStrategyDO> stCShopStrategyList = stCShopStrategyMapper.selectList(wrapper);
        log.debug(LogUtil.format("ShopStrategyQueryService.queryOrderLoadShopStrategy:") + JSONObject.toJSONString(stCShopStrategyList));
        return stCShopStrategyList;
    }

    public List<StCExchangeStrategyOrderDO> queryAllExchangeShopStrategy() {
        return stCMergeOrderMapper.queryAllExchangeShopStrategy();
    }

    public StCShopStrategyLogisticsItemResult queryShopStrategyLogisticsList(Long shopId) {
        StCShopStrategyLogisticsItemResult result = new StCShopStrategyLogisticsItemResult();
        LambdaQueryWrapper<StCShopStrategyDO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(StCShopStrategyDO::getCpCShopId, shopId);
        wrapper.eq(StCShopStrategyDO::getIsactive, StConstant.ISACTIVE_Y);
        StCShopStrategyDO strategy = stCShopStrategyMapper.selectOne(wrapper);


        if (strategy != null) {
            result.setLogisticsType(strategy.getLogisticsType());
            List<StCShopStrategyLogisticsItem> itemList =
                    logisticsItemMapper.selectList(new LambdaQueryWrapper<StCShopStrategyLogisticsItem>()
                            .eq(StCShopStrategyLogisticsItem::getStCShopStrategyId, strategy.getId())
                            .eq(StCShopStrategyLogisticsItem::getIsactive, StConstant.ISACTIVE_Y));
            result.setLogisticsItemList(itemList);
        }

        return result;
    }

    /**
     * 根据店铺ID查询店铺价格策略
     * @param shopId
     * @return
     */
    public ValueHolderV14<StCPriceResult> queryPricesByShopId(Long shopId) {
        ValueHolderV14 v14 = ValueHolderV14Utils.getSuccessValueHolder("查询成功！");
        StCPriceResult stCPriceResult = new StCPriceResult();
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("根据店铺ID查询店铺价格策略！shopId={}",
                    "ShopStrategyQueryService"),shopId);
        }
        StCPriceDO stCPriceDO = priceMapper.queryPriceByShopId(new Date(), shopId);
        if(stCPriceDO != null){
            Long id = stCPriceDO.getId();
            List<StCPriceItemDO> priceItemDOList = stCPriceItemMapper.selectItemByPriceId(id);
            List<StCPriceExcludeItemDO> excludeItemDOList = stCPriceExcludeItemMapper.selectExcludeItemByPriceId(id);

            //设置返回信息
            stCPriceResult.setId(id);
            stCPriceResult.setCpCShopId(shopId);
            stCPriceResult.setStCPriceDO(stCPriceDO);
            stCPriceResult.setStCPriceItemDOS(priceItemDOList);
            stCPriceResult.setStCPriceExcludeItemDOS(excludeItemDOList);
            v14.setData(stCPriceResult);
        }
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("根据店铺ID查询店铺价格策略！result={}",
                    "ShopStrategyQueryService"),JSON.toJSONString(stCPriceResult));
        }


        return v14;
    }

    //public Set<Long> queryMergeShopAllShopIds() {
    //    String redisKey = OmsRedisKeyResources.buildAutoMergeAllListRedisKey();
    //    Set<Long> result = null;
    //
    //    try {
    //        Set<Long> checkShopIds = getRedisAllShopIds(redisKey);
    //        if (CollectionUtils.isNotEmpty(checkShopIds)) {
    //            result = checkShopIds;
    //            return result;
    //        }
    //    } catch (Exception e) {
    //        log.error(LogUtil.format("{}.redis查询所有自动审单策略异常", this.getClass().getName(), e);
    //    }
    //
    //
    //    Map<Long, StCMergeOrderDO> map = queryDbMergeOrderAllShopIds();
    //    if (MapUtils.isEmpty(map)) {
    //        return null;
    //    }
    //
    //    result = map.keySet();
    //
    //    try {
    //        //存放在redis中
    //        this.setRedisAllShopIds(redisKey, result);
    //    } catch (Exception e) {
    //        log.error(LogUtil.format("{}.redis保存所有自动审单策略异常", this.getClass().getName(), e);
    //    }
    //
    //    return result;
    //}


    //private Set<Long> getRedisAllShopIds(String key) {
    //    Set<Long> result = null;
    //
    //    try {
    //        if (redisUtil.strRedisTemplate.hasKey(key)) {
    //            Set<String> checkShopIds = redisUtil.strRedisTemplate.opsForSet().members(key);
    //            if (CollectionUtils.isNotEmpty(checkShopIds)) {
    //
    //                result = checkShopIds.stream().filter(shopId -> StringUtils.isNoneBlank(shopId)).map(shopId -> Long.parseLong(shopId)).collect(Collectors.toSet());
    //                return result;
    //            }
    //        }
    //    } catch (Exception e) {
    //        throw new NDSException("redis查询所有店铺ID异常," + e.getMessage());
    //    }
    //
    //    return null;
    //}

    //private void setRedisAllShopIds(String key, Set<Long> value) {
    //
    //    if (StringUtils.isBlank(key) || org.apache.commons.collections4.CollectionUtils.isEmpty(value)) {
    //        return;
    //    }
    //    String[] valueArray = value.stream().filter(shopId -> shopId != null)
    //            .map(shopId -> shopId.toString()).collect(Collectors.toSet())
    //            .stream().toArray(String[]::new);
    //
    //    setRedisAllShopIds(key, valueArray);
    //}

    //private void setRedisAllShopIds(String key, String[] value) {
    //    if (StringUtils.isBlank(key) || value == null || value.length == 0) {
    //        return;
    //    }
    //
    //    try {
    //        //存放在redis中
    //        if (redisUtil.strRedisTemplate.hasKey(key)) {
    //            redisUtil.strRedisTemplate.delete(key);
    //        }
    //        redisUtil.strRedisTemplate.opsForSet().add(key, value);
    //    } catch (Exception e) {
    //        throw new NDSException("redis保存所有自动审单策略异常," + e.getMessage());
    //    }
    //}


    //private Map<Long, StCMergeOrderDO> queryDbMergeOrderAllShopIds() {
    //
    //    List<StCMergeOrderDO> stCMergeOrderDOS = this.queryAllMergeOrder();
    //    if (CollectionUtils.isEmpty(stCMergeOrderDOS)) {
    //        return null;
    //    }
    //
    //    Map<Long, StCMergeOrderDO> map =
    //            stCMergeOrderDOS.stream().filter(item -> item.getCpCShopId() != null)
    //                    .collect(Collectors.toMap(StCMergeOrderDO::getCpCShopId, item -> item));
    //
    //    return map;
    //}

    /**
     * 查询所有可以进行合并订单的店铺
     *
     * @return
     */
    //public Map<Long, StCMergeOrderDO> queryAllMergeShop() {
    //    Set<Long> autoMergeAllShopIds = this.queryMergeShopAllShopIds();
    //    if (CollectionUtils.isEmpty(autoMergeAllShopIds)) {
    //        return null;
    //    }
    //
    //    Map<Long, StCMergeOrderDO> result = new HashMap<>(autoMergeAllShopIds.size());
    //    List<Long> notExistRedisIds = new ArrayList<>();
    //
    //    try {
    //
    //        try {
    //            for (Long shopId : autoMergeAllShopIds) {
    //                StCMergeOrderDO stCMergeOrderDO = redisHashCommonUtils.hgetall(
    //                        OmsRedisKeyResources.bulidLockStCAutoMergeKey(shopId), StCMergeOrderDO.class);
    //                if (stCMergeOrderDO == null) {
    //                    notExistRedisIds.add(shopId);
    //                    continue;
    //                }
    //                result.put(shopId, stCMergeOrderDO);
    //            }
    //
    //            if (MapUtils.isNotEmpty(result)) {
    //                return result;
    //            }
    //        } catch (Exception e) {
    //            log.error(LogUtil.format("{}.redis查询所有自动合单策略异常", this.getClass().getName(), e);
    //        }
    //
    //        Map<Long, StCMergeOrderDO> map = this.queryDbMergeOrderAllShopIds();
    //        if (MapUtils.isEmpty(map)) {
    //            return null;
    //        }
    //        result = map;
    //
    //        try {
    //            //存放在redis中
    //            redisHashCommonUtils.hset(RedisConstant.bulidLockStCAutoMergeKey(), result);
    //        } catch (Exception e) {
    //            log.error(LogUtil.format("{}.redis保存所有自动合单策略异常", this.getClass().getName(), e);
    //        }
    //    } catch (NDSException e) {
    //        log.error(LogUtil.format("queryAllMergeShop查询所有可以进行合并订单的店铺异常", e);
    //    }
    //    return result;
    //}

    //public StCMergeOrderDO queryMergeShopByShopId(Long shopId) {
    //
    //    try {
    //        StCMergeOrderDO stCMergeOrderDO = redisHashCommonUtils.hgetall(
    //                OmsRedisKeyResources.bulidLockStCAutoMergeKey(shopId), StCMergeOrderDO.class);
    //        if (stCMergeOrderDO != null) {
    //            return stCMergeOrderDO;
    //        }
    //    } catch (Exception e) {
    //        log.error(LogUtil.format("{}.redis查询自动合单策略异常", this.getClass().getName(), e);
    //    }
    //
    //
    //    StCMergeOrderDO stCMergeOrderDO = stCMergeOrderMapper.selectByIdAndIsactive(shopId, "Y");
    //    if (stCMergeOrderDO == null){
    //       return null;
    //    }
    //
    //    try {
    //        //存放在redis中
    //        redisHashCommonUtils.hset(RedisConstant.bulidLockStCAutoMergeKey(shopId), stCMergeOrderDO);
    //    } catch (Exception e) {
    //        log.error(LogUtil.format("{}.redis保存所有自动审单策略异常", this.getClass().getName(), e);
    //    }
    //    return stCMergeOrderDO;
    //}

}
