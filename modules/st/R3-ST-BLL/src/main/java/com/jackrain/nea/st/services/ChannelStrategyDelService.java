package com.jackrain.nea.st.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.st.common.StConstant;
import com.jackrain.nea.st.mapper.StCChannelStrategyItemMapper;
import com.jackrain.nea.st.mapper.StCChannelStrategyMapper;
import com.jackrain.nea.st.model.table.StCChannelStrategyDO;
import com.jackrain.nea.st.model.table.StCChannelStrategyItemDO;
import com.jackrain.nea.st.model.table.StCSyncStockStrategyDO;
import com.jackrain.nea.st.utils.StBeanUtils;
import com.jackrain.nea.st.utils.ValueHolderUtils;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2019/12/4 17:21
 */
@Component
@Slf4j
@Transactional(rollbackFor = {Exception.class}, propagation = Propagation.SUPPORTS)
public class ChannelStrategyDelService extends CommandAdapter {
    @Autowired
    private StCChannelStrategyMapper mapper;
    @Autowired
    private StCChannelStrategyItemMapper itemMapper;

    @Override
    public ValueHolder execute(QuerySession querySession) throws NDSException {
        DefaultWebEvent event = querySession.getEvent();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(
                event.getParameterValue("param"), "yyyy-MM-dd HH:mm:ss"), Feature.OrderedField);
        log.info(LogUtil.format("ChannelStrategyDelServiceParam:{}"), param);
        String isDel = param.getString("isdelmtable");
        Long id = param.getLong("objid");

        //判断主表是否存在
        StCChannelStrategyDO channelStrategy = mapper.selectById(id);
        if (channelStrategy == null) {
            return ValueHolderUtils.getFailValueHolder("当前记录已不存在!");
        }
        JSONObject tabitem = param.getJSONObject("tabitem");
        JSONArray errorArray = new JSONArray();
        //判断是删除主表还是明细表单独删除
        if (StConstant.FALSE_STR.equals(isDel)) {
            //单独删除明细
            JSONArray itemArray = tabitem.getJSONArray(StConstant.TAB_ST_C_CHANNEL_STRATEGY_ITEM);
            if (itemArray.size() > 0) {
                delItemList(itemArray, errorArray, id);
            }
        } else {
            //删除主表
            delMain(id, errorArray);
        }
        return StBeanUtils.getExcuteValueHolder(errorArray);
    }

    public void delItemList(JSONArray itemArray, JSONArray errorArray, Long id) {
        for (int i = 0; i < itemArray.size(); i++) {
            Long itemid = itemArray.getLong(i);
            StCChannelStrategyItemDO delItem = itemMapper.selectById(itemid);
            if (delItem != null) {
                List<StCSyncStockStrategyDO> syncStockStrategys = itemMapper.selectCpCShopIdByChannelStrategy(id);
                //1. 删除明细表数据
                int deleteCount = itemMapper.deleteById(itemid);
                //小于0表示数据已经被删除
                if (deleteCount <= 0) {
                    ValueHolderUtils.getFailValueHolder("逻辑仓已不存在!");
                    errorArray.add(StBeanUtils.getJsonObjectInfo(itemid, "逻辑仓已不存在"));
                }
                for (StCSyncStockStrategyDO syncStockStrategy : syncStockStrategys) {
                    ValueHolderV14 v14 = changeSgChannel(syncStockStrategy.getCpCShopId(), delItem.getCpCStoreId());
                    if (!v14.isOK()) {
                        errorArray.add(StBeanUtils.getJsonObjectInfo(itemid, v14.getMessage()));
                    }
                }
            }
        }
    }

    @Transactional(rollbackFor = Exception.class, value = "drdsFlexibleTransaction", propagation = Propagation.SUPPORTS)
    public ValueHolderV14 changeSgChannel(Long cpCShopId, Long cpCStoreId) {
        ValueHolderV14 v = new ValueHolderV14();
        try {
//            // 删除渠道逻辑仓关系表
//            SgChannelStoreChangeRequest storeChange = new SgChannelStoreChangeRequest();
//            storeChange.setCpCShopId(cpCShopId);
//            storeChange.setCpCStoreId(cpCStoreId);
//            log.info(LogUtil.format("ChannelStrategyDelService,RPC参数_storeChange:{}", storeChange);
//            ValueHolderV14 v14 = sgChannelStoreChangeCmd.deleteSbGChannelStore(storeChange);
//            if (v14.getCode() == ResultCode.FAIL) {
//                log.error(LogUtil.format("调用删除渠道逻辑仓关系RPC接口[sgChannelStoreChangeCmd.deleteSbGChannelStore]失败:" + v14.getMessage());
//                throw new NDSException("调用删除渠道逻辑仓关系RPC接口失败！");
//            }
        } catch (Exception e) {
            throw new NDSException("调用更新渠道逻辑仓关系RPC接口失败");
        }
        v.setCode(ResultCode.SUCCESS);
        v.setMessage("成功!");
        return v;

    }

    public void delMain(Long mainId, JSONArray errorArray) {
        int deleteCount = mapper.deleteById(mainId);
        if (deleteCount <= 0) {
            errorArray.add(StBeanUtils.getJsonObjectInfo(mainId, "渠道策略已不存在"));
        }
    }
}
