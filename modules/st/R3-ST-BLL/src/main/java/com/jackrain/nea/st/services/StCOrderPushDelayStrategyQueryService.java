package com.jackrain.nea.st.services;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.jackrain.nea.st.common.StConstant;
import com.jackrain.nea.st.mapper.StCOrderPushDelayStrategyMapper;
import com.jackrain.nea.st.model.table.StCOrderPushDelayStrategy;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @Author: ganquan
 * @Date Create In 2020/7/1 15:07
 * @Description: 订单推单延时策略查询
 */
@Component
@Slf4j
public class StCOrderPushDelayStrategyQueryService {

    @Autowired
    private StCOrderPushDelayStrategyMapper stCOrderPushDelayStrategyMapper;

    /**
     * <AUTHOR>
     * @Description 根据店铺id查询策略信息(启用的)
     * @Date 16:17 2020/7/1
     * @param shopId
     * @return com.jackrain.nea.st.model.table.StCOrderPushDelayStrategy
     **/
    public StCOrderPushDelayStrategy queryOrderPushDelayStrategy(Long shopId) {
        List<StCOrderPushDelayStrategy> stCOrderPushDelayStrategies =
                stCOrderPushDelayStrategyMapper.selectList(new QueryWrapper<StCOrderPushDelayStrategy>()
                        .lambda().eq(StCOrderPushDelayStrategy::getIsEnableOrderPushDelay, StConstant.TRUE_FLAG)
                        .eq(StCOrderPushDelayStrategy::getIsactive, StConstant.ISACTIVE_Y)
                        .eq(StCOrderPushDelayStrategy::getCpCShopId, shopId));
        if (CollectionUtils.isNotEmpty(stCOrderPushDelayStrategies)) {
            return stCOrderPushDelayStrategies.get(0);
        }
        return null;
    }

    /**
     * <AUTHOR>
     * @Description 获取所有启用的推单延迟策略
     * @Date 16:19 2020/7/1
     * @param
     * @return java.util.List<com.jackrain.nea.st.model.table.StCOrderPushDelayStrategy>
     **/
    public List<StCOrderPushDelayStrategy> queryOrderPushDelayStrategyAll() {
        return stCOrderPushDelayStrategyMapper.selectList(new QueryWrapper<StCOrderPushDelayStrategy>()
                        .lambda().eq(StCOrderPushDelayStrategy::getIsEnableOrderPushDelay, StConstant.TRUE_FLAG));
    }

}
