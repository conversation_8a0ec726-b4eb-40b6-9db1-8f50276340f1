package com.jackrain.nea.st.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.st.mapper.StCRouterMapper;
import com.jackrain.nea.st.utils.StBeanUtils;
import com.jackrain.nea.st.utils.ValueHolderUtils;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @Descroption 路由操作码策略
 * <AUTHOR>
 * @Date 2020/5/11 15:45
 */
@Component
@Slf4j
public class RouterCodeDelService extends CommandAdapter {
    @Autowired
    private StCRouterMapper mapper;

    @Override
    public ValueHolder execute(QuerySession querySession) throws NDSException {
        DefaultWebEvent event = querySession.getEvent();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(
                event.getParameterValue("param"), "yyyy-MM-dd HH:mm:ss"), Feature.OrderedField);
        Long objid = param.getLong("objid");

        //判断主表是否存在
        if (mapper.selectById(objid) == null) {
            return ValueHolderUtils.getFailValueHolder("当前记录已不存在!");
        }

        JSONArray errorArray = new JSONArray();

        //删除主表
        delMain(objid, errorArray);

        return StBeanUtils.getExcuteValueHolder(errorArray);
    }

    /**
     * @param mainId
     * @param errorArray
     */
    public void delMain(Long mainId, JSONArray errorArray) {
        int deleteCount = mapper.deleteById(mainId);
        if (deleteCount <= 0) {
            errorArray.add(StBeanUtils.getJsonObjectInfo(mainId, "路由操作码策略已不存在"));
        }
        mapper.deleteByMasterId(mainId);
    }

}
