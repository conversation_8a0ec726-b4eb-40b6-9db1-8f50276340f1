package com.jackrain.nea.st.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.st.common.StConstant;
import com.jackrain.nea.st.mapper.StCSendPlanMapper;
import com.jackrain.nea.st.model.table.StCSendPlanDO;
import com.jackrain.nea.st.utils.RedisCacheUtil;
import com.jackrain.nea.st.utils.RedisConstant;
import com.jackrain.nea.st.utils.StBeanUtils;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.HashMap;

/**
 * @Descroption 刷单方案-结案逻辑
 * <AUTHOR>
 * @Date 2019/3/26
 */
@Component
@Slf4j
@Transactional
public class SendPlanFinishService extends CommandAdapter {
    @Autowired
    private StCSendPlanMapper stCMainMapper;

    @Override
    public ValueHolder execute(QuerySession querySession) throws NDSException {
        DefaultWebEvent event = querySession.getEvent();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(
                event.getParameterValue("param"), "yyyy-MM-dd HH:mm:ss"),
                Feature.OrderedField);

        JSONArray itemArray = StBeanUtils.makeAuditJsonArray(param);
        JSONArray errorArray = new JSONArray();
        HashMap<Long, Object> errMap = new HashMap();
        if (itemArray.size() > 0) {
            for (int i = 0; i < itemArray.size(); i++) {
                Long itemid = itemArray.getLong(i);
                try {
                    finishOperationcostByID(querySession, itemid, errorArray);
                } catch (Exception e) {
                    errMap.put(itemid, e.getMessage());
                }
            }
        }
        return StBeanUtils.getExcuteValueHolder(itemArray.size(), errMap);
    }

    private void finishOperationcostByID(QuerySession session, Long id, JSONArray errorArray) {
        StCSendPlanDO stCMainDO = stCMainMapper.selectById(id);
        JSONArray tempArray = new JSONArray();
        checkOperationCostStatus(stCMainDO, id, tempArray);
        if (tempArray.size() > 0) {
            errorArray.addAll(tempArray);
            return;
        }
        StBeanUtils.makeModifierField(stCMainDO, session.getUser());//修改信息
        stCMainDO.setModifierename(session.getUser().getEname());//修改人账号
        stCMainDO.setEstatus(StConstant.CON_BILL_STATUS_04);//结案状态
        stCMainDO.setFinishid(Long.valueOf(session.getUser().getId()));//结案人
        stCMainDO.setFinishtime(new Date());//结案时间
        stCMainDO.setFinishname(session.getUser().getName());//结案人姓名
        stCMainDO.setFinishename(session.getUser().getEname());//结案人账号
        if ((stCMainMapper.updateById(stCMainDO)) <= 0) {
            throw new NDSException("方案:" + stCMainDO.getEname() + ",结案失败！");
        }
        RedisCacheUtil.delete(stCMainDO.getCpCShopId(), RedisConstant.SHOP_SEND_PLAN);
    }

    private void checkOperationCostStatus(StCSendPlanDO stCMainDO, Long id, JSONArray errorArray) {
        if (stCMainDO == null) {
            throw new NDSException("当前记录已不存在！");
        }
        //不是已审核，不允许审核
        if (stCMainDO.getEstatus() == null
                || !StConstant.CON_BILL_STATUS_02.equals(stCMainDO.getEstatus())) {
            throw new NDSException("当前记录不是已审核，不允许结案！");
        }
    }

}
