package com.jackrain.nea.st.services;

import com.jackrain.nea.st.mapper.StCChannelStrategyMapper;
import com.jackrain.nea.st.model.table.StCChannelStrategyDO;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2019/12/10 10:30
 */
@Component
@Slf4j
@Deprecated
public class ChannelStrategyQueryService extends CommandAdapter {
    @Autowired
    private StCChannelStrategyMapper mapper;

    public ValueHolder getChannelStrategy(Long id) {
        ValueHolder valueHolder = new ValueHolder();
        List<StCChannelStrategyDO> data = mapper.selectActiveByChannelId(id);
        if (data != null && data.size() > 0) {
            valueHolder.put("code", 0);
            valueHolder.put("message", "查询成功！");
            valueHolder.put("data", data);
        } else {
            valueHolder.put("code", 0);
            valueHolder.put("message", "查询无数据！");
            valueHolder.put("data", null);
        }
        return valueHolder;
    }
}
