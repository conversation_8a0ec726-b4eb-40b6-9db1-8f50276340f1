//package com.jackrain.nea.st.services;
//
//import com.alibaba.fastjson.JSON;
//import com.alibaba.fastjson.JSONArray;
//import com.alibaba.fastjson.JSONObject;
//import com.alibaba.fastjson.parser.Feature;
//import com.jackrain.nea.constants.ResultCode;
//import com.jackrain.nea.exception.NDSException;
//import com.jackrain.nea.redis.util.RedisOpsUtil;
//import com.jackrain.nea.sg.oms.api.SgChannelStoreChangeCmd;
//import com.jackrain.nea.sg.oms.model.request.SgChannelStoreChangeRequest;
//import com.jackrain.nea.st.annotation.StOperationLog;
//import com.jackrain.nea.st.common.StConstant;
//import com.jackrain.nea.st.mapper.StCSyncStockStrategyChannelMapper;
//import com.jackrain.nea.st.mapper.StCSyncStockStrategyMapper;
//import com.jackrain.nea.st.model.table.StCSyncStockStrategyChannelDO;
//import com.jackrain.nea.st.model.table.StCSyncStockStrategyDO;
//import com.jackrain.nea.st.utils.RedisCacheUtil;
//import com.jackrain.nea.st.utils.RedisConstant;
//import com.jackrain.nea.st.utils.StBeanUtils;
//import com.jackrain.nea.st.utils.ValueHolderUtils;
//import com.jackrain.nea.sys.CommandAdapter;
//import com.jackrain.nea.sys.domain.ValueHolderV14;
//import com.jackrain.nea.util.ValueHolder;
//import com.jackrain.nea.web.query.DefaultWebEvent;
//import com.jackrain.nea.web.query.QuerySession;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.dubbo.config.annotation.Reference;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.stereotype.Component;
//import org.springframework.transaction.annotation.Propagation;
//import org.springframework.transaction.annotation.Transactional;
//
//import java.util.ArrayList;
//import java.util.HashMap;
//import java.util.List;
//import java.util.Map;
//
///**
// * @Descroption 店铺同步库存策略删除
// * <AUTHOR>
// * @Date 2019/3/10 22:55
// */
//@Component
//@Slf4j
//public class SyncStockStrategyDelService extends CommandAdapter {
//    @Autowired
//    private StCSyncStockStrategyMapper mapper;
//    @Autowired
//    private StCSyncStockStrategyChannelMapper itemMapper;
//    @Reference(group = "sg", version = "1.0")
//    private SgChannelStoreChangeCmd sgChannelStoreChangeCmd;
//    @Autowired
//    private RedisOpsUtil redisUtil;
//
//    @Override
//    @StOperationLog(mainTableName = "ST_C_SYNC_STOCK_STRATEGY", itemsTableName = "ST_C_SYNC_STOCK_STRATEGY_CHANNEL", operationType = "DEL")
//    public ValueHolder execute(QuerySession querySession) throws NDSException {
//        DefaultWebEvent event = querySession.getEvent();
//        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(
//                event.getParameterValue("param"), "yyyy-MM-dd HH:mm:ss"), Feature.OrderedField);
//        log.info(LogUtil.format("SyncStockStrategyDelService:{}", param);
//        String isDel = param.getString("isdelmtable");
//        Long objid = param.getLong("objid");
//
//        //判断主表是否存在
//        StCSyncStockStrategyDO syncStockStrategy = mapper.selectById(objid);
//        if (syncStockStrategy == null) {
//            return ValueHolderUtils.getFailValueHolder("当前记录已不存在!");
//        } else {
//            //清除rediskey
//            String redisKey = RedisConstant.bulidLockStCSyncStockStrategyItemKey(syncStockStrategy.getCpCShopId());
//            if(redisUtil.objRedisTemplate.hasKey(redisKey)){
//                redisUtil.objRedisTemplate.delete(redisKey);
//            }
//            RedisCacheUtil.delete(syncStockStrategy.getCpCShopId(), RedisConstant.SHOP_SYNC_STOCK_STRATEGY);
//            RedisCacheUtil.deleteAll(RedisConstant.SHOP_SYNC_STOCK_STRATEGY_ALL);
//        }
//        JSONObject tabitem = param.getJSONObject("tabitem");
//        JSONArray errorArray = new JSONArray();
//        //判断是删除主表还是明细表单独删除
//        if (StConstant.FALSE_STR.equals(isDel)) {
//            //单独删除明细
//            JSONArray itemArray = tabitem.getJSONArray(StConstant.TAB_ST_C_SYNC_STOCK_STRATEGY_CHANNEL);
//            if (itemArray.size() > 0) {
//                delItemList(querySession, itemArray, errorArray, syncStockStrategy);
//            }
//        } else {
//            //删除主表
//            delMain(objid, errorArray);
//        }
//        return StBeanUtils.getExcuteValueHolder(errorArray);
//    }
//
//    public ValueHolder deleteSyncStockStrategyRedis(Long channelId){
//        ValueHolder vh = new ValueHolder();
//        vh.put("code",ResultCode.SUCCESS);
//        vh.put("message","删除成功");
//        try {
//            List<String> keys = new ArrayList<>();
//            List<Long> shopIds = mapper.selectShopIdsByChannelId(channelId);
//            for(Long shopId : shopIds){
//                keys.add(RedisConstant.bulidLockStCSyncStockStrategyItemKey(shopId));
//            }
//            redisUtil.objRedisTemplate.delete(keys);
//            RedisCacheUtil.deleteAll(RedisConstant.SHOP_SYNC_STOCK_STRATEGY_ALL);
//        } catch (Exception e) {
//            log.error(LogUtil.format("根据渠道ID={}删除同步库存策略缓存异常={}", channelId, e);
//            vh.put("code",ResultCode.FAIL);
//            vh.put("message",e.getMessage());
//        }
//        return vh;
//    }
//
//    /**
//     * <AUTHOR>
//     * @Description 删除店铺同步库存策略子表数据
//     * @Date 20:33 2020/6/11
//     * @param querySession
//     * @param itemArray
//     * @param errorArray
//     * @param syncStockStrategy
//     * @return void
//     **/
//    public void delItemList(QuerySession querySession, JSONArray itemArray, JSONArray errorArray, StCSyncStockStrategyDO syncStockStrategy) {
//        Map<Long,String> beforeDelObjMap = new HashMap<>();
//        for (int i = 0; i < itemArray.size();i++) {
//            Long itemid = itemArray.getLong(i);
//            StCSyncStockStrategyChannelDO delItem = itemMapper.selectById(itemid);
//            List<Long> cpStoreIds = itemMapper.listCpCStoreId(itemid);
//            if (delItem != null) {
//                String delJsonStr = JSON.toJSONString(delItem);
//                beforeDelObjMap.put(itemid,delJsonStr);
//                for (Long cpStoreId : cpStoreIds) {
//                    ValueHolderV14 v14 = delItem(syncStockStrategy.getCpCShopId(), delItem.getCpCOrgChannelId(), cpStoreId);
//                    if (!v14.isOK()) {
//                        errorArray.add(StBeanUtils.getJsonObjectInfo(itemid, v14.getMessage()));
//                    }
//                }
//            }
//            itemMapper.deleteById(itemid);
//        }
//        if(log.isDebugEnabled()){
//            log.debug(LogUtil.format("打印beforeDelObjMap："+beforeDelObjMap);
//        }
//        //将原始数据放入QuerySession中供日志使用
//        querySession.setAttribute("beforeDelObjMap",beforeDelObjMap);
//    }
//
//    /**
//     * <AUTHOR>
//     * @Description //TODO
//     * @Date 20:33 2020/6/11
//     * @param cpCShopId 店铺id
//     * @param cpCChannelId 渠道仓id
//     * @param cpCStoreId 逻辑仓id
//     * @return com.jackrain.nea.sys.domain.ValueHolderV14
//     **/
//    @Transactional(rollbackFor = Exception.class, value = "drdsFlexibleTransaction", propagation = Propagation.SUPPORTS)
//    public ValueHolderV14 delItem(Long cpCShopId, Long cpCChannelId, Long cpCStoreId) {
//        ValueHolderV14 v = new ValueHolderV14();
//        try {
//            // 删除渠道逻辑仓关系表
//            SgChannelStoreChangeRequest storeChange = new SgChannelStoreChangeRequest();
//            storeChange.setCpCShopId(cpCShopId);
//            storeChange.setCpCChannelId(cpCChannelId);
//            storeChange.setCpCStoreId(cpCStoreId);
//            log.info(LogUtil.format("SyncStockStrategyDelService,RPC参数_storeChange:{}", storeChange);
//            ValueHolderV14 v14 = sgChannelStoreChangeCmd.deleteSbGChannelStore(storeChange);
//            if (v14.getCode() == ResultCode.FAIL) {
//                log.error(LogUtil.format("调用删除渠道逻辑仓关系RPC接口[sgChannelStoreChangeCmd.deleteSbGChannelStore]失败:" + v14.getMessage());
//                throw new NDSException("调用删除渠道逻辑仓关系RPC接口失败！");
//            }
//        } catch (Exception e) {
//            throw new NDSException("调用删除渠道逻辑仓关系RPC接口失败");
//        }
//        v.setCode(ResultCode.SUCCESS);
//        v.setMessage("成功!");
//        return v;
//
//    }
//
//    public void delMain(Long mainId, JSONArray errorArray) {
//        int deleteCount = mapper.deleteById(mainId);
//        if (deleteCount <= 0) {
//            errorArray.add(StBeanUtils.getJsonObjectInfo(mainId, "店铺同步库存策略已不存在"));
//        }
//    }
//
//    public static String bulidLockStCSyncStockStrategyItemKey(Long shopId) {
//        return "st:StCSyncStockStrategyItem:byShopId:" + shopId;
//    }
//
//}
