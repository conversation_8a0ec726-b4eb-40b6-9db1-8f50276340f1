package com.jackrain.nea.st.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.st.common.StConstant;
import com.jackrain.nea.st.mapper.StCOperationCostItemMapper;
import com.jackrain.nea.st.mapper.StCOperationCostMapper;
import com.jackrain.nea.st.model.table.StCOperationcostDO;
import com.jackrain.nea.st.model.table.StCOperationcostItemDO;
import com.jackrain.nea.st.utils.StBeanUtils;
import com.jackrain.nea.st.utils.ValueHolderUtils;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * @Descroption 操作费方案-删除逻辑
 * <AUTHOR>
 * @Date 2019/3/9
 */
@Component
@Slf4j
@Transactional
public class OperationCostDelService extends CommandAdapter {
    @Autowired
    private StCOperationCostMapper stCOperationcostMapper;
    @Autowired
    private StCOperationCostItemMapper stCOperationcostItemMapper;

    @Override
    public ValueHolder execute(QuerySession querySession) throws NDSException {
        DefaultWebEvent event = querySession.getEvent();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(
                event.getParameterValue("param"), "yyyy-MM-dd HH:mm:ss"),
                Feature.OrderedField);
        boolean delMainFlag = param.getBoolean("isdelmtable");//是否删除主表
        Long id = param.getLong("objid");
        if (id != null && id > 0) {
            ValueHolder valueHolder = new ValueHolder();
            StCOperationcostDO stCOperationcostDO = stCOperationcostMapper.selectById(id);
            if (!checkOperationCostStatus(stCOperationcostDO, valueHolder)) {
                return valueHolder;
            }
            if (delMainFlag) {
                List<StCOperationcostItemDO> stCOperationcostDOList = stCOperationcostItemMapper.sselectOperationcostById(id);
                if (!stCOperationcostDOList.isEmpty() && stCOperationcostDOList.size() > 0) {
                    return ValueHolderUtils.getFailValueHolder("操作费方案存在明细，不允许删除！");
                }
                if ((stCOperationcostMapper.deleteById(stCOperationcostDO)) > 0) {
                    return ValueHolderUtils.getFailValueHolder("删除主表成功！");
                }
            } else {
                //单独删除明细
                JSONObject tabitem = param.getJSONObject("tabitem");
                JSONArray errorArray = new JSONArray();
                JSONArray itemArray = tabitem.getJSONArray(StConstant.TAB_ST_C_OPERATIONCOST_ITEM);
                if (itemArray == null || itemArray.size() <= 0) {
                    return ValueHolderUtils.getFailValueHolder("当前明细记录不存在！");
                }
                deleteOperationCostItemByOperationCostID(itemArray, errorArray);
                updateOperationCostDate(querySession, id);
                return StBeanUtils.getExcuteValueHolder(errorArray);
            }
        }
        throw new NDSException("当前记录已不存在！");
    }

    private void deleteOperationCostItemByOperationCostID(JSONArray itemArray, JSONArray errorArray) {
        if (itemArray.size() > 0) {
            for (int i = 0; i < itemArray.size(); i++) {
                Long itemid = itemArray.getLong(i);
                if ((stCOperationcostItemMapper.deleteById(itemid)) <= 0) {
                    errorArray.add(StBeanUtils.getJsonObjectInfo(itemid, "操作费方案-当前明细记录已不存在！"));
                }
            }
        }
    }

    private void updateOperationCostDate(QuerySession session, Long id) {
        StCOperationcostDO stCOperationcostDO = new StCOperationcostDO();
        stCOperationcostDO.setId(id);
        StBeanUtils.makeModifierField(stCOperationcostDO, session.getUser());//修改信息
        stCOperationcostDO.setModifierename(session.getUser().getEname());//修改人账号
        if ((stCOperationcostMapper.updateById(stCOperationcostDO)) <= 0) {
            log.error(LogUtil.format("OperationcostDelService.updateOperationcostDate.Error： 删除明细，主表修改字段信息更新出错id=",
                    id));
        }
    }

    private boolean checkOperationCostStatus(StCOperationcostDO stCOperationcostDO, ValueHolder valueHolder) {
        if (stCOperationcostDO == null) {
            valueHolder.put("code", -1);
            valueHolder.put("message", "当前记录不存在！");
            return false;
        }
        //不是未审核，不允许删除
        if (stCOperationcostDO.getBillStatus() == null
                || !StConstant.CON_BILL_STATUS_01.equals(stCOperationcostDO.getBillStatus())) {
            valueHolder.put("code", -1);
            valueHolder.put("message", "当前记录不是未审核，不允许删除！");
            return false;
        }
        return true;
    }
}
