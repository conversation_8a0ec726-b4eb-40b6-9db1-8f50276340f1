package com.jackrain.nea.st.services;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.cpext.model.table.CpLogistics;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.st.common.StConstant;
import com.jackrain.nea.st.mapper.StCExpressAreaItemMapper;
import com.jackrain.nea.st.mapper.StCExpressAreaMapper;
import com.jackrain.nea.st.model.request.ExpressAreaRequest;
import com.jackrain.nea.st.model.table.StCExpressAreaDO;
import com.jackrain.nea.st.model.table.StCExpressAreaItemDO;
import com.jackrain.nea.st.rpc.RpcCpService;
import com.jackrain.nea.st.utils.JsonUtils;
import com.jackrain.nea.st.utils.StBeanUtils;
import com.jackrain.nea.st.utils.StStringUtils;
import com.jackrain.nea.st.utils.ValueHolderUtils;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.Validator;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Descroption 物流区域设置新增保存
 * <AUTHOR>
 * @Date 2019/3/13 14:55
 */
@Component
@Slf4j
@Transactional
public class ExpressAreaSaveService extends CommandAdapter {
    @Autowired
    private StCExpressAreaMapper mapper;
    @Autowired
    private StCExpressAreaItemMapper itemMapper;
    @Autowired
    private RpcCpService rpcCpService;

    public ValueHolder execute(QuerySession session) {
        //1.获取传入参数
        DefaultWebEvent event = session.getEvent();
        List<String> nullKeyList = new ArrayList<>();
        JSONObject param = JsonUtils.filterNull((JSONObject) event.getParameterValue("param"), nullKeyList);;

        Long id = param.getLong("objid");
        JSONObject fixColumn = param.getJSONObject("fixcolumn");
        //2.传入数据校验
        //2019-4-15 ：修改：此处禅城bug：导致编辑明细保存不成功
//        String mainFixColumn = fixColumn.getString(StConstant.TAB_ST_C_EXPRESS_AREA);
//        if (StringUtils.isEmpty(mainFixColumn)) {
//            return ValueHolderUtils.getFailValueHolder("传入数据异常！");
//        }
        //3.json转换成对象
        ExpressAreaRequest expressAreaRequest = JsonUtils.jsonParseClass(fixColumn, ExpressAreaRequest.class);

        //4.新增或变更
        if (fixColumn != null && id != null) {
            if (id != -1) {
                return updateExpressArea(session, id, expressAreaRequest, nullKeyList);
            } else {
                return addExpressArea(session, expressAreaRequest);
            }
        }
        throw new NDSException("传入数据异常！");
    }

    private ValueHolder addExpressArea(QuerySession session, ExpressAreaRequest expressAreaRequest) {
        StCExpressAreaDO expressArea = expressAreaRequest.getStCExpressArea();
        //1.主表数据业务更新校验
        checkExpressArea(expressArea);
        //主表增加校验：新增时不能新增相同的物流公司;返回值如果为true：代表存在已经保存的物流公司
        if(checkExpressLogisticId(expressArea)){
            return ValueHolderUtils.getFailValueHolder("该物流公司已存在,请选择其他物流公司！");
        }
        //2.主表id及创建修改时间赋值
        expressArea.setId(ModelUtil.getSequence(StConstant.TAB_ST_C_EXPRESS_AREA));
        // 取得物流公司信息
        if (expressArea.getCpCLogisticsId() != null) {
            CpLogistics cpLogistics = rpcCpService.queryLogisticsById(expressArea.getCpCLogisticsId());
            if (cpLogistics != null) {
                expressArea.setCpCLogisticsEcode(cpLogistics.getEcode());
                expressArea.setCpCLogisticsEname(cpLogistics.getEname());
            }
        }
        //主表创建信息更新
        StBeanUtils.makeCreateField(expressArea, session.getUser());
        //主表最后信息修改
        StBeanUtils.makeModifierField(expressArea, session.getUser());
        //3.主表数据保存
        int insertResult = mapper.insert(expressArea);
        if (insertResult <= 0) {
            return ValueHolderUtils.getFailValueHolder("保存失败");
        }
        //4.明细数据遍历保存
        List<StCExpressAreaItemDO> itemList = expressAreaRequest.getStCExpressAreaItemList();
        if (itemList != null && itemList.size() > 0) {
            for (StCExpressAreaItemDO stCExpressAreaItem : itemList) {

                stCExpressAreaItem.setStCExpressAreaId(expressAreaRequest.getStCExpressArea().getId());
                stCExpressAreaItem.setId(ModelUtil.getSequence(StConstant.TAB_ST_C_EXPRESS_AREA_ITEM));
                StBeanUtils.makeCreateField(stCExpressAreaItem, session.getUser());
                if (itemMapper.insert(stCExpressAreaItem) < 1) {
                    return ValueHolderUtils.getFailValueHolder("物流区域明细插入失败");
                }
            }
        }

        return ValueHolderUtils.getSuccessValueHolder( expressArea.getId(), StConstant.TAB_ST_C_EXPRESS_AREA);
    }

    private ValueHolder updateExpressArea(QuerySession session, Long id, ExpressAreaRequest expressAreaRequest, List<String> nullKeyList) {
        //1.更新前校验
        StCExpressAreaDO existsEwaybill = mapper.selectById(id);
        if (existsEwaybill == null) {
            return ValueHolderUtils.getFailValueHolder("当前记录已不存在！");
        }

        //主表数据更新前提条件：页面有需要修改的情况，若是没有主表参数数据修改传入，则不执行更新操作
        StCExpressAreaDO expressArea = expressAreaRequest.getStCExpressArea();
        if(expressArea != null ){
            //主表数据业务更新校验
            checkExpressArea(expressArea);

            expressArea.setId(id);
            if(checkExpressLogisticId(expressArea)){
                return ValueHolderUtils.getFailValueHolder("该物流公司已存在,请选择其他物流公司！");
            }
            // 取得物流公司信息
            if (expressArea.getCpCLogisticsId() != null) {
                CpLogistics cpLogistics = rpcCpService.queryLogisticsById(expressArea.getCpCLogisticsId());
                if (cpLogistics != null) {
                    expressArea.setCpCLogisticsEcode(cpLogistics.getEcode());
                    expressArea.setCpCLogisticsEname(cpLogistics.getEname());
                }
            }
            //2.主表最后修改信息字段变更
            StBeanUtils.makeModifierField(expressArea, session.getUser());
            //3.更新主表信息
            if (mapper.updateById(expressArea) <= 0) {
                return ValueHolderUtils.getFailValueHolder("物流区域设置策略主表更新失败");
            }
        }


        //4.明细数据变更
        //新增子表信息
       // List<StCExpressAreaItemDO> addItemList = expressAreaRequest.getStCExpressAreaItemList().stream().filter(x -> x.getId() == -1).collect(Collectors.toList());
        //修改子表信息
       // List<StCExpressAreaItemDO> updateItemList = expressAreaRequest.getStCExpressAreaItemList().stream().filter(x -> x.getId() > 0).collect(Collectors.toList());

        // 修改更新方法：修改bug：编辑保存报错问题
        // 修改日期：2019-4-15
        List<StCExpressAreaItemDO> expressAreaItemDOList = expressAreaRequest.getStCExpressAreaItemList();
        if (expressAreaItemDOList != null && expressAreaItemDOList.size() > 0) {
            for(StCExpressAreaItemDO stCExpressAreaItemDO:expressAreaItemDOList){
                Long areaItemDOId = stCExpressAreaItemDO.getId();//判断：如果有传id进来则为编辑，否则为新增
                if(areaItemDOId != null && areaItemDOId > 0){
                    StBeanUtils.makeModifierField(stCExpressAreaItemDO, session.getUser());
                    itemMapper.updateById(stCExpressAreaItemDO);

                    JSONObject jsonObject = new JSONObject();
                    jsonObject.put("ID", areaItemDOId);
                    if (stCExpressAreaItemDO.getId() == null && nullKeyList.contains("CP_C_REGION_AREA_ID")) {
                        jsonObject.put("CP_C_REGION_AREA_ID", null);
                        itemMapper.updateAtrributes(jsonObject);
                    }
                }else{
                    // 省市区乡一致的数据进行覆盖
                    List<StCExpressAreaItemDO> stCExpressAreaItemDOList = itemMapper.selectByArea(id, stCExpressAreaItemDO.getCpCRegionProvinceId(), stCExpressAreaItemDO.getCpCRegionCityId());
                    if (!CollectionUtils.isEmpty(stCExpressAreaItemDOList)) {
                        stCExpressAreaItemDOList = stCExpressAreaItemDOList.stream().filter(ea -> StStringUtils.equalsWithNull(ea.getCpCRegionAreaId(), stCExpressAreaItemDO.getCpCRegionAreaId())
                                && StStringUtils.equalsWithNull(ea.getCpCRegionCountryEname(), stCExpressAreaItemDO.getCpCRegionCountryEname())).collect(Collectors.toList());
                    }

                    if (CollectionUtils.isEmpty(stCExpressAreaItemDOList)) {
                        stCExpressAreaItemDO.setId(ModelUtil.getSequence(StConstant.TAB_ST_C_EXPRESS_AREA_ITEM));
                        stCExpressAreaItemDO.setStCExpressAreaId(id);
                        StBeanUtils.makeCreateField(stCExpressAreaItemDO, session.getUser());
                        if (itemMapper.insert(stCExpressAreaItemDO) < 1) {
                            return ValueHolderUtils.getFailValueHolder("物流区域明细插入失败");
                        }
                    } else {
                        stCExpressAreaItemDO.setId(stCExpressAreaItemDOList.get(0).getId());
                        StBeanUtils.makeModifierField(stCExpressAreaItemDO, session.getUser());
                        if (itemMapper.updateById(stCExpressAreaItemDO) < 1) {
                            return ValueHolderUtils.getFailValueHolder("物流区域明细插入失败");
                        }
                    }
                }
            }
        }

        return ValueHolderUtils.getSuccessValueHolder(id, StConstant.TAB_ST_C_EXPRESS_AREA);
    }



    private void checkExpressArea(StCExpressAreaDO expressArea) {
        String errMessage = "";
        if (!errMessage.isEmpty()) {
            throw new NDSException(errMessage);
        }
    }

    //校验新增的物流公司是否已经存在
    private boolean  checkExpressLogisticId (StCExpressAreaDO expressArea){
        Long logisticsId = expressArea.getCpCLogisticsId();//物流公司id
        //根据物流公司id查询数据库，若能查询到数据，代表已存在申请的记录，则不能重复申请同样的物流公司
        List<StCExpressAreaDO> expressAreaItemDOByLogisIdList = mapper.selectStCExpressAreaDO(logisticsId);
        if(expressAreaItemDOByLogisIdList !=null){
            if (expressArea.getId() != null) {
                expressAreaItemDOByLogisIdList = expressAreaItemDOByLogisIdList.stream().filter(ea -> !ea.getId().equals(expressArea.getId())).collect(Collectors.toList());
            }
            if (expressAreaItemDOByLogisIdList.size()>0) {
                return true;
            }
        }
        return false;
    }

}
