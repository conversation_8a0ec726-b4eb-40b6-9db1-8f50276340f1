package com.jackrain.nea.st.services;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.cpext.model.table.CpCPhyWarehouse;
import com.jackrain.nea.cpext.model.table.CpLogistics;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.st.common.StConstant;
import com.jackrain.nea.st.mapper.*;
import com.jackrain.nea.st.model.request.PickUpTimeRequest;
import com.jackrain.nea.st.model.table.*;
import com.jackrain.nea.st.rpc.RpcCpService;
import com.jackrain.nea.st.utils.JsonUtils;
import com.jackrain.nea.st.utils.StBeanUtils;
import com.jackrain.nea.st.utils.ValueHolderUtils;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2020/5/12 13:49
 */
@Component
@Slf4j
@Transactional
public class PickUpTimeSaveService extends CommandAdapter {
    @Autowired
    private StCPickUpTimeMapper pickUpTimeMapper;
    @Autowired
    private StCPickUpTimeItemMapper pickUpTimeItemMapper;
    @Autowired
    private RpcCpService rpcCpService;

    @Override
    public ValueHolder execute(QuerySession session) {
        DefaultWebEvent event = session.getEvent();
        JSONObject param = JsonUtils.filterNull((JSONObject) event.getParameterValue("param"), null);
        if (param != null) {
            Long id = param.getLong("objid");
            JSONObject fixColumn = param.getJSONObject("fixcolumn");
            PickUpTimeRequest pickUpTimeRequest = JsonUtils.jsonParseClass(fixColumn, PickUpTimeRequest.class);
            if (fixColumn != null && id != null) {
                if (id != -1) {
                    return updatePickUpTime(session, id, pickUpTimeRequest);
                } else {
                    return insertPickUpTime(session, pickUpTimeRequest);
                }
            }
        }
        throw new NDSException("当前记录已不存在！");
    }

    /**
     * 插入
     *
     * @param session
     * @param pickUpTimeRequest
     * @return com.jackrain.nea.util.ValueHolder
     */
    private ValueHolder insertPickUpTime(QuerySession session, PickUpTimeRequest pickUpTimeRequest) {
        long id = 0;
        StCPickUpTimeDO pickUpTime = pickUpTimeRequest.getStCPickUpTime();
        if (pickUpTime != null) {
            //1.1 判断名称是否已存在
            checkPickUpByFilter(-1L, pickUpTime, "insert");
            //1.2 插入
            id = ModelUtil.getSequence(StConstant.TAB_ST_C_PICK_UP_TIME);
            pickUpTime.setId(id);
            StBeanUtils.makeCreateField(pickUpTime, session.getUser());
            int insertResult = pickUpTimeMapper.insert(pickUpTime);
            if (insertResult < 0) {
                return ValueHolderUtils.getFailValueHolder("保存失败！");
            }
        }
        savePickUpItem(session, id, pickUpTimeRequest);
        return ValueHolderUtils.getSuccessValueHolder(id, StConstant.TAB_ST_C_PICK_UP_TIME, "");
    }

    /**
     * 更新
     *
     * @param session
     * @param id
     * @return com.jackrain.nea.util.ValueHolder
     */
    private ValueHolder updatePickUpTime(QuerySession session, Long id, PickUpTimeRequest pickUpTimeRequest) {
        StCPickUpTimeDO pickUpTime = pickUpTimeRequest.getStCPickUpTime();
        if (pickUpTime != null) {
            checkPickUpByFilter(id, pickUpTime, "update");
            pickUpTime.setId(id);
            StBeanUtils.makeModifierField(pickUpTime, session.getUser());
            if (pickUpTimeMapper.updateById(pickUpTime) < 0) {
                return ValueHolderUtils.getFailValueHolder("更新失败！");
            }
        }
        savePickUpItem(session, id, pickUpTimeRequest);
        return ValueHolderUtils.getSuccessValueHolder(id, StConstant.TAB_ST_C_PICK_UP_TIME, "");
    }

    private void savePickUpItem(QuerySession session, Long id, PickUpTimeRequest pickUpTimeRequest) {
        //新增子表信息
        List<StCPickUpTimeItemDO> itemList = pickUpTimeRequest.getStCPickUpTimeItem();
        if (!CollectionUtils.isEmpty(itemList)) {
            for (StCPickUpTimeItemDO item : itemList) {
                if (!checkWarehouseInfo(id, item.getId(), item.getCpCPhyWarehouseId())) {
                    if (item.getCpCPhyWarehouseId() != null) {
                        CpCPhyWarehouse phyWarehouse = rpcCpService.getCpCPhyWahouseDoById(item.getCpCPhyWarehouseId());
                        if (phyWarehouse != null) {
                            item.setCpCPhyWarehouseEcode(phyWarehouse.getEcode());
                            item.setCpCPhyWarehouseEname(phyWarehouse.getEname());
                        }
                    }
                    if (item.getId() > 0) {
                        StBeanUtils.makeModifierField(item, session.getUser());
                        int update = pickUpTimeItemMapper.updateById(item);
                        if (update < 0) {
                            throw new NDSException("明细更新失败！");
                        }
                    } else {
                        item.setId(ModelUtil.getSequence(StConstant.TAB_ST_C_PICK_UP_TIME_ITEM));//主键
                        item.setStCPickUpTimeId(id);//外键
                        StBeanUtils.makeCreateField(item, session.getUser());
                        int insert = pickUpTimeItemMapper.insert(item);
                        if (insert < 0) {
                            throw new NDSException("明细保存失败！");
                        }
                    }
                } else {
                    throw new NDSException("当前仓库已在该物流公司中存在！");
                }
            }
        }
    }

    /**
     * @param id
     * @param pickUpTime
     * @param action
     */
    private void checkPickUpByFilter(Long id, StCPickUpTimeDO pickUpTime, String action) {
        if (pickUpTime.getCpCLogisticsId() != null) {
//            List<StCPickUpTimeDO> pickUpTimeList = pickUpTimeMapper.selectList(new QueryWrapper<StCPickUpTimeDO>()
//                    .lambda().eq(StCPickUpTimeDO::getCpCLogisticsId, pickUpTime.getCpCLogisticsId())
//                    .eq(StCPickUpTimeDO::getIsactive, StConstant.ISACTIVE_Y)
//                    .ne(StCPickUpTimeDO::getId, id));
//            if (!CollectionUtils.isEmpty(pickUpTimeList)) {
//                throw new NDSException("快递公司名称已存在！");
//            } else {
                CpLogistics cpLogistics = rpcCpService.queryLogisticsById(pickUpTime.getCpCLogisticsId());
                if (cpLogistics != null) {
                    pickUpTime.setCpCLogisticsEcode(cpLogistics.getEcode());
                    pickUpTime.setCpCLogisticsEname(cpLogistics.getEname());
                }
//            }
        }
    }

    private boolean checkWarehouseInfo(long id, Long itemId, Long cpCPhyWarehouseId){
        StCPickUpTimeDO pickUpTime = pickUpTimeMapper.selectById(id);
        List<StCPickUpTimeDO> pickUpTimeItemList = pickUpTimeMapper.selectCheckByWarehouseLogistics(pickUpTime.getCpCLogisticsId(),
                                                                                                    cpCPhyWarehouseId);
        for (StCPickUpTimeDO pickUp : pickUpTimeItemList) {
            if (itemId == null ||!pickUp.getId().equals(itemId)) {
                return true;
            }
        }
        return false;
    }
}
