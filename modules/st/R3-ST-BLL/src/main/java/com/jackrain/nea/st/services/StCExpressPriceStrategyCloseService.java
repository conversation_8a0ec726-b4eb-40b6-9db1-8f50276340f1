package com.jackrain.nea.st.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.st.annotation.StOperationLog;
import com.jackrain.nea.st.mapper.StCExpressPriceStrategyItemMapper;
import com.jackrain.nea.st.mapper.StCExpressPriceStrategyMapper;
import com.jackrain.nea.st.model.common.StRedisConstant;
import com.jackrain.nea.st.model.enums.StCExpressPriceStrategyEnum;
import com.jackrain.nea.st.model.table.StCExpressPriceStrategyDO;
import com.jackrain.nea.st.utils.RedisCacheUtil;
import com.jackrain.nea.st.utils.StBeanUtils;
import com.jackrain.nea.st.utils.ValueHolderUtils;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2022/6/12 14:24
 * @Description 快递报价设置结案策略
 * @Version 1.0
 */
@Component
@Slf4j
public class StCExpressPriceStrategyCloseService  extends CommandAdapter {

    @Autowired
    private StCExpressPriceStrategyMapper stCExpressPriceStrategyMapper;

    @Autowired
    private StCExpressPriceStrategyItemMapper stCExpressPriceStrategyItemMapper;

    @StOperationLog(operationType = "FINISH", mainTableName = "ST_C_EXPRESS_PRICE_STRATEGY", itemsTableName = "ST_C_EXPRESS_PRICE_STRATEGY_ITEM")
    @Override
    public ValueHolder execute(QuerySession session){
        DefaultWebEvent event = session.getEvent();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(
                event.getParameterValue("param"), "yyyy-MM-dd HH:mm:ss"), Feature.OrderedField);
        if (param == null) {
            throw new NDSException("参数为空！");
        }
        JSONArray voidArray = StBeanUtils.makeUnAuditJsonArray(param);
        if (voidArray.size() == 1) {
            checkAction(voidArray.getLong(0),session);
        }else {
            int success = 0;
            int fail = 0;
            for (int i = 0; i < voidArray.size(); i++) {
                Long id = voidArray.getLong(i);
                try {
                    checkAction(id,session);
                    success++;
                } catch (Exception e) {
                    fail++;
                }
            }
            if (success != voidArray.size()) {
                return ValueHolderUtils.getFailValueHolder("快递报价设置结案成功" + success + "条,失败" + fail + "条");
            }
        }
        return ValueHolderUtils.getSuccessValueHolder("结案成功");
    }


    private void checkAction(Long id, QuerySession session) {
        User user = session.getUser();
        StCExpressPriceStrategyDO stCExpressPriceStrategyDO = stCExpressPriceStrategyMapper.selectById(id);
        //判断主表是否存在
        if (stCExpressPriceStrategyDO == null){
            throw new NDSException("当前记录不存在！");
        }
        if (!StCExpressPriceStrategyEnum.SUBMITTED.getKey().equals(stCExpressPriceStrategyDO.getStatus())){
            throw new NDSException("当前单据状态，不允许结案！");
        }
        if (!StCExpressPriceStrategyEnum.UN_CLOSED.getKey().equals(stCExpressPriceStrategyDO.getCloseStatus())){
            throw new NDSException("当前单据状态，不允许结案！");
        }
        StCExpressPriceStrategyDO newDO = new StCExpressPriceStrategyDO();
        newDO.setId(stCExpressPriceStrategyDO.getId());
        newDO.setPriceHomeDelivery(stCExpressPriceStrategyDO.getPriceHomeDelivery());
        newDO.setRemark(stCExpressPriceStrategyDO.getRemark());
        newDO.setCloseStatus(StCExpressPriceStrategyEnum.CLOSED.getKey());
        newDO.setCloserid(Long.valueOf(user.getId()));
        newDO.setCloseTime(new Date());
        //不可用
        newDO.setIsactive("N");
        //修改人
        StBeanUtils.makeModifierField(newDO,user);
        int update = stCExpressPriceStrategyMapper.updateById(newDO);
        if (update <= 0) {
            throw new NDSException("结案失败！");
        }

        // 清除快递报价关系缓存
        String redisKey = StRedisConstant.buildExpressPriceRelationKey(stCExpressPriceStrategyDO.getCpCPhyWarehouseId(), stCExpressPriceStrategyDO.getCpCLogisticsId());
        RedisCacheUtil.deleteAll(redisKey);
        log.info("清除快递报价关系缓存成功，key={}", redisKey);

        // 清除根据ID查询的分布式缓存
        String idCacheKey = StRedisConstant.buildExpressPriceStrategyRelationByIdKey(stCExpressPriceStrategyDO.getId());
        RedisCacheUtil.deleteAll(idCacheKey);
        log.info("清除快递报价策略ID缓存成功，key={}", idCacheKey);
    }

}
