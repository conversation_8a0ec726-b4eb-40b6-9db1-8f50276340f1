package com.jackrain.nea.st.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.google.common.base.Throwables;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.st.common.StConstant;
import com.jackrain.nea.st.mapper.StCPreSaleMapper;
import com.jackrain.nea.st.model.table.StCPreArrivalDO;
import com.jackrain.nea.st.model.table.StCPreSaleDO;
import com.jackrain.nea.st.utils.StBeanUtils;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.face.impl.UserImpl;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description 预售解析策略结案业务类
 * @Date 2020/06/10
 * @Copyright 2019-2020
 */
@Component
@Slf4j
@Transactional
public class StCPreSaleCloseService extends CommandAdapter {

    @Autowired
    private StCPreSaleMapper stCPreSaleMapper;

    /**
     * @return com.jackrain.nea.util.ValueHolder
     * <AUTHOR>
     * @Description 预售解析策略结案
     * @Date  2020/06/10
     * @Param [session]
     **/
    public ValueHolder execute(QuerySession session) throws NDSException{
        DefaultWebEvent event = session.getEvent();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(
                event.getParameterValue("param"), "yyyy-MM-dd HH:mm:ss", SerializerFeature.WriteMapNullValue),
                Feature.OrderedField);
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("请求JSON{}"), param.toString());
        }
        if (param == null) {
            throw new NDSException("参数为空！");
        }

        ValueHolder resultValueHolder = new ValueHolder();
        HashMap<Long, Object> errMap = new HashMap<Long, Object>();
        // 生成结案Json数组
        JSONArray closeArray = StBeanUtils.makeAuditJsonArray(param);
        for (int i = 0; i < closeArray.size(); i++) {
            Long id = Long.valueOf(closeArray.get(i).toString());
            // 结案验证
            closeCheck(id);
            StCPreSaleDO stCPreSaleDO = new StCPreSaleDO();
            stCPreSaleDO.setId(id);
            stCPreSaleDO.setPreSaleStatus(StConstant.PRE_SALE_STATUS_04);
            StBeanUtils.makeModifierField(stCPreSaleDO,session.getUser());
            //更新单据状态
            int count = stCPreSaleMapper.updateById(stCPreSaleDO);
            if (count < 0) {
                throw new NDSException("结案失败");
            }
        }
        resultValueHolder = StBeanUtils.getExcuteValueHolder(closeArray.size(),errMap);
        return resultValueHolder;
    }

    /**
     * <AUTHOR>
     * @Description 预售解析策略结案
     * @Date  2020/06/10
     **/
    public void closeStCPreSale() throws NDSException{

        Map<String, Object> params = new HashMap<>();
        params.put("pre_sale_status",StConstant.PRE_SALE_STATUS_02);
        List<StCPreSaleDO> list = stCPreSaleMapper.selectByMap(params);
        if(CollectionUtils.isEmpty(list)){
            return;
        }
        for(StCPreSaleDO temp : list){
            // 一旦当前时间大于等于结束日期+7天,自动结案
            LocalDateTime endTime = temp.getEndTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime().plusDays(7L);
            LocalDateTime now = LocalDateTime.now();
            if(now.isBefore(endTime)){
                continue;
            }
            // 执行自动结案
            try {
                closeCheck(temp.getId());
                StCPreSaleDO stCPreSaleDO = new StCPreSaleDO();
                stCPreSaleDO.setId(temp.getId());
                stCPreSaleDO.setPreSaleStatus(StConstant.PRE_SALE_STATUS_04);
                StBeanUtils.makeModifierField(stCPreSaleDO,getRootUser());
                //更新单据状态
                int count = stCPreSaleMapper.updateById(stCPreSaleDO);
                if (count < 0) {
                    throw new NDSException("结案失败");
                }
            } catch (Exception e){
                log.error(LogUtil.format("预售解析策略自动结案失败,策略信息：{},异常信息：{}"),JSONObject.toJSONString(temp),
                        Throwables.getStackTraceAsString(e));
                continue;
            }

        }
    }

    /**
     * @return com.alibaba.fastjson.JSONObject
     * <AUTHOR>
     * @Description 结案验证
     * @Date 2020/06/10
     * @Param [id]
     **/
    private JSONObject closeCheck(Long id) throws NDSException {
        //记录不存在
        StCPreSaleDO stCPreSaleDO = stCPreSaleMapper.selectById(id);
        if (stCPreSaleDO == null) {
            throw new NDSException("当前记录已不存在！");
        } else {
            if (StConstant.PRE_SALE_STATUS_01.equals(stCPreSaleDO.getPreSaleStatus())) {
                throw new NDSException("方案未审核，不允许结案！");
            }else if(StConstant.PRE_SALE_STATUS_03.equals(stCPreSaleDO.getPreSaleStatus())){
                throw new NDSException("方案已作废，不允许结案！");
            }else if(StConstant.PRE_SALE_STATUS_04.equals(stCPreSaleDO.getPreSaleStatus())){
                throw new NDSException("方案已结案，不允许重复结案！");
            }
        }
        return null;
    }

    private User getRootUser() {
        UserImpl user = new UserImpl();
        user.setId(893);
        user.setName("root");
        user.setEname("root");
        return user;
    }
}
