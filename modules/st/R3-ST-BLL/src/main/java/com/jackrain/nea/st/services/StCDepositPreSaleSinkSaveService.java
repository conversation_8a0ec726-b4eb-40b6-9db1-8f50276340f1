package com.jackrain.nea.st.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.jackrain.nea.cpext.api.GeneralOrganizationCmd;
import com.jackrain.nea.cpext.model.table.CpLogistics;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.st.common.StConstant;
import com.jackrain.nea.st.mapper.StCDepositPreSaleSinkItemMapper;
import com.jackrain.nea.st.mapper.StCDepositPreSaleSinkLogisticsMapper;
import com.jackrain.nea.st.mapper.StCDepositPreSaleSinkMapper;
import com.jackrain.nea.st.model.request.StCDepositPreSaleSinkRequest;
import com.jackrain.nea.st.model.table.*;
import com.jackrain.nea.st.rpc.RpcCpService;
import com.jackrain.nea.st.utils.*;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * description:定金预售预下沉策略业务类
 * @Author:  liuwenjin
 * @Date 2021/9/23 4:17 下午
 */
@Component
@Slf4j
public class StCDepositPreSaleSinkSaveService extends CommandAdapter {
    @Autowired
    private StCDepositPreSaleSinkMapper stCDepositPreSaleSinkMapper;

    @Autowired
    private StCDepositPreSaleSinkItemMapper stCDepositPreSaleSinkItemMapper;

    @Autowired
    private StCDepositPreSaleSinkLogisticsMapper stCDepositPreSaleSinkLogisticsMapper;

    @Autowired
    RpcCpService rpcCpService;
    @Override
    public ValueHolder execute(QuerySession session) throws NDSException {
        DefaultWebEvent event = session.getEvent();
        List<String> nullKeyList = new ArrayList<>();
        JSONObject param = JsonUtils.filterNull((JSONObject) event.getParameterValue("param"), nullKeyList);
        if (param != null) {
            Long id = param.getLong("objid");
            JSONObject fixColumn = param.getJSONObject("fixcolumn");
            log.debug(LogUtil.format("fixColumn：{}"), JSON.toJSONString(fixColumn));
            log.info(LogUtil.format("StCDepositPreSaleSinkSaveService.execute querySession:{}"), session);
            StCDepositPreSaleSinkRequest stCDepositPreSaleSinkRequest = JsonUtils.jsonParseClass(fixColumn, StCDepositPreSaleSinkRequest.class);
            if (fixColumn != null && id != null) {
                if (id != -1) {
                    return this.updateStCDepositPreSaleSink(session, stCDepositPreSaleSinkRequest, id);
                } else {
                    return this.insertStCDepositPreSaleSink(session, stCDepositPreSaleSinkRequest);
                }
            }
        }
        throw new NDSException("当前记录已不存在！");
    }
    /**
     * description:新增
     * @Author:  liuwenjin
     * @Date 2021/9/23 4:23 下午
     */
    private ValueHolder insertStCDepositPreSaleSink(QuerySession session, StCDepositPreSaleSinkRequest stCDepositPreSaleSinkRequest) {

        long id = 0;
        //1.HOLD单策略表
        StCDepositPreSaleSinkDO stCDepositPreSaleSinkDO = stCDepositPreSaleSinkRequest.getStCDepositPreSaleSinkDO();
        if (stCDepositPreSaleSinkDO != null) {
            parseWerIds(stCDepositPreSaleSinkDO);
            //1.1 判断名称是否已存在
            ValueHolder check = check(-1L, stCDepositPreSaleSinkRequest, "insert");
            if (check != null) {
                return check;
            }
            //1.2 插入
            id = ModelUtil.getSequence(StConstant.TAB_ST_C_DEPOSIT_PRE_SALE_SINK);
            stCDepositPreSaleSinkDO.setId(id);
            stCDepositPreSaleSinkDO.setEstatus(StConstant.HOLD_ORDER_STATUS_01);
            StBeanUtils.makeCreateField(stCDepositPreSaleSinkDO, session.getUser());
            int insertResult = stCDepositPreSaleSinkMapper.insert(stCDepositPreSaleSinkDO);
            if (insertResult < 0) {
                return ValueHolderUtils.getFailValueHolder("保存失败！");
            }
        }
        //新增明细
        List<StCDepositPreSaleSinkItemDO> stCDepositPreSaleSinkItemDOList = stCDepositPreSaleSinkRequest.getStCDepositPreSaleSinkItemDOList();
        log.info(LogUtil.format("StCDepositPreSaleSinkSaveService.insertStCDepositPreSaleSink param " +
                "stCDepositPreSaleSinkItemDOList:{}"),stCDepositPreSaleSinkItemDOList);
        if (CollectionUtils.isNotEmpty(stCDepositPreSaleSinkItemDOList)){
            List<StCDepositPreSaleSinkItemDO> updateList = new ArrayList<>();
            List<StCDepositPreSaleSinkItemDO> insterList = new ArrayList<>();
            long DepositPreSaleSinkId = id;
            stCDepositPreSaleSinkItemDOList.forEach(i->{
                i.setStCDepositPreSaleSinkId(DepositPreSaleSinkId);
                if (checkItem(i)){
                    throw new NDSException("已存在相同明细！");
                }
                if(Objects.nonNull(i.getId()) &&i.getId()>0){
                    StBeanUtils.makeModifierField(i,session.getUser());
                    updateList.add(i);
                }else {
                    i.setId(ModelUtil.getSequence(StConstant.TAB_ST_C_DEPOSIT_PRE_SALE_SINK_ITEM));
                    StBeanUtils.makeCreateField(i,session.getUser());
                    insterList.add(i);
                }
            });
            Boolean flag = stCDepositPreSaleSinkItemSave(updateList,insterList);
            if (!flag){
                return ValueHolderUtils.getFailValueHolder("保存明细失败！");
            }
        }
        List<StCDepositPreSaleSinkLogisticsDO> stCDepositPreSaleSinkLogisticsDOList = stCDepositPreSaleSinkRequest.getStCDepositPreSaleSinkLogisticsDOList();
        log.info("StCDepositPreSaleSinkSaveService.insertStCDepositPreSaleSink param stCDepositPreSaleSinkLogisticsDOList:{}",stCDepositPreSaleSinkLogisticsDOList);
        if (CollectionUtils.isNotEmpty(stCDepositPreSaleSinkLogisticsDOList)){
            List<StCDepositPreSaleSinkLogisticsDO> updateList = new ArrayList<>();
            List<StCDepositPreSaleSinkLogisticsDO> insterList = new ArrayList<>();
            long depositPreSaleSinkId = id;
            stCDepositPreSaleSinkLogisticsDOList.forEach(i->{
                // 取得物流公司信息
                if (i.getCpCLogisticsId() != null) {
                    CpLogistics cpLogistics = rpcCpService.queryLogisticsById(i.getCpCLogisticsId());
                    if (cpLogistics != null) {
                        i.setCpCLogisticsEcode(cpLogistics.getEcode());
                        i.setCpCLogisticsEname(cpLogistics.getEname());
                    }
                }
                i.setStCDepositPreSaleSinkId(depositPreSaleSinkId);
                if (checkLogistics(i)){
                    throw new NDSException("已存在相同明细！");
                }
                if(Objects.nonNull(i.getId()) &&i.getId()>0){
                    StBeanUtils.makeModifierField(i,session.getUser());
                    updateList.add(i);
                }else {
                    i.setId(ModelUtil.getSequence(StConstant.TAB_ST_C_DEPOSIT_PRE_SALE_SINK_LOGISTICS));
                    StBeanUtils.makeCreateField(i,session.getUser());
                    insterList.add(i);
                }
            });
            Boolean flag = stCDepositPreSaleSinkLogisticsSave(updateList,insterList);
            if (!flag){
                return ValueHolderUtils.getFailValueHolder("保存明细失败！");
            }else {
                StCDepositPreSaleSinkDO updateStCDepositPreSaleSinkDO = new StCDepositPreSaleSinkDO();
                StBeanUtils.makeModifierField(updateStCDepositPreSaleSinkDO,session.getUser());
                updateStCDepositPreSaleSinkDO.setId(id);
                stCDepositPreSaleSinkMapper.updateById(updateStCDepositPreSaleSinkDO);
            }
        }
        return ValueHolderUtils.getSuccessValueHolder(id, StConstant.TAB_ST_C_DEPOSIT_PRE_SALE_SINK, "");
    }
    /**
     * description:明细保存
     * @Author:  liuwenjin
     * @Date 2021/9/23 4:47 下午
     */
    private Boolean stCDepositPreSaleSinkItemSave(List<StCDepositPreSaleSinkItemDO> updateList, List<StCDepositPreSaleSinkItemDO> insterList) {
        log.info(LogUtil.format("StCDepositPreSaleSinkSaveService.stCDepositPreSaleSinkItemSave param updateList:{}," +
                "insterList:{}"),updateList,insterList);
        int n=0;
        if (CollectionUtils.isNotEmpty(updateList)){
            for (StCDepositPreSaleSinkItemDO stCDepositPreSaleSinkItemDO: updateList) {
                n+= stCDepositPreSaleSinkItemMapper.updateById(stCDepositPreSaleSinkItemDO);
            }
        }
        if (CollectionUtils.isNotEmpty(insterList)){
            for (StCDepositPreSaleSinkItemDO stCDepositPreSaleSinkItemDO : insterList) {
                n+= stCDepositPreSaleSinkItemMapper.insert(stCDepositPreSaleSinkItemDO);
            }
        }
        if (n>0){
            return true;
        }
        return false;
    }
    /**
     * description:识别规则合识别内容只存在一个  flag  true=修改
     * @Author:  liuwenjin
     * @Date 2021/9/23 8:31 下午
     */
    private boolean checkItem(StCDepositPreSaleSinkItemDO stCDepositPreSaleSinkItemDO) {
        Long id = stCDepositPreSaleSinkItemDO.getId();
        String rulesRecognition=  stCDepositPreSaleSinkItemDO.getRulesRecognition();
        String content = stCDepositPreSaleSinkItemDO.getContent();
        if (id>0){
            StCDepositPreSaleSinkItemDO oldStCDepositPreSaleSinkItemDO =stCDepositPreSaleSinkItemMapper.selectById(stCDepositPreSaleSinkItemDO);
            if (rulesRecognition==null){
                rulesRecognition = oldStCDepositPreSaleSinkItemDO.getRulesRecognition();
            }
            if (content==null){
                content = oldStCDepositPreSaleSinkItemDO.getContent();
            }
        }
        if (stCDepositPreSaleSinkItemMapper.selectCount(new QueryWrapper<StCDepositPreSaleSinkItemDO>().lambda()
                .eq(StCDepositPreSaleSinkItemDO ::getStCDepositPreSaleSinkId,stCDepositPreSaleSinkItemDO.getStCDepositPreSaleSinkId())
                .eq(StCDepositPreSaleSinkItemDO :: getRulesRecognition,rulesRecognition)
                .eq(StCDepositPreSaleSinkItemDO :: getContent,content))>0){
            return true;
        }
        return false;
    }

    /**
     * description: 修改
     * @Author:  liuwenjin
     * @Date 2021/9/23 4:23 下午
     */
    private ValueHolder updateStCDepositPreSaleSink(QuerySession session, StCDepositPreSaleSinkRequest stCDepositPreSaleSinkRequest, Long id) {
        StCDepositPreSaleSinkDO stCDepositPreSaleSinkDO = stCDepositPreSaleSinkRequest.getStCDepositPreSaleSinkDO();
        if (stCDepositPreSaleSinkDO != null) {
            parseWerIds(stCDepositPreSaleSinkDO);
            ValueHolder holder = check(id, stCDepositPreSaleSinkRequest, "update");
            if (holder != null) {
                return holder;
            }
            stCDepositPreSaleSinkDO.setId(id);
            StBeanUtils.makeModifierField(stCDepositPreSaleSinkDO, session.getUser());
            log.debug(LogUtil.format("stCDepositPreSaleSinkDO：{}"), JSON.toJSONString(stCDepositPreSaleSinkDO));
            if (stCDepositPreSaleSinkMapper.updateById(stCDepositPreSaleSinkDO) < 0) {
                return ValueHolderUtils.getFailValueHolder("更新失败！");
            }
        }
        //修改明细
        List<StCDepositPreSaleSinkItemDO> stCHoldOrderItemDOList = stCDepositPreSaleSinkRequest.getStCDepositPreSaleSinkItemDOList();
        log.info(LogUtil.format("StCDepositPreSaleSinkSaveService.updateStCDepositPreSaleSink param " +
                "StCDepositPreSaleSinkItemDO:{}"), stCHoldOrderItemDOList);
        if (CollectionUtils.isNotEmpty(stCHoldOrderItemDOList)){
            List<StCDepositPreSaleSinkItemDO> updateList = new ArrayList<>();
            List<StCDepositPreSaleSinkItemDO> insterList = new ArrayList<>();
            long depositPreSaleSinkId = id;
            stCHoldOrderItemDOList.forEach(i->{
                i.setStCDepositPreSaleSinkId(depositPreSaleSinkId);
                if (checkItem(i)){
                    throw new NDSException("已存在相同明细！");
                }
                if(Objects.nonNull(i.getId()) &&i.getId()>0){
                    StBeanUtils.makeModifierField(i,session.getUser());
                    updateList.add(i);
                }else {
                    i.setId(ModelUtil.getSequence(StConstant.TAB_ST_C_DEPOSIT_PRE_SALE_SINK_ITEM));
                    StBeanUtils.makeCreateField(i,session.getUser());
                    insterList.add(i);
                }
            });
            Boolean flag = stCDepositPreSaleSinkItemSave(updateList,insterList);
            if (!flag){
                return ValueHolderUtils.getFailValueHolder("保存明细失败！");
            }else {
                StCDepositPreSaleSinkDO updateStCDepositPreSaleSinkDO = new StCDepositPreSaleSinkDO();
                StBeanUtils.makeModifierField(updateStCDepositPreSaleSinkDO,session.getUser());
                updateStCDepositPreSaleSinkDO.setId(id);
                stCDepositPreSaleSinkMapper.updateById(updateStCDepositPreSaleSinkDO);
            }
        }
        List<StCDepositPreSaleSinkLogisticsDO> stCDepositPreSaleSinkLogisticsDOList = stCDepositPreSaleSinkRequest.getStCDepositPreSaleSinkLogisticsDOList();
        log.info("StCDepositPreSaleSinkSaveService.updateStCDepositPreSaleSink param stCDepositPreSaleSinkLogisticsDOList:{}",stCDepositPreSaleSinkLogisticsDOList);
        if (CollectionUtils.isNotEmpty(stCDepositPreSaleSinkLogisticsDOList)){
            List<StCDepositPreSaleSinkLogisticsDO> updateList = new ArrayList<>();
            List<StCDepositPreSaleSinkLogisticsDO> insterList = new ArrayList<>();
            long depositPreSaleSinkId = id;
            stCDepositPreSaleSinkLogisticsDOList.forEach(i->{
                // 取得物流公司信息
                if (i.getCpCLogisticsId() != null) {
                    CpLogistics cpLogistics = rpcCpService.queryLogisticsById(i.getCpCLogisticsId());
                    if (cpLogistics != null) {
                        i.setCpCLogisticsEcode(cpLogistics.getEcode());
                        i.setCpCLogisticsEname(cpLogistics.getEname());
                    }
                }
                i.setStCDepositPreSaleSinkId(depositPreSaleSinkId);
                if (checkLogistics(i)){
                    throw new NDSException("已存在相同明细！");
                }
                if(Objects.nonNull(i.getId()) &&i.getId()>0){
                    StBeanUtils.makeModifierField(i,session.getUser());
                    updateList.add(i);
                }else {
                    i.setId(ModelUtil.getSequence(StConstant.TAB_ST_C_DEPOSIT_PRE_SALE_SINK_LOGISTICS));
                    StBeanUtils.makeCreateField(i,session.getUser());
                    insterList.add(i);
                }
            });
            Boolean flag = stCDepositPreSaleSinkLogisticsSave(updateList,insterList);
            if (!flag){
                return ValueHolderUtils.getFailValueHolder("保存明细失败！");
            }else {
                StCDepositPreSaleSinkDO updateStCDepositPreSaleSinkDO = new StCDepositPreSaleSinkDO();
                StBeanUtils.makeModifierField(updateStCDepositPreSaleSinkDO,session.getUser());
                updateStCDepositPreSaleSinkDO.setId(id);
                stCDepositPreSaleSinkMapper.updateById(updateStCDepositPreSaleSinkDO);
            }
        }
        return ValueHolderUtils.getSuccessValueHolder(id, StConstant.TAB_ST_C_DEPOSIT_PRE_SALE_SINK, "");
    }
    /**
     * description:明细保存
     * @Author:  liuwenjin
     * @Date 2021/10/18 4:31 下午
     */
    private Boolean stCDepositPreSaleSinkLogisticsSave(List<StCDepositPreSaleSinkLogisticsDO> updateList, List<StCDepositPreSaleSinkLogisticsDO> insterList) {
        log.info("StCDepositPreSaleSinkSaveService.stCDepositPreSaleSinkLogisticsSave param updateList:{},insterList:{}",updateList,insterList);
        int n=0;
        if (CollectionUtils.isNotEmpty(updateList)){
            for (StCDepositPreSaleSinkLogisticsDO stCDepositPreSaleSinkLogisticsDO: updateList) {
                n+= stCDepositPreSaleSinkLogisticsMapper.updateById(stCDepositPreSaleSinkLogisticsDO);
            }
        }
        if (CollectionUtils.isNotEmpty(insterList)){
            for (StCDepositPreSaleSinkLogisticsDO stCDepositPreSaleSinkLogisticsDO : insterList) {
                n+= stCDepositPreSaleSinkLogisticsMapper.insert(stCDepositPreSaleSinkLogisticsDO);
            }
        }
        if (n>0){
            return true;
        }
        return false;
    }

    /**
     * description:校验仓库物流
     * @Author:  liuwenjin
     * @Date 2021/10/18 4:24 下午
     */
    private boolean checkLogistics(StCDepositPreSaleSinkLogisticsDO stCDepositPreSaleSinkLogisticsDO) {
        Long id = stCDepositPreSaleSinkLogisticsDO.getId();
        Long cpCPhyWarehouseId=  stCDepositPreSaleSinkLogisticsDO.getCpCPhyWarehouseId();
        if (id>0){
            StCDepositPreSaleSinkLogisticsDO oldStCDepositPreSaleSinkLogistics =stCDepositPreSaleSinkLogisticsMapper.selectById(stCDepositPreSaleSinkLogisticsDO);
            if (cpCPhyWarehouseId==null){
                cpCPhyWarehouseId = oldStCDepositPreSaleSinkLogistics.getCpCPhyWarehouseId();
            }
        }
        if (stCDepositPreSaleSinkLogisticsMapper.selectCount(new QueryWrapper<StCDepositPreSaleSinkLogisticsDO>().lambda()
                .eq(StCDepositPreSaleSinkLogisticsDO ::getStCDepositPreSaleSinkId,stCDepositPreSaleSinkLogisticsDO.getStCDepositPreSaleSinkId())
                .eq(StCDepositPreSaleSinkLogisticsDO :: getCpCPhyWarehouseId,cpCPhyWarehouseId))>0){
            return true;
        }
        return false;
    }

    /**
     * description:解析仓库为字符串
     * @Author:  liuwenjin
     * @Date 2021/10/10 3:14 下午
     */
    private void parseWerIds(StCDepositPreSaleSinkDO stCDepositPreSaleSinkDO) {
        if (stCDepositPreSaleSinkDO.getCpCPhyWarehouse() !=null){
            String cpCPhyWarehouse = stCDepositPreSaleSinkDO.getCpCPhyWarehouse();
            if (StringUtils.isNotEmpty(cpCPhyWarehouse) && cpCPhyWarehouse.contains("value")) {
                log.info(LogUtil.format("要解析的字符串为：{}"), cpCPhyWarehouse);
                JSONObject jsonObject = JSONObject.parseObject(cpCPhyWarehouse);
                JSONObject valJson = jsonObject.getJSONObject("value");
                String ids =valJson.getString("IN");
                String warehouseIds = StringUtils.strip(ids,"[]");
                stCDepositPreSaleSinkDO.setWarehouseIds(warehouseIds);
            }
        }
    }

    private ValueHolder check(Long id, StCDepositPreSaleSinkRequest stCDepositPreSaleSinkRequest, String action) {
        StCDepositPreSaleSinkDO stCDepositPreSaleSinkDO = stCDepositPreSaleSinkRequest.getStCDepositPreSaleSinkDO();
        log.info(LogUtil.format("验证单表单数据{}"), stCDepositPreSaleSinkDO);
        Date beginTime = stCDepositPreSaleSinkDO.getBeginTime();
        Date endTime = stCDepositPreSaleSinkDO.getEndTime();
        //需要考虑编辑情况
        StCDepositPreSaleSinkDO depositPreSaleSinkDO = stCDepositPreSaleSinkMapper.selectById(id);
        if (depositPreSaleSinkDO != null){
            beginTime = beginTime != null ? beginTime : depositPreSaleSinkDO.getBeginTime();
            endTime = endTime != null ? endTime : depositPreSaleSinkDO.getEndTime();
        }

        //时间判断
        if (beginTime != null && endTime != null) {
            if (endTime.before(beginTime)) {
                return ValueHolderUtils.getFailValueHolder("结束日期不能小于开始日期！");
            }
        }
        return null;
    }
}
