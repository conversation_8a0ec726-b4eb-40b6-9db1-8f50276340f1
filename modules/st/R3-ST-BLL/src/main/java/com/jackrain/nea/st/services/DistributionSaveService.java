package com.jackrain.nea.st.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.ext.sequence.util.SequenceGenUtil;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.ps.api.result.PsSkuResult;
import com.jackrain.nea.ps.api.table.PsCSkuExt;
import com.jackrain.nea.psext.model.table.PsCPro;
import com.jackrain.nea.psext.request.SkuQueryListRequest;
import com.jackrain.nea.st.common.StConstant;
import com.jackrain.nea.st.mapper.StCDistributionItemMapper;
import com.jackrain.nea.st.mapper.StCDistributionMapper;
import com.jackrain.nea.st.model.request.DistributionRequest;
import com.jackrain.nea.st.model.table.StCDistributionDO;
import com.jackrain.nea.st.model.table.StCDistributionItemDO;
import com.jackrain.nea.st.rpc.RpcPsService;
import com.jackrain.nea.st.utils.JsonUtils;
import com.jackrain.nea.st.utils.StBeanUtils;
import com.jackrain.nea.st.utils.ValueHolderUtils;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import java.math.BigDecimal;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Descroption 分销代销策略新增保存
 * <AUTHOR>
 * @Date 2019/3/7 10:55
 */
@Component
@Slf4j
@Transactional
public class DistributionSaveService extends CommandAdapter {
    @Autowired
    private StCDistributionMapper mapper;
    @Autowired
    private StCDistributionItemMapper itemMapper;
    @Autowired
    private RpcPsService rpcPsService;

    @Override
    public ValueHolder execute(QuerySession session) {
        //1.获取传入参数
        DefaultWebEvent event = session.getEvent();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(
                event.getParameterValue("param"), "yyyy-MM-dd HH:mm:ss", SerializerFeature.WriteMapNullValue),
                Feature.OrderedField);

        Long id = param.getLong("objid");
        JSONObject fixColumn = param.getJSONObject("fixcolumn");
        //2.json转换成对象
        DistributionRequest distributionRequest = JsonUtils.jsonParseClass(fixColumn, DistributionRequest.class);

        //3.新增或变更
        if (fixColumn != null && id != null) {
            if (id != -1) {
                return updateDistribution(session, id, distributionRequest);
            } else {
                return addDistribution(session, distributionRequest);
            }
        }
        throw new NDSException("传入数据异常！");
    }

    private ValueHolder addDistribution(QuerySession session, DistributionRequest distributionRequest) {
        StCDistributionDO distribution = distributionRequest.getStCDistribution();
        //1.主表数据业务更新校验
        checkDistribution(distribution);

        //2.主表id及创建修改时间赋值
        distribution.setId(ModelUtil.getSequence(StConstant.TAB_ST_C_DISTRIBUTION));
        //主表创建信息更新
        StBeanUtils.makeCreateField(distribution, session.getUser());
        //单据编号生成更新
        JSONObject sequence = new JSONObject();
        String billNo = SequenceGenUtil.generateSquence("SEQ_ST_C_DISTRIBUTION",sequence,session.getUser().getLocale(),false);
        distribution.setBillNo(billNo);
        //3.主表数据保存
        int insertResult = mapper.insert(distribution);
        if (insertResult <= 0) {
            return ValueHolderUtils.getFailValueHolder("保存失败");
        }

        //5.明细数据遍历保存
        List<StCDistributionItemDO> itemList = distributionRequest.getStCDistributionItemList();
        if (!CollectionUtils.isEmpty(itemList)) {
            for (StCDistributionItemDO stCDistributionItem : itemList) {
                //若未录入条码则获取明细商品编码下的所有条码
                if(stCDistributionItem.getPsCSkuId() == null){
                    List<PsCSkuExt> skuList = getSkuInfoByProId(stCDistributionItem.getPsCProId());
                    PsCPro psCPro = rpcPsService.queryProByIds(stCDistributionItem.getPsCProId());
                    if(!CollectionUtils.isEmpty(skuList)){
                        for(PsCSkuExt psCSku : skuList){
                            StCDistributionItemDO newItemDO = generateItemBySkuInfo(psCSku,psCPro,stCDistributionItem,distributionRequest.getStCDistribution(),session.getUser());
                            if (itemMapper.insert(newItemDO) < 1) {
                                throw new NDSException("商品明细插入失败");
                            }
                        }
                    }else{
                        throw new NDSException("商品编码["+psCPro.getEcode()+"]下无条码信息");
                    }
                }else{
                    getSkuInfo(stCDistributionItem);
                    getSettlementPriceFunc(stCDistributionItem,distribution);
                    setItemExtInfo(stCDistributionItem,distributionRequest.getStCDistribution().getId(),session.getUser());
                    if (itemMapper.insert(stCDistributionItem) < 1) {
                        return ValueHolderUtils.getFailValueHolder("商品明细插入失败");
                    }
                }
            }
        }
        return ValueHolderUtils.getSuccessValueHolder(distribution.getId(), StConstant.TAB_ST_C_DISTRIBUTION);
    }

    private ValueHolder updateDistribution(QuerySession session, Long id, DistributionRequest distributionRequest) {
        //1.更新前校验
        StCDistributionDO existsDistribution = mapper.selectById(id);
        if (existsDistribution == null) {
            return ValueHolderUtils.getFailValueHolder("当前记录已不存在！");
        }
        StCDistributionDO distribution = distributionRequest.getStCDistribution();
        distribution.setId(id);
        StBeanUtils.makeModifierField(distribution, session.getUser());
        //主表数据业务更新校验
        checkDistribution(distribution);
        //3.更新主表信息
        if (mapper.updateById(distribution) <= 0) {
            return ValueHolderUtils.getFailValueHolder("分销代销策略主表更新失败");
        }
        //4.明细数据变更
        List<StCDistributionItemDO> distributionItemList = distributionRequest.getStCDistributionItemList();
        if(!CollectionUtils.isEmpty(distributionItemList)){
            Map<String,StCDistributionItemDO> existMap = new HashMap<>();
            //新增子表信息
            List<StCDistributionItemDO> addItemList = distributionItemList.stream().filter(x -> x.getId() == -1).collect(Collectors.toList());
            //修改子表信息
            List<StCDistributionItemDO> updateItemList = distributionItemList.stream().filter(x -> x.getId() > 0).collect(Collectors.toList());
            //原子表信息
            List<StCDistributionItemDO> invariantItemList = itemMapper.selectItemByMainId(id);
            for(StCDistributionItemDO preItem : invariantItemList){
                String itemKey = preItem.getPsCProId() + "," + preItem.getPsCSkuId();
                existMap.put(itemKey,preItem);
            }

            for (StCDistributionItemDO stCDistributionItem : addItemList) {
                if(stCDistributionItem.getPsCSkuId() == null){
                    List<PsCSkuExt> skuList = getSkuInfoByProId(stCDistributionItem.getPsCProId());
                    PsCPro psCPro = rpcPsService.queryProByIds(stCDistributionItem.getPsCProId());
                    if(!CollectionUtils.isEmpty(skuList)){
                        for(PsCSkuExt psCSku : skuList){
                            String addKey = psCSku.getPsCProId() + "," + psCSku.getId();
                            for(String preKey : existMap.keySet()){
                                if(preKey.equals(addKey)){
                                    throw new NDSException("已存在商品编码["+psCSku.getPsCProEcode()+"]下条码为["+psCSku.getEcode()+"]的商品明细!");
                                }
                            }
                            StCDistributionDO stCDistribution = distributionRequest.getStCDistribution();
                            StCDistributionItemDO newItemDO = generateItemBySkuInfo(psCSku,psCPro,stCDistributionItem,stCDistribution,session.getUser());
                            if (itemMapper.insert(newItemDO) < 1) {
                                throw new NDSException("商品明细插入失败");
                            }
                        }
                    }
                }else{
                    String addKey = stCDistributionItem.getPsCProId() + "," + stCDistributionItem.getPsCSkuId();
                    for(String preKey : existMap.keySet()){
                        if(preKey.equals(addKey)){
                            throw new NDSException("已存在该商品明细!");
                        }
                    }
                    getSkuInfo(stCDistributionItem);
                    getSettlementPriceFunc(stCDistributionItem,distribution);
                    checkDistributionItem(stCDistributionItem);
                    stCDistributionItem.setId(ModelUtil.getSequence(StConstant.TAB_ST_C_DISTRIBUTION_ITEM));
                    stCDistributionItem.setStCDistributionId(id);
                    StBeanUtils.makeCreateField(stCDistributionItem, session.getUser());
                    if (itemMapper.insert(stCDistributionItem) < 1) {
                        return ValueHolderUtils.getFailValueHolder("商品明细插入失败");
                    }
                }
            }
            for (StCDistributionItemDO stCDistributionItem : updateItemList) {
                getSettlementPriceFunc(stCDistributionItem,distribution);
                checkDistributionItem(stCDistributionItem);
                StBeanUtils.makeModifierField(stCDistributionItem, session.getUser());
                itemMapper.updateById(stCDistributionItem);
            }
        }
        return ValueHolderUtils.getSuccessValueHolder(id, StConstant.TAB_ST_C_DISTRIBUTION);
    }

    /**
     * @Descroption 计算结算价或折扣
     * @Author: 洪艺安
     * @Date 2019/5/9
     * @param stCDistributionItem
     * @param distribution
     * @return  void
     */
    private void getSettlementPriceFunc(StCDistributionItemDO stCDistributionItem,StCDistributionDO distribution){
        //标准价
        BigDecimal standardprice = stCDistributionItem.getStandardprice();
        if(standardprice == null || standardprice.compareTo(BigDecimal.ZERO) <= 0){
            throw new NDSException("数据异常,标准价小于0");
        }
        if(StConstant.CON_SETTLEMENT_TYPE_02 == distribution.getSettlementtype()){
            //佣金比例
            BigDecimal feeScale =  distribution.getFeescale();
            BigDecimal settlementprice = standardprice.subtract(standardprice.multiply(feeScale));
            stCDistributionItem.setSettlementprice(settlementprice);
        }else{
            BigDecimal settlementprice = stCDistributionItem.getSettlementprice();
            BigDecimal discount = stCDistributionItem.getDiscount();
            if(discount == null){
                discount = settlementprice.divide(standardprice,4,BigDecimal.ROUND_HALF_UP);
                discount = BigDecimal.ONE.subtract(discount);
                stCDistributionItem.setDiscount(discount);
            }else{
                settlementprice = standardprice.multiply(discount);
                stCDistributionItem.setSettlementprice(settlementprice);
            }
        }
    }

    /**
     * @Description 主表数据校验
     * <AUTHOR>
     * @date 2019/7/17 20:31
     * @param distribution
     * @return void
     */
    private void checkDistribution(StCDistributionDO distribution) {
        String errMessage = "";
        if(distribution.getBeginTime() !=null && distribution.getEndTime() != null){
            if(distribution.getBeginTime().compareTo(distribution.getEndTime())>0){
                errMessage = "生效日期结束时间必须大于起始时间！";
            }

            if (distribution.getBeginTime().before(new Date())) {
                errMessage = "起始时间不能小于当前日期！";
            }
        }
        Integer settlementType = distribution.getSettlementtype();
        if(StConstant.CON_SETTLEMENT_TYPE_02 == settlementType){
            if(distribution.getFeescale() == null || distribution.getFeescale().compareTo(BigDecimal.ZERO) <= 0){
                errMessage = "佣金比例不可以小于等于0！";
            }
        }
        if(distribution.getId() > 0){
            if (!StConstant.CON_BILL_STATUS_01.equals(distribution.getBillStatus())) {
                if (StConstant.CON_BILL_STATUS_03.equals(distribution.getBillStatus())) {
                    errMessage = "当前记录已作废，不允许编辑！";
                } else {
                    errMessage = "当前记录状态非未审核，不允许编辑!";
                }
            }
        }
        if (!errMessage.isEmpty()) {
            throw new NDSException(errMessage);
        }
    }

    /**
     * @Descroption 商品明细校验
     * @Author: 洪艺安
     * @Date 2019/5/13
     * @param distributionItem
     * @return  void
     */
    private void checkDistributionItem(StCDistributionItemDO distributionItem) {
        if(distributionItem.getSettlementprice() != null ){
            if(distributionItem.getSettlementprice().compareTo(BigDecimal.ZERO) <= 0){
                throw new NDSException("结算价小于等于0，不允许保存！");
            }
        }
    }

    private void getSkuInfo(StCDistributionItemDO distributionItem){
        SkuQueryListRequest psCSku = rpcPsService.querySkuByIds(distributionItem.getPsCSkuId());
        PsCPro psCPro = rpcPsService.queryProByIds(distributionItem.getPsCProId());
        if (psCSku != null) {
            //颜色id
            distributionItem.setPsCClrId(psCSku.getPsCSpec1objId());
            //颜色code
            distributionItem.setPsCClrEcode(psCSku.getColorEcode());
            //颜色name
            distributionItem.setPsCClrEname(psCSku.getColorName());
            //尺寸id
            distributionItem.setPsCSizeId(psCSku.getPsCSpec2objId());
            //尺寸code
            distributionItem.setPsCSizeEcode(psCSku.getSizeEcode());
            //尺寸name
            distributionItem.setPsCSizeEname(psCSku.getSizeName());
            //商品名称
            distributionItem.setPsCProEname(psCSku.getPsCProEname());
            //国标码
            distributionItem.setGbcode(psCSku.getGbcode());
            //单位
//            distributionItem.setUnit(psCPro.getUnit());   psCPro中没有该字段
            //标准价（吊牌价）
            distributionItem.setStandardprice(psCPro.getPricelist());
        }
    }

    private List<PsCSkuExt> getSkuInfoByProId(long id){
        PsSkuResult psSkuResult = rpcPsService.getSkuResultByProId(id);
        if(psSkuResult != null){
            return psSkuResult.getSkuList();
        }
        return null;
    }

    /**
     * @Description 新增明细公共字段处理
     * <AUTHOR>
     * @date 2019/7/18 14:30
     * @param distributionItem
     * @param mainId
     * @param user
     * @return void
     */
    private void setItemExtInfo(StCDistributionItemDO distributionItem, long mainId, User user){
        checkDistributionItem(distributionItem);
        distributionItem.setId(ModelUtil.getSequence(StConstant.TAB_ST_C_DISTRIBUTION_ITEM));
        distributionItem.setStCDistributionId(mainId);
        StBeanUtils.makeCreateField(distributionItem, user);
    }

    /**
     * @Description 填充明细数据信息
     * <AUTHOR>
     * @date 2019/7/18 14:26
     * @param psCSku
     * @param psCPro
     * @param preItemDO
     * @param mainDO
     * @param user
     * @return com.jackrain.nea.st.model.table.StCDistributionItemDO
     */
    private StCDistributionItemDO generateItemBySkuInfo(PsCSkuExt psCSku, PsCPro psCPro,StCDistributionItemDO preItemDO, StCDistributionDO mainDO, User user){
        StCDistributionItemDO newItemDO = new StCDistributionItemDO();
        BeanUtils.copyProperties(preItemDO, newItemDO);
        //颜色id
        newItemDO.setPsCClrId(psCSku.getPsCSpec1objId());
        //颜色code
        newItemDO.setPsCClrEcode(psCSku.getColorEcode());
        //颜色name
        newItemDO.setPsCClrEname(psCSku.getColorName());
        //尺寸id
        newItemDO.setPsCSizeId(psCSku.getPsCSpec2objId());
        //尺寸code
        newItemDO.setPsCSizeEcode(psCSku.getSizeEcode());
        //尺寸name
        newItemDO.setPsCSizeEname(psCSku.getSizeName());
        //商品名称
        newItemDO.setPsCProEname(psCSku.getPsCProEname());
        //国标码
        newItemDO.setGbcode(psCSku.getGbcode());
        //单位
//        newItemDO.setUnit(psCPro.getUnit());  psCPro中没有该字段
        //标准价（吊牌价）
        newItemDO.setStandardprice(psCPro.getPricelist());
        newItemDO.setPsCSkuId(psCSku.getId());
        newItemDO.setPsCSkuEcode(psCSku.getEcode());
        getSettlementPriceFunc(newItemDO,mainDO);
        setItemExtInfo(newItemDO,mainDO.getId(),user);
        return newItemDO;
    }
}
