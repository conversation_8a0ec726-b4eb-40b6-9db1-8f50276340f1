package com.jackrain.nea.st.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.jackrain.nea.st.common.StConstant;
import com.jackrain.nea.st.mapper.StCProductStrategyItemMapper;
import com.jackrain.nea.st.mapper.StCProductStrategyMapper;
import com.jackrain.nea.st.model.table.StCProductStrategyDO;
import com.jackrain.nea.st.model.table.StCProductStrategyItemDO;
import com.jackrain.nea.st.utils.DatasToEsUtils;
import com.jackrain.nea.st.utils.ListUtils;
import com.jackrain.nea.st.utils.StBeanUtils;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.HashMap;
import java.util.List;

@Component
@Slf4j
@Transactional
public class ProductStrategyUnAuditService extends CommandAdapter {

    @Autowired
    private StCProductStrategyMapper stCProductStrategyMapper;

    @Autowired
    private StCProductStrategyItemMapper stCProductStrategyItemMapper;

    /**
     * <AUTHOR>
     * @Description 反审核操作
     * @Date  2019/3/25
     * @Param [session]
     * @return com.jackrain.nea.util.ValueHolder
    **/
    public ValueHolder unauditProductStrategy(QuerySession session) {
        DefaultWebEvent event = session.getEvent();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(
                event.getParameterValue("param"), "yyyy-MM-dd HH:mm:ss", SerializerFeature.WriteMapNullValue),
                Feature.OrderedField);
        log.debug(LogUtil.format("请求JSON：") + param.toString());

        // 去除无效非空校验
        /*if (param == null) {
            throw new NDSException("参数为空！");
        }*/

        // 移除多余初始化, 节省新生代空间
        ValueHolder resultValueHolder;
        // 非定长hashMap, 给16个长度以尽量减少hash冲突, 定长则用 (长度/0.75f + 1) 以降低resize的开销, 负载因子默认0.75
        HashMap<Long, Object> errMap = new HashMap<>(16);
        //生成反审核Json数组
        JSONArray auditArray = StBeanUtils.makeUnAuditJsonArray(param);
        for (Object o : auditArray) {
            // 建议使用parseLong, 如果需要的是基本类型 valueOf会多一步自动装箱, 自动拆箱
            Long id = Long.parseLong(o.toString());
            try {
                //反审核验证, 移除返回值, 因为check方法内部抛了异常, 返回值占用栈帧, 只有等当前线程执行完成当前方法的时候, 栈帧才会弹出释放.
                checkAudit(id);
                StCProductStrategyDO stCProductStrategyDO = new StCProductStrategyDO();
                stCProductStrategyDO.setId(id);
                stCProductStrategyDO.setEstatus(StConstant.CON_BILL_STATUS_01);
                makeCheckerField(stCProductStrategyDO, session.getUser());
                stCProductStrategyDO.setSyncStartMark(0);
                stCProductStrategyDO.setSyncEndMark(0);
                stCProductStrategyDO.setSyncCompleteMark(0);
                //更新单据状态
                int count = stCProductStrategyMapper.updateById(stCProductStrategyDO);
                if (count < 0) {
                    log.debug(LogUtil.format("更新反审核信息失败！", id));
                    throw new Exception();
                } else {
//                    ValueHolder holder = shopUniversalStockService.deleteSgBSyncChannelStock(StConstant.PRODUCT_STRATEGY_TYPE.longValue(), id);
//                    log.debug(LogUtil.format("商品特殊 反审核 删除同步库存中间表数据 策略id：" + id + " 结果：" + JSONObject.toJSONString(holder));
                    // 更新明细表状态，并主表和明细表都推送ES数据
                    try {
                        //做更新的需要先查询更新后数据库的实体在推ES
                        stCProductStrategyDO = stCProductStrategyMapper.selectById(id);
                        StCProductStrategyItemDO item = new StCProductStrategyItemDO();
                        item.setStatus(StConstant.CON_BILL_STATUS_01);
                        QueryWrapper<StCProductStrategyItemDO> wrapper = new QueryWrapper<>();
                        wrapper.eq("st_c_product_strategy_id", id);
                        stCProductStrategyItemMapper.update(item, wrapper);
                        List<StCProductStrategyItemDO> itemList = ListUtils.batchQueryByCondition(wrapper, stCProductStrategyItemMapper, 1000);
                        DatasToEsUtils.insertProductEsData(stCProductStrategyDO, null, StConstant.TAB_ST_C_PRODUCT_STRATEGY);
                        if (CollectionUtils.isNotEmpty(itemList)) {
                            DatasToEsUtils.insertProductEsData(stCProductStrategyDO, itemList, StConstant.TAB_ST_C_PRODUCT_STRATEGY_ITEM);
                        }
                    } catch (Exception ex) {
                        log.debug(StConstant.TAB_ST_C_PRODUCT_STRATEGY + "店铺商品特殊设置主表更新反审核信息数据至ES失败：" + ex.toString());
                    }
                }
            } catch (Exception e) {
                errMap.put(id, e.getMessage());
            }
        }
        resultValueHolder = StBeanUtils.getExcuteValueHolder(auditArray.size(),errMap);
        return resultValueHolder;
    }
    /**
     * <AUTHOR>
     * @Description 审核验证
     * @Date  2019/3/25
     * @Param [id]
     **/
    private void checkAudit(Long id) throws Exception {
        //记录不存在
        StCProductStrategyDO stCProductStrategyDO = stCProductStrategyMapper.selectById(id);
        if (stCProductStrategyDO == null) {
            JSONObject errJo = new JSONObject();
            errJo.put("objid", id);
            errJo.put("message", "当前记录已不存在！");
            log.debug(LogUtil.format("id当前记录已不存在！", id));
            throw new Exception("当前记录已不存在！");
        }

        //状态为非未审核
        StCProductStrategyDO stCProductStrategyDO1 = stCProductStrategyMapper.selectByIdAndStatus(id, StConstant.CON_BILL_STATUS_02);
        if (stCProductStrategyDO1 == null) {
            JSONObject errJo = new JSONObject();
            errJo.put("objid", id);
            errJo.put("message", "方案状态不是已审核，不允许反审核！");
            log.debug(LogUtil.format("方案状态不是已审核，不允许反审核！", id));
            throw new Exception("方案状态不是已审核，不允许反审核！");
        }

    }
    /**
     * <AUTHOR>
     * @Description 反审核人基础信息封装
     * @Date  2019/3/25
     * @Param [stCProductStrategyDO, user]
     * @return void
     **/
    private void makeCheckerField(StCProductStrategyDO stCProductStrategyDO, User user) {

        stCProductStrategyDO.setReverseId(Long.valueOf(user.getId()));//审核人ID
        stCProductStrategyDO.setReverseName(user.getName());//审核人姓名
        stCProductStrategyDO.setReverseEname(user.getEname());//审核人工号
        stCProductStrategyDO.setReverseTime(new Date());//审核时间
    }

}
