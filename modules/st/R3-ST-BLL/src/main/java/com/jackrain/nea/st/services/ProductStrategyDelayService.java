package com.jackrain.nea.st.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.google.common.base.Throwables;
import com.google.common.collect.Lists;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.st.common.StConstant;
import com.jackrain.nea.st.mapper.StCProductStrategyItemMapper;
import com.jackrain.nea.st.mapper.StCProductStrategyMapper;
import com.jackrain.nea.st.model.table.StCProductStrategyDO;
import com.jackrain.nea.st.model.table.StCProductStrategyItemDO;
import com.jackrain.nea.st.utils.DatasToEsUtils;
import com.jackrain.nea.st.utils.ListUtils;
import com.jackrain.nea.st.utils.StBeanUtils;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * @Descroption 店铺商品特殊设置延期
 * @Author: 黄超
 * @Date: 2019/4/30
 */
@Component
@Transactional
@Slf4j
public class ProductStrategyDelayService {
    @Autowired
    private StCProductStrategyMapper mapper;

    @Autowired
    private StCProductStrategyItemMapper stCProductStrategyItemMapper;

    public ValueHolderV14 execute(JSONObject obj, User user) throws NDSException {
        ValueHolderV14 valueHolder = new ValueHolderV14();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(
                obj, "yyyy-MM-dd HH:mm:ss"),
                Feature.OrderedField);

        Date date = obj.getDate("delayDate");
        if (param != null) {
            JSONArray itemArray = StBeanUtils.makeAuditJsonArray(param);
            valueHolder = productStrategyDelay(date, itemArray, valueHolder, user);
        } else {
            throw new NDSException("参数为空！");
        }
        return valueHolder;
    }


    /**
     * 延期设置
     *
     * @param date  日期
     * @param itemArray item数组
     * @param valueHolderv14 参数
     * @param user 用户
     * @return 结果
     */
    public ValueHolderV14 productStrategyDelay(Date date, JSONArray itemArray, ValueHolderV14 valueHolderv14, User user) {

        //延期记录前，先判断是否存在
        int size = itemArray.size();
        if (size > 0) {
            List<StCProductStrategyDO> cLockSkuStrategyList = mapper.
                    selectBatchIds(JSONObject.parseArray(itemArray.toJSONString(), Long.class));
            JSONArray errorArray = new JSONArray();
            List<Long> ids = Lists.newArrayListWithExpectedSize(size);
            for (StCProductStrategyDO stCProductStrategyDO : cLockSkuStrategyList) {
                JSONObject errJo = new JSONObject();

                Long objId = stCProductStrategyDO.getId();
                JSONObject errorJson = delayParamCheck(date, stCProductStrategyDO, objId, errJo);
                if (errorJson != null) {
                    errorArray.add(errorJson);
                } else {
                    StBeanUtils.makeModifierField(stCProductStrategyDO, user);
                    //结束时间
                    stCProductStrategyDO.setEndTime(date);
                    int iResult = mapper.updateById(stCProductStrategyDO);
                    if (iResult < 0) {
                        errJo.put("objid", objId);
                        errJo.put("message", "延期失败！");
                        errorArray.add(errJo);
                    } else {
                        ids.add(objId);
                        //推送ES数据
                        try {
                            //做更新的需要先查询更新后数据库的实体在推ES
                            stCProductStrategyDO = mapper.selectById(objId);
                            StCProductStrategyItemDO item = new StCProductStrategyItemDO();
                            item.setStatus(stCProductStrategyDO.getEstatus());
                            item.setBeginTime(stCProductStrategyDO.getBeginTime());
                            item.setEndTime(stCProductStrategyDO.getEndTime());
                            QueryWrapper<StCProductStrategyItemDO> wrapper = new QueryWrapper<>();
                            wrapper.eq("st_c_product_strategy_id", objId);
                            stCProductStrategyItemMapper.update(item, wrapper);
                            List<StCProductStrategyItemDO> itemList = ListUtils.batchQueryByCondition(wrapper, stCProductStrategyItemMapper, 1000);
                            DatasToEsUtils.insertProductEsData(stCProductStrategyDO,null,StConstant.TAB_ST_C_PRODUCT_STRATEGY);
                            if (CollectionUtils.isNotEmpty(itemList)) {
                                DatasToEsUtils.insertProductEsData(stCProductStrategyDO, itemList, StConstant.TAB_ST_C_PRODUCT_STRATEGY_ITEM);
                            }
                        } catch (Exception ex) {
                            log.debug(LogUtil.format("店铺商品特殊设置更新延期信息数据至ES失败：{}"), Throwables.getStackTraceAsString(ex));
                        }
                    }
                }

            }
            valueHolderv14 = StBeanUtils.getProcessValueHolderV14(itemArray, errorArray, "延期");
            return valueHolderv14;
        }
        return valueHolderv14;
    }

    private JSONObject delayParamCheck(Date date, StCProductStrategyDO stCProductStrategyDO,
                                       Long objId, JSONObject errJo) {
        if (stCProductStrategyDO == null) {
            errJo.put("objid", objId);
            errJo.put("message", "当前记录已不存在！");
            return errJo;
        } else {
            if (!StConstant.CON_BILL_STATUS_02.equals(stCProductStrategyDO.getEstatus())) {
                errJo.put("objid", objId);
                errJo.put("message", "当前记录不是已审核，不允许延期！");
                return errJo;
            }
        }

        if (date != null) {
            if (date.before(new Date())) {
                errJo.put("objid", objId);
                errJo.put("message", "结束时间不能早于当前时间！");
                return errJo;
            }
        } else {
            errJo.put("objid", objId);
            errJo.put("message", "请选择延期结束日期！");
            return errJo;
        }

        Date beginDate = stCProductStrategyDO.getBeginTime();
        if (beginDate != null) {
            if (date.before(beginDate)) {
                errJo.put("objid", objId);
                errJo.put("message", "结束时间不能早于开始时间！");
                return errJo;
            }
        }
        Date endDate = stCProductStrategyDO.getEndTime();
        if (endDate != null) {
            if (!date.after(endDate)) {
                errJo.put("objid", objId);
                errJo.put("message", "结束时间不能早于原来结束时间！");
                return errJo;
            }
        }
        return null;
    }
}