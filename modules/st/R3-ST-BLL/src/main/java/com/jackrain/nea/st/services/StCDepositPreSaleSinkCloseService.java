package com.jackrain.nea.st.services;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.jackrain.nea.st.common.StConstant;
import com.jackrain.nea.st.mapper.StCDepositPreSaleSinkMapper;
import com.jackrain.nea.st.model.table.StCDepositPreSaleSinkDO;
import com.jackrain.nea.st.utils.RedisCacheUtil;
import com.jackrain.nea.st.utils.RedisConstant;
import com.jackrain.nea.st.utils.StBeanUtils;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.face.impl.UserImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * @ClassName : StCDepositPreSaleSinkClose  
 * @Description : 预下沉自动结案
 * <AUTHOR>  YCH
 * @Date: 2021-09-26 10:44  
 */
@Component
@Slf4j
@Transactional
public class StCDepositPreSaleSinkCloseService {
    @Autowired
    private StCDepositPreSaleSinkMapper mapper;

    public void closeStCDepositPreSaleSink() {

        List<StCDepositPreSaleSinkDO> stCDepositPreSaleSinkDOS = mapper.selectList(new QueryWrapper<StCDepositPreSaleSinkDO>().lambda()
                .eq(StCDepositPreSaleSinkDO::getEstatus, StConstant.HOLD_ORDER_STATUS_02));

        if (CollectionUtils.isEmpty(stCDepositPreSaleSinkDOS)){
            return;
        }
        for (StCDepositPreSaleSinkDO saleSinkDO : stCDepositPreSaleSinkDOS){
            //当前系统时间大于结束时间更新状态已结案
            Date current = new Date();
            if(current.before(saleSinkDO.getEndTime())){
                continue;
            }

            StCDepositPreSaleSinkDO updatePreSaleSink = new StCDepositPreSaleSinkDO();
            updatePreSaleSink.setId(saleSinkDO.getId());
            updatePreSaleSink.setEstatus(StConstant.HOLD_ORDER_STATUS_04);
            StBeanUtils.makeModifierField(updatePreSaleSink,getRootUser());
            mapper.updateById(updatePreSaleSink);

            /**
             * 结案删除对应redis
             */
            String wearIds = saleSinkDO.getCpCPhyWarehouse();
            // 删除Redis缓存
            deleteRedisByWarehouseId(wearIds);

        }
    }

    //删除Redis缓存
    private void deleteRedisByWarehouseId(String wearIds) {
        if (StringUtils.isNotEmpty(wearIds) && wearIds.contains("value")) {
            log.info(LogUtil.format("要解析的字符串为：{}"), wearIds);
            JSONObject jsonObject = JSONObject.parseObject(wearIds);
            JSONObject valJson = jsonObject.getJSONObject("value");
            String valIds =valJson.getString("IN");
            String warehouseIds = StringUtils.strip(valIds,"[]");
            if (StringUtils.isNotEmpty(warehouseIds)) {
                String [] ids = warehouseIds.split(",");
                for (String id : ids) {
                    RedisCacheUtil.delete(Long.parseLong(id), RedisConstant.ST_DEPOSIT_PRE_SALE_SINK_KEY);
                }
            }
        }
    }

    private User getRootUser() {
        UserImpl user = new UserImpl();
        user.setId(893);
        user.setName("root");
        user.setEname("root");
        return user;
    }
}
