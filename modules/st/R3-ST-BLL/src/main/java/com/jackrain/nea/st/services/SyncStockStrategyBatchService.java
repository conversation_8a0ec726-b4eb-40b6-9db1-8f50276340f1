package com.jackrain.nea.st.services;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.cp.api.CpCOrgChannelQueryCmd;
import com.jackrain.nea.cp.entity.CpCOrgChannelEntity;
import com.jackrain.nea.cp.entity.CpCOrgChannelItemEntity;
import com.jackrain.nea.cp.result.CpOrgChannelQueryResult;
import com.jackrain.nea.cpext.model.table.CpCStore;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.st.common.StConstant;
import com.jackrain.nea.st.mapper.StCSyncStockStrategyChannelMapper;
import com.jackrain.nea.st.mapper.StCSyncStockStrategyMapper;
import com.jackrain.nea.st.model.table.StCSyncStockStrategyChannelDO;
import com.jackrain.nea.st.model.table.StCSyncStockStrategyDO;
import com.jackrain.nea.st.utils.RedisCacheUtil;
import com.jackrain.nea.st.utils.RedisConstant;
import com.jackrain.nea.st.utils.StBeanUtils;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Reference;
import org.assertj.core.util.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @author: huang.zaizai
 * @since: 2019-07-01
 * @create at : 2019-07-01 19:52
 */
@Slf4j
@Component
public class SyncStockStrategyBatchService {

    @Autowired
    private StCSyncStockStrategyMapper stCSyncStockStrategyMapper;
    @Autowired
    private StCSyncStockStrategyChannelMapper stCSyncStockStrategyChannelMapper;
//    @Autowired
//    private SyncStockStrategySaveService syncStockStrategySaveService;
//
//    @Reference(group = "sg", version = "1.0")
//    private SgChannelStoreChangeCmd sgChannelStoreChangeCmd;

    @Reference(group = "cp", version = "1.0")
    private CpCOrgChannelQueryCmd cpCOrgChannelQueryCmd;

    /**
     * 批量更新明细
     *
     * @param object   {"ST_C_CHANNEL_STRATEGY_ID":"55,263","IDS":["15"]}
     * @param user 当前登录的用户
     * @return
     * @throws NDSException
     */
    public ValueHolderV14 stockStrategyBatchChangeFun(JSONObject object, User user) throws NDSException {
        ValueHolderV14 valueHolderV14 = new ValueHolderV14();
        valueHolderV14.setCode(ResultCode.SUCCESS);
        valueHolderV14.setMessage("批量处理成功！");

        // 当IDS为空则为所有的数据，否则为勾选的【渠道库存策略】的列表ID
        JSONArray ids = object.getJSONArray("IDS");
        // ST_C_CHANNEL_STRATEGY_ID,弹窗为所选中的供货渠道id
        String channelIds = object.getString("CP_C_ORG_CHANNEL_ID");

        List<Long> idList = Lists.newArrayList();
        if (ids != null && ids.size() > 0) {
            for (int i = 0; i < ids.size(); i++) {
                idList.add(ids.getLong(i));
            }
        }
        List<Long> channelIdList = Lists.newArrayList();
        if (channelIds != null) {
            String[] channelIdStr = channelIds.split(",");
            for (String channelId : channelIdStr) {
                channelIdList.add(Long.valueOf(channelId));
            }
        }

        List<StCSyncStockStrategyChannelDO> items = stCSyncStockStrategyChannelMapper.selectItemByChannelAndId(channelIdList, idList);

        try {
            switch (object.getString("TYPE").toUpperCase()) {
                case "ADD":
                    // 新增
                    if (CollectionUtils.isEmpty(items)) {
                        batchChangeForAdd(user, idList, channelIdList);
                    } else {
                        StCSyncStockStrategyChannelDO item = items.get(0);
                        StCSyncStockStrategyDO syncStockStrategy = stCSyncStockStrategyMapper.selectById(item.getStCSyncStockStrategyId());
                        throw new NDSException("供货渠道" + item.getCpCOrgChannelEname() + "在店铺同步库存策略" + syncStockStrategy.getCpCShopTitle() + "已存在，不允许新增！");
                    }
                    break;
                case "UPD":
                    break;
                case "DEL":
                    // 删除
                    if (!CollectionUtils.isEmpty(items)) {
                        batchChangeForDel(items);
                    } else {
                        throw new NDSException("店铺同步库存策略不存在选中的供货渠道！");
                    }
                    break;
                default:
                    break;
            }
        } catch (Exception ex) {
            valueHolderV14.setCode(ResultCode.FAIL);
            valueHolderV14.setMessage(ex.getMessage());
        }
        return valueHolderV14;
    }

    private void batchChangeForAdd(User user, List<Long> idList, List<Long> channelIdList) {
        Map<Long, CpCStore> storeMap = new HashMap<>();
        for (Long channelId : channelIdList) {
            ValueHolder channelAndItem = cpCOrgChannelQueryCmd.getChannelAndItem(channelId);
            Integer code = (Integer)channelAndItem.getData().get("code");

            if (code.intValue() == 0) {
                CpOrgChannelQueryResult cpOrgChannelQueryResult = (CpOrgChannelQueryResult) channelAndItem.getData().get("data");
                List<CpCOrgChannelItemEntity> channelItemList = cpOrgChannelQueryResult.getChannelItemList();
                CpCOrgChannelEntity channel = cpOrgChannelQueryResult.getChannel();
                if (!CollectionUtils.isEmpty(channelItemList)) {
                    for (Long id : idList) {
                        StCSyncStockStrategyChannelDO itemNew = new StCSyncStockStrategyChannelDO();

                        itemNew.setId(ModelUtil.getSequence(StConstant.TAB_ST_C_SYNC_STOCK_STRATEGY_CHANNEL));
                        itemNew.setCpCOrgChannelId(channelId);
                        itemNew.setStCSyncStockStrategyId(id);
                        if (channel.getEname() != null) {
                            itemNew.setCpCOrgChannelEname(channel.getEname());
                        }
                        StBeanUtils.makeCreateField(itemNew, user);

                        StCSyncStockStrategyDO syncStockStrategy = stCSyncStockStrategyMapper.selectById(id);
                        itemNew.setCpCShopId(syncStockStrategy.getCpCShopId());
                        itemNew.setCpCShopEcode(syncStockStrategy.getCpCShopEcode());
                        itemNew.setCpCShopTitle(syncStockStrategy.getCpCShopTitle());
//                        syncStockStrategySaveService.updateChannelStore(syncStockStrategy, itemNew, channelItemList, null, storeMap);

                        stCSyncStockStrategyChannelMapper.insert(itemNew);
                        RedisCacheUtil.delete(syncStockStrategy.getCpCShopId(), RedisConstant.SHOP_SYNC_STOCK_STRATEGY);
                        RedisCacheUtil.deleteAll(RedisConstant.SHOP_SYNC_STOCK_STRATEGY_ALL);
                    }
                }
            } else {
                throw new NDSException("调用更新渠道逻辑仓关系表接口失败");
            }
        }
    }

    private void batchChangeForDel(List<StCSyncStockStrategyChannelDO> items) {
        for (StCSyncStockStrategyChannelDO item : items) {
            StCSyncStockStrategyDO syncStockStrategy = stCSyncStockStrategyMapper.selectById(item.getStCSyncStockStrategyId());
            List<Long> cpStoreIds = stCSyncStockStrategyChannelMapper.listCpCStoreId(item.getId());
            //删除明细表数据
//            for (Long storeId : cpStoreIds) {
//                // 删除渠道逻辑仓关系表
//                SgChannelStoreChangeRequest storeChange = new SgChannelStoreChangeRequest();
//                storeChange.setCpCShopId(syncStockStrategy.getCpCShopId());
//                storeChange.setCpCChannelId(item.getCpCOrgChannelId());
//                storeChange.setCpCStoreId(storeId);
//                log.info(LogUtil.format("ChannelStrategyDelService,RPC参数_storeChange:{}", storeChange);
//                ValueHolderV14 v14 = sgChannelStoreChangeCmd.deleteSbGChannelStore(storeChange);
//                if (v14.getCode() == ResultCode.FAIL) {
//                    log.error(LogUtil.format("调用删除渠道逻辑仓关系RPC接口[sgChannelStoreChangeCmd.deleteSbGChannelStore]失败:" + v14.getMessage());
//                    throw new NDSException("调用删除渠道逻辑仓关系RPC接口失败！");
//                }
//            }
            stCSyncStockStrategyChannelMapper.deleteById(item.getId());
            RedisCacheUtil.delete(syncStockStrategy.getCpCShopId(), RedisConstant.SHOP_SYNC_STOCK_STRATEGY);
            RedisCacheUtil.deleteAll(RedisConstant.SHOP_SYNC_STOCK_STRATEGY_ALL);
        }
    }
}
