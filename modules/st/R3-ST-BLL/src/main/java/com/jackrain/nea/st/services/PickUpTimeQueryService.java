package com.jackrain.nea.st.services;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.st.mapper.StCPickUpTimeMapper;
import com.jackrain.nea.st.model.request.PickUpTimeQueryRequest;
import com.jackrain.nea.st.model.table.StCPickUpTimeDO;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.utility.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;

@Component
@Slf4j
public class PickUpTimeQueryService {
    @Autowired
    private StCPickUpTimeMapper mapper;

    public ValueHolderV14<StCPickUpTimeDO> selectTimeByWarehouseLogistics(PickUpTimeQueryRequest request) throws NDSException {
        log.debug(LogUtil.format(" PickUpItemQueryService.selectSkuBySellOwngoods入参：")+ JSONObject.toJSONString(request));
        ValueHolderV14<StCPickUpTimeDO> result = new ValueHolderV14<>();
        if (null == request.getCpCLogisticsId() || null == request.getCpCPhyWarehouseId()) {
            result.setCode(ResultCode.FAIL);
            result.setMessage("店铺ID/条码为空");
            return result;
        }

        List<StCPickUpTimeDO> pickUpTimeList = mapper.selectTimeByWarehouseLogistics(request.getCpCLogisticsId(),
                                                    request.getCpCPhyWarehouseId());
        log.debug(LogUtil.format(" PickUpItemQueryService.selectSkuBySellOwngoods返回：") + JSONObject.toJSONString(pickUpTimeList));
        if (CollectionUtils.isEmpty(pickUpTimeList)) {
            result.setCode(ResultCode.FAIL);
            result.setMessage("查询不到对应策略！");
            return result;
        }
        result.setCode(ResultCode.SUCCESS);
        result.setData(pickUpTimeList.get(0));
        result.setMessage("获取成功");
        return result;
    }
}
