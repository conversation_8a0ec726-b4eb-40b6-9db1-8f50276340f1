package com.jackrain.nea.st.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.st.common.StConstant;
import com.jackrain.nea.st.mapper.StCCompensateLogisticsMapper;
import com.jackrain.nea.st.mapper.StCCompensateMapper;
import com.jackrain.nea.st.mapper.StCCompensateWarehouseMapper;
import com.jackrain.nea.st.model.table.StCCompensateDO;
import com.jackrain.nea.st.utils.StBeanUtils;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;

/**
 * <AUTHOR>
 * @Date 2019/3/13 14:28
 */
@Component
@Slf4j
@Transactional
public class CompensateCancleAuditService extends CommandAdapter {

    @Autowired
    private StCCompensateMapper stCCompensateMapper;

    @Autowired
    private StCCompensateLogisticsMapper stCCompensateLogisticsMapper;

    @Autowired
    private StCCompensateWarehouseMapper stCCompensateWarehouseMapper;

    @Override
    public ValueHolder execute(QuerySession querySession) throws NDSException {
        //1.获取传入参数
        DefaultWebEvent event = querySession.getEvent();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(event.getParameterValue("param"),
                "yyyy-MM-dd HH:mm:ss", SerializerFeature.WriteMapNullValue), Feature.OrderedField);
        if (param == null) {
            throw new NDSException("参数为空！");
        }
        log.debug(LogUtil.format("快递赔付取消审核Json:")+param.toJSONString());
        //2.定义错误记录数
        int errRecord = 0;
        HashMap<Long, Object> errMap = new HashMap<Long, Object>();
        //3.生成审核Json数组
        JSONArray cancleAuditArray = StBeanUtils.makeUnAuditJsonArray(param);
        for (int i = 0; i < cancleAuditArray.size(); i++) {
            Long id = cancleAuditArray.getLong(i);
            try {
                //4.遍历反审核方法
                cancleAudit(id, querySession);
            } catch (Exception e) {
                errRecord++;
                errMap.put(id, e.getMessage());
            }
        }
        //5.返回ValueHolder对象
        return StBeanUtils.getExcuteValueHolder(cancleAuditArray.size(), errMap);
    }

    /**
     * @param id
     * @param querySession
     * @return
     * @Author: 朱宇军
     * @Date 2019/3/13
     */

    private void cancleAudit(Long id, QuerySession querySession) {
        StCCompensateDO stCCompensateDO = stCCompensateMapper.selectById(id);
        //校验
        checkCancleAutid(stCCompensateDO);

        JSONObject jsonObject = new JSONObject();
        StBeanUtils.makeModifierField(jsonObject, querySession.getUser());
        StBeanUtils.makeCancelField(jsonObject);//反审-审核置空信息
        //BILL_STATUS    1:未审核 2:已审核 3:已作废 4:已结案
        jsonObject.put("ID", id);
        jsonObject.put("BILL_STATUS", StConstant.CON_BILL_STATUS_01);

        //更新单据状态
        int count = stCCompensateMapper.updateAtrributes(jsonObject);
        if (count < 0) {
            throw new NDSException("方案:" + stCCompensateDO.getEname() + "反审核失败！");
        }
    }

    /**
     * @param stCCompensateDO
     * @return
     * @Author: 朱宇军
     * @Date 2019/3/13
     */

    private void checkCancleAutid(StCCompensateDO stCCompensateDO) {
        if (stCCompensateDO == null) {
            throw new NDSException("当前记录已不存在！");
        } else {
            if (StConstant.CON_BILL_STATUS_01.equals(stCCompensateDO.getBillStatus())) {
                throw new NDSException("当前记录未审核，不允许做反审核！");
            } else if (StConstant.CON_BILL_STATUS_04.equals(stCCompensateDO.getBillStatus())) {
                throw new NDSException("当前记录已结案，不允许做反审核！");
            } else if (StConstant.CON_BILL_STATUS_03.equals(stCCompensateDO.getBillStatus())) {
                throw new NDSException("当前记录已作废，不允许做反审核！");
            }
        }
    }
}
