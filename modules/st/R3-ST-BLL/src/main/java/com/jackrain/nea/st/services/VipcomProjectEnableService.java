package com.jackrain.nea.st.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.st.common.StConstant;
import com.jackrain.nea.st.mapper.StCVipcomProjectMapper;
import com.jackrain.nea.st.model.table.StCVipcomProjectDO;
import com.jackrain.nea.st.utils.StBeanUtils;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.HashMap;

/**
 * @Descroption 日程规划设置-启用接口
 * <AUTHOR>
 * @Date 2021/05/24 15:50
 */

@Component
@Slf4j
@Transactional
public class VipcomProjectEnableService extends CommandAdapter {

    @Autowired
    private StCVipcomProjectMapper stCVipcomProjectMapper;

    @Override
    public ValueHolder execute(QuerySession session) throws NDSException {
        DefaultWebEvent event = session.getEvent();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(
                event.getParameterValue("param"), "yyyy-MM-dd HH:mm:ss", SerializerFeature.WriteMapNullValue),
                Feature.OrderedField);
        HashMap<Long, Object> errMap = new HashMap<>();
        //校验选中数据是否为空
        JSONArray enableArray = StBeanUtils.checkJsonArray(param);
        //列表批量遍历 jsonarray 数组，把每一个对象转成 json 对象
            for (int i = 0; i < enableArray.size(); i++) {
                Long id = Long.valueOf(enableArray.get(i).toString());
                try {
                    enableVipcomProject(id, session);
                } catch (Exception ex) {
                    errMap.put(id, ex.getMessage());
                }
            }
        //返回日志
        return StBeanUtils.getExcuteValueHolder(enableArray.size(), errMap);
    }

    /**
     * 档期日程规划 批量启用
     *
     * @param id
     * @param session
     * @return void
     * @Author: 胡林洋
     * @Date 2021/05/24
     */
    private void enableVipcomProject(long id, QuerySession session) {
        //验证
        checkVipcomProject(id);
        //启用
        StCVipcomProjectDO project = new StCVipcomProjectDO();
        project.setId(id);
        //设置启用状态
        project.setIsactive(StConstant.ISACTIVE_Y);
        //启用人名称
        project.setDelname(session.getUser().getName());
        //启用人id
        project.setDelid(Long.valueOf(session.getUser().getId()));
        //启用人姓名
        project.setDelename(session.getUser().getName());
        //启用时间
        project.setDelTime(new Date());
        StBeanUtils.makeModifierField(project, session.getUser());
        int update = stCVipcomProjectMapper.updateById(project);
        if (update < 0) {
            throw new NDSException("启用失败！");
        }
    }

    /**
     * 校验日程规划
     *
     * @param id
     * @return void
     * @Author: 胡林洋
     * @Date 2021/05/24
     */
    private void checkVipcomProject(long id) {
        StCVipcomProjectDO project = stCVipcomProjectMapper.selectById(id);
        if (project == null) {
            throw new NDSException("当前记录已不存在！");
        } else {
            if (project.getIsactive().equals(StConstant.ISACTIVE_Y)) {
                throw new NDSException("当前记录已启用，不允许重复作废！");
            }
        }
    }


}
