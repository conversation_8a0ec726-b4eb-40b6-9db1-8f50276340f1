package com.jackrain.nea.st.services;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.jackrain.nea.st.mapper.StCVipUrgentStrategyMapper;
import com.jackrain.nea.st.model.result.StCVipUrgentStrategyResult;
import com.jackrain.nea.st.model.table.StCVipUrgentStrategy;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.utils.ValueHolderV14Utils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR> zhuxing
 * @Date : 2021-10-25 11:36
 * @Description : 查询服务
 **/
@Component
@Slf4j
public class StCVipUrgentStrategyQueryService{

    @Autowired
    private StCVipUrgentStrategyMapper stCVipUrgentStrategyMapper;

    public ValueHolderV14<StCVipUrgentStrategyResult> queryVipUrgentStrategyByShopId(Long shopId){

        ValueHolderV14 v14 = ValueHolderV14Utils.getSuccessValueHolder("查询成功！");
        List<StCVipUrgentStrategy> stCVipUrgentStrategyList = stCVipUrgentStrategyMapper.selectList(Wrappers.<StCVipUrgentStrategy>lambdaQuery()
                .eq(StCVipUrgentStrategy::getCpCShopId, shopId)
                .eq(StCVipUrgentStrategy::getIsactive,"Y")
                .le(StCVipUrgentStrategy::getBeginTime,new Date())
                .ge(StCVipUrgentStrategy::getEndTime,new Date())
                .orderByDesc(StCVipUrgentStrategy::getCreationdate)
        );
        if(!CollectionUtils.isEmpty(stCVipUrgentStrategyList)){
            //获取创建日期最新的一条
            StCVipUrgentStrategy stCVipUrgentStrategy = stCVipUrgentStrategyList.get(0);
            StCVipUrgentStrategyResult strategyResult = JSON.parseObject(JSON.toJSONString(stCVipUrgentStrategy),StCVipUrgentStrategyResult.class);
            v14.setData(strategyResult);
        }
        return v14;
    }

}
