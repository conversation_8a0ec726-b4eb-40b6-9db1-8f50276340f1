package com.jackrain.nea.st.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.redis.config.CusRedisTemplate;
import com.jackrain.nea.redis.util.RedisOpsUtil;
import com.jackrain.nea.st.common.StConstant;
import com.jackrain.nea.st.mapper.StCProductStrategyItemMapper;
import com.jackrain.nea.st.mapper.StCProductStrategyMapper;
import com.jackrain.nea.st.model.table.StCProductStrategyDO;
import com.jackrain.nea.st.model.table.StCProductStrategyItemDO;
import com.jackrain.nea.st.utils.DatasToEsUtils;
import com.jackrain.nea.st.utils.ListUtils;
import com.jackrain.nea.st.utils.StBeanUtils;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.utils.AssertUtils;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.concurrent.TimeUnit;

@Component
@Slf4j
@Transactional
public class ProductStrategyAuditService extends CommandAdapter {

    @Autowired
    private StCProductStrategyMapper stCProductStrategyMapper;

    @Autowired
    private StCProductStrategyItemMapper stCProductStrategyItemMapper;


    /**
     * @return com.jackrain.nea.util.ValueHolder
     * <AUTHOR>
     * @Description 审核操作
     * @Date 2019/3/25
     * @Param [session]
     **/
    public ValueHolder auditProductStrategy(QuerySession session) {
        DefaultWebEvent event = session.getEvent();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(
                event.getParameterValue("param"), "yyyy-MM-dd HH:mm:ss", SerializerFeature.WriteMapNullValue),
                Feature.OrderedField);
        log.debug(LogUtil.format("请求JSON ") + param.toString());
        ValueHolder resultValueHolder;
        HashMap<Long, Object> errMap = new HashMap<>(16);
        //生成审核Json数组
        JSONArray auditArray = StBeanUtils.makeAuditJsonArray(param);
        for (Object o : auditArray) {
            Long id = Long.valueOf(o.toString());

            //判断锁
            this.lockProductStrategy(id);

            try {
                //审核验证
                checkAudit(id);

                // 主表
                StCProductStrategyDO stCProductStrategyDO = new StCProductStrategyDO();
                stCProductStrategyDO.setId(id);
                stCProductStrategyDO.setEstatus(StConstant.CON_BILL_STATUS_02);
                makeCheckerField(stCProductStrategyDO, session.getUser());
                //更新单据状态
                int count = stCProductStrategyMapper.updateById(stCProductStrategyDO);
                if (count < 0) {
                    log.debug(LogUtil.format("更新审核信息失败！") + id);
                    throw new Exception();
                } else {
                    //更新明细表状态，并主表和明细表都推送ES数据
                    try {
                        //做更新的需要先查询更新后数据库的实体在推ES
                        stCProductStrategyDO = stCProductStrategyMapper.selectById(id);
                        StCProductStrategyItemDO item = new StCProductStrategyItemDO();
                        item.setStatus(StConstant.CON_BILL_STATUS_02);
                        item.setPlanName(stCProductStrategyDO.getPlanName());
                        item.setBeginTime(stCProductStrategyDO.getBeginTime());
                        item.setEndTime(stCProductStrategyDO.getEndTime());
                        item.setMainCreationdate(stCProductStrategyDO.getCreationdate());
                        QueryWrapper<StCProductStrategyItemDO> wrapper = new QueryWrapper<>();
                        wrapper.eq("st_c_product_strategy_id", id);
                        stCProductStrategyItemMapper.update(item, wrapper);
                        List<StCProductStrategyItemDO> itemList = ListUtils.batchQueryByCondition(wrapper, stCProductStrategyItemMapper, 1000);
                        DatasToEsUtils.insertProductEsData(stCProductStrategyDO, null, StConstant.TAB_ST_C_PRODUCT_STRATEGY);
                        if (CollectionUtils.isNotEmpty(itemList)) {
                            DatasToEsUtils.insertProductEsData(stCProductStrategyDO, itemList, StConstant.TAB_ST_C_PRODUCT_STRATEGY_ITEM);
                        }
                    } catch (Exception ex) {
                        log.debug(StConstant.TAB_ST_C_PRODUCT_STRATEGY + "店铺商品特殊设置主表更新审核信息数据至ES失败：" + ex.toString());
                    }
                }
            } catch (Exception e) {
                errMap.put(id, e.getMessage());
            } finally {
                this.unLockProductStrategy(id);
            }
        }
        resultValueHolder = StBeanUtils.getExcuteValueHolder(auditArray.size(), errMap);
        return resultValueHolder;
    }

    /**
     * <AUTHOR>
     * @Description 审核验证
     * @Date 2019/3/25
     * @Param [id]
     **/
    private void checkAudit(Long id) {
        // 记录不存在
        StCProductStrategyDO stCProductStrategyDO = stCProductStrategyMapper.selectById(id);
        if (stCProductStrategyDO == null) {
            log.debug(LogUtil.format("当前记录已不存在：id=", id));
            throw new NDSException("当前记录已不存在！");
        }

        // 状态为非未审核
        StCProductStrategyDO stCProductStrategyDO1 = stCProductStrategyMapper.selectByIdAndStatus(id, StConstant.CON_BILL_STATUS_01);
        if (stCProductStrategyDO1 == null) {
            log.debug(LogUtil.format("方案状态不是未审核，不允许审核！id=", id));
            throw new NDSException("方案状态不是未审核，不允许审核！");
        }

        // 结束时间小于或等于当前系统时间
        if (stCProductStrategyDO.getEndTime().compareTo(new Date()) <= 0) {
            log.debug(LogUtil.format("结束时间小于或等于当前系统时间！不允许审核！id=", id));
            throw new NDSException("结束时间小于或等于当前系统时间，不允许审核！");
        }

        // 方案不存在明细
        int count  = stCProductStrategyItemMapper.selectCountByItemId(id);
        AssertUtils.cannot(count <= 0, "当前方案明细没有记录，不允许审核！");
    }

    /**
     * @return void
     * <AUTHOR>
     * @Description 审核人基础信息封装
     * @Date 2019/3/25
     * @Param [stCProductStrategyDO, user]
     **/
    private void makeCheckerField(StCProductStrategyDO stCProductStrategyDO, User user) {

        stCProductStrategyDO.setCheckid(Long.valueOf(user.getId()));//审核人ID
        stCProductStrategyDO.setCheckname(user.getName());//审核人姓名
        stCProductStrategyDO.setCheckename(user.getEname());//审核人工号
        stCProductStrategyDO.setChecktime(new Date());//审核时间
    }

    /**
     * 解锁策略
     *
     * @param mainId
     */
    private void unLockProductStrategy(Long mainId) {
        if (mainId != null && mainId > 0) {
            CusRedisTemplate<String, String> redisTemplate = RedisOpsUtil.getStrRedisTemplate();
            String lockKsy = StConstant.TAB_ST_C_PRODUCT_STRATEGY + ":" + mainId;
            if (redisTemplate.opsForValue().get(lockKsy) != null) {
                redisTemplate.delete(lockKsy);
            }
        }
    }

    /**
     * 加锁策略
     *
     * @param mainId
     */
    private void lockProductStrategy(Long mainId) {
        CusRedisTemplate<String, String> redisTemplate = RedisOpsUtil.getStrRedisTemplate();
        String lockKsy = StConstant.TAB_ST_C_PRODUCT_STRATEGY + ":" + mainId;
        Boolean blnCanInit = redisTemplate.opsForValue().setIfAbsent(lockKsy, "OK");
        // 由于导入时间比较长，锁住1小时
        if (blnCanInit != null && blnCanInit) {
            redisTemplate.expire(lockKsy, 30, TimeUnit.MINUTES);
        } else {
            AssertUtils.logAndThrow("当前单据操作中，请稍后重试...");
        }
    }


}
