package com.jackrain.nea.st.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.jackrain.nea.cpext.model.table.CpShop;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.ext.sequence.util.SequenceGenUtil;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.ps.api.result.PsSkuResult;
import com.jackrain.nea.ps.api.table.PsCSkuExt;
import com.jackrain.nea.psext.request.SkuQueryListRequest;
import com.jackrain.nea.st.common.StConstant;
import com.jackrain.nea.st.mapper.StCInventorySkuOwnershipItemMapper;
import com.jackrain.nea.st.mapper.StCInventorySkuOwnershipMapper;
import com.jackrain.nea.st.model.table.StCInventorySkuOwnershipDO;
import com.jackrain.nea.st.model.table.StCInventorySkuOwnershipItemDO;
import com.jackrain.nea.st.rpc.RpcCpService;
import com.jackrain.nea.st.rpc.RpcPsService;
import com.jackrain.nea.st.utils.JsonUtils;
import com.jackrain.nea.st.utils.StBeanUtils;
import com.jackrain.nea.st.utils.ValueHolderUtils;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 库存条码归属策略新增保存业务逻辑
 *
 * <AUTHOR> huang.zaizai
 * @since : 2019-09-23
 * create at : 2019-09-23 9:00
 */
@Component
@Slf4j
@Transactional
public class InventorySkuOwnershipSaveService extends CommandAdapter {
    @Autowired
    private StCInventorySkuOwnershipMapper mapper;

    @Autowired
    private StCInventorySkuOwnershipItemMapper itemMapper;

    @Autowired
    private RpcPsService rpcPsService;

    @Autowired
    private RpcCpService rpcCpService;

    private String strTableMain = "ST_C_INVENTORY_SKU_OWNERSHIP";
    private String strTableList = "ST_C_INVENTORY_SKU_OWNERSHIP_ITEM";
    private static final String CP_C_SHOP_ID = "CP_C_SHOP_ID";

    /**
     * 新增和修改
     * @param querySession 参数封装
     * @return 返回状态
     * @throws NDSException 异常信息
     */
    @Override
    public ValueHolder execute(QuerySession querySession) throws NDSException {
        ValueHolder valueHolder = new ValueHolder();
        DefaultWebEvent event = querySession.getEvent();
        JSONObject param = JsonUtils.filterNull((JSONObject) event.getParameterValue("param"), null);

        if (param != null) {
            Long id = param.getLong("objid");
            JSONObject fixColumn = param.getJSONObject("fixcolumn");
            if (fixColumn != null) {
                JSONObject headMap = fixColumn.getJSONObject(strTableMain);
                JSONArray itemMap = fixColumn.getJSONArray(strTableList);
                if (id != null && id < 0) {
                    valueHolder = saveProcess(headMap, itemMap, valueHolder, querySession, id);
                } else {
                    valueHolder = updateProcess(headMap, itemMap, valueHolder, querySession, id);
                }
            }
        } else {
            throw new NDSException("参数为空！");
        }
        return valueHolder;
    }

    /**
     * 新增操作
     * @param headMap                主表数据
     * @param itemMap                子表数据
     * @param holder                 响应数据
     * @param querySession           封装数据
     * @param objid                  id
     * @return 返回状态
     */
    private ValueHolder saveProcess(JSONObject headMap,
                                       JSONArray itemMap,
                                       ValueHolder holder,
                                       QuerySession querySession, Long objid) {
        StCInventorySkuOwnershipDO stCInventorySkuOwnershipDO = JsonUtils.jsonParseClass(headMap, StCInventorySkuOwnershipDO.class);
        //状态数据检查
        if (!checkStatus(stCInventorySkuOwnershipDO, objid, holder)) {
            return holder;
        }

        if (stCInventorySkuOwnershipDO != null) {
            stCInventorySkuOwnershipDO.setId(ModelUtil.getSequence(strTableMain));
            //单据编号生成更新
            JSONObject sequence = new JSONObject();
            String ecode = SequenceGenUtil.generateSquence("SEQ_ST_C_INVENTORY_SKU_OWNERSHIP",sequence,
                    querySession.getUser().getLocale(),false);
            stCInventorySkuOwnershipDO.setEcode(ecode);

            stCInventorySkuOwnershipDO.setEstatus(StConstant.CON_BILL_STATUS_01);
            StBeanUtils.makeCreateField(stCInventorySkuOwnershipDO, querySession.getUser());

            if (mapper.insert(stCInventorySkuOwnershipDO) < 0) {
                holder = ValueHolderUtils.getFailValueHolder("保存失败！");
                return holder;
            }
        } else {
            holder = ValueHolderUtils.getFailValueHolder("保存失败！");
            return holder;
        }

        if (!saveItemProcess(holder, querySession, stCInventorySkuOwnershipDO, itemMap)) {
            return holder;
        } else {
            StCInventorySkuOwnershipDO stCInventorySkuOwnershipNewDO = new StCInventorySkuOwnershipDO();
            stCInventorySkuOwnershipNewDO.setId(stCInventorySkuOwnershipDO.getId());
            StBeanUtils.makeModifierField(stCInventorySkuOwnershipNewDO, querySession.getUser());
            mapper.updateById(stCInventorySkuOwnershipNewDO);
        }
        holder = ValueHolderUtils.getSuccessValueHolder(stCInventorySkuOwnershipDO.getId(), strTableMain);
        return holder;
    }

    private boolean checkStatus(StCInventorySkuOwnershipDO stCInventorySkuOwnershipDO, Long objid, ValueHolder valueHolder) {
        if (objid > 0) {
            StCInventorySkuOwnershipDO stCInventorySkuOwnershipOldDO = mapper.selectById(objid);
            if (stCInventorySkuOwnershipOldDO != null) {
                int iStatus = stCInventorySkuOwnershipOldDO.getEstatus();
                if (iStatus != 1) {
                    valueHolder.put("code", -1);
                    valueHolder.put("message", "单据处于未审核状态才能进行编辑！");
                    return false;
                }
            } else {
                valueHolder.put("code", -1);
                valueHolder.put("message", "数据已不存在");
                return false;
            }
            if (stCInventorySkuOwnershipDO != null) {
                if (stCInventorySkuOwnershipDO.getEndTime() == null) {
                    stCInventorySkuOwnershipDO.setEndTime(stCInventorySkuOwnershipOldDO.getEndTime());
                }
                if (stCInventorySkuOwnershipDO.getBeginTime() == null) {
                    stCInventorySkuOwnershipDO.setBeginTime(stCInventorySkuOwnershipOldDO.getBeginTime());
                }
            }
        }

        /**
         * 保存前进行的判断
         */
        if (stCInventorySkuOwnershipDO != null) {
            if (stCInventorySkuOwnershipDO.getEndTime() != null && stCInventorySkuOwnershipDO.getBeginTime() != null) {
                if (stCInventorySkuOwnershipDO.getEndTime().before(stCInventorySkuOwnershipDO.getBeginTime())) {
                    valueHolder.put("code", -1);
                    valueHolder.put("message", "方案的结束日期不能小于生效日期！");
                    return false;
                }
            }
        }
        return true;
    }

    /**
     * 更新操作
     *
     * @param headMap                主表数据
     * @param itemMap                子表数据
     * @param holder                 响应数据
     * @param querySession           封装数据
     * @param objid                  主表id
     * @return 返回状态
     */
    private ValueHolder updateProcess(JSONObject headMap,
                                         JSONArray itemMap,
                                         ValueHolder holder,
                                         QuerySession querySession,
                                         Long objid) {
        //主表更新，objid就是主表ID
        if (headMap != null && !headMap.isEmpty()) {
            StCInventorySkuOwnershipDO stCInventorySkuOwnershipDO = JSON.parseObject(headMap.toJSONString(),
                    new TypeReference<StCInventorySkuOwnershipDO>() {
                    });
            //状态数据检查
            if (!checkStatus(stCInventorySkuOwnershipDO, objid, holder)) {
                return holder;
            }
            stCInventorySkuOwnershipDO.setId(objid);
            StBeanUtils.makeModifierField(stCInventorySkuOwnershipDO, querySession.getUser());

            if (mapper.updateById(stCInventorySkuOwnershipDO) < 0) {
                holder = ValueHolderUtils.getFailValueHolder("保存失败！");
                return holder;
            }
        }

        //判断子表数据是否存在
        if (itemMap != null && !itemMap.isEmpty()) {
            //状态数据检查
            if (!checkStatus(null, objid, holder)) {
                return holder;
            }
            StCInventorySkuOwnershipDO stCInventorySkuOwnershipDO = mapper.selectById(objid);
            if (!saveItemProcess(holder, querySession, stCInventorySkuOwnershipDO, itemMap)) {
                return holder;
            } else {
                StCInventorySkuOwnershipDO stCInventorySkuOwnershipNewDO = new StCInventorySkuOwnershipDO();
                stCInventorySkuOwnershipNewDO.setId(stCInventorySkuOwnershipDO.getId());
                StBeanUtils.makeModifierField(stCInventorySkuOwnershipNewDO, querySession.getUser());
                mapper.updateById(stCInventorySkuOwnershipNewDO);
            }
        }
        holder = ValueHolderUtils.getSuccessValueHolder(objid, strTableMain);
        return holder;
    }

    /**
     * 子表新增
     */
    public boolean saveItemProcess(ValueHolder holder, QuerySession session,
                                    StCInventorySkuOwnershipDO stCInventorySkuOwnershipDO, JSONArray itemAllMap){
        // 旧数据取得，防止重复
        List<StCInventorySkuOwnershipItemDO> itemOldList = itemMapper.selectList(new QueryWrapper<StCInventorySkuOwnershipItemDO>()
                .lambda().eq(StCInventorySkuOwnershipItemDO::getStCInventorySkuOwnershipId, stCInventorySkuOwnershipDO.getId()));
        Map<Long, StCInventorySkuOwnershipItemDO> itemOldMap =  new HashMap<>();

        for (StCInventorySkuOwnershipItemDO itemDO : itemOldList) {
            itemOldMap.put(itemDO.getPsCSkuId(), itemDO);
            itemOldMap.put(itemDO.getCpCShopId(),itemDO);
        }
        if (itemAllMap != null) {
            List<StCInventorySkuOwnershipItemDO> stCInventorySkuOwnershipItemDOList = JSON.parseObject(itemAllMap.toJSONString(),
                    new TypeReference<ArrayList<StCInventorySkuOwnershipItemDO>>() {
                    });
            //CP_C_SHOP_ID传递为多个,分割
            JSONObject jsonObject = itemAllMap.getJSONObject(0);
            String str = jsonObject.get(CP_C_SHOP_ID).toString();
            List<Long> longList = Arrays.stream(str.split(","))
                    .map(s -> Long.parseLong(s.trim()))
                    .collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(stCInventorySkuOwnershipItemDOList)) {
                for (Long cpCShopId : longList) {
                    for (StCInventorySkuOwnershipItemDO stCInventorySkuOwnershipItemDO : stCInventorySkuOwnershipItemDOList) {
                            if (stCInventorySkuOwnershipItemDO.getCpCShopId() != null) {
                            CpShop cpShop = rpcCpService.selectCpCShopById(cpCShopId);
                            if (cpShop != null) {
                                stCInventorySkuOwnershipItemDO.setCpCShopId(cpCShopId);
                                stCInventorySkuOwnershipItemDO.setCpCShopEcode(cpShop.getEcode());
                                stCInventorySkuOwnershipItemDO.setCpCShopTitle(cpShop.getCpCShopTitle());
                            }
                        }
                        if (stCInventorySkuOwnershipItemDO.getId() > 0) {
                            //基本字段值设置
                            StBeanUtils.makeModifierField(stCInventorySkuOwnershipItemDO, session.getUser());
                            int updateResult = itemMapper.updateById(stCInventorySkuOwnershipItemDO);
                            if (updateResult <= 0) {
                                holder.put("code", -1);
                                holder.put("message", "保存失败！");
                                return false;
                            }
                        } else {
                            if (!this.insterItemProcess(holder, session, stCInventorySkuOwnershipDO, itemOldMap, stCInventorySkuOwnershipItemDO)) {
                                return false;
                            }
                        }
                    }
                }
            } else {
                holder.put("code", -1);
                holder.put("message", "明细JSON转换失败，保存失败！");
                return false;
            }
        }
        return true;
    }

    private boolean insterItemProcess(ValueHolder holder, QuerySession session,
                                      StCInventorySkuOwnershipDO stCInventorySkuOwnershipDO,
                                      Map<Long, StCInventorySkuOwnershipItemDO> itemOldMap,
                                      StCInventorySkuOwnershipItemDO stCInventorySkuOwnershipItemDO) {
        //商品编码和条码至少输入一个
        if (stCInventorySkuOwnershipItemDO.getPsCProId() == null && stCInventorySkuOwnershipItemDO.getPsCSkuId() == null) {
            holder.put("code", -1);
            holder.put("message", "商品编码和条码至少输入一个！");
            return false;
        }
        if (stCInventorySkuOwnershipItemDO.getPsCSkuId() != null) {
            SkuQueryListRequest psCSku = rpcPsService.querySkuByIds(stCInventorySkuOwnershipItemDO.getPsCSkuId());
            if (psCSku != null) {
                StCInventorySkuOwnershipItemDO itemNewDO = new StCInventorySkuOwnershipItemDO();

                itemNewDO.setOwnershipNum(stCInventorySkuOwnershipItemDO.getOwnershipNum());
                itemNewDO.setCpCShopId(stCInventorySkuOwnershipItemDO.getCpCShopId());
                itemNewDO.setCpCShopEcode(stCInventorySkuOwnershipItemDO.getCpCShopEcode());
                itemNewDO.setCpCShopTitle(stCInventorySkuOwnershipItemDO.getCpCShopTitle());

                if (itemOldMap.containsKey(psCSku.getId()) && itemOldMap.containsKey(itemNewDO.getCpCShopId())) {
                    itemNewDO.setId(itemOldMap.get(psCSku.getId()).getId());

                    //基本字段值设置
                    StBeanUtils.makeModifierField(itemNewDO, session.getUser());
                    int updateResult = itemMapper.updateById(itemNewDO);
                    if (updateResult <= 0) {
                        holder.put("code", -1);
                        holder.put("message", "保存失败！");
                        return false;
                    }
                } else {
                    //序号
                    itemNewDO.setId(ModelUtil.getSequence(strTableList));
                    //主表关联id
                    itemNewDO.setStCInventorySkuOwnershipId(stCInventorySkuOwnershipDO.getId());

                    itemNewDO.setPsCSkuId(psCSku.getId()); //条码id
                    itemNewDO.setPsCSkuEcode(psCSku.getEcode()); //条码
                    itemNewDO.setGbcode(psCSku.getGbcode()); //国标码
                    itemNewDO.setPsCProId(psCSku.getPsCProId()); //商品id
                    itemNewDO.setPsCProEcode(psCSku.getPsCProEcode()); //商品编码
                    itemNewDO.setPsCProEname(psCSku.getPsCProEname()); //商品名称
                    itemNewDO.setPsCClrId(psCSku.getPsCSpec1objId()); //颜色id
                    itemNewDO.setPsCClrEcode(psCSku.getColorEcode()); //颜色编码
                    itemNewDO.setPsCClrEname(psCSku.getColorName()); //颜色名称
                    itemNewDO.setPsCSizeId(psCSku.getPsCSpec2objId()); //尺寸id
                    itemNewDO.setPsCSizeEcode(psCSku.getSizeEcode()); //尺寸编码
                    itemNewDO.setPsCSizeEname(psCSku.getSizeName()); //尺寸名称

                    //基本字段值设置
                    StBeanUtils.makeCreateField(itemNewDO, session.getUser());
                    int insertResult = itemMapper.insert(itemNewDO);
                    if (insertResult <= 0) {
                        holder.put("code", -1);
                        holder.put("message", "保存失败！");
                        return false;
                    }
                    itemOldMap.put(itemNewDO.getPsCSkuId(), itemNewDO);
                }
            }
        } else {
            List<PsCSkuExt> psCSkuExts = new ArrayList<>();
            PsSkuResult psSkuResult = rpcPsService.getSkuResultByProId(stCInventorySkuOwnershipItemDO.getPsCProId());
            if (!CollectionUtils.isEmpty(psSkuResult.getSkuList()) && stCInventorySkuOwnershipItemDO.getPsCSkuId() != null) {
                psCSkuExts = psSkuResult.getSkuList().stream().filter(sku -> sku.getId().equals(stCInventorySkuOwnershipItemDO.getPsCSkuId())).collect(Collectors.toList());
            } else {
                psCSkuExts = psSkuResult.getSkuList();
            }
            if (CollectionUtils.isEmpty(psCSkuExts)) {
                holder.put("code", -1);
                holder.put("message", "无法匹配条码数据！");
                return false;
            }
            for (PsCSkuExt psCSkuExt : psCSkuExts) {
                StCInventorySkuOwnershipItemDO itemNewDO = new StCInventorySkuOwnershipItemDO();

                itemNewDO.setOwnershipNum(stCInventorySkuOwnershipItemDO.getOwnershipNum());
                itemNewDO.setCpCShopId(stCInventorySkuOwnershipItemDO.getCpCShopId());
                itemNewDO.setCpCShopEcode(stCInventorySkuOwnershipItemDO.getCpCShopEcode());
                itemNewDO.setCpCShopTitle(stCInventorySkuOwnershipItemDO.getCpCShopTitle());

                if(itemOldMap.containsKey(psCSkuExt.getId()) && itemOldMap.containsKey(itemNewDO.getCpCShopId())) {
                    itemNewDO.setId(itemOldMap.get(psCSkuExt.getId()).getId());

                    itemNewDO.setOwnershipNum(stCInventorySkuOwnershipItemDO.getOwnershipNum());
                    itemNewDO.setCpCShopId(stCInventorySkuOwnershipItemDO.getCpCShopId());
                    itemNewDO.setCpCShopEcode(stCInventorySkuOwnershipItemDO.getCpCShopEcode());
                    itemNewDO.setCpCShopTitle(stCInventorySkuOwnershipItemDO.getCpCShopTitle());

                    //基本字段值设置
                    StBeanUtils.makeModifierField(itemNewDO, session.getUser());
                    int updateResult = itemMapper.updateById(itemNewDO);
                    if (updateResult <= 0) {
                        holder.put("code", -1);
                        holder.put("message", "保存失败！");
                        return false;
                    }
                } else {
                    //序号
                    itemNewDO.setId(ModelUtil.getSequence(strTableList));
                    //主表关联id
                    itemNewDO.setStCInventorySkuOwnershipId(stCInventorySkuOwnershipDO.getId());
                    itemNewDO.setPsCSkuId(psCSkuExt.getId()); //条码id
                    itemNewDO.setPsCSkuEcode(psCSkuExt.getEcode()); //条码
                    itemNewDO.setGbcode(psCSkuExt.getGbcode()); //国标码
                    itemNewDO.setPsCProId(psCSkuExt.getPsCProId()); //商品id
                    itemNewDO.setPsCProEcode(psCSkuExt.getPsCProEcode()); //商品编码
                    itemNewDO.setPsCProEname(psCSkuExt.getPsCProEname()); //商品名称
                    itemNewDO.setPsCClrId(psCSkuExt.getPsCSpec1objId()); //颜色id
                    itemNewDO.setPsCClrEcode(psCSkuExt.getColorEcode()); //颜色编码
                    itemNewDO.setPsCClrEname(psCSkuExt.getColorName()); //颜色名称
                    itemNewDO.setPsCSizeId(psCSkuExt.getPsCSpec2objId()); //尺寸id
                    itemNewDO.setPsCSizeEcode(psCSkuExt.getSizeEcode()); //尺寸编码
                    itemNewDO.setPsCSizeEname(psCSkuExt.getSizeName()); //尺寸名称

                    //基本字段值设置
                    StBeanUtils.makeCreateField(itemNewDO, session.getUser());
                    int insertResult = itemMapper.insert(itemNewDO);
                    if (insertResult <= 0) {
                        holder.put("code", -1);
                        holder.put("message", "保存失败！");
                        return false;
                    }
                    itemOldMap.put(itemNewDO.getPsCSkuId(), itemNewDO);
                }
            }
        }
        return true;
    }
}
