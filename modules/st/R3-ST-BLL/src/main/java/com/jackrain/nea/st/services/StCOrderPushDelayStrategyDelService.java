package com.jackrain.nea.st.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.model.resources.OmsRedisKeyResources;
import com.jackrain.nea.redis.util.RedisOpsUtil;
import com.jackrain.nea.st.common.StConstant;
import com.jackrain.nea.st.mapper.StCOrderPushDelayStrategyMapper;
import com.jackrain.nea.st.model.table.StCOrderPushDelayStrategy;
import com.jackrain.nea.st.utils.RedisCacheUtil;
import com.jackrain.nea.st.utils.RedisConstant;
import com.jackrain.nea.st.utils.StBeanUtils;
import com.jackrain.nea.st.utils.ValueHolderUtils;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @Author: ganquan
 * @Date Create In 2020/7/1 15:07
 * @Description: 订单推单延时策略删除
 */
@Component
@Slf4j
public class StCOrderPushDelayStrategyDelService extends CommandAdapter {

    @Autowired
    private StCOrderPushDelayStrategyMapper stCOrderPushDelayStrategyMapper;

    @Autowired
    private RedisOpsUtil<String, Object> redisUtil;

    @Override
    public ValueHolder execute(QuerySession querySession) throws NDSException {
        DefaultWebEvent event = querySession.getEvent();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(
                event.getParameterValue("param"), "yyyy-MM-dd HH:mm:ss"), Feature.OrderedField);
        log.info(LogUtil.format("StCOrderPushDelayStrategyDelServiceParam:{}"), param);
        String isDel = param.getString("isdelmtable");
        Long objid = param.getLong("objid");
        //判断主表是否存在
        StCOrderPushDelayStrategy stCOrderPushDelayStrategy = stCOrderPushDelayStrategyMapper.selectById(objid);
        if (stCOrderPushDelayStrategy == null) {
            return ValueHolderUtils.getFailValueHolder("当前记录已不存在!");
        }
        // 删除策略时，删除REDIS
        /*String redisKey =
                OmsRedisKeyResources.buildLockStrderPushDelayRedisKey(stCOrderPushDelayStrategy.getCpCShopId());
        if (redisUtil.objRedisTemplate.hasKey(redisKey)) {
            redisUtil.objRedisTemplate.delete(redisKey);
        }*/
        JSONObject tabitem = param.getJSONObject("tabitem");
        JSONArray errorArray = new JSONArray();
        //判断是删除主表还是明细表单独删除
        if (StConstant.TRUE_STR.equals(isDel)) {
            //删除主表
            int deleteCount = stCOrderPushDelayStrategyMapper.deleteById(objid);
            RedisCacheUtil.delete(stCOrderPushDelayStrategy.getCpCShopId(), RedisConstant.SHOP_ORDER_PUSH_DELAY);
            if (deleteCount <= 0) {
                errorArray.add(StBeanUtils.getJsonObjectInfo(objid, "订单推单延时策略已不存在"));
            }
        }
        return StBeanUtils.getExcuteValueHolder(errorArray);
    }

}
