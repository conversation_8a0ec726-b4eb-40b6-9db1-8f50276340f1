package com.jackrain.nea.st.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.st.common.StConstant;
import com.jackrain.nea.st.mapper.StCVipcomProjectPickorderItemMapper;
import com.jackrain.nea.st.model.table.StCVipcomProjectPickorderItem;
import com.jackrain.nea.st.utils.JsonUtils;
import com.jackrain.nea.st.utils.StBeanUtils;
import com.jackrain.nea.st.utils.ValueHolderUtils;
import com.jackrain.nea.st.validate.StParamConstants;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import java.util.List;

/**
 * 档期日程规划拣货单明细保存
 * <AUTHOR>
 * @Date 2021/05/25/17:10
 */
@Component
@Slf4j
@Transactional(rollbackFor = Exception.class)
public class VipcomProjectPickorderItemSaveService extends CommandAdapter {

    @Autowired
    private StCVipcomProjectPickorderItemMapper stCVipcomProjectPickorderItemMapper;

    @Override
    public ValueHolder execute(QuerySession session) {
        DefaultWebEvent event = session.getEvent();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(
                event.getParameterValue("param"), "yyyy-MM-dd HH:mm:ss", SerializerFeature.WriteMapNullValue),
                Feature.OrderedField);
        log.debug(LogUtil.format("Start VipcomProjectPickorderItemSaveService.execute. ReceiveParams: {}"),
                param.toJSONString());
        Long masterId = param.getLong("ST_C_VIPCOM_PROJECT_ID");
        JSONObject jsonObject = param.getJSONObject("PICKORDER_ITEM");
        if(jsonObject == null){
            throw new NDSException("拣货单明细接口传入数据为空！");
        }
        Long id = jsonObject.getLong("ID");
        StCVipcomProjectPickorderItem stCVipcomProjectPickorderItem = JsonUtils.jsonParseClass(jsonObject, StCVipcomProjectPickorderItem.class);
        if (stCVipcomProjectPickorderItem == null) {
            throw new NDSException("数据异常！");
        }
        /**
         * 仓库唯一性校验
         */
        List<StCVipcomProjectPickorderItem> stCVipcomProjectPickorderItems = stCVipcomProjectPickorderItemMapper.selectList(new QueryWrapper<StCVipcomProjectPickorderItem>().lambda()
                .eq(StCVipcomProjectPickorderItem::getStCVipcomProjectId, masterId));
        if (CollectionUtils.isNotEmpty(stCVipcomProjectPickorderItems)){
            String type = stCVipcomProjectPickorderItems.get(0).getPickorderCreateType();
            stCVipcomProjectPickorderItems.forEach(item ->{
                //如果是按照时间点创建的，不同时间点可以创建相同仓库
                boolean isRepeat = !item.getId().equals(id) && checkWareHouse(item.getCpCOrigId(),stCVipcomProjectPickorderItem.getCpCOrigId());
                if (StConstant.PICKORDER_CREATE_TYPE_1.equals(type)){
                    if (item.getCreateTime().equals(stCVipcomProjectPickorderItem.getCreateTime()) && isRepeat){
                        throw new NDSException("相同时间下，该唯品会仓库已存在！");
                    }
                }else if (isRepeat){
                    throw new NDSException("唯品会仓库已存在！");
                }

            });
        }
        if (id != null) {
            if (id != -1) {
                return updatePickorderItem(session, stCVipcomProjectPickorderItem);
            } else {
                return insertPickorderItem(masterId,stCVipcomProjectPickorderItem,session);
            }
        }else{
            throw new NDSException("拣货单明细接口传入数据，id为空！");
        }
    }

    /**
     * 判断仓库是否重复
     * @param source 已有明细仓库
     * @param input 更新的仓库
     * @return
     */
    private boolean checkWareHouse(String source,String input){

        if(ObjectUtils.isEmpty(source) || ObjectUtils.isEmpty(input)){
            return false;
        }

        for(String id : input.split(",")){
            if(source.contains(id)){
                return true;
            }
        }
        return false;
    }


    /**
     * @param session
     * @param item
     * @return com.jackrain.nea.util.ValueHolder
     * @Author: 胡林洋
     * @Date 2021/05/26
     */
    private ValueHolder updatePickorderItem(QuerySession session,StCVipcomProjectPickorderItem item) {
        StBeanUtils.makeModifierField(item, session.getUser());
        int update = stCVipcomProjectPickorderItemMapper.updateById(item);
        if (update < 0) {
            throw new NDSException("更新失败！");
        }
        return ValueHolderUtils.getSuccessValueHolder("更新成功！");
    }

    /**
     * 日程计划明细插入
     *
     * @param masterId
     * @param item
     * @param session
     * @return com.jackrain.nea.util.ValueHolder
     * @Author: 胡林洋
     * @Date 2021/05/26
     */
    private ValueHolder insertPickorderItem(long masterId, StCVipcomProjectPickorderItem item, QuerySession session) {

        Long id = ModelUtil.getSequence(StConstant.TAB_ST_C_VIPCOM_PROJECT_PICKORDER_ITEM);
        item.setId(id);
        //主键
        item.setStCVipcomProjectId(masterId);
        StBeanUtils.makeCreateField(item, session.getUser());
        checkPickorderItemByFilter(item);
        int insert = stCVipcomProjectPickorderItemMapper.insert(item);
        if (insert < 0) {
            throw new NDSException("保存失败！");
        }
        return ValueHolderUtils.getSuccessValueHolder("新增成功！");
    }

    private void checkPickorderItemByFilter(StCVipcomProjectPickorderItem item) {
        String createType = item.getPickorderCreateType();
        String createTime = item.getCreateTime();
        String jitWare = item.getCpCOrigId();
        Integer peakValue = item.getPeakValue();
        if (StParamConstants.ONE.equals(createType)) {
            if ( StringUtils.isEmpty(createTime) || StringUtils.isEmpty(jitWare)) {
                throw new NDSException("请录入创建时间、唯品会仓库！");
            }
        }else if(StParamConstants.TWO.equals(createType)){
            if ( peakValue == null || StringUtils.isEmpty(jitWare)) {
                throw new NDSException("请录入创建峰值、唯品会仓库！");
            }
        }
    }

}
