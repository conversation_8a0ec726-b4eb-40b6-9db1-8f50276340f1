package com.jackrain.nea.st.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Throwables;
import com.google.common.collect.Lists;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.cp.entity.CpCOrgChannelEntity;
import com.jackrain.nea.cp.request.CpCOrgChannelQueryCmdRequest;
import com.jackrain.nea.cpext.model.table.CpShop;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.psext.model.table.PsCPro;
import com.jackrain.nea.psext.model.table.PsCSku;
import com.jackrain.nea.redis.config.CusRedisTemplate;
import com.jackrain.nea.redis.util.RedisOpsUtil;
import com.jackrain.nea.st.common.StConstant;
import com.jackrain.nea.st.config.R3OssConfig;
import com.jackrain.nea.st.mapper.StCProductStrategyMapper;
import com.jackrain.nea.st.model.common.StEsConstants;
import com.jackrain.nea.st.model.request.ProductStrategyMultiShopImportRequest;
import com.jackrain.nea.st.model.table.StCProductStrategyDO;
import com.jackrain.nea.st.model.table.StCProductStrategyItemDO;
import com.jackrain.nea.st.rpc.RpcCpService;
import com.jackrain.nea.st.rpc.RpcPsService;
import com.jackrain.nea.st.thread.ProductStrategyImportTask;
import com.jackrain.nea.st.thread.ProductStrategyImportTaskExecutorService;
import com.jackrain.nea.st.utils.DatasToEsUtils;
import com.jackrain.nea.st.utils.ExportUtil;
import com.jackrain.nea.st.utils.ListUtils;
import com.jackrain.nea.st.utils.StBeanUtils;
import com.jackrain.nea.st.validate.ProductStrategyImpCheckResult;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.utils.AssertUtils;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.face.impl.UserImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2020-08-24
 * create at : 2020-08-24 11:32
 */
@Component
@Slf4j
public class ProductStrategyImportService {

    @Autowired
    private StCProductStrategyMapper strategyMapper;
    @Autowired
    private ProductStrategyItemService strategyItemService;
    @Autowired
    private ProductStrategyImportTaskExecutorService taskExecutorService;

    @Autowired
    private RpcCpService rpcCpService;
    @Autowired
    private RpcPsService rpcPsService;

    @Autowired
    private ExportUtil exportUtil;
    @Autowired
    private R3OssConfig r3OssConfig;

    /**
     * 一次批量插入和更新的数量
     */
    public final static Integer UPDATE_INSERT_BATCH_SIZE = 300;

    @Value("${st.product.strategy.import.batch.size:500}")
    private Integer importBatchSize;

    /**
     * 正数数字正则表达式匹配
     */
    private final static String POSITIVE_NUMBER_REGEX = "^[+]{0,1}(\\d+)$|^[+]{0,1}(\\d+\\.\\d+)$";
    /**
     * 正整数正则表达式
     */
    private final static String POSITIVE_INTEGER_REGEX = "^[+]{0,1}(\\d+)$";

    /**
     * 下载模板
     *
     * @return
     */
    public ValueHolderV14 downloadTemp() {
        ValueHolderV14 vh = new ValueHolderV14(ResultCode.SUCCESS, "店铺商品特殊设置-多店铺比例明细导入-模板下载成功！");
        exportUtil.setEndpoint(r3OssConfig.getEndpoint());
        exportUtil.setAccessKeyId(r3OssConfig.getAccessKeyId());
        exportUtil.setAccessKeySecret(r3OssConfig.getAccessKeySecret());
        exportUtil.setBucketName(r3OssConfig.getBucketName());
        if (StringUtils.isEmpty(r3OssConfig.getTimeout())) {
            //如果获取不到apllo配置参数，设置默认过期时间为30分钟
            exportUtil.setTimeout("1800000");
        } else {
            exportUtil.setTimeout(r3OssConfig.getTimeout());
        }

        /**
         *  拼接Excel主表sheet表头字段
         * */
        String[] mainNames = {"店铺", "渠道仓", "库存比例", "熔断值", "商品编码", "条码"};
        String[] mustNames = {"店铺", "渠道仓", "库存比例", "熔断值"};
        String[] orderKeys = {"shopCodes", "channelCode", "stockRates", "lowStock", "proCode", "skuCode"};

        User user = new UserImpl();
        ((UserImpl) user).setName("");
        List<String> mainList = Lists.newArrayList(mainNames);
        List<String> mustList = Lists.newArrayList(mustNames);
        List<String> orderKeyList = Lists.newArrayList(orderKeys);
        //生成Excel
        XSSFWorkbook hssfWorkbook = new XSSFWorkbook();
        exportUtil.executeSheet(hssfWorkbook, "店铺商品特殊设置明细数据", "", mainList, mustList,
                orderKeyList, Lists.newArrayList(), false);
        String putMsg = exportUtil.saveFileAndPutOss(hssfWorkbook, "店铺商品特殊设置-多店铺比例明细导入模板",
                user, "OSS-Bucket/EXPORT/StCProductStrategyItem/");
        vh.setData(putMsg);
        return vh;
    }

    /**
     * 导入多店铺比例明细
     * *注：店铺编码支持多个，按,进行分隔，一般可能会有几百家店铺，如果明细有上万条，那么生成的明细数据会有几百万条
     *
     * @param mainId     主表ID
     * @param importList 导入集合list
     * @param user       当前用户
     * @return 结果对象
     */
    public ValueHolder importMultiShopItem(Long mainId, List<ProductStrategyMultiShopImportRequest> importList,
                                           User user) {
        String batchno = (System.currentTimeMillis() + "" + (int) (Math.random() * 9000 + 1000));

        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("importMultiShopItem-导入多店铺比例明细,:{},importList:{},user:{},mainId/batchno="
                    , mainId, batchno), importList == null ? "null" : importList.size(), JSONObject.toJSONString(user));
        }
        ValueHolder vh = new ValueHolder();
        try {
            AssertUtils.notNull(mainId, "策略主表ID不能为空！");
            // 操作锁
            this.lockProductStrategy(mainId);

            // 基础参数校验
            ProductStrategyImpCheckResult checkResult = importDataCheck(mainId, importList);
            if (!checkResult.isOK()) {
                vh.put("code", ResultCode.FAIL);
                vh.put("message", "多店铺比例数据校验不通过！");
                vh.put("data", JSONArray.parseArray(JSON.toJSONString(checkResult.getErrorList())));
                return vh;
            }
            checkResult.setUser(user);
            Map<String, List<ProductStrategyMultiShopImportRequest>> reqMap = fillProAndSkuInfo(importList, checkResult);
            List<List<ProductStrategyMultiShopImportRequest>> pageReqList = new ArrayList<>();
            List<String> keyStrList = new ArrayList<>(reqMap.keySet());
            List<List<String>> pageKeyList = ListUtils.getPageList(keyStrList, importBatchSize);
            pageKeyList.forEach(subKeyList -> {
                if (CollectionUtils.isNotEmpty(subKeyList)) {
                    List<ProductStrategyMultiShopImportRequest> subReqList = new ArrayList<>();
                    subKeyList.forEach(subKey -> {
                        subReqList.addAll(reqMap.get(subKey));
                    });
                    pageReqList.add(subReqList);
                }
            });
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("threads:{},importMultiShopItem-导入多店铺比例明细-完成数据分页，准备分页start-多线程,batchno=",
                        batchno), pageReqList.size());
            }
            ProductStrategyImportService service = ApplicationContextHandle.getBean(ProductStrategyImportService.class);
            List<ProductStrategyImportTask> callableTasks = new ArrayList<>();
            // 分页后的数据进行多线程
            pageReqList.forEach(reqList -> {
                callableTasks.add(new ProductStrategyImportTask(reqList, checkResult, service, batchno));
            });

            // 多线程汇总的处理的异常
            List<String> dealErrorList = taskExecutorService.invokeAll(callableTasks);
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("importMultiShopItem-导入多店铺比例明细-多线程执行-end,batchno=", batchno));
            }
            if (dealErrorList.size() > 0) {
                vh.put("code", ResultCode.FAIL);
                vh.put("message", "多店铺比例-后台数据导入处理出现异常！");
                vh.put("data", JSONArray.parseArray(JSON.toJSONString(dealErrorList)));
                return vh;
            } else {
                vh.put("code", ResultCode.SUCCESS);
                vh.put("message", "多店铺比例-后台数据导入处理成功！");
            }
            // 更新主表信息
            service.updateProductStrategy(user, checkResult);
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("importMultiShopItem-导入多店铺比例明细-更新主表和ES-end,batchno=", batchno));
            }
        } catch (Exception e) {
            vh.put("code", ResultCode.FAIL);
            vh.put("message", "导入多店铺比例明细异常：" + e.getMessage());
            return vh;
        } finally {
            unLockProductStrategy(mainId);
        }
        return vh;
    }

    /**
     * 解锁策略
     *
     * @param mainId
     */
    private void unLockProductStrategy(Long mainId) {
        if (mainId != null && mainId > 0) {
            CusRedisTemplate<String, String> redisTemplate = RedisOpsUtil.getStrRedisTemplate();
            String lockKsy = StConstant.TAB_ST_C_PRODUCT_STRATEGY + ":" + mainId;
            if (redisTemplate.opsForValue().get(lockKsy) != null) {
                redisTemplate.delete(lockKsy);
            }
        }
    }

    /**
     * 加锁策略
     *
     * @param mainId
     */
    private void lockProductStrategy(Long mainId) {
        CusRedisTemplate<String, String> redisTemplate = RedisOpsUtil.getStrRedisTemplate();
        String lockKsy = StConstant.TAB_ST_C_PRODUCT_STRATEGY + ":" + mainId;
        Boolean blnCanInit = redisTemplate.opsForValue().setIfAbsent(lockKsy, "OK");
        // 由于导入时间比较长，锁住1小时
        if (blnCanInit != null && blnCanInit) {
            redisTemplate.expire(lockKsy, 30, TimeUnit.MINUTES);
        } else {
            AssertUtils.logAndThrow("当前单据操作中，请稍后重试...");
        }
    }

    /**
     * 更新主表信息
     *
     * @param user
     * @param checkResult
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateProductStrategy(User user, ProductStrategyImpCheckResult checkResult) {
        try {
            // 更新主表
            StCProductStrategyDO strategyDO = strategyMapper.selectById(checkResult.getProductStrategyDO().getId());
            StBeanUtils.makeModifierField(strategyDO, user);
            strategyMapper.updateById(strategyDO);
            //做更新的需要先查询更新后数据库的实体在推ES
            DatasToEsUtils.insertProductEsData(strategyDO, null,
                    StConstant.TAB_ST_C_PRODUCT_STRATEGY);
        } catch (Exception ex) {
            log.debug(StConstant.TAB_ST_C_PRODUCT_STRATEGY + "店铺商品特殊设置主表更新数据至ES失败：" + ex.toString());
        }
    }

    /**
     * 导入数据检查校验
     *
     * @param mainId     主表ID
     * @param importList 导入数据集合
     */
    private ProductStrategyImpCheckResult importDataCheck(Long mainId,
                                                          List<ProductStrategyMultiShopImportRequest> importList) {
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("importMultiShopItem-导入多店铺比例明细-importDataCheck-导入校验开始"));
        }
        ProductStrategyImpCheckResult checkResult = new ProductStrategyImpCheckResult();
        StCProductStrategyDO strategyDO = strategyMapper.selectById(mainId);
        AssertUtils.notNull(strategyDO, "根据mainId查询不到策略信息！");
        AssertUtils.isTrue(StConstant.ISACTIVE_Y.equals(strategyDO.getIsactive()), "该策略已作废，不允许导入修改！");
        AssertUtils.isTrue(StConstant.CON_BILL_STATUS_01.equals(strategyDO.getEstatus()),
                "该策略状态不是未审核，不允许导入修改！");
        checkResult.setProductStrategyDO(strategyDO);

        // 解析Excel校验
        AssertUtils.notEmpty(importList, "解析导入数据excel为空！");
        List<String> errorList = Lists.newArrayList();

        Set<String> shopCodeSet = new HashSet<>();
        Set<String> channelCodeSet = new HashSet<>();
        Set<String> proCodeSet = new HashSet<>();
        Set<String> skuCodeSet = new HashSet<>();
        Map<String, String> matchProAndSkuMap = new HashMap<>();

        // 校验基础空、数据格式
        checkNullAndFormat(importList, errorList, shopCodeSet, channelCodeSet, proCodeSet, skuCodeSet, matchProAndSkuMap);

        // 校验店铺编码在数据库中是否存在
        ValueHolderV14<List<CpShop>> shopCodeVH = rpcCpService.getCpShopListByCodes(new ArrayList<>(shopCodeSet));
        AssertUtils.isTrue(shopCodeVH.isOK(), "调用CP-EXT接口查询店铺信息失败：" + shopCodeVH.getMessage());
        List<CpShop> shopList = shopCodeVH.getData();
        if (CollectionUtils.isEmpty(shopList)) {
            errorList.add("店铺编码异常，根据店铺编码查询不到店铺信息！");
        } else {
            Map<String, CpShop> shopMap = shopList.stream().collect(Collectors.toMap(CpShop::getEcode, v -> v,
                    (v1, v2) -> v1));
            checkResult.setShopMap(shopMap);
            List<String> shopErrorCodes = shopCodeSet.stream().filter(s -> !shopMap.containsKey(s))
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(shopErrorCodes)) {
                errorList.add("店铺编码找不到店铺信息：" + StringUtils.join(shopErrorCodes, ","));
            }
        }

        // 校验渠道编码在数据库中是否存在
        CpCOrgChannelQueryCmdRequest channelRequest = new CpCOrgChannelQueryCmdRequest();
        channelRequest.setEcodeList(new ArrayList<>(channelCodeSet));
        List<CpCOrgChannelEntity> channelList = rpcCpService.getChannel(channelRequest);
        if (CollectionUtils.isEmpty(channelList)) {
            errorList.add("渠道编码异常，根据渠道编码查询不到渠道信息！");
        } else {
            Map<String, CpCOrgChannelEntity> channelMap = channelList.stream().collect(Collectors.toMap(
                    CpCOrgChannelEntity::getEcode, v -> v, (v1, v2) -> v1));
            checkResult.setChannelMap(channelMap);
            List<String> channelErrorCodes = channelCodeSet.stream().filter(s -> !channelMap.containsKey(s))
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(channelErrorCodes)) {
                errorList.add("渠道编码找不到渠道信息：" + StringUtils.join(channelErrorCodes, ","));
            }
        }

        List<PsCPro> proList = new ArrayList<>();
        // 校验商品编码在数据库中是否存在
        if (CollectionUtils.isNotEmpty(proCodeSet)) {
            proList = rpcPsService.queryProByEcodes(new ArrayList<>(proCodeSet));
            if (CollectionUtils.isEmpty(proList)) {
                errorList.add("商品编码异常，根据商品编码查询不到商品信息！");
            } else {
                Map<String, PsCPro> proMap = proList.stream().collect(Collectors.toMap(
                        PsCPro::getEcode, v -> v, (v1, v2) -> v1));
                checkResult.setProMap(proMap);
                List<String> proErrorCodes = proCodeSet.stream().filter(s -> !proMap.containsKey(s))
                        .collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(proErrorCodes)) {
                    errorList.add("商品编码找不到商品信息：" + StringUtils.join(proErrorCodes, ","));
                }
            }
        }

        // 校验SKU编码在数据库中是否存在
        if (CollectionUtils.isNotEmpty(skuCodeSet)) {
            List<PsCSku> skuList = rpcPsService.querySkuByEcodes(new ArrayList<>(skuCodeSet));
            if (CollectionUtils.isEmpty(skuList)) {
                errorList.add("条码异常，根据条码查询不到商品SKU（条码）信息！");
            } else {
                Map<String, PsCSku> skuMap = skuList.stream().collect(Collectors.toMap(
                        PsCSku::getEcode, v -> v, (v1, v2) -> v1));
                checkResult.setSkuMap(skuMap);
                List<String> skuErrorCodes = skuCodeSet.stream().filter(s -> !skuMap.containsKey(s))
                        .collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(skuErrorCodes)) {
                    errorList.add("条码找不到条码信息：" + StringUtils.join(skuErrorCodes, ","));
                }
            }
        }

        // 如果商品编码不为空，则取出商品编码对应的skuMap集合
        if (CollectionUtils.isNotEmpty(proList)) {
            List<Long> proIds = proList.stream().map(PsCPro::getId).collect(Collectors.toList());
            Map<String, List<PsCSku>> proSkuMap = rpcPsService.querySkuByProIds(proIds);
            if (proSkuMap == null) {
                errorList.add("根据商品编码查询商品条码信息失败！");
            } else {
                // 如果是商品编码，查询商品对应的所有SKU信息
                checkResult.setProSkuMap(proSkuMap);
                Map<String, PsCSku> skuMap = checkResult.getSkuMap();
                if (skuMap == null) {
                    skuMap = new HashMap<>();
                    checkResult.setSkuMap(skuMap);
                }
                // 将查到的Sku信息加入到SkuMap中
                for (String key : proSkuMap.keySet()) {
                    List<PsCSku> skus = proSkuMap.get(key);
                    if (CollectionUtils.isNotEmpty(skus)) {
                        for (PsCSku sku : skus) {
                            skuMap.put(sku.getEcode(), sku);
                        }
                    }
                }
            }
        }

        // 如果商品编码和SKU都有值，则校验匹配关系
        if (!matchProAndSkuMap.isEmpty() && checkResult.getSkuMap() != null) {
            StringBuffer matchErrorStr = new StringBuffer();
            for (String proCode : matchProAndSkuMap.keySet()) {
                String skuCode = matchProAndSkuMap.get(proCode);
                if (checkResult.getSkuMap().containsKey(skuCode)) {
                    String proCode2 = checkResult.getSkuMap().get(skuCode).getPsCProEcode();
                    if (proCode2 != null && !proCode.equals(proCode2)) {
                        matchErrorStr.append("[商品编码：" + proCode + "和条码：" + skuCode + "不匹配！]");
                    }
                }
            }
            if (matchErrorStr.length() > 1) {
                errorList.add(matchErrorStr.toString());
            }
        }
        checkResult.setErrorList(errorList);
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("importMultiShopItem-导入多店铺比例明细-importDataCheck-导入校验结束"));
        }
        return checkResult;
    }

    /**
     * 校验基础空、数据格式
     *
     * @param importList        导入数据
     * @param errorList         错误信息集合
     * @param shopCodeSet       店铺编码
     * @param channelCodeSet    渠道编码
     * @param proCodeSet        商品编码
     * @param skuCodeSet        sku编码
     * @param matchProAndSkuMap 匹配商品编码和sku信息map
     */
    private void checkNullAndFormat(List<ProductStrategyMultiShopImportRequest> importList, List<String> errorList, Set<String> shopCodeSet, Set<String> channelCodeSet, Set<String> proCodeSet, Set<String> skuCodeSet, Map<String, String> matchProAndSkuMap) {
        for (int i = 0; i < importList.size(); i++) {
            ProductStrategyMultiShopImportRequest request = importList.get(i);
            StringBuilder sb = new StringBuilder("");
            int shopCodeSize = 0;
            int stockRateSize = 0;
            if (checkRequestNull(request)) {
                continue;
            }
            // 校验店铺
            if (StringUtils.isBlank(request.getShopCodes())) {
                sb.append("[店铺不能为空！]");
            } else {
                String[] shopCodeArray = request.getShopCodes().split(",");
                shopCodeSize = shopCodeArray.length;
                shopCodeSet.addAll(Arrays.asList(shopCodeArray));
            }
            // 校验渠道仓
            if (StringUtils.isBlank(request.getChannelCode())) {
                sb.append("[渠道仓不能为空！]");
            } else {
                channelCodeSet.add(request.getChannelCode());
            }
            // 校验库存比例
            if (StringUtils.isBlank(request.getStockRates())) {
                sb.append("[库存比例不能为空！]");
            } else {
                String[] stockRates = request.getStockRates().split(",");
                stockRateSize = stockRates.length;
                if (shopCodeSize != stockRateSize) {
                    sb.append("[店铺数量" + shopCodeSize + "和库存比例数量" + stockRateSize + "不匹配！]");
                }

                // 数字校验
                for (String stockRate : stockRates) {
                    if (stockRate.matches(POSITIVE_NUMBER_REGEX)) {
                        BigDecimal stockRateBigDecimal = new BigDecimal(stockRate);
                        if (stockRateBigDecimal.compareTo(BigDecimal.ZERO) < 0
                                || stockRateBigDecimal.compareTo(BigDecimal.valueOf(100)) > 0) {
                            sb.append("[库存比例必须在0~100的区间内，非法比例字符：" + stockRate + "！]");
                        }
                    } else {
                        sb.append("[库存比例存在非数字字符：" + stockRate + "！]");
                    }
                }
            }
            // 校验熔断值
            if (StringUtils.isBlank(request.getLowStock())) {
                sb.append("[熔断值不能为空！]");
            } else {
                if (request.getLowStock().matches(POSITIVE_INTEGER_REGEX)) {
                    BigDecimal stockRateBigDecimal = new BigDecimal(request.getLowStock());
                    if (stockRateBigDecimal.compareTo(BigDecimal.ZERO) < 0) {
                        sb.append("[熔断值不能小于0，非法比例字符：" + request.getLowStock() + "！]");
                    }
                } else {
                    sb.append("[熔断值非数字字符：" + request.getLowStock() + "！]");
                }
            }

            // 校验商品编码
            if (StringUtils.isBlank(request.getProCode()) && StringUtils.isBlank(request.getSkuCode())) {
                sb.append("[商品编码和条码不能同时为空！]");
            } else {
                if (StringUtils.isNotBlank(request.getProCode())) {
                    proCodeSet.add(request.getProCode());
                }
                if (StringUtils.isNotBlank(request.getSkuCode())) {
                    skuCodeSet.add(request.getSkuCode());
                }
                // 如果两个都不为空，则校验匹配关系
                if (StringUtils.isNotBlank(request.getProCode()) && StringUtils.isNotBlank(request.getSkuCode())) {
                    matchProAndSkuMap.put(request.getProCode(), request.getSkuCode());
                }
            }
            if (sb.length() > 1) {
                sb.insert(0, "数据行第" + (i + 1) + "行：");
                errorList.add(sb.toString());
            }
        }
        //去除空值
        Iterator<ProductStrategyMultiShopImportRequest> iterator = importList.iterator();
        while (iterator.hasNext()) {
            ProductStrategyMultiShopImportRequest req = iterator.next();
            if (checkRequestNull(req)) {
                iterator.remove();
            }
        }
    }

    /**
     * <AUTHOR>
     * @Description //校验ProductStrategyMultiShopImportRequest中的值是不是全部都为空
     *                 全为空返回true 否则返回false
     * @Date 18:18 2020/12/26
     * @param request
     * @return boolean
     **/
    private boolean checkRequestNull(ProductStrategyMultiShopImportRequest request) {
        if(StringUtils.isEmpty(request.getSkuCode()) && StringUtils.isEmpty(request.getProCode()) &&
                StringUtils.isEmpty(request.getLowStock()) && StringUtils.isEmpty(request.getStockRates()) &&
                StringUtils.isEmpty(request.getShopCodes()) && StringUtils.isEmpty(request.getChannelCode())) {
            return true;
        }
        return false;
    }

    @Transactional(rollbackFor = Exception.class)
    public void saveOrUpdateStrategyItem(List<ProductStrategyMultiShopImportRequest> importList,
                                         ProductStrategyImpCheckResult checkResult, String batchno) {
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("saveOrUpdateStrategyItem-批量保存多店铺商品比例明细，请求对象:{},batchno=", batchno),
                    JSONObject.toJSONString(importList));
        }
        List<StCProductStrategyItemDO> insertItemList = new ArrayList<>();
        List<StCProductStrategyItemDO> updateItemList = new ArrayList<>();
        // 当前操作对象
        User user = checkResult.getUser();
        Long mainId = checkResult.getProductStrategyDO().getId();
        //主表信息
        StCProductStrategyDO strategyDO = checkResult.getProductStrategyDO();
        // 插入防重复校验
        List<String> uqKeyList = new ArrayList<>();
        // 拼装需要保存的数据
        for (int i = 0; i < importList.size(); i++) {
            ProductStrategyMultiShopImportRequest request = importList.get(i);
            String[] shopCodes = request.getShopCodes().split(",");
            String[] stockRates = request.getStockRates().split(",");
            List<PsCSku> skuList;
            if (StringUtils.isNotBlank(request.getSkuCode())) {
                //  如果条码有值，就用条码的
                PsCSku psCSku = checkResult.getSkuMap().get(request.getSkuCode());
                skuList = new ArrayList<>();
                skuList.add(psCSku);
            } else {
                // 商品编码，需要把商品下所有的SKU进行保存
                skuList = checkResult.getProSkuMap().get(request.getProCode());
            }
            // 渠道信息
            CpCOrgChannelEntity channel = checkResult.getChannelMap().get(request.getChannelCode());
            // 熔断值
            Long lowStock = Long.valueOf(request.getLowStock());
            for (int j = 0; j < shopCodes.length; j++) {
                String shopCode = shopCodes[j];
                // 店铺信息
                CpShop cpShop = checkResult.getShopMap().get(shopCode);
                // 店铺对应的比例
                BigDecimal stockRate = new BigDecimal(stockRates[j]);
                for (PsCSku psCSku : skuList) {
                    String key = cpShop.getId() + "_" + channel.getId() + "_" + psCSku.getId();
                    if (checkResult.getExistsStrategyItemMap().containsKey(key)) {
                        StCProductStrategyItemDO existItem = checkResult.getExistsStrategyItemMap().get(key);
                        existItem.setBeforeUpdateStockRate(existItem.getStockScale());
                        existItem.setStockScale(stockRate);
                        existItem.setLowStock(lowStock);
                        existItem.setPlanName(strategyDO.getPlanName());
                        existItem.setStatus(strategyDO.getEstatus());
                        existItem.setBeginTime(strategyDO.getBeginTime());
                        existItem.setEndTime(strategyDO.getEndTime());
                        existItem.setMainCreationdate(strategyDO.getCreationdate());
                        StBeanUtils.makeModifierField(existItem, user);
                        updateItemList.add(existItem);
                    } else {
                        if (!uqKeyList.contains(key)) {
                            StCProductStrategyItemDO newItem = buildStrategyItemDO(user, mainId, channel, psCSku, lowStock, shopCode, stockRate, cpShop);
                            newItem.setPlanName(strategyDO.getPlanName());
                            newItem.setStatus(strategyDO.getEstatus());
                            newItem.setBeginTime(strategyDO.getBeginTime());
                            newItem.setEndTime(strategyDO.getEndTime());
                            newItem.setMainCreationdate(strategyDO.getCreationdate());
                            insertItemList.add(newItem);
                            uqKeyList.add(key);
                        }
                    }
                }
            }
        }
        // 所有的明细集合
        List<StCProductStrategyItemDO> allItems = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(insertItemList)) {
            strategyItemService.saveBatch(insertItemList, UPDATE_INSERT_BATCH_SIZE);
            allItems.addAll(insertItemList);
        }
        if (CollectionUtils.isNotEmpty(updateItemList)) {
            strategyItemService.updateBatchById(updateItemList, UPDATE_INSERT_BATCH_SIZE);
            allItems.addAll(updateItemList);
        }
        try {
            // 明细ES推送
            DatasToEsUtils.batchInsertProductEsData(strategyDO, allItems);
        } catch (Exception e) {
            log.error(LogUtil.format("saveOrUpdateStrategyItem-商品特殊设置-明细批量推送ES失败：{},batchno=", batchno)
                    , Throwables.getStackTraceAsString(e));
            throw new NDSException("商品特殊设置-明细批量推送ES失败：" + e.getMessage());
        }
    }

    /**
     * 构建策略明细
     *
     * @param user      操作用户
     * @param mainId    主表ID
     * @param channel   渠道信息
     * @param psCSku    条码信息
     * @param lowStock  安全库存
     * @param shopCode  店铺编码
     * @param stockRate 库存比例
     * @param cpShop    店铺信息
     * @return 策略明细信息
     */
    private StCProductStrategyItemDO buildStrategyItemDO(User user, Long mainId, CpCOrgChannelEntity channel,
                                                         PsCSku psCSku, Long lowStock, String shopCode,
                                                         BigDecimal stockRate, CpShop cpShop) {
        StCProductStrategyItemDO item = new StCProductStrategyItemDO();
        item.setId(ModelUtil.getSequence(StConstant.TAB_ST_C_PRODUCT_STRATEGY_ITEM));
        item.setStCProductStrategyId(mainId);
        StBeanUtils.makeCreateField(item, user);
        item.setStockScale(stockRate);
        item.setLowStock(lowStock);
        // item.setBeforeUpdateStockRate();
        // 渠道信息
        item.setCpCOrgChannelId(channel.getId());
        item.setCpCOrgChannelEcode(channel.getEcode());
        item.setCpCOrgChannelEname(channel.getEname());
        // 店铺信息
        item.setCpCShopId(cpShop.getCpCShopId());
        item.setCpCShopEcode(shopCode);
        item.setCpCShopTitle(cpShop.getCpCShopTitle());
        // 商品和SKU信息
        item.setPsCProId(psCSku.getPsCProId());
        item.setPsCProEcode(psCSku.getPsCProEcode());
        item.setPsCProEname(psCSku.getPsCProEname());
        item.setPsCSkuEcode(psCSku.getEcode());
        item.setPsCSkuId(psCSku.getId());
        return item;
    }

    /**
     * 为满足线程拆分要求，填充完成商品和sku信息并返回拆分后Map信息集合
     *
     * @param importList  导入请求数据
     * @param checkResult 校验数据
     * @return 拆分后的信息Map
     */
    private Map<String, List<ProductStrategyMultiShopImportRequest>> fillProAndSkuInfo(
            List<ProductStrategyMultiShopImportRequest> importList, ProductStrategyImpCheckResult checkResult) {
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("importMultiShopItem-导入多店铺比例明细-fillProAndSkuInfo-start"));
        }
        // 查询旧的数据
        List<StCProductStrategyItemDO> existItemList = strategyItemService.
                queryItemListByStrategyId(checkResult.getProductStrategyDO().getId());
        Map<String, StCProductStrategyItemDO> existsStrategyItemMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(existItemList)) {
            existItemList.forEach(i -> {
                existsStrategyItemMap.put(i.getCpCShopId() + "_" + i.getCpCOrgChannelId() + "_" + i.getPsCSkuId(), i);
            });
        }
        checkResult.setExistsStrategyItemMap(existsStrategyItemMap);

        // 补充所有的商品编码和SKU编码（用于多线程时能更好均分，避免一条明细数据落到多个线程中）
        List<ProductStrategyMultiShopImportRequest> fullImportReqList = new ArrayList<>();
        Map<String, List<ProductStrategyMultiShopImportRequest>> reqMap = new HashMap<>();
        importList.forEach(p -> {
            if (StringUtils.isNotBlank(p.getSkuCode())) {
                ProductStrategyMultiShopImportRequest newReq = new ProductStrategyMultiShopImportRequest();
                BeanUtils.copyProperties(p, newReq);
                newReq.setProCode(checkResult.getSkuMap().get(p.getSkuCode()).getPsCProEcode());
                fullImportReqList.add(newReq);
                fillReqMap(reqMap, newReq);
            } else {
                List<PsCSku> list = checkResult.getProSkuMap().get(p.getProCode());
                if (CollectionUtils.isNotEmpty(list)) {
                    list.forEach(sku -> {
                        ProductStrategyMultiShopImportRequest newReq = new ProductStrategyMultiShopImportRequest();
                        BeanUtils.copyProperties(p, newReq);
                        newReq.setSkuCode(sku.getEcode());
                        fullImportReqList.add(newReq);
                        fillReqMap(reqMap, newReq);
                    });
                }
            }
        });
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("importMultiShopItem-导入多店铺比例明细-fillProAndSkuInfo-end"));
        }
        return reqMap;
    }


    /**
     * 填充请求Map，按拆分键存储
     *
     * @param reqMap 请求Map
     * @param newReq 请求对象
     */
    private void fillReqMap(Map<String, List<ProductStrategyMultiShopImportRequest>> reqMap,
                            ProductStrategyMultiShopImportRequest newReq) {
        // 通过渠道编码+商品编码+sku编码拆分线程，构造数据
        if (reqMap.containsKey(newReq.getThreadSplitKey())) {
            List<ProductStrategyMultiShopImportRequest> subList = reqMap.get(newReq.getThreadSplitKey());
            subList.add(newReq);
        } else {
            List<ProductStrategyMultiShopImportRequest> subList = new ArrayList<>();
            subList.add(newReq);
            reqMap.put(newReq.getThreadSplitKey(), subList);
        }
    }
}
