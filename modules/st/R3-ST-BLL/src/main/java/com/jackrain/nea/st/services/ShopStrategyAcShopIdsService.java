package com.jackrain.nea.st.services;

import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.st.mapper.StCShopStrategyMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @author: 陈俊明
 * @since: 2019-04-24
 * @create at : 2019-04-24 11:47
 */

@Component
@Slf4j
public class ShopStrategyAcShopIdsService {
    @Autowired
    private StCShopStrategyMapper stCShopStrategyMapper;

    public List<Long> SelectShopIdsByIsWriteoff(String isactive, int isWriteoff) throws NDSException {
        return stCShopStrategyMapper.SelectShopIdsByIsWriteoff(isactive, isWriteoff);
    }
}
