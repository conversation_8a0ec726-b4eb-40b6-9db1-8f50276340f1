package com.jackrain.nea.st.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.st.common.StConstant;
import com.jackrain.nea.st.mapper.StCAutoInvoiceMapper;
import com.jackrain.nea.st.model.table.StCAutoInvoiceDO;
import com.jackrain.nea.st.utils.StBeanUtils;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.Date;
import java.util.HashMap;

/**
 * <AUTHOR>
 * @Date 2019/9/2 16:02
 */
@Component
@Slf4j
public class AutoInvoiceVoidService extends CommandAdapter {
    @Autowired
    private StCAutoInvoiceMapper mapper;

    @Override
    public ValueHolder execute(QuerySession session) throws NDSException {
        DefaultWebEvent event = session.getEvent();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(
                event.getParameterValue("param"), "yyyy-MM-dd HH:mm:ss",
                SerializerFeature.WriteMapNullValue), Feature.OrderedField);
        HashMap<Long, Object> errMap = new HashMap<Long, Object>();
        //生成Json数组
        JSONArray voidArray = StBeanUtils.makeVoidJsonArray(param);
        //列表批量
        if (!CollectionUtils.isEmpty(voidArray)) {
            for (int i = 0; i < voidArray.size(); i++) {
                //遍历 jsonarray 数组，把每一个对象转成 json 对象
                Long id = Long.valueOf(voidArray.get(i).toString());
                try {
                    voidAutoInvoice(id, session.getUser());
                } catch (Exception ex) {
                    errMap.put(id, ex.getMessage());
                }
            }
        }
        //返回日志
        return StBeanUtils.getExcuteValueHolder(voidArray.size(), errMap);
    }

    /**
     * 作废
     *
     * @param id
     * @param user
     * @return com.jackrain.nea.util.ValueHolder
     */
    private void voidAutoInvoice(long id, User user) {
        //验证
        checkAutoInvoice(id);
        //作废
        StCAutoInvoiceDO autoInvoice = new StCAutoInvoiceDO();
        autoInvoice.setId(id);
        autoInvoice.setIsactive(StConstant.ISACTIVE_N);//作废状态
        autoInvoice.setDelname(user.getName());//作废人用户名
        autoInvoice.setDelid(Long.valueOf(user.getId()));//作废人
        autoInvoice.setDelename(user.getEname());//作废人姓名
        autoInvoice.setDelTime(new Date());//作废时间
        StBeanUtils.makeModifierField(autoInvoice, user);
        int update = mapper.updateById(autoInvoice);
        if (update < 0) {
            throw new NDSException("作废失败！");
        }
    }

    /**
     * @param id
     * @return java.lang.String
     */
    private void checkAutoInvoice(long id) {
        StCAutoInvoiceDO autoInvoice = mapper.selectById(id);
        if (autoInvoice == null) {
            throw new NDSException("当前记录已不存在！");
        } else {
            if (autoInvoice.getIsactive().equals(StConstant.ISACTIVE_N)) {
                throw new NDSException("当前记录已作废，不允许重复作废！");
            }
        }
    }
}
