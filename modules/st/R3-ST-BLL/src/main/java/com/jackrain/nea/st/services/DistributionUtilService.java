package com.jackrain.nea.st.services;

import com.jackrain.nea.st.common.EsConstant;
import com.jackrain.nea.st.model.esModel.StCDistributionEs;
import com.jackrain.nea.st.model.table.StCDistributionCustomerDO;
import com.jackrain.nea.st.model.table.StCDistributionDO;
import com.jackrain.nea.st.utils.EsUtils;
import com.jackrain.nea.st.utils.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @Descroption 分销代销工具类
 * <AUTHOR>
 * @Date 2019/5/24 16:05
 */
@Component
@Slf4j
public class DistributionUtilService {

    public StCDistributionEs getStCDistributionEs(StCDistributionDO distribution){
        StCDistributionEs distributionEs = new StCDistributionEs();
        BeanUtils.copyProperties(distribution, distributionEs);
        if (distribution.getCpCCustomerIdSet() != null) {
            List<Long> cIds = JsonUtils.getIds(distribution.getCpCCustomerIdSet());
            distributionEs.setCpCCustomerIdSet(cIds);
        }
        return distributionEs;
    }
    public void createEsIndex() {
        EsUtils.commonCreateEsIndex(EsConstant.ST_C_DISTRIBUTION_INDEX,StCDistributionDO.class,StCDistributionCustomerDO.class);
    }
}
