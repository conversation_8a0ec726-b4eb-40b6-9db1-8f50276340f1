package com.jackrain.nea.st.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.st.common.StConstant;
import com.jackrain.nea.st.mapper.StCOwnershipWarehouseSetMapper;
import com.jackrain.nea.st.model.table.StCOwnershipWarehouseSetDO;
import com.jackrain.nea.st.utils.StBeanUtils;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.Date;
import java.util.HashMap;

/**
 * @author: 汪聿森
 * @Date: Created in 2020-02-19 10:39
 * @Description : 库存归属仓库属性设置作废
 */
@Component
@Slf4j
@Transactional
public class OwnershipWarehouseSetVoidService extends CommandAdapter {
    @Autowired
    private StCOwnershipWarehouseSetMapper stCOwnershipWarehouseSetMapper;

    @Override
    public ValueHolder execute(QuerySession session) throws NDSException {
        DefaultWebEvent event = session.getEvent();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(
                event.getParameterValue("param"), "yyyy-MM-dd HH:mm:ss",
                SerializerFeature.WriteMapNullValue), Feature.OrderedField);
        HashMap<Long, Object> errMap = new HashMap<Long, Object>();
        //生成Json数组
        JSONArray voidArray = StBeanUtils.makeVoidJsonArray(param);
        //列表批量
        if (!CollectionUtils.isEmpty(voidArray)) {
            for (int i = 0; i < voidArray.size(); i++) {
                Long id = Long.valueOf(voidArray.get(i).toString());   // 遍历 jsonarray 数组，把每一个对象转成 json 对象
                try {
                    voidStCOwnershipWarehouseSet(id, session);
                } catch (Exception ex) {
                    errMap.put(id, ex.getMessage());
                }
            }
        }
        //返回日志
        return StBeanUtils.getExcuteValueHolder(voidArray.size(), errMap);
    }

    /**
     * 作废
     *
     * @param id
     * @param session
     * @return com.jackrain.nea.util.ValueHolder
     */
    private void voidStCOwnershipWarehouseSet(long id, QuerySession session) {
        //验证
        checkStCOwnershipWarehouseSet(id);
        //作废
        StCOwnershipWarehouseSetDO stCOwnershipWarehouseSetDO = new StCOwnershipWarehouseSetDO();
        stCOwnershipWarehouseSetDO.setId(id);
        stCOwnershipWarehouseSetDO.setIsactive(StConstant.ISACTIVE_N);//作废状态
        stCOwnershipWarehouseSetDO.setDelname(session.getUser().getName());//作废人用户名
        stCOwnershipWarehouseSetDO.setDelid(Long.valueOf(session.getUser().getId()));//作废人
        stCOwnershipWarehouseSetDO.setDelename(session.getUser().getName());//作废人姓名
        stCOwnershipWarehouseSetDO.setDelTime(new Date());//作废时间
        StBeanUtils.makeModifierField(stCOwnershipWarehouseSetDO, session.getUser());
        int update = stCOwnershipWarehouseSetMapper.updateById(stCOwnershipWarehouseSetDO);
        if (update < 0) {
            throw new NDSException("作废失败！");
        }
    }

    /**
     * 验证
     *
     * @param id
     * @return java.lang.String
     */
    private void checkStCOwnershipWarehouseSet(long id) {
        StCOwnershipWarehouseSetDO stCOwnershipWarehouseSetDO = stCOwnershipWarehouseSetMapper.selectById(id);
        if (stCOwnershipWarehouseSetDO == null) {
            throw new NDSException("当前记录已不存在！");
        } else {
            if (stCOwnershipWarehouseSetDO.getIsactive().equals(StConstant.ISACTIVE_N)) {
                throw new NDSException("当前记录已作废，不允许重复作废！");
            }
        }
    }
}
