package com.jackrain.nea.st.services;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.st.common.StConstant;
import com.jackrain.nea.st.mapper.StCSplitReasonConfigMapper;
import com.jackrain.nea.st.mapper.StCSplitReasonMapper;
import com.jackrain.nea.st.model.request.StCSplitReasonRequest;
import com.jackrain.nea.st.model.table.StCSplitReasonConfigDO;
import com.jackrain.nea.st.utils.JsonUtils;
import com.jackrain.nea.st.utils.StBeanUtils;
import com.jackrain.nea.st.utils.ValueHolderUtils;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

/**
 * @program: r3-st
 * @description: 自定义拆单配置保存
 * @author: liuwj
 * @create: 2021-05-31 11:33
 **/
@Component
@Slf4j
@Transactional
public class StCSplitReasonConfigSaveService extends CommandAdapter {
    @Autowired
    StCSplitReasonConfigMapper stCSplitReasonConfigMapper;

    @Autowired
    StCSplitReasonMapper stCSplitReasonMapper;
    /**
     * <AUTHOR>
     * @Date 14:55 2021/5/31
     * @Description 保存服务
     */
    @Override
    public ValueHolder execute(QuerySession session) {
        DefaultWebEvent event = session.getEvent();
        List<String> nullKeyList = new ArrayList<>();
        JSONObject param = JsonUtils.filterNull((JSONObject) event.getParameterValue("param"), nullKeyList);
        if (param != null) {
            Long id = param.getLong("objid");
            JSONObject fixColumn = param.getJSONObject("fixcolumn");
            StCSplitReasonRequest stCSplitReasonRequest = JsonUtils.jsonParseClass(fixColumn, StCSplitReasonRequest.class);
            StCSplitReasonConfigDO stCSplitReasonConfigDO = stCSplitReasonRequest.getStCSplitReasonConfigDO();
            if (fixColumn != null && id != null) {
                if (id != -1) {
                    return updateSplitReasonConfig(session, id, stCSplitReasonConfigDO);
                } else {
                    return insertSplitReasonConfig(session, stCSplitReasonConfigDO);
                }
            }
        }
        return ValueHolderUtils.getFailValueHolder("当前记录已不存在！");
    }
    /**
     * <AUTHOR>
     * @Date 13:52 2021/5/31
     * @Description 自定义拆单配置新增
     */
    private ValueHolder insertSplitReasonConfig(QuerySession session, StCSplitReasonConfigDO stCSplitReasonConfigDO) {
        if (stCSplitReasonConfigDO == null){
            return ValueHolderUtils.getFailValueHolder("当前记录已不存在！");
        }
        if(log.isDebugEnabled()){
            log.info(getClass().getName()+"自定义拆单配置新增 param ：{}",stCSplitReasonConfigDO);
        }
        Long splitReasonConfigId = ModelUtil.getSequence(StConstant.TAB_ST_C_SPLIT_REASON_CONFIG);
        stCSplitReasonConfigDO.setId(splitReasonConfigId);
        ValueHolder holder =  checkSplitReasonConfig(stCSplitReasonConfigDO);
        if (holder != null) {
            return holder;
        }
        StBeanUtils.makeCreateField(stCSplitReasonConfigDO, session.getUser());
        if (stCSplitReasonConfigMapper.insert(stCSplitReasonConfigDO)<=0){
            return ValueHolderUtils.getFailValueHolder("保存失败");
        }
        return ValueHolderUtils.getSuccessValueHolder(splitReasonConfigId, StConstant.TAB_ST_C_SPLIT_REASON_CONFIG, "");
    }
    /**
     * <AUTHOR>
     * @Date 14:04 2021/5/31
     * @Description 自定义拆单配置校验
     */
    private ValueHolder checkSplitReasonConfig(StCSplitReasonConfigDO stCSplitReasonConfigDO) {
        if (stCSplitReasonConfigDO == null ){
            return null;
        }
        if(log.isDebugEnabled()){
            log.info(getClass().getName()+"自定义拆单配置校验 param ：{}",stCSplitReasonConfigDO);
        }
        if (stCSplitReasonConfigDO.getEcode() !=null){
            if (stCSplitReasonConfigMapper.selectCount(new QueryWrapper<StCSplitReasonConfigDO>()
                    .lambda().eq(StCSplitReasonConfigDO :: getEcode,stCSplitReasonConfigDO.getEcode())
                    .eq(StCSplitReasonConfigDO :: getIsactive,"Y"))>0){
                return ValueHolderUtils.getFailValueHolder("该编码已存在，请勿重复新增！");
            }
        }
        if (stCSplitReasonConfigDO.getCustomReason() !=null){
            if (stCSplitReasonConfigMapper.selectCount(new QueryWrapper<StCSplitReasonConfigDO>()
                    .lambda().eq(StCSplitReasonConfigDO :: getCustomReason,stCSplitReasonConfigDO.getCustomReason())
                    .eq(StCSplitReasonConfigDO :: getIsactive,"Y"))>0){
                return ValueHolderUtils.getFailValueHolder("该自定义拆单原因已存在，请勿重复新增！");
            }
        }
        return  null;
    }

    /**
     * <AUTHOR>
     * @Date 13:52 2021/5/31
     * @Description 自定义拆单配置修改
     */
    private ValueHolder updateSplitReasonConfig(QuerySession session, Long id, StCSplitReasonConfigDO stCSplitReasonConfigDO) {
        StCSplitReasonConfigDO stCSplitReasonConfig = stCSplitReasonConfigMapper.selectById(id);
        if (stCSplitReasonConfig == null){
            return ValueHolderUtils.getFailValueHolder("当前记录已不存在！");
        }
        if(log.isDebugEnabled()){
            log.info(getClass().getName()+"自定义拆单配置修改 param ：{}",stCSplitReasonConfigDO);
        }
        ValueHolder holder =  checkSplitReasonConfig(stCSplitReasonConfigDO);
        if (holder != null) {
            return holder;
        }
        stCSplitReasonConfigDO.setId(id);
        StBeanUtils.makeModifierField(stCSplitReasonConfigDO, session.getUser());
        if (stCSplitReasonConfigMapper.updateById(stCSplitReasonConfigDO)<=0){
            return ValueHolderUtils.getFailValueHolder("保存失败");
        }
        return ValueHolderUtils.getSuccessValueHolder(id, StConstant.TAB_ST_C_SPLIT_REASON_CONFIG, "");
    }
}
