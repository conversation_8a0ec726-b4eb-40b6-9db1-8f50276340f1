package com.jackrain.nea.st.intercept;

import com.google.common.base.Throwables;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.st.annotation.StOperationLog;
import com.jackrain.nea.st.services.LogService;
import com.jackrain.nea.sys.domain.BaseModel;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utility.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.AfterReturning;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.Ordered;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;
import java.util.concurrent.CompletableFuture;

/**
 * @Descroption 策略中心通用操作日志
 * <AUTHOR>
 * @Date 2020/2/7 15:50
 */
@Aspect
@Component
@Slf4j
public class OperationLogIntercept<T extends BaseModel> implements Ordered {
    @Autowired
    private LogService logService;

    /**
     * 日志切入点
     */
    @Pointcut("@annotation(com.jackrain.nea.st.annotation.StOperationLog)")
    public void logPointCut() {
    }

    @AfterReturning(value = "logPointCut()", returning = "returnValue")
    public void doAfter(JoinPoint joinPoint, Object returnValue) {
        /**
         * 解析Log注解
         */
        CompletableFuture.runAsync(() -> {
            try {
                String methodName = joinPoint.getSignature().getName();
                Method method = currentMethod(joinPoint, methodName);
                if (method == null) {
                    log.error(LogUtil.format("method is null"));
                    return;
                }
                StOperationLog logObj = method.getAnnotation(StOperationLog.class);
                //公共方法统一判断是否成功，对于定制的返回类型不一定是这种，需要后面自行判断
                if (logObj.configurationFlag()) {
                    ValueHolder valueHolder = getValueHolder(returnValue);
                    ValueHolderV14 valueHolderV14 = getValueHolderV14(returnValue);
                    if (valueHolder == null && valueHolderV14 == null) {
                        return;
                    }
                    if (valueHolder != null && ResultCode.SUCCESS != (int) valueHolder.getData().get("code")) {
                        return;
                    }
                    if (valueHolderV14 != null && ResultCode.SUCCESS != valueHolderV14.getCode()) {
                        return;
                    }
                }
                logService.saveLog(joinPoint, logObj, returnValue);
            } catch (Exception e) {
                log.error(LogUtil.format("LogService.saveLog,error:{}")
                        , Throwables.getStackTraceAsString(e));
            }
        });
    }

    private ValueHolder getValueHolder(Object param) {
        ValueHolder vh = null;
        if (param instanceof ValueHolder) {
            vh = (ValueHolder) param;
        }
        return vh;
    }

    private ValueHolderV14 getValueHolderV14(Object param) {
        ValueHolderV14 vh = null;
        if (param instanceof ValueHolderV14) {
            vh = (ValueHolderV14) param;
        }
        return vh;
    }

    private Method currentMethod(JoinPoint joinPoint, String methodName) {
        /**
         * 获取目标类的所有方法，找到当前要执行的方法
         */
        Method[] methods = joinPoint.getTarget().getClass().getMethods();
        Method resultMethod = null;
        for (Method method : methods) {
            if (method.getName().equals(methodName)) {
                resultMethod = method;
                break;
            }
        }
        return resultMethod;
    }

    @Override
    public int getOrder() {
        return 1002;
    }
}
