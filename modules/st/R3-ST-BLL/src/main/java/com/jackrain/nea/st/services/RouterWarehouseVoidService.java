package com.jackrain.nea.st.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.st.common.StConstant;
import com.jackrain.nea.st.mapper.StCRouterWarehouseMapper;
import com.jackrain.nea.st.model.table.StCRouterWarehouseDO;
import com.jackrain.nea.st.utils.StBeanUtils;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.HashMap;

/**
 * @Descroption 路由操作码策略
 * <AUTHOR>
 * @Date 2020/5/11 15:45
 */
@Component
@Slf4j
public class RouterWarehouseVoidService extends CommandAdapter {
    @Autowired
    private StCRouterWarehouseMapper mapper;

    @Override
    public ValueHolder execute(QuerySession querySession) throws NDSException {
        //1.获取传入参数
        DefaultWebEvent event = querySession.getEvent();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(event.getParameterValue("param"),
                "yyyy-MM-dd HH:mm:ss", SerializerFeature.WriteMapNullValue), Feature.OrderedField);
        if (param == null) {
            throw new NDSException("参数为空！");
        }
        log.debug(LogUtil.format("Start RouterWarehouseVoidService.execute. ReceiveParams: {}"), param.toJSONString());
        //2.定义错误记录数
        HashMap<Long, Object> errMap = new HashMap();
        //3.生成审核Json数组
        JSONArray voidArray = StBeanUtils.makeVoidJsonArray(param);
        for (int i = 0; i < voidArray.size(); i++) {
            Long id = voidArray.getLong(i);
            try {
                //4.遍历作废方法
                voidRouterWarehouse(id, querySession);
            } catch (Exception e) {
                errMap.put(id, e.getMessage());
            }
        }
        //5.返回ValueHolder对象
        return StBeanUtils.getExcuteValueHolder(voidArray.size(), errMap);
    }

    /**
     * @param id
     * @param querySession
     * @return void
     * @Descroption 检查并更新作废状态
     * <AUTHOR>
     * @Date 2020/5/11 15:45
     */
    public void voidRouterWarehouse(Long id, QuerySession querySession) {
        StCRouterWarehouseDO stCRouterWarehouse = mapper.selectById(id);
        checkDistribution(stCRouterWarehouse);
        //更新作废状态
        StBeanUtils.makeModifierField(stCRouterWarehouse, querySession.getUser());
        stCRouterWarehouse.setIsactive(StConstant.ISACTIVE_N);//作废
        setVoidCommonField(stCRouterWarehouse, querySession.getUser());
        int updateNum = mapper.updateById(stCRouterWarehouse);
        if (updateNum < 0) {
            throw new NDSException("作废失败！");
        }
    }

    private void checkDistribution(StCRouterWarehouseDO stCRouterWarehouse) {
        if (stCRouterWarehouse == null) {
            throw new NDSException("当前记录已不存在！");
        } else {
            if (StConstant.ISACTIVE_N.equals(stCRouterWarehouse.getIsactive())) {
                throw new NDSException("当前记录已作废，不允许重复作废！");
            }
        }
    }

    /**
     * @param stCRouterWarehouse
     * @param user
     * @return void
     * @Descroption 设置作废公共字段
     * <AUTHOR>
     * @Date 2020/5/11 15:45
     */
    private void setVoidCommonField(StCRouterWarehouseDO stCRouterWarehouse, User user) {
        stCRouterWarehouse.setDelid(Long.valueOf(user.getId()));
        stCRouterWarehouse.setDelename(user.getEname());
        stCRouterWarehouse.setDelname(user.getName());
        stCRouterWarehouse.setDelTime(new Date());
    }
}
