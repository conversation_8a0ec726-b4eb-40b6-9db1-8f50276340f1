package com.jackrain.nea.st.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.st.common.StConstant;
import com.jackrain.nea.st.mapper.StCVipUrgentStrategyMapper;
import com.jackrain.nea.st.model.table.StCVipUrgentStrategy;
import com.jackrain.nea.st.utils.StBeanUtils;
import com.jackrain.nea.st.utils.ValueHolderUtils;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;

/**
 * <AUTHOR> zhuxing
 * @Date : 2021-10-24 16:12
 * @Description : 唯品会补货加急策略 - 新增保存服务
 **/
@Component
@Slf4j
@Transactional(rollbackFor = {Exception.class})
public class StCVipUrgentStrategySaveService extends CommandAdapter {

    @Autowired
    private StCVipUrgentStrategyMapper stCVipUrgentStrategyMapper;

    @Override
    public ValueHolder execute(QuerySession querySession) throws NDSException {
        DefaultWebEvent event = querySession.getEvent();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(
                event.getParameterValue("param"), "yyyy-MM-dd HH:mm:ss"),
                Feature.OrderedField);
        log.info("StCVipUrgentStrategySaveService.execute.param:{}",JSON.toJSONString(param));
        Long id = param.getLong("objid");
        JSONObject fixColumn = param.getJSONObject("aftervalue") == null?param.getJSONObject("fixcolumn"):param.getJSONObject("aftervalue");
        if (fixColumn != null) {
            if (id != null && id > 0) {
                return updateUrgentStrategy(querySession, fixColumn, id);
            } else {
                return addUrgentStrategy(querySession, fixColumn);
            }
        }
        throw new NDSException("操作失败，参数信息为空！");
    }

    /**
     * 基础数据校验
     * @param stCVipUrgentStrategy
     */
    public void checkAddData(StCVipUrgentStrategy stCVipUrgentStrategy){
        String strategyTitle = stCVipUrgentStrategy.getStrategyTitle();
        Long shopId = stCVipUrgentStrategy.getCpCShopId();
        Date beginDate = stCVipUrgentStrategy.getBeginTime();
        Date endDate = stCVipUrgentStrategy.getEndTime();
        String remark = stCVipUrgentStrategy.getRemark();
        if(strategyTitle == null){
            throw new NDSException("方案名称为空！");
        }
        if(shopId == null){
            throw new NDSException("店铺为空！");
        }
        if(beginDate == null){
            throw new NDSException("开始时间为空！");
        }
        if(endDate == null){
            throw new NDSException("结束时间为空！");
        }
        if (beginDate.before(new Date())) {
            throw new NDSException("开始日期不能小于当前日期！");
        }
        if (endDate.before(beginDate)) {
            throw new NDSException("结束日期不能小于开始日期！");
        }
        if (remark == null) {
            throw new NDSException("备注为空！");
        }

    }

    /**
     * 新增
     * @param session
     * @param fixColumn
     * @return
     */
    private ValueHolder addUrgentStrategy(QuerySession session, JSONObject fixColumn) {
        ValueHolder valueHolder = new ValueHolder();
        String postfee = fixColumn.getString(StConstant.ST_C_VIP_URGENT_STRATEGY);
        StCVipUrgentStrategy stCVipUrgentStrategy = JSON.parseObject(postfee, StCVipUrgentStrategy.class);

        //基础数据校验
        checkAddData(stCVipUrgentStrategy);
        stCVipUrgentStrategy.setId(ModelUtil.getSequence(StConstant.ST_C_VIP_URGENT_STRATEGY));
        //基本字段值设置
        StBeanUtils.makeCreateField(stCVipUrgentStrategy, session.getUser());
        stCVipUrgentStrategyMapper.insert(stCVipUrgentStrategy);
        valueHolder=  ValueHolderUtils.getSuccessValueHolder(stCVipUrgentStrategy.getId(), StConstant.ST_C_VIP_URGENT_STRATEGY);
        return valueHolder;
    }

    /**
     * 修改数据校验
     * @param obj
     * @param id
     */
    public void checkUpdateData(JSONObject obj,Long id){
        Date beginDate = null;
        Date endDate = null;
        //查询记录是否存在
        StCVipUrgentStrategy oldStrategy = stCVipUrgentStrategyMapper.selectById(id);
        if(oldStrategy == null){
            throw new NDSException("当前记录不存在！");
        }
        beginDate = oldStrategy.getBeginTime();
        endDate = oldStrategy.getEndTime();
        if(obj.containsKey("STRATEGY_TITLE") && obj.getString("STRATEGY_TITLE") == null){
            throw new NDSException("方案名称为空！");
        }
        if(obj.containsKey("CP_C_SHOP_ID") && obj.getLong("CP_C_SHOP_ID") == null){
            throw new NDSException("店铺为空！");
        }
        if(obj.containsKey("BEGIN_TIME")){
            if(obj.getDate("BEGIN_TIME") == null){
                throw new NDSException("开始时间为空！");
            }else if(obj.getDate("BEGIN_TIME").before(new Date())){
                throw new NDSException("开始日期不能小于当前日期！");
            }
            beginDate = obj.getDate("BEGIN_TIME");
        }
        if(obj.containsKey("END_TIME")){
            if(obj.getDate("END_TIME") == null){
                throw new NDSException("结束时间为空！");
            }
            endDate = obj.getDate("END_TIME");
        }
        if (endDate.before(beginDate)) {
            throw new NDSException("结束日期不能小于开始日期！");
        }
        if(obj.containsKey("REMARK") && obj.getString("REMARK") == null){
            throw new NDSException("备注为空！");
        }
    }

    /**
     * 修改
     * @param session
     * @param fixColumn
     * @param id
     * @return
     */
    private ValueHolder updateUrgentStrategy(QuerySession session, JSONObject fixColumn,Long id) {
        ValueHolder valueHolder = new ValueHolder();

        JSONObject obj = fixColumn.getJSONObject(StConstant.ST_C_VIP_URGENT_STRATEGY);
        //基础数据校验
        checkUpdateData(obj,id);
        StCVipUrgentStrategy stCVipUrgentStrategy = JSON.parseObject(JSON.toJSONString(obj), StCVipUrgentStrategy.class);
        //基本字段赋值
        stCVipUrgentStrategy.setId(id);
        StBeanUtils.makeModifierField(stCVipUrgentStrategy, session.getUser());
        stCVipUrgentStrategyMapper.updateById(stCVipUrgentStrategy);
        valueHolder.put("code", ResultCode.SUCCESS);
        valueHolder.put("message", "保存成功！");
        return valueHolder;
    }

}
