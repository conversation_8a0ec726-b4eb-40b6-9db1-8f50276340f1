package com.jackrain.nea.st.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.st.common.StConstant;
import com.jackrain.nea.st.mapper.StCSellOwngoodsCustomerMapper;
import com.jackrain.nea.st.mapper.StCSellOwngoodsItemMapper;
import com.jackrain.nea.st.mapper.StCSellOwngoodsMapper;
import com.jackrain.nea.st.model.table.StCSellOwngoodsDO;
import com.jackrain.nea.st.utils.StBeanUtils;
import com.jackrain.nea.st.utils.ValueHolderUtils;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description 经销商自有商品删除业务实现类
 * @Date 2019/3/10
 **/
@Component
@Slf4j
@Transactional
public class SellOwngGoodsDeleteService extends CommandAdapter {

    @Autowired
    private StCSellOwngoodsMapper stCSellOwngoodsMapper;
    @Autowired
    private StCSellOwngoodsItemMapper stCSellOwngoodsItemMapper;
    @Autowired
    private StCSellOwngoodsCustomerMapper stCSellOwngoodsCustomerMapper;

    /**
     * @return com.jackrain.nea.util.ValueHolder
     * <AUTHOR>
     * @Description 经销商自有商品主表删除方法
     * @Date 2019/3/8
     * @Param [session]
     **/
    public ValueHolder deleteSellOwnGoods(QuerySession session) {

        DefaultWebEvent event = session.getEvent();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(
                event.getParameterValue("param"), "yyyy-MM-dd HH:mm:ss", SerializerFeature.WriteMapNullValue),
                Feature.OrderedField);
        log.debug(LogUtil.format("请求JSON：") + param.toString());

        Boolean isdelmtable = param.getBoolean("isdelmtable");
        //判断主表删除，还是删除从表
        Long objid = param.getLong("objid");
        if (isdelmtable) {
            return delMainSellOwnGoods(objid);
        } else {
            String tableName = "";//删除的明细表名
            List ids = new ArrayList();
            JSONObject tableItem = (JSONObject) param.get("tabitem");
            if (tableItem.containsKey(StConstant.TAB_ST_C_SELL_OWNGOODS_ITEM)) {
                tableName = StConstant.TAB_ST_C_SELL_OWNGOODS_ITEM;
                JSONArray jsonArray = tableItem.getJSONArray(tableName);
                if (jsonArray != null) {
                    ids = jsonArray;
                }
            } else {
                tableName = StConstant.TAB_ST_C_SELL_OWNGOODS_CUSTOMER;
                JSONArray jsonArray = tableItem.getJSONArray(tableName);
                if (jsonArray != null) {
                    ids = jsonArray;
                }
            }
            return delSubOwnGoods(session, objid, tableName, ids);
        }
    }

    /**
     * @return com.jackrain.nea.util.ValueHolder
     * <AUTHOR>
     * @Description 从表删除
     * @Date 2019/3/10
     * @Param [session, objid,tableName,ids]
     **/
    private ValueHolder delSubOwnGoods(QuerySession session, Long objid, String tableName, List ids) {
        ValueHolder resultValueHolder = new ValueHolder();
        //判断记录是否符合删除条件
        resultValueHolder = delCheck(objid, resultValueHolder);
        if (!resultValueHolder.isOK()) {
            return resultValueHolder;
        }

        //删除明细表数据1.经销商商品明细 2.经销商明细
        int count = 0;
        if (StConstant.TAB_ST_C_SELL_OWNGOODS_ITEM.equals(tableName)) {
            count = stCSellOwngoodsItemMapper.deleteBatchIds(ids);
            if (count > 0) {
                //组装更新修改人bean
                StCSellOwngoodsDO stCSellOwngoodsDO = new StCSellOwngoodsDO();
                stCSellOwngoodsDO.setId(objid);
                StBeanUtils.makeModifierField(stCSellOwngoodsDO, session.getUser());
                int mainCount = stCSellOwngoodsMapper.updateById(stCSellOwngoodsDO);
                if (mainCount < 0) {
                    log.debug(LogUtil.format("子表ST_C_SELL_OWNGOODS_ITEM删除失败！"));
                    resultValueHolder = ValueHolderUtils.getFailValueHolder("明细删除失败！");
                } else {
                    resultValueHolder = ValueHolderUtils.getDeleteSuccessValueHolder();
                }
            } else {
                log.debug(LogUtil.format("子表ST_C_SELL_OWNGOODS_ITEM删除失败！"));
                resultValueHolder = ValueHolderUtils.getFailValueHolder("明细删除失败！");
            }
        } else {
            count = 0;
            count = stCSellOwngoodsCustomerMapper.deleteBatchIds(ids);
            if (count > 0) {
                //组装更新修改人bean
                StCSellOwngoodsDO stCSellOwngoodsDO = new StCSellOwngoodsDO();
                stCSellOwngoodsDO.setId(objid);
                StBeanUtils.makeModifierField(stCSellOwngoodsDO, session.getUser());
                int mainCount = stCSellOwngoodsMapper.updateById(stCSellOwngoodsDO);
                if (mainCount < 0) {
                    log.debug(LogUtil.format("子表ST_C_SELL_OWNGOODS_CUSTOMER删除失败！"));
                    resultValueHolder = ValueHolderUtils.getFailValueHolder("明细删除失败！");
                } else {
                    resultValueHolder = ValueHolderUtils.getDeleteSuccessValueHolder();
                }
            } else {
                log.debug(LogUtil.format("子表ST_C_SELL_OWNGOODS_CUSTOMER删除失败！"));
                resultValueHolder = ValueHolderUtils.getFailValueHolder("明细删除失败！");
            }
        }
        return resultValueHolder;

    }

    /**
     * @return com.jackrain.nea.util.ValueHolder
     * <AUTHOR>
     * @Description 主表删除
     * @Date 2019/3/10
     * @Param [objid]
     **/
    private ValueHolder delMainSellOwnGoods(Long objid) {
        ValueHolder resultValueHolder;
        resultValueHolder = ValueHolderUtils.getDeleteSuccessValueHolder();
        //判断记录是否符合删除条件
        resultValueHolder = delCheck(objid, resultValueHolder);
        if (!resultValueHolder.isOK()) {
            return resultValueHolder;
        }

        //删除主表数据
        int result = stCSellOwngoodsMapper.deleteById(objid);
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("st_c_sell_owngoods_id", objid);
        int resultItem = stCSellOwngoodsItemMapper.deleteByMap(map);
        int resultCustomer = stCSellOwngoodsCustomerMapper.deleteByMap(map);
        //删除失败
        if (result <= 0) {
            //子表删除失败
            if (resultItem < 0) {
                log.debug(LogUtil.format("子表ST_C_SELL_OWNGOODS_CUSTOMER删除失败！"));
                return ValueHolderUtils.getFailValueHolder("记录删除失败！");
            }
            if (resultCustomer < 0) {
                log.debug(LogUtil.format("子表ST_C_SELL_OWNGOODS_CUSTOMER删除失败！"));
                return ValueHolderUtils.getFailValueHolder("记录删除失败！");
            }
        }
        return resultValueHolder;
    }

    /**
     * @return com.jackrain.nea.util.ValueHolder
     * <AUTHOR>
     * @Description 删除前校验单据状态
     * @Date 2019/3/10
     * @Param [objid, valueHolder]
     **/
    private ValueHolder delCheck(Long objid, ValueHolder valueHolder) {

        //主表记录不存在
        StCSellOwngoodsDO recordDo = stCSellOwngoodsMapper.selectById(objid);
        if (recordDo == null) {
            valueHolder = ValueHolderUtils.getFailValueHolder("当前记录已不存在！");
        } else {
            if (StConstant.CON_BILL_STATUS_02.equals(recordDo.getBillStatus())) {
                throw new NDSException("当前记录已审核，不允许删除明细！");
            }else if(StConstant.CON_BILL_STATUS_03.equals(recordDo.getBillStatus())){
                throw new NDSException("当前记录已作废，不允许删除明细！");
            }else if(StConstant.CON_BILL_STATUS_04.equals(recordDo.getBillStatus())){
                throw new NDSException("当前记录已结案，不允许删除明细！");
            }
        }
        return valueHolder;
    }
}
