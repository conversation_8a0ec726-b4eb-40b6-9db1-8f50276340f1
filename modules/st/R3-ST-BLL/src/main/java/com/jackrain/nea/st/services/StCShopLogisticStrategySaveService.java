package com.jackrain.nea.st.services;

import cn.hutool.core.collection.ListUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.google.common.collect.Lists;
import com.jackrain.nea.cpext.api.CpLogisticsQueryCmd;
import com.jackrain.nea.cpext.api.CpShopQueryCmd;
import com.jackrain.nea.cpext.api.CpcPhyWareHouseQueryCmd;
import com.jackrain.nea.cpext.api.RegionQueryExtCmd;
import com.jackrain.nea.cpext.model.request.CpCShopQueryByNameRequest;
import com.jackrain.nea.cpext.model.result.CpCShopQueryByNameInfoResult;
import com.jackrain.nea.cpext.model.result.CpCShopQueryByNameResult;
import com.jackrain.nea.cpext.model.table.CpCPhyWarehouse;
import com.jackrain.nea.cpext.model.table.CpCRegion;
import com.jackrain.nea.cpext.model.table.CpLogistics;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.oc.oms.model.enums.YesNoEnum;
import com.jackrain.nea.ps.api.PsCProdimItemQueryCmd;
import com.jackrain.nea.ps.api.table.PsCProdimItem;
import com.jackrain.nea.psext.api.PscProQueryForEcodeCmd;
import com.jackrain.nea.psext.model.table.PsCPro;
import com.jackrain.nea.redis.config.CusRedisTemplate;
import com.jackrain.nea.redis.util.RedisOpsUtil;
import com.jackrain.nea.st.annotation.StOperationLog;
import com.jackrain.nea.st.common.StConstant;
import com.jackrain.nea.st.mapper.StCShopLogisticStrategyItemMapper;
import com.jackrain.nea.st.mapper.StCShopLogisticStrategyMapper;
import com.jackrain.nea.st.model.request.StCShopLogisticStrategyRequest;
import com.jackrain.nea.st.model.table.StCShopLogisticStrategy;
import com.jackrain.nea.st.model.table.StCShopLogisticStrategyItem;
import com.jackrain.nea.st.model.vo.StCShopLogisticStrategyImpVo;
import com.jackrain.nea.st.rpc.RpcPsService;
import com.jackrain.nea.st.utils.JsonUtils;
import com.jackrain.nea.st.utils.StBeanUtils;
import com.jackrain.nea.st.utils.ValueHolderUtils;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Created with IntelliJ IDEA.
 *
 * @Author: jg.zhan
 * @Date: 2022/6/20 18:31
 * @Description: 店铺物流设置保存
 */
@Component
@Slf4j
@Transactional(rollbackFor = Exception.class)
public class StCShopLogisticStrategySaveService extends CommandAdapter {

    @Autowired
    private StCShopLogisticStrategyMapper stCShopLogisticStrategyMapper;

    @Autowired
    private StCShopLogisticStrategyItemMapper stCShopLogisticStrategyItemMapper;

    @Autowired
    private RpcPsService rpcPsService;

    @Reference(group = "cp-ext", version = "1.0")
    private CpLogisticsQueryCmd cpLogisticsQueryCmd;

    @Reference(group = "cp-ext", version = "1.0")
    private CpShopQueryCmd cpShopQueryCmd;

    @Reference(version = "1.0", group = "ps-ext")
    private PscProQueryForEcodeCmd pscProQueryForEcodeCmd;

    @Reference(version = "1.0", group = "ps")
    private PsCProdimItemQueryCmd psCProdimItemQueryCmd;

    @Reference(version = "1.0", group = "cp-ext")
    private RegionQueryExtCmd regionQueryExtCmd;

    @Reference(version = "1.0", group = "cp-ext")
    private CpcPhyWareHouseQueryCmd cpcPhyWareHouseQueryCmd;

    @Override
    @StOperationLog(mainTableName = "ST_C_SHOP_LOGISTIC_STRATEGY", itemsTableName = "ST_C_SHOP_LOGISTIC_STRATEGY_ITEM")
    public ValueHolder execute(QuerySession session) {
        DefaultWebEvent event = session.getEvent();
        List<String> nullKeyList = new ArrayList<>();
        JSONObject param = JsonUtils.filterNull((JSONObject) event.getParameterValue("param"), nullKeyList);
        log.info(LogUtil.format("##店铺物流设置保存##入参：{}"), param);
        if (param != null) {
            Long id = param.getLong("objid");
            JSONObject fixColumn = param.getJSONObject("fixcolumn");
            StCShopLogisticStrategyRequest stCShopLogisticStrategyRequest = JsonUtils.jsonParseClass(fixColumn, StCShopLogisticStrategyRequest.class);
            if (fixColumn != null && id != null) {
                if (id != -1) {
                    return updateStCShopLogisticStrategy(session, id, stCShopLogisticStrategyRequest, nullKeyList);
                } else {
                    return insertStCShopLogisticStrategy(session, stCShopLogisticStrategyRequest, nullKeyList);
                }
            }
        }
        throw new NDSException("当前记录已不存在！");
    }

    private void checkInsert(StCShopLogisticStrategyRequest request) {
        if (Objects.isNull(request)) {
            throw new NDSException("入参不能为空！");
        }
        StCShopLogisticStrategy stCShopLogisticStrategy = request.getStCShopLogisticStrategy();
        if (Objects.nonNull(stCShopLogisticStrategy.getCpCShopId())) {
            List<StCShopLogisticStrategy> stCShopLogisticStrategieList = stCShopLogisticStrategyMapper.selectList(new QueryWrapper<StCShopLogisticStrategy>()
                    .lambda()
                    .eq(StCShopLogisticStrategy::getCpCShopId, stCShopLogisticStrategy.getCpCShopId()));
            if (CollectionUtils.isNotEmpty(stCShopLogisticStrategieList)) {
                throw new NDSException("店铺已经存在，不允许重复录入");
            }
        }
    }

    /**
     * 更新店铺物流设置
     *
     * @param session
     * @param id
     * @param request
     * @param nullKeyList
     * @return
     */
    private ValueHolder updateStCShopLogisticStrategy(QuerySession session, Long id, StCShopLogisticStrategyRequest request, List<String> nullKeyList) {

        List<StCShopLogisticStrategyItem> stCShopLogisticStrategyItemList = request.getStCShopLogisticStrategyItemList();

        StCShopLogisticStrategy stCShopLogisticStrategy = stCShopLogisticStrategyMapper.selectById(id);

        if (stCShopLogisticStrategy == null) {
            return ValueHolderUtils.getFailValueHolder("记录不存在！");
        }

        StBeanUtils.makeModifierField(stCShopLogisticStrategy, session.getUser());

        // 主表仅备注可修改
        if (request.getStCShopLogisticStrategy() != null) {
            stCShopLogisticStrategy.setRemark(request.getStCShopLogisticStrategy().getRemark());
        }

        stCShopLogisticStrategyMapper.updateById(stCShopLogisticStrategy);

        // 清空redis
        CusRedisTemplate<String, String> redisTemplate = RedisOpsUtil.getStrRedisTemplate();
        redisTemplate.delete("st:shop:express:shopId:" + stCShopLogisticStrategy.getCpCShopId());

        if (CollectionUtils.isNotEmpty(stCShopLogisticStrategyItemList)) {
            List<StCShopLogisticStrategyItem> insertItemList = stCShopLogisticStrategyItemList.stream().filter(item -> item.getId() == -1).collect(Collectors.toList());
            List<StCShopLogisticStrategyItem> updateItemList = stCShopLogisticStrategyItemList.stream().filter(item -> item.getId() > 0).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(insertItemList)) {
                //插入明细表
                insertStCShopLogisticStrategyItem(session, stCShopLogisticStrategy, insertItemList);
            }
            if (CollectionUtils.isNotEmpty(updateItemList)) {
                //更新明细表
                updateStCShopLogisticStrategyItem(session, stCShopLogisticStrategy, updateItemList);
            }
        }
        // 清空redis
        redisTemplate.delete("st:shop:express:shopId:" + stCShopLogisticStrategy.getCpCShopId());

        return ValueHolderUtils.getSuccessValueHolder(id, StConstant.ST_C_SHOP_LOGISTIC_STRATEGY, "");
    }

    /**
     * 新增店铺物流设置
     *
     * @param session
     * @param request
     * @param nullKeyList
     * @return
     */
    private ValueHolder insertStCShopLogisticStrategy(QuerySession session, StCShopLogisticStrategyRequest request, List<String> nullKeyList) {

        long id = 0;
        StCShopLogisticStrategy stCShopLogisticStrategy = request.getStCShopLogisticStrategy();
        List<StCShopLogisticStrategyItem> stCShopLogisticStrategyItemList = request.getStCShopLogisticStrategyItemList();

        if (stCShopLogisticStrategy != null) {
            //校验数据
            checkInsert(request);
            //1.预售解析策略主表处理
            id = ModelUtil.getSequence(StConstant.ST_C_SHOP_LOGISTIC_STRATEGY);
            stCShopLogisticStrategy.setId(id);
            StBeanUtils.makeCreateField(stCShopLogisticStrategy, session.getUser());
            if (stCShopLogisticStrategyMapper.insert(stCShopLogisticStrategy) < 0) {
                return ValueHolderUtils.getFailValueHolder("新增失败！");
            }
        }
        if (CollectionUtils.isNotEmpty(stCShopLogisticStrategyItemList)) {
            //插入明细表
            insertStCShopLogisticStrategyItem(session, stCShopLogisticStrategy, stCShopLogisticStrategyItemList);
        }
        // 清空redis
        CusRedisTemplate<String, String> redisTemplate = RedisOpsUtil.getStrRedisTemplate();
        redisTemplate.delete("st:shop:express:shopId:" + stCShopLogisticStrategy.getCpCShopId());
        return ValueHolderUtils.getSuccessValueHolder(id, StConstant.ST_C_SHOP_LOGISTIC_STRATEGY, "");
    }

    /**
     * 新增店铺物流设置明细表
     *
     * @param session
     * @param stCShopLogisticStrategy
     * @param itemList
     */
    private void insertStCShopLogisticStrategyItem(QuerySession session, StCShopLogisticStrategy stCShopLogisticStrategy, List<StCShopLogisticStrategyItem> itemList) {

        for (StCShopLogisticStrategyItem item : itemList) {
            Long cpCLogisticsId = item.getCpCLogisticsId();
            Long psCProId = item.getPsCProId();
            Long cpCPhyWarehouseId = item.getCpCPhyWarehouseId();
            //校验明细数据
            checkItemData(item, stCShopLogisticStrategy.getId());
            //保存商品信息
            if (Objects.nonNull(psCProId)) {
                PsCPro psCPro = rpcPsService.queryProByID(psCProId);
                if (Objects.isNull(psCPro)) {
                    throw new NDSException("商品不存在！");
                }
                item.setPsCProEcode(psCPro.getEcode());
                item.setPsCProEname(psCPro.getEname());
            }
            //保存物流公司档案
            if (Objects.nonNull(cpCLogisticsId)) {
                ValueHolderV14<Map<Long, CpLogistics>> mapValueHolderV14 = cpLogisticsQueryCmd.queryLogisticsByIds(Lists.newArrayList(cpCLogisticsId));
                if (!mapValueHolderV14.isOK()) {
                    throw new NDSException("物流公司档案不存在！");
                }
                mapValueHolderV14.getData().forEach((k, v) -> {
                    item.setCpCLogisticsId(cpCLogisticsId);
                    item.setCpCLogisticsEcode(v.getEcode());
                    item.setCpCLogisticsEname(v.getEname());
                    item.setLogisticType(v.getType());
                });
            }
            //保存仓库信息
            if (Objects.nonNull(cpCPhyWarehouseId)) {
                CpCPhyWarehouse cpCPhyWarehouse = cpcPhyWareHouseQueryCmd.queryCpCPhyWarehouseById(cpCPhyWarehouseId);
                if (Objects.isNull(cpCPhyWarehouse)) {
                    throw new NDSException("所选仓库不存在！");
                }
                item.setCpCPhyWarehouseEcode(cpCPhyWarehouse.getEcode());
                item.setCpCPhyWarehouseEname(cpCPhyWarehouse.getEname());
            }
            item.setId(ModelUtil.getSequence(StConstant.ST_C_SHOP_LOGISTIC_STRATEGY_ITEM));//主键
            item.setStCShopLogisticStrategyId(stCShopLogisticStrategy.getId());
            StBeanUtils.makeCreateField(item, session.getUser());
            int insert = stCShopLogisticStrategyItemMapper.insert(item);
            if (insert < 1) {
                throw new NDSException("店铺物流设置明细插入失败！");
            }
        }
    }

    /**
     * 校验明细
     *
     * @param item
     * @param masterId
     */
    private void checkItemData(StCShopLogisticStrategyItem item, Long masterId) {
        boolean flag = checkItem(item);
        if (!flag) {
            throw new NDSException("规则不正确，请检查!");
        }
        LambdaQueryWrapper<StCShopLogisticStrategyItem> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(StCShopLogisticStrategyItem::getStCShopLogisticStrategyId, masterId);
        if (item.getCpCProvinceId() != null) {
            wrapper.eq(StCShopLogisticStrategyItem::getCpCProvinceId, item.getCpCProvinceId());
        } else {
            wrapper.isNull(StCShopLogisticStrategyItem::getCpCProvinceId);
        }
        if (item.getCpCCityId() != null) {
            wrapper.eq(StCShopLogisticStrategyItem::getCpCCityId, item.getCpCCityId());
        } else {
            wrapper.isNull(StCShopLogisticStrategyItem::getCpCCityId);
        }
        if (item.getCpCPhyWarehouseId() != null) {
            wrapper.eq(StCShopLogisticStrategyItem::getCpCPhyWarehouseId, item.getCpCPhyWarehouseId());
        } else {
            wrapper.isNull(StCShopLogisticStrategyItem::getCpCPhyWarehouseId);
        }
        if (item.getPsCProId() != null) {
            wrapper.eq(StCShopLogisticStrategyItem::getPsCProId, item.getPsCProId());
        } else {
            wrapper.isNull(StCShopLogisticStrategyItem::getPsCProId);
        }
        if (item.getPsCProdimId() != null) {
            wrapper.eq(StCShopLogisticStrategyItem::getPsCProdimId, item.getPsCProdimId());
        } else {
            wrapper.isNull(StCShopLogisticStrategyItem::getPsCProdimId);
        }
        if (StringUtils.isNotEmpty(item.getSellerRemark())) {
            wrapper.eq(StCShopLogisticStrategyItem::getSellerRemark, item.getSellerRemark());
        } else {
            wrapper.isNull(StCShopLogisticStrategyItem::getSellerRemark);
        }
        wrapper.eq(StCShopLogisticStrategyItem::getCpCLogisticsId, item.getCpCLogisticsId());
        int count = stCShopLogisticStrategyItemMapper.selectCount(wrapper);
        if (count > 0) {
            throw new NDSException("记录已存在，请重新录入！");
        }
    }

    /**
     * 检查维护规则
     *
     * @param item
     * @return
     */
    private boolean checkItem(StCShopLogisticStrategyItem item) {
        if (Objects.isNull(item.getCpCLogisticsId())) {
            throw new NDSException("物流公司不能为空！");
        }
        //卖家备注+物流
        if (StringUtils.isNotEmpty(item.getSellerRemark()) && Objects.isNull(item.getCpCProvinceId())
                && Objects.isNull(item.getCpCCityId()) && Objects.isNull(item.getCpCPhyWarehouseId())
                && Objects.isNull(item.getPsCProId()) && Objects.isNull(item.getPsCProdimId())) {
            return true;
        }
        //省+市+仓+商品+物流
        if (Objects.nonNull(item.getCpCProvinceId()) && Objects.nonNull(item.getCpCCityId())
                && Objects.nonNull(item.getCpCPhyWarehouseId()) && Objects.nonNull(item.getPsCProId())
                && Objects.isNull(item.getPsCProdimId()) && StringUtils.isEmpty(item.getSellerRemark())) {
            return true;
        }
        //省+市+仓+四级分类+物流
        if (Objects.nonNull(item.getCpCProvinceId()) && Objects.nonNull(item.getCpCCityId())
                && Objects.nonNull(item.getCpCPhyWarehouseId()) && Objects.isNull(item.getPsCProId())
                && Objects.nonNull(item.getPsCProdimId()) && StringUtils.isEmpty(item.getSellerRemark())) {
            return true;
        }
        //省+仓+商品+物流
        if (Objects.nonNull(item.getCpCProvinceId()) && Objects.isNull(item.getCpCCityId())
                && Objects.nonNull(item.getCpCPhyWarehouseId()) && Objects.nonNull(item.getPsCProId())
                && Objects.isNull(item.getPsCProdimId()) && StringUtils.isEmpty(item.getSellerRemark())) {
            return true;
        }
        //仓+商品+物流
        if (Objects.isNull(item.getCpCProvinceId()) && Objects.isNull(item.getCpCCityId())
                && Objects.nonNull(item.getCpCPhyWarehouseId()) && Objects.nonNull(item.getPsCProId())
                && Objects.isNull(item.getPsCProdimId()) && StringUtils.isEmpty(item.getSellerRemark())) {
            return true;
        }
        //省+仓+物流
        if (Objects.nonNull(item.getCpCProvinceId()) && Objects.isNull(item.getCpCCityId())
                && Objects.nonNull(item.getCpCPhyWarehouseId()) && Objects.isNull(item.getPsCProId())
                && Objects.isNull(item.getPsCProdimId()) && StringUtils.isEmpty(item.getSellerRemark())) {
            return true;
        }
        //商品+物流
        if (Objects.isNull(item.getCpCProvinceId()) && Objects.isNull(item.getCpCCityId())
                && Objects.isNull(item.getCpCPhyWarehouseId()) && Objects.nonNull(item.getPsCProId())
                && Objects.isNull(item.getPsCProdimId()) && StringUtils.isEmpty(item.getSellerRemark())) {
            return true;
        }
        //四级分类+物流
        if (Objects.isNull(item.getCpCProvinceId()) && Objects.isNull(item.getCpCCityId())
                && Objects.isNull(item.getCpCPhyWarehouseId()) && Objects.isNull(item.getPsCProId())
                && Objects.nonNull(item.getPsCProdimId()) && StringUtils.isEmpty(item.getSellerRemark())) {
            return true;
        }
        //仓+物流
        if (Objects.isNull(item.getCpCProvinceId()) && Objects.isNull(item.getCpCCityId())
                && Objects.nonNull(item.getCpCPhyWarehouseId()) && Objects.isNull(item.getPsCProId())
                && Objects.isNull(item.getPsCProdimId()) && StringUtils.isEmpty(item.getSellerRemark())) {
            return true;
        }
        return false;
    }

    /**
     * 更新店铺物流设置明细表
     *
     * @param session
     * @param stCShopLogisticStrategy
     * @param itemList
     */
    private void updateStCShopLogisticStrategyItem(QuerySession session, StCShopLogisticStrategy stCShopLogisticStrategy, List<StCShopLogisticStrategyItem> itemList) {

        for (StCShopLogisticStrategyItem stShopLogisticStrategyItem : itemList) {

            Long cpCLogisticsId = stShopLogisticStrategyItem.getCpCLogisticsId();

            StCShopLogisticStrategyItem item = stCShopLogisticStrategyItemMapper.selectById(stShopLogisticStrategyItem.getId());

            if (Objects.isNull(item)) {
                throw new NDSException("当前记录已不存在！");
            }

            if (Objects.isNull(cpCLogisticsId)) {
                continue;
            }

            item.setCpCLogisticsId(stShopLogisticStrategyItem.getCpCLogisticsId());

            checkItemData(item, stCShopLogisticStrategy.getId());

            ValueHolderV14<Map<Long, CpLogistics>> mapValueHolderV14 = cpLogisticsQueryCmd.queryLogisticsByIds(Lists.newArrayList(cpCLogisticsId));
            if (!mapValueHolderV14.isOK()) {
                throw new NDSException("物流公司档案不存在！");
            }
            mapValueHolderV14.getData().forEach((k, v) -> {
                item.setCpCLogisticsId(cpCLogisticsId);
                item.setCpCLogisticsEcode(v.getEcode());
                item.setCpCLogisticsEname(v.getEname());
                item.setLogisticType(v.getType());
            });
            StBeanUtils.makeModifierField(item, session.getUser());
            int update = stCShopLogisticStrategyItemMapper.updateById(item);
            if (update < 1) {
                throw new NDSException("店铺物流设置明细更新失败！");
            }
        }
    }


    /**
     * 导入批量保存数据
     *
     * @param stCShopLogisticStrategyImpVos
     * @param user
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public int batchSaveInvoice(List<StCShopLogisticStrategyImpVo> stCShopLogisticStrategyImpVos, User user) {
        if (log.isDebugEnabled()) {
            log.info("## 店铺物流设置导入数据：" + JSONObject.toJSONString(stCShopLogisticStrategyImpVos));
        }
        //查询店铺信息
        List<String> shopNameList = stCShopLogisticStrategyImpVos.stream().distinct().map(StCShopLogisticStrategyImpVo::getCpCShopName).collect(Collectors.toList());
        List<CpCShopQueryByNameInfoResult> cpCShops = new ArrayList<>();
        for (String shopName : shopNameList) {
            CpCShopQueryByNameRequest request = new CpCShopQueryByNameRequest();
            request.setCpCShopEname(shopName);
            request.setUserId(new Long(user.getId()));
            ValueHolderV14<CpCShopQueryByNameResult> shopV14 = cpShopQueryCmd.queryShopByName(request);
            if (Objects.nonNull(shopV14) && Objects.nonNull(shopV14.getData())) {
                List<CpCShopQueryByNameInfoResult> cpCShopList = shopV14.getData().getCpCShopList();
                if (CollectionUtils.isNotEmpty(cpCShopList)) {
                    cpCShops.addAll(cpCShopList);
                }
            }
        }
        Map<String, CpCShopQueryByNameInfoResult> shopMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(cpCShops)) {
            shopMap = cpCShops.stream().collect(Collectors.toMap(CpCShopQueryByNameInfoResult::getCpCShopEname, Function.identity(), (key1, key2) -> key2));
        }
        //查询物流公司信息
        List<String> logisticNameList = stCShopLogisticStrategyImpVos.stream()
                .distinct().map(StCShopLogisticStrategyImpVo::getCpCLogisticsName).collect(Collectors.toList());
        Map<String, CpLogistics> logisticsMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(logisticNameList)) {
            List<CpLogistics> cpLogistics = cpLogisticsQueryCmd.queryLogisticsByNames(logisticNameList);
            if (CollectionUtils.isNotEmpty(cpLogistics)) {
                logisticsMap = cpLogistics.stream().collect(Collectors.toMap(CpLogistics::getEname, Function.identity(), (key1, key2) -> key2));
            }
        }

        //查询商品
        Map<String, PsCPro> proMap = new HashMap<>();
        for (StCShopLogisticStrategyImpVo vo : stCShopLogisticStrategyImpVos) {
            if (StringUtils.isNotBlank(vo.getPsCProCode())) {
                PsCPro psCPro = pscProQueryForEcodeCmd.pscProQueryForEcode(vo.getPsCProCode());
                if (psCPro != null) {
                    proMap.put(psCPro.getEcode(), psCPro);
                }
            }
        }
        //查询品类
        List<String> prodimNameList = stCShopLogisticStrategyImpVos.stream()
                .filter(p -> StringUtils.isNotEmpty(p.getPsCProdimNanme()))
                .distinct().map(StCShopLogisticStrategyImpVo::getPsCProdimNanme).collect(Collectors.toList());
        Map<String, PsCProdimItem> prodimMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(prodimNameList)) {
            ValueHolderV14<List<PsCProdimItem>> listValueHolderV14 = psCProdimItemQueryCmd.queryPsCProdimItemListByNames(prodimNameList, 6L);
            List<PsCProdimItem> PsCProdimItemList = listValueHolderV14.getData();
            if (CollectionUtils.isNotEmpty(PsCProdimItemList)) {
                prodimMap = PsCProdimItemList.stream().collect(Collectors.toMap(PsCProdimItem::getEname, Function.identity(), (key1, key2) -> key2));
            }
        }
        //查询省
        List<String> provinceNameList = stCShopLogisticStrategyImpVos.stream()
                .filter(p -> StringUtils.isNotEmpty(p.getProvinceName()))
                .distinct().map(StCShopLogisticStrategyImpVo::getProvinceName).collect(Collectors.toList());
        Map<String, CpCRegion> provinceMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(provinceNameList)) {
            List<CpCRegion> provs = regionQueryExtCmd.queryRegionByNameAndType("PROV", provinceNameList);
            if (CollectionUtils.isNotEmpty(provs)) {
                provinceMap = provs.stream().collect(Collectors.toMap(CpCRegion::getEname, Function.identity(), (key1, key2) -> key2));
            }
        }
        //查询城市
        List<String> cityNameList = stCShopLogisticStrategyImpVos.stream()
                .filter(p -> StringUtils.isNotEmpty(p.getCityName()))
                .distinct().map(StCShopLogisticStrategyImpVo::getCityName).collect(Collectors.toList());
        Map<String, CpCRegion> cityMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(cityNameList)) {
            List<CpCRegion> citys = regionQueryExtCmd.queryRegionByNameAndType("CITY", cityNameList);
            if (CollectionUtils.isNotEmpty(citys)) {
                cityMap = citys.stream().collect(Collectors.toMap(CpCRegion::getEname, Function.identity(), (key1, key2) -> key2));
            }
        }
        //查仓库
        List<String> warehouseNameList = stCShopLogisticStrategyImpVos.stream()
                .filter(p -> StringUtils.isNotEmpty(p.getWarehouseName()))
                .distinct().map(StCShopLogisticStrategyImpVo::getWarehouseName).collect(Collectors.toList());
        Map<String, CpCPhyWarehouse> warehouseMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(warehouseNameList)) {
            List<CpCPhyWarehouse> warehouseList = cpcPhyWareHouseQueryCmd.queryWarehouseByEnames(warehouseNameList);
            if (CollectionUtils.isNotEmpty(warehouseList)) {
                warehouseMap = warehouseList.stream().collect(Collectors.toMap(CpCPhyWarehouse::getEname, Function.identity(), (x, y) -> y));
            }
        }

        List<StCShopLogisticStrategy> insertList = new ArrayList<>();
        List<StCShopLogisticStrategyItem> insertItemList = new ArrayList<>();

        StringBuilder checkMessage = new StringBuilder();
        //校验仓库是否存在，重复性校验
        Map<String, List<StCShopLogisticStrategyImpVo>> dataShopMap =
                stCShopLogisticStrategyImpVos.stream().collect(Collectors.groupingBy(StCShopLogisticStrategyImpVo::getCpCShopName));

        //缓存连接
        CusRedisTemplate<String, String> redisTemplate = RedisOpsUtil.getStrRedisTemplate();
        for (String shopName : dataShopMap.keySet()) {
            CpCShopQueryByNameInfoResult cpCShop = shopMap.get(shopName);
            List<StCShopLogisticStrategyImpVo> impVoList = dataShopMap.get(shopName);

            if (Objects.isNull(cpCShop)) {
                checkMessage.append("[未查询到该店铺信息！]");
                copyMessageToItem(impVoList, checkMessage);
                checkMessage.setLength(0);
                log.error(LogUtil.format("未查询到该店铺信息！", "店铺物流设置头明细导入"));
                continue;
            }
            //删除缓存
            String redisKey="st:shop:express:shopId:" +cpCShop.getId();
            redisTemplate.delete(redisKey);

            Long mainObjid = 0L;
            boolean isInsertMain = true;
            StCShopLogisticStrategy stCShopLogisticStrategy = stCShopLogisticStrategyMapper.selectOne(
                    new QueryWrapper<StCShopLogisticStrategy>()
                            .lambda().eq(StCShopLogisticStrategy::getCpCShopId, cpCShop.getId())
                            .eq(StCShopLogisticStrategy::getIsactive, YesNoEnum.Y.getKey()));
            if (Objects.nonNull(stCShopLogisticStrategy)) {
                isInsertMain = false;
                mainObjid = stCShopLogisticStrategy.getId();
            }

            List<StCShopLogisticStrategyItem> itemList = new ArrayList<>();
            for (StCShopLogisticStrategyImpVo itemVo : impVoList) {

                CpLogistics logistics = logisticsMap.get(itemVo.getCpCLogisticsName());
                if (Objects.isNull(logistics)) {
                    checkMessage.append("[未查询到该物流公司信息！]");
                    appendMessageToItem(itemVo, checkMessage);
                    checkMessage.setLength(0);
                    log.error(LogUtil.format("未查询到该物流公司信息！", "店铺物流设置头明细导入"));
                    continue;
                }
                PsCPro psCPro = proMap.get(itemVo.getPsCProCode());
                if (StringUtils.isNotBlank(itemVo.getPsCProCode()) && Objects.isNull(psCPro)) {
                    checkMessage.append("[未查询到该商品信息！]");
                    appendMessageToItem(itemVo, checkMessage);
                    checkMessage.setLength(0);
                    log.error(LogUtil.format("未查询到该商品信息！", "店铺物流设置头明细导入"));
                    continue;
                }

                PsCProdimItem psCProdimItem = prodimMap.get(itemVo.getPsCProdimNanme());
                if (StringUtils.isNotBlank(itemVo.getPsCProdimNanme()) && Objects.isNull(psCProdimItem)) {
                    checkMessage.append("[未查询到该四级分类信息！]");
                    appendMessageToItem(itemVo, checkMessage);
                    checkMessage.setLength(0);
                    log.error(LogUtil.format("未查询到该四级分类信息！", "店铺物流设置头明细导入"));
                    continue;
                }
                CpCRegion province = provinceMap.get(itemVo.getProvinceName());
                if (StringUtils.isNotBlank(itemVo.getProvinceName())) {
                    if (Objects.isNull(province)) {
                        checkMessage.append("[未查询到该省份信息！]");
                        appendMessageToItem(itemVo, checkMessage);
                        checkMessage.setLength(0);
                        log.error(LogUtil.format("未查询到该省份信息！", "店铺物流设置头明细导入"));
                        continue;
                    }
                }
                CpCRegion city = cityMap.get(itemVo.getCityName());
                if (StringUtils.isNotBlank(itemVo.getCityName())) {
                    if (Objects.isNull(city)) {
                        checkMessage.append("[未查询到该城市信息！]");
                        appendMessageToItem(itemVo, checkMessage);
                        checkMessage.setLength(0);
                        log.error(LogUtil.format("未查询到该省份信息！", "店铺物流设置头明细导入"));
                        continue;
                    }
                }
                CpCPhyWarehouse warehouse = warehouseMap.get(itemVo.getWarehouseName());
                if (StringUtils.isNotBlank(itemVo.getWarehouseName())) {
                    if (Objects.isNull(warehouse)) {
                        checkMessage.append("[未查询到该仓库信息！]");
                        appendMessageToItem(itemVo, checkMessage);
                        checkMessage.setLength(0);
                        log.error(LogUtil.format("未查询到该仓库信息！", "店铺物流设置头明细导入"));
                        continue;
                    }
                }

                if (!isInsertMain) {
                    LambdaQueryWrapper<StCShopLogisticStrategyItem> wrapper = new LambdaQueryWrapper<>();
                    wrapper.eq(StCShopLogisticStrategyItem::getStCShopLogisticStrategyId, mainObjid);
                    if (province != null) {
                        wrapper.eq(StCShopLogisticStrategyItem::getCpCProvinceId, province.getId());
                    } else {
                        wrapper.isNull(StCShopLogisticStrategyItem::getCpCProvinceId);
                    }
                    if (city != null) {
                        wrapper.eq(StCShopLogisticStrategyItem::getCpCCityId, city.getId());
                    } else {
                        wrapper.isNull(StCShopLogisticStrategyItem::getCpCCityId);
                    }
                    if (warehouse != null) {
                        wrapper.eq(StCShopLogisticStrategyItem::getCpCPhyWarehouseId, warehouse.getId());
                    } else {
                        wrapper.isNull(StCShopLogisticStrategyItem::getCpCPhyWarehouseId);
                    }
                    if (psCPro != null) {
                        wrapper.eq(StCShopLogisticStrategyItem::getPsCProId, psCPro.getId());
                    } else {
                        wrapper.isNull(StCShopLogisticStrategyItem::getPsCProId);
                    }
                    if (psCProdimItem != null) {
                        wrapper.eq(StCShopLogisticStrategyItem::getPsCProdimId, psCProdimItem.getId());
                    } else {
                        wrapper.isNull(StCShopLogisticStrategyItem::getPsCProdimId);
                    }
                    if (StringUtils.isNotEmpty(itemVo.getSellerRemark())) {
                        wrapper.eq(StCShopLogisticStrategyItem::getSellerRemark, itemVo.getSellerRemark());
                    } else {
                        wrapper.isNull(StCShopLogisticStrategyItem::getSellerRemark);
                    }
                    wrapper.eq(StCShopLogisticStrategyItem::getCpCLogisticsId, logistics.getId());
                    int count = stCShopLogisticStrategyItemMapper.selectCount(wrapper);
                    if (count > 0) {
                        checkMessage.append("[记录已存在，请重新录入！]");
                        appendMessageToItem(itemVo, checkMessage);
                        checkMessage.setLength(0);
                        continue;
                    }
                }

                StCShopLogisticStrategyItem item = new StCShopLogisticStrategyItem();
                Long objid = ModelUtil.getSequence("ST_C_SHOP_LOGISTIC_STRATEGY_ITEM");
                item.setId(objid);
                item.setCpCLogisticsId(logistics.getId());
                item.setCpCLogisticsEcode(logistics.getEcode());
                item.setCpCLogisticsEname(logistics.getEname());
                item.setLogisticType(logistics.getType());
                item.setPsCProEcode(itemVo.getPsCProCode());
                if (Objects.nonNull(psCPro)) {
                    item.setPsCProEname(psCPro.getEname());
                    item.setPsCProId(psCPro.getId());
                }
                if (Objects.nonNull(psCProdimItem)) {
                    item.setPsCProdimId(psCProdimItem.getId());
                }
                if (Objects.nonNull(province)) {
                    item.setCpCProvinceId(province.getId());
                }
                if (Objects.nonNull(city)) {
                    item.setCpCCityId(city.getId());
                }
                if (Objects.nonNull(warehouse)) {
                    item.setCpCPhyWarehouseId(warehouse.getId());
                    item.setCpCPhyWarehouseEcode(warehouse.getEcode());
                    item.setCpCPhyWarehouseEname(warehouse.getEname());
                }
                if (StringUtils.isNotEmpty(itemVo.getSellerRemark())) {
                    item.setSellerRemark(itemVo.getSellerRemark());
                }
                StBeanUtils.makeCreateField(item, user);
                itemList.add(item);
            }
            if(isInsertMain) {
                if (CollectionUtils.isNotEmpty(itemList)) {
                    Long objid = ModelUtil.getSequence("ST_C_SHOP_LOGISTIC_STRATEGY");
                    StCShopLogisticStrategy po = new StCShopLogisticStrategy();
                    po.setId(objid);
                    po.setCpCShopId(cpCShop.getId());
                    StBeanUtils.makeCreateField(po, user);
                    insertList.add(po);
                    itemList.forEach(p -> p.setStCShopLogisticStrategyId(objid));
                    insertItemList.addAll(itemList);
                }
            }else{
                Long finalMainObjid = mainObjid;
                itemList.forEach(p -> p.setStCShopLogisticStrategyId(finalMainObjid));
                insertItemList.addAll(itemList);
            }
        }
        if (CollectionUtils.isNotEmpty(insertList)) {
            List<List<StCShopLogisticStrategy>> splitList = ListUtil.split(insertList, 1000);
            for (List<StCShopLogisticStrategy> split : splitList) {
                stCShopLogisticStrategyMapper.batchInsert(split);
            }
        }
        if (CollectionUtils.isNotEmpty(insertItemList)) {
            List<List<StCShopLogisticStrategyItem>> splitList = ListUtil.split(insertItemList, 1000);
            for (List<StCShopLogisticStrategyItem> split : splitList) {
                stCShopLogisticStrategyItemMapper.batchInsert(split);
            }
        }
        if (MapUtils.isNotEmpty(shopMap)) {
            for (CpCShopQueryByNameInfoResult cpCShop : shopMap.values()) {
                //删除缓存
                String redisKey = "st:shop:express:shopId:" + cpCShop.getId();
                redisTemplate.delete(redisKey);
            }
        }
        return insertItemList.size();
    }


    /**
     * 拷贝message到子表
     *
     * @param impVoList
     * @param message
     */
    private void copyMessageToItem(List<StCShopLogisticStrategyImpVo> impVoList, StringBuilder message) {
        for (StCShopLogisticStrategyImpVo impVo : impVoList) {
            String desc = impVo.getDesc();
            if (StringUtils.isNotEmpty(message.toString())) {
                if (StringUtils.isNotBlank(desc)) {
                    desc = desc + message.toString();
                } else {
                    desc = message.toString();
                }
                impVo.setDesc(desc);

            }
        }
    }

    /**
     * 添加message到子表
     *
     * @param impVo
     * @param message
     */
    private void appendMessageToItem(StCShopLogisticStrategyImpVo impVo, StringBuilder message) {
        String desc = impVo.getDesc();
        if (StringUtils.isNotEmpty(message.toString())) {
            if (StringUtils.isNotBlank(desc)) {
                desc = desc + message.toString();
            } else {
                desc = message.toString();
            }
            impVo.setDesc(desc);
        }
    }

}
