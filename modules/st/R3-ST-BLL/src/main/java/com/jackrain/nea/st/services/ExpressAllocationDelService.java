package com.jackrain.nea.st.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.st.mapper.StCExpressAllocationItemMapper;
import com.jackrain.nea.st.mapper.StCExpressAllocationMapper;
import com.jackrain.nea.st.model.table.StCExpressAllocationDO;
import com.jackrain.nea.st.model.table.StCExpressAllocationItemDO;
import com.jackrain.nea.st.utils.StBeanUtils;
import com.jackrain.nea.st.utils.ValueHolderUtils;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.StringUtils;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

/**
 * 删除业务逻辑
 *
 * <AUTHOR> 黄火县
 * @since : 2019-03-11
 * create at : 2019-03-11 10:00
 */

@Component
@Slf4j
@Transactional
public class ExpressAllocationDelService extends CommandAdapter {
    @Autowired
    private StCExpressAllocationMapper stCExpressAllocationMapper;

    @Autowired
    private StCExpressAllocationItemMapper stCExpressAllocationItemMapper;

    /**
     * 主子表删除
     *
     * @param querySession
     * @return
     * @throws NDSException
     */
    @Override
    public ValueHolder execute(QuerySession querySession) throws NDSException {
        ValueHolder holder = new ValueHolder();
        DefaultWebEvent event = querySession.getEvent();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(
                event.getParameterValue("param"), "yyyy-MM-dd HH:mm:ss"), Feature.OrderedField);

        if (param == null || param.size() == 0) {
            holder = ValueHolderUtils.getFailValueHolder("参数为空!");
            return holder;
        }

        String isDel = param.getString("isdelmtable");
        Long objid = param.getLong("objid");

        //判断主表是否存在
        StCExpressAllocationDO stCExpressAllocationDO = stCExpressAllocationMapper.selectById(objid);
        if (stCExpressAllocationDO == null) {
            holder = ValueHolderUtils.getFailValueHolder("当前记录已不存在！");
            return holder;
        }

        String isActive = stCExpressAllocationDO.getIsactive();
        if ("N".equals(isActive) ) {
            holder = ValueHolderUtils.getFailValueHolder("当前记录已作废，不允许重复作废！");
            return holder;
        }

        JSONObject tabitem = param.getJSONObject("tabitem");

        //判断是删除主表还是明细表单独删除
        if ("false".equals(isDel)) {
            JSONArray itemArray = tabitem.getJSONArray("ST_C_EXPRESS_ALLOCATION_ITEM");
            //单独删除明细
            holder = delItem(itemArray, objid, querySession, stCExpressAllocationDO);
        } else {
            stCExpressAllocationDO.setDelid(2L);
            StBeanUtils.makeModifierField(stCExpressAllocationDO, querySession.getUser());
            stCExpressAllocationMapper.updateById(stCExpressAllocationDO);
            holder = ValueHolderUtils.getDeleteSuccessValueHolder();
        }
        return holder;
    }

    /**
     * 删除明细表
     *
     * @param itemArray 子表数据
     * @param mainId    主表id
     * @return 返回状态
     */
    public ValueHolder delItem(JSONArray itemArray, Long mainId, QuerySession querySession,
                               StCExpressAllocationDO stCExpressAllocationDO) {
        ValueHolder holder = new ValueHolder();
        int iSuc = 0;
        //明细表记录不存在，则提示：当前记录已不存在！
        for (int i = 0; i < itemArray.size(); i++) {
            Long itemId = itemArray.getLong(i);
            StCExpressAllocationItemDO stCExpressAllocationItemDO = stCExpressAllocationItemMapper.selectById(itemId);
            if (stCExpressAllocationItemDO != null) {
                if (stCExpressAllocationItemMapper.deleteById(itemId) > 0) {
                    iSuc += 1;
                }
            }
        }
        if (iSuc > 0) {
            StBeanUtils.makeModifierField(stCExpressAllocationDO, querySession.getUser());
            stCExpressAllocationMapper.updateById(stCExpressAllocationDO);
            holder = ValueHolderUtils.getDeleteSuccessValueHolder();
        } else {
            holder = ValueHolderUtils.getFailValueHolder("删除记录不存在！");
        }
        return holder;
    }
}
