package com.jackrain.nea.st.services;

import com.google.common.base.Throwables;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.st.mapper.StCOwnershipWarehouseSetMapper;
import com.jackrain.nea.st.mapper.StCSyncStockStrategyMapper;
import com.jackrain.nea.st.model.result.OwnershipWarehouseSetSyncResult;
import com.jackrain.nea.st.model.table.StCOwnershipWarehouseSetDO;
import com.jackrain.nea.st.utils.ValueHolderUtils;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.sql.Timestamp;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;

/**
 * @author: 汪聿森
 * @Date: Created in 2020-02-19 11:49
 * @Description : 库存归属仓库属性设置同步
 */
@Component
@Slf4j
public class OwnershipWarehouseSetSyncService extends CommandAdapter {
    @Autowired
    private StCSyncStockStrategyMapper mapper;
    @Autowired
    private StCOwnershipWarehouseSetMapper stCOwnershipWarehouseSetMapper;

    @Override
    public ValueHolder execute(QuerySession session) throws NDSException {
        ValueHolder holder = new ValueHolder();
        //根据渠道类型+仓库ID分组  获取店铺同步库存策略信息
        List<OwnershipWarehouseSetSyncResult> ownershipWarehouseSetSyncResultList = mapper.selectAllByChannelTypeAndStoreId();
        if(log.isDebugEnabled()){
            log.debug(LogUtil.format("ownershipWarehouseSetSyncResultList:") + ownershipWarehouseSetSyncResultList);
        }
        if (CollectionUtils.isNotEmpty(ownershipWarehouseSetSyncResultList)) {
            int count = 0;//记录同步失败+无需同步的条数
            for (OwnershipWarehouseSetSyncResult ownershipWarehouseSetSyncResult : ownershipWarehouseSetSyncResultList) {
                Long channelType = Long.valueOf(ownershipWarehouseSetSyncResult.getChannelType());
                Long cpCStoreId = ownershipWarehouseSetSyncResult.getCpCStoreId();
                //根据仓库id+渠道类型判断是否已经存在
                StCOwnershipWarehouseSetDO stCOwnershipWarehouseSetDOExist = stCOwnershipWarehouseSetMapper.selectDataByStoreId(cpCStoreId, channelType);
                if (stCOwnershipWarehouseSetDOExist == null) {//不存在，新增
                    StCOwnershipWarehouseSetDO stCOwnershipWarehouseSetDO = new StCOwnershipWarehouseSetDO();
                    stCOwnershipWarehouseSetDO.setId(ModelUtil.getSequence("ST_C_OWNERSHIP_WAREHOUSE_SET"));
                    stCOwnershipWarehouseSetDO.setCpCStoreId(cpCStoreId);
                    stCOwnershipWarehouseSetDO.setCpCStoreEcode(ownershipWarehouseSetSyncResult.getCpCStoreEcode() == null ? null : ownershipWarehouseSetSyncResult.getCpCStoreEcode());
                    stCOwnershipWarehouseSetDO.setCpCStoreEname(ownershipWarehouseSetSyncResult.getCpCStoreEname() == null ? null : ownershipWarehouseSetSyncResult.getCpCStoreEname());
                    stCOwnershipWarehouseSetDO.setCpCStorePropertyType(Long.valueOf(ownershipWarehouseSetSyncResult.getChannelType()));
                    Timestamp timestamp = new Timestamp(System.currentTimeMillis());
                    //基本字段值设置
                    stCOwnershipWarehouseSetDO.setOwnerename(session.getUser().getEname());
                    stCOwnershipWarehouseSetDO.setOwnerid(Long.valueOf(session.getUser().getId()));
                    stCOwnershipWarehouseSetDO.setOwnername(session.getUser().getName());
                    stCOwnershipWarehouseSetDO.setModifiername(session.getUser().getName());
                    stCOwnershipWarehouseSetDO.setModifierename(session.getUser().getEname());
                    stCOwnershipWarehouseSetDO.setModifierid(Long.valueOf(session.getUser().getId()));
                    stCOwnershipWarehouseSetDO.setCreationdate(timestamp);
                    stCOwnershipWarehouseSetDO.setModifieddate(timestamp);
                    stCOwnershipWarehouseSetDO.setAdClientId((long) session.getUser().getClientId());
                    stCOwnershipWarehouseSetDO.setAdOrgId((long) session.getUser().getOrgId());
                    try {
                        int insertResult = stCOwnershipWarehouseSetMapper.insert(stCOwnershipWarehouseSetDO);
                        if (insertResult <= 0) {
                            count++;
                        }
                    } catch (Exception ex) {
                        log.error(LogUtil.format("OwnershipWarehouseSetSyncService.execute Error{}"),
                                Throwables.getStackTraceAsString(ex));
                        return ValueHolderUtils.getFailValueHolder("保存异常add");
                    }
                } else {
                    StCOwnershipWarehouseSetDO stCOwnershipWarehouseSetDO = new StCOwnershipWarehouseSetDO();
                    stCOwnershipWarehouseSetDO.setId(stCOwnershipWarehouseSetDOExist.getId());
                    stCOwnershipWarehouseSetDO.setCpCStoreEcode(ownershipWarehouseSetSyncResult.getCpCStoreEcode() == null ? null : ownershipWarehouseSetSyncResult.getCpCStoreEcode());
                    stCOwnershipWarehouseSetDO.setCpCStoreEname(ownershipWarehouseSetSyncResult.getCpCStoreEname() == null ? null : ownershipWarehouseSetSyncResult.getCpCStoreEname());
                    stCOwnershipWarehouseSetDO.setCpCStorePropertyType(Long.valueOf(ownershipWarehouseSetSyncResult.getChannelType()));
                    Timestamp timestamp = new Timestamp(System.currentTimeMillis());
                    //基本字段值设置
                    stCOwnershipWarehouseSetDO.setOwnerename(session.getUser().getEname());
                    stCOwnershipWarehouseSetDO.setOwnerid(Long.valueOf(session.getUser().getId()));
                    stCOwnershipWarehouseSetDO.setOwnername(session.getUser().getName());
                    stCOwnershipWarehouseSetDO.setModifiername(session.getUser().getName());
                    stCOwnershipWarehouseSetDO.setModifierename(session.getUser().getEname());
                    stCOwnershipWarehouseSetDO.setModifierid(Long.valueOf(session.getUser().getId()));
                    stCOwnershipWarehouseSetDO.setCreationdate(timestamp);
                    stCOwnershipWarehouseSetDO.setModifieddate(timestamp);
                    try {
                        int updateResult = stCOwnershipWarehouseSetMapper.updateById(stCOwnershipWarehouseSetDO);
                        if (updateResult <= 0) {
                            count++;
                        }
                    } catch (Exception ex) {
                        log.error(LogUtil.format("OwnershipWarehouseSetSyncService.execute Error{}"),
                                Throwables.getStackTraceAsString(ex));
                        return ValueHolderUtils.getFailValueHolder("更新异常add");
                    }
                }
            }
            if (ownershipWarehouseSetSyncResultList.size() == count) {
                HashMap map = new HashMap();
                map.put("code", ResultCode.FAIL);
                map.put("message", Resources.getMessage("无数据同步", new Locale("zh", "CN")));
                holder.setData(map);
                return holder;
            }
            HashMap map = new HashMap();
            map.put("code", ResultCode.SUCCESS);
            map.put("message", Resources.getMessage("同步数：" + ownershipWarehouseSetSyncResultList.size() + "条，成功：" + (ownershipWarehouseSetSyncResultList.size() - count) + "条，失败或无需同步：" + count + "条", new Locale("zh", "CN")));
            holder.setData(map);
            return holder;
        } else {
            HashMap map = new HashMap();
            map.put("code", ResultCode.FAIL);
            map.put("message", Resources.getMessage("无数据同步", new Locale("zh", "CN")));
            holder.setData(map);
            return holder;
        }
    }

    /**
     * 根据仓库属性查询供货仓信息
     * @param channelType
     * @return
     */
    public List<StCOwnershipWarehouseSetDO> selectByChannelType(Long channelType){
        return stCOwnershipWarehouseSetMapper.selectByChannelType(channelType);
    }
}
