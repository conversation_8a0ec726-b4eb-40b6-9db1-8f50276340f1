package com.jackrain.nea.st.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.alibaba.fastjson.parser.Feature;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.google.common.base.Throwables;
import com.jackrain.nea.cpext.model.table.CpShop;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.st.mapper.StCResolveRuleMapper;
import com.jackrain.nea.st.model.table.StCResolveRuleDO;
import com.jackrain.nea.st.rpc.RpcCpService;
import com.jackrain.nea.st.utils.ValueHolderUtils;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.JSONUtils;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import com.jackrain.nea.st.utils.JsonUtils;

import java.sql.Timestamp;
import java.util.List;

/**
 * 解析规则新增保存接口
 *
 * <AUTHOR>
 * @since 20190307
 */
@Component
@Slf4j
@Transactional
public class ResolveRuleSaveService extends CommandAdapter {

    @Autowired
    private StCResolveRuleMapper stCResolveRuleMapper;

    @Autowired
    private RpcCpService rpcCpService;
    /**
     * @param session
     * @return
     * @Author: 朱宇军
     * @Date 2019/3/7
     */

    @Override
    public ValueHolder execute(QuerySession session) {
        DefaultWebEvent event = session.getEvent();
        ValueHolder valueHolder=new ValueHolder();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(
                event.getParameterValue("param"), "yyyy-MM-dd HH:mm:ss", SerializerFeature.WriteMapNullValue),
                Feature.OrderedField);
        log.debug(LogUtil.format("解析规则保存Json:") + param.toJSONString());
        Long id = param.getLong("objid");//获取objid参数
        if (param != null && id != null) {
            if (id != -1) {
                return updateResolveRule(session, param, id);
            } else {
                return addResolveRule(session, param);
            }
        }
        throw new NDSException("当前记录已不存在！");
    }
    /**
     * @param session
     * @param param
     * @param id
     * @return
     * @Author: 朱宇军
     * @Date 2019/3/7
     */

    private ValueHolder updateResolveRule(QuerySession session,JSONObject param, Long id) {
            StCResolveRuleDO stCResolveRuleDO = JsonUtils.jsonParseClass(param, StCResolveRuleDO.class);

            StCResolveRuleDO existsResolveRule = stCResolveRuleMapper.selectById(id);
            if (existsResolveRule == null) {
                throw new NDSException("当前记录已不存在！");
            }
            //update基础字段
            stCResolveRuleDO.setId(id);
            stCResolveRuleDO.setModifierid(Long.valueOf(session.getUser().getId()));
            stCResolveRuleDO.setModifieddate(new Timestamp(System.currentTimeMillis()));
            stCResolveRuleDO.setModifiername(session.getUser().getName());
            stCResolveRuleDO.setModifierename(session.getUser().getEname());
            try {
                if (stCResolveRuleMapper.updateById(stCResolveRuleDO) > 0) {
                    return ValueHolderUtils.getSuccessValueHolder("保存成功");
                } else {
                    return ValueHolderUtils.getFailValueHolder("保存失败");
                }
            } catch (Exception ex) {
                log.error(LogUtil.format("ResolveRuleSaveService.updateResolveRule Error：{}"),
                        Throwables.getStackTraceAsString(ex));
                return ValueHolderUtils.getFailValueHolder("保存异常update");
            }
    }

    /**
     * @param session
     * @param param
     * @return
     * @Author: 朱宇军
     * @Date 2019/3/7
     */

    private ValueHolder addResolveRule(QuerySession session, JSONObject param) {
        Long platformId=param.getLong("CP_C_PLATFORM_ID");
        StCResolveRuleDO stCResolveRuleDO=JsonUtils.jsonParseClass(param,StCResolveRuleDO.class);
        stCResolveRuleDO.setId(ModelUtil.getSequence("ST_C_RESOLVE_RULE"));
        if(log.isDebugEnabled()){
            log.info(LogUtil.format("插入ID： ->")+stCResolveRuleDO.getId());
        }
        Timestamp timestamp = new Timestamp(System.currentTimeMillis());

        //基本字段值设置
        stCResolveRuleDO.setOwnerename(session.getUser().getEname());
        stCResolveRuleDO.setOwnerid(Long.valueOf(session.getUser().getId()));
        stCResolveRuleDO.setOwnername(session.getUser().getName());
        stCResolveRuleDO.setCreationdate(timestamp);

        stCResolveRuleDO.setAdClientId((long) session.getUser().getClientId());
        stCResolveRuleDO.setAdOrgId((long) session.getUser().getOrgId());
        //stCResolveRuleDO.setModifierename(session.getUser().getEname());
        //stCResolveRuleDO.setModifieddate(timestamp);
//        stCResolveRuleDO.setModifierid(Long.valueOf(session.getUser().getId()));
//        stCResolveRuleDO.setModifiername(session.getUser().getEname());

        try {
            int insertResult = stCResolveRuleMapper.insert(stCResolveRuleDO);
            if (insertResult > 0) {
                return ValueHolderUtils.getSuccessValueHolder("保存成功");
            } else {
                return ValueHolderUtils.getFailValueHolder("保存失败");

            }
        } catch (Exception ex) {
            log.error(LogUtil.format("ResolveRuleSaveService.addResolveRule Error{}"), Throwables.getStackTraceAsString(ex));
            return ValueHolderUtils.getFailValueHolder("保存异常add");
        }
    }
}
