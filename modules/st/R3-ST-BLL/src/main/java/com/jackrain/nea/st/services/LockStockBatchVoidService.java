package com.jackrain.nea.st.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.burgeon.r3.sg.basic.api.SgLockStockBasicCmd;
import com.google.common.base.Throwables;
import com.google.common.collect.Lists;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.st.common.StConstant;
import com.jackrain.nea.st.mapper.StCLockStockStrategyItemMapper;
import com.jackrain.nea.st.mapper.StCLockStockStrategyMapper;
import com.jackrain.nea.st.model.table.StCLockStockStrategyDO;
import com.jackrain.nea.st.model.table.StCLockStockStrategyItemDO;
import com.jackrain.nea.st.utils.DatasToEsUtils;
import com.jackrain.nea.st.utils.ListUtils;
import com.jackrain.nea.st.utils.StBeanUtils;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.Date;
import java.util.HashMap;
import java.util.List;

/**
 * 店铺锁库策略设置
 *
 * <AUTHOR>
 * @Date 2019/3/8 10:02
 */
@Component
@Slf4j
public class LockStockBatchVoidService extends CommandAdapter {
    @Autowired
    private StCLockStockStrategyMapper lockStockMapper;

    @Autowired
    private StCLockStockStrategyItemMapper stCLockStockStrategyItemMapper;

    @Reference(group = "sg", version = "1.0")
    private SgLockStockBasicCmd sgLockStockBasicCmd;

//    @Autowired
//    private ShopLockSkuSyncStockService shopLockSkuSyncStockService;

    @Override
    public ValueHolder execute(QuerySession session) throws NDSException {
        DefaultWebEvent event = session.getEvent();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(
                event.getParameterValue("param"), "yyyy-MM-dd HH:mm:ss", SerializerFeature.WriteMapNullValue),
                Feature.OrderedField);
        HashMap<Long, Object> errMap = new HashMap<>(16);
        //生成Json数组
        JSONArray voidArray = StBeanUtils.makeVoidJsonArray(param);
        //列表批量
        if (!CollectionUtils.isEmpty(voidArray)) {
            List<Long> ids = Lists.newArrayListWithExpectedSize(voidArray.size());
            for (Object o : voidArray) {
                // 遍历 jsonarray 数组，把每一个对象转成 json 对象
                long id = Long.parseLong(o.toString());
                try {
                    voidLockStock(id, session, ids);
                } catch (Exception ex) {
                    errMap.put(id, ex.getMessage());
                }
            }
            // 作废同步PG
            sgLockStockBasicCmd.toVoid(ids);
        }
        //返回日志
        return StBeanUtils.getExcuteValueHolder(voidArray.size(), errMap);
    }

    /**
     * 作废
     *
     * @param id
     * @param session
     * @return com.jackrain.nea.util.ValueHolder
     * @Author: 陈秀楼
     * @Date 2019/3/8
     */
    private void voidLockStock(long id, QuerySession session,List<Long> ids) {
        //验证
        checkLockStock(id);
        //作废数据
        StCLockStockStrategyDO lockStock = new StCLockStockStrategyDO();
        lockStock.setId(id);
        //作废状态 作废 作废人 作废时间 修改信息
        lockStock.setIsactive(StConstant.ISACTIVE_N);
        lockStock.setEstatus(StConstant.CON_BILL_STATUS_03);
        lockStock.setDelerId(Long.valueOf(session.getUser().getId()));
        lockStock.setDelname(session.getUser().getName());
        lockStock.setDelename(session.getUser().getEname());
        lockStock.setDelTime(new Date());
        StBeanUtils.makeModifierField(lockStock, session.getUser());

        int update = lockStockMapper.updateById(lockStock);
        if (update < 0) {
            throw new NDSException("作废失败！");
        }else{
            //同步更新到同步库存中间表
            ids.add(id);
            //推送ES数据
            try {
                //做更新的需要先查询更新后数据库的实体在推ES
                lockStock = lockStockMapper.selectById(id);
                StCLockStockStrategyItemDO item = new StCLockStockStrategyItemDO();
                item.setStatus(StConstant.CON_BILL_STATUS_03);
                QueryWrapper<StCLockStockStrategyItemDO> wrapper = new QueryWrapper<>();
                wrapper.eq("st_c_lock_stock_strategy_id", id);
                stCLockStockStrategyItemMapper.update(item, wrapper);
                List<StCLockStockStrategyItemDO> itemList = ListUtils.batchQueryByCondition(wrapper, stCLockStockStrategyItemMapper, 1000);
                DatasToEsUtils.insertLoclStockEsData(lockStock,null,StConstant.TAB_ST_C_LOCK_STOCK_STRATEGY);
                if (org.apache.commons.collections.CollectionUtils.isNotEmpty(itemList)) {
                    DatasToEsUtils.insertLoclStockEsData(lockStock, itemList, StConstant.TAB_ST_C_LOCK_STOCK_STRATEGY_ITEM);
                }
            } catch (Exception ex) {
                log.debug(LogUtil.format("店铺锁库策略作废推数据到ES失败：{}"), Throwables.getStackTraceAsString(ex));
            }
        }
    }

    /**
     * 检查
     *
     * @param id
     * @return java.lang.String
     * @Author: 陈秀楼
     * @Date 2019/3/8
     */
    private void checkLockStock(long id) {
        StCLockStockStrategyDO lockStock = lockStockMapper.selectById(id);
        if (lockStock == null) {
            throw new NDSException("当前记录已不存在！");
        } else {
            if (lockStock.getIsactive().equals(StConstant.ISACTIVE_N)) {
                throw new NDSException("当前记录已作废，不允许重复作废！");
            }
            if (lockStock.getEstatus().equals(StConstant.CON_BILL_STATUS_03)) {
                throw new NDSException("当前记录已作废，不允许重复作废！");
            }
            if (lockStock.getEstatus().equals(StConstant.CON_BILL_STATUS_04)) {
                throw new NDSException("当前记录已结案，不允许作废！");
            }
            if (lockStock.getEstatus().equals(StConstant.CON_BILL_STATUS_02)) {
                throw new NDSException("当前记录已审核，不允许作废！");
            }
        }
    }


}
