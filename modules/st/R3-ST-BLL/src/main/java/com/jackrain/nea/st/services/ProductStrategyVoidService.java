package com.jackrain.nea.st.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.st.common.StConstant;
import com.jackrain.nea.st.mapper.StCProductStrategyItemMapper;
import com.jackrain.nea.st.mapper.StCProductStrategyMapper;
import com.jackrain.nea.st.model.table.StCProductStrategyDO;
import com.jackrain.nea.st.model.table.StCProductStrategyItemDO;
import com.jackrain.nea.st.utils.DatasToEsUtils;
import com.jackrain.nea.st.utils.ListUtils;
import com.jackrain.nea.st.utils.StBeanUtils;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.HashMap;
import java.util.List;

@Component
@Slf4j
@Transactional
public class ProductStrategyVoidService extends CommandAdapter {

    @Autowired
    private StCProductStrategyMapper stCProductStrategyMapper;

    @Autowired
    private StCProductStrategyItemMapper stCProductStrategyItemMapper;

    /**
     * @return com.jackrain.nea.util.ValueHolder
     * <AUTHOR>
     * @Description 作废操作
     * @Date 2019/3/12
     * @Param [session]
     **/
    public ValueHolder voidProductStrategy(QuerySession session) {
        DefaultWebEvent event = session.getEvent();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(
                event.getParameterValue("param"), "yyyy-MM-dd HH:mm:ss", SerializerFeature.WriteMapNullValue),
                Feature.OrderedField);
        log.debug(LogUtil.format("请求JSON：") + param.toString());
        if (param == null) {
            throw new NDSException("参数为空！");
        }
        //生成作废Json数组
        JSONArray auditArray = StBeanUtils.makeAuditJsonArray(param);
        HashMap<Long, Object> errorMap = new HashMap<Long, Object>();
        ValueHolder valueHolder = new ValueHolder();

        for (int i = 0; i < auditArray.size(); i++) {
            Long id = Long.valueOf(auditArray.get(i).toString());
            try {
                //作废验证
                voidCheck(id);
                StCProductStrategyDO stCProductStrategyDO = new StCProductStrategyDO();
                stCProductStrategyDO.setId(id);
                stCProductStrategyDO.setIsactive("N");//作废
                stCProductStrategyDO.setEstatus(StConstant.CON_BILL_STATUS_03);
                makeVoidField(stCProductStrategyDO, session.getUser());
                //更新
                int count = stCProductStrategyMapper.updateById(stCProductStrategyDO);
                if (count < 0) {
                    log.debug(LogUtil.format("更新作废信息失败！", id));
                    throw new Exception();
                }else{
                    //更新明细表状态，并主表和明细表都推送ES数据
                    try {
                        //做更新的需要先查询更新后数据库的实体在推ES
                        stCProductStrategyDO = stCProductStrategyMapper.selectById(id);
                        StCProductStrategyItemDO item = new StCProductStrategyItemDO();
                        item.setStatus(StConstant.CON_BILL_STATUS_03);
                        QueryWrapper<StCProductStrategyItemDO> wrapper = new QueryWrapper<>();
                        wrapper.eq("st_c_product_strategy_id", id);
                        stCProductStrategyItemMapper.update(item, wrapper);
                        List<StCProductStrategyItemDO> itemList = ListUtils.batchQueryByCondition(wrapper, stCProductStrategyItemMapper, 1000);
                        DatasToEsUtils.insertProductEsData(stCProductStrategyDO,null,StConstant.TAB_ST_C_PRODUCT_STRATEGY);
                        if (CollectionUtils.isNotEmpty(itemList)) {
                            DatasToEsUtils.insertProductEsData(stCProductStrategyDO, itemList, StConstant.TAB_ST_C_PRODUCT_STRATEGY_ITEM);
                        }
                    } catch (Exception ex) {
                        log.debug(StConstant.TAB_ST_C_PRODUCT_STRATEGY + "店铺商品特殊设置主表更新作废信息数据至ES失败：" + ex.toString());
                    }
                }
            } catch (Exception e) {
                errorMap.put(id, e.getMessage());
            }
        }
        valueHolder = StBeanUtils.getExcuteValueHolder(auditArray.size(),errorMap);
        return valueHolder;
    }

    /**
     * @return void
     * <AUTHOR>
     * @Description 构造作废人基本bean
     * @Date 2019/3/12
     * @Param [stCProductStrategyDO, user]
     **/
    private void makeVoidField(StCProductStrategyDO stCProductStrategyDO, User user) {
        stCProductStrategyDO.setDelTime(new Date());
        stCProductStrategyDO.setDelerId(Long.valueOf(user.getId()));//作废人ID
        stCProductStrategyDO.setDelname(user.getName());
        stCProductStrategyDO.setDelename(user.getEname());

        stCProductStrategyDO.setModifierid(Long.valueOf(user.getId()));//修改人Id
        stCProductStrategyDO.setModifiername(user.getName());
        stCProductStrategyDO.setModifierename(user.getEname());
        stCProductStrategyDO.setModifieddate(new Date());
    }

    /**
     * @return com.alibaba.fastjson.JSONObject
     * <AUTHOR>
     * @Description 作废验证
     * @Date 2019/3/12
     * @Param [objid]
     **/
    private JSONObject voidCheck(Long objid) throws Exception {

        //记录不存在
        StCProductStrategyDO stCProductStrategyDO = stCProductStrategyMapper.selectById(objid);
        if (stCProductStrategyDO == null) {
            JSONObject errJo = new JSONObject();
            errJo.put("objid", objid);
            errJo.put("message", "当前记录已不存在！");
            log.debug(LogUtil.format("当前记录已不存在！", objid));
            throw new Exception("当前记录已不存在！");
        }

        //已作废
        StCProductStrategyDO stCProductStrategyDO1 = stCProductStrategyMapper.selectByIdAndIsactive(objid, "N");
        if (stCProductStrategyDO1 != null) {
            JSONObject errJo = new JSONObject();
            errJo.put("objid", objid);
            errJo.put("message", "当前记录已作废，不允许重复作废！");
            log.debug(LogUtil.format("当前记录已作废，不允许重复作废！", objid));
            throw new Exception("当前记录已作废，不允许重复作废！");
        }

        //非未审核
        StCProductStrategyDO stCProductStrategyDO2 = stCProductStrategyMapper.selectByIdAndStatus(objid, StConstant.CON_BILL_STATUS_01);
        if (stCProductStrategyDO2 == null) {
            JSONObject errJo = new JSONObject();
            errJo.put("objid", objid);
            errJo.put("message", "方案状态不是未审核，不允许作废！");
            log.debug(LogUtil.format("方案状态不是未审核，不允许作废！", objid));
            throw new Exception("方案状态不是未审核，不允许作废！");
        }

        return null;
    }
}
