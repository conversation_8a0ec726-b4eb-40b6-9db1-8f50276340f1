package com.jackrain.nea.st.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.st.mapper.StCVipcomMailMapper;
import com.jackrain.nea.st.model.table.StCResolveRuleDO;
import com.jackrain.nea.st.model.table.StCVipcomMailDO;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 * @Date 2019/3/11 15:38
 */
@Component
@Slf4j
@Transactional
public class VipcomMailDelService extends CommandAdapter {

    @Autowired
    private StCVipcomMailMapper stCVipcomMailMapper;

    /**
     * @param session
     * @return
     * @Author: 朱宇军
     * @Date 2019/3/11
     */

    @Override
    public ValueHolder execute(QuerySession session) throws NDSException {
        DefaultWebEvent event = session.getEvent();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(
                event.getParameterValue("param"), "yyyy-MM-dd HH:mm:ss"),
                Feature.OrderedField);
        Long id = param.getLong("objid");
        ValueHolder valueHolder = new ValueHolder();
        if (id != null && id > 0) {
            // 判断主表;
            StCVipcomMailDO stCVipcomMailDO = stCVipcomMailMapper.selectById(id);
            if (stCVipcomMailDO != null) {
                stCVipcomMailMapper.deleteById(id);
                valueHolder.put("code", 0);
                valueHolder.put("message", "删除成功！");
            }
        } else {
            valueHolder.put("code", 1);
            valueHolder.put("message", "当前记录已不存在！");
        }
        return valueHolder;
    }
}
