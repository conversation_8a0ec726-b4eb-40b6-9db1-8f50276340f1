package com.jackrain.nea.st.services;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.jackrain.nea.cpext.model.table.CpCPhyWarehouse;
import com.google.common.base.Throwables;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.oc.oms.model.resources.OmsRedisKeyResources;
import com.jackrain.nea.redis.config.CusRedisTemplate;
import com.jackrain.nea.redis.util.RedisOpsUtil;
import com.jackrain.nea.st.common.StConstant;
import com.jackrain.nea.st.mapper.StCVipcomJitxWarehouseLogMapper;
import com.jackrain.nea.st.mapper.StCVipcomJitxWarehouseMapper;
import com.jackrain.nea.st.model.table.StCVipcomJitxWarehouse;
import com.jackrain.nea.st.model.table.StCVipcomJitxWarehouseLog;
import com.jackrain.nea.st.rpc.RpcCpService;
import com.jackrain.nea.st.utils.JsonUtils;
import com.jackrain.nea.st.utils.ValueHolderUtils;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.face.impl.R3SystemUserResource;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Description
 * @createDate 2020/8/12 2:50 下午
 **/
@Component
@Slf4j
@Transactional
public class VipcomJixtWarehouseSaveService extends CommandAdapter {
    @Autowired
    StCVipcomJitxWarehouseMapper stCVipcomJitxWarehouseMapper;
    @Autowired
    StCVipcomJitxWarehouseLogMapper stCVipcomJitxWarehouseLogMapper;
    @Autowired
    private RpcCpService rpcCpService;

    /**
     * 唯品会JITX仓库对照表保存
     *
     * @param session
     * @return
     */
    @Override
    public ValueHolder execute(QuerySession session) {
        log.debug(LogUtil.format("start唯品会JITX仓库对照表保存！"));
        DefaultWebEvent event = session.getEvent();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(event.getParameterValue("param"),
                "yyyy-MM-dd HH:mm:ss", SerializerFeature.WriteMapNullValue), Feature.OrderedField);
        if (param == null) {
            throw new NDSException("参数为空！");
        }
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("Start VipcomJixtWarehouseSaveService.execute. ReceiveParams: {}"),
                    JSONObject.toJSONString(param, SerializerFeature.WriteMapNullValue));
        }
        Long id = param.getLong("objid");
        JSONObject fixColumn = param.getJSONObject("fixcolumn");
        if (fixColumn != null && id != null) {
            if (id != -1) {
                return updateVipcomJixtWarehouse(session, fixColumn, id, param);
            } else {
                return addVipcomJixtWarehouse(session, fixColumn);
            }
        }
        throw new NDSException("当前记录已不存在！");
    }

    /**
     * 唯品会JITX仓库对照表保存
     *
     * @param session
     * @param fixColumn
     * @return
     */
    private ValueHolder addVipcomJixtWarehouse(QuerySession session, JSONObject fixColumn) {
        JSONObject jsonObject = fixColumn.getJSONObject(StConstant.TAB_ST_C_VIPCOM_JITX_WAREHOUSE);
        String json = jsonObject.toString();
        if (StringUtils.isNotEmpty(json)) {

            StCVipcomJitxWarehouse stCVipcomJitxWarehouse = JsonUtils.jsonParseClass(jsonObject, StCVipcomJitxWarehouse.class);
            //校验重复数据店铺+实体仓+唯品会仓库编码唯一
            List<StCVipcomJitxWarehouse> vipcomJitxWarehouse = checkVipcomJitlWarehouse(stCVipcomJitxWarehouse);
            if (!CollectionUtils.isEmpty(vipcomJitxWarehouse)) {
                return ValueHolderUtils.getFailValueHolder("录入的店铺+实体仓唯一或者店铺+唯品会仓库编码唯一，请重新选择！！");
            }
            User user = session.getUser();
            stCVipcomJitxWarehouse.setId(ModelUtil.getSequence(StConstant.TAB_ST_C_VIPCOM_JITX_WAREHOUSE));
            //基础信息赋值
            stCVipcomJitxWarehouse.setAdClientId(R3SystemUserResource.DEFAULT_AD_CLIENT_ID);
            stCVipcomJitxWarehouse.setAdOrgId(R3SystemUserResource.DEFAULT_AD_ORG_ID);
            stCVipcomJitxWarehouse.setCreationdate(new Date());
            stCVipcomJitxWarehouse.setOwnerid(Long.valueOf(user.getId()));
            stCVipcomJitxWarehouse.setOwnername(user.getName());
            stCVipcomJitxWarehouse.setOwnerename(user.getEname());
            stCVipcomJitxWarehouse.setIsactive("Y");
            //保存实体仓编码
            CpCPhyWarehouse cpCPhyWarehouse = rpcCpService.getCpCPhyWahouseDoById(stCVipcomJitxWarehouse.getCpCPhyWarehouseId());
            stCVipcomJitxWarehouse.setCpCPhyWarehouseEcode(cpCPhyWarehouse.getEcode());
            try {
                int count = this.stCVipcomJitxWarehouseMapper.insert(stCVipcomJitxWarehouse);
                if (count > 0) {
                    return ValueHolderUtils.getSuccessValueHolder(stCVipcomJitxWarehouse.getId(), StConstant.TAB_ST_C_VIPCOM_JITX_WAREHOUSE);
                } else {
                    return ValueHolderUtils.getFailValueHolder("保存失败");
                }
            } catch (Exception ex) {
                log.error(LogUtil.format("VipcomJixtWarehouseSaveService.addVipcomJixtWarehouse Error{}"),
                        Throwables.getStackTraceAsString(ex));
                return ValueHolderUtils.getFailValueHolder("唯品会JITX仓库对照表保存异常！！！");
            }
        }
        throw new NDSException("当前表ST_C_VIPCOM_JITX_WAREHOUSE不存在！");
    }

    /**
     * 校验重复数据店铺+实体仓+唯品会仓库编码唯一
     *
     * @param stCVipcomJitxWarehouse
     * @return
     */
    private List<StCVipcomJitxWarehouse> checkVipcomJitlWarehouse(StCVipcomJitxWarehouse stCVipcomJitxWarehouse) {
        List<StCVipcomJitxWarehouse> exitList = new ArrayList<>();
        if (stCVipcomJitxWarehouse.getCpCPhyWarehouseId() != null) {
            StCVipcomJitxWarehouse vipcomJitxWarehouse = this.stCVipcomJitxWarehouseMapper
                    .selectOne(new QueryWrapper<StCVipcomJitxWarehouse>()
                            .eq("CP_C_SHOP_ID", stCVipcomJitxWarehouse.getCpCShopId())
                            .eq("CP_C_PHY_WAREHOUSE_ID", stCVipcomJitxWarehouse.getCpCPhyWarehouseId()));
            if (vipcomJitxWarehouse != null) {
                exitList.add(vipcomJitxWarehouse);
            }
        }
        if (StringUtils.isNotBlank(stCVipcomJitxWarehouse.getVipcomWarehouseEcode())) {
            StCVipcomJitxWarehouse vipcomJitxWarehouse = this.stCVipcomJitxWarehouseMapper
                    .selectOne(new QueryWrapper<StCVipcomJitxWarehouse>()
                            .eq("CP_C_SHOP_ID", stCVipcomJitxWarehouse.getCpCShopId())
                            .eq("VIPCOM_WAREHOUSE_ECODE", stCVipcomJitxWarehouse.getVipcomWarehouseEcode())
                            .eq("VIPCOM_SPECIAL_WAREHOUSE_ECODE", stCVipcomJitxWarehouse.getVipcomSpecialWarehouseEcode()));
            if (vipcomJitxWarehouse != null) {
                exitList.add(vipcomJitxWarehouse);
            }
        }

        return exitList;
    }

    /**
     * 更新唯品会JITX仓库对照表
     *
     * @param session
     * @param fixColumn
     * @param id
     * @return
     */
    private ValueHolder updateVipcomJixtWarehouse(QuerySession session, JSONObject fixColumn, Long id, JSONObject param) {
        JSONObject jsonObject = fixColumn.getJSONObject(StConstant.TAB_ST_C_VIPCOM_JITX_WAREHOUSE);
        StCVipcomJitxWarehouse vipcomJitxWarehouse = JsonUtils.jsonParseClass(jsonObject, StCVipcomJitxWarehouse.class);
        StCVipcomJitxWarehouse existssVipcomJitxWarehouse = this.stCVipcomJitxWarehouseMapper.selectById(id);
        log.debug(LogUtil.format("vipcomJitxWarehouse : ") + vipcomJitxWarehouse);
        if (existssVipcomJitxWarehouse == null) {
            throw new NDSException("当前记录已不存在！");
        }
        //校验重复数据店铺+实体仓+唯品会仓库编码唯一
        Long cpCShopId = existssVipcomJitxWarehouse.getCpCShopId();
        if (vipcomJitxWarehouse.getCpCShopId() != null) {
            cpCShopId = vipcomJitxWarehouse.getCpCShopId();
        }
        Long cpCPhyWarehouseId = existssVipcomJitxWarehouse.getCpCPhyWarehouseId();
        if (vipcomJitxWarehouse.getCpCPhyWarehouseId() != null) {
            cpCPhyWarehouseId = vipcomJitxWarehouse.getCpCPhyWarehouseId();
            //保存实体仓编码
            CpCPhyWarehouse cpCPhyWarehouse = rpcCpService.getCpCPhyWahouseDoById(cpCPhyWarehouseId);
            vipcomJitxWarehouse.setCpCPhyWarehouseEcode(cpCPhyWarehouse.getEcode());
        }
        String vipcomWarehouseEcode = existssVipcomJitxWarehouse.getVipcomWarehouseEcode();
        if (StringUtils.isNotBlank(vipcomJitxWarehouse.getVipcomWarehouseEcode())) {
            vipcomWarehouseEcode = vipcomJitxWarehouse.getVipcomWarehouseEcode();
        }
        vipcomJitxWarehouse.setCpCShopId(cpCShopId);
        vipcomJitxWarehouse.setCpCPhyWarehouseId(cpCPhyWarehouseId);
        vipcomJitxWarehouse.setVipcomWarehouseEcode(vipcomWarehouseEcode);
        List<StCVipcomJitxWarehouse> checkWarehouse = checkVipcomJitlWarehouse(vipcomJitxWarehouse);

        boolean flag = false;
        if (!CollectionUtils.isEmpty(checkWarehouse)) {
            for (StCVipcomJitxWarehouse jitPO : checkWarehouse) {
                if (!jitPO.getId().equals(existssVipcomJitxWarehouse.getId())) {
                    flag = true;
                    break;
                }
            }
        }

        if (flag) {
            return ValueHolderUtils.getFailValueHolder("录入的店铺+实体仓+唯品会仓库编码已存在，请重新选择！！");
        }

        if (vipcomJitxWarehouse != null) {
            log.debug(LogUtil.format("唯品会JITX仓库对照表更新----！"));
            //更新字段
            User user = session.getUser();
            vipcomJitxWarehouse.setId(id);
            vipcomJitxWarehouse.setModifieddate(new Date());
            vipcomJitxWarehouse.setModifierid(Long.valueOf(user.getId()));
            vipcomJitxWarehouse.setModifiername(user.getName());
            vipcomJitxWarehouse.setModifierename(user.getEname());

            try {
                JSONObject beforevalue = param.getJSONObject("beforevalue");
                JSONObject before = beforevalue.getJSONObject(beforevalue.keySet().iterator().next());
                JSONObject aftervalue = param.getJSONObject("aftervalue");
                JSONObject after = aftervalue.getJSONObject(aftervalue.keySet().iterator().next());
                batchInsertLog(before, after, id, user);
                if (log.isDebugEnabled()) {
                    log.debug(LogUtil.format("before:" + before + ",bfeforevalue:" + beforevalue + ",aftervalue:" + aftervalue + ",after" + after));
                }
                //非必填项
                if(after.containsKey("VIPCOM_UNSHOP_WAREHOUSE_ECODE") && (after.getString("VIPCOM_UNSHOP_WAREHOUSE_ECODE") == null || "".equals(after.getString("VIPCOM_UNSHOP_WAREHOUSE_ECODE")))){
                    vipcomJitxWarehouse.setVipcomUnshopWarehouseEcode("");
                }
                int count = this.stCVipcomJitxWarehouseMapper.updateById(vipcomJitxWarehouse);
                if (count > 0) {
                    CusRedisTemplate<String, String> strRedisTemplate = RedisOpsUtil.getStrRedisTemplate();
                    String redisKey = OmsRedisKeyResources.buildStJitxWarehouseRedisKey(cpCShopId, existssVipcomJitxWarehouse.getCpCPhyWarehouseId());
                    if (strRedisTemplate.hasKey(redisKey)){
                        strRedisTemplate.delete(redisKey);
                    }
                    redisKey = OmsRedisKeyResources.buildStJitxWarehouseRedisKey(cpCShopId, existssVipcomJitxWarehouse.getVipcomWarehouseEcode());
                    if (strRedisTemplate.hasKey(redisKey)){
                        strRedisTemplate.delete(redisKey);
                    }
                    if(StringUtils.isNotEmpty(existssVipcomJitxWarehouse.getVipcomWarehouseEcode())) {
                        redisKey = OmsRedisKeyResources.buildStJitxWarehouseRedisKeyByShopIdAndCode(cpCShopId, existssVipcomJitxWarehouse.getVipcomWarehouseEcode(), OmsRedisKeyResources.STORE);
                        if (strRedisTemplate.hasKey(redisKey)) {
                            strRedisTemplate.delete(redisKey);
                        }
                    }
                    if(StringUtils.isNotEmpty(existssVipcomJitxWarehouse.getVipcomUnshopWarehouseEcode())) {
                        redisKey = OmsRedisKeyResources.buildStJitxWarehouseRedisKeyByShopIdAndCode(cpCShopId, existssVipcomJitxWarehouse.getVipcomUnshopWarehouseEcode(), OmsRedisKeyResources.WAREHOUSE);
                        if (strRedisTemplate.hasKey(redisKey)) {
                            strRedisTemplate.delete(redisKey);
                        }
                    }
                    return ValueHolderUtils.getSuccessValueHolder(vipcomJitxWarehouse.getId(), StConstant.TAB_ST_C_VIPCOM_JITX_WAREHOUSE);
                } else {
                    return ValueHolderUtils.getFailValueHolder("保存失败");
                }
            } catch (Exception ex) {
                log.error(LogUtil.format("VipcomJixtWarehouseSaveService.addVipcomJixtWarehouse Error{}"),
                        Throwables.getStackTraceAsString(ex));
                return ValueHolderUtils.getFailValueHolder("唯品会JITX仓库对照表更新异常！！！");
            }
        }

        ValueHolder valueHolder = ValueHolderUtils.getSuccessValueHolder(id, StConstant.TAB_ST_C_VIPCOM_JITX_WAREHOUSE);
        return valueHolder;
    }


    /**
     * 插入修改日志
     *
     * @param before 开始
     * @param after  修改后
     * @param id     id
     */
    private void batchInsertLog(JSONObject before, JSONObject after, Long id, User user) {

        try {
            List<StCVipcomJitxWarehouseLog> logs = new ArrayList<>();
            before.keySet().forEach(
                    key -> {

                        Object bValue = before.get(key);
                        if (bValue == null) {
                            bValue = "";
                        }
                        Object aValue = after.get(key);
                        if (aValue == null) {
                            aValue = "";
                        }
                        StCVipcomJitxWarehouseLog log = establishLog(bValue.toString(), aValue.toString(), key, id, user);
                        logs.add(log);
                    }
            );
            this.stCVipcomJitxWarehouseLogMapper.batchInsert(logs);
        } catch (Exception e) {
            log.error(LogUtil.format("VipcomJixtWarehouseSaveService.batchInsertLog Error{}"), Throwables.getStackTraceAsString(e));
        }

    }

    /**
     * 实例化StCVipcomJitxWarehouseLog
     *
     * @param after  修改前参数
     * @param before 修改后参数参数
     * @param type   修改内容
     * @return StCVipcomJitxWarehouseLog 实体
     */
    private StCVipcomJitxWarehouseLog establishLog(String before, String after, String type, Long id, User user) {

        StCVipcomJitxWarehouseLog stCVipcomJitxWarehouseLog = new StCVipcomJitxWarehouseLog();
        stCVipcomJitxWarehouseLog.setId(ModelUtil.getSequence(StConstant.TAB_ST_C_VIPCOM_JITX_WAREHOUSE_LOG));
        stCVipcomJitxWarehouseLog.setOutId(id);
        stCVipcomJitxWarehouseLog.setModifyBefore(before);
        stCVipcomJitxWarehouseLog.setModifyAfter(after);
        stCVipcomJitxWarehouseLog.setModifyContent(type);
        stCVipcomJitxWarehouseLog.setModifieddate(new Date());
        stCVipcomJitxWarehouseLog.setModifierid(Long.valueOf(user.getId()));
        stCVipcomJitxWarehouseLog.setModifiername(user.getEname());
        stCVipcomJitxWarehouseLog.setIsactive("Y");
        stCVipcomJitxWarehouseLog.setAdClientId(R3SystemUserResource.DEFAULT_AD_CLIENT_ID);
        stCVipcomJitxWarehouseLog.setAdOrgId(R3SystemUserResource.DEFAULT_AD_ORG_ID);
        return stCVipcomJitxWarehouseLog;
    }
}





