package com.jackrain.nea.st.services;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.google.common.base.Throwables;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.cpext.model.table.CpCPhyWarehouse;
import com.jackrain.nea.cpext.model.table.CpCVipcomWahouse;
import com.jackrain.nea.psext.api.utils.JsonUtils;
import com.jackrain.nea.st.mapper.*;
import com.jackrain.nea.st.model.enums.SendRuleEtype;
import com.jackrain.nea.st.model.result.*;
import com.jackrain.nea.st.model.table.*;
import com.jackrain.nea.st.rpc.RpcCpService;
import com.jackrain.nea.st.utils.RegionTreeUtils;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.utility.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.assertj.core.util.Lists;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @author: huang.zaizai
 * @since: 2019/09/04
 * create at : 2019/7/24 09:00
 */
@Slf4j
@Component
public class SendRuleQueryService {
    @Autowired
    private RegionTreeQueryService regionTreeQueryService;
    @Autowired
    private StCSendRuleMapper mapper;
    @Autowired
    private StCSendRuleAddressRentMapper rentMapper;
    @Autowired
    private StCSendRuleAddressRankMapper rankMapper;
    @Autowired
    private StCSendRuleAddressVipMapper vipMapper;
    @Autowired
    private StCSendRuleWarehouseRateMapper rateMapper;
    @Autowired
    private RpcCpService rpcCpService;
    private List<StCSendRuleAddressVipDo> vipDoList;

    /**
     * 查询省市区树
     *
     * @param obj
     * @return ValueHolderV14
     */
    public ValueHolderV14 querySendRuleTree(JSONObject obj) {
        ValueHolderV14<SendRuleTreeResult> vh = new ValueHolderV14<>();
        vh.setCode(ResultCode.SUCCESS);
        if (null == obj || !obj.containsKey("objid")) {
            vh.setCode(ResultCode.FAIL);
            vh.setMessage("参数为空");
            return vh;
        }
        SendRuleTreeResult result = new SendRuleTreeResult();
        Long id = obj.getLong("objid");
        if (id > 0) {
            List<StCSendRuleAddressRentDO> stCSendRuleAddressRentDOList = getRentList(id);
            result.setSendRuleAddressRents(stCSendRuleAddressRentDOList);
        }
        //查询地址
        obj.put("regiontype", "PROV");
        ValueHolderV14<List<RegionTreeResult>> treeVh = regionTreeQueryService.queryRegionTree(obj);
        if (treeVh.isOK()) {
            Map<Long, StCSendRuleAddressRankDO> rankMap = new HashMap<>();
            if (id > 0) {
                List<StCSendRuleAddressRankDO> rankList = rankMapper.selectList(new QueryWrapper<StCSendRuleAddressRankDO>()
                        .lambda().eq(StCSendRuleAddressRankDO::getStCSendRuleId, id));
                for (StCSendRuleAddressRankDO rank : rankList) {
                    rankMap.put(rank.getCpCRegionProvinceId(), rank);
                }
            }
            //构造树
            List<RegionTreeResult> resultList = treeVh.getData();
            if (rankMap.size() > 0) {
                changeTreeChecked(resultList, rankMap);
            }
            result.setSendRuleTree(resultList);
        } else {
            vh.setCode(ResultCode.FAIL);
            vh.setMessage("省市区获取失败！");
            return vh;
        }
        vh.setData(result);
        return vh;
    }

    /**
     * 构建树
     *
     * @param resultList
     * @param rankMap
     * @return Boolean
     */
    private Boolean changeTreeChecked(List<RegionTreeResult> resultList,
                                      Map<Long, StCSendRuleAddressRankDO> rankMap) {
        Boolean checkedFlg = true;
        for (RegionTreeResult result : resultList) {
            if (rankMap.containsKey(result.getId())) {
                result.setChecked(true);
                if (CollectionUtils.isNotEmpty(result.getChildren())) {
                    changeTreeChecked(result.getChildren(), rankMap);
                }
            } else {
                if (CollectionUtils.isNotEmpty(result.getChildren())) {
                    result.setChecked(changeTreeChecked(result.getChildren(), rankMap));
                }
            }
            if (!result.getChecked()) {
                checkedFlg = false;
            }
        }
        return checkedFlg;
    }

    /**
     * 获取订单派单规则唯品会仓库树
     *
     * @param obj
     * @return
     */
    public ValueHolderV14 querySendRuleVipWarehouseTree(JSONObject obj) {
        ValueHolderV14<SendRuleTreeResult> vh = new ValueHolderV14<>();
        vh.setCode(ResultCode.FAIL);
        if (null == obj || !obj.containsKey("objid")) {
            vh.setMessage("参数为空");
            return vh;
        }
        //唯品会仓库信息
        List<CpCVipcomWahouse> cpCVipcomWahouseList = rpcCpService.getCpCVipcomWahouse();
        if (CollectionUtils.isEmpty(cpCVipcomWahouseList)) {
            vh.setMessage("唯品会仓库信息获取失败");
            return vh;
        }
        SendRuleTreeResult result = new SendRuleTreeResult();
        Long stCSendRuleId = obj.getLong("objid");
        if (stCSendRuleId > 0) {
            StCSendRuleDO stCSendRuleDO = mapper.selectById(stCSendRuleId);
            if (SendRuleEtype.VIP.getEtype().equals(stCSendRuleDO.getEtype())) {
                List<StCSendRuleAddressRentDO> stCSendRuleAddressRentDOList = getRentList(stCSendRuleId);
                result.setSendRuleAddressRents(stCSendRuleAddressRentDOList);
            }
        }
        //已选择的唯品会仓库ID集合
        List<Long> cpCVipcomWahouseIdList = getcpCVipcomWahouseIdList(stCSendRuleId);
        List<RegionTreeResult> regionTreeResultList = Lists.newArrayList();
        cpCVipcomWahouseList.forEach(cpCVipcomWahouse -> {
            RegionTreeResult regionTreeResult = new RegionTreeResult();
            regionTreeResult.setChecked(cpCVipcomWahouseIdList.contains(cpCVipcomWahouse.getId()));
            regionTreeResult.setId(cpCVipcomWahouse.getId());
            regionTreeResult.setEcode(cpCVipcomWahouse.getWarehouseCode());
            regionTreeResult.setTitle(cpCVipcomWahouse.getWarehouseName() + cpCVipcomWahouse.getWarehouseCode());
            regionTreeResultList.add(regionTreeResult);
        });
        result.setSendRuleTree(regionTreeResultList);
        vh.setCode(ResultCode.SUCCESS);
        vh.setData(result);
        return vh;
    }

    /**
     * <AUTHOR>
     * @create 2020-06-16 16:33
     * @desc 获取订单派单规则-唯品会仓库ID集合
     **/
    private List<Long> getcpCVipcomWahouseIdList(Long stCSendRuleId) {
        List<Long> cpCVipcomWahouseIdList = Lists.newArrayList();
        List<StCSendRuleAddressVipDo> vipDoList = getStCSendRuleAddressVipDoList(stCSendRuleId);
        if (CollectionUtils.isNotEmpty(vipDoList)) {
            cpCVipcomWahouseIdList = vipDoList.stream().map(StCSendRuleAddressVipDo::getCpCVipcomWahouseId).distinct().collect(Collectors.toList());
        }
        return cpCVipcomWahouseIdList;
    }

    /**
     * 获取订单派单规则-唯品会明细Map
     *
     * @param stCSendRuleId
     * @return
     */
    private Map<Long, StCSendRuleAddressVipDo> getStCSendRuleAddressVipDoMap(Long stCSendRuleId) {
        List<StCSendRuleAddressVipDo> stCSendRuleAddressVipDoList = getStCSendRuleAddressVipDoList(stCSendRuleId);
        if (CollectionUtils.isEmpty(stCSendRuleAddressVipDoList)) {
            return null;
        }
        Map<Long, StCSendRuleAddressVipDo> vipDoMap = stCSendRuleAddressVipDoList.stream().collect(Collectors.toMap(StCSendRuleAddressVipDo::getCpCVipcomWahouseId, a -> a));
        return vipDoMap;
    }

    /**
     * <AUTHOR>
     * @create 2020-06-16 17:13
     * @desc 获取订单派单规则-唯品会明细
     **/
    private List<StCSendRuleAddressVipDo> getStCSendRuleAddressVipDoList(Long stCSendRuleId) {
        return vipMapper.selectList(new QueryWrapper<StCSendRuleAddressVipDo>().lambda().eq(StCSendRuleAddressVipDo::getStCSendRuleId, stCSendRuleId));
    }

    /**
     * <AUTHOR>
     * @create 2020-06-16 15:58
     * @desc 获取已选择的仓库信息
     **/
    private List<StCSendRuleAddressRentDO> getRentList(Long stCSendRuleId) {
        List<StCSendRuleAddressRentDO> rentList = rentMapper.selectList(new QueryWrapper<StCSendRuleAddressRentDO>().lambda().eq(StCSendRuleAddressRentDO::getStCSendRuleId, stCSendRuleId));
        if (CollectionUtils.isNotEmpty(rentList)) {
            rentList = rentList.stream().sorted(Comparator.comparing(StCSendRuleAddressRentDO::getId)).collect(Collectors.toList());
        } else {
            rentList = Lists.newArrayList();
        }
        return rentList;
    }

    /**
     * <AUTHOR>
     * @create 2020-06-16 17:07
     * @desc 获取唯品会仓库优先级明细数据
     **/
    public ValueHolderV14 queryVipWarehouseRankResultTable(JSONObject obj) {
        ValueHolderV14<List<SendRuleAddressVipResult>> vh = new ValueHolderV14<>();
        vh.setCode(ResultCode.FAIL);
        if (null == obj || !obj.containsKey("objid")) {
            vh.setMessage("参数为空");
            return vh;
        }
        Long stCSendRuleId = obj.getLong("objid");
        String treeNode = JSONArray.toJSONString(obj.getJSONArray("treeNode"));
        List<StCSendRuleAddressVipDo> stCSendRuleAddressVipDoList = getVipDo(stCSendRuleId, treeNode);
        List<SendRuleAddressVipResult> sendRuleAddressVipResultList = copyToVipRankResultList(stCSendRuleId, stCSendRuleAddressVipDoList);
        vh.setCode(ResultCode.SUCCESS);
        vh.setData(sendRuleAddressVipResultList);
        return vh;
    }

    /**
     * 根据前端参数获取唯品会明细
     *
     * @param stCSendRuleId
     * @param treeNode
     * @return
     */
    private List<StCSendRuleAddressVipDo> getVipDo(Long stCSendRuleId, String treeNode) {
        List<StCSendRuleAddressVipDo> list = new ArrayList<>();
        if (StringUtils.isEmpty(treeNode)) {
            return list;
        }
        List<RegionTreeResult> treeNodeList = JsonUtils.jsonToList(RegionTreeResult.class, treeNode);
        //唯品会全部仓库信息
        List<CpCVipcomWahouse> cpCVipcomWahouseList = rpcCpService.getCpCVipcomWahouse();
        //获取订单派单规则-唯品会明细Map
        Map<Long, StCSendRuleAddressVipDo> vipDoMap = getStCSendRuleAddressVipDoMap(stCSendRuleId);
        if (CollectionUtils.isNotEmpty(treeNodeList)) {
            treeNodeList = treeNodeList.stream().sorted(Comparator.comparing(RegionTreeResult::getId)).collect(Collectors.toList());
            for (RegionTreeResult node : treeNodeList) {
                if (null == node.getId()) {
                    continue;
                }
                if (null != vipDoMap && vipDoMap.containsKey(node.getId())) {
                    StCSendRuleAddressVipDo vipDo = vipDoMap.get(node.getId());
                    list.add(vipDo);
                } else {
                    RegionTreeResult treeResult = getTreeResultVip(cpCVipcomWahouseList, node.getId());
                    if (treeResult != null) {
                        //设置值
                        StCSendRuleAddressVipDo vipDo = new StCSendRuleAddressVipDo();
                        vipDo.setId(-1L);
                        vipDo.setCpCVipcomWahouseId(treeResult.getId());
                        vipDo.setCpCVipcomWahouseWarehouseCode(treeResult.getEcode());
                        vipDo.setCpCVipcomWahouseWarehouseName(treeResult.getTitle());
                        list.add(vipDo);
                    }
                }
            }
        }
        return list;
    }

    /**
     * 获取唯品会仓库信息tree
     *
     * @param cpCVipcomWahouseList 唯品会仓库信息
     * @param treeId               树ID
     * @return
     */
    private RegionTreeResult getTreeResultVip(List<CpCVipcomWahouse> cpCVipcomWahouseList, Long treeId) {
        for (CpCVipcomWahouse cpCVipcomWahouse : cpCVipcomWahouseList) {
            if (treeId.equals(cpCVipcomWahouse.getId())) {
                RegionTreeResult treeResult = new RegionTreeResult();
                treeResult.setId(cpCVipcomWahouse.getId());
                treeResult.setEcode(cpCVipcomWahouse.getWarehouseCode());
                treeResult.setTitle(cpCVipcomWahouse.getWarehouseName());
                return treeResult;
            }
        }
        return null;
    }

    /**
     * @param stCSendRuleId               规则ID
     * @param stCSendRuleAddressVipDoList 获取订单派单规则-唯品会明细
     * <AUTHOR>
     * @create 2020-06-16 17:35
     * @desc 转换成画面使用rankResult
     **/
    private List<SendRuleAddressVipResult> copyToVipRankResultList(Long stCSendRuleId, List<StCSendRuleAddressVipDo> stCSendRuleAddressVipDoList) {
        //获取已选择的仓库信息
        List<StCSendRuleAddressRentDO> rentList = getRentList(stCSendRuleId);
        List<SendRuleAddressVipResult> vipResultList = Lists.newArrayList();
        for (StCSendRuleAddressVipDo stCSendRuleAddressVipDo : stCSendRuleAddressVipDoList) {
            SendRuleAddressVipResult rankResult = new SendRuleAddressVipResult();
            BeanUtils.copyProperties(stCSendRuleAddressVipDo, rankResult);

            //获取仓库优先级集合
            Map<Long, WarehouseRankResult> warehouseRankMap = new HashMap<>();
            if (!StringUtils.isBlank(stCSendRuleAddressVipDo.getWarehouseRank())) {
                List<WarehouseRankResult> warehouseRankList = JsonUtils.jsonToList(WarehouseRankResult.class, stCSendRuleAddressVipDo.getWarehouseRank());
                if (CollectionUtils.isNotEmpty(warehouseRankList)) {
                    warehouseRankMap = warehouseRankList.stream().collect(
                            Collectors.toMap(WarehouseRankResult::getWarehouseId, Function.identity()));
                }
            }
            int i = 1;
            List<WarehouseRankResult> warehouseRankNewList = Lists.newArrayList();
            for (StCSendRuleAddressRentDO rent : rentList) {
                WarehouseRankResult warehouseRankNew = new WarehouseRankResult();
                warehouseRankNew.setWarehouseId(rent.getCpCPhyWarehouseId());
                warehouseRankNew.setWarehouseEcode(rent.getCpCPhyWarehouseEcode());
                warehouseRankNew.setWarehouseEname(rent.getCpCPhyWarehouseEname());
                try {
                    Method method = SendRuleAddressVipResult.class.getMethod("setRank" + i, new Class[]{String.class});
                    Method idMethod = SendRuleAddressVipResult.class.getMethod("setCpCPhyWarehouseId" + i, new Class[]{Long.class});
                    Method ecodeMethod = SendRuleAddressVipResult.class.getMethod("setCpCPhyWarehouseEcode" + i, new Class[]{String.class});
                    Method enameMethod = SendRuleAddressVipResult.class.getMethod("setCpCPhyWarehouseEname" + i, new Class[]{String.class});

                    idMethod.invoke(rankResult, rent.getCpCPhyWarehouseId());
                    ecodeMethod.invoke(rankResult, rent.getCpCPhyWarehouseEcode());
                    enameMethod.invoke(rankResult, rent.getCpCPhyWarehouseEname());
                    if (warehouseRankMap.containsKey(rent.getCpCPhyWarehouseId())) {
                        WarehouseRankResult warehouseRank = warehouseRankMap.get(rent.getCpCPhyWarehouseId());
                        method.invoke(rankResult, warehouseRank.getRank());
                        warehouseRankNew.setRank(warehouseRank.getRank());
                    } else {
                        method.invoke(rankResult, "");
                        warehouseRankNew.setRank("");
                    }
                } catch (Exception ex) {
                    log.error(LogUtil.format("优先级设置失败：{},派单规则ID/唯品会仓库/仓库=", stCSendRuleId,
                            stCSendRuleAddressVipDo.getCpCVipcomWahouseWarehouseName(),
                            rent.getCpCPhyWarehouseEname()), Throwables.getStackTraceAsString(ex));
                }
                warehouseRankNew.setWarehouseIdField("CP_C_PHY_WAREHOUSE_ID" + i);
                warehouseRankNew.setWarehouseEcodeField("CP_C_PHY_WAREHOUSE_ECODE" + i);
                warehouseRankNew.setWarehouseEnameField("CP_C_PHY_WAREHOUSE_ENAME" + i);
                warehouseRankNew.setRankField("RANK" + i);
                warehouseRankNewList.add(warehouseRankNew);
                i++;
            }
            rankResult.setWarehouseRank(JSONArray.toJSONString(warehouseRankNewList));
            rankResult.setStCSendRuleId(stCSendRuleId);
            vipResultList.add(rankResult);
        }
        return vipResultList;
    }


    /**
     * 查询仓库优先级明细表
     *
     * @param obj
     * @return ValueHolderV14
     */
    public ValueHolderV14 queryRankResultTable(JSONObject obj) {
        ValueHolderV14<List<SendRuleAddressRankResult>> vh = new ValueHolderV14<>();
        vh.setCode(ResultCode.SUCCESS);
        if (null == obj || !obj.containsKey("objid")) {
            vh.setCode(ResultCode.FAIL);
            vh.setMessage("参数为空");
            return vh;
        }
        if (!obj.containsKey("treeNode")) {
            return vh;
        }
        //查询地址
        obj.put("regiontype", "PROV");
        ValueHolderV14<List<RegionTreeResult>> treeVh = regionTreeQueryService.queryRegionTree(obj);
        if (treeVh.isOK()) {
            //查询明细
            Long id = obj.getLong("objid");
            String treeNode = JSONArray.toJSONString(obj.getJSONArray("treeNode"));
            List<RegionTreeResult> treeList = treeVh.getData();
            List<RegionTreeResult> treeNodeList = JsonUtils.jsonToList(RegionTreeResult.class, treeNode);

            //构造仓库优先级明细
            List<StCSendRuleAddressRankDO> sendRuleAddressRankList = buildRankDOList(id, treeList, treeNodeList);
            if (CollectionUtils.isNotEmpty(sendRuleAddressRankList)) {
                //转换成画面使用rankResult
                List<SendRuleAddressRankResult> rankResultList = copyToRankResultList(id, sendRuleAddressRankList);
                vh.setData(rankResultList);
            }
        } else {
            vh.setCode(ResultCode.FAIL);
            vh.setMessage("仓库优先级明细获取失败！");
            return vh;
        }
        return vh;
    }

    /**
     * 转换成画面使用rankResult
     *
     * @param id
     * @param id
     * @param sendRuleAddressRankList
     * @return List<SendRuleAddressRankResult>
     */
    private List<SendRuleAddressRankResult> copyToRankResultList(Long id, List<StCSendRuleAddressRankDO> sendRuleAddressRankList) {
        List<SendRuleAddressRankResult> rankResultList = Lists.newArrayList();
        //获取物流公司信息
        List<StCSendRuleAddressRentDO> rentList = rentMapper.selectList(new QueryWrapper<StCSendRuleAddressRentDO>()
                .lambda().eq(StCSendRuleAddressRentDO::getStCSendRuleId, id));
        rentList = rentList.stream().sorted(Comparator.comparing(StCSendRuleAddressRentDO::getId))
                .collect(Collectors.toList());
        for (StCSendRuleAddressRankDO rank : sendRuleAddressRankList) {
            SendRuleAddressRankResult rankResult = new SendRuleAddressRankResult();
            BeanUtils.copyProperties(rank, rankResult);

            //获取仓库优先级集合
            Map<Long, WarehouseRankResult> warehouseRankMap = new HashMap<>();
            if (!StringUtils.isBlank(rank.getWarehouseRank())) {
                List<WarehouseRankResult> warehouseRankList = JsonUtils.jsonToList(WarehouseRankResult.class, rank.getWarehouseRank());
                if (CollectionUtils.isNotEmpty(warehouseRankList)) {
                    warehouseRankMap = warehouseRankList.stream().collect(
                            Collectors.toMap(WarehouseRankResult::getWarehouseId, Function.identity()));
                }
            }
            int i = 1;
            List<WarehouseRankResult> warehouseRankNewList = Lists.newArrayList();
            for (StCSendRuleAddressRentDO rent : rentList) {
                WarehouseRankResult warehouseRankNew = new WarehouseRankResult();
                warehouseRankNew.setWarehouseId(rent.getCpCPhyWarehouseId());
                warehouseRankNew.setWarehouseEcode(rent.getCpCPhyWarehouseEcode());
                warehouseRankNew.setWarehouseEname(rent.getCpCPhyWarehouseEname());
                try {
                    Method method = SendRuleAddressRankResult.class.getMethod("setRank" + i, new Class[]{String.class});
                    Method idMethod = SendRuleAddressRankResult.class.getMethod("setCpCPhyWarehouseId" + i, new Class[]{Long.class});
                    Method ecodeMethod = SendRuleAddressRankResult.class.getMethod("setCpCPhyWarehouseEcode" + i, new Class[]{String.class});
                    Method enameMethod = SendRuleAddressRankResult.class.getMethod("setCpCPhyWarehouseEname" + i, new Class[]{String.class});

                    idMethod.invoke(rankResult, rent.getCpCPhyWarehouseId());
                    ecodeMethod.invoke(rankResult, rent.getCpCPhyWarehouseEcode());
                    enameMethod.invoke(rankResult, rent.getCpCPhyWarehouseEname());
                    if (warehouseRankMap.containsKey(rent.getCpCPhyWarehouseId())) {
                        WarehouseRankResult warehouseRank = warehouseRankMap.get(rent.getCpCPhyWarehouseId());
                        method.invoke(rankResult, warehouseRank.getRank());
                        warehouseRankNew.setRank(warehouseRank.getRank());
                    } else {
                        method.invoke(rankResult, "");
                        warehouseRankNew.setRank("");
                    }
                } catch (Exception ex) {
                    log.debug(LogUtil.format("仓库优先级设置失败：{}"), Throwables.getStackTraceAsString(ex));
                }
                warehouseRankNew.setWarehouseIdField("CP_C_PHY_WAREHOUSE_ID" + i);
                warehouseRankNew.setWarehouseEcodeField("CP_C_PHY_WAREHOUSE_ECODE" + i);
                warehouseRankNew.setWarehouseEnameField("CP_C_PHY_WAREHOUSE_ENAME" + i);
                warehouseRankNew.setRankField("RANK" + i);
                warehouseRankNewList.add(warehouseRankNew);
                i++;
            }
            rankResult.setWarehouseRank(JSONArray.toJSONString(warehouseRankNewList));
            rankResultList.add(rankResult);
        }
        return rankResultList;
    }

    /**
     * 构造仓库优先级明细
     *
     * @param id
     * @param treeList
     * @param treeNodeList
     * @return List<StCSendRuleAddressRankDO>
     */
    private List<StCSendRuleAddressRankDO> buildRankDOList(Long id, List<RegionTreeResult> treeList,
                                                           List<RegionTreeResult> treeNodeList) {
        List<StCSendRuleAddressRankDO> sendRuleAddressItemList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(treeNodeList)) {
            treeNodeList = treeNodeList.stream().sorted(Comparator.comparing(RegionTreeResult::getId))
                    .collect(Collectors.toList());
            Map<Long, StCSendRuleAddressRankDO> rankMap = getRankMap(id, treeNodeList);

            for (RegionTreeResult node : treeNodeList) {
                if (rankMap.containsKey(node.getId())) {
                    StCSendRuleAddressRankDO rank = rankMap.get(node.getId());
                    sendRuleAddressItemList.add(rank);
                } else {
                    RegionTreeResult treeResult = RegionTreeUtils.getTreeResult(treeList, node.getId());
                    if (treeResult != null) {
                        //设置值
                        StCSendRuleAddressRankDO rank = new StCSendRuleAddressRankDO();
                        rank.setId(-1L);
                        //省
                        rank.setCpCRegionProvinceId(treeResult.getId());
                        rank.setCpCRegionProvinceEcode(treeResult.getEcode());
                        rank.setCpCRegionProvinceEname(treeResult.getTitle());

                        sendRuleAddressItemList.add(rank);
                    }
                }
            }
        }
        return sendRuleAddressItemList;
    }

    /**
     * 获取明细MAP
     *
     * @param id
     * @param treeNodeList
     * @return Map<Long, StCSendRuleAddressRankDO>
     */
    private Map<Long, StCSendRuleAddressRankDO> getRankMap(Long id, List<RegionTreeResult> treeNodeList) {
        Map<Long, StCSendRuleAddressRankDO> rankMap = new HashMap<>();
        if (id > 0) {
            Map<String, List<Long>> treeNodeMap = new HashMap<>();
            for (RegionTreeResult regionTree : treeNodeList) {
                List<Long> idList = new ArrayList<>();
                if (treeNodeMap.containsKey(regionTree.getRegiontype())) {
                    idList = treeNodeMap.get(regionTree.getRegiontype());
                }
                idList.add(regionTree.getId());
                treeNodeMap.put(regionTree.getRegiontype(), idList);
            }
            List<StCSendRuleAddressRankDO> rankAllList = rankMapper.selectList(new QueryWrapper<StCSendRuleAddressRankDO>()
                    .lambda().eq(StCSendRuleAddressRankDO::getStCSendRuleId, id));
            for (String key : treeNodeMap.keySet()) {
                List<StCSendRuleAddressRankDO> rankList = rankAllList.stream().filter(rank ->
                        treeNodeMap.get(key).contains(rank.getCpCRegionProvinceId())).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(rankList)) {
                    for (StCSendRuleAddressRankDO rank : rankList) {
                        rankMap.put(rank.getCpCRegionProvinceId(), rank);
                    }
                }
            }
        }
        return rankMap;
    }

    /**
     * 查询仓库信息
     *
     * @param obj
     * @return ValueHolderV14
     */
    public ValueHolderV14 queryWarehouseInfo(JSONObject obj) {
        ValueHolderV14<SendRuleWarehouseInfoResult> vh = new ValueHolderV14<>();
        vh.setCode(ResultCode.SUCCESS);
        if (null == obj || !obj.containsKey("objid")) {
            vh.setCode(ResultCode.FAIL);
            vh.setMessage("参数为空");
            return vh;
        }
        SendRuleWarehouseInfoResult result = new SendRuleWarehouseInfoResult();
        Long id = obj.getLong("objid");
        if (id > 0) {
            //获取物流公司信息
            List<StCSendRuleAddressRentDO> rentList = rentMapper.selectList(new QueryWrapper<StCSendRuleAddressRentDO>()
                    .lambda().eq(StCSendRuleAddressRentDO::getStCSendRuleId, id));
            if (CollectionUtils.isNotEmpty(rentList)) {
                rentList = rentList.stream().sorted(Comparator.comparing(StCSendRuleAddressRentDO::getId))
                        .collect(Collectors.toList());
                result.setSendRuleAddressRents(rentList);
            } else {
                result.setSendRuleAddressRents(Lists.newArrayList());
            }
        }
        //查询地址
        List<CpCPhyWarehouse> cpCPhyWarehouseList = rpcCpService.queryWarehouseByLike(obj.getString("warehouseInfo"));
        if (CollectionUtils.isNotEmpty(cpCPhyWarehouseList)) {
            result.setCpCPhyWarehouseList(cpCPhyWarehouseList);
        }
        vh.setData(result);
        return vh;
    }

    /**
     * 查询分仓比例明细表
     *
     * @param obj
     * @return ValueHolderV14
     */
    public ValueHolderV14 queryRateResultTable(JSONObject obj) {
        ValueHolderV14<List<StCSendRuleWarehouseRateDO>> vh = new ValueHolderV14<>();
        vh.setCode(ResultCode.SUCCESS);
        if (null == obj || !obj.containsKey("objid")) {
            vh.setCode(ResultCode.FAIL);
            vh.setMessage("参数为空");
            return vh;
        }
        //查询明细
        Long id = obj.getLong("objid");
        List<StCSendRuleWarehouseRateDO> resultList = Lists.newArrayList();
        if (id > 0) {
            //获取仓库信息
            List<StCSendRuleWarehouseRateDO> rateList = rateMapper.selectList(new QueryWrapper<StCSendRuleWarehouseRateDO>()
                    .lambda().eq(StCSendRuleWarehouseRateDO::getStCSendRuleId, id));
            if (CollectionUtils.isNotEmpty(rateList)) {
                rateList = rateList.stream().sorted(Comparator.comparing(StCSendRuleWarehouseRateDO::getId))
                        .collect(Collectors.toList());
                resultList.addAll(rateList);
            }
        }
        vh.setData(resultList);
        return vh;
    }

    /**
     * 根据省查询仓库优先级
     *
     * @param cpCRegionProvinceId
     * @return ValueHolderV14
     */
    public ValueHolderV14<StCSendRuleAddressRankDO> queryWarehouseRankInfo(Long cpCRegionProvinceId) {
        ValueHolderV14<StCSendRuleAddressRankDO> vh = new ValueHolderV14<>();
        vh.setCode(ResultCode.SUCCESS);
        //查询地址
        List<StCSendRuleAddressRankDO> rankList = rankMapper.queryWarehouseRankInfo(cpCRegionProvinceId);
        if (CollectionUtils.isNotEmpty(rankList)) {
            vh.setData(rankList.get(0));
        } else {
            vh.setData(null);
        }
        return vh;
    }

    /**
     * 查询主表信息
     *
     * @param obj
     * @return ValueHolderV14
     */
    public ValueHolderV14 querySendRule(JSONObject obj) {
        ValueHolderV14<StCSendRuleDO> vh = new ValueHolderV14<>();
        vh.setCode(ResultCode.SUCCESS);
        if (null == obj || !obj.containsKey("objid")) {
            vh.setCode(ResultCode.FAIL);
            vh.setMessage("参数为空");
            return vh;
        }
        //查询
        Long id = obj.getLong("objid");
        StCSendRuleDO result = new StCSendRuleDO();
        if (id > 0) {
            //获取派单规则信息
            result = mapper.selectById(id);
        }
        vh.setData(result);
        return vh;
    }

    /**
     * 模糊地区查询仓库优先级明细表
     *
     * @param obj
     * @return ValueHolderV14
     */
    public ValueHolderV14 queryLikeRankResultTable(JSONObject obj) {
        ValueHolderV14<SendRuleTreeLikeResult> vh = new ValueHolderV14<>();
        vh.setCode(ResultCode.SUCCESS);
        if (null == obj || !obj.containsKey("objid")) {
            vh.setCode(ResultCode.FAIL);
            vh.setMessage("参数为空");
            return vh;
        }
        if (!obj.containsKey("treeLikeKey")) {
            return vh;
        }
        //查询地址
        obj.put("regiontype", "PROV");
        ValueHolderV14<List<RegionTreeResult>> treeVh = regionTreeQueryService.queryRegionTree(obj);
        SendRuleTreeLikeResult likeResult = new SendRuleTreeLikeResult();
        likeResult.setSendRuleAddressRanks(Lists.newArrayList());
        if (treeVh.isOK()) {
            //查询明细
            Long id = obj.getLong("objid");
            List<RegionTreeResult> treeList = treeVh.getData();

            List<RegionTreeResult> treeNodeList = Lists.newArrayList();
            RegionTreeUtils.changeTreeLikeChecked(treeList, obj.getString("treeLikeKey"), false, treeNodeList);
            likeResult.setSendRuleTree(treeList);
            //构造仓库优先级明细
            List<StCSendRuleAddressRankDO> sendRuleAddressRankList = buildRankDOList(id, treeList, treeNodeList);
            if (CollectionUtils.isNotEmpty(sendRuleAddressRankList)) {
                //转换成画面使用rankResult
                List<SendRuleAddressRankResult> rankResultList = copyToRankResultList(id, sendRuleAddressRankList);
                likeResult.setSendRuleAddressRanks(rankResultList);
            }
        } else {
            vh.setCode(ResultCode.FAIL);
            vh.setMessage("仓库优先级明细获取失败！");
            return vh;
        }
        vh.setData(likeResult);
        return vh;
    }
}
