package com.jackrain.nea.st.services;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.st.common.StConstant;
import com.jackrain.nea.st.mapper.StCLockStockStrategyItemMapper;
import com.jackrain.nea.st.model.request.StCLockStockStrategyItemRequest;
import com.jackrain.nea.st.model.table.StCLockStockStrategyItemDO;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.utility.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;

/**
 * @Author: ganquan
 * @Date Create In 2020/12/18 17:36
 * @Description: 店铺锁库策略查询
 */
@Slf4j
@Component
public class LockStockQueryService {

    @Autowired
    private StCLockStockStrategyItemMapper stCLockStockStrategyItemMapper;

    /**
     * <AUTHOR>
     * @Description 查询有效店铺锁库策略明细
     * @Date 16:07 2020/12/18
     * @param request
     * @return com.jackrain.nea.sys.domain.ValueHolderV14<com.jackrain.nea.st.model.table.StCLockStockStrategyItemDO>
     **/
    public ValueHolderV14<List<StCLockStockStrategyItemDO>> queryLockStockItemValid(StCLockStockStrategyItemRequest request) {
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("【查询有效店铺锁库策略明细入参】{}"), JSONObject.toJSON(request));
        }
        ValueHolderV14<List<StCLockStockStrategyItemDO>> valueHolder14 = new ValueHolderV14();
        valueHolder14.setCode(ResultCode.SUCCESS);
        valueHolder14.setMessage("成功");
        String errorMes = checkLockSkuStrategyItemRequest(request);
        if (StringUtils.isNotEmpty(errorMes)) {
            valueHolder14.setCode(ResultCode.FAIL);
            valueHolder14.setMessage(errorMes);
            return valueHolder14;
        }
        LambdaQueryWrapper<StCLockStockStrategyItemDO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(StCLockStockStrategyItemDO::getCpCShopId, request.getCpCShopId());
        wrapper.eq(StCLockStockStrategyItemDO::getStatus, StConstant.CON_BILL_STATUS_02);
        wrapper.le(StCLockStockStrategyItemDO::getLockBtime, request.getExpireTime());
        wrapper.ge(StCLockStockStrategyItemDO::getLockEtime, request.getExpireTime());
        List<StCLockStockStrategyItemDO> itemList = stCLockStockStrategyItemMapper.selectList(wrapper);
        if (CollectionUtils.isNotEmpty(itemList)) {
            valueHolder14.setData(itemList);
        }
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("【查询有效店铺锁库策略明细出参】{}"), JSONObject.toJSON(itemList));
        }
        return valueHolder14;
    }

    /**
     * <AUTHOR>
     * @Description 入参校验
     * @Date 17:21 2020/12/18
     * @param request
     * @return java.lang.String
     **/
    private String checkLockSkuStrategyItemRequest(StCLockStockStrategyItemRequest request) {
        if (request.getCpCShopId() == null) {
            return "店铺不允许为空";
        }
        if (request.getExpireTime() == null) {
            return "当前时间不允许为空";
        }
        return null;
    }

}
