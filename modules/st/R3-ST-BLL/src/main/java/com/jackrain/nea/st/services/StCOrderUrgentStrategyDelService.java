package com.jackrain.nea.st.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.st.common.StConstant;
import com.jackrain.nea.st.mapper.StCOrderUrgentStrategyMapper;
import com.jackrain.nea.st.model.table.StCOrderUrgentStrategyDO;
import com.jackrain.nea.st.utils.StBeanUtils;
import com.jackrain.nea.st.utils.ValueHolderUtils;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 * @Package com.jackrain.nea.st.services
 * @Description:
 * @date 2020/8/29 23:05
 */
@Component
@Slf4j
@Transactional
public class StCOrderUrgentStrategyDelService extends CommandAdapter {

    @Autowired(required = false)
    private StCOrderUrgentStrategyMapper mapper;

    @Override
    public ValueHolder execute(QuerySession querySession) throws NDSException {
        DefaultWebEvent event = querySession.getEvent();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(
                event.getParameterValue("param"), "yyyy-MM-dd HH:mm:ss"), Feature.OrderedField);
        String isDel = param.getString("isdelmtable");
        Long objid = param.getLong("objid");
        //判断主表是否存在
        StCOrderUrgentStrategyDO orderUrgentStrategyDO = mapper.selectById(objid);
        if (orderUrgentStrategyDO == null) {
            return ValueHolderUtils.getFailValueHolder("当前记录已不存在!");
        }
        JSONObject tabitem = param.getJSONObject("tabitem");
        JSONArray errorArray = new JSONArray();
        //判断是删除主表还是明细表单独删除
        if (StConstant.TRUE_STR.equals(isDel)) {
            //删除主表
            int deleteCount = mapper.deleteById(objid);
            if (deleteCount <= 0) {
                errorArray.add(StBeanUtils.getJsonObjectInfo(objid, "订单加急打标策略已不存在"));
            }
        }
        return StBeanUtils.getExcuteValueHolder(errorArray);
    }


}
