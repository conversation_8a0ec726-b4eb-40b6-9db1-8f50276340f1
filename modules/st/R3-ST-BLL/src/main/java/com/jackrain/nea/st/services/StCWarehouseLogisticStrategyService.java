package com.jackrain.nea.st.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.cp.api.CsupplierQueryCmd;
import com.jackrain.nea.cp.result.CpCSupplier;
import com.jackrain.nea.cpext.api.CpLogisticsQueryCmd;
import com.jackrain.nea.cpext.api.CpcPhyWareHouseQueryCmd;
import com.jackrain.nea.cpext.model.table.CpCPhyWarehouse;
import com.jackrain.nea.cpext.model.table.CpLogistics;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.oc.oms.model.enums.YesNoEnum;
import com.jackrain.nea.redis.config.CusRedisTemplate;
import com.jackrain.nea.redis.util.RedisOpsUtil;
import com.jackrain.nea.st.common.StConstant;
import com.jackrain.nea.st.mapper.StCWarehouseLogisticStrategyItemMapper;
import com.jackrain.nea.st.mapper.StCWarehouseLogisticStrategyMapper;
import com.jackrain.nea.st.model.enums.CommStatusEnum;
import com.jackrain.nea.st.model.request.StCWarehouseLogisticStrategyItemQueryRequest;
import com.jackrain.nea.st.model.request.StCWarehouseLogisticStrategyQueryRequest;
import com.jackrain.nea.st.model.result.StCWarehouseLogisticStrategyQueryResult;
import com.jackrain.nea.st.model.result.StCWarehouseLogisticStrategyResult;
import com.jackrain.nea.st.model.table.StCWarehouseLogisticStrategy;
import com.jackrain.nea.st.model.table.StCWarehouseLogisticStrategyItem;
import com.jackrain.nea.st.model.vo.StCWarehouseLogisticStrategyImpVo;
import com.jackrain.nea.st.utils.StBeanUtils;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @program: r3-st
 * @description: 仓库物流设置服务类
 * @author: caomalai
 * @create: 2022-06-23 15:40
 **/
@Component
@Slf4j
public class StCWarehouseLogisticStrategyService {
    @Reference(version = "1.0", group = "cp-ext")
    private CpcPhyWareHouseQueryCmd cpcPhyWareHouseQueryCmd;
    @Reference(version = "1.0", group = "cp-ext")
    private CpLogisticsQueryCmd cpLogisticsQueryCmd;
    @Reference(version = "1.0", group = "cp")
    private CsupplierQueryCmd csupplierQueryCmd;
    @Autowired
    private StCWarehouseLogisticStrategyMapper stCWarehouseLogisticStrategyMapper;
    @Autowired
    private StCWarehouseLogisticStrategyItemMapper stCWarehouseLogisticStrategyItemMapper;


    @Transactional(rollbackFor = Exception.class)
    public Long save(JSONObject param, User user) {

        JSONObject fixColumn = param.getJSONObject("fixcolumn");
        Long objid = param.getLong("objid");

        // 删除redis 缓存
        if (objid != null && objid > 0L) {
            StCWarehouseLogisticStrategy strategy = stCWarehouseLogisticStrategyMapper.selectById(objid);
            CusRedisTemplate<String, String> redisTemplate = RedisOpsUtil.getStrRedisTemplate();
            redisTemplate.delete("st:warehouse:express:warehouseId:" + strategy.getCpCPhyWarehouseId());
        }

        String tableName = param.getString("table");
        JSONObject jsonObject = fixColumn.getJSONObject(tableName);
        if (Objects.nonNull(jsonObject)) {
            StCWarehouseLogisticStrategy stCWarehouseLogisticStrategy
                    = JSONObject.parseObject(jsonObject.toJSONString(), StCWarehouseLogisticStrategy.class);
            //校验门店唯一性
            QueryWrapper<StCWarehouseLogisticStrategy> queryWrapper = new QueryWrapper<>();
            queryWrapper.lambda().eq(StCWarehouseLogisticStrategy::getCpCPhyWarehouseId, stCWarehouseLogisticStrategy.getCpCPhyWarehouseId())
                    .ne(objid > 0, StCWarehouseLogisticStrategy::getId, stCWarehouseLogisticStrategy.getId());
            List<StCWarehouseLogisticStrategy> itemList = stCWarehouseLogisticStrategyMapper.selectList(queryWrapper);
            if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(itemList)) {
                throw new NDSException("仓库已经存在，不允许重复录入！");
            }
            //新增
            if (Objects.isNull(objid) || objid < 0) {
                Long id = ModelUtil.getSequence(tableName.toLowerCase());
                stCWarehouseLogisticStrategy.setId(id);
                StBeanUtils.makeCreateField(stCWarehouseLogisticStrategy, user);
                CpCPhyWarehouse cpCPhyWarehouse = cpcPhyWareHouseQueryCmd.queryCpCPhyWarehouseById(stCWarehouseLogisticStrategy.getCpCPhyWarehouseId());
                if (Objects.isNull(cpCPhyWarehouse)) {
                    throw new NDSException("所选仓库不存在！");
                }
                stCWarehouseLogisticStrategy.setCpCPhyWarehouseEcode(cpCPhyWarehouse.getEcode());
                stCWarehouseLogisticStrategy.setCpCPhyWarehouseEname(cpCPhyWarehouse.getEname());
                stCWarehouseLogisticStrategyMapper.insert(stCWarehouseLogisticStrategy);
                objid = id;
            } else {
                //更新
                JSONObject afterValue = param.getJSONObject("aftervalue");
                JSONObject jsonObjectAfter = afterValue.getJSONObject(tableName);
                stCWarehouseLogisticStrategy
                        = JSONObject.parseObject(jsonObjectAfter.toJSONString(), StCWarehouseLogisticStrategy.class);
                stCWarehouseLogisticStrategy.setId(objid);
                StBeanUtils.makeModifierField(stCWarehouseLogisticStrategy, user);
                if (CommStatusEnum.YES.desc().equals(stCWarehouseLogisticStrategy.getIsactive())) {
                    stCWarehouseLogisticStrategy.setIsactive(CommStatusEnum.YES.charVal());
                }
                if (CommStatusEnum.NO.desc().equals(stCWarehouseLogisticStrategy.getIsactive())) {
                    stCWarehouseLogisticStrategy.setIsactive(CommStatusEnum.NO.charVal());
                }
                if (CommStatusEnum.YES.desc().equals(stCWarehouseLogisticStrategy.getIsExpire())) {
                    stCWarehouseLogisticStrategy.setIsExpire(CommStatusEnum.YES.charVal());
                }
                if (CommStatusEnum.NO.desc().equals(stCWarehouseLogisticStrategy.getIsExpire())) {
                    stCWarehouseLogisticStrategy.setIsExpire(CommStatusEnum.NO.charVal());
                }
                stCWarehouseLogisticStrategyMapper.updateById(stCWarehouseLogisticStrategy);
            }
        }

        //子表更新
        String subTableName = "ST_C_WAREHOUSE_LOGISTIC_STRATEGY_ITEM";
        JSONArray jsonObjectItems = fixColumn.getJSONArray(subTableName);
        if (CollectionUtils.isNotEmpty(jsonObjectItems)) {
            for (Object o : jsonObjectItems) {
                JSONObject object = (JSONObject) o;
                if (object.containsKey("EXPIRE_START_TIME") && object.getString("EXPIRE_START_TIME") == null) {
                    object.put("EXPIRE_START_TIME", "");
                }
                if (object.containsKey("EXPIRE_END_TIME") && object.getString("EXPIRE_END_TIME") == null) {
                    object.put("EXPIRE_END_TIME", "");
                }
            }
            List<StCWarehouseLogisticStrategyItem> insertList = new ArrayList<>();
            List<StCWarehouseLogisticStrategyItem> updateList = new ArrayList<>();
            List<StCWarehouseLogisticStrategyItem> stCWarehouseLogisticStrategyItems = JSONArray.parseArray(jsonObjectItems.toJSONString(), StCWarehouseLogisticStrategyItem.class);
            Long finalObjid = objid;
            List<Long> logisticsIds = stCWarehouseLogisticStrategyItems.stream().map(StCWarehouseLogisticStrategyItem::getCpCLogisticsId).collect(Collectors.toList());

            ValueHolderV14<Map<Long, CpLogistics>> mapValueHolderV14 = cpLogisticsQueryCmd.queryLogisticsByIds(logisticsIds);
            Map<Long, CpLogistics> logisticsMap = mapValueHolderV14.getData();
            List<StCWarehouseLogisticStrategyItem> existItems =
                    stCWarehouseLogisticStrategyItemMapper.selectList(new QueryWrapper<StCWarehouseLogisticStrategyItem>()
                            .lambda().eq(StCWarehouseLogisticStrategyItem::getStCWarehouseLogisticStrategyId, finalObjid));
            Map<Long, StCWarehouseLogisticStrategyItem> existItemMap = new HashMap<>();
            List<Integer> itemPriorityList = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(existItems)) {
                for (StCWarehouseLogisticStrategyItem existItem : existItems) {
                    existItemMap.put(existItem.getId(), existItem);
                    itemPriorityList.add(existItem.getItemPriority());
                }
            }
            stCWarehouseLogisticStrategyItems.forEach(p -> {
                if (p.getCpCLogisticsId() != null && p.getCpCLogisticsId() > 0L) {
                    // 校验物流公司唯一性
                    QueryWrapper<StCWarehouseLogisticStrategyItem> queryWrapper = new QueryWrapper<>();
                    queryWrapper.lambda()
                            .eq(StCWarehouseLogisticStrategyItem::getCpCLogisticsId, p.getCpCLogisticsId())
                            .eq(StCWarehouseLogisticStrategyItem::getStCWarehouseLogisticStrategyId, finalObjid)
                            .ne(p.getId() > 0, StCWarehouseLogisticStrategyItem::getId, p.getId());
                    List<StCWarehouseLogisticStrategyItem> itemList = stCWarehouseLogisticStrategyItemMapper.selectList(queryWrapper);
                    if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(itemList)) {
                        throw new NDSException("物流公司已经存在，不允许重复录入！");
                    }

                    CpLogistics cpLogistics = logisticsMap.get(p.getCpCLogisticsId());
                    if (Objects.isNull(cpLogistics)) {
                        throw new NDSException("所选物流公司不存在！");
                    } else {
                        p.setCpCLogisticsEcode(cpLogistics.getEcode());
                        p.setCpCLogisticsEname(cpLogistics.getEname());
                        p.setLogisticsType(cpLogistics.getType());
                    }
                }

                if (p.getLogisticsSupplierId() != null) {
                    ValueHolderV14<CpCSupplier> cpCSupplierValueHolderV14 = csupplierQueryCmd.queryCpSupplierById(p.getLogisticsSupplierId());
                    CpCSupplier supplier = cpCSupplierValueHolderV14.getData();
                    if (Objects.isNull(supplier)) {
                        throw new NDSException("所选物流结算公司不存在！");
                    } else {
                        p.setLogisticsSupplierEcode(supplier.getEcode());
                        p.setLogisticsSupplierEname(supplier.getEname());
                    }
                }

                //校验优先级
                if (!Objects.isNull(p.getItemPriority()) && p.getItemPriority() < 1) {
                    throw new NDSException("优先级必须大于等于1的正整数！");
                }
                if (CollectionUtils.isNotEmpty(itemPriorityList) && itemPriorityList.contains(p.getItemPriority())) {
                    throw new NDSException("优先级不能重复！");
                }

                //校验最大接单量
                String enableMaximumOrderVolume = p.getEnableMaximumOrderVolume();
                Integer maximumOrderVolume = p.getMaximumOrderVolume();

                //是否启用最大接单量
                boolean flag = Objects.nonNull(enableMaximumOrderVolume) && StConstant.ISACTIVE_Y.equals(enableMaximumOrderVolume);
                if (p.getId() == null || p.getId() < 0) {
                    if (flag && Objects.isNull(maximumOrderVolume)) {
                        throw new NDSException("最大接单量必须填写!");
                    }
                } else {
                    StCWarehouseLogisticStrategyItem orgItem = stCWarehouseLogisticStrategyItemMapper.selectById(p.getId());

                    if (orgItem != null) {
                        if (flag && Objects.isNull(maximumOrderVolume) && orgItem.getMaximumOrderVolume() == null) {
                            throw new NDSException("最大接单量必须填写!");
                        }
                    } else {
                        throw new NDSException("当前明细记录已经不存在!");
                    }

                }

                if (!Objects.isNull(maximumOrderVolume) && maximumOrderVolume < 0) {
                    throw new NDSException("最大接单量必须填写正整数!");
                }

                if (p.getId() == null || p.getId() < 0) {
                    p.setStCWarehouseLogisticStrategyId(finalObjid);
                    Long id = ModelUtil.getSequence(subTableName.toLowerCase());
                    p.setId(id);
                    StBeanUtils.makeCreateField(p, user);
                    insertList.add(p);
                } else {
                    if (MapUtils.isNotEmpty(existItemMap) && existItemMap.get(p.getId()) != null) {
                        if (p.getExpireStartTime() == null) {
                            p.setExpireStartTime(existItemMap.get(p.getId()).getExpireStartTime());
                        }
                        if (p.getExpireEndTime() == null) {
                            p.setExpireEndTime(existItemMap.get(p.getId()).getExpireEndTime());
                        }
                    }
                    if (!(StringUtils.isEmpty(p.getExpireStartTime()) == StringUtils.isEmpty(p.getExpireEndTime()))) {
                        throw new NDSException("[失效开始时间]和[失效截止时间]要么都为空，要么都不为空");
                    }
                    if (p.getExpireStartTime() != null && StringUtils.isEmpty(p.getExpireStartTime())) {
                        p.setExpireStartTime(null);
                    }
                    if (p.getExpireEndTime() != null && StringUtils.isEmpty(p.getExpireEndTime())) {
                        p.setExpireEndTime(null);
                    }
                    StBeanUtils.makeModifierField(p, user);
                    updateList.add(p);
                }

            });
            if (CollectionUtils.isNotEmpty(insertList)) {
                stCWarehouseLogisticStrategyItemMapper.batchInsert(insertList);
            }
            if (CollectionUtils.isNotEmpty(updateList)) {
                for (StCWarehouseLogisticStrategyItem item : updateList) {
                    stCWarehouseLogisticStrategyItemMapper.updateById(item);
                }
            }
        }
        deleteRedisKey(objid);
        return objid;
    }


    public void delete(JSONObject param, QuerySession querySession) {
        JSONObject tabItem = param.getJSONObject("tabitem");
        Long objid = param.getLong("objid");
        // 清除缓存
        deleteRedisKey(objid);
        //判断是不是只删除子表
        if (Objects.nonNull(tabItem) && CollectionUtils.isNotEmpty(tabItem.getJSONArray("ST_C_WAREHOUSE_LOGISTIC_STRATEGY_ITEM"))) {
            JSONArray itemIds = tabItem.getJSONArray("ST_C_WAREHOUSE_LOGISTIC_STRATEGY_ITEM");
            List<Long> itemIdList = JSONArray.parseArray(itemIds.toJSONString(), Long.class);
            List<StCWarehouseLogisticStrategyItem> itemList = stCWarehouseLogisticStrategyItemMapper.selectBatchIds(itemIdList);
            if (CollectionUtils.isNotEmpty(itemList)) {
                Map<Long, String> beforeDelObjMap = new HashMap<>();
                for (StCWarehouseLogisticStrategyItem item : itemList) {
                    beforeDelObjMap.put(item.getId(), JSON.toJSONString(item));
                }
                querySession.setAttribute("beforeDelObjMap", beforeDelObjMap);
            }
            stCWarehouseLogisticStrategyItemMapper.deleteBatchIds(itemIdList);
        } else {
            //删除主表
            stCWarehouseLogisticStrategyMapper.deleteById(objid);
            //删除关联的全部子表
            QueryWrapper<StCWarehouseLogisticStrategyItem> queryWrapper = new QueryWrapper<>();
            queryWrapper.lambda().eq(StCWarehouseLogisticStrategyItem::getStCWarehouseLogisticStrategyId, objid);
            stCWarehouseLogisticStrategyItemMapper.delete(queryWrapper);
        }
    }

    void deleteRedisKey(Long id){
        StCWarehouseLogisticStrategy stCWarehouseLogisticStrategy = stCWarehouseLogisticStrategyMapper.selectById(id);
        if(stCWarehouseLogisticStrategy==null){
            return;
        }
        //清理redis key："st:warehouse:express:warehouseId:" + warehouseId
        String redisKey="st:warehouse:express:warehouseId:" +stCWarehouseLogisticStrategy.getCpCPhyWarehouseId();
        CusRedisTemplate<String, String> redisTemplate = RedisOpsUtil.getStrRedisTemplate();
        redisTemplate.delete(redisKey);
    }

    /**
     * 导入批量保存数据
     *
     * @param stCWarehouseLogisticStrategyImpVos
     * @param user
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public int batchSaveInvoice(List<StCWarehouseLogisticStrategyImpVo> stCWarehouseLogisticStrategyImpVos, User user) {
        if (log.isDebugEnabled()) {
            log.info("## 仓库物流设置导入数据：" + JSONObject.toJSONString(stCWarehouseLogisticStrategyImpVos));
        }
        if(CollectionUtils.isEmpty(stCWarehouseLogisticStrategyImpVos)){
            return 0;
        }
        //查询仓库信息
        List<String> warehouseNameList = stCWarehouseLogisticStrategyImpVos.stream().distinct().map(StCWarehouseLogisticStrategyImpVo::getCpCPhyWarehouseEname).collect(Collectors.toList());
        List<CpCPhyWarehouse> cpCPhyWarehouses = cpcPhyWareHouseQueryCmd.queryWarehouseByEnames(warehouseNameList);
        Map<String, CpCPhyWarehouse> warehouseMap = cpCPhyWarehouses.stream().collect(Collectors.toMap(CpCPhyWarehouse::getEname, Function.identity(), (key1, key2) -> key2));
        //查询物流公司信息
        List<String> logisticNameList = stCWarehouseLogisticStrategyImpVos.stream().distinct().map(StCWarehouseLogisticStrategyImpVo::getCpCLogisticsEname).collect(Collectors.toList());
        List<CpLogistics> cpLogistics = cpLogisticsQueryCmd.queryLogisticsByNames(logisticNameList);
        Map<String, CpLogistics> logisticsMap = cpLogistics.stream().collect(Collectors.toMap(CpLogistics::getEname, Function.identity(), (key1, key2) -> key2));
        //查询结算公司信息
        List<String> supplierNameList = stCWarehouseLogisticStrategyImpVos.stream().distinct().map(StCWarehouseLogisticStrategyImpVo::getLogisticsSupplierEname).collect(Collectors.toList());
        List<CpCSupplier> cpCSuppliers = csupplierQueryCmd.queryCpSupplierByEnames(supplierNameList);
        Map<String, CpCSupplier> supplierMap = cpCSuppliers.stream().collect(Collectors.toMap(CpCSupplier::getEname, Function.identity(), (key1, key2) -> key2));

        List<StCWarehouseLogisticStrategy> insertList = new ArrayList<>();
        List<StCWarehouseLogisticStrategyItem> insertItemList = new ArrayList<>();

        StringBuilder checkMessage = new StringBuilder();
        //校验仓库是否存在，重复性校验
        Map<String, List<StCWarehouseLogisticStrategyImpVo>> dataWarehouseMap =
                stCWarehouseLogisticStrategyImpVos.stream().collect(Collectors.groupingBy(p -> p.getCpCPhyWarehouseEname() + "-" + p.getIsExpire()));
        //缓存连接
        CusRedisTemplate<String, String> redisTemplate = RedisOpsUtil.getStrRedisTemplate();
        for (String key : dataWarehouseMap.keySet()) {
            CpCPhyWarehouse cpCPhyWarehouse = warehouseMap.get(key.split("-")[0]);
            List<StCWarehouseLogisticStrategyImpVo> impVoList = dataWarehouseMap.get(key);

            if (Objects.isNull(cpCPhyWarehouse)) {
                checkMessage.append("[未查询到该仓库信息！]");
                copyMessageToItem(impVoList, checkMessage);
                checkMessage.setLength(0);
                continue;
            }
            //删除仓库物流设置缓存
            String redisKey = "st:warehouse:express:warehouseId:" + cpCPhyWarehouse.getId();
            redisTemplate.delete(redisKey);

            Long mainObjid = 0L;
            boolean isInsertMain = true;
            StCWarehouseLogisticStrategy stCWarehouseLogisticStrategy = stCWarehouseLogisticStrategyMapper.selectOne(
                    new QueryWrapper<StCWarehouseLogisticStrategy>()
                            .lambda().eq(StCWarehouseLogisticStrategy::getCpCPhyWarehouseId, cpCPhyWarehouse.getId())
                            .eq(StCWarehouseLogisticStrategy::getIsactive, YesNoEnum.Y.getKey()));
            if (Objects.nonNull(stCWarehouseLogisticStrategy)) {
                isInsertMain = false;
                mainObjid = stCWarehouseLogisticStrategy.getId();
            }


            List<Long> logisticsList = new ArrayList<>();
            List<StCWarehouseLogisticStrategyItem> itemList = new ArrayList<>();
            for (StCWarehouseLogisticStrategyImpVo impVo : impVoList) {

                CpLogistics logistics = logisticsMap.get(impVo.getCpCLogisticsEname());
                if (Objects.isNull(logistics)) {
                    checkMessage.append("[未查询到该物流公司信息！]");
                    copyMessageToItem(impVoList, checkMessage);
                    checkMessage.setLength(0);
                    continue;
                }
                CpCSupplier supplier = supplierMap.get(impVo.getLogisticsSupplierEname());
                if (Objects.isNull(supplier)) {
                    checkMessage.append("[未查询到该物流结算公司信息！]");
                    copyMessageToItem(impVoList, checkMessage);
                    checkMessage.setLength(0);
                    continue;
                }
                if(logisticsList.contains(logistics.getId())){
                    checkMessage.append("[文件中物流公司信息重复！]");
                    copyMessageToItem(impVoList, checkMessage);
                    checkMessage.setLength(0);
                    continue;
                }

                if(!isInsertMain){
                    List<StCWarehouseLogisticStrategyItem> stCWarehouseLogisticStrategyItems = stCWarehouseLogisticStrategyItemMapper.selectList(new QueryWrapper<StCWarehouseLogisticStrategyItem>().lambda()
                            .eq(StCWarehouseLogisticStrategyItem::getStCWarehouseLogisticStrategyId, mainObjid)
                            .eq(StCWarehouseLogisticStrategyItem::getCpCLogisticsId, logistics.getId())
                    );
                    if(CollectionUtils.isNotEmpty(stCWarehouseLogisticStrategyItems)){
                        checkMessage.append("[物流公司信息已存在！]");
                        copyMessageToItem(impVoList, checkMessage);
                        checkMessage.setLength(0);
                        continue;
                    }
                }

                StCWarehouseLogisticStrategyItem item = new StCWarehouseLogisticStrategyItem();
                Long objid = ModelUtil.getSequence("ST_C_WAREHOUSE_LOGISTIC_STRATEGY_ITEM");
                item.setId(objid);
                item.setLogisticsType(logistics.getType());
                item.setCpCLogisticsEname(logistics.getEname());
                item.setCpCLogisticsEcode(logistics.getEcode());
                item.setCpCLogisticsId(logistics.getId());
                item.setLogisticsSupplierEname(supplier.getEname());
                item.setLogisticsSupplierEcode(supplier.getEcode());
                item.setLogisticsSupplierId(supplier.getId());
                item.setEnableMaximumOrderVolume(impVo.getEnableMaximumOrderVolume());
                item.setMaximumOrderVolume(impVo.getMaximumOrderVolume());
                item.setItemPriority(impVo.getItemPriority());
                item.setExpireStartTime(impVo.getExpireStartTime());
                item.setExpireEndTime(impVo.getExpireEndTime());
                StBeanUtils.makeCreateField(item, user);
                itemList.add(item);
                //加入列表
                logisticsList.add(logistics.getId());
            }
            if(isInsertMain){
                if (CollectionUtils.isNotEmpty(itemList)) {
                    mainObjid = ModelUtil.getSequence("ST_C_WAREHOUSE_LOGISTIC_STRATEGY");
                    StCWarehouseLogisticStrategy po = new StCWarehouseLogisticStrategy();
                    po.setId(mainObjid);
                    po.setCpCPhyWarehouseEname(cpCPhyWarehouse.getEname());
                    po.setCpCPhyWarehouseId(cpCPhyWarehouse.getId());
                    po.setCpCPhyWarehouseEcode(cpCPhyWarehouse.getEcode());
                    po.setIsExpire(key.split("-")[1]);
                    StBeanUtils.makeCreateField(po, user);
                    insertList.add(po);
                    Long finalMainObjid = mainObjid;
                    itemList.forEach(p -> p.setStCWarehouseLogisticStrategyId(finalMainObjid));
                    insertItemList.addAll(itemList);
                }
            }else{
                Long finalMainObjid = mainObjid;
                itemList.forEach(p -> p.setStCWarehouseLogisticStrategyId(finalMainObjid));
                insertItemList.addAll(itemList);
            }

        }
        if (CollectionUtils.isNotEmpty(insertList)) {
            stCWarehouseLogisticStrategyMapper.batchInsert(insertList);
        }
        if (CollectionUtils.isNotEmpty(insertItemList)) {
            stCWarehouseLogisticStrategyItemMapper.batchInsert(insertItemList);
        }

        int successSize = insertItemList.size();
        return successSize;
    }

    /**
     * 拷贝message到子表
     *
     * @param impVoList
     * @param message
     */
    private void copyMessageToItem(List<StCWarehouseLogisticStrategyImpVo> impVoList, StringBuilder message) {
        for (StCWarehouseLogisticStrategyImpVo impVo : impVoList) {
            String desc = impVo.getDesc();
            if (StringUtils.isNotEmpty(message.toString())) {
                if (StringUtils.isNotBlank(desc)) {
                    desc = desc + message.toString();
                } else {
                    desc = message.toString();
                }
                impVo.setDesc(desc);

            }
        }
    }

    /**
     * 根据仓库物流明细查找仓库物流信息
     *
     * @param request
     * @return
     */
    public List<StCWarehouseLogisticStrategy> queryLogisticStrategyByDetail(StCWarehouseLogisticStrategyItemQueryRequest request)
            throws NDSException {
        return stCWarehouseLogisticStrategyMapper.queryLogisticStrategyByDetail(request);
    }

    /**
     * 根据仓库物流 实体仓 查找仓库物流信息 （大货物流明细）
     */
    public ValueHolderV14<StCWarehouseLogisticStrategyQueryResult> queryLogisticStrategyByWarehouseId(StCWarehouseLogisticStrategyQueryRequest request) {

        if (log.isDebugEnabled()) {
            log.info(this.getClass().getName() + " queryLogisticStrategyByWarehouseId,request:{}", JSONObject.toJSONString(request));
        }

        ValueHolderV14<StCWarehouseLogisticStrategyQueryResult> result = new ValueHolderV14<>(ResultCode.SUCCESS, "SUCCESS");

        if (request == null || CollectionUtils.isEmpty(request.getWarehouseIdList())) {
            result.setCode(ResultCode.FAIL);
            result.setMessage("查询仓库物流设置参数为空");
            return result;
        }

        List<Long> warehouseIdList = request.getWarehouseIdList();

        List<StCWarehouseLogisticStrategy> warehouseLogisticStrategies
                = stCWarehouseLogisticStrategyMapper.selectList(new QueryWrapper<StCWarehouseLogisticStrategy>()
                .lambda()
                .in(StCWarehouseLogisticStrategy::getCpCPhyWarehouseId, warehouseIdList)
                .eq(StCWarehouseLogisticStrategy::getIsactive, StConstant.ISACTIVE_Y));

        if (CollectionUtils.isEmpty(warehouseLogisticStrategies)) {

            result.setCode(ResultCode.FAIL);
            result.setMessage("未查到对应仓库物流设置");
            return result;
        }

        List<Long> mainTableIdList = warehouseLogisticStrategies.stream().map(StCWarehouseLogisticStrategy::getId).collect(Collectors.toList());

        List<StCWarehouseLogisticStrategyItem> warehouseLogisticStrategyItemList = stCWarehouseLogisticStrategyItemMapper.selectList(new QueryWrapper<StCWarehouseLogisticStrategyItem>()
                .lambda()
                .in(StCWarehouseLogisticStrategyItem::getStCWarehouseLogisticStrategyId, mainTableIdList)
//                .in(StCWarehouseLogisticStrategyItem::getLogisticsType, LogisticsTypeEnum.BULK_CARGO_TRANSPORTATION.getKey(),LogisticsTypeEnum.TRANSPORTATION.getKey())
                .eq(StCWarehouseLogisticStrategyItem::getIsactive, StConstant.ISACTIVE_Y));

        if (CollectionUtils.isEmpty(warehouseLogisticStrategyItemList)) {

            result.setCode(ResultCode.FAIL);
            result.setMessage("未查到对应仓库物流设置明细");
            return result;
        }

        Map<Long, List<StCWarehouseLogisticStrategyItem>> collect = warehouseLogisticStrategyItemList.stream().collect(Collectors.groupingBy(StCWarehouseLogisticStrategyItem::getStCWarehouseLogisticStrategyId));
        List<Long> logisticsIdList = warehouseLogisticStrategyItemList.stream().map(StCWarehouseLogisticStrategyItem::getCpCLogisticsId).distinct().collect(Collectors.toList());

        StCWarehouseLogisticStrategyQueryResult resultModel = new StCWarehouseLogisticStrategyQueryResult();
        resultModel.setLogisticStrategiesList(warehouseLogisticStrategies);
        resultModel.setLogisticStrategiesItemMap(collect);
        resultModel.setLogisticsIdList(logisticsIdList);

        result.setData(resultModel);
        return result;
    }

    public ValueHolderV14<List<StCWarehouseLogisticStrategyResult>> queryLogisticStrategyByWarehouseAndLogistics(StCWarehouseLogisticStrategyItemQueryRequest request) {

        List<StCWarehouseLogisticStrategyResult> result = stCWarehouseLogisticStrategyMapper.queryLogisticStrategyByWarehouseAndLogistics(request);
        return new ValueHolderV14<>(result, ResultCode.SUCCESS, "SUCCESS");

    }
}
