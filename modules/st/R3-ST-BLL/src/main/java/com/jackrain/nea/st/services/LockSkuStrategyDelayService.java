package com.jackrain.nea.st.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.google.common.base.Throwables;
import com.google.common.collect.Lists;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.st.common.StConstant;
import com.jackrain.nea.st.mapper.StCLockSkuStrategyItemMapper;
import com.jackrain.nea.st.mapper.StCLockSkuStrategyMapper;
import com.jackrain.nea.st.model.table.StCLockSkuStrategyDO;
import com.jackrain.nea.st.model.table.StCLockSkuStrategyItemDO;
import com.jackrain.nea.st.utils.DatasToEsUtils;
import com.jackrain.nea.st.utils.ListUtils;
import com.jackrain.nea.st.utils.StBeanUtils;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * @author: 陈俊明
 * @since: 2019-04-08
 * @create at : 2019-04-08 21:10
 */
@Slf4j
@Component
@Transactional
public class LockSkuStrategyDelayService {
    @Autowired
    StCLockSkuStrategyMapper stCLockSkuStrategyMapper;

    @Autowired
    private StCLockSkuStrategyItemMapper stCLockSkuStrategyItemMapper;

//    @Reference(group = "sg", version = "1.0")
//    private SgLockSkuStrategyBasicCmd skuStrategyBasicCmd;
//    @Autowired
//    private ShopUniversalStockService shopUniversalStockService;

    public ValueHolderV14 execute(JSONObject obj, User user) throws NDSException {
        ValueHolderV14 valueHolder = new ValueHolderV14();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(
                obj, "yyyy-MM-dd HH:mm:ss"),
                Feature.OrderedField);

        Date date = obj.getDate("delayDate");
        if (param != null) {
            JSONArray itemArray = StBeanUtils.makeAuditJsonArray(param);
            valueHolder = lockSkuStrategyDelay(date, itemArray, valueHolder, user);
        } else {
            throw new NDSException("参数为空！");
        }
        return valueHolder;
    }

    /**
     * 延期设置
     *
     * @param itemArray
     * @param valueHolderv14
     * @param user
     * @return 结果
     */
    public ValueHolderV14 lockSkuStrategyDelay(Date date, JSONArray itemArray, ValueHolderV14 valueHolderv14, User user) {

        //延期记录前，先判断是否存在
        int size = itemArray.size();
        if (size > 0) {
            List<StCLockSkuStrategyDO> cLockSkuStrategyList = stCLockSkuStrategyMapper.
                    selectBatchIds(JSONObject.parseArray(itemArray.toJSONString(), Long.class));
            JSONArray errorArray = new JSONArray();
            // 同步策略到PG库
            List<Long> ids = Lists.newArrayListWithExpectedSize(size);
            for (StCLockSkuStrategyDO cLockSkuStrategyDO : cLockSkuStrategyList) {
                JSONObject errJo = new JSONObject();

                Long objId = cLockSkuStrategyDO.getId();
                JSONObject errorJson = checkStCLockSkuStrategy(date, cLockSkuStrategyDO, objId, errJo);
                if (errorJson != null) {
                    errorArray.add(errorJson);
                } else {
                    StBeanUtils.makeModifierField(cLockSkuStrategyDO, user);
                    //结束时间
                    cLockSkuStrategyDO.setLockEtime(date);
                    int iResult = stCLockSkuStrategyMapper.updateById(cLockSkuStrategyDO);
                    if (iResult < 0) {
                        errJo.put("objid", objId);
                        errJo.put("message", "延期失败！");
                        errorArray.add(errJo);
                    } else {
                        ids.add(objId);
                        //推送ES数据
                        try {
                            //做更新的需要先查询更新后数据库的实体在推ES
                            cLockSkuStrategyDO = stCLockSkuStrategyMapper.selectById(objId);
                            StCLockSkuStrategyItemDO item = new StCLockSkuStrategyItemDO();
                            item.setStatus(cLockSkuStrategyDO.getEstatus());
                            item.setBeginTime(cLockSkuStrategyDO.getLockBtime());
                            item.setEndTime(cLockSkuStrategyDO.getLockEtime());
                            item.setCpCShopId(cLockSkuStrategyDO.getCpCShopId());
                            item.setCpCShopEcode(cLockSkuStrategyDO.getCpCShopEcode());
                            item.setCpCShopTitle(cLockSkuStrategyDO.getCpCShopTitle());
                            item.setMainCreationdate(cLockSkuStrategyDO.getCreationdate());
                            QueryWrapper<StCLockSkuStrategyItemDO> wrapper = new QueryWrapper<>();
                            wrapper.eq("st_c_lock_sku_strategy_id", objId);
                            stCLockSkuStrategyItemMapper.update(item, wrapper);
                            List<StCLockSkuStrategyItemDO> itemList = ListUtils.batchQueryByCondition(wrapper, stCLockSkuStrategyItemMapper, 1000);
                            DatasToEsUtils.insertLoclSkuEsData(cLockSkuStrategyDO, null, StConstant.TAB_ST_C_LOCK_SKU_STRATEGY);
                            if (CollectionUtils.isNotEmpty(itemList)) {
                                DatasToEsUtils.insertLoclSkuEsData(cLockSkuStrategyDO, itemList, StConstant.TAB_ST_C_LOCK_SKU_STRATEGY_ITEM);
                            }
                        } catch (Exception ex) {
                            log.debug(LogUtil.format("店铺锁库条码特殊设置策略延期推数据到ES失败：{}"), Throwables.getStackTraceAsString(ex));
                        }
                    }
                }

            }
            if (!ids.isEmpty()) {
//                skuStrategyBasicCmd.delay(ids, date);
            }
            valueHolderv14 = StBeanUtils.getProcessValueHolderV14(itemArray, errorArray, "延期");
            return valueHolderv14;
        }
        return valueHolderv14;
    }

    private JSONObject checkStCLockSkuStrategy(Date date, StCLockSkuStrategyDO cLockSkuStrategyDO,
                                               Long objId, JSONObject errJo) {
        if (!StConstant.CON_BILL_STATUS_02.equals(cLockSkuStrategyDO.getEstatus())) {
            errJo.put("objid", objId);
            errJo.put("message", "当前记录不是已审核，不允许延期！");
            return errJo;
        }

        if (date != null) {
            if (date.before(new Date())) {
                errJo.put("objid", objId);
                errJo.put("message", "结束时间不能早于当前时间！");
                return errJo;
            }
        } else {
            errJo.put("objid", objId);
            errJo.put("message", "请选择延期结束日期！");
            return errJo;
        }

        Date beginDate = cLockSkuStrategyDO.getLockBtime();
        if (beginDate != null) {
            if (date.before(beginDate)) {
                errJo.put("objid", objId);
                errJo.put("message", "结束时间不能早于开始时间！");
                return errJo;
            }
        }

        Date endDate = cLockSkuStrategyDO.getLockEtime();
        if (endDate != null) {
            if (date.before(endDate)) {
                errJo.put("objid", objId);
                errJo.put("message", "生效结束时间不能早于原来生效结束时间！");
                return errJo;
            }
        }
        return null;
    }
}
