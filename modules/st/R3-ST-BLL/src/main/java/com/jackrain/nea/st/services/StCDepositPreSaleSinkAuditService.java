package com.jackrain.nea.st.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.st.common.StConstant;
import com.jackrain.nea.st.mapper.StCDepositPreSaleSinkMapper;
import com.jackrain.nea.st.model.table.StCDepositPreSaleSinkDO;
import com.jackrain.nea.st.utils.RedisCacheUtil;
import com.jackrain.nea.st.utils.RedisConstant;
import com.jackrain.nea.st.utils.StBeanUtils;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;

/**
 * description:定金预售预下沉策略审核
 * @Author:  liuwenjin
 * @Date 2021/9/23 5:26 下午
 */
@Component
@Slf4j
public class StCDepositPreSaleSinkAuditService extends CommandAdapter {
    @Autowired
    private StCDepositPreSaleSinkMapper mapper;

    @Override
    public ValueHolder execute(QuerySession querySession) throws NDSException {

        //1.获取传入参数
        DefaultWebEvent event = querySession.getEvent();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(event.getParameterValue("param"),
                "yyyy-MM-dd HH:mm:ss", SerializerFeature.WriteMapNullValue), Feature.OrderedField);
        if (param == null) {
            throw new NDSException("参数为空！");
        }
        log.debug(LogUtil.format("Start StCHoldOrderAuditService.execute. ReceiveParams: {}"), param.toJSONString());
        //2.定义错误记录数
        HashMap<Long, Object> errMap = new HashMap();
        //3.生成审核Json数组
        JSONArray auditArray = StBeanUtils.makeAuditJsonArray(param);
        for (int i = 0; i < auditArray.size(); i++) {
            Long id = auditArray.getLong(i);
            try {
                //4.遍历审核方法
                auditStCDepositPreSaleSink(id, querySession);
            } catch (Exception e) {
                errMap.put(id, e.getMessage());
            }
        }
        //5.返回ValueHolder对象
        return StBeanUtils.getExcuteValueHolder(auditArray.size(), errMap);
    }
    public void auditStCDepositPreSaleSink(Long id, QuerySession querySession) {
        StCDepositPreSaleSinkDO stCDepositPreSaleSinkDO= mapper.selectById(id);
        //主表校验
        checkEstatus(stCDepositPreSaleSinkDO);

        //更新单据状态
        StBeanUtils.makeModifierField(stCDepositPreSaleSinkDO, querySession.getUser());
        stCDepositPreSaleSinkDO.setEstatus(StConstant.HOLD_ORDER_STATUS_02);
        StBeanUtils.makeModifierField(stCDepositPreSaleSinkDO,querySession.getUser());
        int updateNum = mapper.updateById(stCDepositPreSaleSinkDO);
        if (updateNum < 0) {
            throw new NDSException("审核失败！");
        }
        String wearIds = stCDepositPreSaleSinkDO.getCpCPhyWarehouse();
        // 删除Redis缓存
        deleteRedisByWarehouseId(wearIds);
    }
    //删除Redis缓存
    private void deleteRedisByWarehouseId(String wearIds) {
        if (StringUtils.isNotEmpty(wearIds) && wearIds.contains("value")) {
            log.info(LogUtil.format("要解析的字符串为：{}"), wearIds);
            JSONObject jsonObject = JSONObject.parseObject(wearIds);
            JSONObject valJson = jsonObject.getJSONObject("value");
            String valIds =valJson.getString("IN");
            String warehouseIds = StringUtils.strip(valIds,"[]");
            if (StringUtils.isNotEmpty(warehouseIds)) {
                String [] ids = warehouseIds.split(",");
                for (String id : ids) {
                    RedisCacheUtil.delete(Long.parseLong(id), RedisConstant.ST_DEPOSIT_PRE_SALE_SINK_KEY);
                }
            }
        }
    }
    /**
     * description:校验状态
     * @Author:  liuwenjin
     * @Date 2021/9/23 6:03 下午
     */
    private void checkEstatus(StCDepositPreSaleSinkDO stCDepositPreSaleSinkDO) {
        if (stCDepositPreSaleSinkDO == null) {
            throw new NDSException("当前记录已不存在！");
        } else {
            if (StConstant.CON_BILL_STATUS_02.equals(stCDepositPreSaleSinkDO.getEstatus())) {
                throw new NDSException("当前记录已审核，不允许重复审核！");
            }else if(StConstant.CON_BILL_STATUS_03.equals(stCDepositPreSaleSinkDO.getEstatus())){
                throw new NDSException("当前记录已作废，不允许审核！");
            }else if(StConstant.CON_BILL_STATUS_04.equals(stCDepositPreSaleSinkDO.getEstatus())){
                throw new NDSException("当前记录已结案，不允许审核！");
            }
        }
    }
}
