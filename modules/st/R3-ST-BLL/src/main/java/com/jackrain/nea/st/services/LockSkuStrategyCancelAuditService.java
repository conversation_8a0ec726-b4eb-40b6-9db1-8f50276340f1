//package com.jackrain.nea.st.services;
//
//import com.alibaba.fastjson.JSON;
//import com.alibaba.fastjson.JSONArray;
//import com.alibaba.fastjson.JSONObject;
//import com.alibaba.fastjson.parser.Feature;
//import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
//import com.google.common.collect.Lists;
//import com.jackrain.nea.exception.NDSException;
//import com.jackrain.nea.st.common.StConstant;
//import com.jackrain.nea.st.mapper.StCLockSkuStrategyItemMapper;
//import com.jackrain.nea.st.mapper.StCLockSkuStrategyMapper;
//import com.jackrain.nea.st.model.table.StCLockSkuStrategyDO;
//import com.jackrain.nea.st.model.table.StCLockSkuStrategyItemDO;
//import com.jackrain.nea.st.utils.DatasToEsUtils;
//import com.jackrain.nea.st.utils.ListUtils;
//import com.jackrain.nea.st.utils.StBeanUtils;
//import com.jackrain.nea.sys.CommandAdapter;
//import com.jackrain.nea.util.ValueHolder;
//import com.jackrain.nea.web.query.DefaultWebEvent;
//import com.jackrain.nea.web.query.QuerySession;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.commons.collections.CollectionUtils;
//import org.apache.dubbo.config.annotation.Reference;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.stereotype.Component;
//import org.springframework.transaction.annotation.Transactional;
//
//import java.util.List;
//
//@Component
//@Slf4j
//@Transactional
//public class LockSkuStrategyCancelAuditService extends CommandAdapter {
//    @Autowired
//    private StCLockSkuStrategyMapper stCLockSkuStrategyMapper;
//
//    @Autowired
//    private StCLockSkuStrategyItemMapper stCLockSkuStrategyItemMapper;
//
//    @Reference(group = "sg", version = "1.0")
//    private SgLockSkuStrategyBasicCmd skuStrategyBasicCmd;
//
//    @Autowired
//    private ShopUniversalStockService shopUniversalStockService;
//
//    /**
//     * 反审核
//     *
//     * @param querySession 参数封装
//     * @return 返回状态
//     * @throws NDSException 异常信息
//     */
//    @Override
//    public ValueHolder execute(QuerySession querySession) throws NDSException {
//        ValueHolder valueHolder = new ValueHolder();
//        DefaultWebEvent event = querySession.getEvent();
//        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(
//                event.getParameterValue("param"), "yyyy-MM-dd HH:mm:ss"),
//                Feature.OrderedField);
//
//        if (param != null) {
//            JSONArray itemArray = StBeanUtils.makeUnAuditJsonArray(param);
//
//            valueHolder = cancelAuditLockSkuStrategy(itemArray, valueHolder, querySession);
//        } else {
//            throw new NDSException("参数为空！");
//        }
//        return valueHolder;
//    }
//
//    private JSONObject checkStatus(StCLockSkuStrategyDO stCLockSkuStrategyDO, Long objId, JSONObject errJo) {
//        int iStatus = stCLockSkuStrategyDO.getEstatus();
//        if (iStatus != StConstant.CON_BILL_STATUS_02) {
//            errJo.put("objid", objId);
//            errJo.put("message", "单据处于审核状态才能进行反审！");
//            return errJo;
//        }
//
//        return null;
//    }
//
//    private ValueHolder cancelAuditLockSkuStrategy(JSONArray itemArray, ValueHolder valueHolder, QuerySession querySession) {
//        //反审核记录前，先判断是否存在
//        int size = itemArray.size();
//        if (size > 0) {
//            List<StCLockSkuStrategyDO> cLockSkuStrategyDOList = stCLockSkuStrategyMapper.
//                    selectBatchIds(JSONObject.parseArray(itemArray.toJSONString(), Long.class));
//            List<Long> ids = Lists.newArrayListWithExpectedSize(size);
//            JSONArray errorArray = new JSONArray();
//            for (StCLockSkuStrategyDO cLockSkuStrategyDO : cLockSkuStrategyDOList) {
//                JSONObject errJo = new JSONObject();
//                Long objId = cLockSkuStrategyDO.getId();
//
//                JSONObject errorJson = checkStatus(cLockSkuStrategyDO, objId, errJo);
//                if (errorJson != null) {
//                    errorArray.add(errorJson);
//                }
//                JSONObject jsonObject = new JSONObject();
//                StBeanUtils.makeModifierField(jsonObject, querySession.getUser());
//                //反审-审核置空信息
//                StBeanUtils.makeCancelField(jsonObject);
//                StBeanUtils.makeCancelField(jsonObject, querySession.getUser());
//                //BILL_STATUS    1:未审核 2:已审核 3:已作废 4:已结案
//                jsonObject.put("ID", objId);
//                jsonObject.put("ESTATUS", StConstant.CON_BILL_STATUS_01);
//                jsonObject.put("SYNC_END_MARK",0);
//                jsonObject.put("SYNC_COMPLETE_MARK",0);
//
//                    int iResult = stCLockSkuStrategyMapper.updateAtrributes(jsonObject);
//                    if (iResult < 0) {
//                        errJo.put("objid", objId);
//                        errJo.put("message", "反审核失败！");
//                        errorArray.add(errJo);
//                    } else {
//                        ids.add(objId);
//                        //更新到同步库存中间
//                        ValueHolder v14 = shopUniversalStockService.deleteSgBSyncChannelStock(StConstant.LOCK_SKU_STRATEGY_TYPE.longValue(), objId);
//                        log.debug(LogUtil.format("锁库条码 反审核 删除同步库存中间表数据 策略id："+objId+" 结果："+JSONObject.toJSONString(v14));
//                        //推送ES数据
//                        try {
//                            //做更新的需要先查询更新后数据库的实体在推ES
//                            cLockSkuStrategyDO = stCLockSkuStrategyMapper.selectById(objId);
//                            StCLockSkuStrategyItemDO item = new StCLockSkuStrategyItemDO();
//                            item.setStatus(StConstant.CON_BILL_STATUS_01);
//                            QueryWrapper<StCLockSkuStrategyItemDO> wrapper = new QueryWrapper<>();
//                            wrapper.eq("st_c_lock_sku_strategy_id", objId);
//                            stCLockSkuStrategyItemMapper.update(item, wrapper);
//                            List<StCLockSkuStrategyItemDO> itemList = ListUtils.batchQueryByCondition(wrapper, stCLockSkuStrategyItemMapper, 1000);
//                            DatasToEsUtils.insertLoclSkuEsData(cLockSkuStrategyDO, null, StConstant.TAB_ST_C_LOCK_SKU_STRATEGY);
//                            if (CollectionUtils.isNotEmpty(itemList)) {
//                                DatasToEsUtils.insertLoclSkuEsData(cLockSkuStrategyDO, itemList, StConstant.TAB_ST_C_LOCK_SKU_STRATEGY_ITEM);
//                            }
//                        } catch (Exception ex) {
//                            log.debug(LogUtil.format("店铺锁库条码特殊设置策略主表反审核推数据到ES失败：" + ex.toString());
//                        }
//                    }
//                }
//
//            if (!ids.isEmpty()) {
//                // 反审核同步至PG
//                skuStrategyBasicCmd.status(ids, StConstant.CON_BILL_STATUS_01);
//            }
//            valueHolder = StBeanUtils.getProcessValueHolder(itemArray, errorArray, "反审核");
//            return valueHolder;
//        }
//        return valueHolder;
//    }
//
//}
//
