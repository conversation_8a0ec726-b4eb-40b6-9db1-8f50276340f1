package com.jackrain.nea.st.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.burgeon.r3.sg.basic.api.SgLockStockBasicCmd;
import com.google.common.base.Throwables;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.st.common.StConstant;
import com.jackrain.nea.st.mapper.StCLockStockStrategyItemMapper;
import com.jackrain.nea.st.mapper.StCLockStockStrategyMapper;
import com.jackrain.nea.st.model.table.StCLockStockStrategyDO;
import com.jackrain.nea.st.model.table.StCLockStockStrategyItemDO;
import com.jackrain.nea.st.utils.DatasToEsUtils;
import com.jackrain.nea.st.utils.ListUtils;
import com.jackrain.nea.st.utils.StBeanUtils;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.utils.AssertUtils;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.assertj.core.util.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.HashMap;
import java.util.List;

/**
 * @Descroption 店铺策略锁库设置审核
 * @Author: 汪聿森
 * @Date: 2019/3/26 19:17
 */

@Component
@Slf4j
public class LockStockAuditService extends CommandAdapter {
    @Autowired
    private StCLockStockStrategyMapper mapper;

    @Autowired
    private StCLockStockStrategyItemMapper stCLockStockStrategyItemMapper;

    @Reference(group = "sg", version = "1.0")
    private SgLockStockBasicCmd sgLockStockBasicCmd;

    @Autowired
    private ShopLockStockSyncStockService shopLockStockSyncStockService;

    @Override
    public ValueHolder execute(QuerySession querySession) throws NDSException {
        //1.获取传入参数
        DefaultWebEvent event = querySession.getEvent();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(event.getParameterValue("param"),
                "yyyy-MM-dd HH:mm:ss", SerializerFeature.WriteMapNullValue), Feature.OrderedField);
        if (param == null) {
            throw new NDSException("参数为空！");
        }
        log.debug(LogUtil.format("Start LockStockAuditService.execute. ReceiveParams: {}"), param.toJSONString());
        //2.定义错误记录数
        HashMap<Long, Object> errMap = new HashMap();
        //3.生成审核Json数组
        JSONArray auditArray = StBeanUtils.makeAuditJsonArray(param);
        List<Long> ids = Lists.newArrayList();
        if (auditArray.size() > 0) {
            List<StCLockStockStrategyDO> cLockStockStrategyDOList = mapper.
                    selectBatchIds(JSONObject.parseArray(auditArray.toJSONString(), Long.class));
            for (StCLockStockStrategyDO lockStockStrategyDO : cLockStockStrategyDOList) {
                try {
                    //4.遍历审核方法
                    auditLockStock(lockStockStrategyDO, querySession, ids);
                } catch (Exception e) {
                    errMap.put(lockStockStrategyDO.getId(), e.getMessage());
                }
            }
        }
        // 同步至PG
        sgLockStockBasicCmd.status(ids, StConstant.CON_BILL_STATUS_02);
        //5.返回ValueHolder对象
        return StBeanUtils.getExcuteValueHolder(auditArray.size(), errMap);
    }

    public void auditLockStock(StCLockStockStrategyDO stCLockStockStrategyDO, QuerySession querySession, List<Long> ids) {

        Long id = stCLockStockStrategyDO.getId();
        //主表校验
        checkLockStock(stCLockStockStrategyDO);

        //更新单据状态
        StBeanUtils.makeModifierField(stCLockStockStrategyDO, querySession.getUser());
        stCLockStockStrategyDO.setEstatus(StConstant.CON_BILL_STATUS_02);
        setAuditCommonField(stCLockStockStrategyDO, querySession.getUser());
        int updateNum = mapper.updateById(stCLockStockStrategyDO);
        if (updateNum < 0) {
            throw new NDSException("审核失败！");
        } else {
            // 同步至PG库状态
            ids.add(id);
            //推送ES数据
            try {
                //做更新的需要先查询更新后数据库的实体在推ES
                stCLockStockStrategyDO = mapper.selectById(id);
                StCLockStockStrategyItemDO item = new StCLockStockStrategyItemDO();
                item.setStatus(StConstant.CON_BILL_STATUS_02);
                item.setPlanName(stCLockStockStrategyDO.getPlanName());
                item.setRank(stCLockStockStrategyDO.getRank());
                item.setMainCreationdate(stCLockStockStrategyDO.getCreationdate());
                QueryWrapper<StCLockStockStrategyItemDO> wrapper = new QueryWrapper<>();
                wrapper.eq("st_c_lock_stock_strategy_id", id);
                stCLockStockStrategyItemMapper.update(item, wrapper);
                List<StCLockStockStrategyItemDO> itemList = ListUtils.batchQueryByCondition(wrapper, stCLockStockStrategyItemMapper, 1000);
                DatasToEsUtils.insertLoclStockEsData(stCLockStockStrategyDO, null, StConstant.TAB_ST_C_LOCK_STOCK_STRATEGY);
                if (CollectionUtils.isNotEmpty(itemList)) {
                    DatasToEsUtils.insertLoclStockEsData(stCLockStockStrategyDO, itemList, StConstant.TAB_ST_C_LOCK_STOCK_STRATEGY_ITEM);
                }
            } catch (Exception ex) {
                log.debug(LogUtil.format("店铺锁库策略作废推数据到ES失败：{}"), Throwables.getStackTraceAsString(ex));
            }
        }
    }

    private void checkLockStock(StCLockStockStrategyDO stCLockStockStrategyDO) {
        if (stCLockStockStrategyDO == null) {
            throw new NDSException("当前记录已不存在！");
        } else {
            if (StConstant.CON_BILL_STATUS_02.equals(stCLockStockStrategyDO.getEstatus())) {
                throw new NDSException("当前记录已审核，不允许重复审核！");
            } else if (StConstant.CON_BILL_STATUS_03.equals(stCLockStockStrategyDO.getEstatus())) {
                throw new NDSException("当前记录已作废，不允许审核！");
            } else if (StConstant.CON_BILL_STATUS_04.equals(stCLockStockStrategyDO.getEstatus())) {
                throw new NDSException("当前记录已结案，不允许审核！");
            }
            int count = stCLockStockStrategyItemMapper.selectCountByItemId(stCLockStockStrategyDO.getId());
            AssertUtils.cannot(count <= 0, "当前方案明细没有记录，不允许审核！");
        }
    }

    private void setAuditCommonField(StCLockStockStrategyDO stCLockStockStrategyDO, User user) {
        stCLockStockStrategyDO.setCheckid(Long.valueOf(user.getId()));
        stCLockStockStrategyDO.setCheckename(user.getEname());
        stCLockStockStrategyDO.setCheckname(user.getName());
        stCLockStockStrategyDO.setChecktime(new Date());
    }
}
