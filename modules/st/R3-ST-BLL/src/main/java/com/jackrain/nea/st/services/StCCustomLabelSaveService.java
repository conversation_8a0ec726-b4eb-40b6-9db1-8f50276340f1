package com.jackrain.nea.st.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.github.pagehelper.util.StringUtil;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.st.common.StConstant;
import com.jackrain.nea.st.mapper.StCCustomLabelMapper;
import com.jackrain.nea.st.model.request.StCCustomLabelRequest;
import com.jackrain.nea.st.model.request.StCOrderLabelRequest;
import com.jackrain.nea.st.model.table.StCCustomLabelDO;
import com.jackrain.nea.st.model.table.StCOrderLabelDO;
import com.jackrain.nea.st.utils.JsonUtils;
import com.jackrain.nea.st.utils.StBeanUtils;
import com.jackrain.nea.st.utils.ValueHolderUtils;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.StringUtils;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

/**
 * @ClassName : StCCustomLabelSaveService  
 * @Description : 自定义打标档案
 * <AUTHOR>  YCH
 * @Date: 2021-11-29 10:42  
 */
@Component
@Slf4j
@Transactional
public class StCCustomLabelSaveService  extends CommandAdapter {

    @Autowired
    private StCCustomLabelMapper stCCustomLabelMapper;
    @Override
    public ValueHolder execute(QuerySession session) {
        DefaultWebEvent event = session.getEvent();
        List<String> nullKeyList = new ArrayList<>();
        JSONObject param = JsonUtils.filterNull((JSONObject) event.getParameterValue("param"), nullKeyList);
        if (param != null) {
            Long id = param.getLong("objid");
            JSONObject
                    fixColumn = param.getJSONObject("fixcolumn");
            log.debug(LogUtil.format("fixColumn：{}"), JSON.toJSONString(fixColumn));
            log.info(LogUtil.format("StCCustomLabelSaveService.execute querySession:{}"), session);
            StCCustomLabelRequest stCCustomLabelRequest = JsonUtils.jsonParseClass(fixColumn, StCCustomLabelRequest.class);
            StCCustomLabelDO stCCustomLabelDO = stCCustomLabelRequest.getStCCustomLabelDO();
            if (fixColumn != null && id != null) {
                if (id != -1) {
                    return this.updateStCOrderLabel(session, stCCustomLabelDO, id);
                } else {
                    return this.insertStCOrderLabel(session, stCCustomLabelDO);
                }
            }
        }
        throw new NDSException("当前记录已不存在！");
    }

    private ValueHolder updateStCOrderLabel(QuerySession session, StCCustomLabelDO stCCustomLabelDO, Long id) {
        //主表修改
        StCCustomLabelDO cCustomLabelDO = stCCustomLabelMapper.selectById(id);
        if (cCustomLabelDO == null){
            return ValueHolderUtils.getFailValueHolder("记录不存在！");
        }
        if (stCCustomLabelDO != null) {
            ValueHolder holder = check(stCCustomLabelDO);
            if (holder != null) {
                return holder;
            }

            stCCustomLabelDO.setId(id);
            StBeanUtils.makeModifierField(stCCustomLabelDO, session.getUser());
            log.debug(LogUtil.format("stCCustomLabelDO：{}"), JSON.toJSONString(stCCustomLabelDO));
            if (stCCustomLabelMapper.updateById(stCCustomLabelDO) < 0) {
                return ValueHolderUtils.getFailValueHolder("更新失败！");
            }
        }
        return ValueHolderUtils.getSuccessValueHolder(id, StConstant.TAB_ST_C_CUSTOM_LABEL, "");
    }

    private ValueHolder insertStCOrderLabel(QuerySession session, StCCustomLabelDO stCCustomLabelDO) {
        long id = 0;
        if (stCCustomLabelDO != null) {
            //1.1 判断名称是否已存在
            ValueHolder check = check(stCCustomLabelDO);
            if (check != null) {
                return check;
            }
            //1.2 插入
            id = ModelUtil.getSequence(StConstant.TAB_ST_C_CUSTOM_LABEL);
            stCCustomLabelDO.setId(id);
            StBeanUtils.makeCreateField(stCCustomLabelDO, session.getUser());
            int insertResult = stCCustomLabelMapper.insert(stCCustomLabelDO);
            if (insertResult < 0) {
                return ValueHolderUtils.getFailValueHolder("保存失败！");
            }
        }
        return ValueHolderUtils.getSuccessValueHolder(id, StConstant.TAB_ST_C_CUSTOM_LABEL, "");
    }

    private ValueHolder check(StCCustomLabelDO stCCustomLabelDO) {
        String ename = stCCustomLabelDO.getEname();
        if (StringUtil.isNotEmpty(ename)){
            List<StCCustomLabelDO> stCCustomLabelDOS = stCCustomLabelMapper.selectList(new QueryWrapper<StCCustomLabelDO>().lambda()
                    .eq(StCCustomLabelDO::getEname, ename));
            if (CollectionUtils.isNotEmpty(stCCustomLabelDOS)){
                return ValueHolderUtils.getFailValueHolder("已存在相同标签!");
            }

        }
        return null;
    }
}
