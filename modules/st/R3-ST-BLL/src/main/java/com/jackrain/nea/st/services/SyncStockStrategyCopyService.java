package com.jackrain.nea.st.services;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.redis.util.RedisOpsUtil;
import com.jackrain.nea.st.common.StConstant;
import com.jackrain.nea.st.mapper.StCSyncStockStrategyItemMapper;
import com.jackrain.nea.st.mapper.StCSyncStockStrategyMapper;
import com.jackrain.nea.st.model.table.StCSyncStockStrategyDO;
import com.jackrain.nea.st.model.table.StCSyncStockStrategyItemDO;
import com.jackrain.nea.st.utils.RedisCacheUtil;
import com.jackrain.nea.st.utils.RedisConstant;
import com.jackrain.nea.st.utils.StBeanUtils;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @Descroption 店铺同步库存策略复制
 * <AUTHOR>
 * @Date 2019/8/19 14:55
 */
@Component
@Slf4j
public class SyncStockStrategyCopyService {
    @Autowired
    private StCSyncStockStrategyMapper mapper;

    @Autowired
    private StCSyncStockStrategyItemMapper itemMapper;

    @Autowired
    private RedisOpsUtil redisUtil;

    /**
     * 复制
     * @param obj 入参
     * @param user 用户
     * @return 结果
     */
    public ValueHolderV14 copyStrategy(JSONObject obj, User user) {
        ValueHolderV14 vh = new ValueHolderV14();

        if (null == obj) {
            vh.setCode(ResultCode.FAIL);
            vh.setMessage("请求参数不能为空！");
            return vh;
        }
        //获取ID
        JSONArray idsArray = obj.getJSONArray("ids");
        Long shopId = obj.getLong("shopId");

        if (idsArray != null && idsArray.size() > 0) {
            for (int i = 0; i < idsArray.size(); i++) {
                Long objId = idsArray.getLong(i);
                StCSyncStockStrategyDO stCSyncStockStrategyDO = mapper.selectById(objId);

                if (stCSyncStockStrategyDO == null) {
                    vh.setCode(ResultCode.FAIL);
                    vh.setMessage("当前记录已不存在！");
                    return vh;
                } else {
                    //清除rediskey
                    String redisKey = RedisConstant.bulidLockStCSyncStockStrategyItemKey(stCSyncStockStrategyDO.getCpCShopId());
                    if(redisUtil.objRedisTemplate.hasKey(redisKey)){
                        redisUtil.objRedisTemplate.delete(redisKey);
                    }
                    RedisCacheUtil.delete(stCSyncStockStrategyDO.getCpCShopId(), RedisConstant.SHOP_SYNC_STOCK_STRATEGY);
                    RedisCacheUtil.deleteAll(RedisConstant.SHOP_SYNC_STOCK_STRATEGY_ALL);
                }
                //根据店铺找出店铺同步策略
                StCSyncStockStrategyDO copySyncStockStrategyDO = null;
                if (shopId != null) {
                    List<StCSyncStockStrategyDO> copyList = mapper.selectBycpCShopId(shopId);
                    if (CollectionUtils.isNotEmpty(copyList)) {
                        copySyncStockStrategyDO = copyList.get(0);
                    }
                } else {
                    vh.setCode(ResultCode.FAIL);
                    vh.setMessage("请选择店铺！");
                    return vh;
                }
                //开始复制
                if (copySyncStockStrategyDO != null) {
                    //先删除
                    itemMapper.delete(new QueryWrapper<StCSyncStockStrategyItemDO>()
                            .eq("ST_C_SYNC_STOCK_STRATEGY_ID", objId));

                    List<StCSyncStockStrategyItemDO> copyItemList = itemMapper.selectList(new QueryWrapper<StCSyncStockStrategyItemDO>()
                            .lambda().eq(StCSyncStockStrategyItemDO::getStCSyncStockStrategyId, copySyncStockStrategyDO.getId()));
                    for (StCSyncStockStrategyItemDO syncStockStrategyItem : copyItemList) {

                        syncStockStrategyItem.setId(ModelUtil.getSequence(StConstant.TAB_ST_C_SYNC_STOCK_STRATEGY_ITEM));
                        syncStockStrategyItem.setStCSyncStockStrategyId(objId);
                        StBeanUtils.makeCreateField(syncStockStrategyItem, user);

                        int insert = itemMapper.insert(syncStockStrategyItem);
                        if (insert < 0) {
                            throw new NDSException("店铺同步库存策略-明细插入失败！");
                        }
                    }
                    StBeanUtils.makeModifierField(stCSyncStockStrategyDO, user);
                    if (mapper.updateById(stCSyncStockStrategyDO) < 0) {
                        vh.setCode(ResultCode.FAIL);
                        vh.setMessage("复制出错！");
                        return vh;
                    }
                } else {
                    vh.setCode(ResultCode.FAIL);
                    vh.setMessage("查询不到已选店铺的策略！");
                    return vh;
                }
            }
        } else {
            vh.setCode(ResultCode.FAIL);
            vh.setMessage("单据ID为空，传参失败！");
            return vh;
        }
        vh.setCode(ResultCode.SUCCESS);
        vh.setMessage("方案复制成功！");
        return vh;
    }
}
