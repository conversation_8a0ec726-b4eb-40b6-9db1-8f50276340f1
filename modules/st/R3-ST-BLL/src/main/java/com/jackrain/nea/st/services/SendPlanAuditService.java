package com.jackrain.nea.st.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.st.common.StConstant;
import com.jackrain.nea.st.mapper.*;
import com.jackrain.nea.st.model.table.StCScalpingDO;
import com.jackrain.nea.st.model.table.StCScalpingLogisticsDO;
import com.jackrain.nea.st.model.table.StCScalpingReplaceProDO;
import com.jackrain.nea.st.model.table.StCSendPlanDO;
import com.jackrain.nea.st.utils.RedisCacheUtil;
import com.jackrain.nea.st.utils.RedisConstant;
import com.jackrain.nea.st.utils.StBeanUtils;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.HashMap;
import java.util.List;

/**
 * @Descroption 订单派单方案-审核逻辑
 * <AUTHOR>
 * @Date 2019/3/8
 */
@Component
@Slf4j
@Transactional
public class SendPlanAuditService extends CommandAdapter {
    @Autowired
    private StCSendPlanMapper stCMainMapper;
    @Autowired
    private StCSendPlanItemMapper stCItemMapperA;

    @Override
    public ValueHolder execute(QuerySession querySession) throws NDSException {
        DefaultWebEvent event = querySession.getEvent();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(
                event.getParameterValue("param"), "yyyy-MM-dd HH:mm:ss"),
                Feature.OrderedField);

        JSONArray itemArray = StBeanUtils.makeAuditJsonArray(param);
        JSONArray errorArray = new JSONArray();
        HashMap<Long, Object> errMap = new HashMap();
        if (itemArray.size() > 0) {
            for (int i = 0; i < itemArray.size(); i++) {
                Long itemid = itemArray.getLong(i);
                try {
                    auditByID(querySession, itemid, errorArray);
                } catch (Exception e) {
                    errMap.put(itemid, e.getMessage());
                }
            }
        }
        return StBeanUtils.getExcuteValueHolder(itemArray.size(), errMap);
    }

    private void auditByID(QuerySession session, Long id, JSONArray errorArray) {
        StCSendPlanDO stcMainDo = stCMainMapper.selectById(id);
        JSONArray tempArray = new JSONArray();
        checkAudit(stcMainDo, id, tempArray);
        if (tempArray.size() > 0) {
            errorArray.addAll(tempArray);
            return;
        }

        StBeanUtils.makeModifierField(stcMainDo, session.getUser());//修改信息
        stcMainDo.setModifierename(session.getUser().getEname());//修改人账号
        stcMainDo.setEstatus(StConstant.CON_BILL_STATUS_02);
        stcMainDo.setCheckid(Long.valueOf(session.getUser().getId()));//审核人
        stcMainDo.setChecktime(new Date());//审核时间
        stcMainDo.setCheckname(session.getUser().getName());//审核人姓名
        stcMainDo.setCheckename(session.getUser().getEname());//审核人账号
        if ((stCMainMapper.updateById(stcMainDo)) <= 0) {
            throw new NDSException("方案:" + stcMainDo.getEname() + ",审核失败！");
        }
        RedisCacheUtil.delete(stcMainDo.getCpCShopId(), RedisConstant.SHOP_SEND_PLAN);
    }

    private void checkAudit(StCSendPlanDO stCCheckDO, Long id, JSONArray errorArray) {
        if (stCCheckDO == null) {
            throw new NDSException("当前记录已不存在");
        }
        //不是未审核，不允许审核
        if (stCCheckDO.getEstatus() == null
                || !StConstant.CON_BILL_STATUS_01.equals(stCCheckDO.getEstatus())) {
            throw new NDSException("当前记录不是未审核，不允许审核！");
        }
        if (stCCheckDO.getBeginTime() == null) {
            throw new NDSException("方案的开始日期为空，不允许审核！");
        }
//        if (stCCheckDO.getBeginTime().before(new Date())) {
//            errorArray.add(StBeanUtils.getJsonObjectInfo(id, "方案的开始日期小于当前日期，不允许审核！"));
//            return;
//        }
        if (stCCheckDO.getEndTime() == null) {
            throw new NDSException("方案的结束日期为空，不允许审核！");
        }
        if (stCCheckDO.getEndTime().before(stCCheckDO.getBeginTime())) {
            throw new NDSException("方案的结束日期小于开始日期，不允许审核！");
        }
        if (stCItemMapperA.selectItemCountByMainId(id) <= 0) {
            throw new NDSException("当前方案没有设置派单明细，不允许审核！");
        }
    }

}
