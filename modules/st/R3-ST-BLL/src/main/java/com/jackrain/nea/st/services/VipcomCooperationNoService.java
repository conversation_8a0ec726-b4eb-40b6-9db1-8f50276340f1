package com.jackrain.nea.st.services;

import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.st.mapper.StCVipcomCooperationNoMapper;
import com.jackrain.nea.st.model.table.StCVipcomCooperationNo;
import com.jackrain.nea.sys.CommandAdapter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * description：根据常态合作编码查询常态合作编码信息
 * <AUTHOR>
 * @date 2021/7/7
 */

@Component
@Slf4j
public class VipcomCooperationNoService extends CommandAdapter {
    @Autowired
    private StCVipcomCooperationNoMapper stCVipcomCooperationNoMapper;

    /**
     * 根据常态合作编码查询常态合作编码信息
     * @param ecode 常态合作编码
     * @return stCVipcomCooperationNo
     */

    public StCVipcomCooperationNo queryCooperationNoInfo(String ecode) {
        StCVipcomCooperationNo stCVipcomCooperationNo = stCVipcomCooperationNoMapper.queryCooperationNoInfoByNo(ecode);
        return stCVipcomCooperationNo;
    }

}
