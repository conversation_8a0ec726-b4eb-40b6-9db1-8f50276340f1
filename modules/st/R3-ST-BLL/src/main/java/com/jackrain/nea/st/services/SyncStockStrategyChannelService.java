package com.jackrain.nea.st.services;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jackrain.nea.st.annotation.StOperationLog;
import com.jackrain.nea.st.mapper.StCSyncStockStrategyChannelMapper;
import com.jackrain.nea.st.model.request.SyncStockStrategyChannelQueryRequest;
import com.jackrain.nea.st.model.table.StCSyncStockStrategyChannelDO;
import com.jackrain.nea.utils.AssertUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 同步库存策略渠道明细
 *
 * <AUTHOR>
 * @since 2020-08-19
 * create at : 2020-08-19 13:43
 */
@Slf4j
@Component
public class SyncStockStrategyChannelService extends
        ServiceImpl<StCSyncStockStrategyChannelMapper, StCSyncStockStrategyChannelDO> {

    @Autowired
    private StCSyncStockStrategyChannelMapper syncStockStrategyChannelMapper;

    /**
     * 查询库存同步策略渠道明细集合
     *
     * @param req 请求对象
     * @return 渠道明细集合
     */
    public List<StCSyncStockStrategyChannelDO> getSyncStockStrategyChannel(SyncStockStrategyChannelQueryRequest req) {
        LambdaQueryWrapper<StCSyncStockStrategyChannelDO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(StringUtils.isNotBlank(req.getIsactive()),
                StCSyncStockStrategyChannelDO::getIsactive, req.getIsactive());
        wrapper.in(CollectionUtils.isNotEmpty(req.getSyncStockStrategyIds()),
                StCSyncStockStrategyChannelDO::getStCSyncStockStrategyId, req.getSyncStockStrategyIds());
        wrapper.in(CollectionUtils.isNotEmpty(req.getOrgChannelIds()),
                StCSyncStockStrategyChannelDO::getCpCOrgChannelId, req.getOrgChannelIds());
        wrapper.in(CollectionUtils.isNotEmpty(req.getOrgChannelCodes()),
                StCSyncStockStrategyChannelDO::getCpCOrgChannelEcode, req.getOrgChannelCodes());
        return syncStockStrategyChannelMapper.selectList(wrapper);
    }
}
