package com.jackrain.nea.st.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.st.mapper.StCExchangeStrategyOrderMapper;
import com.jackrain.nea.st.mapper.StCMergeOrderMapper;
import com.jackrain.nea.st.model.table.StCExchangeStrategyOrderDO;
import com.jackrain.nea.st.model.table.StCMergeOrderDO;
import com.jackrain.nea.st.utils.RedisCacheUtil;
import com.jackrain.nea.st.utils.RedisConstant;
import com.jackrain.nea.st.utils.StBeanUtils;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.HashMap;


/**
 * <AUTHOR> ruan.gz
 * @Description : 作废换货策略
 * @Date : 2020/6/19
 **/
@Component
@Slf4j
@Transactional
public class ExchangeStrategyOrderVoidService extends CommandAdapter {

    @Autowired
    private StCExchangeStrategyOrderMapper stCExchangeStrategyOrderMapper;

    /**
     * <AUTHOR> ruan.gz
     * @Description : 作废换货策略
     * @Date : 2020/6/19
     **/
    public ValueHolder voidOrder(QuerySession session) {
        DefaultWebEvent event = session.getEvent();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(
                event.getParameterValue("param"), "yyyy-MM-dd HH:mm:ss", SerializerFeature.WriteMapNullValue),
                Feature.OrderedField);
        log.debug(LogUtil.format("请求JSON: ") + param.toString());

        //生成作废Json数组
        JSONArray auditArray = StBeanUtils.makeAuditJsonArray(param);
        HashMap<Long, Object> errorMap = new HashMap<Long, Object>();
        ValueHolder valueHolder = new ValueHolder();

        for (int i = 0; i < auditArray.size(); i++) {
            Long id = Long.valueOf(auditArray.get(i).toString());
            try {
                //作废验证
                voidCheck(id);
                StCExchangeStrategyOrderDO stCOrderDO = new StCExchangeStrategyOrderDO();
                stCOrderDO.setId(id);
                stCOrderDO.setIsactive("N");//作废
                makeVoidField(stCOrderDO, session.getUser());
                //更新
                int count = stCExchangeStrategyOrderMapper.updateById(stCOrderDO);
                if (count < 0) {
                    throw new Exception();
                }
                StCExchangeStrategyOrderDO stCOrder = stCExchangeStrategyOrderMapper.selectById(id);
                if (stCOrder != null) {
                    RedisCacheUtil.delete(stCOrder.getCpCShopId(), RedisConstant.SHOP_EXCHANGE_STRATEGY);
                }
            } catch (Exception e) {
                errorMap.put(id, e.getMessage());
            }
        }
        //失败成功信息
        StBeanUtils.getExcuteValueHolder(auditArray.size(),errorMap);
        return valueHolder;
    }

    /**
     * @return com.alibaba.fastjson.JSONObject
     * <AUTHOR>
     * @Description 作废验证
     * @Date 2019/3/12
     * @Param [objid]
     **/
    private JSONObject voidCheck(Long objid) throws Exception {

        //记录不存在
        StCExchangeStrategyOrderDO stCOrderDO = stCExchangeStrategyOrderMapper.selectById(objid);
        if (stCOrderDO == null) {
            JSONObject errJo = new JSONObject();
            errJo.put("objid", objid);
            errJo.put("message", "当前记录已不存在！");
            log.debug(LogUtil.format("当前记录已不存在！") + objid);
            throw new Exception("当前记录已不存在！");

        }

        //已作废
        StCExchangeStrategyOrderDO stCOrderDO1 = stCExchangeStrategyOrderMapper.selectByIdAndIsactive(objid, "N");
        if (stCOrderDO1 != null) {
            JSONObject errJo = new JSONObject();
            errJo.put("objid", objid);
            errJo.put("message", "当前记录已作废，不允许重复作废！");
            log.debug(LogUtil.format("当前记录已作废，不允许重复作废！") + objid);
            throw new Exception("当前记录已作废，不允许重复作废！");
        }
        return null;
    }

    /**
     * @return void
     * <AUTHOR>
     * @Description 作废人bean组装
     * @Date 2019/3/12
     * @Param [stCMergeOrderDO, user]
     **/
    public static void makeVoidField(StCExchangeStrategyOrderDO stCOrderDO, User user) {
        stCOrderDO.setDelid(Long.valueOf(user.getId()));//作废人ID
        stCOrderDO.setDelname(user.getName());
        stCOrderDO.setDelename(user.getEname());//作废人账号
        stCOrderDO.setDelTime(new Date());
        stCOrderDO.setModifierid(Long.valueOf(user.getId()));//修改人Id
        stCOrderDO.setModifiername(user.getName());
        stCOrderDO.setModifierename(user.getEname());
        stCOrderDO.setModifieddate(new Date());
    }
}
