package com.jackrain.nea.st.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.google.common.base.Throwables;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.redis.util.RedisOpsUtil;
import com.jackrain.nea.st.common.StConstant;
import com.jackrain.nea.st.mapper.StCOrderLabelLogMapper;
import com.jackrain.nea.st.mapper.StCOrderLabelMapper;
import com.jackrain.nea.st.mapper.StCOrderLabelShopItemMapper;
import com.jackrain.nea.st.model.table.StCOrderLabelDO;
import com.jackrain.nea.st.model.table.StCOrderLabelLogDO;
import com.jackrain.nea.st.model.table.StCOrderLabelShopItemDO;
import com.jackrain.nea.st.utils.RedisConstant;
import com.jackrain.nea.st.utils.StBeanUtils;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @ClassName : StCOrderLabelVoidService  
 * @Description : 订单打标作废
 * <AUTHOR>  YCH
 * @Date: 2021-11-23 15:39  
 */
@Component
@Slf4j
public class StCOrderLabelVoidService extends CommandAdapter {

    @Autowired
    private StCOrderLabelMapper stCOrderLabelMapper;

    @Autowired
    private StCOrderLabelLogMapper stCOrderLabelLogMapper;
    @Autowired
    private StCOrderLabelShopItemMapper shopItemMapper;
    @Autowired
    private RedisOpsUtil<String, String> redisUtil;

    @Override
    public ValueHolder execute(QuerySession session) throws NDSException {
        DefaultWebEvent event = session.getEvent();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(
                        event.getParameterValue("param"), "yyyy-MM-dd HH:mm:ss", SerializerFeature.WriteMapNullValue),
                Feature.OrderedField);

        log.debug(LogUtil.format("request:{}", "StCOrderLabelVoidService.execute"), JSONObject.toJSONString(param));

        HashMap<Long, Object> errMap = new HashMap<>();
        // 生成Json数组
        JSONArray voidArray = StBeanUtils.makeVoidJsonArray(param);
        // 列表批量
        if (!CollectionUtils.isEmpty(voidArray)) {
            for (int i = 0; i < voidArray.size(); i++) {
                // 遍历 jsonarray 数组，把每一个对象转成 json 对象
                Long id = voidArray.getLong(i);
                try {
                    StCOrderLabelDO orderLabelDO = stCOrderLabelMapper.selectById(id);
                    voidStCHoldOrder(orderLabelDO, session);
                    saveLog(id, session);
                    List<StCOrderLabelShopItemDO> oldShopItemDOList = shopItemMapper.selectStCOrderLabelShopItemList(id);
                    if (org.apache.commons.collections.CollectionUtils.isNotEmpty(oldShopItemDOList)) {
                        List<Long> shopIds = oldShopItemDOList.stream().distinct().map(StCOrderLabelShopItemDO::getCpCShopId).collect(Collectors.toList());
                        for (Long shopId : shopIds) {
                            redisUtil.strRedisTemplate.delete(RedisConstant.ST_ORDER_LABEL + shopId);
                        }
                    }
                } catch (Exception ex) {
                    log.error("error:{}", Throwables.getStackTraceAsString(ex));
                    errMap.put(id, ex.getMessage());
                }
            }
        }
        //返回日志
        return StBeanUtils.getExcuteValueHolder(voidArray.size(), errMap);
    }

    private void voidStCHoldOrder(StCOrderLabelDO orderLabelDO, QuerySession session) {
        //验证
        checkEstatus(orderLabelDO);
        //作废
        StCOrderLabelDO stCOrderLabelDO = new StCOrderLabelDO();
        stCOrderLabelDO.setId(orderLabelDO.getId());
        stCOrderLabelDO.setStatus(StConstant.HOLD_ORDER_STATUS_03);
        stCOrderLabelDO.setIsactive("N");
        StBeanUtils.makeModifierField(stCOrderLabelDO, session.getUser());
        orderLabelDO.setVoidId(session.getUser().getId().longValue());
        orderLabelDO.setVoidDate(new Date());
        int update = stCOrderLabelMapper.updateById(stCOrderLabelDO);
        if (update < 0) {
            throw new NDSException("作废失败！");
        }
    }

    private void saveLog(Long id, QuerySession querySession) {
        StCOrderLabelLogDO stCOrderLabelLogDO = new StCOrderLabelLogDO();
        stCOrderLabelLogDO.setId(ModelUtil.getSequence(StConstant.TAB_ST_C_ORDER_LABEL_LOG));
        stCOrderLabelLogDO.setStCOrderLabelId(id);
        stCOrderLabelLogDO.setModcontent("作废");
        stCOrderLabelLogDO.setBmod("待审核");
        stCOrderLabelLogDO.setAmod("已作废");
        StBeanUtils.makeCreateField(stCOrderLabelLogDO, querySession.getUser());
        stCOrderLabelLogMapper.insert(stCOrderLabelLogDO);
    }

    private void checkEstatus(StCOrderLabelDO orderLabelDO) {
        if (orderLabelDO == null) {
            throw new NDSException("当前记录已不存在！");
        } else {
            if (StConstant.CON_BILL_STATUS_02.equals(orderLabelDO.getStatus())) {
                throw new NDSException("当前记录已审核，不允许作废！");
            }else if(StConstant.CON_BILL_STATUS_03.equals(orderLabelDO.getStatus())){
                throw new NDSException("当前记录已作废，不允许作废！");
            }else if(StConstant.CON_BILL_STATUS_04.equals(orderLabelDO.getStatus())){
                throw new NDSException("当前记录已结案，不允许作废！");
            }
        }
    }
}
