package com.jackrain.nea.st.services;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.st.mapper.*;
import com.jackrain.nea.st.model.table.*;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.utility.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import java.util.List;

/**
 * @description: 订单自动审核策略-标识审核等待时间列表服务
 * @author: zhuxiaofan
 * @date: 2020-06-12
 */
@Component
@Slf4j
public class AuditMarkQueryService extends CommandAdapter {

    @Autowired
    private StCAutocheckAuditMarkMapper stCAutocheckAuditMarkMapper;

    /**
     * 根据订单审核策略ID获取标识审核等待时间列表
     * @param sTAutocheckId 自动审核策略id
     * @return List<StCAutocheckAuditMarkDO>
     */
    public List<StCAutocheckAuditMarkDO> selectCStAutditMarkItemInfo(Long sTAutocheckId) {
        if(log.isDebugEnabled()){
            log.debug(LogUtil.format("AuditMarkQueryService.selectCStAutditMarkItemInfo入参：sTAutocheckId=",
                    sTAutocheckId));
        }
        List<StCAutocheckAuditMarkDO> stCAutocheckAuditMarkDOS = stCAutocheckAuditMarkMapper.selectCStAutditMarkItemInfo(sTAutocheckId);
        if(log.isDebugEnabled()) {
            log.debug(LogUtil.format("AuditMarkQueryService.selectCStAutditMarkItemInfo返回：{}"),
                    JSONObject.toJSONString(stCAutocheckAuditMarkDOS));
        }
        return stCAutocheckAuditMarkDOS;
    }
}
