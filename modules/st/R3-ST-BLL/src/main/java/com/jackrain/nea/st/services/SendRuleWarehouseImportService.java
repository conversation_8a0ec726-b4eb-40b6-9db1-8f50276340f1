package com.jackrain.nea.st.services;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.google.common.base.Throwables;
import com.google.common.collect.Lists;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.cpext.model.table.CpCPhyWarehouse;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.oc.oms.model.resources.OmsRedisKeyResources;
import com.jackrain.nea.redis.util.RedisOpsUtil;
import com.jackrain.nea.st.config.R3OssConfig;
import com.jackrain.nea.st.mapper.StCSendRuleAddressRankMapper;
import com.jackrain.nea.st.mapper.StCSendRuleAddressRentMapper;
import com.jackrain.nea.st.mapper.StCSendRuleWarehouseRateMapper;
import com.jackrain.nea.st.model.result.RegionTreeResult;
import com.jackrain.nea.st.model.result.SendRuleAddressRankResult;
import com.jackrain.nea.st.model.result.StErrMsgResult;
import com.jackrain.nea.st.model.result.WarehouseRankResult;
import com.jackrain.nea.st.model.table.StCSendRuleAddressRankDO;
import com.jackrain.nea.st.model.table.StCSendRuleAddressRentDO;
import com.jackrain.nea.st.model.table.StCSendRuleWarehouseRateDO;
import com.jackrain.nea.st.rpc.RpcCpService;
import com.jackrain.nea.st.utils.ExportUtil;
import com.jackrain.nea.st.utils.RedisCacheUtil;
import com.jackrain.nea.st.utils.RedisConstant;
import com.jackrain.nea.st.utils.RegionTreeUtils;
import com.jackrain.nea.st.utils.StBeanUtils;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @author:huang.zaizai
 * @since: 2019/9/5
 * @create at : 2019/9/5 14:14
 */
@Component
@Slf4j
public class SendRuleWarehouseImportService extends CommandAdapter {

    //导入excel,业务校验错误提示返回 错误excel地址
    public static final int IMPORT_ERROR_CODE = 10001;

    @Autowired
    private ExportUtil exportUtil;
    @Autowired
    private RegionTreeQueryService regionTreeQueryService;
    @Autowired
    private R3OssConfig r3OssConfig;
    @Autowired
    private StCSendRuleAddressRentMapper rentMapper;
    @Autowired
    private StCSendRuleAddressRankMapper rankMapper;
    @Autowired
    private StCSendRuleWarehouseRateMapper rateMapper;
    @Autowired
    private RpcCpService rpcCpService;
    @Autowired
    private RedisOpsUtil redisUtil;

    /**
     * @Description 模板下载
     * @param
     * @return com.jackrain.nea.sys.domain.ValueHolderV14
     */
    public ValueHolderV14 downloadRankTemp(JSONObject obj, User user){
        ValueHolderV14 vh = new ValueHolderV14(ResultCode.SUCCESS, "仓库优先级明细导入模板下载成功！");
        exportUtil.setEndpoint(r3OssConfig.getEndpoint());
        exportUtil.setAccessKeyId(r3OssConfig.getAccessKeyId());
        exportUtil.setAccessKeySecret(r3OssConfig.getAccessKeySecret());
        exportUtil.setBucketName(r3OssConfig.getBucketName());
        if(StringUtils.isEmpty(r3OssConfig.getTimeout())){
            //如果获取不到apllo配置参数，设置默认过期时间为30分钟
            exportUtil.setTimeout("1800000");
        } else {
            exportUtil.setTimeout(r3OssConfig.getTimeout());
        }
        /**
         *  拼接Excel主表sheet表头字段
         * */
        String mainNames[] = {"省"};
        String mustNames[] = {"省"};
        List<String> mainList = Lists.newArrayList(mainNames);
        List<String> mustList = Lists.newArrayList(mustNames);

        Long id = obj.getLong("objid");
        //获取仓库信息
        List<StCSendRuleAddressRentDO> rentList = rentMapper.selectList(new QueryWrapper<StCSendRuleAddressRentDO>()
                .lambda().eq(StCSendRuleAddressRentDO::getStCSendRuleId, id));
        if (CollectionUtils.isNotEmpty(rentList)) {
            rentList = rentList.stream().sorted(Comparator.comparing(StCSendRuleAddressRentDO::getId))
                    .collect(Collectors.toList());
        }
        for (StCSendRuleAddressRentDO rent : rentList) {
            mainList.add(rent.getCpCPhyWarehouseEname());
        }
        //生成Excel
        XSSFWorkbook hssfWorkbook = new XSSFWorkbook();
        exportUtil.executeSheet(hssfWorkbook, "仓库优先级明细数据", "", mainList, mustList,
                Lists.newArrayList(), Lists.newArrayList(), false);
        String putMsg = exportUtil.saveFileAndPutOss(hssfWorkbook, "仓库优先级明细导入模板",
                user, "OSS-Bucket/EXPORT/StCSendRuleAddressRank/");
        vh.setData(putMsg);
        return vh;
    }

    /**
     * @Description 导入
     * @param rankResultList
     * @param user
     * @return com.jackrain.nea.sys.domain.ValueHolderV14
     */
    public ValueHolderV14 importSendRuleWarehouseRank(Long objid, List<SendRuleAddressRankResult> rankResultList, User user){
        ValueHolderV14 vh = new ValueHolderV14();
        if(CollectionUtils.isEmpty(rankResultList)){
            vh.setCode(ResultCode.FAIL);
            vh.setMessage("导入失败,上传文件未录入数据!");
            return vh;
        }
        //保存物流明细
        List<String> errMsgList = saveSendRuleWarehouseRank(objid, rankResultList, user);
        //3.若有错误信息支持导出
        if(errMsgList.size() > 0){
            List<StErrMsgResult> errExcelList = Lists.newArrayList();
            errMsgList.forEach(errMsg->{
                StErrMsgResult errMsgResult = new StErrMsgResult();
                errMsgResult.setErrMsg(errMsg);
                errExcelList.add(errMsgResult);
            });
            int successNum = rankResultList.size() - errMsgList.size();
            vh.setCode(IMPORT_ERROR_CODE);
            vh.setMessage(String.format("导入成功%d条,失败%d条",successNum,errMsgList.size()));
            String sdd = downloadRankErrMsg(user, errExcelList);
            vh.setData(sdd);
        }else{
            vh.setCode(ResultCode.SUCCESS);
            vh.setMessage("导入成功");
        }
        return vh;
    }

    /**
     * 下载错误信息
     * @param user
     * @param errExcelList
     * @return 错误信息
     */
    private String downloadRankErrMsg(User user, List<StErrMsgResult> errExcelList) {
        String columnNames[] = {"错误原因"};
        List<String> columnList = Lists.newArrayList(columnNames);
        String keys[] = {"errMsg"};
        List<String> keyList = Lists.newArrayList(keys);
        exportUtil.setEndpoint(r3OssConfig.getEndpoint());
        exportUtil.setAccessKeyId(r3OssConfig.getAccessKeyId());
        exportUtil.setAccessKeySecret(r3OssConfig.getAccessKeySecret());
        exportUtil.setBucketName(r3OssConfig.getBucketName());
        if(StringUtils.isEmpty(r3OssConfig.getTimeout())){
            //如果获取不到apllo配置参数，设置默认过期时间为30分钟
            exportUtil.setTimeout("1800000");
        } else {
            exportUtil.setTimeout(r3OssConfig.getTimeout());
        }
        Workbook hssfWorkbook = exportUtil.execute("仓库优先级明细", "仓库优先级明细",
                columnList, keyList, errExcelList);
        return exportUtil.saveFileAndPutOss(hssfWorkbook, "仓库优先级明细导入错误信息",
                user,"OSS-Bucket/EXPORT/StCSendRuleAddressRank/");
    }

    /**
     * 保存仓库优先级信息
     * @param objid
     * @param rankResultList
     * @return 错误信息
     */
    private List<String> saveSendRuleWarehouseRank(Long objid, List<SendRuleAddressRankResult> rankResultList, User user) {
        List<String> errMsgList = Lists.newArrayList();
        List<StCSendRuleAddressRankDO> rankList = Lists.newArrayList();
        List<RegionTreeResult> treeList = Lists.newArrayList();
        //查询地址
        JSONObject obj = new JSONObject();
        obj.put("regiontype", "PROV");
        ValueHolderV14<List<RegionTreeResult>> treeVh = regionTreeQueryService.queryRegionTree(obj);
        if (treeVh.isOK()) {
            treeList = treeVh.getData();
        }
        //1.组装集合
        //获取仓库信息
        List<StCSendRuleAddressRentDO> rentList = rentMapper.selectList(new QueryWrapper<StCSendRuleAddressRentDO>()
                .lambda().eq(StCSendRuleAddressRentDO::getStCSendRuleId, objid));
        if (CollectionUtils.isNotEmpty(rentList)) {
            rentList = rentList.stream().sorted(Comparator.comparing(StCSendRuleAddressRentDO::getId))
                    .collect(Collectors.toList());
        }
        for(SendRuleAddressRankResult rankResult : rankResultList) {
            try{
                //补足冗余信息
                StCSendRuleAddressRankDO rank = getImportSendRuleAddressRank(rankResult, treeList, rentList);
                rank.setStCSendRuleId(objid);
                rankList.add(rank);
            }catch(Exception e){
                String area = rankResult.getCpCRegionProvinceEname();
                errMsgList.add(area + e.getMessage());
            }
        }

        Map<String, StCSendRuleAddressRankDO> rankMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(rankList)) {
            List<StCSendRuleAddressRankDO> rankOldList = rankMapper.selectList(new QueryWrapper<StCSendRuleAddressRankDO>()
                    .lambda().eq(StCSendRuleAddressRankDO::getStCSendRuleId, objid));
            for (StCSendRuleAddressRankDO rankOld : rankOldList) {
                String key = rankOld.getCpCRegionProvinceEname();
                rankMap.put(key, rankOld);
            }
        }
        //2.保存信息
        for(StCSendRuleAddressRankDO rank : rankList){
            try{
                //存在更新 不存在新增
                String key = rank.getCpCRegionProvinceEname();
                if (rankMap.containsKey(key)) {
                    // todo 待删除
                    //清除rediskey
                    String redisKey = RedisConstant.bulidLockStCSendRuleAddressRankKey(objid, rank.getCpCRegionProvinceId());
                    if(redisUtil.objRedisTemplate.hasKey(redisKey)){
                        redisUtil.objRedisTemplate.delete(redisKey);
                    }
                    rank.setId(rankMap.get(key).getId());
                    StBeanUtils.makeModifierField(rank, user);
                    if (rankMapper.updateById(rank) < 1) {
                        throw new NDSException(key + "仓库优先级明细更新失败");
                    }
                } else {
                    rank.setId(ModelUtil.getSequence("ST_C_SEND_RULE_ADDRESS_RANK"));
                    StBeanUtils.makeCreateField(rank, user);
                    if (rankMapper.insert(rank) < 1) {
                        throw new NDSException(key + "仓库优先级明细插入失败");
                    }
                }
                String redisKey2 = OmsRedisKeyResources.buildLockStCSendRuleAddressRankKey(rank.getStCSendRuleId(),
                        rank.getCpCRegionProvinceId());
                RedisCacheUtil.deleteAll(redisKey2);
            }catch(Exception e){
                errMsgList.add(e.getMessage());
            }
        }
        return errMsgList;
    }

    /**
     * 补足冗余信息
     * @param rankResult
     * @param treeList
     * @param rentList
     */
    private StCSendRuleAddressRankDO getImportSendRuleAddressRank(SendRuleAddressRankResult rankResult,
                                                                      List<RegionTreeResult> treeList,
                                                                      List<StCSendRuleAddressRentDO> rentList) {
        StCSendRuleAddressRankDO rank = new StCSendRuleAddressRankDO();
        //省设值
        if (!StringUtils.isBlank(rankResult.getCpCRegionProvinceEname())) {
            RegionTreeResult treeResult = RegionTreeUtils.getTreeResultByName(treeList, rankResult.getCpCRegionProvinceEname(), 1L);
            if(treeResult == null){
                throw new NDSException(String.format("不存在名称为[%s]的省", rankResult.getCpCRegionProvinceEname()));
            } else {
                rank.setCpCRegionProvinceId(treeResult.getId());
                rank.setCpCRegionProvinceEcode(treeResult.getEcode());
                rank.setCpCRegionProvinceEname(treeResult.getTitle());
            }
        } else {
            throw new NDSException("省不能为空");
        }

        List<WarehouseRankResult> warehouseRankNewList = Lists.newArrayList();
        List<String> rankAllList = Lists.newArrayList();
        int i = 1;
        for (StCSendRuleAddressRentDO rent : rentList) {
            WarehouseRankResult warehouseRankNew = new WarehouseRankResult();
            warehouseRankNew.setWarehouseId(rent.getCpCPhyWarehouseId());
            try {
                Method method = SendRuleAddressRankResult.class.getMethod("getRank" + i);
                warehouseRankNew.setRank((String) method.invoke(rankResult));
            } catch (Exception ex) {
                warehouseRankNew.setRank("");
                log.debug(LogUtil.format("仓库优先级获取失败：{}"), Throwables.getStackTraceAsString(ex));
            }
            i++;
            if (warehouseRankNew.getRank() != null && !"".equals(warehouseRankNew.getRank())) {
                if (rankAllList.contains(warehouseRankNew.getRank())) {
                    throw new NDSException("仓库优先级重复！");
                } else {
                    rankAllList.add(warehouseRankNew.getRank());
                }
                warehouseRankNewList.add(warehouseRankNew);
            }
        }
        rank.setWarehouseRank(JSONArray.toJSONString(warehouseRankNewList));
        return rank;
    }

    /**
     * @Description 模板下载
     * @param
     * @return com.jackrain.nea.sys.domain.ValueHolderV14
     */
    public ValueHolderV14 downloadRateTemp(JSONObject obj, User user){
        ValueHolderV14 vh = new ValueHolderV14(ResultCode.SUCCESS, "仓库比例明细导入模板下载成功！");
        exportUtil.setEndpoint(r3OssConfig.getEndpoint());
        exportUtil.setAccessKeyId(r3OssConfig.getAccessKeyId());
        exportUtil.setAccessKeySecret(r3OssConfig.getAccessKeySecret());
        exportUtil.setBucketName(r3OssConfig.getBucketName());
        if(StringUtils.isEmpty(r3OssConfig.getTimeout())){
            //如果获取不到apllo配置参数，设置默认过期时间为30分钟
            exportUtil.setTimeout("1800000");
        } else {
            exportUtil.setTimeout(r3OssConfig.getTimeout());
        }
        /**
         *  拼接Excel主表sheet表头字段
         * */
        String mainNames[] = {"仓库","仓库优先级","发货比例"};
        String mustNames[] = {"仓库","仓库优先级","发货比例"};
        List<String> mainList = Lists.newArrayList(mainNames);
        List<String> mustList = Lists.newArrayList(mustNames);

        //生成Excel
        XSSFWorkbook hssfWorkbook = new XSSFWorkbook();
        exportUtil.executeSheet(hssfWorkbook, "仓库比例明细数据", "", mainList, mustList,
                Lists.newArrayList(), Lists.newArrayList(), false);
        String putMsg = exportUtil.saveFileAndPutOss(hssfWorkbook, "仓库比例明细导入模板",
                user, "OSS-Bucket/EXPORT/StCSendRuleWarehouseRate/");
        vh.setData(putMsg);
        return vh;
    }

    /**
     * @Description 导入
     * @param rateResultList
     * @param user
     * @return com.jackrain.nea.sys.domain.ValueHolderV14
     */
    public ValueHolderV14 importSendRuleWarehouseRate(Long objid, List<StCSendRuleWarehouseRateDO> rateResultList, User user){
        ValueHolderV14 vh = new ValueHolderV14();
        if(CollectionUtils.isEmpty(rateResultList)){
            vh.setCode(ResultCode.FAIL);
            vh.setMessage("导入失败,上传文件未录入数据!");
            return vh;
        }
        //保存明细
        List<String> errMsgList = saveSendRuleWarehouseRate(objid, rateResultList, user);
        //3.若有错误信息支持导出
        if(errMsgList.size() > 0){
            List<StErrMsgResult> errExcelList = Lists.newArrayList();
            errMsgList.forEach(errMsg->{
                StErrMsgResult errMsgResult = new StErrMsgResult();
                errMsgResult.setErrMsg(errMsg);
                errExcelList.add(errMsgResult);
            });
            int successNum = rateResultList.size() - errMsgList.size();
            vh.setCode(IMPORT_ERROR_CODE);
            vh.setMessage(String.format("导入成功%d条,失败%d条",successNum,errMsgList.size()));
            String sdd = downloadRateErrMsg(user, errExcelList);
            vh.setData(sdd);
        }else{
            vh.setCode(ResultCode.SUCCESS);
            vh.setMessage("导入成功");
        }
        return vh;
    }

    /**
     * 保存仓库比例信息
     * @param objid
     * @param rateResultList
     * @return 错误信息
     */
    private List<String> saveSendRuleWarehouseRate(Long objid, List<StCSendRuleWarehouseRateDO> rateResultList, User user) {
        List<String> errMsgList = Lists.newArrayList();
        List<StCSendRuleWarehouseRateDO> rateList = Lists.newArrayList();
        //1.组装集合
        for(StCSendRuleWarehouseRateDO rateResult : rateResultList) {
            try{
                //补足冗余信息
                getImportSendRuleWarehouseRate(rateResult);
                rateResult.setStCSendRuleId(objid);
                rateList.add(rateResult);
            }catch(Exception e){
                String name = rateResult.getCpCPhyWarehouseEname();
                errMsgList.add(name + e.getMessage());
            }
        }

        Map<String, StCSendRuleWarehouseRateDO> rateMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(rateList)) {
            List<StCSendRuleWarehouseRateDO> rateOldList = rateMapper.selectList(new QueryWrapper<StCSendRuleWarehouseRateDO>()
                    .lambda().eq(StCSendRuleWarehouseRateDO::getStCSendRuleId, objid));
            for (StCSendRuleWarehouseRateDO rateOld : rateOldList) {
                String key = rateOld.getCpCPhyWarehouseEname();
                rateMap.put(key, rateOld);
            }
        }
        //2.保存信息
        for(StCSendRuleWarehouseRateDO rate : rateList){
            try{
                //存在更新 不存在新增
                String key = rate.getCpCPhyWarehouseEname();
                if (rateMap.containsKey(key)) {
                    rate.setId(rateMap.get(key).getId());
                    StBeanUtils.makeModifierField(rate, user);
                    if (rateMapper.updateById(rate) < 1) {
                        throw new NDSException(key + "仓库比例明细更新失败");
                    }
                } else {
                    rate.setId(ModelUtil.getSequence("ST_C_SEND_RULE_WAREHOUSE_RATE"));
                    StBeanUtils.makeCreateField(rate, user);
                    if (rateMapper.insert(rate) < 1) {
                        throw new NDSException(key + "仓库比例明细插入失败");
                    }
                }
            }catch(Exception e){
                errMsgList.add(e.getMessage());
            }
        }
        return errMsgList;
    }

    /**
     * 补足冗余信息
     * @param rateResult
     */
    private void getImportSendRuleWarehouseRate(StCSendRuleWarehouseRateDO rateResult) {
        //仓库设值
        if (!StringUtils.isBlank(rateResult.getCpCPhyWarehouseEname())) {
            CpCPhyWarehouse cpCPhyWarehouse = rpcCpService.getCpCPhyWahouseByName(rateResult.getCpCPhyWarehouseEname());
            if(cpCPhyWarehouse == null){
                throw new NDSException(String.format("不存在名称为[%s]的仓库", rateResult.getCpCPhyWarehouseEname()));
            } else {
                rateResult.setCpCPhyWarehouseId(cpCPhyWarehouse.getId());
                rateResult.setCpCPhyWarehouseEcode(cpCPhyWarehouse.getEcode());
            }
        } else {
            throw new NDSException("仓库不能为空");
        }
        //仓库优先级设值
        if (rateResult.getRank() == null) {
            throw new NDSException("仓库优先级不能为空");
        }
        //发货比例设值
        if (rateResult.getSendRate() == null) {
            throw new NDSException("发货比例不能为空");
        }
    }

    /**
     * 下载错误信息
     * @param user
     * @param errExcelList
     * @return 错误信息
     */
    private String downloadRateErrMsg(User user, List<StErrMsgResult> errExcelList) {
        String columnNames[] = {"错误原因"};
        List<String> columnList = Lists.newArrayList(columnNames);
        String keys[] = {"errMsg"};
        List<String> keyList = Lists.newArrayList(keys);
        exportUtil.setEndpoint(r3OssConfig.getEndpoint());
        exportUtil.setAccessKeyId(r3OssConfig.getAccessKeyId());
        exportUtil.setAccessKeySecret(r3OssConfig.getAccessKeySecret());
        exportUtil.setBucketName(r3OssConfig.getBucketName());
        if(StringUtils.isEmpty(r3OssConfig.getTimeout())){
            //如果获取不到apllo配置参数，设置默认过期时间为30分钟
            exportUtil.setTimeout("1800000");
        } else {
            exportUtil.setTimeout(r3OssConfig.getTimeout());
        }
        Workbook hssfWorkbook = exportUtil.execute("仓库比例明细", "仓库比例明细",
                columnList, keyList, errExcelList);
        return exportUtil.saveFileAndPutOss(hssfWorkbook, "仓库比例明细导入错误信息",
                user,"OSS-Bucket/EXPORT/StCSendRuleWarehouseRate/");
    }

    public static String bulidLockStCSendRuleAddressRankKey(Long sendRuleId, Long regionProvinceId) {
        return "st:StCSendRuleAddressRent:type1:byStCSendrule:" + sendRuleId + "region:" + regionProvinceId;
    }
}
