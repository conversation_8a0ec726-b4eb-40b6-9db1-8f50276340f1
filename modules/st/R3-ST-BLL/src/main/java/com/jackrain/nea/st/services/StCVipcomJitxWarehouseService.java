package com.jackrain.nea.st.services;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.google.common.base.Throwables;
import com.jackrain.nea.st.mapper.StCVipcomJitxWarehouseMapper;
import com.jackrain.nea.st.model.enums.CalculationTypeEnum;
import com.jackrain.nea.st.model.table.StCVipcomJitxWarehouse;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utility.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;


/**
 * <AUTHOR>
 * @Description 获取唯品会jitx仓库对照表
 **/
@Component
@Slf4j
public class StCVipcomJitxWarehouseService extends CommandAdapter {

    @Autowired
    private StCVipcomJitxWarehouseMapper jitxWarehouseMapper;

    /**
     * 获取开启产能控制的JITX仓库对照表
     *
     * @param shopId               店铺ID
     * @param cpCPhyWarehouseId    实体仓ID
     * @param vipcomWarehouseEcode JITX仓库编码
     * @return
     */
    public StCVipcomJitxWarehouse selectVipcomWarehouseByShopId(Long shopId, Long cpCPhyWarehouseId, String vipcomWarehouseEcode) {
        StCVipcomJitxWarehouse jitxWarehouse = null;
        QueryWrapper<StCVipcomJitxWarehouse> queryWrapper = new QueryWrapper<StCVipcomJitxWarehouse>();
        queryWrapper.eq("isactive", "Y");
        //queryWrapper.eq("is_enable_jitx_capacity", "Y");
        queryWrapper.eq("cp_c_shop_id", shopId);
        if (cpCPhyWarehouseId != null) {
            queryWrapper.eq("cp_c_phy_warehouse_id", cpCPhyWarehouseId);
        }
        if (vipcomWarehouseEcode != null) {
            queryWrapper.eq("vipcom_warehouse_ecode", vipcomWarehouseEcode);
        }
        List<StCVipcomJitxWarehouse> stVipcomProjectDOList = jitxWarehouseMapper.selectList(queryWrapper);

        if (CollectionUtils.isNotEmpty(stVipcomProjectDOList)) {
            jitxWarehouse = stVipcomProjectDOList.get(0);
        }
        return jitxWarehouse;
    }

    /**
     * 根据店铺查询唯品会仓库对照表
     *
     * @param shopId
     * @return
     */
    public List<StCVipcomJitxWarehouse> selectVipWarehouseListByShopId(Long shopId) {
        QueryWrapper<StCVipcomJitxWarehouse> queryWrapper = new QueryWrapper<StCVipcomJitxWarehouse>();
        queryWrapper.eq("isactive", "Y");
        if (shopId != null) {
            queryWrapper.eq("cp_c_shop_id", shopId);
        }
        return jitxWarehouseMapper.selectList(queryWrapper);
    }

    public List<StCVipcomJitxWarehouse> selectByShopIdAndVipcomUnshopWarehouseEcode(Long shopId, String vipcomUnshopWarehouseEcode) {
        QueryWrapper<StCVipcomJitxWarehouse> queryWrapper = new QueryWrapper<StCVipcomJitxWarehouse>();
        queryWrapper.eq("isactive", "Y");
        if (shopId != null) {
            queryWrapper.eq("cp_c_shop_id", shopId);
        }
        if (vipcomUnshopWarehouseEcode != null) {
            queryWrapper.eq("vipcom_unshop_warehouse_ecode", vipcomUnshopWarehouseEcode);
        }
        return jitxWarehouseMapper.selectList(queryWrapper);
    }

    public StCVipcomJitxWarehouse selectVipcomWarehouse(StCVipcomJitxWarehouse stCVipcomJitxWarehouse) {
        StCVipcomJitxWarehouse jitxWarehouse = null;
        LambdaQueryWrapper<StCVipcomJitxWarehouse> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(StCVipcomJitxWarehouse::getIsactive, "Y");
        //queryWrapper.eq("is_enable_jitx_capacity", "Y");
        queryWrapper.eq(StCVipcomJitxWarehouse::getCpCShopId, stCVipcomJitxWarehouse.getCpCShopId());
        if (stCVipcomJitxWarehouse.getCpCPhyWarehouseId() != null) {
            queryWrapper.eq(StCVipcomJitxWarehouse::getCpCPhyWarehouseId, stCVipcomJitxWarehouse.getCpCPhyWarehouseId());
        }
        if (StringUtils.isNotEmpty(stCVipcomJitxWarehouse.getVipcomWarehouseEcode())) {
            queryWrapper.eq(StCVipcomJitxWarehouse::getVipcomWarehouseEcode, stCVipcomJitxWarehouse.getVipcomWarehouseEcode());
        }
        if (StringUtils.isNotEmpty(stCVipcomJitxWarehouse.getVipcomUnshopWarehouseEcode())) {
            queryWrapper.eq(StCVipcomJitxWarehouse::getVipcomUnshopWarehouseEcode, stCVipcomJitxWarehouse.getVipcomUnshopWarehouseEcode());
        }
        List<StCVipcomJitxWarehouse> stVipcomProjectDOList = jitxWarehouseMapper.selectList(queryWrapper);

        if (CollectionUtils.isNotEmpty(stVipcomProjectDOList)) {
            jitxWarehouse = stVipcomProjectDOList.get(0);
        }
        return jitxWarehouse;
    }


    /**
     * 重置JITX产能
     *
     * @return
     */
    public ValueHolder resetOrderQty() {
        ValueHolder valueHolder = new ValueHolder();
        HashMap<String, Object> map = new HashMap<>();
        try {
            int flag = jitxWarehouseMapper.batchResetOrderQty();
            map.put("code", 0);
            map.put("message", "");
            valueHolder.setData(map);
        } catch (Exception e) {
            log.error(LogUtil.format("重置唯品会JITX产品异常{}"), Throwables.getStackTraceAsString(e));
            map.put("code", -1);
            map.put("message", "重置唯品会JITX产品异常！");
            valueHolder.setData(map);
        }
        return valueHolder;
    }


    /**
     * 更新JITX产能
     *
     * @param jitxWarehouse
     * @param calculationType
     * @return
     */
    public ValueHolder updateJitxCapacity(StCVipcomJitxWarehouse jitxWarehouse, Integer calculationType) {
        ValueHolder valueHolder = new ValueHolder();
        HashMap<String, Object> map = new HashMap<>();
        BigDecimal jitxCapacity = jitxWarehouse.getQtyJitxOrderToday() == null ? new BigDecimal("0") : jitxWarehouse.getQtyJitxOrderToday();
        log.debug(LogUtil.format("JITX产能数据-jitxCapacity:{},qtyJitxOrderToday:{}"), jitxWarehouse.getJitxCapacity(),
                jitxCapacity);
        if (CalculationTypeEnum.INCREMENT.getKey().equals(calculationType)) {
            if (jitxWarehouse.getJitxCapacity().compareTo(jitxCapacity) > 0) {
                jitxCapacity = jitxCapacity.add(new BigDecimal("1"));
                jitxWarehouse.setQtyJitxOrderToday(jitxCapacity);
                jitxWarehouseMapper.updateById(jitxWarehouse);
                map.put("code", 0);
                map.put("message", "");
                valueHolder.setData(map);
            } else {
                log.error(LogUtil.format("无符合的唯品会jitx仓库！"));
                map.put("code", -1);
                map.put("message", "无符合的唯品会jitx仓库！");
                valueHolder.setData(map);
            }
        } else {
            if (jitxCapacity.intValue() > 0) {
                jitxCapacity = jitxCapacity.subtract(new BigDecimal("1"));
                jitxWarehouse.setQtyJitxOrderToday(jitxCapacity);
                jitxWarehouseMapper.updateById(jitxWarehouse);
                map.put("code", 0);
                map.put("message", "");
                valueHolder.setData(map);
            } else {
                log.error(LogUtil.format("无符合的唯品会jitx仓库！"));
                map.put("code", -1);
                map.put("message", "无符合的唯品会jitx仓库！");
                valueHolder.setData(map);
            }
        }
        return valueHolder;
    }
}
