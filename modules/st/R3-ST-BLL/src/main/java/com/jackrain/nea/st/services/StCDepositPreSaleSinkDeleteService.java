package com.jackrain.nea.st.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.st.common.StConstant;
import com.jackrain.nea.st.mapper.StCDepositPreSaleSinkItemMapper;
import com.jackrain.nea.st.mapper.StCDepositPreSaleSinkLogisticsMapper;
import com.jackrain.nea.st.mapper.StCDepositPreSaleSinkMapper;
import com.jackrain.nea.st.model.table.StCDepositPreSaleSinkDO;
import com.jackrain.nea.st.utils.StBeanUtils;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.Objects;

/**
 * description:定金预售预下沉策略审核
 * @Author:  liuwenjin
 * @Date 2021/9/23 5:26 下午
 */
@Component
@Slf4j
public class StCDepositPreSaleSinkDeleteService extends CommandAdapter {

    @Autowired
    private StCDepositPreSaleSinkMapper stCDepositPreSaleSinkMapper;

    @Autowired
    private StCDepositPreSaleSinkItemMapper stCDepositPreSaleSinkItemMapper;

    @Autowired
    private StCDepositPreSaleSinkLogisticsMapper stCDepositPreSaleSinkLogisticsMapper;


    @Override
    public ValueHolder execute(QuerySession session){
        DefaultWebEvent event = session.getEvent();
        JSONObject param = JSON.parseObject(
                JSON.toJSONStringWithDateFormat(event.getParameterValue("param"), "yyyy-MM-dd HH:mm:ss"),
                Feature.OrderedField);

        boolean delMainFlag = param.getBoolean("isdelmtable");//是否删除主表
        Long id = param.getLong("objid");

        if (delMainFlag) {
            // 删除主表
            if (Objects.nonNull(id) && id > 0) {
                //主表删除写在这里
            }
        } else {
            // 删除明细
            JSONObject tabitem = param.getJSONObject("tabitem");
            if (!CollectionUtils.isEmpty(tabitem)) {
                JSONArray itemArray = tabitem.getJSONArray(StConstant.TAB_ST_C_DEPOSIT_PRE_SALE_SINK_ITEM);
                JSONArray errorArray = new JSONArray();
                if (!CollectionUtils.isEmpty(itemArray)) {
                    deleteItemByID(itemArray, errorArray);
                }
                JSONArray logisticsItemArray = tabitem.getJSONArray(StConstant.TAB_ST_C_DEPOSIT_PRE_SALE_SINK_LOGISTICS);
                if (!CollectionUtils.isEmpty(logisticsItemArray)) {
                    deletelogisticsItemByID(logisticsItemArray, errorArray);
                }
                //修改主表信息
                updateHoldOrder(id,session);
                return StBeanUtils.getExcuteValueHolder(errorArray);
            }
        }

        throw new NDSException("当前记录已不存在！");
    }
    /**
     * description:删除仓库明细
     * @Author:  liuwenjin
     * @Date 2021/10/18 4:41 下午
     */
    private void deletelogisticsItemByID(JSONArray logisticsItemArray, JSONArray errorArray) {
        for (int i = 0; i < logisticsItemArray.size(); i++) {
            Long itemid = logisticsItemArray.getLong(i);
            if ((stCDepositPreSaleSinkLogisticsMapper.deleteById(itemid)) <= 0) {
                errorArray.add(StBeanUtils.getJsonObjectInfo(itemid, "删除明细失败"));
            }
        }
    }

    /**
     * description:修改主表信息
     * @Author:  liuwenjin
     * @Date 2021/9/23 8:24 下午
     */
    private void updateHoldOrder(Long id, QuerySession session) {
        StCDepositPreSaleSinkDO stCDepositPreSaleSinkDO =new StCDepositPreSaleSinkDO();
        stCDepositPreSaleSinkDO.setId(id);
        StBeanUtils.makeModifierField(stCDepositPreSaleSinkDO,session.getUser());
        int n = stCDepositPreSaleSinkMapper.updateById(stCDepositPreSaleSinkDO);
        if (n<=0){
            throw new NDSException("修改主表的信息失败！");
        }
    }
    /**
     * description:删除明细
     * @Author:  liuwenjin
     * @Date 2021/9/23 8:24 下午
     */
    private void deleteItemByID(JSONArray itemArray, JSONArray errorArray) {
        for (int i = 0; i < itemArray.size(); i++) {
            Long itemid = itemArray.getLong(i);
            if ((stCDepositPreSaleSinkItemMapper.deleteById(itemid)) <= 0) {
                errorArray.add(StBeanUtils.getJsonObjectInfo(itemid, "删除明细失败"));
            }
        }
    }
}
