package com.jackrain.nea.st.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.st.common.StConstant;
import com.jackrain.nea.st.mapper.StCScalpingLogisticsMapper;
import com.jackrain.nea.st.mapper.StCScalpingMapper;
import com.jackrain.nea.st.mapper.StCScalpingReplaceProMapper;
import com.jackrain.nea.st.model.table.StCScalpingDO;
import com.jackrain.nea.st.model.table.StCScalpingLogisticsDO;
import com.jackrain.nea.st.model.table.StCScalpingReplaceProDO;
import com.jackrain.nea.st.utils.StBeanUtils;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * @Descroption 刷单方案-审核逻辑
 * <AUTHOR>
 * @Date 2019/3/8
 */
@Component
@Slf4j
@Transactional
public class ScalpingAuditService extends CommandAdapter {
    @Autowired
    private StCScalpingMapper stCMainMapper;
    @Autowired
    private StCScalpingLogisticsMapper stCItemMapperA;
    @Autowired
    private StCScalpingReplaceProMapper stCItemMapperB;

    @Override
    public ValueHolder execute(QuerySession querySession) throws NDSException {
        DefaultWebEvent event = querySession.getEvent();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(
                event.getParameterValue("param"), "yyyy-MM-dd HH:mm:ss"),
                Feature.OrderedField);

        JSONArray itemArray = StBeanUtils.makeAuditJsonArray(param);
        JSONArray errorArray = new JSONArray();
        if (itemArray.size() > 0) {
            for (int i = 0; i < itemArray.size(); i++) {
                Long itemid = itemArray.getLong(i);
                auditByID(querySession, itemid, errorArray);
            }
        }
        return StBeanUtils.getProcessValueHolder(itemArray, errorArray, StConstant.CON_BILL_ACTION_AUDIT);
    }

    private void auditByID(QuerySession session, Long id, JSONArray errorArray) {
        StCScalpingDO stcMainDo = stCMainMapper.selectById(id);
        JSONArray tempArray = new JSONArray();
        checkAudit(stcMainDo, id, tempArray);
        if (tempArray.size() > 0) {
            errorArray.addAll(tempArray);
            return;
        }

        StBeanUtils.makeModifierField(stcMainDo, session.getUser());//修改信息
        stcMainDo.setModifierename(session.getUser().getEname());//修改人账号
        stcMainDo.setEstatus(StConstant.CON_BILL_STATUS_02);
        stcMainDo.setCheckid(Long.valueOf(session.getUser().getId()));//审核人
        stcMainDo.setChecktime(new Date());//审核时间
        stcMainDo.setCheckname(session.getUser().getName());//审核人姓名
        stcMainDo.setCheckename(session.getUser().getEname());//审核人账号
        if ((stCMainMapper.updateById(stcMainDo)) <= 0) {
            errorArray.add(StBeanUtils.getJsonObjectInfo(id, "店铺:" + stcMainDo.getCpCShopTitle() + ",方案审核失败！"));
        }
    }

    private void checkAudit(StCScalpingDO stCCheckDO, Long id, JSONArray errorArray) {
        if (stCCheckDO == null) {
            errorArray.add(StBeanUtils.getJsonObjectInfo(id, "当前记录已不存在！"));
            return;
        }
        //不是未审核，不允许审核
        if (stCCheckDO.getEstatus() == null
                || !StConstant.CON_BILL_STATUS_01.equals(stCCheckDO.getEstatus())) {
            errorArray.add(StBeanUtils.getJsonObjectInfo(id, "当前记录不是未审核，不允许审核！"));
            return;
        }
        if (stCCheckDO.getBeginTime() == null) {
            errorArray.add(StBeanUtils.getJsonObjectInfo(id, "方案的开始日期为空，不允许审核！"));
            return;
        }
        if (stCCheckDO.getBeginTime().before(new Date())) {
            errorArray.add(StBeanUtils.getJsonObjectInfo(id, "方案的开始日期小于当前日期，不允许审核！"));
            return;
        }
        if (stCCheckDO.getEndTime() == null) {
            errorArray.add(StBeanUtils.getJsonObjectInfo(id, "方案的结束日期为空，不允许审核！"));
            return;
        }
        if (stCCheckDO.getEndTime().before(stCCheckDO.getBeginTime())) {
            errorArray.add(StBeanUtils.getJsonObjectInfo(id, "方案的结束日期小于开始日期，不允许审核！"));
            return;
        }
        Integer iEtype = stCCheckDO.getEtype();
        if (iEtype == 1) {
            //刷单自动出库快递公司设置
            List<StCScalpingLogisticsDO> stCItemA = stCItemMapperA.listByMainid(id);
            if (stCItemA.isEmpty() || stCItemA.size() <= 0) {
                errorArray.add(StBeanUtils.getJsonObjectInfo(id, "当前方案没有设置快递公司明细，不允许审核！"));
                return;
            }
        } else if (iEtype == 2) {
            //替换商品出库，需要设置商品明细
            List<StCScalpingReplaceProDO> stCItemB = stCItemMapperB.listByMainid(id);
            if (stCItemB.isEmpty() || stCItemB.size() <= 0) {
                errorArray.add(StBeanUtils.getJsonObjectInfo(id, "当前方案没有设置替换商品明细，不允许审核！"));
                return;
            }
        }
    }

}
