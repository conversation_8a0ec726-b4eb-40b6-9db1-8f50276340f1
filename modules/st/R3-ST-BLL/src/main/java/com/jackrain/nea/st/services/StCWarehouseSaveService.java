package com.jackrain.nea.st.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.google.common.base.Throwables;
import com.jackrain.nea.cpext.model.table.CpCPhyWarehouse;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.ps.api.result.PsCProSkuResult;
import com.jackrain.nea.ps.api.table.PsCBrand;
import com.jackrain.nea.psext.model.table.PsCPro;
import com.jackrain.nea.psext.model.table.PsCSku;
import com.jackrain.nea.st.common.StConstant;
import com.jackrain.nea.st.mapper.*;
import com.jackrain.nea.st.model.request.StCWarehouseRequest;
import com.jackrain.nea.st.model.result.StCWarehouseQueryResult;
import com.jackrain.nea.st.model.table.*;
import com.jackrain.nea.st.rpc.RpcCpService;
import com.jackrain.nea.st.rpc.RpcPsService;
import com.jackrain.nea.st.utils.JsonUtils;
import com.jackrain.nea.st.utils.RedisCacheUtil;
import com.jackrain.nea.st.utils.RedisConstant;
import com.jackrain.nea.st.utils.StBeanUtils;
import com.jackrain.nea.st.utils.ValueHolderUtils;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description 仓库拆单策略
 * @Date 2020/06/10
 * @Copyright 2019-2020
 */
@Component
@Slf4j
@Transactional
public class StCWarehouseSaveService extends CommandAdapter {

    @Autowired
    private StCWarehouseMapper stCWarehouseMapper;

    @Autowired
    private StCWarehouseSkuMapper stCWarehouseSkuMapper;

    @Autowired
    private StCWarehouseGoodsMapper stCWarehouseGoodsMapper;

    @Autowired
    private StCWarehouseBrandMapper stCWarehouseBrandMapper;

    @Autowired
    private StCWarehouseGoodsClassMapper stCWarehouseGoodsClassMapper;

    @Autowired
    private RpcCpService rpcCpService;

    @Autowired
    private RpcPsService rpcPsService;

    /**
     * @return com.jackrain.nea.util.ValueHolder
     * <AUTHOR>
     * @Description 仓库拆单策略保存
     * @Date  2020/06/10
     * @Param [session]
     **/
    public ValueHolder execute(QuerySession session) throws NDSException{
        DefaultWebEvent event = session.getEvent();
        List<String> nullKeyList = new ArrayList<>();
        JSONObject param = JsonUtils.filterNull((JSONObject) event.getParameterValue("param"), nullKeyList);
        if(log.isDebugEnabled()){
            log.debug(LogUtil.format("StCWarehouseSaveService execute param:{}"), param);
        }
        User user = session.getUser();
        if (param != null) {
            Long id = param.getLong("objid");
            JSONObject fixColumn = param.getJSONObject("fixcolumn");
            StCWarehouseRequest stCWarehouseRequest = JsonUtils.jsonParseClass(fixColumn, StCWarehouseRequest.class);
            if (fixColumn != null && id != null) {
                if (id != -1) {
                    checkStCWarehouse(stCWarehouseRequest.getStCWarehouse());
                    StCWarehouseDO stCWarehouseDO = stCWarehouseMapper.selectById(id);
                    if (stCWarehouseDO != null) {
                        RedisCacheUtil.delete(stCWarehouseDO.getCpCPhyWarehouseId(),
                                RedisConstant.PHY_WAREHOUSE_SPLIT_ORDER);
                    }
                    return updateStCWarehouse(user, id, stCWarehouseRequest);
                } else {
                    checkStCWarehouse(stCWarehouseRequest.getStCWarehouse());
                    // 仓库拆单策略 新增策略时先删除REIDS 缓存
                    if (stCWarehouseRequest.getStCWarehouse() != null) {
                        RedisCacheUtil.delete(stCWarehouseRequest.getStCWarehouse().getCpCPhyWarehouseId(),
                                RedisConstant.PHY_WAREHOUSE_SPLIT_ORDER);
                    }
                    return insertStCWarehouse(user, stCWarehouseRequest);
                }
            }
        }
        throw new NDSException("当前记录已不存在！");
    }

    public void checkStCWarehouse(StCWarehouseDO stCWarehouseDO){
        if (stCWarehouseDO!=null && stCWarehouseDO.getCpCPhyWarehouseId()!=null){
            QueryWrapper<StCWarehouseDO> wrapper = new QueryWrapper<>();
            wrapper.eq("CP_C_PHY_WAREHOUSE_ID", stCWarehouseDO.getCpCPhyWarehouseId());
            StCWarehouseDO stCWarehouseDOTemp = stCWarehouseMapper.selectOne(wrapper);
            if (stCWarehouseDOTemp != null) {
                throw new NDSException("仓库已存在");
            }
        }
    }
    public StCWarehouseQueryResult getStCWarehouseQueryResultByWareHouseId(Long wareHouseId){
        StCWarehouseQueryResult result = new StCWarehouseQueryResult();
        QueryWrapper<StCWarehouseDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("cp_c_phy_warehouse_id",wareHouseId);
        queryWrapper.eq("ISACTIVE","Y");
        StCWarehouseDO stCWarehouseDO = stCWarehouseMapper.selectOne(queryWrapper);
        if(stCWarehouseDO == null){
            return null;
        }
        result.setStCWarehouse(stCWarehouseDO);
        QueryWrapper<StCWarehouseBrandDO> brandWrapper = new QueryWrapper<>();
        brandWrapper.eq("st_c_warehouse_id",stCWarehouseDO.getId());
        List<StCWarehouseBrandDO> brandDOList = stCWarehouseBrandMapper.selectList(brandWrapper);

        QueryWrapper<StCWarehouseGoodsDO> goodsWrapper = new QueryWrapper<>();
        goodsWrapper.eq("st_c_warehouse_id",stCWarehouseDO.getId());
        List<StCWarehouseGoodsDO> goodsList = stCWarehouseGoodsMapper.selectList(goodsWrapper);

        QueryWrapper<StCWarehouseSkuDO> skuWrapper = new QueryWrapper<>();
        skuWrapper.eq("st_c_warehouse_id",stCWarehouseDO.getId());
        List<StCWarehouseSkuDO> skuDOList = stCWarehouseSkuMapper.selectList(skuWrapper);


        QueryWrapper<StCWarehouseGoodsClassDO> goodsClassWrapper = new QueryWrapper<>();
        goodsClassWrapper.eq("st_c_warehouse_id",stCWarehouseDO.getId());
        List<StCWarehouseGoodsClassDO> goodsClassList = stCWarehouseGoodsClassMapper.selectList(goodsClassWrapper);


        if(CollectionUtils.isEmpty(brandDOList)
                && CollectionUtils.isEmpty(goodsList)
                && CollectionUtils.isEmpty(skuDOList)
                && CollectionUtils.isEmpty(goodsClassList)){
            return null;
        }
        result.setStCWarehouseBrandList(brandDOList);
        result.setStCWarehouseGoodsList(goodsList);
        result.setStCWarehouseSkuList(skuDOList);
        result.setStCWarehouseGoodsClassList(goodsClassList);
        return result;
    }

    /**
     * 仓库拆单策略新增
     *
     * @param user
     * @param stCWarehouseRequest
     * @return com.jackrain.nea.util.ValueHolder
     * @Date  2020/06/10
     * @Param [session]
     **/
    @Transactional(rollbackFor = Exception.class)
    public ValueHolder insertStCWarehouse(User user, StCWarehouseRequest stCWarehouseRequest) {

        Long stCWarehouseId = ModelUtil.getSequence(StConstant.TAB_ST_C_WAREHOUSE);
        StCWarehouseDO stCWarehouse = stCWarehouseRequest.getStCWarehouse();
        if (stCWarehouse != null) {
            // 1.仓库拆单策略主表
            stCWarehouse.setId(stCWarehouseId);
            StBeanUtils.makeCreateField(stCWarehouse, user);
            CpCPhyWarehouse cpCPhyWarehouse = rpcCpService.getCpCPhyWahouseDoById(stCWarehouse.getCpCPhyWarehouseId());
            stCWarehouse.setCpCPhyWarehouseEcode(cpCPhyWarehouse.getEcode());
            stCWarehouse.setCpCPhyWarehouseEname(cpCPhyWarehouse.getEname());
            long id  = stCWarehouseMapper.insert(stCWarehouse);
            if (id <= 0) {
                return ValueHolderUtils.getFailValueHolder("保存失败！");
            }
            // 品牌组
            if(!CollectionUtils.isEmpty(stCWarehouseRequest.getStCWarehouseBrandList())){
                insertStCWarehouseBrand(user, stCWarehouseId, stCWarehouseRequest.getStCWarehouseBrandList());
            }
            // 商品款号编码
            if(!CollectionUtils.isEmpty(stCWarehouseRequest.getStCWarehouseGoodsList())){
                insertStCWarehouseGoods(user, stCWarehouseId, stCWarehouseRequest.getStCWarehouseGoodsList());
            }
            // 2.商品sku
            if (!CollectionUtils.isEmpty(stCWarehouseRequest.getStCWarehouseSkuList())) {
                insertStCWarehouseSku(user, stCWarehouseId, stCWarehouseRequest.getStCWarehouseSkuList());
            }
            // 商品品类
            if (!CollectionUtils.isEmpty(stCWarehouseRequest.getStCWarehouseGoodsClassList())) {
                insertStCWarehouseGoodsClass(user, stCWarehouseId, stCWarehouseRequest.getStCWarehouseGoodsClassList());
            }
        }
        return ValueHolderUtils.getSuccessValueHolder(stCWarehouseId, StConstant.TAB_ST_C_WAREHOUSE, "");
    }

    @Transactional(rollbackFor = Exception.class)
    public ValueHolder updateStCWarehouse(User user, Long id, StCWarehouseRequest stCWarehouseRequest) {
        StCWarehouseDO stCWarehouse = stCWarehouseRequest.getStCWarehouse();
        if (stCWarehouse != null) {
            stCWarehouse.setId(id);
            if(log.isDebugEnabled()){
                log.debug(LogUtil.format("updateStCWarehouse params:{}"), JSONObject.toJSONString(stCWarehouse));
            }
            //1.仓库拆单策略主表处理
            StBeanUtils.makeModifierField(stCWarehouse, user);
            if (stCWarehouseMapper.updateById(stCWarehouse) < 0) {
                return ValueHolderUtils.getFailValueHolder("更新失败！");
            }
        }

        // 仓库拆单策略-品牌组表
        List<StCWarehouseBrandDO> stCWarehouseBrandList = stCWarehouseRequest.getStCWarehouseBrandList();
        if (!CollectionUtils.isEmpty(stCWarehouseBrandList)) {
            //新增子表信息
            List<StCWarehouseBrandDO> addList = stCWarehouseBrandList.stream().filter(x -> x.getId() == -1).collect(Collectors.toList());
            List<StCWarehouseBrandDO> updateList = stCWarehouseBrandList.stream().filter(x -> x.getId() > 0).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(addList)) {
                insertStCWarehouseBrand(user, id, addList);
            }
            //修改子表信息
            if (CollectionUtils.isNotEmpty(updateList)) {
                updateStCWarehouseBrand(user, id, updateList);
            }
        }
        // 仓库拆单策略-商品表
        List<StCWarehouseGoodsDO> stCWarehouseGoodsList = stCWarehouseRequest.getStCWarehouseGoodsList();
        if (CollectionUtils.isNotEmpty(stCWarehouseGoodsList)) {
            // 1.根据仓库ID查询，sku表中有的商品SKU
            QueryWrapper<StCWarehouseSkuDO> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("st_c_warehouse_id",id);
            List<StCWarehouseSkuDO> skuDOList = stCWarehouseSkuMapper.selectList(queryWrapper);
            List<String> historySkus = skuDOList.stream().map(StCWarehouseSkuDO::getPsCSkuEcode).collect(Collectors.toList());
            //新增子表信息
            List<StCWarehouseGoodsDO> addList =
                    stCWarehouseGoodsList.stream().filter(x -> x.getId() == -1).collect(Collectors.toList());
            List<StCWarehouseGoodsDO> updateList =
                    stCWarehouseGoodsList.stream().filter(x -> x.getId() > 0).collect(Collectors.toList());
            // 2.根据商品编码从PS查询对应的skulist
            if (CollectionUtils.isNotEmpty(historySkus) && CollectionUtils.isNotEmpty(addList)) {
                StCWarehouseGoodsDO goods = addList.get(0);
                PsCPro pro = rpcPsService.queryProByID(goods.getPsCProId());
                List<PsCProSkuResult> list = rpcPsService.querySkuInfoByProEcode(pro.getEcode());
                List<String> skuCodes = list.stream().map(PsCProSkuResult::getSkuEcode).collect(Collectors.toList());
                // 3.判断当前款号对应的SKU在sku是否存在，若存在则不允许保存
                if (CollectionUtils.isNotEmpty(skuCodes)) {
                    for(String code : skuCodes){
                        if(historySkus.contains(code)){
                            throw new NDSException("该商品款号对应的sku已有部分在按SKU拆分配置里添加");
                        }
                    }
                }
            }
            if (CollectionUtils.isNotEmpty(addList)) {
                insertStCWarehouseGoods(user, id, addList);
            }
            //修改子表信息
            if (CollectionUtils.isNotEmpty(updateList)) {
                updateStCWarehouseGoods(user, updateList);
            }
        }

        // 仓库拆单策略-SKU表
        List<StCWarehouseSkuDO> stCWarehouseSkuList = stCWarehouseRequest.getStCWarehouseSkuList();
        if (CollectionUtils.isNotEmpty(stCWarehouseSkuList)) {
            // 1.根据仓库ID查询，商品款号表中有的款号
            QueryWrapper<StCWarehouseGoodsDO> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("st_c_warehouse_id",id);
            List<StCWarehouseGoodsDO> goodsDOList = stCWarehouseGoodsMapper.selectList(queryWrapper);
            List<String> historyProCodes = goodsDOList.stream().map(StCWarehouseGoodsDO::getPsCProEcode).collect(Collectors.toList());
            //新增子表信息
            List<StCWarehouseSkuDO> addList =
                    stCWarehouseSkuList.stream().filter(x -> x.getId() == -1).collect(Collectors.toList());
            List<StCWarehouseSkuDO> updateList =
                    stCWarehouseSkuList.stream().filter(x -> x.getId() > 0).collect(Collectors.toList());
            // 2.根据商品编码从PS查询对应的款号列表
            if (CollectionUtils.isNotEmpty(historyProCodes) && CollectionUtils.isNotEmpty(addList)) {
                StCWarehouseSkuDO skuDO = stCWarehouseSkuList.get(0);
                PsCSku sku = rpcPsService.getSkuById(skuDO.getPsCSkuId());
                List<PsCProSkuResult> list = rpcPsService.querySkuInfoBySkuEcode(sku.getEcode());
                List<String> spuCodes = list.stream().map(PsCProSkuResult::getPsCProEcode).collect(Collectors.toList());
                // 3.判断当前sku对应的spu在配置的商品款号表中是否存在，若存在则不允许保存
                if (CollectionUtils.isNotEmpty(historyProCodes)) {
                    for(String code : spuCodes){
                        if(historyProCodes.contains(code)){
                            throw new NDSException("该商品对应的商品款号已在按商品款号拆分配置里添加");
                        }
                    }
                }
            }
            if (CollectionUtils.isNotEmpty(addList)) {
                insertStCWarehouseSku(user, id, addList);
            }
            //修改子表信息
            if (CollectionUtils.isNotEmpty(updateList)) {
                updateStCWarehouseSku(user, updateList);
            }
        }
        //拆单策略-商品品类规则
        List<StCWarehouseGoodsClassDO> stCWarehouseGoodsClassDOList = stCWarehouseRequest.getStCWarehouseGoodsClassList();
        if (CollectionUtils.isNotEmpty(stCWarehouseGoodsClassDOList)){
            List<StCWarehouseGoodsClassDO> addList = stCWarehouseGoodsClassDOList.stream().filter(i-> i.getId() ==-1).collect(Collectors.toList());

            List<StCWarehouseGoodsClassDO> updateList = stCWarehouseGoodsClassDOList.stream().filter(i-> i.getId() >0).collect(Collectors.toList());
            //新增
            if (CollectionUtils.isNotEmpty(addList)){
                insertStCWarehouseGoodsClass(user, id, addList);
            }
            //修改
            if (CollectionUtils.isNotEmpty(updateList)) {
                updateStCWarehouseGoodsClass(user, updateList);
            }
        }
        return ValueHolderUtils.getSuccessValueHolder(id, StConstant.TAB_ST_C_WAREHOUSE, "");
    }
    /**
     * <AUTHOR>
     * @Date 14:26 2021/5/10
     * @Description 修改商品品类规则
     */
    private void updateStCWarehouseGoodsClass(User user, List<StCWarehouseGoodsClassDO> updateList) {
        for (StCWarehouseGoodsClassDO item : updateList) {
            StBeanUtils.makeCreateField(item, user);
            if (!checkStCWarehouseGoodsClass(item)){
                throw new NDSException("该品类已设置过品类拆分规则，不可重复设置");
            }
            stCWarehouseGoodsClassMapper.updateById(item);
        }

    }

    private void insertStCWarehouseBrand(User user, Long id, List<StCWarehouseBrandDO> addList) {
        QueryWrapper<StCWarehouseBrandDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(StCWarehouseBrandDO::getStCWarehouseId, id);
        List<StCWarehouseBrandDO> brandDOList = stCWarehouseBrandMapper.selectList(queryWrapper);
        List<String> brandIdList = null;
        if (CollectionUtils.isNotEmpty(brandDOList)) {
            brandIdList =
                    brandDOList.stream().map(StCWarehouseBrandDO::getBrandId).distinct().collect(Collectors.toList());
        }
        //新增
        for (StCWarehouseBrandDO item : addList) {
            if (CollectionUtils.isNotEmpty(brandIdList) && brandIdList.contains(item.getBrandId())) {
                List<PsCBrand> brands = rpcPsService.queryBrandByIds(Arrays.asList(Long.valueOf(item.getBrandId())));
                if (CollectionUtils.isEmpty(brands)) {
                    throw new NDSException("品牌《" + item.getBrandId() + "》在其他品牌组存在");
                } else {
                    throw new NDSException("品牌《" + brands.get(0).getEname() + "》在其他品牌组存在");
                }
            }
            item.setStCWarehouseId(id);//外键
            item.setId(ModelUtil.getSequence(StConstant.TAB_ST_C_WAREHOUSE_BRAND));
            StBeanUtils.makeCreateField(item, user);
            String brandStr = item.getBrandId();
            if(!StringUtils.isEmpty(brandStr)){
                List<Long> brandIds = Arrays.asList(brandStr.split(","))
                        .stream().filter(o -> !StringUtils.isEmpty(o))
                        .map(o->Long.parseLong(o))
                        .collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(brandIds)) {
                    List<PsCBrand> brands = rpcPsService.queryBrandByIds(brandIds);
                    StringBuilder builder = new StringBuilder();
                    if (CollectionUtils.isNotEmpty(brands)) {
                        for(PsCBrand brand : brands){
                            builder.append(brand.getEname());
                            builder.append(",");
                        }
                        item.setBrandName(builder.toString().substring(0,builder.toString().length()-1));
                    }
                }
            }
            int insert = stCWarehouseBrandMapper.insert(item);
            if (insert < 0) {
                throw new NDSException("按品牌组拆单明细插入失败！");
            }

        }
    }

    private void insertStCWarehouseGoods(User user, Long id, List<StCWarehouseGoodsDO> addList) {
        //新增
        for (StCWarehouseGoodsDO item : addList) {
            try {
                item.setId(ModelUtil.getSequence(StConstant.TAB_ST_C_WAREHOUSE_GOODS));
                item.setStCWarehouseId(id);//外键
                StBeanUtils.makeCreateField(item, user);
                // 根据商品ID 查询商品编码
                // 商品编码
                PsCPro pro = rpcPsService.queryProByID(item.getPsCProId());
                if (pro != null) {
                    item.setPsCProEcode(pro.getEcode());
                }
                stCWarehouseGoodsMapper.insert(item);
            } catch (Exception e) {
                log.error(LogUtil.format("insertStCWarehouseGoods 异常{}"), Throwables.getStackTraceAsString(e));
                throw new NDSException("按商品拆单明细插入失败！");
            }
        }
    }

    private void insertStCWarehouseSku(User user, Long id, List<StCWarehouseSkuDO> addList) {

        // 新增
        for (StCWarehouseSkuDO item : addList) {
            try {
                item.setId(ModelUtil.getSequence(StConstant.TAB_ST_C_WAREHOUSE_SKU));
                item.setStCWarehouseId(id);//外键
                // 商品SKU编码
                PsCSku sku = rpcPsService.getSkuById(item.getPsCSkuId());
                if (sku != null) {
                    item.setPsCSkuEcode(sku.getEcode());
                }
                StBeanUtils.makeCreateField(item, user);
                stCWarehouseSkuMapper.insert(item);
            } catch (Exception e) {
                log.error(LogUtil.format("insertStCWarehouseSku 异常{}"), Throwables.getStackTraceAsString(e));
                throw new NDSException("按sku拆单明细插入失败！");
            }
        }
    }
    /**
     * <AUTHOR>
     * @Date 16:12 2021/5/8
     * @Description 商品品类拆分规则
     */
    private void insertStCWarehouseGoodsClass(User user, Long id, List<StCWarehouseGoodsClassDO> addList) {
        // 新增
        for (StCWarehouseGoodsClassDO item : addList) {
                item.setId(ModelUtil.getSequence(StConstant.TAB_ST_C_WAREHOUSE_GOODS_CLASS));
                item.setStCWarehouseId(id);//外键
                StBeanUtils.makeCreateField(item, user);
                if (!checkStCWarehouseGoodsClass(item)){
                    throw new NDSException("该品类已设置过品类拆分规则，不可重复设置");
                }
                stCWarehouseGoodsClassMapper.insert(item);
        }
    }
    /**
     * <AUTHOR>
     * @Date 14:25 2021/5/10
     * @Description 校验商品品类唯一性
     */
    private boolean  checkStCWarehouseGoodsClass(StCWarehouseGoodsClassDO stCWarehouseGoodsClassDO){
      int n =  stCWarehouseGoodsClassMapper.selectCount(new QueryWrapper<StCWarehouseGoodsClassDO>().lambda()
                .eq(StCWarehouseGoodsClassDO::getStCWarehouseId,stCWarehouseGoodsClassDO.getStCWarehouseId())
                .eq(StCWarehouseGoodsClassDO::getPsCProdimId,stCWarehouseGoodsClassDO.getPsCProdimId()));
      if (n>0){
          return false;
      }
      return true;
    }
    private void updateStCWarehouseBrand(User user, Long id, List<StCWarehouseBrandDO> updateList) {
        QueryWrapper<StCWarehouseBrandDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(StCWarehouseBrandDO::getStCWarehouseId, id);
        List<StCWarehouseBrandDO> brandDOList = stCWarehouseBrandMapper.selectList(queryWrapper);
        List<String> brandIdList = null;
        if (CollectionUtils.isNotEmpty(brandDOList)) {
            brandIdList =
                    brandDOList.stream().map(StCWarehouseBrandDO::getBrandId).distinct().collect(Collectors.toList());
        }
        // 更新
        for (StCWarehouseBrandDO item : updateList) {
            if (CollectionUtils.isNotEmpty(brandIdList) && brandIdList.contains(item.getBrandId())) {
                List<PsCBrand> brands = rpcPsService.queryBrandByIds(Arrays.asList(Long.valueOf(item.getBrandId())));
                if (CollectionUtils.isEmpty(brands)) {
                    throw new NDSException("品牌《" + item.getBrandId() + "》在其他品牌组存在");
                } else {
                    throw new NDSException("品牌《" + brands.get(0).getEname() + "》在其他品牌组存在");
                }
            }
            item.setId(id);
            StBeanUtils.makeModifierField(item, user);
            String brandStr = item.getBrandId();
            if(!StringUtils.isEmpty(brandStr)){
                List<Long> brandIds = Arrays.asList(brandStr.split(","))
                        .stream().filter(o -> !StringUtils.isEmpty(o))
                        .map(o->Long.parseLong(o))
                        .collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(brandIds)) {
                    List<PsCBrand> brands = rpcPsService.queryBrandByIds(brandIds);
                    StringBuilder builder = new StringBuilder();
                    if (CollectionUtils.isNotEmpty(brands)) {
                        for(PsCBrand brand : brands){
                            builder.append(brand.getEname());
                            builder.append(",");
                        }
                        item.setBrandName(builder.toString().substring(0,builder.toString().length()-1));
                    }
                }
            }
            int update = stCWarehouseBrandMapper.updateById(item);
            if (update < 0) {
                throw new NDSException("按品牌组拆单明细更新失败！");
            }

        }
    }

    private void updateStCWarehouseGoods(User user, List<StCWarehouseGoodsDO> updateList) {
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("updateStCWarehouseGoods 入参={}"), JSON.toJSONString(updateList));
        }
        // 更新
        for (StCWarehouseGoodsDO item : updateList) {
            try {
                StBeanUtils.makeModifierField(item, user);
                stCWarehouseGoodsMapper.updateById(item);
            } catch (Exception e) {
                log.error(LogUtil.format("updateStCWarehouseGoods 异常{}"), Throwables.getStackTraceAsString(e));
                throw new NDSException("按商品款号拆单明细更新失败！");
            }
        }
    }

    private void updateStCWarehouseSku(User user, List<StCWarehouseSkuDO> updateList) {
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("updateStCWarehouseSku 入参={}"), JSON.toJSONString(updateList));
        }
        // 更新
        for (StCWarehouseSkuDO item : updateList) {
            StBeanUtils.makeCreateField(item, user);
            try {
                stCWarehouseSkuMapper.updateById(item);
            } catch (Exception e) {
                log.error(LogUtil.format("updateStCWarehouseSku 异常{}"), Throwables.getStackTraceAsString(e));
                throw new NDSException("按sku拆单明细更新失败！");
            }
        }
    }



}
