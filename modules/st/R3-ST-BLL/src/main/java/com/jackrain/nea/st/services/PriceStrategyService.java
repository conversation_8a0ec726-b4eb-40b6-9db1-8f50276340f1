package com.jackrain.nea.st.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Maps;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.st.common.StConstant;
import com.jackrain.nea.st.model.table.StCPriceDO;
import com.jackrain.nea.st.request.StCpCShopPriceSaveAndAuditRequest;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.face.impl.R3SystemUserResource;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import com.jackrain.nea.web.query.QueryUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.time.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.Map;
import java.util.Objects;

/**
 * 价格策略服务
 *
 * <AUTHOR>
 * @since 2023-02-03 16:02
 */
@Slf4j
@Component
public class PriceStrategyService {
    @Autowired
    private PriceSaveService priceSaveService;
    @Autowired
    private PriceAuditService priceAuditService;


    /**
     * 保存与审核：商品价格策略
     *
     * @param request 店铺创建参数：【shopEcode】【cpCShopId】
     * @return 执行结果
     */
    @Transactional(rollbackFor = Throwable.class)
    public ValueHolderV14<Long> saveAndAudit(StCpCShopPriceSaveAndAuditRequest request) {
        ValueHolder saveHolder = priceSaveService.execute(buildSaveParam(request));
        if (!saveHolder.isOK() || MapUtils.isEmpty(saveHolder.getData())
                || Objects.isNull(saveHolder.getData().get("data"))
                || Objects.isNull(((Map) saveHolder.getData().get("data")).get("objid"))) {
            log.error(LogUtil.format("保存商品价格策略失败，入参：{},返回值：{}", "PriceStrategyService.saveAndAudit"),
                    JSON.toJSON(request), JSON.toJSON(saveHolder));
            throw new NDSException("保存商品价格策略失败");
        }

        Long priceStId = Long.valueOf(String.valueOf(((Map) saveHolder.getData().get("data")).get("objid")));
        ValueHolder auditHolder = priceAuditService.execute(buildAuditParam(priceStId));
        if (!auditHolder.isOK()) {
            log.error(LogUtil.format("保存商品价格策略失败，入参：{},返回值：{}", "PriceStrategyService.saveAndAudit"),
                    JSON.toJSON(request), JSON.toJSON(auditHolder));
            throw new NDSException("审核商品价格策略失败");
        }
        return new ValueHolderV14<>(priceStId, ResultCode.SUCCESS, "商品价格策略保存与审核成功");
    }

    /**
     * 元数据-保存入参：
     * {"ST_C_PRICE":{"ENAME":"202302031704","CP_C_SHOP_ID":"2085","BEGIN_TIME":"2023-02-03 00:00:00","END_TIME":"2023-02-04 00:00:00","ACTIVE_TYPE":3,"RANK":1,"PRO_RANGE":"1","PRICE_TYPE":"1","REMARK":"奇奇测试"}}
     *
     * @param request 店铺创建参数：【shopEcode】【cpCShopId】
     * @return 创建入参
     */
    private QuerySession buildSaveParam(StCpCShopPriceSaveAndAuditRequest request) {
        User user = R3SystemUserResource.getSystemRootUser();
        QuerySession session = QueryUtils.createQuerySession(user);
        DefaultWebEvent event = new DefaultWebEvent("save", Maps.newHashMap());

        JSONObject param = new JSONObject();
        param.put("objid", -1);
        param.put("table", "ST_C_PRICE");

        JSONObject fixColumn = new JSONObject();
        fixColumn.put(StConstant.TAB_ST_C_PRICE, buildSaveObj(request));
        param.put("fixcolumn", fixColumn);

        event.put("param", param);
        session.setEvent(event);
        return session;
    }

    /**
     * 构建保存参数
     *
     * @param request 店铺创建参数：【shopEcode】【cpCShopId】
     * @return 保存对象
     */
    private StCPriceDO buildSaveObj(StCpCShopPriceSaveAndAuditRequest request) {
        StCPriceDO priceDO = new StCPriceDO();
        // eCode自动生成
        priceDO.setEcode(null);
        //活动名称 = 日常价格控制 -“店铺编码”
        priceDO.setEname("日常价格控制-" + request.getShopEcode());
        priceDO.setCpCShopId(request.getCpCShopId() + "");
        /*创建策略时有个校验，开始时间不能小于当前时间*/
        priceDO.setBeginTime(DateUtils.addMinutes(new Date(), 5));
        //开始日期 - 结束日期 = 系统当前时间 - 当前时间 + 一年
        priceDO.setEndTime(DateUtils.addYears(priceDO.getBeginTime(), 1));
        //活动类型 = 日常
        priceDO.setActiveType(3);
        //优先级 = 1
        priceDO.setRank(1L);
        //商品范围 = 全部商品
        priceDO.setProRange(1);
        //价格取值类型 = 按商品基价下限
        priceDO.setPriceType(1);
        priceDO.setRemark("SAP下发创建平台店铺时自动生成");
        return priceDO;
    }

    /**
     * 元数据-审核入参：{"objid":"678","table":"ST_C_PRICE","menu":"商品价格策略","subParam":{"idArr":[],"table":"ST_C_PRICE_EXCLUDE_ITEM"}}
     *
     * @param priceStId 保存的策略ID
     * @return 审核结果
     */
    private QuerySession buildAuditParam(Long priceStId) {
        User user = R3SystemUserResource.getSystemRootUser();
        QuerySession session = QueryUtils.createQuerySession(user);
        DefaultWebEvent event = new DefaultWebEvent("audit", Maps.newHashMap());

        JSONObject param = new JSONObject();
        param.put("objid", priceStId);
        param.put("table", "ST_C_PRICE");
        event.put("param", param);
        session.setEvent(event);
        return session;
    }
}
