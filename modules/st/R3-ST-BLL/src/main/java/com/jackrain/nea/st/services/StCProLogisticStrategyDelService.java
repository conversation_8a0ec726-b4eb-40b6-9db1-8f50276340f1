package com.jackrain.nea.st.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.jackrain.nea.st.mapper.StCProLogisticStrategyMapper;
import com.jackrain.nea.st.model.table.StCProLogisticStrategy;
import com.jackrain.nea.st.utils.StBeanUtils;
import com.jackrain.nea.st.utils.ValueHolderUtils;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;


/**
 * Created with IntelliJ IDEA.
 * @Author: jg.zhan
 * @Date:  2022/6/21 16:09
 * @Description: 商品物流设置删除
 */
@Component
@Slf4j
public class StCProLogisticStrategyDelService extends CommandAdapter {

    @Autowired
    private StCProLogisticStrategyMapper stCProLogisticStrategyMapper;


    @Override
    public ValueHolder execute(QuerySession session) {
        DefaultWebEvent event = session.getEvent();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(
                event.getParameterValue("param"), "yyyy-MM-dd HH:mm:ss"),
                Feature.OrderedField);
        log.info(LogUtil.format("##商品物流设置删除##入参：{}"),param);
        Long id = param.getLong("objid");
        JSONArray errorArray = new JSONArray();
        if (id != null && id > 0) {
            StCProLogisticStrategy stCProLogisticStrategy = stCProLogisticStrategyMapper.selectById(id);
            if (stCProLogisticStrategy == null){
                return ValueHolderUtils.getFailValueHolder("当前记录已不存在！");
            }
            int record = stCProLogisticStrategyMapper.deleteById(id);
            if (record <= 0) {
                errorArray.add("删除主表失败");
            }
        }
        return StBeanUtils.getExcuteValueHolder(errorArray);
    }

}
