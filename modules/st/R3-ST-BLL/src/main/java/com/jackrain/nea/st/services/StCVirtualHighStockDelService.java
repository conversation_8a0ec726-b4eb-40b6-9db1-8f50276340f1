package com.jackrain.nea.st.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.st.common.StConstant;
import com.jackrain.nea.st.mapper.StCVirtualHighStockItemMapper;
import com.jackrain.nea.st.mapper.StCVirtualHighStockMapper;
import com.jackrain.nea.st.model.table.StCShopVirtualHighStockDO;
import com.jackrain.nea.st.utils.StBeanUtils;
import com.jackrain.nea.st.utils.ValueHolderUtils;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR> ShiLong
 * @Date: 2020/7/9 10:42 上午
 * @Desc:
 */
@Component
@Slf4j
public class StCVirtualHighStockDelService extends CommandAdapter {
    @Autowired
    private StCVirtualHighStockMapper mapper;
    @Autowired
    private StCVirtualHighStockItemMapper itemMapper;

    @Override
    public ValueHolder execute(QuerySession querySession) throws NDSException {
        DefaultWebEvent event = querySession.getEvent();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(
                event.getParameterValue("param"), "yyyy-MM-dd HH:mm:ss"),
                Feature.OrderedField);
        //是否删除主表
        boolean delMainFlag = param.getBoolean("isdelmtable");
        Long id = param.getLong("objid");
        JSONObject tabitem = param.getJSONObject("tabitem");
        if (id != null && id > 0) {
            ValueHolder valueHolder = new ValueHolder();
            //1. 检查主表
            StCShopVirtualHighStockDO queryResult = mapper.selectById(id);
            if (!checkStatus(queryResult, valueHolder)) {
                return valueHolder;
            }
            // 2. 删除
            if (delMainFlag) {
                //2.1 删除主表
                if ((mapper.deleteById(queryResult)) > 0) {
                    return ValueHolderUtils.getFailValueHolder("删除主表成功！");
                }
            } else {
                //2.2 单独删除明细
                JSONArray errorArray = new JSONArray();
                JSONArray itemArray = tabitem.getJSONArray(StConstant.TAB_ST_C_SHOP_VIRTUAL_HIGH_STOCK_ITEM);
                //2.2.1 从表
                if (itemArray != null && itemArray.size() > 0) {
                    delItemByID(itemArray, errorArray);
                }
                updateModifyInfo(querySession, id);
                return StBeanUtils.getExcuteValueHolder(errorArray);
            }
        }
        throw new NDSException("当前记录已不存在！");
    }

    private void delItemByID(JSONArray itemArray, JSONArray errorArray) {
        if (itemArray.size() > 0) {
            for (int i = 0; i < itemArray.size(); i++) {
                Long itemid = itemArray.getLong(i);
                if ((itemMapper.deleteById(itemid)) <= 0) {
                    errorArray.add(StBeanUtils.getJsonObjectInfo(itemid, "明细记录已不存在"));
                }
            }
        }
    }

    private void updateModifyInfo(QuerySession session, Long id) {
        StCShopVirtualHighStockDO stCShopItemVirtualHighStockDO = new StCShopVirtualHighStockDO();
        stCShopItemVirtualHighStockDO.setId(id);
        //修改信息
        StBeanUtils.makeModifierField(stCShopItemVirtualHighStockDO, session.getUser());
        //修改人账号
        stCShopItemVirtualHighStockDO.setModifierename(session.getUser().getEname());
        if ((mapper.updateById(stCShopItemVirtualHighStockDO)) <= 0) {
            log.error(LogUtil.format("VirtualHighStockDelService.updateModifyInfo Error", "删除明细，主表修改字段信息更新出错id:", id));
        }
    }

    private boolean checkStatus(StCShopVirtualHighStockDO stCShopItemVirtualHighStockDO, ValueHolder valueHolder) {
        if (stCShopItemVirtualHighStockDO == null) {
            valueHolder.put("code", -1);
            valueHolder.put("message", "当前记录不存在！");
            return false;
        }
        //不是未审核，不允许删除
        if (!StConstant.CON_BILL_STATUS_01.equals(stCShopItemVirtualHighStockDO.getState()) ) {
            valueHolder.put("code", -1);
            valueHolder.put("message", "当前记录不是未审核，不允许删除！");
            return false;
        }
        return true;
    }
}
