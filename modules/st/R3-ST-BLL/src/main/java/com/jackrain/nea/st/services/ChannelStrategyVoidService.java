package com.jackrain.nea.st.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.st.common.StConstant;
import com.jackrain.nea.st.mapper.StCChannelStrategyMapper;
import com.jackrain.nea.st.model.table.StCChannelStrategyDO;
import com.jackrain.nea.st.model.table.StSyncStockStrategyChannelDO;
import com.jackrain.nea.st.utils.StBeanUtils;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.Date;
import java.util.HashMap;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2019/12/4 15:47
 */
@Component
@Slf4j
@Transactional(rollbackFor = {Exception.class}, propagation = Propagation.SUPPORTS)
@Deprecated
public class ChannelStrategyVoidService extends CommandAdapter {
    @Autowired
    private StCChannelStrategyMapper mapper;

    @Override
    public ValueHolder execute(QuerySession session) throws NDSException {
        DefaultWebEvent event = session.getEvent();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(
                event.getParameterValue("param"), "yyyy-MM-dd HH:mm:ss",
                SerializerFeature.WriteMapNullValue), Feature.OrderedField);
        log.info(LogUtil.format("ChannelStrategyVoidServiceParam：{}"), param);
        HashMap<Long, Object> errMap = new HashMap<Long, Object>();
        //生成Json数组
        JSONArray voidArray = StBeanUtils.makeVoidJsonArray(param);
        //列表批量
        if (!CollectionUtils.isEmpty(voidArray)) {
            for (int i = 0; i < voidArray.size(); i++) {
                Long id = Long.valueOf(voidArray.get(i).toString());   // 遍历 jsonarray 数组，把每一个对象转成 json 对象
                try {
                    List<StSyncStockStrategyChannelDO> exists = mapper.selectRelateSyncStockStrategyChannel(id);
                    if (exists != null && exists.size() > 0) {
                        errMap.put(id, "渠道策略在店铺同步库存中应用，不予许作废");
                    } else {
                        voidChannelStrategy(id, session);
                    }
                } catch (Exception ex) {
                    errMap.put(id, ex.getMessage());
                }
            }
        }
        //返回日志
        return StBeanUtils.getExcuteValueHolder(voidArray.size(), errMap);
    }

    /**
     * 作废
     *
     * @param id
     * @param session
     * @return com.jackrain.nea.util.ValueHolder
     */
    private void voidChannelStrategy(long id, QuerySession session) {
        //验证
        checkChannelStrategy(id);
        //作废
        StCChannelStrategyDO channelStrategy = new StCChannelStrategyDO();
        channelStrategy.setId(id);
        channelStrategy.setIsactive(StConstant.ISACTIVE_N);//作废状态
        channelStrategy.setDelname(session.getUser().getName());//作废人用户名
        channelStrategy.setDelid(Long.valueOf(session.getUser().getId()));//作废人
        channelStrategy.setDelename(session.getUser().getName());//作废人姓名
        channelStrategy.setDelTime(new Date());//作废时间
        StBeanUtils.makeModifierField(channelStrategy, session.getUser());
        int update = mapper.updateById(channelStrategy);
        if (update < 0) {
            throw new NDSException("作废失败！");
        }
    }

    /**
     * 验证
     *
     * @param id
     * @return java.lang.String
     */
    private void checkChannelStrategy(long id) {
        StCChannelStrategyDO existsChannelStrategy = mapper.selectById(id);
        if (existsChannelStrategy == null) {
            throw new NDSException("当前记录已不存在！");
        } else {
            if (existsChannelStrategy.getIsactive().equals(StConstant.ISACTIVE_N)) {
                throw new NDSException("当前记录已作废，不允许重复作废！");
            }
        }
    }

}
