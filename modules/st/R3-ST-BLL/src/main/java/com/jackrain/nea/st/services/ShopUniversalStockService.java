//package com.jackrain.nea.st.services;
//
//import com.jackrain.nea.sg.oms.api.SgBSyncChannelStockCmd;
//import com.jackrain.nea.sg.oms.model.request.SgBSyncChannelStockRequest;
//import com.jackrain.nea.sg.oms.model.table.SgBSyncChannelStock;
//import com.jackrain.nea.st.common.StConstant;
//import com.jackrain.nea.st.utils.ValueHolderUtils;
//import com.jackrain.nea.sys.domain.ValueHolderV14;
//import com.jackrain.nea.util.ValueHolder;
//import com.jackrain.nea.web.face.User;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.dubbo.config.annotation.Reference;
//import org.springframework.stereotype.Component;
//
//import java.util.Date;
//
///**
// *  策略通用
// *  反审核 结案 删除库存同步中间表
// *  延期 修改库存中间表
// */
//@Component
//@Slf4j
//public class ShopUniversalStockService {
//
//
//    @Reference(group = "sg", version = "1.0")
//    private SgBSyncChannelStockCmd sgBSyncChannelStockCmd;
//
//    /**
//     *  延期修改方法
//     * @param objid
//     * @param endTime
//     */
//    public ValueHolderV14<Integer> updateSgBSyncChannelStock(Long objid, Date endTime, User user) {
////        log.debug(LogUtil.format("【延期 修改同步库存】入参objid:{},endTime{},executeStatus{}:", objid, endTime);
////        SgBSyncChannelStock sgBSyncChannelStock = new SgBSyncChannelStock();
////        sgBSyncChannelStock.setEndTime(endTime);
////        sgBSyncChannelStock.setStatus(StConstant.NO_SYNC_STATUS);
////        sgBSyncChannelStock.setStrategyId(objid);
////        ValueHolderV14<Integer> valueHolderV14 = new ValueHolderV14<>();
////        try {
////            valueHolderV14 = sgBSyncChannelStockCmd.updateSgBSyncChannelStock(sgBSyncChannelStock,user);
////            if (!valueHolderV14.isOK()) {
////                log.debug(LogUtil.format("【延期 修改同步库存失败】objid:{},endTime{},", objid, endTime);
////                return valueHolderV14;
////            }
////        } catch (Exception e) {
////            e.printStackTrace();
////            log.debug(LogUtil.format("【延期 修改同步库存失败】objid:{},endTime{}", objid, endTime);
////
////        }
////        return valueHolderV14;
//        ValueHolderV14<Integer> valueHolderV14 = new ValueHolderV14<>();
//        return valueHolderV14;
//    }
//    /**
//     * 反审核 结案 删除同步库存中间表中的数据
//     *
//     * @param objid
//     * @return
//     */
//    public ValueHolder deleteSgBSyncChannelStock(Long type, Long objid) {
////        log.debug(LogUtil.format("【清除中间表数据】 策略类型:type:{},策略id:objid{}:", type, objid);
////
////        SgBSyncChannelStockRequest request = new SgBSyncChannelStockRequest();
////        request.setType(type);        //设置类型
////        request.setStrategyId(objid); //制定策略id
////        ValueHolderV14<Integer> v14 = sgBSyncChannelStockCmd.deleteSgBSyncChannelStock(request);
////        if (v14.isOK()) {
////            return ValueHolderUtils.getSuccessValueHolder("成功");
////        }
////        return ValueHolderUtils.getFailValueHolder("失败");
//        return ValueHolderUtils.getSuccessValueHolder("成功");
//    }
//}
