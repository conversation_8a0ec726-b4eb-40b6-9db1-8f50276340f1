package com.jackrain.nea.st.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.st.common.StConstant;
import com.jackrain.nea.st.mapper.StCDetentionPolicyItemMapper;
import com.jackrain.nea.st.mapper.StCDetentionPolicyMapper;
import com.jackrain.nea.st.model.table.StCDetentionPolicy;
import com.jackrain.nea.st.utils.StBeanUtils;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.Objects;

/**
 * @program: r3-st
 * @description:  预售卡单删除
 * @author: liuwj
 * @create: 2021-07-05 11:45
 **/
@Component
@Slf4j
public class StCDetentionPolicydeleteService extends CommandAdapter {

    @Autowired
    private StCDetentionPolicyMapper mapper;

    @Autowired
    private StCDetentionPolicyItemMapper stCDetentionPolicyItemMapper;

    @Override
    public ValueHolder execute(QuerySession session){
        DefaultWebEvent event = session.getEvent();
        JSONObject param = JSON.parseObject(
                JSON.toJSONStringWithDateFormat(event.getParameterValue("param"), "yyyy-MM-dd HH:mm:ss"),
                Feature.OrderedField);

        boolean delMainFlag = param.getBoolean("isdelmtable");//是否删除主表
        Long id = param.getLong("objid");

        if (delMainFlag) {
            // 删除主表
            if (Objects.nonNull(id) && id > 0) {
                //主表删除写在这里
            }
        } else {
            // 删除明细
            JSONObject tabitem = param.getJSONObject("tabitem");
            if (!CollectionUtils.isEmpty(tabitem)) {
                JSONArray itemArray = tabitem.getJSONArray(StConstant.TAB_ST_C_DETENTION_POLICY_ITEM);
                JSONArray errorArray = new JSONArray();
                if (!CollectionUtils.isEmpty(itemArray)) {
                    deleteItemByID(itemArray, errorArray);
                }
                //修改主表信息
                updateHoldOrder(id,session);
                return StBeanUtils.getExcuteValueHolder(errorArray);
            }
        }

        throw new NDSException("当前记录已不存在！");
    }

    /**
     * <AUTHOR>
     * @Date  2021/4/23
     * @Description  修改主表信息
     */
    private void updateHoldOrder(Long id, QuerySession session) {
        StCDetentionPolicy stCDetentionPolicy =new StCDetentionPolicy();
        stCDetentionPolicy.setId(id);
        StBeanUtils.makeModifierField(stCDetentionPolicy,session.getUser());
        int n = mapper.updateById(stCDetentionPolicy);
        if (n<=0){
            throw new NDSException("修改主表的信息失败！");
        }
    }
    /**
     * <AUTHOR>
     * @Date 14:34 2021/4/23
     * @Description  明细删除
     */
    private void deleteItemByID(JSONArray itemArray, JSONArray errorArray) {
        for (int i = 0; i < itemArray.size(); i++) {
            Long itemid = itemArray.getLong(i);
            if ((stCDetentionPolicyItemMapper.deleteById(itemid)) <= 0) {
                errorArray.add(StBeanUtils.getJsonObjectInfo(itemid, "删除明细失败"));
            }
        }
    }

}
