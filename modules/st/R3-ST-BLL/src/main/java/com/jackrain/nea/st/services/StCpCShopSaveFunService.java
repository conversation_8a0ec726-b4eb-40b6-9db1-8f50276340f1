package com.jackrain.nea.st.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.cpext.model.table.CpCPhyWarehouse;
import com.jackrain.nea.cpext.model.table.CpShop;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.st.mapper.StCAutoCheckMapper;
import com.jackrain.nea.st.mapper.StCExchangeStrategyOrderMapper;
import com.jackrain.nea.st.mapper.StCMergeOrderMapper;
import com.jackrain.nea.st.mapper.StCShopStrategyMapper;
import com.jackrain.nea.st.mapper.StCSyncStockStrategyChannelMapper;
import com.jackrain.nea.st.mapper.StCSyncStockStrategyMapper;
import com.jackrain.nea.st.model.table.StCAutoCheckDO;
import com.jackrain.nea.st.model.table.StCExchangeStrategyOrderDO;
import com.jackrain.nea.st.model.table.StCMergeOrderDO;
import com.jackrain.nea.st.model.table.StCShopStrategyDO;
import com.jackrain.nea.st.model.table.StCSyncStockStrategyChannelDO;
import com.jackrain.nea.st.model.table.StCSyncStockStrategyDO;
import com.jackrain.nea.st.rpc.RpcCpService;
import com.jackrain.nea.st.utils.JsonUtils;
import com.jackrain.nea.st.utils.RedisCacheUtil;
import com.jackrain.nea.st.utils.RedisConstant;
import com.jackrain.nea.st.utils.StBeanUtils;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.face.impl.UserImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @author: 陈俊明
 * @since: 2019-05-13
 * @create at : 2019-05-13 17:27
 */

@Component
@Slf4j
public class StCpCShopSaveFunService {

    @Autowired
    private StCMergeOrderMapper stCMergeOrderMapper; //订单合并策略

    @Autowired
    private StCExchangeStrategyOrderMapper stCExchangeStrategyOrderMapper; //订单换货策略


    @Autowired
    private StCAutoCheckMapper stCAutocheckMapper;  //订单自动审核策略

    @Autowired
    private StCShopStrategyMapper stCShopStrategyMapper; //店铺策略

    @Autowired
    private StCSyncStockStrategyMapper stCSyncStockStrategyMapper; //店铺库存同步策略

    @Autowired
    private StCSyncStockStrategyChannelMapper stCSyncStockStrategyChannelMapper; //店铺库存同步策略明细

//    @Autowired
//    private RpcSgService rpcSgService;

    @Autowired
    private RpcCpService rpcCpService;

    @NacosValue(value = "${st.shop.strategy.warehouse.code:8051}", autoRefreshed = true)
    private String warehouseCode;

    @NacosValue(value = "${st.shop.strategy.return.warehouse:8048}", autoRefreshed = true)
    private String returnWarehouseCode;

    public ValueHolderV14 insertStCpCShopSaveFun(JSONObject jsonObject) {
        ValueHolderV14 valueHolderV14 = new ValueHolderV14();

        if (log.isDebugEnabled()) {
            log.info(LogUtil.format(" insertStCpCShopSaveFun 【入参JSON】：") + JsonUtils.toJsonString(jsonObject));
        }

        if (jsonObject != null) {
            JSONObject cpShopMap = jsonObject.getJSONObject("shop");
            JSONObject userMap = jsonObject.getJSONObject("user");

            CpShop cpShop = JsonUtils.jsonParseClass(cpShopMap, CpShop.class);
            User user = JSON.parseObject(JSON.toJSONString(userMap, SerializerFeature.IgnoreErrorGetter), UserImpl.class);

            if (cpShop != null) {
                Long cpCShopId = cpShop.getId();

                //查找对应的CpCShop
                CpShop cpShopFromRpc = rpcCpService.selectCpCShopById(cpCShopId);
                if (log.isDebugEnabled()) {
                    log.info(LogUtil.format(" 调用Rpc接口【rpcCpService.selectCpCShopById】：") + JsonUtils.toJsonString(cpShopFromRpc));
                }

                Long userId = user.getId() == null ? 0L:Long.valueOf(user.getId());
                log.info(LogUtil.format(" userId：") + userId);
                String userName = user.getName();
                Date nowDate = new Date();

                //操作：订单合并策略、订单自动审核策略、店铺策略、店铺库存同步策略
                valueHolderV14 = this.mergeOrderFun(cpShopFromRpc, user, userId, userName, nowDate,
                        valueHolderV14);

                if (valueHolderV14.isOK()) {
                    valueHolderV14.setCode(ResultCode.SUCCESS);
                    valueHolderV14.setMessage("策略平台单据执行成功！ ");
                }
            }
        } else {
            valueHolderV14.setCode(ResultCode.FAIL);
            valueHolderV14.setMessage("入参的格式有误，为null！");
            return valueHolderV14;
        }

        return valueHolderV14;
    }

    /**
     * 订单合并策略、订单自动审核策略、店铺策略、店铺库存同步策略
     *
     * @param cpShopFromRpc 店铺信息
     * @param user 用户实体
     * @param userId 用户id
     * @param userName 用户名称
     * @param nowDate 当前日期
     * @return
     */
    public ValueHolderV14 mergeOrderFun(CpShop cpShopFromRpc, User user, Long userId,
                                        String userName, Date nowDate, ValueHolderV14 valueHolderV14) {
        Long cpCShopId = cpShopFromRpc.getCpCShopId();
        String cpCShopEcode = cpShopFromRpc.getEcode();
        String cpCShopTitle = cpShopFromRpc.getCpCShopTitle();
        String channelType = cpShopFromRpc.getChannelType();
        Long cpCPlatformId = cpShopFromRpc.getCpCPlatformId();
        String storeCategary = cpShopFromRpc.getStoreCategary();
        if (cpCShopId == null || cpCShopId <= 0) {
            valueHolderV14.setCode(ResultCode.FAIL);
            valueHolderV14.setMessage("传递的cpCShopId为空！");
            return valueHolderV14;
        }

        //订单合并策略
        List<StCMergeOrderDO> mergeOrderDOList = stCMergeOrderMapper.selectBycpCShopId(cpCShopId);
        if (mergeOrderDOList != null && mergeOrderDOList.size() > 0) {
            if (StringUtils.isBlank(cpCShopTitle)) {
                valueHolderV14.setCode(ResultCode.SUCCESS);
            } else {
                //执行更新店铺名称的操作
                if (stCMergeOrderMapper.updateShopTitleByShopId(cpCShopTitle, userId, userName, nowDate,
                        cpCShopId) <= 0) {
                    valueHolderV14.setCode(ResultCode.FAIL);
                    valueHolderV14.setMessage(valueHolderV14.getMessage().concat(";更新订单合并策略失败！=>>" +
                            "店铺:" + cpCShopId.toString()));
                }
            }
        } else {
            //将数据插入到订单合并策略
            StCMergeOrderDO mergeOrderDO = new StCMergeOrderDO();
            mergeOrderDO.setId(ModelUtil.getSequence("ST_C_MERGE_ORDER")); //ID
            mergeOrderDO.setCpCShopId(cpCShopId);  //店铺编码
            mergeOrderDO.setCpCShopTitle(cpCShopTitle); //店铺名称
            mergeOrderDO.setIsAutomerge(0); //自动合并订单 默认:不勾选 值为：0
            mergeOrderDO.setIsAutochange(0); //自动合并换货订单 默认：不勾选 值为:0
            mergeOrderDO.setIsMergerLive(0);
            mergeOrderDO.setIsMergerPresell(0);
            StBeanUtils.makeCreateField(mergeOrderDO, user);
            if (stCMergeOrderMapper.insert(mergeOrderDO) <= 0) {
                valueHolderV14.setCode(ResultCode.FAIL);
                valueHolderV14.setMessage(valueHolderV14.getMessage().concat(";新增订单合并策略失败！=>>" +
                        "店铺:" + cpCShopId.toString()));
            }
        }

        //订单换货策略
        List<StCExchangeStrategyOrderDO> exchangeDOList = stCExchangeStrategyOrderMapper.selectBycpCShopId(cpCShopId);
        if (exchangeDOList != null && exchangeDOList.size() > 0) {
            if (StringUtils.isBlank(cpCShopTitle)) {
                valueHolderV14.setCode(ResultCode.SUCCESS);
            } else {
                //执行更新店铺名称的操作
                if (stCExchangeStrategyOrderMapper.updateShopTitleByShopId(cpCShopTitle, userId, userName, nowDate,
                        cpCShopId) <= 0) {
                    valueHolderV14.setCode(ResultCode.FAIL);
                    valueHolderV14.setMessage(valueHolderV14.getMessage().concat(";更新订单换货策略失败！=>>" +
                            "店铺:" + cpCShopId.toString()));
                }
            }
        } else {
            //将数据插入到订单换货策略
            StCExchangeStrategyOrderDO stCExchangeStrategyOrderDO = new StCExchangeStrategyOrderDO();
            stCExchangeStrategyOrderDO.setId(ModelUtil.getSequence("ST_C_EXCHANGE_ORDER_STRATEGY")); //ID
            stCExchangeStrategyOrderDO.setCpCShopId(cpCShopId);  //店铺编码
            stCExchangeStrategyOrderDO.setCpCShopTitle(cpCShopTitle); //店铺名称
            stCExchangeStrategyOrderDO.setIsOutRefuse(0);
            stCExchangeStrategyOrderDO.setIsNoRemarkAgree(0);
            stCExchangeStrategyOrderDO.setIsPriceOtherAgree(0);
            stCExchangeStrategyOrderDO.setIsOffAgree(0);
            stCExchangeStrategyOrderDO.setIsOffRefuse(0);
            stCExchangeStrategyOrderDO.setIsPriceOtherRefuse(0);

            StBeanUtils.makeCreateField(stCExchangeStrategyOrderDO, user);
            if (stCExchangeStrategyOrderMapper.insert(stCExchangeStrategyOrderDO) <= 0) {
                valueHolderV14.setCode(ResultCode.FAIL);
                valueHolderV14.setMessage(valueHolderV14.getMessage().concat(";新增订单换货策略失败！=>>" +
                        "店铺:" + cpCShopId.toString()));
            }
        }

        //订单自动审核策略
        List<StCAutoCheckDO> autocheckDOList = stCAutocheckMapper.selectBycpCShopId(cpCShopId);
        if (autocheckDOList != null && autocheckDOList.size() > 0) {
            if (StringUtils.isBlank(cpCShopTitle)) {
                valueHolderV14.setCode(ResultCode.SUCCESS);
            } else {
                //执行更新店铺名称的操作
                if (stCAutocheckMapper.updateShopTitleByShopId(cpCShopTitle, userId, userName, nowDate,
                        cpCShopId) <= 0) {
                    valueHolderV14.setCode(ResultCode.FAIL);
                    valueHolderV14.setMessage(valueHolderV14.getMessage().concat(";更新订单自动审核策略失败！=>>" +
                            "店铺:" + cpCShopId.toString()));
                }
            }
        } else {
            //将数据插入到订单自动审核策略
            StCAutoCheckDO autocheckDO = new StCAutoCheckDO();
            autocheckDO.setId(ModelUtil.getSequence("ST_C_AUTOCHECK")); //ID
            autocheckDO.setCpCShopId(cpCShopId); //店铺编码
            autocheckDO.setCpCShopTitle(cpCShopTitle); //店铺名称
            autocheckDO.setIsAutocheckOrder("N"); //自动审核订单参数 默认：不勾选 值为：N 否
            autocheckDO.setIsAutocheckPay("N"); //自动审核货到付款 默认：不勾选 值为：N 否
            autocheckDO.setIsRemarkAutocheck("N"); //有备注订单自动审核 默认：不勾选 值为：N 否
            StBeanUtils.makeCreateField(autocheckDO, user);
            if (stCAutocheckMapper.insert(autocheckDO) <= 0) {
                valueHolderV14.setCode(ResultCode.FAIL);
                valueHolderV14.setMessage(valueHolderV14.getMessage().concat(";新增订单自动审核策略失败！=>>" +
                        "店铺:" + cpCShopId.toString()));
            }
        }

        //店铺策略
        List<StCShopStrategyDO> shopStrategyDOList = stCShopStrategyMapper.selectBycpCShopId(cpCShopId);
        if (shopStrategyDOList != null && shopStrategyDOList.size() > 0) {
            if (StringUtils.isBlank(cpCShopTitle)) {
                valueHolderV14.setCode(ResultCode.SUCCESS);
            } else {
                //执行更新店铺名称的操作
                if (stCShopStrategyMapper.updateShopTitleByShopId(cpCShopTitle, userId, userName, nowDate,
                        cpCShopId) <= 0) {
                    valueHolderV14.setCode(ResultCode.FAIL);
                    valueHolderV14.setMessage(valueHolderV14.getMessage().concat(";更新店铺策略失败！=>>" +
                            "店铺:" + cpCShopId.toString()));
                }
            }
        } else {
            //将数据插入到店铺策略
            StCShopStrategyDO shopStrategyDO = new StCShopStrategyDO();
            shopStrategyDO.setId(ModelUtil.getSequence("ST_C_SHOP_STRATEGY")); //ID
            shopStrategyDO.setCpCShopId(cpCShopId); //店铺编码
            shopStrategyDO.setCpCShopTitle(cpCShopTitle); //店铺名称
            shopStrategyDO.setIsAg("N"); //是否开启AG 默认：不勾选 值为：N 否
            shopStrategyDO.setIsAutoSplit("N"); //是否缺货自动拆单 默认：不勾选 值为：N 否
            shopStrategyDO.setIsAutoAudit("N"); //自动退货入库匹配 默认：不勾选 值为：N 否
            shopStrategyDO.setIsForceSend(1); //是否强制发货 默认：勾选 值为：1 是
            shopStrategyDO.setCanSplit("Y"); //是否拆单 默认：勾选 值为：Y 是
            shopStrategyDO.setIsDetentionSplit("Y"); //是否卡单拆单 默认：勾选 值为：Y 是
            shopStrategyDO.setIsAdvanceSplit("Y"); //是否预售拆单 默认：勾选 值为：Y 是
            shopStrategyDO.setCancelReturnOrder("Y"); //是否取消退换货单 默认：勾选 值为：Y 是
            shopStrategyDO.setIsPlatformSplit("Y"); //是否京东平台拆单 默认：勾选 值为：Y 是
            shopStrategyDO.setIsOrderTmsTrack("Y"); //是否开启订单物流轨迹跟踪 默认：勾选 值为：Y 是
            if (StringUtils.isNotEmpty(storeCategary) && "Z009".equals(storeCategary)) {
                shopStrategyDO.setIsManuallyCreate("Y"); //旺店通类型店铺开启允许手工建单 值为：Y 是
            }else {
                shopStrategyDO.setIsManuallyCreate("N"); //值为：N 否
            }
            shopStrategyDO.setOccupyType("2"); //默认允许订单按行拆 值为：'2'
            //查询默认仓库
            CpCPhyWarehouse warehouse = rpcCpService.queryWarehouseByCode(warehouseCode);
            if (warehouse == null) {
                valueHolderV14.setCode(ResultCode.FAIL);
                valueHolderV14.setMessage(valueHolderV14.getMessage().concat(";查询默认仓库失败！仓库编码：" + warehouseCode));
            }else {
                shopStrategyDO.setDefaultStoreId(warehouse.getId());
            }
            //查询默认退货仓库
            CpCPhyWarehouse returnWarehouse = rpcCpService.queryWarehouseByCode(returnWarehouseCode);
            if (returnWarehouse == null) {
                valueHolderV14.setCode(ResultCode.FAIL);
                valueHolderV14.setMessage(valueHolderV14.getMessage().concat(";查询默认退货仓库失败！仓库编码：" + returnWarehouseCode));
            }else {
                shopStrategyDO.setCpCWarehouseDefId(returnWarehouse.getId());
            }
            StBeanUtils.makeCreateField(shopStrategyDO, user);
            if (stCShopStrategyMapper.insert(shopStrategyDO) <= 0) {
                valueHolderV14.setCode(ResultCode.FAIL);
                valueHolderV14.setMessage(valueHolderV14.getMessage().concat(";新增店铺策略失败！=>>" +
                        "店铺:" + cpCShopId.toString()));
            }
        }

        //店铺库存同步策略
        List<StCSyncStockStrategyDO> stockStrategyDOList = stCSyncStockStrategyMapper.selectBycpCShopId(cpCShopId);
        if (stockStrategyDOList != null && stockStrategyDOList.size() > 0) {
            if (StringUtils.isBlank(cpCShopTitle) && cpCPlatformId == null) {
                valueHolderV14.setCode(ResultCode.SUCCESS);
            } else {
                //执行更新店铺名称以及平台ID的操作
                if (stCSyncStockStrategyMapper.updateShopTitleByShopId(cpCShopTitle, cpCPlatformId, channelType,
                        userId, userName, nowDate, cpCShopId) <= 0) {
                    valueHolderV14.setCode(ResultCode.FAIL);
                    valueHolderV14.setMessage(valueHolderV14.getMessage().concat(";更新店铺库存同步策略失败！=>>" +
                            "店铺:" + cpCShopId.toString()));
                } else {
                    //明细中有店铺信息，所以主表更新成功后需要更新明细数据
                    List<Long> stockStrategyIdList = stockStrategyDOList.stream().map(StCSyncStockStrategyDO::getId).collect(Collectors.toList());
                    StCSyncStockStrategyChannelDO updateInfo = new StCSyncStockStrategyChannelDO();
                    updateInfo.setCpCShopTitle(cpCShopTitle);
                    QueryWrapper<StCSyncStockStrategyChannelDO> wrapper = new QueryWrapper<>();
                    wrapper.in("st_c_sync_stock_strategy_id", stockStrategyIdList);
                    stCSyncStockStrategyChannelMapper.update(updateInfo, wrapper);
                }

                //执行更新[渠道逻辑仓]店铺名称的操作
//                rpcSgService.updateSgChannelStoreChange(cpCShopId, cpCShopTitle);
                RedisCacheUtil.delete(cpCShopId, RedisConstant.SHOP_SYNC_STOCK_STRATEGY);
                RedisCacheUtil.deleteAll(RedisConstant.SHOP_SYNC_STOCK_STRATEGY_ALL);
            }
        } else {
            //将数据插入到店铺库存同步策略
            StCSyncStockStrategyDO syncStockStrategyDO = new StCSyncStockStrategyDO();
            //ID
            syncStockStrategyDO.setId(ModelUtil.getSequence("ST_C_SYNC_STOCK_STRATEGY"));
            //店铺编码
            syncStockStrategyDO.setCpCShopId(cpCShopId);
            //店铺编码
            syncStockStrategyDO.setCpCShopEcode(cpCShopEcode);
            //店铺名称
            syncStockStrategyDO.setCpCShopTitle(cpCShopTitle);
            //平台ID
            syncStockStrategyDO.setCpCPlatformId(cpCPlatformId);
            //渠道类型
            syncStockStrategyDO.setChannelType(channelType);

            //是否同步库存 默认：不勾选 值为：0
            syncStockStrategyDO.setIsSyncStock(0);
            StBeanUtils.makeCreateField(syncStockStrategyDO, user);
            if (stCSyncStockStrategyMapper.insert(syncStockStrategyDO) <= 0) {
                valueHolderV14.setCode(ResultCode.FAIL);
                valueHolderV14.setMessage(valueHolderV14.getMessage().concat(";新增店铺库存同步策略失败！=>>" +
                        "店铺:" + cpCShopId.toString()));
            } else {
                RedisCacheUtil.delete(syncStockStrategyDO.getCpCShopId(), RedisConstant.SHOP_SYNC_STOCK_STRATEGY);
                RedisCacheUtil.deleteAll(RedisConstant.SHOP_SYNC_STOCK_STRATEGY_ALL);
            }
        }

        return valueHolderV14;
    }
}
