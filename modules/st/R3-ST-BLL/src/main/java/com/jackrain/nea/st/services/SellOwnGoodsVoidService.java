package com.jackrain.nea.st.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.st.common.StConstant;
import com.jackrain.nea.st.mapper.StCSellOwngoodsMapper;
import com.jackrain.nea.st.model.table.StCSellOwngoodsDO;
import com.jackrain.nea.st.utils.StBeanUtils;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.HashMap;

/**
 * <AUTHOR>
 * @Description 经销商自有商品作废业务类
 * @Date 2019/3/12
 **/
@Component
@Slf4j
@Transactional
public class SellOwnGoodsVoidService extends CommandAdapter {

    @Autowired
    private StCSellOwngoodsMapper stCSellOwngoodsMapper;

    /**
     * @return com.jackrain.nea.util.ValueHolder
     * <AUTHOR>
     * @Description 经销商自有商品作废方法
     * @Date 2019/3/12
     * @Param [session]
     **/
    public ValueHolder voidSellOwnGoods(QuerySession session) {
        DefaultWebEvent event = session.getEvent();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(
                event.getParameterValue("param"), "yyyy-MM-dd HH:mm:ss", SerializerFeature.WriteMapNullValue),
                Feature.OrderedField);
        log.debug(LogUtil.format("请求JSON：")+param.toString());

        //生成作废Json数组
        JSONArray auditArray = StBeanUtils.makeAuditJsonArray(param);
        HashMap<Long, Object> errorMap = new HashMap<Long, Object>();
        ValueHolder valueHolder = new ValueHolder();

        for (int i = 0; i < auditArray.size(); i++) {
            Long id = Long.valueOf(auditArray.get(i).toString());
            try {
                //作废验证
                voidCheck(id);
                StCSellOwngoodsDO stCSellOwngoodsDO = new StCSellOwngoodsDO();
                stCSellOwngoodsDO.setId(id);
                stCSellOwngoodsDO.setBillStatus(StConstant.CON_BILL_STATUS_03);//更改单据状态
                stCSellOwngoodsDO.setIsactive(StConstant.ISACTIVE_N);//作废
                makeCloserField(stCSellOwngoodsDO, session.getUser());
                //更新单据状态
                int count = stCSellOwngoodsMapper.updateById(stCSellOwngoodsDO);
                if (count < 0) {
                    throw new Exception();
                }
            } catch (Exception e) {
                errorMap.put(id, e.getMessage());
            }
        }
        valueHolder = StBeanUtils.getExcuteValueHolder(auditArray.size(),errorMap);

        return valueHolder;
    }

    /**
     * @return com.alibaba.fastjson.JSONObject
     * <AUTHOR>
     * @Description 作废验证
     * @Date 2019/3/11
     * @Param [objid]
     **/
    private JSONObject voidCheck(Long objid) throws Exception {
        //记录不存在
        StCSellOwngoodsDO stCSellOwngoodsDO = stCSellOwngoodsMapper.selectById(objid);
        if (stCSellOwngoodsDO == null) {
            throw new Exception("当前记录已不存在！");
        } else {
            if (StConstant.CON_BILL_STATUS_02.equals(stCSellOwngoodsDO.getBillStatus())) {
                throw new NDSException("当前记录已审核，不允许作废！");
            }else if(StConstant.CON_BILL_STATUS_03.equals(stCSellOwngoodsDO.getBillStatus())){
                throw new NDSException("当前记录已作废，不允许作废！");
            }else if(StConstant.CON_BILL_STATUS_04.equals(stCSellOwngoodsDO.getBillStatus())){
                throw new NDSException("当前记录已结案，不允许作废！");
            }
        }
        return null;
    }

    /**
     * @return void
     * <AUTHOR>
     * @Description 组装作废bean
     * @Date 2019/3/11
     * @Param [stCSellOwngoodsDO, user]
     **/
    public static void makeCloserField(StCSellOwngoodsDO stCSellOwngoodsDO, User user) {
        stCSellOwngoodsDO.setDelid(Long.valueOf(user.getId()));//作废人ID
        stCSellOwngoodsDO.setDelname(user.getName());
        stCSellOwngoodsDO.setDelename(user.getEname());//作废人账号
        stCSellOwngoodsDO.setDelTime(new Date());
        stCSellOwngoodsDO.setModifierid(Long.valueOf(user.getId()));//修改人Id
        stCSellOwngoodsDO.setModifiername(user.getName());
        stCSellOwngoodsDO.setModifierename(user.getEname());
        stCSellOwngoodsDO.setModifieddate(new Date());
    }
}
