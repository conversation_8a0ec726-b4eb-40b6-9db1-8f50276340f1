package com.jackrain.nea.st.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.st.annotation.StOperationLog;
import com.jackrain.nea.st.common.StConstant;
import com.jackrain.nea.st.mapper.StCUnfullcarCostItemMapper;
import com.jackrain.nea.st.mapper.StCUnfullcarCostMapper;
import com.jackrain.nea.st.model.enums.CloseStatusEnum;
import com.jackrain.nea.st.model.enums.SubmitStatusEnum;
import com.jackrain.nea.st.model.table.StCUnfullcarCost;
import com.jackrain.nea.st.model.table.StCUnfullcarCostItem;
import com.jackrain.nea.st.utils.ValueHolderUtils;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * <AUTHOR>
 * @Date 2022/6/10 10:35
 */
@Component
@Slf4j
public class StCUnfullcarCostDelService extends CommandAdapter {

    @Autowired
    private StCUnfullcarCostMapper stCUnfullcarCostMapper;

    @Autowired
    private StCUnfullcarCostItemMapper stCUnfullcarCostItemMapper;

    @StOperationLog(operationType = "DEL", mainTableName = "ST_C_UNFULLCAR_COST", itemsTableName = "ST_C_UNFULLCAR_COST_ITEM")
    @Override
    public ValueHolder execute(QuerySession querySession) throws NDSException {
        DefaultWebEvent event = querySession.getEvent();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(
                event.getParameterValue("param"), "yyyy-MM-dd HH:mm:ss"), Feature.OrderedField);
        String isDel = param.getString("isdelmtable");
        Long objid = param.getLong("objid");
        //判断主表是否存在
        StCUnfullcarCost unfullcarCost = stCUnfullcarCostMapper.selectById(objid);
        if (unfullcarCost == null){
            return ValueHolderUtils.getFailValueHolder("当前记录不存在！");
        }
        //判断是主表删除还是明细删除
        if (StConstant.FALSE_STR.equals(isDel)){
            //单独删除明细
            //主表ID
            if (CloseStatusEnum.CLOSE.getKey().equals(unfullcarCost.getCloseStatus())){
                return ValueHolderUtils.getFailValueHolder("当前记录已结案，不允许删除！");
            }
            if (SubmitStatusEnum.SUBMIT.getKey().equals(unfullcarCost.getStatus())){
                return ValueHolderUtils.getFailValueHolder("当前记录已提交，不允许删除！");
            }
            JSONObject tabitem = param.getJSONObject("tabitem");
            JSONArray unfullcarCostItemArray = tabitem.getJSONArray(StConstant.TAB_ST_C_UNFULLCAR_COST_ITEM);
            if (unfullcarCostItemArray != null && unfullcarCostItemArray.size() > 0) {
                return delUnfullcarCostItemArray(unfullcarCostItemArray, querySession);
            }else {
                return ValueHolderUtils.getFailValueHolder("请选择需要删除的明细数据！");
            }
        }else {
            if (!SubmitStatusEnum.NO_SUBMIT.getKey().equals(unfullcarCost.getStatus())){
                return ValueHolderUtils.getFailValueHolder("当前单据状态，不允许删除！");
            }
            return delUnfullcarCost(objid);
        }
    }

    private ValueHolder delUnfullcarCost(Long objid) {
        //删除明细
        stCUnfullcarCostItemMapper.delete(new LambdaQueryWrapper<StCUnfullcarCostItem>().in(StCUnfullcarCostItem::getUnfullcarCostId, objid));
        //删除主表
        if (stCUnfullcarCostMapper.deleteById(objid) <= 0) {
            return ValueHolderUtils.getFailValueHolder("当前记录不存在！");
        }
        return ValueHolderUtils.getDeleteSuccessValueHolder();
    }

    private ValueHolder delUnfullcarCostItemArray(JSONArray unfullcarCostItemArray, QuerySession querySession) {
        List<Long> ids = JSONObject.parseArray(unfullcarCostItemArray.toJSONString(), Long.class);
        List<StCUnfullcarCostItem> stCUnfullcarCostItems = stCUnfullcarCostItemMapper.selectBatchIds(ids);
        if (!CollectionUtils.isEmpty(stCUnfullcarCostItems)) {
            Map<Long, String> beforeDelObjMap = new HashMap<>();
            for (StCUnfullcarCostItem item : stCUnfullcarCostItems) {
                beforeDelObjMap.put(item.getId(), JSON.toJSONString(item));
            }
            querySession.setAttribute("beforeDelObjMap", beforeDelObjMap);
        }
        stCUnfullcarCostItemMapper.deleteBatchIds(ids);
        return ValueHolderUtils.getDeleteSuccessValueHolder();
    }


}
