package com.jackrain.nea.st.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.st.annotation.StOperationLog;
import com.jackrain.nea.st.common.StConstant;
import com.jackrain.nea.st.mapper.StCExpressPriceStrategyItemMapper;
import com.jackrain.nea.st.mapper.StCExpressPriceStrategyMapper;
import com.jackrain.nea.st.model.enums.StCExpressPriceStrategyEnum;
import com.jackrain.nea.st.model.table.StCExpressPriceStrategyDO;
import com.jackrain.nea.st.model.table.StCExpressPriceStrategyItemDO;
import com.jackrain.nea.st.utils.StBeanUtils;
import com.jackrain.nea.st.utils.ValueHolderUtils;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Date 2022/6/12 9:38
 * @Description 快递报价设置删除策略
 * @Version 1.0
 */
@Component
@Slf4j
public class StCExpressPriceStrategyDelService extends CommandAdapter {

    @Autowired
    private StCExpressPriceStrategyMapper stCExpressPriceStrategyMapper;

    @Autowired
    private StCExpressPriceStrategyItemMapper stCExpressPriceStrategyItemMapper;

    @StOperationLog(operationType = "DEL", mainTableName = "ST_C_EXPRESS_PRICE_STRATEGY", itemsTableName = "ST_C_EXPRESS_PRICE_STRATEGY_ITEM")
    @Override
    public ValueHolder execute(QuerySession session){
        DefaultWebEvent event = session.getEvent();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(event.getParameterValue("param"), "yyyy-MM-dd HH:mm:ss"), Feature.OrderedField);
        //是否删除主表
        boolean delMainFlag = param.getBoolean("isdelmtable");
        Long id = param.getLong("objid");

        StCExpressPriceStrategyDO stCExpressPriceStrategyDO = stCExpressPriceStrategyMapper.selectById(id);
        if (stCExpressPriceStrategyDO == null){
            return ValueHolderUtils.getFailValueHolder("当前记录已不存在！");
        }

        if (delMainFlag) {
            // 删除主表
            if (Objects.nonNull(id) && id > 0) {
                //主表删除写在这里
                //判断前置条件
                ValueHolder check = check(stCExpressPriceStrategyDO);
                if (check != null) {
                    return check;
                }
                //先删明细表再删主表
                return delExpressPriceStrategy(id, session);
            }
        } else {
            //判断前置条件
            ValueHolder check = checkItem(stCExpressPriceStrategyDO);
            if (check != null) {
                return check;
            }
            // 删除明细
            JSONObject tabitem = param.getJSONObject("tabitem");
            if (!CollectionUtils.isEmpty(tabitem)) {
                JSONArray itemArray = tabitem.getJSONArray(StConstant.TAB_ST_C_EXPRESS_PRICE_STRATEGY_ITEM);
                JSONArray errorArray = new JSONArray();
                if (!CollectionUtils.isEmpty(itemArray)) {
                    deleteItemByID(itemArray, errorArray, session);
                }
                return StBeanUtils.getExcuteValueHolder(errorArray);
            }
        }
        throw new NDSException("当前记录已不存在！");
    }

    /**
     * 删除明细表明细
     *
     * @param itemArray
     * @param errorArray
     * @param session
     */
    private void deleteItemByID(JSONArray itemArray, JSONArray errorArray, QuerySession session) {
        List<Long> ids = JSONObject.parseArray(itemArray.toJSONString(), Long.class);
        if (!CollectionUtils.isEmpty(ids)) {
            List<StCExpressPriceStrategyItemDO> itemDOList = stCExpressPriceStrategyItemMapper.selectBatchIds(ids);
            if (!CollectionUtils.isEmpty(itemDOList)) {
                Map<Long, String> beforeDelObjMap = new HashMap<>();
                for (StCExpressPriceStrategyItemDO item : itemDOList) {
                    beforeDelObjMap.put(item.getId(), JSON.toJSONString(item));
                }
                session.setAttribute("beforeDelObjMap", beforeDelObjMap);
            }
        }
        for (int i = 0; i < itemArray.size(); i++) {
            Long itemid = itemArray.getLong(i);
            if ((stCExpressPriceStrategyItemMapper.deleteById(itemid)) <= 0) {
                errorArray.add(StBeanUtils.getJsonObjectInfo(itemid, "删除明细失败"));
            }
        }
    }

    /**
     * 主表删除方法
     * @param objid
     * @param querySession
     * @return
     */
    private ValueHolder delExpressPriceStrategy(Long objid, QuerySession querySession) {
        //删除明细
        stCExpressPriceStrategyItemMapper.delete(new LambdaQueryWrapper<StCExpressPriceStrategyItemDO>().in(StCExpressPriceStrategyItemDO::getStCExpressPriceStrategyId,objid));
        //删除主表
        if (stCExpressPriceStrategyMapper.deleteById(objid) <= 0){
            return ValueHolderUtils.getFailValueHolder("当前记录不存在！");
        }
        return ValueHolderUtils.getDeleteSuccessValueHolder();
    }

//    /**
//     * 明细表删除方法
//     * @param expressPriceStrategyItemArray
//     * @param querySession
//     * @return
//     */
//
//    private ValueHolder delExpressPriceStrategyItemArray(JSONArray expressPriceStrategyItemArray, QuerySession querySession) {
//        for (int i = 0; i < expressPriceStrategyItemArray.size(); i++) {
//            Long slaverId = expressPriceStrategyItemArray.getLong(i);
//            int deleteCount = stCExpressPriceStrategyItemMapper.deleteById(slaverId);
//            if (deleteCount <= 0) {
//                return ValueHolderUtils.getFailValueHolder("当前记录已不存在！");
//            }
//        }
//        return ValueHolderUtils.getDeleteSuccessValueHolder();
//    }

    /**
     * 主表前置条件删除验证
     * @param stCExpressPriceStrategyDO
     * @return
     */
    private ValueHolder check(StCExpressPriceStrategyDO stCExpressPriceStrategyDO) {
        log.info(LogUtil.format("验证单表单数据{}"), stCExpressPriceStrategyDO);

        Integer submitStatus = stCExpressPriceStrategyDO.getStatus();

        if (submitStatus != null) {
            if (!submitStatus.equals(StCExpressPriceStrategyEnum.UN_SUBMITTED.getKey())) {
                return ValueHolderUtils.getFailValueHolder("当前单据状态，不允许删除！");
            }
        }

        return null;
    }

    /**
     * 明细表前置条件删除校验
     * @param stCExpressPriceStrategyDO
     * @return
     */
    private ValueHolder checkItem(StCExpressPriceStrategyDO stCExpressPriceStrategyDO) {

        Integer submitStatus = stCExpressPriceStrategyDO.getStatus();
        Integer closeStatus = stCExpressPriceStrategyDO.getCloseStatus();

        if (closeStatus != null) {
            if (closeStatus.equals(StCExpressPriceStrategyEnum.CLOSED.getKey())) {
                return ValueHolderUtils.getFailValueHolder("当前记录已结案，不允许删除！");
            }
        }

        if (submitStatus != null) {
            if (submitStatus.equals(StCExpressPriceStrategyEnum.SUBMITTED.getKey())) {
                return ValueHolderUtils.getFailValueHolder("当前记录已提交，不允许删除！");
            }
        }

        return null;
    }

    /**
     * 修改主表信息
     * @param id
     * @param session
     */
    private void updateHoldOrder(Long id, QuerySession session) {
        StCExpressPriceStrategyDO stCExpressPriceStrategyDO =new StCExpressPriceStrategyDO();
        stCExpressPriceStrategyDO.setId(id);
        StBeanUtils.makeModifierField(stCExpressPriceStrategyDO,session.getUser());
        int n = stCExpressPriceStrategyMapper.updateById(stCExpressPriceStrategyDO);
        if (n<=0){
            throw new NDSException("修改主表的信息失败！");
        }
    }






}
