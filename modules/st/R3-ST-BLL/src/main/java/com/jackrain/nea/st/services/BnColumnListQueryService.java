package com.jackrain.nea.st.services;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.st.common.StConstant;
import com.jackrain.nea.st.mapper.StCBnColumnListMapper;
import com.jackrain.nea.st.model.request.BnColumnListQueryRequest;
import com.jackrain.nea.st.model.result.BnColumnListQueryResult;
import com.jackrain.nea.st.model.table.StCBnColumnListDO;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

/**
 * 班牛工单组件列表查询服务
 */
@Component
@Slf4j
@Transactional(readOnly = true)
public class BnColumnListQueryService extends CommandAdapter {

    @Autowired
    private StCBnColumnListMapper stCBnColumnListMapper;

    /**
     * 查询班牛工单组件列表
     *
     * @param request 查询请求
     * @return 查询结果
     */
    public ValueHolderV14<BnColumnListQueryResult> queryBnColumnList(BnColumnListQueryRequest request) {
        if (log.isDebugEnabled()) {
            log.debug("BnColumnListQueryService.queryBnColumnList, request: {}", JSONObject.toJSONString(request));
        }

        ValueHolderV14<BnColumnListQueryResult> result = new ValueHolderV14<>(ResultCode.SUCCESS, "SUCCESS");
        BnColumnListQueryResult queryResult = new BnColumnListQueryResult();

        try {
            // 构建查询条件
            LambdaQueryWrapper<StCBnColumnListDO> queryWrapper = new LambdaQueryWrapper<>();

            // 添加查询条件
            if (request != null) {
                if (request.getId() != null) {
                    queryWrapper.eq(StCBnColumnListDO::getId, request.getId());
                }

                if (request.getProjectId() != null) {
                    queryWrapper.eq(StCBnColumnListDO::getProjectId, request.getProjectId());
                }

                if (request.getColumnId() != null) {
                    queryWrapper.eq(StCBnColumnListDO::getColumnId, request.getColumnId());
                }

                if (StringUtils.isNotBlank(request.getName())) {
                    queryWrapper.like(StCBnColumnListDO::getName, request.getName());
                }

                if (StringUtils.isNotBlank(request.getColumnType())) {
                    queryWrapper.eq(StCBnColumnListDO::getColumnType, request.getColumnType());
                }

                if (StringUtils.isNotBlank(request.getType())) {
                    queryWrapper.eq(StCBnColumnListDO::getType, request.getType());
                }

                if (StringUtils.isNotBlank(request.getBehaviorType())) {
                    queryWrapper.eq(StCBnColumnListDO::getBehaviorType, request.getBehaviorType());
                }

                if (StringUtils.isNotBlank(request.getOptions())) {
                    queryWrapper.like(StCBnColumnListDO::getOptions, request.getOptions());
                }

                if (StringUtils.isNotBlank(request.getRelationOptions())) {
                    queryWrapper.like(StCBnColumnListDO::getRelationOptions, request.getRelationOptions());
                }

                if (StringUtils.isNotBlank(request.getSonColumnBos())) {
                    queryWrapper.like(StCBnColumnListDO::getSonColumnBos, request.getSonColumnBos());
                }

                if (request.getIsInside() != null) {
                    queryWrapper.eq(StCBnColumnListDO::getIsInside, request.getIsInside());
                }

                if (StringUtils.isNotBlank(request.getIsactive())) {
                    queryWrapper.eq(StCBnColumnListDO::getIsactive, request.getIsactive());
                } else {
                    // 默认只查询激活状态的记录
                    queryWrapper.eq(StCBnColumnListDO::getIsactive, StConstant.ISACTIVE_Y);
                }
            } else {
                // 默认只查询激活状态的记录
                queryWrapper.eq(StCBnColumnListDO::getIsactive, StConstant.ISACTIVE_Y);
            }

            // 按ID排序
            queryWrapper.orderByAsc(StCBnColumnListDO::getId);

            // 执行查询
            List<StCBnColumnListDO> columnListList = stCBnColumnListMapper.selectList(queryWrapper);
            
            // 设置查询结果
            queryResult.setColumnListList(columnListList != null ? columnListList : new ArrayList<>());

            result.setData(queryResult);

            if (log.isDebugEnabled()) {
                log.debug("查询班牛工单组件列表成功，找到{}条记录",
                        columnListList != null ? columnListList.size() : 0);
            }

            return result;
        } catch (Exception e) {
            log.error("查询班牛工单组件列表异常", e);
            result.setCode(ResultCode.FAIL);
            result.setMessage("查询班牛工单组件列表异常: " + e.getMessage());
            return result;
        }
    }
}
