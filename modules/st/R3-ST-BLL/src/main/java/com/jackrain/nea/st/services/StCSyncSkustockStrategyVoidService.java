package com.jackrain.nea.st.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.cpext.utils.ValueHolderUtils;
import com.jackrain.nea.st.common.StConstant;
import com.jackrain.nea.st.mapper.StCSyncSkustockStrategyMapper;
import com.jackrain.nea.st.model.table.StCSyncSkustockStrategyDO;
import com.jackrain.nea.st.utils.StBeanUtils;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

/**
 * @author: 汪聿森
 * @Date: Created in 2020-05-20 9:17
 * @Description : 作废
 */
@Component
@Slf4j
public class StCSyncSkustockStrategyVoidService extends CommandAdapter {
    @Autowired
    private StCSyncSkustockStrategyMapper stCSyncSkustockStrategyMapper;

    @Override
    public ValueHolder execute(QuerySession querySession) {
        ValueHolder vh = new ValueHolder();
        DefaultWebEvent event = querySession.getEvent();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(
                event.getParameterValue("param"), "yyyy-MM-dd HH:mm:ss"),
                Feature.OrderedField);
        if (param == null || param.isEmpty()) {
            return ValueHolderUtils.fail(Resources.getMessage("参数不能为空！", querySession.getUser().getLocale()));
        }
        // 动作定义列表页面传参为数组，单对象为单个单据
        Boolean isObj = true;
        JSONArray ids = new JSONArray();
        if (param.containsKey("ids")) {
            ids = param.getJSONArray("ids");
            isObj = false;
        } else {
            ids.add(param.getLong("objid"));
        }
        if (CollectionUtils.isEmpty(ids)) {
            return ValueHolderUtils.fail(Resources.getMessage("请选择要审核的数据！", querySession.getUser().getLocale()));
        }
        JSONArray errorArr = new JSONArray();
        Integer accSuccess = 0, accFailed = 0;
        for (int i = 0; i < ids.size(); i++) {
            Long objId = ids.getLong(i);
            StCSyncSkustockStrategyDO stcMainDo = stCSyncSkustockStrategyMapper.selectById(objId);
            if (stcMainDo == null) {
                accFailed = errorRecord(objId, "当前记录已不存在!", errorArr, accFailed);
                continue;
            }
            //不是未审核，不允许作废
            if (!StConstant.SKUSTOCK_STATUS_01.equals(stcMainDo.getBillStatus())) {
                accFailed = errorRecord(objId, "方案状态不是未审核，不允许作废！", errorArr, accFailed);
                continue;
            }
            StBeanUtils.makeModifierField(stcMainDo, querySession.getUser());//修改信息
            stcMainDo.setBillStatus(StConstant.SKUSTOCK_STATUS_03);
            if ((stCSyncSkustockStrategyMapper.updateById(stcMainDo)) <= 0) {
                accFailed = errorRecord(objId, "方案:" + stcMainDo.getTemplateName() + ",作废失败！", errorArr, accFailed);
                continue;
            }
            accSuccess++;
        }
        String message = "作废成功记录数：" + accSuccess;
        if (!CollectionUtils.isEmpty(errorArr)) {
            if (isObj) {
                vh = ValueHolderUtils.fail(errorArr.getJSONObject(0).getString("message"));
            } else {
                vh = ValueHolderUtils.fail(message + "，作废失败记录数：" + accFailed, errorArr);
            }
        } else {
            vh = ValueHolderUtils.success(message);
        }
        return vh;
    }

    /**
     * 错误数据收集
     *
     * @param objid
     * @param message
     * @param errorArr
     */
    public Integer errorRecord(Long objid, String message, JSONArray errorArr, Integer accFailed) {
        JSONObject errorDate = new JSONObject();
        errorDate.put("code", ResultCode.FAIL);
        errorDate.put("objid", objid);
        errorDate.put("message", message);
        errorArr.add(errorDate);
        return ++accFailed;
    }
}
