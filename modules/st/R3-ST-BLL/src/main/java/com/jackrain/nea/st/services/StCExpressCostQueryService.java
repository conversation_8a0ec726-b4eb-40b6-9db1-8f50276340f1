package com.jackrain.nea.st.services;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.oc.oms.model.enums.YesNoEnum;
import com.jackrain.nea.st.common.StConstant;
import com.jackrain.nea.st.mapper.StCExpressCostItemMapper;
import com.jackrain.nea.st.mapper.StCExpressCostMapper;
import com.jackrain.nea.st.model.enums.CloseStatusEnum;
import com.jackrain.nea.st.model.enums.SubmitStatusEnum;
import com.jackrain.nea.st.model.request.StCExpressCostDetailQueryRequest;
import com.jackrain.nea.st.model.request.StCExpressCostQueryRequest;
import com.jackrain.nea.st.model.request.StCExpressCostRelationQueryRequest;
import com.jackrain.nea.st.model.result.StCExpressCostDetailQueryResult;
import com.jackrain.nea.st.model.result.StCExpressCostQueryResult;
import com.jackrain.nea.st.model.result.StCExpressCostRelation;
import com.jackrain.nea.st.model.table.StCExpressCost;
import com.jackrain.nea.st.model.table.StCExpressCostItem;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 快运报价设置查询服务
 */
@Component
@Slf4j
@Transactional(readOnly = true)
public class StCExpressCostQueryService {

    @Autowired
    private StCExpressCostMapper stCExpressCostMapper;

    @Autowired
    private StCExpressCostItemMapper stCExpressCostItemMapper;

    /**
     * 查询快运报价设置
     *
     * @param request 查询请求
     * @return 查询结果
     */
    public ValueHolderV14<StCExpressCostQueryResult> queryExpressCost(StCExpressCostQueryRequest request) {
        if (log.isDebugEnabled()) {
            log.debug("StCExpressCostQueryService.queryExpressCost, request: {}", JSONObject.toJSONString(request));
        }

        ValueHolderV14<StCExpressCostQueryResult> result = new ValueHolderV14<>(ResultCode.SUCCESS, "SUCCESS");

        // 参数校验
        if (request == null || request.getProvinceId() == null || request.getTotalWeight() == null) {
            result.setCode(ResultCode.FAIL);
            result.setMessage("查询快运报价设置参数不完整");
            return result;
        }

        StCExpressCostQueryResult queryResult = new StCExpressCostQueryResult();
        Date currentDate = new Date();

        try {
            // 查询符合条件的快运报价设置
            LambdaQueryWrapper<StCExpressCost> costWrapper = new LambdaQueryWrapper<>();
            costWrapper.eq(StCExpressCost::getIsactive, StConstant.ISACTIVE_Y);
            costWrapper.eq(StCExpressCost::getStatus, SubmitStatusEnum.SUBMIT.getKey());
            costWrapper.eq(StCExpressCost::getCloseStatus, CloseStatusEnum.NO_CLOSE.getKey());
            costWrapper.le(StCExpressCost::getStartDate, currentDate);
            costWrapper.ge(StCExpressCost::getEndDate, currentDate);

            // 如果指定了仓库ID列表，则添加条件
            if (!CollectionUtils.isEmpty(request.getWarehouseIdList())) {
                costWrapper.in(StCExpressCost::getCpCPhyWarehouseId, request.getWarehouseIdList());
            }

            // 如果指定了物流公司ID列表，则添加条件
            if (!CollectionUtils.isEmpty(request.getLogisticsIdList())) {
                costWrapper.in(StCExpressCost::getCpCLogisticsId, request.getLogisticsIdList());
            }

            // 查询快运报价设置
            List<StCExpressCost> expressCostList = stCExpressCostMapper.selectList(costWrapper);

            if (CollectionUtils.isEmpty(expressCostList)) {
                log.debug("未找到符合条件的快运报价设置");
                result.setData(queryResult);
                return result;
            }

            // 获取所有快运报价设置ID
            List<Long> expressCostIds = expressCostList.stream()
                    .map(StCExpressCost::getId)
                    .collect(Collectors.toList());

            // 查询快运报价设置明细
            LambdaQueryWrapper<StCExpressCostItem> itemWrapper = new LambdaQueryWrapper<>();
            itemWrapper.eq(StCExpressCostItem::getIsactive, StConstant.ISACTIVE_Y);
            itemWrapper.in(StCExpressCostItem::getStCExpressCostId, expressCostIds);
            itemWrapper.eq(StCExpressCostItem::getProvinceId, request.getProvinceId());

            // 根据重量范围筛选
            itemWrapper.le(StCExpressCostItem::getStartWeight, request.getTotalWeight());
            itemWrapper.ge(StCExpressCostItem::getEndWeight, request.getTotalWeight());

            List<StCExpressCostItem> expressCostItemList = stCExpressCostItemMapper.selectList(itemWrapper);

            Set<Long> existMainTableIdSet = expressCostItemList.stream().map(StCExpressCostItem::getStCExpressCostId).collect(Collectors.toSet());
            expressCostList = expressCostList.stream().filter(o -> existMainTableIdSet.contains(o.getId())).collect(Collectors.toList());

            // 设置查询结果
            queryResult.setExpressCostList(expressCostList);
            queryResult.setExpressCostItemList(expressCostItemList);

            result.setData(queryResult);

            if (log.isDebugEnabled()) {
                log.debug("查询快运报价设置成功，找到{}条主表记录，{}条明细记录",
                        expressCostList.size(), expressCostItemList.size());
            }

            return result;
        } catch (Exception e) {
            log.error("查询快运报价设置异常", e);
            result.setCode(ResultCode.FAIL);
            result.setMessage("查询快运报价设置异常: " + e.getMessage());
            return result;
        }
    }

    /**
     * 根据报价ID和重量查询快运报价明细
     *
     * @param request 查询请求
     * @return 查询结果
     */
    public ValueHolderV14<StCExpressCostDetailQueryResult> queryExpressCostDetail(StCExpressCostDetailQueryRequest request) {
        if (log.isDebugEnabled()) {
            log.debug("StCExpressCostQueryService.queryExpressCostDetail, request: {}", JSONObject.toJSONString(request));
        }

        ValueHolderV14<StCExpressCostDetailQueryResult> result = new ValueHolderV14<>(ResultCode.SUCCESS, "SUCCESS");

        // 参数校验
        if (request == null || request.getExpressCostId() == null || request.getWeight() == null) {
            result.setCode(ResultCode.FAIL);
            result.setMessage("查询快运报价明细参数不完整");
            return result;
        }

        StCExpressCostDetailQueryResult queryResult = new StCExpressCostDetailQueryResult();

        try {
            // 查询快运报价设置是否存在
            StCExpressCost expressCost = stCExpressCostMapper.selectById(request.getExpressCostId());
            if (expressCost == null) {
                log.debug("未找到快运报价设置，ID: {}", request.getExpressCostId());
                result.setData(queryResult);
                return result;
            }

            // 构建查询条件
            LambdaQueryWrapper<StCExpressCostItem> itemWrapper = new LambdaQueryWrapper<>();
            itemWrapper.eq(StCExpressCostItem::getIsactive, StConstant.ISACTIVE_Y);
            itemWrapper.eq(StCExpressCostItem::getStCExpressCostId, request.getExpressCostId());

            // 如果指定了省份，则添加条件
            if (request.getProvinceId() != null) {
                itemWrapper.eq(StCExpressCostItem::getProvinceId, request.getProvinceId());
            }

            // 根据重量范围筛选
            itemWrapper.le(StCExpressCostItem::getStartWeight, request.getWeight());
            itemWrapper.ge(StCExpressCostItem::getEndWeight, request.getWeight());

            List<StCExpressCostItem> expressCostItemList = stCExpressCostItemMapper.selectList(itemWrapper);

            // 设置查询结果
            queryResult.setExpressCostItemList(expressCostItemList);

            result.setData(queryResult);

            return result;
        } catch (Exception e) {
            log.error("查询快运报价明细异常", e);
            result.setCode(ResultCode.FAIL);
            result.setMessage("查询快运报价明细异常: " + e.getMessage());
            return result;
        }
    }

    /**
     * 根据实体仓ID和物流公司ID查询快运报价关系
     *
     * @param request 查询请求
     * @return 快运报价关系列表
     */
    public List<StCExpressCostRelation> queryExpressCostRelation(StCExpressCostRelationQueryRequest request) {
        if (log.isDebugEnabled()) {
            log.debug("StCExpressCostQueryService.queryExpressCostRelation request: {}",
                    JSONObject.toJSONString(request));
        }

        if (request == null) {
            log.error("查询快运报价关系请求参数为空");
            return new ArrayList<>();
        }
        if (request.getCpCPhyWarehouseId() == null) {
            log.error("实体仓不能为空!");
            return new ArrayList<>();
        }
        if (request.getCpCLogisticsId() == null) {
            log.error("物流公司不能为空!");
            return new ArrayList<>();
        }

        List<StCExpressCostRelation> relations = new ArrayList<>();
        Date currentDate = new Date();

        try {
            // 查询符合条件的快运报价设置主表
            LambdaQueryWrapper<StCExpressCost> costWrapper = new LambdaQueryWrapper<>();
            costWrapper.eq(StCExpressCost::getCpCPhyWarehouseId, request.getCpCPhyWarehouseId())
                    .eq(StCExpressCost::getCpCLogisticsId, request.getCpCLogisticsId())
                    .eq(StCExpressCost::getStatus, SubmitStatusEnum.SUBMIT.getKey())
                    .eq(StCExpressCost::getCloseStatus, CloseStatusEnum.NO_CLOSE.getKey())
                    .ge(StCExpressCost::getEndDate, currentDate)
                    .eq(StCExpressCost::getIsactive, StConstant.ISACTIVE_Y);

            List<StCExpressCost> expressCostList = stCExpressCostMapper.selectList(costWrapper);
            if (CollectionUtils.isEmpty(expressCostList)) {
                return new ArrayList<>();
            }

            // 获取所有快运报价设置ID
            List<Long> expressCostIds = expressCostList.stream()
                    .map(StCExpressCost::getId)
                    .collect(Collectors.toList());

            // 查询快运报价设置明细
            LambdaQueryWrapper<StCExpressCostItem> itemWrapper = new LambdaQueryWrapper<>();
            itemWrapper.in(StCExpressCostItem::getStCExpressCostId, expressCostIds)
                    .eq(StCExpressCostItem::getIsactive, StConstant.ISACTIVE_Y);

            List<StCExpressCostItem> expressCostItemList = stCExpressCostItemMapper.selectList(itemWrapper);
            if (CollectionUtils.isEmpty(expressCostItemList)) {
                return new ArrayList<>();
            }

            // 按主表ID分组明细
            Map<Long, List<StCExpressCostItem>> itemGroupMap = expressCostItemList.stream()
                    .collect(Collectors.groupingBy(StCExpressCostItem::getStCExpressCostId));

            // 构建返回结果
            for (StCExpressCost expressCost : expressCostList) {
                StCExpressCostRelation relation = new StCExpressCostRelation();
                relation.setStCExpressCost(expressCost);
                relation.setStCExpressCostItemList(itemGroupMap.get(expressCost.getId()));
                relations.add(relation);
            }

            return relations;
        } catch (Exception e) {
            log.error("查询快运报价关系异常", e);
            return new ArrayList<>();
        }
    }

    /**
     * 根据主表ID查询快运报价关系
     *
     * @param id 快运报价主表ID
     * @return 快运报价关系对象，包含主表信息和明细列表
     */
    public StCExpressCostRelation queryExpressCostRelationById(Long id) {
        if (id == null) {
            log.error("主表ID不能为空!");
            return null;
        }

        // 根据ID查询主表信息
        StCExpressCost expressCost = stCExpressCostMapper.selectById(id);
        if (expressCost == null) {
            log.error("未找到ID为{}的快运报价设置!", id);
            return null;
        }

        // 查询对应的明细信息
        List<StCExpressCostItem> expressCostItemList = stCExpressCostItemMapper.selectList(
                new LambdaQueryWrapper<StCExpressCostItem>()
                        .eq(StCExpressCostItem::getStCExpressCostId, id)
                        .eq(StCExpressCostItem::getIsactive, YesNoEnum.Y.getKey())
        );

        // 构建返回对象
        StCExpressCostRelation relation = new StCExpressCostRelation();
        relation.setStCExpressCost(expressCost);
        relation.setStCExpressCostItemList(expressCostItemList);

        return relation;
    }
}
