package com.jackrain.nea.st.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.base.Throwables;
import com.google.common.collect.Lists;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.cpext.api.CpCAnchorArchivesQueryCmd;
import com.jackrain.nea.cpext.api.CpCMainbodyQueryCmd;
import com.jackrain.nea.cpext.model.table.CpCAnchorArchives;
import com.jackrain.nea.cpext.model.table.CpCMainbody;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.ext.sequence.util.SequenceGenUtil;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.st.common.StConstant;
import com.jackrain.nea.st.mapper.LiveCastStrategyItemMapper;
import com.jackrain.nea.st.mapper.LiveCastStrategyMapper;
import com.jackrain.nea.st.model.common.StLiveCastStrategyConstants;
import com.jackrain.nea.st.model.table.AcFManageDO;
import com.jackrain.nea.st.model.table.StCLiveCastStrategyDO;
import com.jackrain.nea.st.model.table.StCLiveCastStrategyItemDO;
import com.jackrain.nea.st.utils.*;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import com.jackrain.nea.web.query.QueryUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 策略解析业务逻辑层
 * 包括各种行为操作
 * 包括主表明细表
 */
@Service
@Slf4j
public class LiveCastStrategyService {

    // 主表
    @Autowired
    LiveCastStrategyMapper liveCastStrategyMapper;
    // 明细表
    @Autowired
    LiveCastStrategyItemMapper liveCastStrategyItemMapper;

    @Reference(version = "1.0", group = "cp-ext")
    CpCAnchorArchivesQueryCmd cpCAnchorArchivesQueryCmd;

    @Reference(version = "1.0", group = "cp-ext")
    CpCMainbodyQueryCmd cpCMainbodyQueryCmd;

    // 入口
    /**
     * 审核
     * @param session
     * @param strategyIds
     */
    public void approve(QuerySession session, List<Long> strategyIds) {
        if(CollectionUtils.isNotEmpty(strategyIds)) {
            // 手动审核，删除可审核的REDIS KEY
            List<StCLiveCastStrategyDO> list = liveCastStrategyMapper.selectList(
                    new LambdaQueryWrapper<StCLiveCastStrategyDO>()
                            .eq(StCLiveCastStrategyDO::getStrategyStatus, StConstant.LIVE_STRATEGY_STATUS_INIT)
                            .in(StCLiveCastStrategyDO::getId, strategyIds));
            if (CollectionUtils.isNotEmpty(list)) {
                for (StCLiveCastStrategyDO st : list) {
                    // 更新策略结束时间，删除REDIS KEY
                    RedisCacheUtil.delete(st.getCpCShopId(), RedisConstant.SHOP_LIVE_CAST_STRATEGY);
                }
            }
            String message = "处理失败，部分数据已发生变更或者状态不为初始化状态，请确认后重试";
            updateStrategyStatus(session, strategyIds, StConstant.LIVE_STRATEGY_STATUS_INIT, StConstant.LIVE_STRATEGY_STATUS_APPROVED, message);
        }
    }

    /**
     * 结案
     * @param session
     * @param strategyIds
     */
    public void finish(QuerySession session, List<Long> strategyIds) {
        if(CollectionUtils.isNotEmpty(strategyIds)) {
            // 手动结案，删除可结案的REDIS KEY
            List<StCLiveCastStrategyDO> list = liveCastStrategyMapper.selectList(
                    new LambdaQueryWrapper<StCLiveCastStrategyDO>()
                            .eq(StCLiveCastStrategyDO::getStrategyStatus, StConstant.LIVE_STRATEGY_STATUS_APPROVED)
                            .in(StCLiveCastStrategyDO::getId, strategyIds));
            if (CollectionUtils.isNotEmpty(list)) {
                for (StCLiveCastStrategyDO st : list) {
                    // 更新策略结束时间，删除REDIS KEY
                    RedisCacheUtil.delete(st.getCpCShopId(), RedisConstant.SHOP_LIVE_CAST_STRATEGY);
                }
            }
            String message = "处理失败，部分数据已发生变更或者状态不为审核通过状态，请确认后重试";
            updateStrategyStatus(session, strategyIds, StConstant.LIVE_STRATEGY_STATUS_APPROVED, StConstant.LIVE_STRATEGY_STATUS_FINISHED, message);
        }
    }

    /**
     * 作废
     * @param session
     * @param strategyIds
     */
    public void aVoid(QuerySession session, List<Long> strategyIds) {
        if(!CollectionUtils.isEmpty(strategyIds)) {
            String message = "处理失败，部分数据已发生变更或者状态不为初始化状态，请确认后重试";
            updateStrategyStatus(session, strategyIds, StConstant.LIVE_STRATEGY_STATUS_INIT, StConstant.LIVE_STRATEGY_STATUS_VOID, message);
        }
    }

    /**
     * updateStatus
     * @param session
     * @param strategyIds
     * @param status
     * @param statusNew
     */
    public void updateStrategyStatus(QuerySession session, List<Long> strategyIds, String status, String statusNew, String message) {
        Integer userId = session.getUser().getId();
        String userName = session.getUser().getName();
        String userEName = session.getUser().getEname();

        // 更新状态
        int c = liveCastStrategyMapper.updateStrategyStatus(status, statusNew, strategyIds, userId, userName, userEName);

        if(c < strategyIds.size()) {
            log.error(LogUtil.format("{}:{}"), message, strategyIds);
            throw new NDSException(message);
        }
    }

    /**
     * 查询未结案数据，限制查询数量
     * @param limit
     * @return
     */
    public List<StCLiveCastStrategyDO> queryTimeoutStrategy(Integer limit) {
        return liveCastStrategyMapper.selectList(
                new LambdaQueryWrapper<StCLiveCastStrategyDO>()
                .le(StCLiveCastStrategyDO::getModifieddate, new Date(System.currentTimeMillis() + StConstant.LIVE_STRATEGY_CLOSE_TIMEOUT))
                .last("limit " + limit));
    }

    /**
     * saveOrUpdateStrategy 保存或更新
     * @param liveCastStrategyDO
     * @param liveCastStrategyItemDOList
     */
    @Transactional(rollbackFor = Exception.class)
    public void saveOrUpdateStrategy(QuerySession session, StCLiveCastStrategyDO liveCastStrategyDO, List<StCLiveCastStrategyItemDO> liveCastStrategyItemDOList) {
        // 保存 or 更新
        if(Objects.nonNull(liveCastStrategyDO)) {
            if(Objects.isNull(liveCastStrategyDO.getId())) {
                // 插入主表
                insert(session, liveCastStrategyDO);
            } else {
                // 更新
                update(session, liveCastStrategyDO);
            }

            // 保存明细
            // 存ID
            if (!CollectionUtils.isEmpty(liveCastStrategyItemDOList)) {
                liveCastStrategyItemDOList.forEach(item -> {
                    item.setStCLiveCastId(liveCastStrategyDO.getId());
                });

                saveItems(session, liveCastStrategyItemDOList);
            }
        }
    }

    // 直播解析策略主表
    /**
     * create 新增
     * @param liveCastStrategyDO
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public int insert(QuerySession session, StCLiveCastStrategyDO liveCastStrategyDO) {
        if(Objects.nonNull(liveCastStrategyDO)) {
            liveCastStrategyDO.setId(ModelUtil.getSequence(StConstant.TAB_ST_C_LIVE_CAST_STRATEGY));
            StBeanUtils.makeCreateField(liveCastStrategyDO, session.getUser());
            return liveCastStrategyMapper.insert(liveCastStrategyDO);
        }

        return 0;
    }

    /**
     * 更新结束时间
     * @param id
     * @param endTime
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateEndTime(QuerySession session, Long id, Date endTime) {
        // 查询
        StCLiveCastStrategyDO liveCastStrategyDO = liveCastStrategyMapper.selectById(id);
        // 赋值
        if (Objects.isNull(liveCastStrategyDO)) {
            throw new NDSException("修改结束时间失败，依据ID未查询到记录：" + id);
        }
        // ●状态必须为“已审核”，不满足则提示：“状态不是已审核，不允许延迟结束时间”。
        if (!StConstant.LIVE_STRATEGY_STATUS_APPROVED.equals(liveCastStrategyDO.getStrategyStatus())) {
            throw new NDSException("状态不是已审核，不允许延迟结束时间");
        }
        liveCastStrategyDO.setEndTime(endTime);
        checkTime(liveCastStrategyDO);

        // 更新
        update(session, liveCastStrategyDO);
        // 更新策略结束时间，删除REDIS KEY
        RedisCacheUtil.delete(liveCastStrategyDO.getCpCShopId(), RedisConstant.SHOP_LIVE_CAST_STRATEGY);

    }

    /**
     * update 修改
     * @param liveCastStrategyDO
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public int update(QuerySession session, StCLiveCastStrategyDO liveCastStrategyDO) {
        if(Objects.nonNull(liveCastStrategyDO)) {
            if(Objects.isNull(liveCastStrategyDO.getId()) || liveCastStrategyDO.getId() <= 0) {
                throw new NDSException("直播解析策略更新失败，ID为空：" + JSON.toJSONString(liveCastStrategyDO));
            }

            StBeanUtils.makeModifierField(liveCastStrategyDO, session.getUser());
            int r = liveCastStrategyMapper.updateById(liveCastStrategyDO);

            if(r <= 0) {
                throw new NDSException("直播解析策略更新失败，更新到的记录为0，id=" + liveCastStrategyDO.getId());
            }
        }

        return  0;
    }

    /**
     * delete 删除
     * @param strategyId
     * @return TODO 不需要返回
     */
    @Transactional(rollbackFor = Exception.class)
    public int delete(QuerySession session, Long strategyId) {
        if(Objects.nonNull(strategyId) && strategyId > 0) {
            int c = liveCastStrategyMapper.deleteById(strategyId);

            if(c > 0) {
                deleteItem(session, strategyId);
            }

            return c;
        }

        return 0;
    }

    /**
     * 删除列表
     * @param session
     * @param strategyIds
     * @return
     */
    public int delete(QuerySession session, List<Long> strategyIds) {
        if (!CollectionUtils.isEmpty(strategyIds)) {
            int c = liveCastStrategyMapper.deleteBatchIds(strategyIds);

            if (c > 0) {
                deleteItems(session, strategyIds);
            }
        }

        return 0;
    }

    /**
     * 保存：包括新增和修改
     * @param liveCastStrategyDO
     * @param id
     * @param strategy
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public int save(QuerySession session, StCLiveCastStrategyDO liveCastStrategyDO, Long id, JSONObject strategy) {
        log.info(LogUtil.format("LiveCastStrategyService.save{}"), strategy);
        User user = session.getUser();
        if(Objects.nonNull(liveCastStrategyDO)) {
            CpCMainbody acFManage =new CpCMainbody();
            CpCMainbody cooperate =new CpCMainbody();
            CpCAnchorArchives cpCAnchorArchives =new CpCAnchorArchives();
            Long acFManageId = liveCastStrategyDO.getAcFManageId();
            if (null != acFManageId) {
                ValueHolderV14 v14 = cpCMainbodyQueryCmd.cpCMainbodyQueryByID(acFManageId);
                acFManage = (CpCMainbody) v14.getData();
                log.info(LogUtil.format("调用ac服务的数据为1-》{}"), JSON.toJSONString(v14));
            }
            Long cooperateId = liveCastStrategyDO.getCooperateId();
            if (null != cooperateId) {
                ValueHolderV14 v14 = cpCMainbodyQueryCmd.cpCMainbodyQueryByID(cooperateId);
                cooperate = (CpCMainbody) v14.getData();
                log.info(LogUtil.format("调用ac服务的数据为2-》{}"), JSON.toJSONString(v14));
            }
            Long anchorArchivesId = liveCastStrategyDO.getAnchorArchivesId();
            if (null != anchorArchivesId) {
                cpCAnchorArchives = cpCAnchorArchivesQueryCmd.queryCpCAnchorArchivesById(anchorArchivesId);
                log.info(LogUtil.format("调用cp-ext服务的数据为2-》{}"), cpCAnchorArchives);
            }
            if( id > 0) {
                // 更新
                liveCastStrategyDO.setId(id);
                checkTime(liveCastStrategyDO);
                strategy.put("ID",id);
                strategy.put("MODIFIERID",Long.valueOf(user.getId()));
                strategy.put("MODIFIEDDATE",new Date());
                strategy.put("MODIFIERENAME",user.getEname());
                liveCastStrategyMapper.updateMainTable(StConstant.TAB_ST_C_LIVE_CAST_STRATEGY,strategy);
            } else {
                // 插入
                liveCastStrategyDO.setId(ModelUtil.getSequence(StConstant.TAB_ST_C_LIVE_CAST_STRATEGY));
                checkTime(liveCastStrategyDO);
                JSONObject sequence = new JSONObject();
                String str =SequenceGenUtil.generateSquence("SEQ_ST_C_LIVE_CAST_STRATEGY", sequence, session.getUser().getLocale(), false);
                Integer liveEvents =  Integer.valueOf(str);
                liveCastStrategyDO.setLiveEvents(liveEvents);
                liveCastStrategyDO.setAcFManageEcode(acFManage.getEcode());
                liveCastStrategyDO.setAcFManageEname(acFManage.getEname());
                liveCastStrategyDO.setCooperateEcode(cooperate.getEcode());
                liveCastStrategyDO.setCooperateEname(cooperate.getEname());
                liveCastStrategyDO.setAnchorId(cpCAnchorArchives == null ? null : cpCAnchorArchives.getAnchorId().toString());
                liveCastStrategyDO.setAnchorNickName(cpCAnchorArchives == null ? null : cpCAnchorArchives.getAnchorNickName());

                if (user!=null){
                    Date systemDate = new Date();
                    Long loginUserId = user.getId() == null ? null : user.getId().longValue();
                    liveCastStrategyDO.setAdClientId(Long.valueOf(user.getClientId()));
                    liveCastStrategyDO.setAdOrgId(Long.valueOf(user.getOrgId()));
                    liveCastStrategyDO.setIsactive("Y");
                    liveCastStrategyDO.setOwnerid(loginUserId);
                    liveCastStrategyDO.setOwnername(user.getName());
                    liveCastStrategyDO.setCreationdate(systemDate);
                    liveCastStrategyDO.setModifierid(loginUserId);
                    liveCastStrategyDO.setModifiername(user.getName());
                    liveCastStrategyDO.setModifieddate(systemDate);
                }
                liveCastStrategyMapper.insert(liveCastStrategyDO);
            }
        }

        return 0;
    }

    // 新增 开始时间,结束时间都不能为空,  更新可以,liveCastStrategyDO要有id
    /**
     * <AUTHOR>
     * @Date 14:37 2021/6/24
     * @Description 校验订单
     */
    private void checkTime(StCLiveCastStrategyDO liveCastStrategyDO) {
        Date startTime = liveCastStrategyDO.getStartTime();
        Date endTime = liveCastStrategyDO.getEndTime();
        StringBuffer sb = new StringBuffer();
        if (Objects.nonNull(startTime) && Objects.nonNull(endTime)) {
            // ●结束时间须大于开始时间，不满足则提示：“结束时间必须大于开始时间！”。
            // ●结束时间必须大于当前系统时间，不满足则提示：“结束时间必须大于当前系统时间！”。
            if (!endTime.after(new Date())) {
                sb.append("结束时间必须大于当前系统时间！");
            }
            if (!endTime.after(startTime)) {
                sb.append("结束时间必须大于开始时间！");
            }
        } else if (Objects.nonNull(startTime) || Objects.nonNull(endTime)) {
            // 修改操作未修改的值不会出过来,要查询数据库
            //todo 查询多次
            StCLiveCastStrategyDO select = liveCastStrategyMapper.selectById(liveCastStrategyDO.getId());
            if (Objects.nonNull(endTime)) {
                if (!endTime.after(new Date())) {
                    sb.append("结束时间必须大于当前系统时间！");
                }
                if (!endTime.after(select.getStartTime())) {
                    sb.append("结束时间必须大于开始时间！");
                }
            } else {
                if (!select.getEndTime().after(startTime)) {
                    sb.append("结束时间必须大于开始时间！");
                }
            }
        }
        if (org.apache.commons.lang3.StringUtils.isNotBlank(sb)) {
            throw new NDSException(sb.toString());
        }
    }

    /**
     * 按策略名称查找
     * @param strategyName
     * @return
     */
    public StCLiveCastStrategyDO findByStrategyName(String strategyName) {
        if (StringUtils.isEmpty(strategyName)) {
            return null;
        }

        return liveCastStrategyMapper.selectOne(new LambdaQueryWrapper<StCLiveCastStrategyDO>()
                .eq(StCLiveCastStrategyDO::getStrategyName, strategyName));
    }

    // 直播解析策略明细表

    /**
     * 新增单条记录
     * @param liveCastStrategyItemDO
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public int insertItem(QuerySession session, StCLiveCastStrategyItemDO liveCastStrategyItemDO) {
        if(Objects.nonNull(liveCastStrategyItemDO)) {
            liveCastStrategyItemDO.setId(ModelUtil.getSequence(StConstant.TAB_ST_C_LIVE_CAST_STRATEGY_ITEM));
            return liveCastStrategyItemMapper.insert(liveCastStrategyItemDO);
        }

        return 0;
    }

    @Transactional(rollbackFor = Exception.class)
    public int insertItems(QuerySession session, List<StCLiveCastStrategyItemDO> liveCastStrategyItemDOList) {
        if (!CollectionUtils.isEmpty(liveCastStrategyItemDOList)) {
            // 赋值ID
            liveCastStrategyItemDOList.forEach(item -> item.setId(ModelUtil.getSequence(StConstant.TAB_ST_C_LIVE_CAST_STRATEGY_ITEM)));
            return liveCastStrategyItemMapper.batchInsert(liveCastStrategyItemDOList);
        }

        return 0;
    }

    /**
     * update 单条记录
     * @param liveCastStrategyItemDO
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public int updateItem(QuerySession session, StCLiveCastStrategyItemDO liveCastStrategyItemDO) {
        if(Objects.nonNull(liveCastStrategyItemDO)) {
            if(Objects.isNull(liveCastStrategyItemDO.getId()) || liveCastStrategyItemDO.getId() <= 0) {
                throw new NDSException("直播解析策略明细更新失败，ID为空：" + JSON.toJSONString(liveCastStrategyItemDO));
            }

            int r = liveCastStrategyItemMapper.updateById(liveCastStrategyItemDO);

            if (r <= 0) {
                throw new NDSException("直播解析策略明细更新失败，更新到的记录为0，id=" + liveCastStrategyItemDO.getId());
            }
        }

        return 0;
    }

    /**
     * 更新
     * @param session
     * @param liveCastStrategyItemDOList
     * @return
     */
    public int updateItems(QuerySession session, List<StCLiveCastStrategyItemDO> liveCastStrategyItemDOList) {
        if(!CollectionUtils.isEmpty(liveCastStrategyItemDOList)) {
            // 没有批量更新的可能，所以这里暂且写个遍历
            int c = 0;

            LiveCastStrategyService bean = ApplicationContextHandle.getBean(LiveCastStrategyService.class);
            //TODO 验证
            for (StCLiveCastStrategyItemDO dto : liveCastStrategyItemDOList) {
                c = c + bean.updateItem(session, dto);
            }

            return c;
        }

        return 0;
    }

    /**
     * 删除
     * @param strategyId
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public int deleteItem(QuerySession session, Long strategyId) {
        if(Objects.nonNull(strategyId) && strategyId > 0) {
            return liveCastStrategyItemMapper.delete(new LambdaQueryWrapper<StCLiveCastStrategyItemDO>().eq(StCLiveCastStrategyItemDO::getStCLiveCastId, strategyId));
        }

        return 0;
    }

    /**
     * 列表删除
     * @param session
     * @param strategyIds
     * @return
     */
    public int deleteItems(QuerySession session, List<Long> strategyIds) {
        if (!CollectionUtils.isEmpty(strategyIds)) {
            return liveCastStrategyItemMapper.delete(new LambdaQueryWrapper<StCLiveCastStrategyItemDO>().in(StCLiveCastStrategyItemDO::getStCLiveCastId, strategyIds));
        }

        return 0;
    }

    /**
     * 明细批量删除
     * @param session
     * @param strategyItemsIds
     * @return
     */
    public int deleteItemsByIds(QuerySession session, List<Long> strategyItemsIds) {
        if (!CollectionUtils.isEmpty(strategyItemsIds)) {
            int c = liveCastStrategyItemMapper.deleteBatchIds(strategyItemsIds);

            if (c > 0) {
                return c;
            } else {
                throw new NDSException("明细删除失败，数据已被修改，请刷新重试");
            }
        }

        return 0;
    }

    /**
     * 批量保存明细：包括修改和新增
     * @param liveCastStrategyItemDOList
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public int saveItems(QuerySession session, List<StCLiveCastStrategyItemDO> liveCastStrategyItemDOList) {
        // 拆分保存和更新
        if (!CollectionUtils.isEmpty(liveCastStrategyItemDOList)) {
            List<StCLiveCastStrategyItemDO> updates = new ArrayList<>();
            List<StCLiveCastStrategyItemDO> inserts = new ArrayList<>();

            liveCastStrategyItemDOList.forEach(item -> {
                if(Objects.nonNull(item.getId()) && item.getId() > 0) {
                    updates.add(item);
                } else {
                    item.setId(ModelUtil.getSequence(StConstant.TAB_ST_C_LIVE_CAST_STRATEGY_ITEM));
                    inserts.add(item);
                }
            });

            int c = 0;
            // 保存
            updates.forEach(u -> StBeanUtils.makeModifierField(u, session.getUser()));
            c = c + updateItems(session, updates);
            // 插入
            inserts.forEach(i -> StBeanUtils.makeCreateField(i, session.getUser()));
            c = c + insertItems(session, inserts);

            return c;
        }

        return 0;
    }

    /**
     * 查找主表：findStrategyByShopIdAndDate
     * @param cpCShopId
     * @param orderDate
     * @param payTime
     * @return
     */
    public List<StCLiveCastStrategyDO> findStrategyByShopIdAndDate(Long cpCShopId, Date orderDate, Date payTime) {
        List<StCLiveCastStrategyDO> liveCastStrategyDOs = liveCastStrategyMapper.selectList(
                new LambdaQueryWrapper<StCLiveCastStrategyDO>()
                        .eq(StCLiveCastStrategyDO::getStrategyStatus, StConstant.LIVE_STRATEGY_STATUS_APPROVED)
                        .eq(StCLiveCastStrategyDO::getCpCShopId, cpCShopId).orderByDesc(StCLiveCastStrategyDO::getCreationdate));
        return liveCastStrategyDOs;
    }

    /**
     * 查找明细：findStrategyItemsByStrateById
     * @param strategyId
     * @return
     */
    public List<StCLiveCastStrategyItemDO> findStrategyItemsByStrateById(Long strategyId) {
        return liveCastStrategyItemMapper.selectList(new LambdaQueryWrapper<StCLiveCastStrategyItemDO>().eq(StCLiveCastStrategyItemDO::getStCLiveCastId, strategyId));
    }

    /**
     * 自动结案
     * @param session
     * @return
     */
    public int updateStrategyStatusByAuto(QuerySession session) {
        Integer userId = null;
        String userName = null;
        String userEName = null;

        if (Objects.nonNull(session) && Objects.nonNull(session.getUser())) {
            userId = session.getUser().getId();
            userName = session.getUser().getName();
            userEName = session.getUser().getEname();
        } else {
            userId = 893;
            userName = "系统管理员";
            userEName = "root";
        }

        String beforeStatus = StConstant.LIVE_STRATEGY_STATUS_APPROVED;    // 前置状态：审核状态
        String afterStatus = StConstant.LIVE_STRATEGY_STATUS_FINISHED;     // 后置状态：结案状态
        Integer offset = -7;     // 自动结案时间差：超过结束时间7天的已审核记录需要自动结案
        // 删除可以自动结案的REDIS KEY
        LocalDateTime localDateTime = LocalDateTime.now().minusDays(7L);
        List<StCLiveCastStrategyDO> list = liveCastStrategyMapper.selectList(
                new LambdaQueryWrapper<StCLiveCastStrategyDO>().
                        le(StCLiveCastStrategyDO::getEndTime,
                                Date.from(localDateTime.atZone(ZoneId.systemDefault()).toInstant())));
        if (CollectionUtils.isNotEmpty(list)) {
            for (StCLiveCastStrategyDO st : list) {
                // 更新策略结束时间，删除REDIS KEY
                RedisCacheUtil.delete(st.getCpCShopId(), RedisConstant.SHOP_LIVE_CAST_STRATEGY);
            }
        }
        return liveCastStrategyMapper.updateStrategyStatusByAuto(beforeStatus, afterStatus, offset, userId, userName, userEName);
    }

    /**
     * <AUTHOR>
     * @Description
     * @Date 17:08 2021/4/6
     * @Param
     * @return 直播策略列表显示
     */
    public ValueHolder queryList(QuerySession querySession) {
        ValueHolder vh = new ValueHolder();
        DefaultWebEvent event = querySession.getEvent();
        JSONObject param = JSON.parseObject(event.getParameterValue("param").toString());
        log.info(LogUtil.format("LiveCastStrategyService.queryList.param{}"), param.toJSONString());
        JSONObject fixedcolumns = param.getJSONObject("fixedcolumns");
        int range = param.getInteger("range") == null ? QueryUtils.getdefalutrange() : param.getInteger("range");
        Integer startIndex = param.getInteger("startindex") == null ? 0 : param.getInteger("startindex");
        //店铺id
        JSONArray cShopIds = fixedcolumns.getJSONArray("CP_C_SHOP_ID");
        String shopId = cShopIds == null || cShopIds.size() == 0 ? null : String.join(",", JSONObject.parseArray(cShopIds.toJSONString(), String.class));
        //方案名称
        String strategyName = fixedcolumns.getString("STRATEGY_NAME");
        //方案状态
        JSONArray strategyStatus = fixedcolumns.getJSONArray("STRATEGY_STATUS");
        //订单时间
        JSONArray billTimeType = fixedcolumns.getJSONArray("BILL_TIME_TYPE");
        String startTimeBegin ="",startTimeEnd="",endTimeBegin="",endTimeEnd="";
        //开始时间
        if (fixedcolumns.containsKey("START_TIME")){
            String startTime = fixedcolumns.getString("START_TIME");
            String[] startTimeDate = startTime.split("~");
            startTimeBegin = startTimeDate[0];
            startTimeEnd = startTimeDate[1];
        }
        //结束时间
        if (fixedcolumns.containsKey("END_TIME")){
            String endTime = fixedcolumns.getString("END_TIME");
            String[] endTimeDate = endTime.split("~");
            endTimeBegin = endTimeDate[0];
            endTimeEnd = endTimeDate[1];
        }
        //直播平台
        JSONArray livePlatform = fixedcolumns.getJSONArray("LIVE_PLATFORM");
        //主播id ANCHOR_ID
        String anchorId = fixedcolumns.getString("ANCHOR_ID");
        //主播昵称
        String anchorNickName = fixedcolumns.getString("ANCHOR_NICK_NAME");
        JSONArray isactives = fixedcolumns.getJSONArray("ISACTIVE");

        //直播主体
        JSONArray acFManageids = fixedcolumns.getJSONArray("AC_F_MANAGE_ID");
        String acFManageid = acFManageids == null || acFManageids.size() == 0 ? null : String.join(",", JSONObject.parseArray(acFManageids.toJSONString(), String.class));

        //配合主体
        JSONArray cooperateIds = fixedcolumns.getJSONArray("COOPERATE_ID");
        String cooperateId = cooperateIds == null || cooperateIds.size() == 0 ? null : String.join(",", JSONObject.parseArray(cooperateIds.toJSONString(), String.class));

        //直播场次
        String liveEvents = fixedcolumns.getString("LIVE_EVENTS");
        try {
            List<String> strategyStatuParse = splitFramePrefixByStr(strategyStatus);
            List<String> billTimeTypeParse = splitFramePrefixByStr(billTimeType);
            List<String> livePlatformParse = splitFramePrefixByStr(livePlatform);
            List<String> isactiveParse = splitFramePrefixByStr(isactives);
            int pageNum = 1;
            if (startIndex>=range){
                pageNum = (startIndex/range)+1;
            }
            PageHelper.startPage(pageNum, range,"id DESC");
            List<StCLiveCastStrategyDO> strategyDOList = liveCastStrategyMapper.selectPageList(shopId, strategyName,strategyStatuParse,billTimeTypeParse, startTimeBegin,startTimeEnd,endTimeBegin,endTimeEnd,livePlatformParse,anchorId,anchorNickName,isactiveParse,acFManageid,cooperateId,liveEvents);
            PageInfo<StCLiveCastStrategyDO> pageInfo = new PageInfo<>(strategyDOList);
            /*
            可视化处理
             */
            List<StCLiveCastStrategyDO> collect = strategyDOList.stream().map(LiveCastStrategyService::exChangeOutPutField).collect(Collectors.toList());
            JSONArray jsonArray = (JSONArray) JSONArray.toJSON(collect);
            log.info(LogUtil.format("查询数据为:{}"), jsonArray);
            List<JSONObject> jsonObjectList = JSONObject.parseArray(
                    JSON.toJSONStringWithDateFormat(jsonArray, "yyyy-MM-dd HH:mm:ss", SerializerFeature.WriteDateUseDateFormat), JSONObject.class);
            JSONArray getFrameDataFormat = JsonUtils.getFrameDataFormat(jsonObjectList);

            JSONObject resultData = new JSONObject();
            resultData.put("start", startIndex);
            resultData.put("rowCount", range);
            resultData.put("row", getFrameDataFormat);
            resultData.put("totalRowCount", pageInfo.getTotal());
            vh.put("data", resultData);
            vh.put("code", 0);
            vh.put("message", "success");
            return vh;

        } catch (Exception e) {
            log.error(LogUtil.format("直播策略查询异常：{}"), Throwables.getStackTraceAsString(e));
            JSONObject resultData = new JSONObject();
            resultData.put("start", startIndex);
            resultData.put("row", "");
            resultData.put("totalRowCount", 0);
            vh.put("data", resultData);
            vh.put("code", 0);
            vh.put("message", "系统异常");
            vh.put("error", e.getMessage());
            return vh;
        }
    }
    /**
     * 去除框架下拉栏数据中的前缀"="
     */
    private static List<String> splitFramePrefixByStr(JSONArray jsonArray) {
        List<String> list = Lists.newArrayList();
        if (jsonArray != null && jsonArray.size() != 0) {
            List<String> strings = JSONObject.parseArray(jsonArray.toJSONString(), String.class);
            list = strings.stream().map(item->item.substring(1)).collect(Collectors.toList());
        }
        return list;
    }
    /**
     * <AUTHOR>
     * @Date 19:46 2021/4/6
     * @Description 字段翻译
     */
    private static StCLiveCastStrategyDO exChangeOutPutField(StCLiveCastStrategyDO stCLiveCastStrategyDO) {
        Assert.notNull(stCLiveCastStrategyDO, "exChangeOutPutField-短信策略-实体类为空");
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("StCLiveCastStrategyDO:") + JSONObject.toJSONString(stCLiveCastStrategyDO));
        }
        String strategyStatus = stCLiveCastStrategyDO.getStrategyStatus();
        String billTimeType = stCLiveCastStrategyDO.getBillTimeType();
        String livePlatform = stCLiveCastStrategyDO.getLivePlatform();
        String isactive = stCLiveCastStrategyDO.getIsactive();
        stCLiveCastStrategyDO.setStrategyStatus(StLiveCastStrategyConstants.STRATEGYSTATUS.get(strategyStatus));
        stCLiveCastStrategyDO.setBillTimeType(StLiveCastStrategyConstants.BILLTIMETYPE.get(billTimeType));
        stCLiveCastStrategyDO.setLivePlatform(StLiveCastStrategyConstants.LIVEPLATFORM.get(livePlatform));
        stCLiveCastStrategyDO.setIsactive(StLiveCastStrategyConstants.ISACTIVE.get(isactive));
        return stCLiveCastStrategyDO;
    }
    /**
     * <AUTHOR>
     * @Date 16:06 2021/4/7
     * @Description 单对象查询
     */
    public ValueHolderV14 queryListById(Long objId) {
        log.info(LogUtil.format("LiveCastStrategyService.queryListById.objid{}"), objId);
        ValueHolderV14 vh =new ValueHolderV14();
        vh.setCode(ResultCode.SUCCESS);
        vh.setMessage("查询成功");
        if (null ==objId){
            vh.setCode(ResultCode.FAIL);
            vh.setMessage("当前记录已不存在");
        }
        StCLiveCastStrategyDO stCLiveCastStrategyDO = liveCastStrategyMapper.selectById(objId);
        if (null ==stCLiveCastStrategyDO){
            vh.setCode(ResultCode.FAIL);
            vh.setMessage("当前记录已不存在");
        }
        vh.setData(stCLiveCastStrategyDO);
        return vh;
    }
    /**
     * <AUTHOR>
     * @Date 18:09 2021/4/7
     * @Description  根据 objid查询
     */
    public StCLiveCastStrategyDO queryStCLiveCastStrategyById(Long objId){
        return liveCastStrategyMapper.selectById(objId);
    }
    /**
     * <AUTHOR>
     * @Date 14:50 2021/6/7
     * @Description 根据主播id查询出直播策略
     */
    public List<StCLiveCastStrategyDO> queryLiveCastStrategyByAnchorArchivesId(Long anchorArchivesId) {
        return  liveCastStrategyMapper.selectList(new QueryWrapper<StCLiveCastStrategyDO>().lambda().eq(StCLiveCastStrategyDO :: getAnchorArchivesId,anchorArchivesId));
    }
}
