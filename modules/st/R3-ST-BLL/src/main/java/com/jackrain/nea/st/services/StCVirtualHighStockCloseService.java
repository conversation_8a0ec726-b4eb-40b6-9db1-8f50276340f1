package com.jackrain.nea.st.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.google.common.base.Throwables;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.st.common.StConstant;
import com.jackrain.nea.st.mapper.StCVirtualHighStockItemMapper;
import com.jackrain.nea.st.mapper.StCVirtualHighStockMapper;
import com.jackrain.nea.st.model.table.StCShopVirtualHighStockDO;
import com.jackrain.nea.st.model.table.StCShopVirtualHighStockItemDO;
import com.jackrain.nea.st.utils.StBeanUtils;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.HashMap;
import java.util.List;

/**
 * <AUTHOR> ShiLong
 * @Date: 2020/6/22 10:26 下午
 * @Desc: 结案逻辑（店铺商品虚高库存）
 */
@Component
@Slf4j
public class StCVirtualHighStockCloseService extends CommandAdapter {
    @Autowired
    private StCVirtualHighStockMapper stCVirtualHighStockMapper;

    @Autowired
    private StCVirtualHighStockItemMapper stCVirtualHighStockItemMapper;

//    @Autowired
//    private ShopUniversalStockService shopUniversalStockService;

    @Override
    public ValueHolder execute(QuerySession querySession) throws NDSException {
        DefaultWebEvent event = querySession.getEvent();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(
                event.getParameterValue("param"), "yyyy-MM-dd HH:mm:ss"),
                Feature.OrderedField);
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("入参StCVirtualHighStockCloseService:param:{}"), param.toJSONString());
        }
        ValueHolder valueHolder = new ValueHolder();
        HashMap<Long, Object> errMap = new HashMap<>();
        //生成数组
        JSONArray auditArray = StBeanUtils.makeFinishJsonArray(param);
        StCVirtualHighStockCloseService bean =
                ApplicationContextHandle.getBean(StCVirtualHighStockCloseService.class);
        //列表批量
        if (!CollectionUtils.isEmpty(auditArray)) {
            for (int i = 0; i < auditArray.size(); i++) {
                //遍历 jsonarray 数组，把每一个对象转成 json 对象
                Long id = Long.valueOf(auditArray.get(i).toString());
                try {
                    // 引用传递,无需返回值
                    bean.updateAuditState(querySession, id, valueHolder);
                } catch (Exception ex) {
                    errMap.put(id, ex.getMessage());
                }
            }
        }
        //返回日志
        return StBeanUtils.getExcuteValueHolder(auditArray.size(), errMap);
    }

    @Transactional(rollbackFor = Exception.class)
    public void updateAuditState(QuerySession session, Long id, ValueHolder valueHolder) {
        StCShopVirtualHighStockDO result = stCVirtualHighStockMapper.selectById(id);
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("【执行虚高库存结案】{}"), JSONObject.toJSON(result));
        }
        checkAutocheckStatus(result, valueHolder);
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("【执行虚高库存结案验参结果】{}"), JSONObject.toJSON(valueHolder));
        }
        if (!valueHolder.isOK()) {
            return;
        }
        //修改信息
        try {
            StBeanUtils.makeModifierField(result, session.getUser());
            this.setFinishCommonField(result, session.getUser());
            //结案
            result.setState(StConstant.CON_BILL_STATUS_04);
            result.setCloseId(Long.valueOf(session.getUser().getId()));
            //结案时间
            result.setCloseTime(new Date());
            //结案姓名
            result.setCloseName(session.getUser().getName());
            int update = stCVirtualHighStockMapper.updateById(result);
            if (update > 0) {
                //推送ES数据
//                ValueHolder holder = shopUniversalStockService.deleteSgBSyncChannelStock(StConstant.VIRTUALHIGH_STRATEGY_TYPE.longValue(), id);
//                log.debug(LogUtil.format("商品虚高 结案 删除同步库存中间表数据 策略id：" + id + " 结果：" + JSONObject.toJSONString(holder));
                try {
                    //做更新的需要先查询更新后数据库的实体在推ES
                    StCShopVirtualHighStockItemDO item = new StCShopVirtualHighStockItemDO();
                    item.setStatus(StConstant.CON_BILL_STATUS_04);
                    QueryWrapper<StCShopVirtualHighStockItemDO> wrapper = new QueryWrapper<>();
                    wrapper.eq("st_c_shop_virtual_high_stock_id", id);
                    stCVirtualHighStockItemMapper.update(item, wrapper);
                    StCVirtualHighStockDelayService.pushVirtualHighItemsToEle(result, wrapper, stCVirtualHighStockItemMapper);
                } catch (Exception ex) {
                    log.debug(LogUtil.format("店铺商品虚高库存推数据到ES失败：{}"), Throwables.getStackTraceAsString(ex));
                }
            }
        } catch (Exception e) {
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("【执行虚高库存结案异常】{}"), Throwables.getStackTraceAsString(e));
            }
        }
    }

    /**
     * <AUTHOR>
     * @Description 批量完结虚高库存策略
     * @Date 13:28 2020/12/18
     * @param ids
     * @param user
     * @return com.jackrain.nea.util.ValueHolder
     **/
    @Transactional(rollbackFor = Exception.class)
    public ValueHolderV14 batchCloseStCVirtualHighStock(List<Long> ids ,User user){
        ValueHolderV14 valueHolder = new ValueHolderV14();
        if (CollectionUtils.isEmpty(ids)) {
            valueHolder.setCode(ResultCode.FAIL);
            valueHolder.setMessage("传入结案参数列表为空");
            return valueHolder;
        }
        try{
            /**1. 批量修改主表数据*/
            StCShopVirtualHighStockDO main = new StCShopVirtualHighStockDO();
            main.setState(StConstant.CON_BILL_STATUS_04);
            main.setCloseId(Long.valueOf(user.getId()));
            main.setCloseName(user.getEname());
            main.setCloseTime(new Date());
            StBeanUtils.makeModifierField(main,user);
            QueryWrapper<StCShopVirtualHighStockDO> wrapper = new QueryWrapper<>();
            wrapper.in("id", ids);
            stCVirtualHighStockMapper.update(main, wrapper);

            /**2. 批量修改明细数据*/
            StCShopVirtualHighStockItemDO item = new StCShopVirtualHighStockItemDO();
            item.setStatus(StConstant.CON_BILL_STATUS_04);
            StBeanUtils.makeModifierField(item,user);
            QueryWrapper<StCShopVirtualHighStockItemDO> itemWrapper = new QueryWrapper<>();
            itemWrapper.in("st_c_shop_virtual_high_stock_id", ids);
            stCVirtualHighStockItemMapper.update(item, itemWrapper);

            /**3. p更新es*/
            List<StCShopVirtualHighStockDO> stCShopVirtualHighStockDOS = stCVirtualHighStockMapper.selectList(wrapper);
            for (StCShopVirtualHighStockDO shopVirtualHighStockDO : stCShopVirtualHighStockDOS) {
                QueryWrapper<StCShopVirtualHighStockItemDO> esWrapper = new QueryWrapper<>();
                esWrapper.in("st_c_shop_virtual_high_stock_id", ids);
                StCVirtualHighStockDelayService.pushVirtualHighItemsToEle(shopVirtualHighStockDO, esWrapper, stCVirtualHighStockItemMapper);
            }
            valueHolder.setCode(ResultCode.SUCCESS);
        } catch (Exception e) {
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("【执行批量虚高库存结案异常】{}"), Throwables.getStackTraceAsString(e));
            }
            valueHolder.setCode(ResultCode.FAIL);
            valueHolder.setMessage(e.getMessage());
        }
        return valueHolder;
    }

    private void checkAutocheckStatus(StCShopVirtualHighStockDO stCAutocheckDO, ValueHolder valueHolder) {
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("执行结案参数校验:stCAutocheckDO:{},valueHolder:{}"), JSONObject.toJSON(stCAutocheckDO),
                    valueHolder.toJSONObject());
        }
        if (stCAutocheckDO == null) {
            throw new NDSException("请选择需要结案的方案！");
        }
        //方案不是已审核状态不允许结案
        if (!stCAutocheckDO.getState().equals(StConstant.CON_BILL_STATUS_02)) {
            throw new NDSException("方案状态不是已审核，不允许结案！");
        }
        valueHolder.put("code", 0);
        valueHolder.put("message", "成功");
    }

    private void setFinishCommonField(StCShopVirtualHighStockDO virtualHighStockDO, User user) {
        virtualHighStockDO.setCloseId(Long.valueOf(user.getId()));
        virtualHighStockDO.setCloseName(user.getName());
        virtualHighStockDO.setCloseTime(new Date());
    }

}
