package com.jackrain.nea.st.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.aliyun.openservices.shade.org.apache.commons.lang3.StringUtils;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.st.common.StConstant;
import com.jackrain.nea.st.mapper.StCDetentionPolicyMapper;
import com.jackrain.nea.st.model.table.StCDetentionPolicy;
import com.jackrain.nea.st.utils.RedisCacheUtil;
import com.jackrain.nea.st.utils.RedisConstant;
import com.jackrain.nea.st.utils.StBeanUtils;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;

/**
 * @program: r3-st
 * @description: 预售卡单审核
 * @author: liuwj
 * @create: 2021-06-17 17:21
 **/
@Component
@Slf4j
public class StCDetentionPolicyAuditService extends CommandAdapter {

    @Autowired
    private StCDetentionPolicyMapper stCDetentionPolicyMapper;

    @Override
    public ValueHolder execute(QuerySession querySession) throws NDSException {
        //1.获取传入参数
        DefaultWebEvent event = querySession.getEvent();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(event.getParameterValue("param"),
                "yyyy-MM-dd HH:mm:ss", SerializerFeature.WriteMapNullValue), Feature.OrderedField);
        if (param == null) {
            throw new NDSException("参数为空！");
        }
        log.debug(LogUtil.format("Start StCDetentionPolicyAuditService.execute. ReceiveParams: {}"),
                param.toJSONString());
        //2.定义错误记录数
        HashMap<Long, Object> errMap = new HashMap();
        //3.生成审核Json数组
        JSONArray auditArray = StBeanUtils.makeAuditJsonArray(param);
        for (int i = 0; i < auditArray.size(); i++) {
            Long id = auditArray.getLong(i);
            try {
                //4.遍历审核方法
                auditStCDetention(id, querySession);
            } catch (Exception e) {
                errMap.put(id, e.getMessage());
            }
        }
        //5.返回ValueHolder对象
        return StBeanUtils.getExcuteValueHolder(auditArray.size(), errMap);
    }

    public void auditStCDetention(Long id, QuerySession querySession) {
        StCDetentionPolicy stCDetentionPolicy= stCDetentionPolicyMapper.selectById(id);
        //主表校验
        checkEstatus(stCDetentionPolicy);

        //更新单据状态
        StBeanUtils.makeModifierField(stCDetentionPolicy, querySession.getUser());
        stCDetentionPolicy.setEstatus(StConstant.CON_BILL_STATUS_02);
        StBeanUtils.makeModifierField(stCDetentionPolicy,querySession.getUser());
        int updateNum = stCDetentionPolicyMapper.updateById(stCDetentionPolicy);
        if (updateNum < 0) {
            throw new NDSException("审核失败！");
        }
        Long shopId = StringUtils.isBlank(stCDetentionPolicy.getCpCShopId()) ? null : Long.valueOf(stCDetentionPolicy.getCpCShopId());
        RedisCacheUtil.delete(shopId, RedisConstant.SHOP_DETENTION_ORDER_ST);
    }

    private void checkEstatus(StCDetentionPolicy stCDetentionPolicy) {
        if (stCDetentionPolicy == null) {
            throw new NDSException("当前记录已不存在！");
        } else {
            if (StConstant.CON_BILL_STATUS_02.equals(stCDetentionPolicy.getEstatus())) {
                throw new NDSException("当前记录已审核，不允许重复审核！");
            }else if(StConstant.CON_BILL_STATUS_03.equals(stCDetentionPolicy.getEstatus())){
                throw new NDSException("当前记录已作废，不允许审核！");
            }else if(StConstant.CON_BILL_STATUS_04.equals(stCDetentionPolicy.getEstatus())){
                throw new NDSException("当前记录已结案，不允许审核！");
            }
        }
    }
}
