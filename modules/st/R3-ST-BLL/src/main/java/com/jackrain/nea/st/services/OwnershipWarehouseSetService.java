package com.jackrain.nea.st.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.google.common.base.Throwables;
import com.jackrain.nea.common.ReferenceUtil;
import com.jackrain.nea.cpext.api.GeneralOrganizationCmd;
import com.jackrain.nea.cpext.model.table.CpStore;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.st.common.StConstant;
import com.jackrain.nea.st.mapper.StCOwnershipWarehouseSetMapper;
import com.jackrain.nea.st.model.table.StCOwnershipWarehouseSetDO;
import com.jackrain.nea.st.utils.JsonUtils;
import com.jackrain.nea.st.utils.StBeanUtils;
import com.jackrain.nea.st.utils.ValueHolderUtils;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.sql.Timestamp;

/**
 * @author: 汪聿森
 * @Date: Created in 2020-02-17 17:02
 * @Description : 库存归属仓库属性设置新增
 */
@Component
@Slf4j
public class OwnershipWarehouseSetService extends CommandAdapter {
    @Autowired
    private StCOwnershipWarehouseSetMapper stCOwnershipWarehouseSetMapper;

    @Override
    public ValueHolder execute(QuerySession querySession) throws NDSException {
        DefaultWebEvent event = querySession.getEvent();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(
                event.getParameterValue("param"), "yyyy-MM-dd HH:mm:ss"),
                Feature.OrderedField);

        Long id = param.getLong("objid");
        JSONObject fixColumn = param.getJSONObject("fixcolumn");
        JSONObject stCOwnershipWarehouseSetMap = fixColumn.getJSONObject(StConstant.TAB_ST_C_OWNERSHIP_WAREHOUSE_SET);
        StCOwnershipWarehouseSetDO stCOwnershipWarehouseSetDO = JsonUtils.jsonParseClass(stCOwnershipWarehouseSetMap, StCOwnershipWarehouseSetDO.class);
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("stCOwnershipWarehouseSetDO:") + stCOwnershipWarehouseSetDO);
        }
        if (stCOwnershipWarehouseSetDO != null && id != null) {
            stCOwnershipWarehouseSetDO.setId(id);
            return addOwnershipWarehouseSet(querySession, stCOwnershipWarehouseSetDO);
        } else {
            return ValueHolderUtils.getSuccessValueHolder(id, StConstant.TAB_ST_C_OWNERSHIP_WAREHOUSE_SET, "");
        }
    }

    /**
     * 新增库存归属仓库属性
     *
     * @param session
     * @param stCOwnershipWarehouseSetDO
     * @return
     */
    public ValueHolder addOwnershipWarehouseSet(QuerySession session, StCOwnershipWarehouseSetDO stCOwnershipWarehouseSetDO) {
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("==========stCOwnershipWarehouseSetDO") + stCOwnershipWarehouseSetDO);
        }
        Long id = stCOwnershipWarehouseSetDO.getId();
        if (stCOwnershipWarehouseSetDO.getId() > 0) {
            StBeanUtils.makeModifierField(stCOwnershipWarehouseSetDO, session.getUser());
            if (stCOwnershipWarehouseSetMapper.updateById(stCOwnershipWarehouseSetDO) < 0) {
                return ValueHolderUtils.getFailValueHolder("更新失败！");
            }
        } else {
            Long cStorePropertyType = stCOwnershipWarehouseSetDO.getCpCStorePropertyType();
            Long cpCStoreId = stCOwnershipWarehouseSetDO.getCpCStoreId();
            if (cpCStoreId == null) {
                throw new NDSException("仓库不能为空！");
            }
            if (cStorePropertyType == null) {
                throw new NDSException("仓库属性不能为空！");
            }
            Integer integer = stCOwnershipWarehouseSetMapper.selectCount(new QueryWrapper<StCOwnershipWarehouseSetDO>().lambda()
                    .eq(StCOwnershipWarehouseSetDO::getCpCStoreId, cpCStoreId)
                    .eq(StCOwnershipWarehouseSetDO::getCpCStorePropertyType, cStorePropertyType)
                    .eq(StCOwnershipWarehouseSetDO::getIsactive, StConstant.ISACTIVE_Y));
            if (integer > 0) {
                throw new NDSException("仓库属性已经存在！");
            }
            GeneralOrganizationCmd generalOrganizationCmd = (GeneralOrganizationCmd) ReferenceUtil.refer(
                    ApplicationContextHandle.getApplicationContext(), GeneralOrganizationCmd.class.getName(), "cp-ext", "1.0");
            try {
                CpStore cpStore = generalOrganizationCmd.selectCpCStoreById(cpCStoreId);
                if (cpStore != null) {
                    stCOwnershipWarehouseSetDO.setCpCStoreEcode(cpStore.getEcode());
                    stCOwnershipWarehouseSetDO.setCpCStoreEname(cpStore.getEname());
                }
            } catch (Exception e) {
                log.error(LogUtil.format("调用根据逻辑仓ID查询逻辑仓信息服务异常：{}"), Throwables.getStackTraceAsString(e));
            }
            stCOwnershipWarehouseSetDO.setId(ModelUtil.getSequence("ST_C_OWNERSHIP_WAREHOUSE_SET"));
            Timestamp timestamp = new Timestamp(System.currentTimeMillis());
            //基本字段值设置
            stCOwnershipWarehouseSetDO.setOwnerename(session.getUser().getEname());
            stCOwnershipWarehouseSetDO.setOwnerid(Long.valueOf(session.getUser().getId()));
            stCOwnershipWarehouseSetDO.setOwnername(session.getUser().getName());
            stCOwnershipWarehouseSetDO.setModifiername(session.getUser().getName());
            stCOwnershipWarehouseSetDO.setModifierename(session.getUser().getEname());
            stCOwnershipWarehouseSetDO.setModifierid(Long.valueOf(session.getUser().getId()));
            stCOwnershipWarehouseSetDO.setCreationdate(timestamp);
            stCOwnershipWarehouseSetDO.setModifieddate(timestamp);
            stCOwnershipWarehouseSetDO.setAdClientId((long) session.getUser().getClientId());
            stCOwnershipWarehouseSetDO.setAdOrgId((long) session.getUser().getOrgId());
            try {
                int insertResult = stCOwnershipWarehouseSetMapper.insert(stCOwnershipWarehouseSetDO);
                if (insertResult > 0) {
                    return ValueHolderUtils.getSuccessValueHolder(stCOwnershipWarehouseSetDO.getId(), StConstant.TAB_ST_C_OWNERSHIP_WAREHOUSE_SET);
                } else {
                    return ValueHolderUtils.getFailValueHolder("保存失败");
                }
            } catch (Exception ex) {
                log.error(LogUtil.format("OwnershipWarehouseSetService.addOwnershipWarehouseSet Error{}"), Throwables.getStackTraceAsString(ex));
                return ValueHolderUtils.getFailValueHolder("保存异常add");
            }
        }
        return ValueHolderUtils.getSuccessValueHolder(id, StConstant.TAB_ST_C_OWNERSHIP_WAREHOUSE_SET, "");
    }

    /**
     * 更新库存归属仓库属性
     *
     * @param session
     * @param fixColumn
     * @param id
     * @return
     */
    public ValueHolder updateOwnershipWarehouseSet(QuerySession session, JSONObject fixColumn, Long id) {
        StCOwnershipWarehouseSetDO stCOwnershipWarehouseSetDO = JsonUtils.jsonParseClass(fixColumn, StCOwnershipWarehouseSetDO.class);
        StCOwnershipWarehouseSetDO isExit = stCOwnershipWarehouseSetMapper.selectById(id);

        if (stCOwnershipWarehouseSetDO == null) {
            throw new NDSException("fixColumn入参为空！");
        }
        if (isExit == null) {
            throw new NDSException("当前记录已不存在！");
        }

        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("stCOwnershipWarehouseSetDO：{},isExit=", isExit), stCOwnershipWarehouseSetDO);
            log.debug(LogUtil.format("stCOwnershipWarehouseSetDO") + stCOwnershipWarehouseSetDO);
        }

        stCOwnershipWarehouseSetDO.setId(id);
        stCOwnershipWarehouseSetDO.setModifierid(Long.valueOf(session.getUser().getId()));
        stCOwnershipWarehouseSetDO.setModifieddate(new Timestamp(System.currentTimeMillis()));
        stCOwnershipWarehouseSetDO.setModifiername(session.getUser().getName());
        stCOwnershipWarehouseSetDO.setModifierename(session.getUser().getEname());
        try {
            if (stCOwnershipWarehouseSetMapper.updateById(stCOwnershipWarehouseSetDO) > 0) {
                return ValueHolderUtils.getSuccessValueHolder(stCOwnershipWarehouseSetDO.getId(), StConstant.TAB_ST_C_OWNERSHIP_WAREHOUSE_SET);
            } else {
                return ValueHolderUtils.getFailValueHolder("更新失败");
            }
        } catch (Exception ex) {
            log.error(LogUtil.format("OwnershipWarehouseSetService.updateOwnershipWarehouseSet Error{}"), Throwables.getStackTraceAsString(ex));
            return ValueHolderUtils.getFailValueHolder("保存异常update");
        }
    }
}
