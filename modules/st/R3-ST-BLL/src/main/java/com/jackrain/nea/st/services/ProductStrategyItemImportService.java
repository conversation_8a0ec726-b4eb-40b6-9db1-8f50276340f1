//package com.jackrain.nea.st.services;
//
//import com.alibaba.fastjson.JSONArray;
//import com.alibaba.fastjson.JSONObject;
//import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
//import com.google.common.collect.Lists;
//import com.jackrain.nea.config.PropertiesConf;
//import com.jackrain.nea.constants.ResultCode;
//import com.jackrain.nea.cpext.model.table.CpShop;
//import com.jackrain.nea.exception.NDSException;
//import com.jackrain.nea.ip.model.SpecialbarcodeRetModel;
//import com.jackrain.nea.model.util.ModelUtil;
//import com.jackrain.nea.ps.api.result.PsSkuResult;
//import com.jackrain.nea.ps.api.table.PsCSkuExt;
//import com.jackrain.nea.psext.model.table.PsCPro;
//import com.jackrain.nea.sg.oms.model.request.SgChannelProductQueryForSTRequest;
//import com.jackrain.nea.sg.oms.model.result.SgChannelProductQueryForSTResult;
//import com.jackrain.nea.sg.oms.model.table.SgBChannelDef;
//import com.jackrain.nea.st.common.StConstant;
//import com.jackrain.nea.st.config.R3OssConfig;
//import com.jackrain.nea.st.mapper.StCProductStrategyItemMapper;
//import com.jackrain.nea.st.mapper.StCProductStrategyMapper;
//import com.jackrain.nea.st.model.result.StErrMsgResult;
//import com.jackrain.nea.st.model.table.StCProductStrategyDO;
//import com.jackrain.nea.st.model.table.StCProductStrategyItemDO;
//import com.jackrain.nea.st.rpc.RpcCpService;
//import com.jackrain.nea.st.rpc.RpcIpService;
//import com.jackrain.nea.st.rpc.RpcPsService;
//import com.jackrain.nea.st.rpc.RpcSgService;
//import com.jackrain.nea.st.utils.*;
//import com.jackrain.nea.sys.CommandAdapter;
//import com.jackrain.nea.sys.domain.ValueHolderV14;
//import com.jackrain.nea.util.ValueHolder;
//import com.jackrain.nea.web.face.User;
//import com.jackrain.nea.web.face.impl.UserImpl;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.commons.collections.CollectionUtils;
//import org.apache.commons.lang3.StringUtils;
//import org.apache.commons.lang3.math.NumberUtils;
//import org.apache.poi.ss.usermodel.Workbook;
//import org.apache.poi.xssf.usermodel.XSSFWorkbook;
//import org.springframework.beans.BeanUtils;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.stereotype.Component;
//
//import java.math.BigDecimal;
//import java.util.*;
//import java.util.function.Function;
//import java.util.stream.Collectors;
//
///**
// * @author:huang.zaizai
// * @since: 2019/9/25
// * @create at : 2019/9/25 10:00
// */
//@Component
//@Slf4j
//public class ProductStrategyItemImportService extends CommandAdapter {
//
//    //导入excel,业务校验错误提示返回 错误excel地址
//    public static final int IMPORT_ERROR_CODE = 10001;
//
//    @Autowired
//    private ExportUtil exportUtil;
//    @Autowired
//    private R3OssConfig r3OssConfig;
//    @Autowired
//    private StCProductStrategyMapper mapper;
//    @Autowired
//    private StCProductStrategyItemMapper itemMapper;
//
//    @Autowired
//    private RpcPsService rpcPsService;
//    @Autowired
//    private RpcCpService rpcCpService;
//    @Autowired
//    private RpcIpService rpcIpService;
//
//    @Autowired
//    private RpcSgService rpcSgService;
//    @Autowired
//    private PropertiesConf propertiesConf;
//
//    private Integer channelTypeByShop = null;
//
//
//    /**
//     * @param
//     * @return com.jackrain.nea.sys.domain.ValueHolderV14
//     * @Description 模板下载
//     * @date 2019/9/25 10:00
//     */
//    public ValueHolderV14 downloadTemp() {
//        ValueHolderV14 vh = new ValueHolderV14(ResultCode.SUCCESS, "店铺商品特殊设置明细导入模板下载成功！");
//        exportUtil.setEndpoint(r3OssConfig.getEndpoint());
//        exportUtil.setAccessKeyId(r3OssConfig.getAccessKeyId());
//        exportUtil.setAccessKeySecret(r3OssConfig.getAccessKeySecret());
//        exportUtil.setBucketName(r3OssConfig.getBucketName());
//        if (StringUtils.isEmpty(r3OssConfig.getTimeout())) {
//            //如果获取不到apllo配置参数，设置默认过期时间为30分钟
//            exportUtil.setTimeout("1800000");
//        } else {
//            exportUtil.setTimeout(r3OssConfig.getTimeout());
//        }
//        /**
//         *  拼接Excel主表sheet表头字段
//         * */
//        String mainNames[] = {"店铺名称", "商品编码", "库存比例", "低库存数"};
//        String mustNames[] = {"店铺名称", "商品编码", "库存比例", "低库存数"};
//        User user = new UserImpl();
//        ((UserImpl) user).setName("");
//        List<String> mainList = Lists.newArrayList(mainNames);
//        List<String> mustList = Lists.newArrayList(mustNames);
//        //生成Excel
//        XSSFWorkbook hssfWorkbook = new XSSFWorkbook();
//        exportUtil.executeSheet(hssfWorkbook, "店铺商品特殊设置明细数据", "", mainList, mustList,
//                Lists.newArrayList(), Lists.newArrayList(), false);
//        String putMsg = exportUtil.saveFileAndPutOss(hssfWorkbook, "店铺商品特殊设置明细导入模板",
//                user, "OSS-Bucket/EXPORT/StCProductStrategyItem/");
//        vh.setData(putMsg);
//        return vh;
//    }
//
//    /**
//     * @param productStrategyItemList
//     * @param user
//     * @return com.jackrain.nea.sys.domain.ValueHolderV14
//     * @Description 导入
//     * @date 2019/9/25 15:00
//     */
//    public ValueHolder importProductStrategyItem(Long objid, List<StCProductStrategyItemDO> productStrategyItemList, User user, Integer channelType) {
//        ValueHolder vh = new ValueHolder();
//        channelTypeByShop = channelType;
//        if (CollectionUtils.isEmpty(productStrategyItemList)) {
//            vh.put("code", ResultCode.FAIL);
//            vh.put("message", "导入失败,上传文件未录入数据!");
//            return vh;
//        }
//        if (objid == null || objid < 0) {
//            vh.put("code", ResultCode.FAIL);
//            vh.put("message", "主表信息不存在!");
//            return vh;
//        }
//        //保存明细
//        List<String> errMsgList = saveProductStrategyItem(objid, productStrategyItemList, user);
//        //3.若有错误信息支持导出
//        if (errMsgList.size() > 0) {
//            List<StErrMsgResult> errExcelList = Lists.newArrayList();
//            errMsgList.forEach(errMsg -> {
//                StErrMsgResult errMsgResult = new StErrMsgResult();
//                errMsgResult.setErrMsg(errMsg);
//                errExcelList.add(errMsgResult);
//            });
//            vh.put("code", ResultCode.FAIL);
//            vh.put("message", String.format("导入失败%d条", errMsgList.size()));
////            String sdd = downloadErrMsg(user, errExcelList);
////            vh.put("path", sdd);
//            JSONArray errorArray = new JSONArray();
//            for (String error : errMsgList) {
//                errorArray.add(error);
//            }
//            vh.put("data", errorArray);
//        } else {
//            vh.put("code", ResultCode.SUCCESS);
//            vh.put("message", "导入成功!");
//        }
//        return vh;
//    }
//
//    /**
//     * 下载错误信息
//     *
//     * @param user
//     * @param errExcelList
//     * @return 错误信息
//     */
//    private String downloadErrMsg(User user, List<StErrMsgResult> errExcelList) {
//        String columnNames[] = {"错误原因"};
//        List<String> columnList = Lists.newArrayList(columnNames);
//        String keys[] = {"errMsg"};
//        List<String> keyList = Lists.newArrayList(keys);
//        exportUtil.setEndpoint(r3OssConfig.getEndpoint());
//        exportUtil.setAccessKeyId(r3OssConfig.getAccessKeyId());
//        exportUtil.setAccessKeySecret(r3OssConfig.getAccessKeySecret());
//        exportUtil.setBucketName(r3OssConfig.getBucketName());
//        if (StringUtils.isEmpty(r3OssConfig.getTimeout())) {
//            //如果获取不到apllo配置参数，设置默认过期时间为30分钟
//            exportUtil.setTimeout("1800000");
//        } else {
//            exportUtil.setTimeout(r3OssConfig.getTimeout());
//        }
//        Workbook hssfWorkbook = exportUtil.execute("店铺商品特殊设置明细", "店铺商品特殊设置明细",
//                columnList, keyList, errExcelList);
//        return exportUtil.saveFileAndPutOss(hssfWorkbook, "店铺商品特殊设置明细导入错误信息",
//                user, "OSS-Bucket/EXPORT/StCProductStrategyItem/");
//    }
//
//    /**
//     * 保存信息
//     *
//     * @param objid
//     * @param productStrategyItemList
//     * @return 错误信息
//     */
//    private List<String> saveProductStrategyItem(Long objid, List<StCProductStrategyItemDO> productStrategyItemList, User user) {
//        List<String> errMsgList = Lists.newArrayList();
//        //准备数据
////        List<StCProductStrategyItemDO> itemList = buildProductStrategy(objid, productStrategyItemList, errMsgList);
//        //zyj 20191225 店铺商品特殊设置明细全量导入-获取商品信息
//        List<SgChannelProductQueryForSTResult> sgChannelProductQueryForSTResults = getChannelProduct(productStrategyItemList);
//        if (CollectionUtils.isEmpty(sgChannelProductQueryForSTResults)) {
//            errMsgList.add("渠道商品查询不到");
//            return errMsgList;
//        }
//        if (log.isDebugEnabled()) {
//            log.debug(LogUtil.format(" 获取渠道商品出参：" + JSONObject.toJSONString(sgChannelProductQueryForSTResults));
//        }
//        //组装店铺商品信息
//        List<StCProductStrategyItemDO> itemList = getShopProductStrategyItemList(productStrategyItemList, sgChannelProductQueryForSTResults, errMsgList);
//        //旧数据准备
//        Map<String, StCProductStrategyItemDO> itemOldMap = new HashMap<>();
//        if (CollectionUtils.isNotEmpty(itemList)) {
//            List<StCProductStrategyItemDO> itemOldList = itemMapper.selectList(new QueryWrapper<StCProductStrategyItemDO>()
//                    .lambda().eq(StCProductStrategyItemDO::getStCProductStrategyId, objid));
//            for (StCProductStrategyItemDO itemOld : itemOldList) {
//                String key = itemOld.getPtProId() + "," + itemOld.getPtSkuId() + "," + itemOld.getPsCProId() + "," + itemOld.getPsCSkuId();
//                itemOldMap.put(key, itemOld);
//            }
//        }
//        //保存信息
//        StCProductStrategyDO productStrategy = mapper.selectById(objid);
//        for (StCProductStrategyItemDO item : itemList) {
//            try {
//                item.setPlanName(productStrategy.getPlanName());
//                item.setStatus(productStrategy.getEstatus());
//                item.setBeginTime(productStrategy.getBeginTime());
//                item.setEndTime(productStrategy.getEndTime());
//                item.setMainCreationdate(productStrategy.getCreationdate());
//                String key = item.getPtProId() + "," + item.getPtSkuId() + "," + item.getPsCProId() + "," + item.getPsCSkuId();
//                //存在更新 不存在新增
//                if (itemOldMap.containsKey(key)) {
//                    item.setId(itemOldMap.get(key).getId());
//                    item.setStCProductStrategyId(objid);
//                    StBeanUtils.makeModifierField(item, user);
//                    if (itemMapper.updateById(item) < 1) {
//                        throw new NDSException(key + "店铺商品特殊设置明细更新失败");
//                    }
//                } else {
//                    item.setId(ModelUtil.getSequence(StConstant.TAB_ST_C_PRODUCT_STRATEGY_ITEM));
//                    item.setStCProductStrategyId(objid);
//                    StBeanUtils.makeCreateField(item, user);
//                    if (itemMapper.insert(item) < 1) {
//                        throw new NDSException(key + "店铺商品特殊设置明细插入失败");
//                    }
//                }
//            } catch (Exception e) {
//                log.error(LogUtil.format("saveProductStrategyItem error: {}", e);
//                errMsgList.add(e.getMessage());
//            }
//        }
//        try {
//            QueryWrapper<StCProductStrategyItemDO> wrapper = new QueryWrapper<>();
//            wrapper.eq("ST_C_PRODUCT_STRATEGY_ID", objid);
//            List<StCProductStrategyItemDO> newItemList = ListUtils.batchQueryByCondition(wrapper, itemMapper, 1000);
//            if (CollectionUtils.isNotEmpty(newItemList)) {
//                DatasToEsUtils.insertProductEsData(productStrategy, newItemList, StConstant.TAB_ST_C_PRODUCT_STRATEGY_ITEM);
//            }
//        } catch (Exception e) {
//            log.error(LogUtil.format("saveProductStrategyItem es update error: {}", e);
//        }
//        if (CollectionUtils.isNotEmpty(itemList) && itemList.size() != errMsgList.size()) {
//            //基本字段值设置
//            StBeanUtils.makeModifierField(productStrategy, user);
//            int count = mapper.updateById(productStrategy);
//            if (count > 0) {
//                //主表推送ES数据
//                try {
//                    //做更新的需要先查询更新后数据库的实体在推ES
//                    DatasToEsUtils.insertProductEsData(productStrategy, null, StConstant.TAB_ST_C_PRODUCT_STRATEGY);
//                } catch (Exception ex) {
//                    log.debug(StConstant.TAB_ST_C_PRODUCT_STRATEGY + "店铺商品特殊设置主表更新数据至ES失败：" + ex.toString());
//                }
//            }
//        }
//        return errMsgList;
//    }
//
//    /**
//     * 构建实体
//     */
//    private List<StCProductStrategyItemDO> buildProductStrategy(Long objid, List<StCProductStrategyItemDO> productStrategyItemList,
//                                                                List<String> errMsgList) {
//        //准备数据
//        Map<String, CpShop> shopMap = new HashMap<>();
//        Map<String, PsCPro> proMap = new HashMap<>();
//        Map<String, List<SpecialbarcodeRetModel>> platformMap = new HashMap<>();
//
//        List<String> shopTitleList = Lists.newArrayList();
//        List<String> proEcodeList = Lists.newArrayList();
//        for (StCProductStrategyItemDO productStrategyItem : productStrategyItemList) {
//            if (!shopTitleList.contains(productStrategyItem.getCpCShopTitle())) {
//                shopTitleList.add(productStrategyItem.getCpCShopTitle());
//            }
//            if (!proEcodeList.contains(productStrategyItem.getPsCProEcode())) {
//                proEcodeList.add(productStrategyItem.getPsCProEcode());
//            }
//        }
//        if (!CollectionUtils.isEmpty(shopTitleList)) {
//            List<CpShop> cpShopList = rpcCpService.queryByListTitle(shopTitleList);
//            if (cpShopList != null) {
//                shopMap = cpShopList.stream().collect(Collectors.toMap(CpShop::getCpCShopTitle, Function.identity()));
//            }
//        }
//        if (!CollectionUtils.isEmpty(proEcodeList)) {
//            List<PsCPro> proList = rpcPsService.queryProByEcodes(proEcodeList);
//            if (proList != null) {
//                proMap = proList.stream().collect(Collectors.toMap(PsCPro::getEcode, Function.identity()));
//            }
//        }
//
//        Map<String, String> errMsgMap = new HashMap<>();
//        List<StCProductStrategyItemDO> itemList = getPlatformMap(productStrategyItemList, shopMap, proMap, platformMap, errMsgMap);
//        for (String errMsg : errMsgMap.values()) {
//            errMsgList.add(errMsg);
//        }
//        //组装集合
//        List<StCProductStrategyItemDO> itemLastList = Lists.newArrayList();
//        Map<Long, CpShop> shopTitleMap = new HashMap<>();
//        Map<String, StCProductStrategyItemDO> itemNewMap = new HashMap<>();
//        for (StCProductStrategyItemDO productStrategyItem : itemList) {
//            try {
//                if (!errMsgMap.containsKey(productStrategyItem.getCpCShopTitle() + "," + productStrategyItem.getPsCProEcode())) {
//                    //补足冗余信息
//                    getImportProductStrategyItem(productStrategyItem, shopMap);
//                    productStrategyItem.setStCProductStrategyId(objid);
//                    List<SpecialbarcodeRetModel> platformList = platformMap.get(productStrategyItem.getPsCSkuEcode());
//                    if (CollectionUtils.isNotEmpty(platformList)) {
//                        for (SpecialbarcodeRetModel platform : platformList) {
//                            StCProductStrategyItemDO itemNew = new StCProductStrategyItemDO();
//                            BeanUtils.copyProperties(productStrategyItem, itemNew);
//
//                            itemNew.setCpCShopId(platform.getCpCShopId());
//                            itemNew.setPtProId(platform.getPtProId());
//                            itemNew.setPtSkuId(platform.getPtSkuId());
//
//                            String key = itemNew.getPtProId() + "," + itemNew.getPtSkuId() + "," + itemNew.getPsCProId() + "," + itemNew.getPsCSkuId();
//                            if (itemNewMap.containsKey(key)) {
//                                itemNewMap.get(key).setStockScale(itemNew.getStockScale());
//                                itemNewMap.get(key).setLowStock(itemNew.getLowStock());
//                            } else {
//                                if (!productStrategyItem.getCpCShopId().equals(itemNew.getCpCShopId())) {
//                                    itemNew.setCpCShopTitle(null);
//                                    CpShop cpShop = null;
//                                    if (shopTitleMap.containsKey(itemNew.getCpCShopId())) {
//                                        cpShop = shopTitleMap.get(itemNew.getCpCShopId());
//                                    } else {
//                                        cpShop = rpcCpService.selectCpCShopById(itemNew.getCpCShopId());
//                                    }
//                                    if (cpShop != null) {
//                                        itemNew.setCpCShopTitle(cpShop.getCpCShopTitle());
//                                        shopTitleMap.put(itemNew.getCpCShopId(), cpShop);
//                                    }
//                                    itemNew.setStockScale(BigDecimal.ZERO);
//                                    itemNew.setLowStock(0L);
//                                }
//                            }
//                            if (StringUtils.isNotBlank(itemNew.getCpCShopTitle())) {
//                                itemNewMap.put(key, itemNew);
//                            }
//                        }
//                    }
//                }
//            } catch (Exception e) {
//                String key = productStrategyItem.getCpCShopTitle() + "商品编码" + productStrategyItem.getPsCProEcode();
//                errMsgList.add(key + e.getMessage());
//            }
//        }
//        for (StCProductStrategyItemDO itemNew : itemNewMap.values()) {
//            itemLastList.add(itemNew);
//        }
//        return itemLastList;
//    }
//
//    private List<StCProductStrategyItemDO> getPlatformMap(List<StCProductStrategyItemDO> productStrategyItemList
//            , Map<String, CpShop> shopMap
//            , Map<String, PsCPro> proMap
//            , Map<String, List<SpecialbarcodeRetModel>> platformMap
//            , Map<String, String> errMsgMap) {
//        Map<String, String> skuProMap = new HashMap<>();
//        List<String> skuEcodeList = Lists.newArrayList();
//        List<StCProductStrategyItemDO> itemList = Lists.newArrayList();
//        for (StCProductStrategyItemDO productStrategyItem : productStrategyItemList) {
//            try {
//                //店铺设值
//                if (!StringUtils.isBlank(productStrategyItem.getCpCShopTitle())) {
//                    if (shopMap.containsKey(productStrategyItem.getCpCShopTitle())) {
//                        productStrategyItem.setCpCShopId(shopMap.get(productStrategyItem.getCpCShopTitle()).getCpCShopId());
//                    } else {
//                        throw new NDSException(String.format("不存在名称为[%s]的店铺", productStrategyItem.getCpCShopTitle()));
//                    }
//                } else {
//                    throw new NDSException("店铺名称不能为空");
//                }
//                if (!StringUtils.isBlank(productStrategyItem.getPsCProEcode())) {
//                    if (proMap.containsKey(productStrategyItem.getPsCProEcode())) {
//                        productStrategyItem.setPsCProId(proMap.get(productStrategyItem.getPsCProEcode()).getId());
//                        productStrategyItem.setPsCProEname(proMap.get(productStrategyItem.getPsCProEcode()).getEname());
//
//                        List<PsCSkuExt> psCSkuExts = new ArrayList<>();
//                        PsSkuResult psSkuResult = rpcPsService.getSkuResultByProId(productStrategyItem.getPsCProId());
//                        if (!CollectionUtils.isEmpty(psSkuResult.getSkuList())) {
//                            psCSkuExts = psSkuResult.getSkuList();
//                        }
//                        for (PsCSkuExt psCSkuExt : psCSkuExts) {
//                            StCProductStrategyItemDO productStrategyItemNew = new StCProductStrategyItemDO();
//                            BeanUtils.copyProperties(productStrategyItem, productStrategyItemNew);
//                            productStrategyItemNew.setPsCSkuId(psCSkuExt.getId());
//                            productStrategyItemNew.setPsCSkuEcode(psCSkuExt.getEcode());
//                            itemList.add(productStrategyItemNew);
//                            skuEcodeList.add(productStrategyItemNew.getPsCSkuEcode());
//                            skuProMap.put(productStrategyItemNew.getPsCSkuEcode(), productStrategyItemNew.getPsCProEcode());
//                        }
//                    } else {
//                        throw new NDSException(String.format("不存在为[%s]的商品编码", productStrategyItem.getPsCProEcode()));
//                    }
//                } else {
//                    throw new NDSException("商品编码不能为空");
//                }
//            } catch (Exception e) {
//                String key = productStrategyItem.getCpCShopTitle() + "商品编码" + productStrategyItem.getPsCProEcode();
//                errMsgMap.put(productStrategyItem.getCpCShopTitle() + "," + productStrategyItem.getPsCProEcode(), key + e.getMessage());
//            }
//        }
//
//        Map<String, List<SpecialbarcodeRetModel>> proPlatformMap = new HashMap<>();
//        if (!CollectionUtils.isEmpty(skuEcodeList)) {
//            List<SpecialbarcodeRetModel> specialbarcodeList = Lists.newArrayList();
//            List<String> skuEcodes = Lists.newArrayList();
//            for (String skuEcode : skuEcodeList) {
//                skuEcodes.add(skuEcode);
//                if (skuEcodes.size() >= 100) {
//                    List<SpecialbarcodeRetModel> specialbarcodePartList = rpcIpService.specialBarcodeBySkuList(skuEcodes);
//                    if (CollectionUtils.isNotEmpty(specialbarcodePartList)) {
//                        specialbarcodeList.addAll(specialbarcodePartList);
//                    }
//                    skuEcodes.clear();
//                }
//            }
//            if (skuEcodes.size() != 0) {
//                List<SpecialbarcodeRetModel> specialbarcodePartList = rpcIpService.specialBarcodeBySkuList(skuEcodes);
//                if (CollectionUtils.isNotEmpty(specialbarcodePartList)) {
//                    specialbarcodeList.addAll(specialbarcodePartList);
//                }
//                skuEcodes.clear();
//            }
//
//            if (CollectionUtils.isNotEmpty(specialbarcodeList)) {
//                List<SpecialbarcodeRetModel> mapList;
//                List<SpecialbarcodeRetModel> proMapList;
//                for (SpecialbarcodeRetModel retModel : specialbarcodeList) {
//                    if (platformMap.containsKey(retModel.getPsCSkuId())) {
//                        mapList = platformMap.get(retModel.getPsCSkuId());
//                    } else {
//                        mapList = Lists.newArrayList();
//                    }
//                    mapList.add(retModel);
//                    platformMap.put(retModel.getPsCSkuId(), mapList);
//                    if (skuProMap.containsKey(retModel.getPsCSkuId())) {
//                        if (proPlatformMap.containsKey(skuProMap.get(retModel.getPsCSkuId()))) {
//                            proMapList = proPlatformMap.get(skuProMap.get(retModel.getPsCSkuId()));
//                        } else {
//                            proMapList = Lists.newArrayList();
//                        }
//                        proMapList.add(retModel);
//                        proPlatformMap.put(skuProMap.get(retModel.getPsCSkuId()), proMapList);
//                    }
//                }
//            }
//        }
//        for (StCProductStrategyItemDO productStrategyItem : productStrategyItemList) {
//            try {
//                if (proPlatformMap.containsKey(productStrategyItem.getPsCProEcode())
//                        && productStrategyItem.getCpCShopId() != null) {
//                    List<SpecialbarcodeRetModel> platformList = proPlatformMap.get(productStrategyItem.getPsCProEcode());
//                    platformList = platformList.stream().filter(x ->
//                            productStrategyItem.getCpCShopId().equals(x.getCpCShopId())).collect(Collectors.toList());
//                    if (CollectionUtils.isEmpty(platformList)) {
//                        throw new NDSException(String.format("商品编码[%s]匹配不到平台数据", productStrategyItem.getPsCProEcode()));
//                    }
//                } else {
//                    throw new NDSException(String.format("商品编码[%s]匹配不到平台数据", productStrategyItem.getPsCProEcode()));
//                }
//            } catch (Exception e) {
//                String key = productStrategyItem.getCpCShopTitle() + "商品编码" + productStrategyItem.getPsCProEcode();
//                errMsgMap.put(productStrategyItem.getCpCShopTitle() + "," + productStrategyItem.getPsCProEcode(), key + e.getMessage());
//            }
//        }
//        return itemList;
//    }
//
//    /**
//     * 补足冗余信息
//     *
//     * @param productStrategyItem
//     */
//    private void getImportProductStrategyItem(StCProductStrategyItemDO productStrategyItem, Map<String, CpShop> shopMap) {
//        if (productStrategyItem.getStockScale() == null) {
//            throw new NDSException("库存比例不能为空");
//        } else {
//            if (productStrategyItem.getStockScale().compareTo(BigDecimal.valueOf(0)) == -1) {
//                throw new NDSException("库存比例不能为负数");
//            }
//            if (productStrategyItem.getStockScale().compareTo(BigDecimal.valueOf(100)) == 1) {
//                throw new NDSException("库存比例不能超过100");
//            }
//        }
//        if (productStrategyItem.getLowStock() == null) {
//            throw new NDSException("低库存数不能为空");
//        } else {
//            if (productStrategyItem.getLowStock() < 0) {
//                throw new NDSException("低库存数不能为负数");
//            }
//        }
//    }
////
////    /**
////     * 获取渠道商品
////     *
////     * @param stCProductStrategyItemDOList
////     * @return
////     */
////    private List<SgChannelProductQueryForSTResult> getChannelProduct(List<StCProductStrategyItemDO> stCProductStrategyItemDOList) {
////        List<SgChannelProductQueryForSTResult> newSgChannelProductQueryForSTResultList = new ArrayList<>();
////        SgChannelProductQueryForSTRequest sgChannelProductQueryForSTRequest = new SgChannelProductQueryForSTRequest();
////        //商品编码
////        List<String> psCProEcodeList = stCProductStrategyItemDOList.stream().map(x -> x.getPsCProEcode()).collect(Collectors.toList());
////        sgChannelProductQueryForSTRequest.setPsCProEcodeList(psCProEcodeList);
////
////        if (log.isDebugEnabled()) {
////            log.debug(LogUtil.format(" 【店铺商品策略】查询全量渠道商品入参:" + JSONObject.toJSONString(sgChannelProductQueryForSTRequest));
////        }
////        String pageNum = propertiesConf.getProperty("r3.st.queryChannelProduct.pageNum");
////        if (StringUtils.isEmpty(pageNum)) {
////            pageNum = "2000";
////        }
////        Integer count = rpcSgService.queryChannelProductCount(sgChannelProductQueryForSTRequest);
////        if (count.intValue() == 0) {
////            return new ArrayList<>();
////        }
////        sgChannelProductQueryForSTRequest.setPageNum(Integer.valueOf(pageNum));
////        ListPageUtil pageResultList = new ListPageUtil(count, Integer.valueOf(pageNum));
////        for (int i = 1; i <= pageResultList.getPageCount(); i++) {
////            List<SgChannelProductQueryForSTResult> tempList = rpcSgService.queryChannelProduct(sgChannelProductQueryForSTRequest);
////            if (tempList != null && !tempList.isEmpty()) {
////                newSgChannelProductQueryForSTResultList.addAll(tempList);
////                Optional<SgChannelProductQueryForSTResult> resultOp = tempList.stream().max(Comparator.comparingLong(SgChannelProductQueryForSTResult::getId));
////                if (resultOp.isPresent()) {
////                    SgChannelProductQueryForSTResult sgChannelProductQueryForSTResult = resultOp.get();
////                    sgChannelProductQueryForSTRequest.setId(sgChannelProductQueryForSTResult.getId());
////                }
////            }
////
////            if (i == pageResultList.getPageCount()) {
////                break;
////            }
////        }
////        return newSgChannelProductQueryForSTResultList;
////    }
////
////    /**
////     * @Description 根据查询的渠道商品跟本地商品信息进行比较，获取最新的需要保存的店铺商品
////     **/
////    private List<StCProductStrategyItemDO> getShopProductStrategyItemList(List<StCProductStrategyItemDO> stCProductStrategyItemDOList,
////                                                                          List<SgChannelProductQueryForSTResult> resultList,
////                                                                          List<String> errMsgList) {
////        //查询渠道信息
////        List<SgBChannelDef> channelDefList = new ArrayList<>();
////        if (channelTypeByShop != null) {
////            SgChannelProductQueryForSTRequest sgChannelProductQueryForSTRequest = new SgChannelProductQueryForSTRequest();
////            sgChannelProductQueryForSTRequest.setCpCShopChannelType(channelTypeByShop);
////            channelDefList = rpcSgService.queryChannelInfo(sgChannelProductQueryForSTRequest);
////        }
////        List<StCProductStrategyItemDO> newStCProductStrategyItemList = new ArrayList<>();
////        Map<String, List<SgChannelProductQueryForSTResult>> map = resultList.stream().collect(Collectors.groupingBy(SgChannelProductQueryForSTResult::getPsCProEcode));
////        Map<String, List<SgChannelProductQueryForSTResult>> newMap = new HashMap<>();
////        /**
////         * 根据渠道商品来判断是否包含excel的数据，zyj-20200418
////         */
////        if (CollectionUtils.isNotEmpty(channelDefList)) {
////            /**
////             * 过滤渠道类型
////             */
////            for (String proEcode : map.keySet()) {
////                List<SgChannelProductQueryForSTResult> results = map.get(proEcode);
////                /**
////                 * 渠道商品查询，是全渠道，故而需要过滤前端传来的渠道类型
////                 */
////                List<SgChannelProductQueryForSTResult> sgChannelProductQueryForSTResultList = new ArrayList<>();
////                for (SgChannelProductQueryForSTResult sgChannelProductQueryForSTResult : results) {
////                    List<SgBChannelDef> sgBChannelDefs = channelDefList.stream().filter(x -> x.getCpCShopTitle().equals(sgChannelProductQueryForSTResult.getCpCShopTitle())).collect(Collectors.toList());
////                    if (CollectionUtils.isNotEmpty(sgBChannelDefs)) {
////                        sgChannelProductQueryForSTResultList.add(sgChannelProductQueryForSTResult);
////                    }
////                }
////                newMap.put(proEcode, sgChannelProductQueryForSTResultList);
////            }
////        }
////        if (newMap != null) {
////            for (String proEcode : newMap.keySet()) {
////                /**
////                 * 获取渠道商品
////                 */
////                List<SgChannelProductQueryForSTResult> results = newMap.get(proEcode);
////                for (StCProductStrategyItemDO stCProductStrategyItemDO : stCProductStrategyItemDOList) {
////                    List<SgChannelProductQueryForSTResult> sgChannelProductQueryForSTResultList =
////                            results.stream().filter(x -> x.getCpCShopTitle().equals(stCProductStrategyItemDO.getCpCShopTitle())).collect(Collectors.toList());
////                    if (CollectionUtils.isEmpty(sgChannelProductQueryForSTResultList)) {
////                        /**
////                         * excel上的店铺，款号在渠道商品找不到
////                         */
////                        errMsgList.add("店铺:" + stCProductStrategyItemDO.getCpCShopTitle() + " 款号:" + stCProductStrategyItemDO.getPsCProEcode() + " 不存在渠道商品");
////                    } else {
////                        /**
////                         * 渠道商品包含excel，库存比例用excel
////                         */
////                        for (SgChannelProductQueryForSTResult sgChannelProductQueryForSTResult : sgChannelProductQueryForSTResultList) {
////                            StCProductStrategyItemDO productStrategyItemDO = new StCProductStrategyItemDO();
////                            BeanUtils.copyProperties(stCProductStrategyItemDO, productStrategyItemDO);
////                            if (NumberUtils.isDigits(sgChannelProductQueryForSTResult.getNumiid())) {
////                                productStrategyItemDO.setPtProId(sgChannelProductQueryForSTResult.getNumiid());
////                            } else {
////                                errMsgList.add("平台商品ID必须为纯数字" + sgChannelProductQueryForSTResult.getNumiid());
////                            }
////                            if (NumberUtils.isDigits(sgChannelProductQueryForSTResult.getSkuId())) {
////                                productStrategyItemDO.setPtSkuId(sgChannelProductQueryForSTResult.getSkuId());
////                            } else {
////                                errMsgList.add("平台条码ID必须为纯数字" + sgChannelProductQueryForSTResult.getSkuId());
////                            }
////                            productStrategyItemDO.setPsCSkuEcode(sgChannelProductQueryForSTResult.getPsCSkuEcode());
////                            productStrategyItemDO.setPsCProId(sgChannelProductQueryForSTResult.getPsCProId());
////                            productStrategyItemDO.setPsCProEname(sgChannelProductQueryForSTResult.getPsCProEname());
////                            productStrategyItemDO.setPsCProEcode(sgChannelProductQueryForSTResult.getPsCProEcode());
////                            productStrategyItemDO.setCpCShopId(sgChannelProductQueryForSTResult.getCpCShopId());
////                            newStCProductStrategyItemList.add(productStrategyItemDO);
////                        }
////
////                    }
////                }
////                /**
////                 * 渠道商品不在excel的，库存比例为0
////                 */
////                List<String> shopTitleList = stCProductStrategyItemDOList.stream().map(x -> x.getCpCShopTitle()).collect(Collectors.toList());
////                List<SgChannelProductQueryForSTResult> resultsNotExistExcel = results.stream().filter(x -> !shopTitleList.contains(x.getCpCShopTitle())).collect(Collectors.toList());
////                if (CollectionUtils.isNotEmpty(resultsNotExistExcel)) {
////                    for (SgChannelProductQueryForSTResult sgChannelProductQueryForSTResult : resultsNotExistExcel) {
////                        StCProductStrategyItemDO stCProductNotExist = new StCProductStrategyItemDO();
////                        BeanUtils.copyProperties(sgChannelProductQueryForSTResult, stCProductNotExist);
////                        if (NumberUtils.isDigits(sgChannelProductQueryForSTResult.getNumiid())) {
////                            stCProductNotExist.setPtProId(sgChannelProductQueryForSTResult.getNumiid());
////                        } else {
////                            errMsgList.add("平台商品ID必须为纯数字" + sgChannelProductQueryForSTResult.getNumiid());
////                        }
////                        if (NumberUtils.isDigits(sgChannelProductQueryForSTResult.getSkuId())) {
////                            stCProductNotExist.setPtSkuId(sgChannelProductQueryForSTResult.getSkuId());
////                        } else {
////                            errMsgList.add("平台条码ID必须为纯数字" + sgChannelProductQueryForSTResult.getSkuId());
////                        }
////                        stCProductNotExist.setPsCSkuId(sgChannelProductQueryForSTResult.getPsCSkuId());
////                        stCProductNotExist.setPsCSkuEcode(sgChannelProductQueryForSTResult.getPsCSkuEcode());
////                        stCProductNotExist.setPsCProId(sgChannelProductQueryForSTResult.getPsCProId());
////                        stCProductNotExist.setPsCProEname(sgChannelProductQueryForSTResult.getPsCProEname());
////                        stCProductNotExist.setPsCProEcode(sgChannelProductQueryForSTResult.getPsCProEcode());
////                        stCProductNotExist.setCpCShopId(sgChannelProductQueryForSTResult.getCpCShopId());
////                        stCProductNotExist.setCpCShopTitle(sgChannelProductQueryForSTResult.getCpCShopTitle());
////                        stCProductNotExist.setLowStock(0L);
////                        stCProductNotExist.setStockScale(new BigDecimal("0"));
////                        newStCProductStrategyItemList.add(stCProductNotExist);
////                    }
////                }
////            }
////        }
////        return newStCProductStrategyItemList;
////    }
////
////    /**
////     * 获取渠道商品
////     *
////     * @param stCProductStrategyItemDOList
////     * @return
////     */
////    private List<SgChannelProductQueryForSTResult> getSingleChannelProduct(List<StCProductStrategyItemDO> stCProductStrategyItemDOList) {
////        List<SgChannelProductQueryForSTResult> resultList = new ArrayList<>();
////        for (StCProductStrategyItemDO stCProductStrategyItemDO : stCProductStrategyItemDOList) {
////            SgChannelProductQueryForSTRequest sgChannelProductQueryForSTRequest = new SgChannelProductQueryForSTRequest();
////            sgChannelProductQueryForSTRequest.setPsCProEcode(stCProductStrategyItemDO.getPsCProEcode());
////            resultList.addAll(rpcSgService.queryChannelProduct(sgChannelProductQueryForSTRequest));
////        }
////        return resultList;
////    }
//}
