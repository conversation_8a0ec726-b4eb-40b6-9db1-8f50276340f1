package com.jackrain.nea.st.model.request;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2022/8/8 16:44
 * @Description
 */
@Data
public class StCExpressCostPoi implements Serializable {

    /**
     * 仓库ID
     */
    private Long cpCPhyWarehouseId;

    /**
     * 仓库名称
     */
    private String warehouseName;

    /**
     * 物流公司ID
     */
    private Long cpCLogisticsId;

    /**
     * 物流公司名称
     */
    private String logisticsName;

    /**
     * 备注
     */
    private String remark;

    /**
     * 开始日期，日期格式
     */
    private Date startDateForDate;

    /**
     * 开始日期
     */
    private String startDate;

    /**
     * 结束日期，日期格式
     */
    private Date endDateForDate;

    /**
     * 结束日期
     */
    private String endDate;

    /**
     * 省份ID
     */
    private Long provinceId;

    /**
     * 省份名称
     */
    private String provinceName;

    /**
     * 城市ID
     */
    private Long cityId;

    /**
     * 城市名称
     */
    private String cityName;

    /**
     * 起始重量，数值
     */
    private BigDecimal startWeightNum;

    /**
     * 起始重量
     */
    private String startWeight;

    /**
     * 结束重量，数值
     */
    private BigDecimal endWeightNum;

    /**
     * 结束重量
     */
    private String endWeight;

    /**
     * 快递费用，数值
     */
    private BigDecimal priceExpressNum;

    /**
     * 快递费用
     */
    private String priceExpress;

    /**
     * 首重，数值
     */
    private BigDecimal priceFirstWeightNum;

    /**
     * 首重
     */
    private String priceFirstWeight;

    /**
     * 行数
     */
    private Integer rowNum;
}
