package com.jackrain.nea.st.model.request;

import com.alibaba.fastjson.annotation.JSONField;
import com.jackrain.nea.st.model.table.StCDepositPreSaleSinkDO;
import com.jackrain.nea.st.model.table.StCDepositPreSaleSinkItemDO;
import com.jackrain.nea.st.model.table.StCDepositPreSaleSinkLogisticsDO;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * description: 定实体类金预售预下沉订
 * @Author:  liuwenjin
 * @Date 2021/9/23 4:07 下午
 */
@Data
public class StCDepositPreSaleSinkRequest implements Serializable {
    @JSONField(name = "ST_C_DEPOSIT_PRE_SALE_SINK")
    private StCDepositPreSaleSinkDO stCDepositPreSaleSinkDO;

    @JSONField(name="ST_C_DEPOSIT_PRE_SALE_SINK_ITEM")
    private List<StCDepositPreSaleSinkItemDO> stCDepositPreSaleSinkItemDOList;

    @JSONField(name="ST_C_DEPOSIT_PRE_SALE_SINK_LOGISTICS")
    private  List<StCDepositPreSaleSinkLogisticsDO> stCDepositPreSaleSinkLogisticsDOList;
}
