package com.jackrain.nea.st.model.result;

import com.jackrain.nea.st.model.table.StCAutoCheckAutoTimeDO;
import com.jackrain.nea.st.model.table.StCAutoCheckDO;
import com.jackrain.nea.st.model.table.StCAutoCheckExcludeProductDO;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @program: r3-st
 * @description: 自动审核策略子表集
 * @author: caomalai
 * @create: 2022-07-28 11:39
 **/
@Data
public class StCAutoCheckResult implements Serializable {

    private StCAutoCheckDO stCAutoCheckDO;

    private List<StCAutoCheckExcludeProductDO> excludeProducts;

    private List<StCAutoCheckAutoTimeDO> autoTimes;
}
