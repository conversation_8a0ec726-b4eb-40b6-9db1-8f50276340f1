package com.jackrain.nea.st.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.*;
import com.jackrain.nea.st.model.base.SubBaseModel;
import lombok.Data;

@TableName(value = "ST_C_EXPRESS_PACKAGE")
@Data
public class StCExpressPackageDO extends SubBaseModel {
    @JSO<PERSON>ield(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    @JSONField(name = "ST_C_EXPRESS_ID")
    private Long stCExpressId;

    @JSONField(name = "PKG_ATTRIBUTE")
    private String pkgAttribute;

    @JSONField(name = "CONDITIONS")
    private String conditions;

    @J<PERSON><PERSON>ield(name = "BEGIN_VAL")
    private Long beginVal;

    @JSONField(name = "END_VAL")
    private Long endVal;

    @JSONField(name = "<PERSON>W<PERSON>RE<PERSON><PERSON>")
    private String ownerename;

    @JSONField(name = "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>")
    private String modifierename;
}