package com.jackrain.nea.st.model.vo;

import lombok.Data;

import java.util.Map;

/**
 * @program: r3-st
 * @description: 仓库物流设置导入实体
 * @author: caomalai
 * @create: 2022-07-01 15:19
 **/
@Data
public class StCWarehouseLogisticStrategyImpVo{
    public static final String cellStr = "cell_";
    public static final String rowStr = "row_";

    private String cpCPhyWarehouseEname;
    private String isExpire;
    private String cpCLogisticsEname;
    private String logisticsSupplierEname;
    private String enableMaximumOrderVolumeZh;
    private String enableMaximumOrderVolume;
    private Integer maximumOrderVolume;
    private Integer itemPriority;
    private String expireStartTime;
    private String expireEndTime;
    //行号
    private int rowNum;

    private String desc;

    public static StCWarehouseLogisticStrategyImpVo importCreate(int index, StCWarehouseLogisticStrategyImpVo impVo, Map<String, String> columnMap) {
        try {
            //仓库名称
            impVo.setCpCPhyWarehouseEname(columnMap.get(rowStr + index + cellStr + 0));
            //仓库名称
            impVo.setIsExpire(columnMap.get(rowStr + index + cellStr + 1) == null ? null : columnMap.get(rowStr + index + cellStr + 1));
            //物流公司
            impVo.setCpCLogisticsEname(columnMap.get(rowStr + index + cellStr + 2));
            //物流计算公司
            impVo.setLogisticsSupplierEname(columnMap.get(rowStr + index + cellStr + 3));
            //优先级
            impVo.setItemPriority(columnMap.get(rowStr + index + cellStr + 4) == null ? null : Integer.valueOf(columnMap.get(rowStr + index + cellStr + 4)));
            //失效开始时间
            impVo.setExpireStartTime(columnMap.get(rowStr + index + cellStr + 5) == null ? null : columnMap.get(rowStr + index + cellStr + 5));
            //失效截止时间
            impVo.setExpireEndTime(columnMap.get(rowStr + index + cellStr + 6) == null ? null : columnMap.get(rowStr + index + cellStr + 6));
            //启用最大接单量
            impVo.setEnableMaximumOrderVolumeZh(columnMap.get(rowStr + index + cellStr + 7));
            //最大接单量
            impVo.setMaximumOrderVolume(columnMap.get(rowStr + index + cellStr + 8) == null ? null : Integer.valueOf(columnMap.get(rowStr + index + cellStr + 8)));
        } catch (Exception e) {

        }
        impVo.setRowNum(index + 1);
        return impVo;
    }
}
