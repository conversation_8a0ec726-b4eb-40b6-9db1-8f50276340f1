package com.jackrain.nea.st.model.request;

import com.alibaba.fastjson.annotation.JSONField;
import com.jackrain.nea.st.model.table.StCVipcomMailDO;
import com.jackrain.nea.st.model.table.StCVipcomProjectDO;
import lombok.Data;

import java.io.Serializable;

/**
 * 唯品会邮件
 *
 * <AUTHOR>
 * @Date 2019/4/24
 */
@Data
public class VipcomMailRequest implements Serializable {
    @JSONField(name = "ST_C_VIPCOM_MAIL")
    private StCVipcomMailDO stCVipcomMailDO;
}
