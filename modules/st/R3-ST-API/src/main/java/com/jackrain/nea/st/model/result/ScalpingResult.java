package com.jackrain.nea.st.model.result;

import com.jackrain.nea.st.model.table.StCScalpingDO;
import com.jackrain.nea.st.model.table.StCScalpingLogisticsDO;
import com.jackrain.nea.st.model.table.StCScalpingReplaceProDO;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @program: r3-st
 * @author: Lijp
 * @create: 2019-07-16 16:25
 */
@Data
public class ScalpingResult implements Serializable {

    private StCScalpingDO stCScalpingDO;

    private List<StCScalpingLogisticsDO> stCScalpingLogisticsDOList;

    private List<StCScalpingReplaceProDO> stCScalpingReplaceProDOList;
}
