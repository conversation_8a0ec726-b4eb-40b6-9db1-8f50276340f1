package com.jackrain.nea.st.model.result;

import com.jackrain.nea.st.model.table.StCExpressPriceStrategyDO;
import com.jackrain.nea.st.model.table.StCExpressPriceStrategyItemDO;
import lombok.Data;

import java.io.Serializable;
import java.util.List;


/**
 * <AUTHOR>
 * @Date 2025/5/13
 * @Description 快递报价设置关系表
 * @Version 1.0
 */
@Data
public class StCExpressPriceStrategyRelation implements Serializable {

    /**
     * 快递报价主表
     */
    private StCExpressPriceStrategyDO stCExpressPriceStrategyDO;

    /**
     * 快递报价明细
     */
    private List<StCExpressPriceStrategyItemDO> stCExpressPriceStrategyItemDOList;
}
