package com.jackrain.nea.st.model.result;

import com.jackrain.nea.st.model.table.StCDistributionDO;
import com.jackrain.nea.st.model.table.StCDistributionItemDO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * @program: r3-st
 * @author: huang.z<PERSON><PERSON>
 * @create: 2019-07-03 14:15
 */

@Data
@AllArgsConstructor
@NoArgsConstructor
public class DistributionQueryResult implements Serializable {

    private StCDistributionDO distribution;

    private List<StCDistributionItemDO> distributionItemList;
}
