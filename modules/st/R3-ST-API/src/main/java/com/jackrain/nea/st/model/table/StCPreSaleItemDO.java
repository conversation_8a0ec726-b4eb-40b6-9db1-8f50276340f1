package com.jackrain.nea.st.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.*;
import com.jackrain.nea.es.annotations.Document;
import com.jackrain.nea.es.annotations.Field;
import com.jackrain.nea.es.constans.FieldType;
import com.jackrain.nea.st.model.base.SubBaseModel;
import lombok.Data;

import java.util.Date;

@TableName(value = "ST_C_PRE_SALE_ITEM")
@Data
@Document(index = "st_c_pre_sale",type = "st_c_pre_sale_item")
public class StCPreSaleItemDO extends SubBaseModel {
    @JSONField(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "id", type = IdType.INPUT)
    @Field(type = FieldType.Long)
    private Long id;

    @JSONField(name = "ST_C_PRE_SALE_ID")
    @Field(type = FieldType.Long)
    private Long stCPreSaleId;

    @JSONField(name = "ST_C_PRE_SALE_ENAME")
    @Field(type = FieldType.Keyword)
    private String stCPreSaleEname;

    @JSONField(name = "PRE_SALE_WAY")
    @Field(type = FieldType.Keyword)
    private Integer preSaleWay;

    @JSONField(name = "RECOGNITION_RULE")
    @Field(type = FieldType.Integer)
    private Integer recognitionRule;

    @JSONField(name = "RECOGNITION_RULE_CONTENT")
    @Field(type = FieldType.Keyword)
    private String recognitionRuleContent;

    @JSONField(name = "DELIVERY_TYPE")
    @Field(type = FieldType.Integer)
    private Integer deliveryType;

    @JSONField(name = "agreed_delivery_date")
    @Field(type = FieldType.Keyword)
    private Date agreedDeliveryDate;

    @JSONField(name = "ADVANCE_DELIVERY_DAYS")
    @Field(type = FieldType.Integer)
    private Integer advanceDeliveryDays;


}