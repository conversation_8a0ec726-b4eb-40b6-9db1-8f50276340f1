package com.jackrain.nea.st.model.result;

import com.jackrain.nea.cpext.model.table.CpLogistics;
import com.jackrain.nea.st.model.table.StCWarehouseLogisticsItemDO;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @Author: huang.z<PERSON>
 * @Date: 2019-08-13
 * @Version 1.0
 */

@Data
public class WarehouseLogisticsInfoResult implements Serializable {

    private List<CpLogistics> cpLogisticsList;

    private List<StCWarehouseLogisticsItemDO> warehouseLogisticsItems;
}