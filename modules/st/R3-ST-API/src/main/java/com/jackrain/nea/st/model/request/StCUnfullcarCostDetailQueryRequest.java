package com.jackrain.nea.st.model.request;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 零担报价明细查询请求
 */
@Data
public class StCUnfullcarCostDetailQueryRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 零担报价设置ID
     */
    private Long unfullcarCostId;

    /**
     * 重量
     */
    private BigDecimal weight;

    /**
     * 省份ID
     */
    private Long provinceId;

    /**
     * 城市ID
     */
    private Long cityId;
}
