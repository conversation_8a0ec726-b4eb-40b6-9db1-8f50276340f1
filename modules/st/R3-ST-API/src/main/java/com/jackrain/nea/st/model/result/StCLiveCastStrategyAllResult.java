package com.jackrain.nea.st.model.result;

import com.jackrain.nea.st.model.table.StCLiveCastStrategyDO;
import com.jackrain.nea.st.model.table.StCLiveCastStrategyItemDO;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 直播策略单查结果
 */
@Data
public class StCLiveCastStrategyAllResult implements Serializable {

    // 主表
    private StCLiveCastStrategyDO liveCastStrategyDO;
    // 列表
    private List<StCLiveCastStrategyItemDO> liveCastStrategyItemDOList;

}
