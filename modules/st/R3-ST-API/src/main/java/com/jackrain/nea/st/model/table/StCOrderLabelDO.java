package com.jackrain.nea.st.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jackrain.nea.es.annotations.Document;
import com.jackrain.nea.es.annotations.Field;
import com.jackrain.nea.es.constans.FieldType;
import java.util.Date;

import com.jackrain.nea.st.model.base.SubBaseModel;
import lombok.Data;

/**
 * <AUTHOR>
 */
@TableName(value = "st_c_order_label")
@Data
@Document(index = "st_c_order_label",type = "st_c_order_label")
public class StCOrderLabelDO extends SubBaseModel {
    @JSONField(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "id", type = IdType.INPUT)
    @Field(type = FieldType.Long)
    private Long id;

    @JSONField(name = "TITLE")
    @Field(type = FieldType.Keyword)
    private String title;

    @JSONField(name = "ST_C_CUSTOM_LABEL_ID")
    @Field(type = FieldType.Keyword)
    private String stCCustomLabelId;

    @JSONField(name = "ST_C_CUSTOM_LABEL_ENAME")
    @Field(type = FieldType.Keyword)
    private String stCCustomLabelEname;

    @JSONField(name = "TIME_TYPE")
    @Field(type = FieldType.Keyword)
    private String timeType;

    @JSONField(name = "BEGIN_TIME")
    @Field(type = FieldType.Date)
    private Date beginTime;

    @JSONField(name = "END_TIME")
    @Field(type = FieldType.Date)
    private Date endTime;

    @JSONField(name = "REMAKE")
    @Field(type = FieldType.Keyword)
    private String remake;

    @JSONField(name = "STATUS")
    @Field(type = FieldType.Integer)
    private Integer status;

    @JSONField(name = "AD_ORG_ID")
    @Field(type = FieldType.Long)
    private Long adOrgId;

    @JSONField(name = "ISACTIVE")
    @Field(type = FieldType.Keyword)
    private String isactive;

    @JSONField(name = "AD_CLIENT_ID")
    @Field(type = FieldType.Long)
    private Long adClientId;

    @JSONField(name = "OWNERID")
    @Field(type = FieldType.Long)
    private Long ownerid;

    @JSONField(name = "OWNERENAME")
    @Field(type = FieldType.Keyword)
    private String ownerename;

    @JSONField(name = "OWNERNAME")
    @Field(type = FieldType.Keyword)
    private String ownername;

    @JSONField(name = "CREATIONDATE")
    @Field(type = FieldType.Date)
    private Date creationdate;

    @JSONField(name = "MODIFIERID")
    @Field(type = FieldType.Long)
    private Long modifierid;

    @JSONField(name = "MODIFIERENAME")
    @Field(type = FieldType.Keyword)
    private String modifierename;

    @JSONField(name = "MODIFIERNAME")
    @Field(type = FieldType.Keyword)
    private String modifiername;

    @JSONField(name = "MODIFIEDDATE")
    @Field(type = FieldType.Date)
    private Date modifieddate;

    @JSONField(name = "VOID_ID")
    @Field(type = FieldType.Long)
    private Long voidId;

    @JSONField(name = "STATUS_ID")
    @Field(type = FieldType.Long)
    private Long statusId;

    @JSONField(name = "VOID_DATE")
    @Field(type = FieldType.Date)
    private Date voidDate;

    @JSONField(name = "STATUS_DATE")
    @Field(type = FieldType.Date)
    private Date statusDate;

    @JSONField(name = "FINAL_ID")
    @Field(type = FieldType.Long)
    private Long finalId;

    @JSONField(name = "FIANL_DATE")
    @Field(type = FieldType.Date)
    private Date finalDate;

}