package com.jackrain.nea.st.model.request;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2019/7/18 19:30
 */

@Data
public class CompensateQueryRequest implements Serializable {

    private Long phyWarehouseId;

    private Long logisticsId;

    private Date currentDate;

    private BigDecimal priceList;

    private BigDecimal price;

    private String sourceCode;
}
