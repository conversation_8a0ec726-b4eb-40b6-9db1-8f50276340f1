package com.jackrain.nea.st.model.vo;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.jackrain.nea.st.model.base.SubBaseModel;
import com.jackrain.nea.st.model.table.StCAutoCheckExcludeProductDO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * @program: r3-st
 * @description: 自动审核页面返回实体
 * @author: caomalai
 * @create: 2022-07-28 20:50
 **/
@Data
public class StCAutoCheckVo extends SubBaseModel {

    @ApiModelProperty(value = "id")
    @JSONField(name = "ID")
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    @ApiModelProperty(value = "店铺编码")
    @JSONField(name = "CP_C_SHOP_ID")
    private Long cpCShopId;

    @ApiModelProperty(value = "店铺编码")
    @JSONField(name = "CP_C_SHOP_ECODE")
    private String cpCShopEcode;

    @ApiModelProperty(value = "店铺名称")
    @JSONField(name = "CP_C_SHOP_TITLE")
    private String cpCShopTitle;

    @ApiModelProperty(value = "自动审核订单")
    @JSONField(name = "IS_AUTOCHECK_ORDER")
    private String isAutocheckOrder;

    @ApiModelProperty(value = "自动审核货到付款")
    @JSONField(name = "IS_AUTOCHECK_PAY")
    private String isAutocheckPay;

    @ApiModelProperty(value = "有备注订单自动审核")
    @JSONField(name = "IS_REMARK_AUTOCHECK")
    private String isRemarkAutocheck;

    @ApiModelProperty(value = "物流公司id")
    @JSONField(name = "CP_C_LOGISTICS_ID")
    private String cpCLogisticsId;

    @ApiModelProperty(value = "物流公司编码")
    @JSONField(name = "CP_C_LOGISTICS_ECODE")
    private String cpCLogisticsEcode;

    @ApiModelProperty(value = "物流公司名称")
    @JSONField(name = "CP_C_LOGISTICS_ENAME")
    private String cpCLogisticsEname;

    @ApiModelProperty(value = "收货人所在省id")
    @JSONField(name = "CP_C_REGION_PROVINCE_ID")
    private String cpCRegionProvinceId;

    @ApiModelProperty(value = "收货人所在省编码")
    @JSONField(name = "CP_C_REGION_PROVINCE_ECODE")
    private String cpCRegionProvinceEcode;

    @ApiModelProperty(value = "收货人所在省名称")
    @JSONField(name = "CP_C_REGION_PROVINCE_ENAME")
    private String cpCRegionProvinceEname;

    @ApiModelProperty(value = "收货人地址")
    @JSONField(name = "RECEIVER_ADDRESS")
    private String receiverAddress;

    @ApiModelProperty(value = "下单开始时间")
    @JSONField(name = "BEGIN_TIME")
    private Date beginTime;

    @ApiModelProperty(value = "下单结束时间")
    @JSONField(name = "END_TIME")
    private Date endTime;

    @ApiModelProperty(value = "订单限制金额上限")
    @JSONField(name = "LIMIT_PRICE_UP")
    private BigDecimal limitPriceUp;

    @ApiModelProperty(value = "订单限制金额下限")
    @JSONField(name = "LIMIT_PRICE_DOWN")
    private BigDecimal limitPriceDown;

    @ApiModelProperty(value = "审核等待时间")
    @JSONField(name = "WAIT_TIME")
    private Integer waitTime;

    @ApiModelProperty(value = "关键字")
    @JSONField(name = "KEYWORD")
    private String keyword;

    @ApiModelProperty(value = "备注")
    @JSONField(name = "REMARK")
    private String remark;

    @ApiModelProperty(value = "作废人id")
    @JSONField(name = "DELID")
    private Long delid;

    @ApiModelProperty(value = "作废人姓名")
    @JSONField(name = "DELENAME")
    private String delename;

    @ApiModelProperty(value = "作废人用户名")
    @JSONField(name = "DELNAME")
    private String delname;

    @ApiModelProperty(value = "作废时间")
    @JSONField(name = "DEL_TIME")
    private Date delTime;

    @ApiModelProperty(value = "是否全赠品订单自动审核")
    @JSONField(name = "IS_FULL_GIFT_ORDER")
    private String isFullGiftOrder;

    @ApiModelProperty(value = "是否手工单自动审核")
    @JSONField(name = "IS_MANUAL_ORDER")
    private String isManualOrder;

    @ApiModelProperty(value = "买家备注")
    @JSONField(name = "BUYER_REMARK")
    private String buyerRemark;

    @ApiModelProperty(value = "卖家备注")
    @JSONField(name = "SELLER_REMARK")
    private String sellerRemark;

    @ApiModelProperty(value = "换货单自动审核")
    @JSONField(name = "IS_AUTOCHECK_EXCHANGE")
    private String isAutocheckExchange;

    @ApiModelProperty(value = "1:指定sku 2:排除sku")
    @JSONField(name = "EXCLUDE_SKU_TYPE")
    private Integer excludeSkuType;

    @ApiModelProperty(value = "指定或者排除sku内容，多个参数以；隔开")
    @JSONField(name = "SKU_CONTENT")
    private String skuContent;

    @ApiModelProperty(value = "订单类型，正常订单=1，预售订单=2，补发订单-3，虚拟订单=4,换货订单=5，货到付款=6，手工订单=7；多个以英文逗号隔开")
    @JSONField(name = "ORDER_TYPE")
    private String orderType;

    @ApiModelProperty(value = "特殊订单，货到付款订单=1，手工创建订单=2，换货订单订单=3，商品明细都为赠品=4；多个英文逗号隔开")
    @JSONField(name = "SPECIAL_ORDER")
    private String specialOrder;

    @ApiModelProperty(value = "生效条件")
    @JSONField(name = "EFFECTIVE_CONDITION")
    private String effectiveCondition;

    @ApiModelProperty(value = "是否能合并订单审核")
    @JSONField(name = "IS_MERGE_ORDER")
    private String isMergeOrder;

    @ApiModelProperty(value = "hold单等待时间")
    @JSONField(name = "HOLD_WAIT_TIME")
    private Integer holdWaitTime;

    @ApiModelProperty(value = "反审核等待时间")
    @JSONField(name = "ANTI_AUDIT_WAIT_TIME")
    private Integer antiAuditWaitTime;

    @ApiModelProperty(value = "订单限制金额上限")
    @JSONField(name = "ORDER_DISCOUNT_UP")
    private BigDecimal orderDiscountUp;

    @ApiModelProperty(value = "订单限制金额下限")
    @JSONField(name = "ORDER_DISCOUNT_DOWN")
    private BigDecimal orderDiscountDown;


    @ApiModelProperty(value = "单条码数量上限")
    @JSONField(name = "SINGLE_SKU_NUM")
    private Integer singleSkuNum;

    // 自定义标签档案id
    @JSONField(name = "ST_C_CUSTOM_LABEL_ID")
    private String stCCustomLabelId;

    // 自定义标签档案ename
    @JSONField(name = "ST_C_CUSTOM_LABEL_ENAME")
    private String stCCustomLabelEname;

    /**
     * 排除仓库ID
     */
    @JSONField(name = "CP_C_PHY_WAREHOUSE_IDS")
    private List<Label> cpCPhyWarehouseIds;

    /**
     * 排除业务类型ID
     */
    @JSONField(name = "ST_C_BUSINESS_TYPE_IDS")
    private List<Label> stCBusinessTypeIds;

    @JSONField(name = "ST_C_AUTOCHECK_EXCLUDE_PRODUCT")
    private List<StCAutoCheckExcludeProductDO> excludeProducts;

    @Data
    public static class Label {
        @JSONField(name = "ID")
        private Long id;
        @JSONField(name = "Label")
        private String label;
    }
}
