package com.jackrain.nea.st.model.result;

import com.jackrain.nea.st.model.table.StCBnProblemConfigDO;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 班牛问题清单查询结果
 */
@Data
public class BnProblemConfigQueryResult implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 班牛问题清单列表
     */
    private List<StCBnProblemConfigDO> problemConfigList;
}
