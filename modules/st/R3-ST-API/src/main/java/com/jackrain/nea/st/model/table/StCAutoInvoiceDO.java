package com.jackrain.nea.st.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.*;
import com.jackrain.nea.st.model.base.SubBaseModel;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@TableName(value = "st_c_auto_invoice")
@Data
public class StCAutoInvoiceDO extends SubBaseModel {
    @JSONField(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    @J<PERSON>NField(name = "CP_C_SHOP_ECODE")
    private String cpCShopEcode;

    @J<PERSON><PERSON>ield(name = "CP_C_SHOP_TITLE")
    private String cpCShopTitle;

    @JSONField(name = "IS_AUTO_INVOICE_NOTICE")
    private String isAutoInvoiceNotice;

    @J<PERSON>NField(name = "INVOICE_NODE")
    private Integer invoiceNode;

    @J<PERSON><PERSON>ield(name = "REMARK")
    private String remark;

    @JSONField(name = "IS_AUTO_MERGE_INVOICE")
    private String isAutoMergeInvoice;

    @JSONField(name = "IS_UNAUDIT_INVOICE_ESTATUS")
    private String isUnauditInvoiceEstatus;

    @JSONField(name = "IS_SAME_INVOICE_SHOP")
    private String isSameInvoiceShop;

    @JSONField(name = "IS_SAME_INVOICE_INFO")
    private String isSameInvoiceInfo;

    @JSONField(name = "SAME_INVOICE_INFO_WORD")
    private String sameInvoiceInfoWord;

    @JSONField(name = "IS_SAME_SOURCE_CODE")
    private String isSameSourceCode;

    @JSONField(name = "SAME_SOURCE_CODE_WORD")
    private String sameSourceCodeWord;

    @JSONField(name = "IS_LIMIT_MAXIMUM_AMOUNT")
    private String isLimitMaximumAmount;

    @JSONField(name = "MAXIMUM_AMOUNT")
    private BigDecimal maximumAmount;

    @JSONField(name = "OWNERENAME")
    private String ownerename;

    @JSONField(name = "MODIFIERENAME")
    private String modifierename;

    @JSONField(name = "DELID")
    private Long delid;

    @JSONField(name = "DELENAME")
    private String delename;

    @JSONField(name = "DELNAME")
    private String delname;

    @JSONField(name = "DEL_TIME")
    private Date delTime;

    @JSONField(name = "CP_C_SHOP_ID")
    private String cpCShopId;
}