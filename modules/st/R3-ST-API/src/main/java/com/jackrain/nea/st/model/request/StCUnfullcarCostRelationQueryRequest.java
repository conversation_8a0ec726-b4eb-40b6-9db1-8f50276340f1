package com.jackrain.nea.st.model.request;

import lombok.Data;

import java.io.Serializable;

/**
 * 零担报价关系查询请求
 *
 * <AUTHOR>
 * @Date 2025/7/15
 * @Description 零担报价关系查询请求
 * @Version 1.0
 */
@Data
public class StCUnfullcarCostRelationQueryRequest implements Serializable {

    /**
     * 实体仓id
     */
    private Long cpCPhyWarehouseId;
    
    /**
     * 物流公司id
     */
    private Long cpCLogisticsId;
}
