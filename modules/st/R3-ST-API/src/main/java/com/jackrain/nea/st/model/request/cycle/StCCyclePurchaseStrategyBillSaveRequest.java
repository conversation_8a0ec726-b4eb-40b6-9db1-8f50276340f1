package com.jackrain.nea.st.model.request.cycle;


import com.alibaba.fastjson.annotation.JSONField;
import com.burgeon.r3.sg.core.model.request.SgR3BaseRequest;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @Auther: chen<PERSON>
 * @Date: 2022-09-03 15:30
 * @Description:
 */

@Data
public class StCCyclePurchaseStrategyBillSaveRequest extends SgR3BaseRequest implements Serializable {

    /**
     * 主表信息
     */
    @JSONField(name = "ST_C_CYCLE_PURCHASE_STRATEGY")
    private StCCyclePurchaseStrategySaveRequest saveRequest;

    /**
     * 明细信息
     */
    @J<PERSON><PERSON><PERSON>(name = "ST_C_CYCLE_PURCHASE_STRATEGY_ITEM")
    private List<StCCyclePurchaseStrategyItemSaveRequest> items;
}
