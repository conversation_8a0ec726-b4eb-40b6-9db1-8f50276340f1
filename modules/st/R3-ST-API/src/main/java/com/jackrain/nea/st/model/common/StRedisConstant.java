package com.jackrain.nea.st.model.common;

/**
 * Redis 常量类
 * 
 * <AUTHOR>
 * @since 2023/5/13
 */
public class StRedisConstant {
    
    /**
     * 根据实体仓id和物流公司id查询快递报价关系
     */
    public static final String ST_C_EXPRESS_PRICE_RELATION_KEY = "st:express:price:relation:warehouse:";
    /**
     * 构建快递报价关系缓存key
     * @param warehouseId 实体仓id
     * @param logisticsId 物流公司id
     * @return Redis缓存key
     */
    public static String buildExpressPriceRelationKey(Long warehouseId, Long logisticsId) {
        return ST_C_EXPRESS_PRICE_RELATION_KEY + warehouseId + ":logistics:" + logisticsId;
    }
    /**
     * 根据快递报价策略主表ID查询快递报价策略关系
     */
    public static final String ST_C_EXPRESS_PRICE_STRATEGY_RELATION_BY_ID_KEY = "st:express:price:relation:id:";
    /**
     * 构建根据ID查询快递报价策略关系的缓存key
     * @param id 快递报价策略主表ID
     * @return Redis缓存key
     */
    public static String buildExpressPriceStrategyRelationByIdKey(Long id) {
        return ST_C_EXPRESS_PRICE_STRATEGY_RELATION_BY_ID_KEY + id;
    }

    /**
     * 根据实体仓id和物流公司id查询快运报价关系
     */
    public static final String ST_C_EXPRESS_COST_RELATION_KEY = "st:express:cost:relation:warehouse:";
    /**
     * 构建快运报价关系缓存key
     * @param warehouseId 实体仓id
     * @param logisticsId 物流公司id
     * @return Redis缓存key
     */
    public static String buildExpressCostRelationKey(Long warehouseId, Long logisticsId) {
        return ST_C_EXPRESS_COST_RELATION_KEY + warehouseId + ":logistics:" + logisticsId;
    }
    /**
     * 根据快运报价主表ID查询快运报价关系
     */
    public static final String ST_C_EXPRESS_COST_RELATION_BY_ID_KEY = "st:expressCost:relation:id:";
    /**
     * 构建根据ID查询快运报价关系的缓存key
     * @param id 快运报价主表ID
     * @return Redis缓存key
     */
    public static String buildExpressCostRelationByIdKey(Long id) {
        return ST_C_EXPRESS_COST_RELATION_BY_ID_KEY + id;
    }

    /**
     * 根据实体仓id和物流公司id查询零担报价关系
     */
    public static final String ST_C_UNFULLCAR_COST_RELATION_KEY = "st:unfullcar:cost:relation:warehouse:";
    /**
     * 构建零担报价关系缓存key
     * @param warehouseId 实体仓id
     * @param logisticsId 物流公司id
     * @return Redis缓存key
     */
    public static String buildUnfullcarCostRelationKey(Long warehouseId, Long logisticsId) {
        return ST_C_UNFULLCAR_COST_RELATION_KEY + warehouseId + ":logistics:" + logisticsId;
    }
    /**
     * 根据零担报价主表ID查询零担报价关系
     */
    public static final String ST_C_UNFULLCAR_COST_RELATION_BY_ID_KEY = "st:unfullcarCost:relation:id:";
    /**
     * 构建根据ID查询零担报价关系的缓存key
     * @param id 零担报价主表ID
     * @return Redis缓存key
     */
    public static String buildUnfullcarCostRelationByIdKey(Long id) {
        return ST_C_UNFULLCAR_COST_RELATION_BY_ID_KEY + id;
    }

    /**
     * 根据实体仓id和物流公司id查询整车报价关系
     */
    public static final String ST_C_FULLCAR_COST_RELATION_KEY = "st:fullcar:cost:relation:warehouse:";
    /**
     * 构建整车报价关系缓存key
     * @param warehouseId 实体仓id
     * @param logisticsId 物流公司id
     * @return Redis缓存key
     */
    public static String buildFullcarCostRelationKey(Long warehouseId, Long logisticsId) {
        return ST_C_FULLCAR_COST_RELATION_KEY + warehouseId + ":logistics:" + logisticsId;
    }
    /**
     * 根据整车报价主表ID查询整车报价关系
     */
    public static final String ST_C_FULLCAR_COST_RELATION_BY_ID_KEY = "st:fullcarCost:relation:id:";
    /**
     * 构建根据ID查询整车报价关系的缓存key
     * @param id 整车报价主表ID
     * @return Redis缓存key
     */
    public static String buildFullcarCostRelationByIdKey(Long id) {
        return ST_C_FULLCAR_COST_RELATION_BY_ID_KEY + id;
    }
}
