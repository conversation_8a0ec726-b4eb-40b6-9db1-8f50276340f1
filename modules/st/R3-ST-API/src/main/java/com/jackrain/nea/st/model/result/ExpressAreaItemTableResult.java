package com.jackrain.nea.st.model.result;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @Author: huang.z<PERSON><PERSON>
 * @Date: 2019-08-7
 * @Version 1.0
 */
@Data
public class ExpressAreaItemTableResult implements Serializable {
    @JSONField(name = "TABLE_SIZE")
    private Integer tableSize;
    @JSONField(name = "ST_C_EXPRESS_AREA_ITEM_RESULT")
    private List<ExpressAreaItemResult> itemResultList;
    @JSONField(name = "REGION_TREE_RESULT")
    private List<RegionTreeResult> expressAreaTree;
}
