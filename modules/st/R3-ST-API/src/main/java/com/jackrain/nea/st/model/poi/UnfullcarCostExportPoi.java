package com.jackrain.nea.st.model.poi;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2022/6/18 11:50
 */
@Data
public class UnfullcarCostExportPoi implements Serializable {

    /**
     * 仓库名称
     */
    private String warehouseName;

    /**
     * 物流公司名称
     */
    private String logisticsName;

    /**
     * 开始日期
     */
    private String startDate;

    /**
     * 结束日期
     */
    private String endDate;

    /**
     * 油价联动（%）
     */
    private String oilPriceLinkage;

    /**
     * 备注
     */
    private String remark;

    /**
     * 目的省份
     */
    private String province;

    /**
     * 目的城市
     */
    private String city;

    /**
     * 到货天数
     */
    private String arrivalDays;

    /**
     * 起始重量
     */
    private String startWeight;

    /**
     * 结束重量
     */
    private String endWeight;

    /**
     * 干线费用
     */
    private String trunkFreight;

    /**
     * 提货费
     */
    private String deliveryFee;

    /**
     * 送货费
     */
    private String freight;

//    /**
//     * B类客户送货费
//     */
//    private String customerBDeliveryFee;

    /**
     * 保费
     */
    private String premium;

    /**
     * 卸货费
     */
    private String unloadingFee;

    /**
     * 其他费用
     */
    private String otherFee;
}
