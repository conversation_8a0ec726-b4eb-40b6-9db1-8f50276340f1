package com.jackrain.nea.st.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jackrain.nea.es.annotations.Document;
import com.jackrain.nea.st.model.base.SubBaseModel;
import lombok.Data;

/**
 * @Description: 拆合单原因类型
 * @author: 江家雷
 * @since: 2020/11/12
 * create at : 2020/11/12 11:21
 */
@TableName(value = "ST_C_ORDER_SPLIT_MERGE_TYPE")
@Data
@Document(index = "st_c_order_split_merge_type", type = "st_c_order_split_merge_type")
public class StCOrderSplitMergeType  extends SubBaseModel {

    @JSONField(name = "ID")
    private Long id;

    @JSONField(name = "TYPE")
    private Integer type;

    @JSONField(name = "TYPE_CODE")
    private String typeCode;

    @J<PERSON>NField(name = "TYPE_NAME")
    private String typeName;
}