package com.jackrain.nea.st.model.result;

import com.jackrain.nea.st.model.table.StCExpressCost;
import com.jackrain.nea.st.model.table.StCExpressCostItem;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 快运报价关系
 *
 * <AUTHOR>
 * @Date 2025/1/15
 * @Description 快运报价设置关系表
 * @Version 1.0
 */
@Data
public class StCExpressCostRelation implements Serializable {

    /**
     * 快运报价主表
     */
    private StCExpressCost stCExpressCost;

    /**
     * 快运报价明细
     */
    private List<StCExpressCostItem> stCExpressCostItemList;
}
