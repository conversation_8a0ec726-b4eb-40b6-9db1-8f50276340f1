package com.jackrain.nea.st.model.enums;

import lombok.Getter;

/**
 * @ClassName : autocheckLogEnum  
 * @Description : 
 * <AUTHOR>  YCH
 * @Date: 2021-09-13 19:47  
 */
public enum AutocheckLogEnum {

    IS_AUTOCHECK_ORDER("IS_AUTOCHECK_ORDER","启用自动审核"),
    IS_MERGE_ORDER("IS_MERGE_ORDER","检查可合并订单"),
    IS_FULL_GIFT_ORDER("IS_FULL_GIFT_ORDER","全赠品订单开启自动审核"),
    WAIT_TIME("WAIT_TIME"," 等待审核时间"),
    ANTI_AUDIT_WAIT_TIME("ANTI_AUDIT_WAIT_TIME"," 反审核等待时间"),
    HOLD_WAIT_TIME("HOLD_WAIT_TIME"," hold单等待时间"),
    IS_AUTOCHECK_PAY("IS_AUTOCHECK_PAY"," 自动审核货到付款"),
    CP_C_LOGISTICS_ID("CP_C_LOGISTICS_ID"," 排除物流公司"),
    IS_MANUAL_ORDER("IS_MANUAL_ORDER"," 手工订单"),
    ORDER_TYPE("ORDER_TYPE"," 订单类型"),
    ORDER_DISCOUNT_DOWN("ORDER_DISCOUNT_DOWN"," 订单折扣范围下限"),
    ORDER_DISCOUNT_UP("ORDER_DISCOUNT_UP"," 订单折扣范围上限"),
    BEGIN_TIME("BEGIN_TIME"," 付款开始时间"),
    END_TIME("END_TIME"," 付款结束时间"),
    LIMIT_PRICE_DOWN("LIMIT_PRICE_DOWN"," 订单金额下限"),
    LIMIT_PRICE_UP("LIMIT_PRICE_UP"," 订单金额上限"),
    RECEIVER_ADDRESS("RECEIVER_ADDRESS"," 收货地址"),
    SELLER_REMARK("SELLER_REMARK"," 卖家备注"),
    BUYER_REMARK("BUYER_REMARK"," 买家备注"),
    SINGLE_SKU_NUM("SINGLE_SKU_NUM","单条码数量上限");
    @Getter
    private String operationName;
    @Getter
    private String operationValue;

    AutocheckLogEnum(String operationValue, String operationName) {
        this.operationValue = operationValue;
        this.operationName = operationName;
    }

    public static String getNameByValue(String value) {
        String operationName = "";
        for (AutocheckLogEnum e : AutocheckLogEnum.values()) {
            if (e.getOperationValue().equals(value)) {
                operationName = e.getOperationName();
                break;
            }
        }
        return operationName;
    }
}
