package com.jackrain.nea.st.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jackrain.nea.es.annotations.Field;
import com.jackrain.nea.es.constans.FieldType;
import com.jackrain.nea.st.model.base.SubBaseModel;
import java.util.Date;
import lombok.Data;

/**
 * <AUTHOR>
 */
@TableName(value = "st_c_detention_policy")
@Data
public class StCDetentionPolicy extends SubBaseModel {
    @JSONField(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    @JSONField(name = "CP_C_SHOP_ID")
    private String cpCShopId;

    @JSONField(name = "POLICY_TIME_TYPE")
    private String policyTimeType;

    @JSONField(name = "NAME")
    private String name;

    @JSONField(name = "START_TIME")
    private Date startTime;

    @JSONField(name = "END_TIME")
    private Date endTime;

    @JSONField(name = "IS_PRE_SALE")
    private String isPreSale;

    @JSONField(name = "IS_AUTO_RELEASE")
    private String isAutoRelease;

    @JSONField(name = "BUSINESS_TYPE")
    private String businessType;

    @JSONField(name = "RELEASE_TYPE")
    private String releaseType;

    @JSONField(name = "REELEASE_TIME")
    private Date reeleaseTime;

    @JSONField(name = "DURATION")
    private Long duration;

    @JSONField(name = "AD_ORG_ID")
    private Long adOrgId;

    @JSONField(name = "ISACTIVE")
    private String isactive;

    @JSONField(name = "AD_CLIENT_ID")
    private Long adClientId;

    @JSONField(name = "OWNERID")
    private Long ownerid;

    @JSONField(name = "RELEASE_TIME_UNIT")
    private String releaseTimeUnit;

    @JSONField(name = "OWNERENAME")
    private String ownerename;

    @JSONField(name = "OWNERNAME")
    private String ownername;

    @JSONField(name = "CREATIONDATE")
    private Date creationdate;

    @JSONField(name = "MODIFIERID")
    private Long modifierid;

    @JSONField(name = "MODIFIERENAME")
    private String modifierename;

    @JSONField(name = "MODIFIERNAME")
    private String modifiername;

    @JSONField(name = "MODIFIEDDATE")
    private Date modifieddate;

    // 方案状态 1:未审核 2:已审核 3:已作废 4:已结案
    @JSONField(name = "ESTATUS")
    private Integer estatus;

}