package com.jackrain.nea.st.model.request;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;

/**
 * controller请求入参
 */
@Data
public class StCLiveCastStrategyQueryRequest implements Serializable {

    // 店铺编码
    @JSONField(name = "cpCShopEcode")
    private String cpCShopEcode;
    // 店铺名称
    @JSONField(name = "cpCShopTitle")
    private String cpCShopTitle;
    // 方案名称
    @JSONField(name = "strategyName")
    private String strategyName;
    // 方案描述
    @JSONField(name = "strategyDesc")
    private String strategyDesc;
    // 状态
    @JSONField(name = "strategyStatus")
    private Integer strategyStatus;
    // 开始时间
    @JSONField(name = "startTime")
    private Data startTime;
    // 结束时间
    @JSONField(name = "endTime")
    private Data endTime;
    // 订单时间
    @JSONField(name = "billTimeType")
    private Integer billTimeType;
    // 直播平台：快手,抖音,蘑菇街,陌陌,淘宝
    @JSONField(name = "livePlatform")
    private Integer livePlatform;
    // 主播ID
    @JSONField(name = "anchorId")
    private String anchorId;
    // 主播昵称
    @JSONField(name = "anchorNickName")
    private String anchorNickName;

}
