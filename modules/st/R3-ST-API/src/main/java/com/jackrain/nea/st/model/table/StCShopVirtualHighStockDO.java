package com.jackrain.nea.st.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.*;
import com.jackrain.nea.es.annotations.Document;
import com.jackrain.nea.es.annotations.Field;
import com.jackrain.nea.es.constans.FieldType;
import com.jackrain.nea.st.model.base.SubBaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @Date: 2020/6/22 4:34 下午
 * @Desc: 商品虚高库存DO类
 */
@TableName(value = "st_c_shop_virtual_high_stock")
@Data
@Document(index = "st_c_shop_virtual_high_stock",type = "st_c_shop_virtual_high_stock")
@EqualsAndHashCode(callSuper=false)
public class StCShopVirtualHighStockDO extends SubBaseModel{
    @JSONField(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "id", type = IdType.INPUT)
    @Field(type = FieldType.Long)
    private Long id;

    @JSONField(name = "PLAN")
    @Field(type = FieldType.Keyword)
    private String plan;

    @JSONField(name = "CP_C_SHOP_ID")
    @Field(type = FieldType.Long)
    private Long cpCShopId;

    @JSONField(name = "CP_C_SHOP_TITLE")
    @Field(type = FieldType.Keyword)
    private String cpCShopTitle;

    @JSONField(name = "CP_C_SHOP_ECODE")
    @Field(type = FieldType.Keyword)
    private String cpCShopEcode;

    @JSONField(name = "VIRTUAL_HIGH_FAILURE_VALUE")
    @Field(type = FieldType.Double)
    private BigDecimal virtualHighFailureValue;

    @JSONField(name = "BEGIN_TIME")
    @Field(type = FieldType.Date)
    private Date beginTime;

    @JSONField(name = "END_TIME")
    @Field(type = FieldType.Date)
    private Date endTime;

    @JSONField(name = "PLAN_DESC")
    @Field(type = FieldType.Keyword)
    private String planDesc;

    @JSONField(name = "REMARK")
    @Field(type = FieldType.Keyword)
    private String remark;

    @JSONField(name = "STATE")
    @Field(type = FieldType.Integer)
    private Integer state;

    @JSONField(name = "CANCEL_ID")
    @Field(type = FieldType.Long)
    private Long cancelId;

    @JSONField(name = "CANCEL_TIME")
    @Field(type = FieldType.Date)
    private Date cancelTime;

    @JSONField(name = "CANCEL_NAME")
    @Field(type = FieldType.Keyword)
    private String cancelName;

    @JSONField(name = "AUDIT_ID")
    @Field(type = FieldType.Long)
    private Long auditId;

    @JSONField(name = "AUDIT_TIME")
    @Field(type = FieldType.Date)
    private Date auditTime;

    @JSONField(name = "AUDIT_NAME")
    @Field(type = FieldType.Keyword)
    private String auditName;

    @JSONField(name = "REVERSE_AUDIT_ID")
    @Field(type = FieldType.Long)
    private Long reverseAuditId;

    @JSONField(name = "REVERSE_AUDIT_TIME")
    @Field(type = FieldType.Date)
    private Date reverseAuditTime;

    @JSONField(name = "REVERSE_AUDIT_NAME")
    @Field(type = FieldType.Keyword)
    private String reverseAuditName;

    @JSONField(name = "CLOSE_ID")
    @Field(type = FieldType.Long)
    private Long closeId;

    @JSONField(name = "CLOSE_TIME")
    @Field(type = FieldType.Date)
    private Date closeTime;

    @JSONField(name = "CLOSE_NAME")
    @Field(type = FieldType.Keyword)
    private String closeName;

    @JSONField(name = "SYNC_START_MARK")
    @Field(type = FieldType.Integer)
    private Integer syncStartMark;

    @JSONField(name = "SYNC_END_MARK")
    @Field(type = FieldType.Integer)
    private Integer syncEndMark;

    @JSONField(name = "SYNC_COMPLETE_MARK")
    @Field(type = FieldType.Integer)
    private Integer syncCompleteMark;
}
