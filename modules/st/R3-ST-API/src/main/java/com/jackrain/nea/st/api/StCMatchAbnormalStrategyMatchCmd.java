package com.jackrain.nea.st.api;

import com.jackrain.nea.st.model.table.StCMatchAbnormalStrategy;
import com.jackrain.nea.sys.domain.ValueHolderV14;

import java.util.List;

/**
 * @program: r3-st
 * @description:
 * @author: caomalai
 * @create: 2022-10-16 17:33
 **/
public interface StCMatchAbnormalStrategyMatchCmd {

    ValueHolderV14<List<StCMatchAbnormalStrategy>> match(Integer abnormalType, String abnormalContent);
}
