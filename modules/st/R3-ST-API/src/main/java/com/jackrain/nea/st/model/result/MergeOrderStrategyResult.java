package com.jackrain.nea.st.model.result;

import com.alibaba.fastjson.annotation.JSONField;
import com.jackrain.nea.st.model.table.StCMergeCategoryLimitItemDO;
import com.jackrain.nea.st.model.table.StCMergeOrderDO;
import com.jackrain.nea.st.model.table.StCVipcomMailDO;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class MergeOrderStrategyResult implements Serializable {

    @JSONField(name = "ST_C_MERGE_ORDER")
    private StCMergeOrderDO mergeOrderDO;

    @JSONField(name = "MERGE_CATEGORY_LIMIT_ITEM")
    private List<StCMergeCategoryLimitItemDO> mergeCategoryLimitItemList;

}
