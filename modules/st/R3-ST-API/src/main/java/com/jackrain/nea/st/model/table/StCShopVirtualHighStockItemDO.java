package com.jackrain.nea.st.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.*;
import com.jackrain.nea.es.annotations.Document;
import com.jackrain.nea.es.annotations.Field;
import com.jackrain.nea.es.constans.FieldType;
import com.jackrain.nea.st.model.base.SubBaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @Date: 2020/6/22 5:10 下午
 * @Desc: 商品虚高库存明细表DO类
 */
@TableName(value = "st_c_shop_virtual_high_stock_item")
@Data
@Document(index = "st_c_shop_virtual_high_stock",type = "st_c_shop_virtual_high_stock_item")
@EqualsAndHashCode(callSuper=false)
public class StCShopVirtualHighStockItemDO extends SubBaseModel {
    @JSONField(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "id", type = IdType.INPUT)
    @Field(type = FieldType.Long)
    private Long id;

    @JSONField(name = "ST_C_SHOP_VIRTUAL_HIGH_STOCK_ID")
    @Field(type = FieldType.Long)
    private Long stCShopVirtualHighStockId;

    /**
     * 条码
     */
    @JSONField(name = "PS_C_SKU_ID")
    @Field(type = FieldType.Long)
    private Long psCSkuId;

    /**
     * 商品编码
     */
    @JSONField(name = "PS_C_PRO_ID")
    @Field(type = FieldType.Long)
    private Long psCProId;

    /**
     * 平台条码
     */
    @JSONField(name = "SKU_ID")
    @Field(type = FieldType.Keyword)
    private String skuId;

    /**
     * 平台商品
     */
    @JSONField(name = "NUMBER_ID")
    @Field(type = FieldType.Keyword)
    private String numberId;

    @JSONField(name = "PS_C_PRO_ENAME")
    @Field(type = FieldType.Keyword)
    private String psCProEname;

    @JSONField(name = "PS_C_SKU_ECODE")
    @Field(type = FieldType.Keyword)
    private String psCSkuEcode;

    @JSONField(name = "PS_C_PRO_ECODE")
    @Field(type = FieldType.Keyword)
    private String psCProEcode;

    @JSONField(name = "EXPIRE_VALUE")
    @Field(type = FieldType.Double)
    private BigDecimal expireValue;

    @JSONField(name = "VIRTUAL_HIGH_VALUE")
    @Field(type = FieldType.Double)
    private BigDecimal virtualHighValue;
    /**
     * 方案名称
     */
    @JSONField(name = "PLAN_NAME")
    @Field(type = FieldType.Keyword)
    private String planName;
    /**
     * 开始时间
     */
    @JSONField(name = "BEGIN_TIME")
    @Field(type = FieldType.Date)
    private Date beginTime;
    /**
     * 结束时间
     */
    @JSONField(name = "END_TIME")
    @Field(type = FieldType.Date)
    private Date endTime;
    /**
     * 主表状态
     */
    @JSONField(name = "STATUS")
    @Field(type = FieldType.Integer)
    private Integer status;
    /**
     * 店铺id
     */
    @JSONField(name = "CP_C_SHOP_ID")
    @Field(type = FieldType.Long)
    private Long cpCShopId;
    /**
     * 店铺名称
     */
    @JSONField(name = "CP_C_SHOP_TITLE")
    @Field(type = FieldType.Keyword)
    private String cpCShopTitle;
    /**
     * 店铺ecode
     */
    @JSONField(name = "CP_C_SHOP_ECODE")
    @Field(type = FieldType.Keyword)
    private String cpCShopEcode;
    /**
     * 主表创建时间
     */
    @JSONField(name = "MAIN_CREATIONDATE")
    @Field(type = FieldType.Date)
    private Date mainCreationdate;
}
