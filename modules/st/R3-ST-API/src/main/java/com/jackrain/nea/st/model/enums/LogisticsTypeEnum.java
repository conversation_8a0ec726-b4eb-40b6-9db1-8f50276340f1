package com.jackrain.nea.st.model.enums;

import lombok.Getter;

import java.util.Objects;

/**
 * <AUTHOR>
 * @Date 2022/6/22 18:44
 */
public enum LogisticsTypeEnum {

    EXPRESS(1, "快递"),
    TRANSPORTATION(2, "快运"),
    BULK_CARGO_TRANSPORTATION(3, "大货运输"),
    SELF_WITHDRAWAL(4, "自提"),
    CARGO_TRANSPORTATION(5, "整车"),
    ;


    @Getter
    private Integer key;
    @Getter
    private String desc;

    LogisticsTypeEnum(Integer key, String desc) {
        this.key = key;
        this.desc = desc;
    }

    public static LogisticsTypeEnum getByKey(Integer key) {
        for (LogisticsTypeEnum current : values()) {
            if (Objects.equals(current.getKey(), key)) {
                return current;
            }
        }
        return null;
    }
}
