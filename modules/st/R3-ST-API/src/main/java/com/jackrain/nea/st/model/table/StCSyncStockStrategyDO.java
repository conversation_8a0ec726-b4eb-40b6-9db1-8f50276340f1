package com.jackrain.nea.st.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.*;
import com.jackrain.nea.st.model.base.SubBaseModel;
import lombok.Data;

import java.math.BigDecimal;

@TableName(value = "ST_C_SYNC_STOCK_STRATEGY")
@Data
public class StCSyncStockStrategyDO extends SubBaseModel {
    @JSONField(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    @JSONField(name = "CP_C_SHOP_ID")
    private Long cpCShopId;

    @J<PERSON><PERSON><PERSON>(name = "CP_C_SHOP_ECODE")
    private String cpCShopEcode;

    @JSONField(name = "CP_C_SHOP_TITLE")
    private String cpCShopTitle;

    @JSONField(name = "CP_C_PLATFORM_ID")
    private Long cpCPlatformId;

    @JSONField(name = "SHOP_PROPERTY")
    private String shopProperty;

    @J<PERSON><PERSON><PERSON>(name = "CHANNEL_TYPE")
    private String channelType;

    @Deprecated
    @JSONField(name = "STOCK_RATE")
    private BigDecimal stockRate;

    @Deprecated
    @JSONField(name = "LOW_STOCK")
    private Long lowStock;

    @JSONField(name = "IS_SYNC_STOCK")
    private Integer isSyncStock;

    @JSONField(name = "OWNERENAME")
    private String ownerename;

    @JSONField(name = "MODIFIERENAME")
    private String modifierename;
}