package com.jackrain.nea.st.model.request;

import com.alibaba.fastjson.annotation.JSONField;
import com.jackrain.nea.st.model.table.StCProLogisticStrategy;
import lombok.Data;

import java.io.Serializable;

/**
 * Created with IntelliJ IDEA.
 * @Author: jg.zhan
 * @Date:  2022/6/21 14:51
 * @Description:  商品物流设置
 */
@Data
public class StCProLogisticStrategyRequest implements Serializable {
    @JSONField(name = "ST_C_PRO_LOGISTIC_STRATEGY")
    private StCProLogisticStrategy stCProLogisticStrategy;

    private Long objId;
}
