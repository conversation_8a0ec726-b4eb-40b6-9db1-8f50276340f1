package com.jackrain.nea.st.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.*;
import com.jackrain.nea.es.annotations.Document;
import com.jackrain.nea.es.annotations.Field;
import com.jackrain.nea.es.constans.FieldType;
import com.jackrain.nea.sys.domain.BaseModel;
import lombok.Data;

/**
 * @ClassName StCPreorderFieldStrategy
 * @Description 订单预导入模板策略
 * <AUTHOR>
 * @Date 2022/12/21 17:12
 * @Version 1.0
 */
@TableName(value = "st_c_preorder_model_strategy")
@Data
@Document(index = "st_c_preorder_model_strategy", type = "st_c_preorder_model_strategy")
public class StCPreorderModelStrategyDO extends BaseModel {

    @JSONField(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "id", type = IdType.INPUT)
    @Field(type = FieldType.Long)
    private Long id;

    /**
     * 编码
     */
    @JSONField(name = "CODE")
    @Field(type = FieldType.Keyword)
    private String code;

    /**
     * 名称
     */
    @JSONField(name = "NAME")
    @Field(type = FieldType.Keyword)
    private String name;

    /**
     * 提交状态
     */
    @JSONField(name = "SUBMIT_STATUS")
    @Field(type = FieldType.Integer)
    private Integer submitStatus;

    /**
     * 店铺编码
     */
    @JSONField(name = "CP_C_SHOP_ECODE")
    @Field(type = FieldType.Keyword)
    private String cpCShopEcode;

    /**
     * 店铺编码
     */
    @JSONField(name = "CP_C_SHOP_Id")
    @Field(type = FieldType.Long)
    private Long cpCShopId;
}
