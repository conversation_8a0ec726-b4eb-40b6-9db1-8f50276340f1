package com.jackrain.nea.st.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jackrain.nea.st.model.base.SubBaseModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * Created with IntelliJ IDEA.
 *
 * @Author: jg.zhan
 * @Date: 2022/6/17 15:02
 * @Description:
 */

/**
 * 店铺物流策略明细表
 */
@ApiModel(value="店铺物流策略明细表")
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "st_c_shop_logistic_strategy_item")
public class StCShopLogisticStrategyItem extends SubBaseModel implements Serializable {
    /**
     * ID
     */
    @JSONField(name = "ID")
    @TableId(value = "id", type = IdType.INPUT)
    @ApiModelProperty(value = "ID")
    private Long id;

    /**
     * 省份
     */
    @JSONField(name = "CP_C_PROVINCE_ID")
    @TableField(value = "cp_c_province_id")
    @ApiModelProperty(value = "省份")
    private Long cpCProvinceId;

    /**
     * 城市
     */
    @JSONField(name = "CP_C_CITY_ID")
    @TableField(value = "cp_c_city_id")
    @ApiModelProperty(value = "城市")
    private Long cpCCityId;

    /**
     * 实体仓档案
     */
    @JSONField(name = "CP_C_PHY_WAREHOUSE_ID")
    @TableField(value = "cp_c_phy_warehouse_id")
    @ApiModelProperty(value = "实体仓档案")
    private Long cpCPhyWarehouseId;

    /**
     * 实体仓档案
     */
    @JSONField(name = "CP_C_PHY_WAREHOUSE_ECODE")
    @TableField(value = "cp_c_phy_warehouse_ecode")
    @ApiModelProperty(value = "实体仓档案")
    private String cpCPhyWarehouseEcode;

    /**
     * 实体仓档案
     */
    @JSONField(name = "CP_C_PHY_WAREHOUSE_ENAME")
    @TableField(value = "cp_c_phy_warehouse_ename")
    @ApiModelProperty(value = "实体仓档案")
    private String cpCPhyWarehouseEname;


    /**
     * 所属店铺物流设置
     */
    @JSONField(name = "ST_C_SHOP_LOGISTIC_STRATEGY_ID")
    @TableField(value = "st_c_shop_logistic_strategy_id")
    @ApiModelProperty(value = "所属店铺物流设置")
    private Long stCShopLogisticStrategyId;

    /**
     * 品项
     */
    @JSONField(name = "PS_C_PRODIM_ID")
    @TableField(value = "ps_c_prodim_id")
    @ApiModelProperty(value="品项")
    private Long psCProdimId;

    /**
     * 商品
     */
    @JSONField(name = "PS_C_PRO_ID")
    @TableField(value = "ps_c_pro_id")
    @ApiModelProperty(value="商品")
    private Long psCProId;

    /**
     * 商品编码
     */
    @JSONField(name = "PS_C_PRO_ECODE")
    @TableField(value = "ps_c_pro_ecode")
    @ApiModelProperty(value="商品编码")
    private String psCProEcode;

    /**
     * 商品名称
     */
    @JSONField(name = "PS_C_PRO_ENAME")
    @TableField(value = "ps_c_pro_ename")
    @ApiModelProperty(value="商品名称")
    private String psCProEname;

    /**
     * 物流公司档案
     */
    @JSONField(name = "CP_C_LOGISTICS_ID")
    @TableField(value = "cp_c_logistics_id")
    @ApiModelProperty(value="物流公司档案")
    private Long cpCLogisticsId;

    /**
     * 物流公司档案
     */
    @JSONField(name = "CP_C_LOGISTICS_ECODE")
    @TableField(value = "cp_c_logistics_ecode")
    @ApiModelProperty(value="物流公司档案")
    private String cpCLogisticsEcode;

    /**
     * 物流公司档案
     */
    @JSONField(name = "CP_C_LOGISTICS_ENAME")
    @TableField(value = "cp_c_logistics_ename")
    @ApiModelProperty(value="物流公司档案")
    private String cpCLogisticsEname;

    /**
     * 物流类型
     */
    @JSONField(name = "LOGISTIC_TYPE")
    @TableField(value = "logistic_type")
    @ApiModelProperty(value = "物流类型")
    private Integer logisticType;

    /**
     * 卖家备注
     */
    @JSONField(name = "SELLER_REMARK")
    @TableField(value = "seller_remark")
    @ApiModelProperty(value = "卖家备注")
    private String sellerRemark;


    private static final long serialVersionUID = 1L;

    public static final String TABLE_NAME = "st_c_shop_logistic_strategy_item";

    public static final String COL_ID = "id";

    public static final String COL_ST_C_SHOP_LOGISTIC_STRATEGY_ID = "st_c_shop_logistic_strategy_id";

    public static final String COL_PS_C_PRODIM_ID = "ps_c_prodim_id";

    public static final String COL_PS_C_PRO_ID = "ps_c_pro_id";

    public static final String COL_PS_C_PRO_ECODE = "ps_c_pro_ecode";

    public static final String COL_PS_C_PRO_ENAME = "ps_c_pro_ename";

    public static final String COL_CP_C_LOGISTICS_ID = "cp_c_logistics_id";

    public static final String COL_LOGISTIC_TYPE = "logistic_type";

    public static final String COL_AD_ORG_ID = "ad_org_id";

    public static final String COL_ISACTIVE = "isactive";

    public static final String COL_AD_CLIENT_ID = "ad_client_id";

    public static final String COL_OWNERID = "ownerid";

    public static final String COL_OWNERENAME = "ownerename";

    public static final String COL_OWNERNAME = "ownername";

    public static final String COL_CREATIONDATE = "creationdate";

    public static final String COL_MODIFIERID = "modifierid";

    public static final String COL_MODIFIERENAME = "modifierename";

    public static final String COL_MODIFIERNAME = "modifiername";

    public static final String COL_MODIFIEDDATE = "modifieddate";
}