package com.jackrain.nea.st.model.table.message;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.*;
import com.jackrain.nea.sys.domain.BaseModel;
import lombok.Data;

import java.util.Date;

@TableName(value = "st_c_message_strategy")
@Data
public class StCMessageStrategy extends BaseModel {
    @JSONField(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    @J<PERSON>NField(name = "REMARK")
    private String remark;

    @J<PERSON><PERSON>ield(name = "VERSION")
    private Long version;

    @JSONField(name = "OWNERENAME")
    private String ownerename;

    @J<PERSON><PERSON>ield(name = "MODIFIERENAME")
    private String modifierename;

    @JSONField(name = "STRATEGY_NAME")
    private String strategyName;

    @JSONField(name = "ORDER_BUSINESS_TYPE")
    private String orderBusinessType;

    @J<PERSON><PERSON>ield(name = "ORDER_BUSINESS_TYPE_CODE")
    private String orderBusinessTypeCode;

    @J<PERSON><PERSON>ield(name = "ORDER_BUSINESS_TYPE_NAME")
    private String orderBusinessTypeName;

    @JSONField(name = "ORDER_STATUS")
    private String orderStatus;

    @JSONField(name = "ORDER_STATUS_CODE")
    private String orderStatusCode;

    @JSONField(name = "WAREHOUSE_DELIVERY_TIME")
    private Date warehouseDeliveryTime;

    @JSONField(name = "LAST_TIME")
    private Date lastTime;

}
