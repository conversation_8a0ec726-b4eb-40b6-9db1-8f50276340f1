package com.jackrain.nea.st.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.*;
import com.jackrain.nea.st.model.base.SubBaseModel;
import lombok.Data;

@TableName(value = "st_c_sell_owngoods_customer")
@Data
public class StCSellOwngoodsCustomerDO extends SubBaseModel {
    @JSO<PERSON>ield(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    @JSONField(name = "ST_C_SELL_OWNGOODS_ID")
    private Long stCSellOwngoodsId;

    @JSO<PERSON>ield(name = "CP_C_CUSTOMER_ID")
    private Long cpCCustomerId;

    @JSONField(name = "CP_C_CUSTOMER_ECODE")
    private String cpCCustomerEcode;

    @JSONField(name = "CP_C_CUSTOMER_ENAME")
    private String cpCCustomerEname;

    @J<PERSON>NField(name = "OWNERENAME")
    private String ownerename;

    @<PERSON><PERSON><PERSON>ield(name = "MODIFIERENAME")
    private String modifierename;
}