package com.jackrain.nea.st.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jackrain.nea.st.model.base.SubBaseModel;
import com.jackrain.nea.sys.domain.BaseModel;
import java.math.BigDecimal;
import lombok.Data;

@TableName(value = "st_c_unfullcar_cost_item")
@Data
public class StCUnfullcarCostItem extends SubBaseModel {
    @JSONField(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    @JSONField(name = "UNFULLCAR_COST_ID")
    private Long unfullcarCostId;

    @JSONField(name = "PROVINCE_ID")
    private Long provinceId;

    @JSONField(name = "CITY_ID")
    private Long cityId;

    @JSONField(name = "ARRIVAL_DAYS")
    private Integer arrivalDays;

    @JSONField(name = "START_WEIGHT")
    private BigDecimal startWeight;

    @JSONField(name = "END_WEIGHT")
    private BigDecimal endWeight;

    @JSONField(name = "TRUNK_FREIGHT")
    private BigDecimal trunkFreight;

    @JSONField(name = "DELIVERY_FEE")
    private BigDecimal deliveryFee;

    @JSONField(name = "FREIGHT")
    private BigDecimal freight;

    @JSONField(name = "PREMIUM")
    private BigDecimal premium;

    @JSONField(name = "UNLOADING_FEE")
    private BigDecimal unloadingFee;

    @JSONField(name = "OTHER_FEE")
    private BigDecimal otherFee;

    @JSONField(name = "BIGINT1")
    private Long bigint1;

    @JSONField(name = "BIGINT2")
    private Long bigint2;

    @JSONField(name = "BIGINT3")
    private Long bigint3;

    @JSONField(name = "DECIMAL1")
    private BigDecimal decimal1;

    @JSONField(name = "DECIMAL2")
    private BigDecimal decimal2;

    @JSONField(name = "DECIMAL3")
    private BigDecimal decimal3;

    @JSONField(name = "DECIMAL4")
    private BigDecimal decimal4;

    @JSONField(name = "DECIMAL5")
    private BigDecimal decimal5;

    @JSONField(name = "DECIMAL6")
    private BigDecimal decimal6;

    @JSONField(name = "VERSION")
    private Long version;

    @JSONField(name = "OWNERENAME")
    private String ownerename;

    @JSONField(name = "MODIFIERENAME")
    private String modifierename;
}