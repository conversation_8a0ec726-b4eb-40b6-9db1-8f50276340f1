package com.jackrain.nea.st.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jackrain.nea.st.model.base.SubBaseModel;

import java.math.BigDecimal;
import lombok.Data;

@TableName(value = "st_added_service_strategy_detail")
@Data
public class StAddedServiceStrategyDetailDO extends SubBaseModel {
    @JSONField(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;


    @JSONField(name = "ADDED_STRATEGY_ID")
    private Long addedStrategyId;

    @J<PERSON>NField(name = "ADDED_TYPE_DOC_ID")
    private Long addedTypeDocId;

    @JSONField(name = "UNIT_PRICE")
    private BigDecimal unitPrice;

}