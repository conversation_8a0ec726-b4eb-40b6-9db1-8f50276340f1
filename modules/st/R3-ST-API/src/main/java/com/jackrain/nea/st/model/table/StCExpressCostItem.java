package com.jackrain.nea.st.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jackrain.nea.st.model.base.SubBaseModel;
import lombok.Data;

import java.math.BigDecimal;

@TableName(value = "st_c_express_cost_item")
@Data
public class StCExpressCostItem extends SubBaseModel {
    @JSONField(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    @JSONField(name = "ST_C_EXPRESS_COST_ID")
    private Long stCExpressCostId;

    @JSONField(name = "PROVINCE_ID")
    private Long provinceId;

    @JSONField(name = "CITY_ID")
    @TableField(strategy = FieldStrategy.IGNORED)
    private Long cityId;

    @JSONField(name = "START_WEIGHT")
    private BigDecimal startWeight;

    @JSONField(name = "END_WEIGHT")
    private BigDecimal endWeight;

    @JSONField(name = "PRICE_EXPRESS")
    @TableField(strategy = FieldStrategy.IGNORED)
    private BigDecimal priceExpress;

    @JSONField(name = "PRICE_FIRST_WEIGHT")
    private BigDecimal priceFirstWeight;

    @JSONField(name = "VERSION")
    private Long version;

    @JSONField(name = "OWNERENAME")
    private String ownerename;

    @JSONField(name = "MODIFIERENAME")
    private String modifierename;
}