package com.jackrain.nea.st.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.*;
import com.jackrain.nea.st.model.base.SubBaseModel;
import lombok.Data;

import java.util.Date;

@TableName(value = "st_c_operation_log")
@Data
public class StCOperationLogDO extends SubBaseModel {
    @JSO<PERSON>ield(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    @JSONField(name = "TABLE_NAME")
    private String tableName;

    @JSONField(name = "OPERATION_TYPE")
    private String operationType;

    @J<PERSON><PERSON>ield(name = "UPDATE_ID")
    private Long updateId;

    @JSONField(name = "UPDATE_MODEL_NAME")
    private String updateModelName;

    @JSONField(name = "MOD_CONTENT")
    private String modContent;

    @J<PERSON><PERSON>ield(name = "BEFORE_DATA")
    private String beforeData;

    @JSONField(name = "AFTER_DATA")
    private String afterData;
}