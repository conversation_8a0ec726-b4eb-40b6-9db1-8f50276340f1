package com.jackrain.nea.st.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.*;
import com.jackrain.nea.st.model.base.SubBaseModel;
import lombok.Data;

@TableName(value = "st_c_vipcom_mail")
@Data
public class StCVipcomMailDO extends SubBaseModel {
    @JSONField(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    @JSONField(name = "ENAME")
    private String ename;

    @J<PERSON><PERSON>ield(name = "SALE_TYPE")
    private Integer saleType;

    @JSONField(name = "IS_MAIL")
    private Integer isMail;

    @JSONField(name = "IS_DING_TALK")
    private Integer isDingTalk;

    @JSONField(name = "ARRIVAL_INTERVAL")
    private Long arrivalInterval;

    @JSONField(name = "RECEIVE_MAIL")
    private String receiveMail;

    @J<PERSON>NField(name = "COPY_MAIL")
    private String copyMail;

    @J<PERSON><PERSON>ield(name = "CP_C_SHOP_ID")
    private Long cpCShopId;

    @JSONField(name = "CP_C_SHOP_TITLE")
    private String cpCShopTitle;

    @JSONField(name = "CP_C_PHY_WAREHOUSE_ID")
//    @TableField(strategy = FieldStrategy.IGNORED)
    private Long cpCPhyWarehouseId;

    @JSONField(name = "CP_C_PHY_WAREHOUSE_ECODE")
    private String cpCPhyWarehouseEcode;

    @JSONField(name = "CP_C_PHY_WAREHOUSE_ENAME")
    private String cpCPhyWarehouseEname;

    @JSONField(name = "MAIL_TITLE")
    private String mailTitle;

    @JSONField(name = "MAIL_CONTENT")
    private String mailContent;

    @JSONField(name = "OWNERENAME")
    private String ownerename;

    @JSONField(name = "MODIFIERENAME")
    private String modifierename;

    @JSONField(name = "TASK_NODE")
    private Integer taskNode;

    @JSONField(name = "DING_TALK_NO")
    private String dingTalkNo;
}