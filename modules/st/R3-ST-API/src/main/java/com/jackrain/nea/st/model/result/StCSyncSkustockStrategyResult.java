package com.jackrain.nea.st.model.result;

import com.alibaba.fastjson.annotation.JSONField;
import com.jackrain.nea.es.annotations.Field;
import com.jackrain.nea.es.constans.FieldType;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * @author: 汪聿森
 * @Date: Created in 2020-05-22 9:10
 * @Description : 店铺条码库存同步策略查询结果集
 */
@Data
public class StCSyncSkustockStrategyResult implements Serializable {

    @JSONField(name = "ID")
    @Field(type = FieldType.Long)
    private Long id;

    @JSONField(name = "CP_C_SHOP_ID")
    @Field(type = FieldType.Long)
    private Long cpCShopId;

    @JSONField(name = "PS_C_SKU_ID")
    @Field(type = FieldType.Long)
    private Long psCSkuId;

    @JSONField(name = "CP_C_STORE_ID")
    @Field(type = FieldType.Long)
    private Long cpCStoreId;

    @JSONField(name = "BEGIN_TIME")
    @Field(type = FieldType.Date)
    private Date beginTime;

    @J<PERSON>NField(name = "END_TIME")
    @Field(type = FieldType.Date)
    private Date endTime;
}
