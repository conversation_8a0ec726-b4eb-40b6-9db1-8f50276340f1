package com.jackrain.nea.st.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jackrain.nea.st.model.base.SubBaseModel;
import com.jackrain.nea.sys.domain.BaseModel;
import lombok.Data;

@TableName(value = "st_c_warehouse_goods_class")
@Data
public class StCWarehouseGoodsClassDO extends SubBaseModel {
    @JSONField(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    @JSONField(name = "ST_C_WAREHOUSE_ID")
    private Long stCWarehouseId;

    @JSONField(name = "PS_C_PRODIM_ID")
    private Long psCProdimId;

    @JSONField(name = "PS_C_PRODIM_ECODE")
    private String psCProdimEcode;

    @JSONField(name = "NUM")
    private Integer num;

    @JSONField(name = "OTHER_NUM")
    private Integer otherNum;

    @JSONField(name = "OWNERENAME")
    private String ownerename;

    @JSONField(name = "MODIFIERENAME")
    private String modifierename;
}