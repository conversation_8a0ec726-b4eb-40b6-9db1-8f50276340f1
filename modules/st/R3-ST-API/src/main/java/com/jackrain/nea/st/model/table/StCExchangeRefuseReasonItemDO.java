package com.jackrain.nea.st.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jackrain.nea.st.model.base.SubBaseModel;
import lombok.Data;

import java.math.BigDecimal;

@TableName(value = "st_c_exchange_refuse_reason_item")
@Data
public class StCExchangeRefuseReasonItemDO extends SubBaseModel {
    @JSONField(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    @JSONField(name = "EXCHANGE_ORDER_STRATEGY_ID")
    private Long exchangeOrderStrategyId;

    @JSONField(name = "REFUSE_ID")
    private String refuseId;

    @JSONField(name = "REFUSE_REASON")
    private String refuseReason;

    @JSONField(name = "ISACTIVE")
    private String isactive;

}