package com.jackrain.nea.st.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.*;
import com.jackrain.nea.st.model.base.SubBaseModel;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 对等换货策略明细
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-06-16 13:26:40
 */
@TableName(value = "st_c_equity_barter_strategy_item")
@Data
public class StCEquityBarterStrategyItem extends SubBaseModel {
    /**
     * ID
     */
    @JSONField(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;
    /**
     * 主表id
     */
    @JSONField(name = "ST_C_EQUITY_BARTER_STRATEGY_ID")
    private Long stCEquityBarterStrategyId;
    /**
     * 商品SKU
     */
    @JSONField(name = "PS_C_SKU_ID")
    private Long psCSkuId;
    /**
     * 换货商品名称
     */
    @JSONField(name = "PS_C_SKU_NAME")
    private String psCSkuName;
    /**
     * 换货数量
     */
    @JSONField(name = "QTY")
    private BigDecimal qty;
    /**
     * 对等商品
     */
    @JSONField(name = "EQUITY_SKU_ID")
    private Long equitySkuId;
    /**
     * 对等商品名称
     */
    @JSONField(name = "EQUITY_SKU_NAME")
    private String equitySkuName;
    /**
     * 对等数量
     */
    @JSONField(name = "EQUITY_QTY")
    private BigDecimal equityQty;
    /**
     * 换货类型:1多还少 2对等置换
     */
    @JSONField(name = "EXCHANGE_TYPE")
    private String exchangeType;
    /**
     * 生效状态:1生效 2不生效
     */
    @JSONField(name = "EFFECTIVE_STATUS")
    private String effectiveStatus;
    /**
     * 备注
     */
    @JSONField(name = "REMARK")
    private String remark;
    /**
     * 版本号
     */
    @JSONField(name = "VERSION")
    private Long version;
    /**
     * 创建人姓名
     */
    @JSONField(name = "OWNERENAME")
    private String ownerename;
    /**
     * 修改人姓名
     */
    @JSONField(name = "MODIFIERENAME")
    private String modifierename;

}
