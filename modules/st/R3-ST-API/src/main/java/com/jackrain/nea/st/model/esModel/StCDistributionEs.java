package com.jackrain.nea.st.model.esModel;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.*;
import com.jackrain.nea.es.annotations.Document;
import com.jackrain.nea.es.annotations.Field;
import com.jackrain.nea.es.constans.FieldType;
import com.jackrain.nea.sys.domain.BaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@TableName(value = "st_c_distribution")
@Data
@Document(index = "st_c_distribution",type = "st_c_distribution")
@EqualsAndHashCode(callSuper=false)
public class StCDistributionEs extends BaseModel {
    @JSONField(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "id", type = IdType.INPUT)
    @Field(type = FieldType.Long)
    private Long id;

    @JSONField(name = "BILL_NO")
    @Field(type = FieldType.Keyword)
    private String billNo;

    @JSONField(name = "ENAME")
    @Field(type = FieldType.Keyword)
    private String ename;

    @JSONField(name = "BILL_STATUS")
    @Field(type = FieldType.Integer)
    private Integer billStatus;

    @JSONField(name = "BEGIN_TIME")
    @Field(type = FieldType.Date)
    private Date beginTime;

    @JSONField(name = "END_TIME")
    @Field(type = FieldType.Date)
    private Date endTime;

    @JSONField(name = "CP_C_CUSTOMER_ECODE")
    @Field(type = FieldType.Keyword)
    private String cpCCustomerEcode;

    @JSONField(name = "CP_C_CUSTOMER_ENAME")
    @Field(type = FieldType.Keyword)
    private String cpCCustomerEname;

    @JSONField(name = "SETTLEMENTTYPE")
    @Field(type = FieldType.Integer)
    private Integer settlementtype;

    @JSONField(name = "FEESCALE")
    @Field(type = FieldType.Double)
    private BigDecimal feescale;

    @JSONField(name = "PLAN_DESC")
    @Field(type = FieldType.Keyword)
    private String planDesc;

    @JSONField(name = "OWNERENAME")
    @Field(type = FieldType.Keyword)
    private String ownerename;

    @JSONField(name = "FINISHID")
    @Field(type = FieldType.Long)
    private Long finishid;

    @JSONField(name = "FINISHENAME")
    @Field(type = FieldType.Keyword)
    private String finishename;

    @JSONField(name = "FINISHNAME")
    @Field(type = FieldType.Keyword)
    private String finishname;

    @JSONField(name = "FINISHTIME")
    @Field(type = FieldType.Date)
    private Date finishtime;

    @JSONField(name = "MODIFIERENAME")
    @Field(type = FieldType.Keyword)
    private String modifierename;

    @JSONField(name = "DELID")
    @Field(type = FieldType.Long)
    private Long delid;

    @JSONField(name = "DELENAME")
    @Field(type = FieldType.Keyword)
    private String delename;

    @JSONField(name = "DELNAME")
    @Field(type = FieldType.Keyword)
    private String delname;

    @JSONField(name = "DEL_TIME")
    @Field(type = FieldType.Date)
    private Date delTime;

    @JSONField(name = "CHECKID")
    @Field(type = FieldType.Long)
    private Long checkid;

    @JSONField(name = "CHECKENAME")
    @Field(type = FieldType.Keyword)
    private String checkename;

    @JSONField(name = "CHECKNAME")
    @Field(type = FieldType.Keyword)
    private String checkname;

    @JSONField(name = "CHECKTIME")
    @Field(type = FieldType.Date)
    private Date checktime;

    @JSONField(name = "CP_C_CUSTOMER_ID_SET")
    @Field(type = FieldType.Object)
    private List<Long> cpCCustomerIdSet;
}