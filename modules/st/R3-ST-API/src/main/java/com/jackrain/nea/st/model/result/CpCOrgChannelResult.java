package com.jackrain.nea.st.model.result;

import com.alibaba.fastjson.annotation.JSONField;
import com.jackrain.nea.cp.entity.CpCOrgChannelItemEntity;
import lombok.Data;

import java.io.Serializable;

/**
 * @author: ganquan
 * @Date: Created in 2020-05-21 10:06
 * @Description : 渠道仓子表+区域属性
 */
@Data
public class CpCOrgChannelResult extends CpCOrgChannelItemEntity implements Serializable {
    @JSONField(name = "AREATYPE")
    private Long areatype;
}
