package com.jackrain.nea.st.model.enums;

import lombok.Getter;

/**
 * @ClassName MatchAbnormalHandleTypeEnum
 * @Description 异常
 * <AUTHOR>
 * @Date 2022/12/29 16:43
 * @Version 1.0
 */
public enum MatchAbnormalHandleTypeEnum {

    ONLY_TAG(1, "仅打标"),
    TAG_DETENTION(2, "打标并卡单"),
    TAG_HOLD(3, "打标并Hold单");

    @Getter
    private Integer value;

    @Getter
    private String desc;

    MatchAbnormalHandleTypeEnum(Integer value, String desc) {
        this.value = value;
        this.desc = desc;
    }
}
