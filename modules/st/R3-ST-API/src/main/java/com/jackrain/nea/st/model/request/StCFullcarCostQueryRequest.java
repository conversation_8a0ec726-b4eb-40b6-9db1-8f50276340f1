package com.jackrain.nea.st.model.request;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * @ClassName StCFullcarCostQueryRequest
 * @Description 整车报价
 * <AUTHOR>
 * @Date 2024/4/8 14:47
 * @Version 1.0
 */
@Data
public class StCFullcarCostQueryRequest implements Serializable {

    private static final long serialVersionUID = -3878720976534810642L;

    /**
     * 仓库id
     */
    private List<Long> warehouseIdList;

    /**
     * 承运商id
     */
    private List<Long> logisticsIdList;

    /**
     * 总重量
     */
    private BigDecimal totalWeight;

    /**
     * 省份id
     */
    private Long regionProvinceId;

    /**
     * 城市id
     */
    private Long regionCityId;
}
