package com.jackrain.nea.st.model.vo;

import lombok.Data;

import java.util.Map;

/**
 * @program: r3-st
 * @description: 仓库物流设置导入实体
 * @author: caomalai
 * @create: 2022-07-01 15:19
 **/
@Data
public class StCShopLogisticStrategyImpVo {
    public static final String cellStr = "cell_";
    public static final String rowStr = "row_";

    private String cpCShopName;
    private String cpCLogisticsName;
    private String psCProdimNanme;
    private String psCProCode;
    private String provinceName;
    private String cityName;
    private String warehouseName;
    private String sellerRemark;
    //行号
    private int rowNum;

    private String desc;

    public static StCShopLogisticStrategyImpVo importCreate(int index, StCShopLogisticStrategyImpVo impVo, Map<String, String> columnMap) {
        try {
            //仓库名称
            impVo.setCpCShopName(columnMap.get(rowStr + index + cellStr + 0));
            //物流公司
            impVo.setCpCLogisticsName(columnMap.get(rowStr + index + cellStr + 1));
            //四级分类
            impVo.setPsCProdimNanme(columnMap.get(rowStr + index + cellStr + 2));
            //商品
            impVo.setPsCProCode(columnMap.get(rowStr + index + cellStr + 3));
            //省份名称
            impVo.setProvinceName(columnMap.get(rowStr + index + cellStr + 4));
            //城市名称
            impVo.setCityName(columnMap.get(rowStr + index + cellStr + 5));
            //仓库
            impVo.setWarehouseName(columnMap.get(rowStr + index + cellStr + 6));
            //卖家备注
            impVo.setSellerRemark(columnMap.get(rowStr + index + cellStr + 7));
        } catch (Exception e) {

        }
        impVo.setRowNum(index + 1);
        return impVo;
    }
}
