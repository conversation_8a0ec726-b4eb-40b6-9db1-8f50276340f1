package com.jackrain.nea.st.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.*;
import com.jackrain.nea.es.annotations.Document;
import com.jackrain.nea.es.annotations.Field;
import com.jackrain.nea.es.constans.FieldType;
import com.jackrain.nea.st.model.base.SubBaseModel;
import lombok.Data;

import java.util.Date;

@TableName(value = "ST_C_VIPCOM_ASCRIPTION")
@Data
@Document(index = "st_c_vipcom_ascription",type = "st_c_vipcom_ascription")
public class StCVipcomAscriptionDO extends SubBaseModel {
    @JSONField(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "id", type = IdType.INPUT)
    @Field(type = FieldType.Long)
    private Long id;

    @JSONField(name = "ECODE")
    @Field(type = FieldType.Keyword)
    private String ecode;

    @JSONField(name = "ENAME")
    @Field(type = FieldType.Keyword)
    private String ename;

    @JSONField(name = "EXP_SENDTIME")
    @Field(type = FieldType.Date)
    private String expSendtime;

    @JSONField(name = "EXP_ARRIVETIME")
    @Field(type = FieldType.Date)
    private String expArrivetime;

    @JSONField(name = "ARRIVAL_INTERVAL")
    @Field(type = FieldType.Long)
    private Long arrivalInterval;

    @JSONField(name = "BILL_TYPE")
    @Field(type = FieldType.Integer)
    private Integer billType;

    @JSONField(name = "BOX_CODE")
    @Field(type = FieldType.Long)
    private Long boxCode;

    @JSONField(name = "OWNERENAME")
    @Field(type = FieldType.Keyword)
    private String ownerename;

    @JSONField(name = "MODIFIERENAME")
    @Field(type = FieldType.Keyword)
    private String modifierename;

    @JSONField(name = "DELID")
    @Field(type = FieldType.Long)
    private Long delid;

    @JSONField(name = "DELENAME")
    @Field(type = FieldType.Keyword)
    private String delename;

    @JSONField(name = "DELNAME")
    @Field(type = FieldType.Keyword)
    private String delname;

    @JSONField(name = "DEL_TIME")
    @Field(type = FieldType.Date)
    private Date delTime;

    @JSONField(name = "SEND_INTERVAL")
    @Field(type = FieldType.Long)
    private Long sendInterval;
}