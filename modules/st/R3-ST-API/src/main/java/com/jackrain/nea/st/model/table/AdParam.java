package com.jackrain.nea.st.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.*;
import com.jackrain.nea.st.model.base.SubBaseModel;
import lombok.Data;

/**
 * @author: 汪聿森
 * @Date: Created in 2019-05-21 20:22
 * @Description : ${description}
 */
@TableName(value = "AD_PARAM")
@Data
public class AdParam extends SubBaseModel {
    @JSONField(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    @J<PERSON>NField(name = "NAME")
    private String name;

    @J<PERSON><PERSON>ield(name = "DEFAULTVALUE")
    private String defaultvalue;

    @J<PERSON>NField(name = "VALUE")
    private String value;

    @J<PERSON><PERSON>ield(name = "VALUETYPE")
    private String valuetype;

    @JSONField(name = "PARMTYPE")
    private String parmtype;

    @J<PERSON>NField(name = "VALUELIST")
    private String valuelist;

    @J<PERSON>NField(name = "DESCRIP<PERSON><PERSON>")
    private String description;

    @J<PERSON><PERSON>ield(name = "M<PERSON><PERSON><PERSON><PERSON>NA<PERSON>")
    private String modifierename;

    @JSONField(name = "OWNERENAME")
    private String ownerename;
}
