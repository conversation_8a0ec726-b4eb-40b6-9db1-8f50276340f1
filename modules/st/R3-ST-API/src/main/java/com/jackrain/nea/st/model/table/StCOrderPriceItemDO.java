package com.jackrain.nea.st.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jackrain.nea.st.model.base.SubBaseModel;
import com.jackrain.nea.sys.domain.BaseModel;
import java.math.BigDecimal;
import lombok.Data;

@TableName(value = "ST_C_ORDER_PRICE_ITEM")
@Data
public class StCOrderPriceItemDO extends SubBaseModel {
    @JSONField(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    @JSONField(name = "ST_C_PRICE_ID")
    private Long stCPriceId;

    @JSONField(name = "POLICY_TYPE")
    private String policyType;

    @JSONField(name = "QTY_DISCOUNT")
    private BigDecimal qtyDiscount;

    @JSONField(name = "OWNERENAME")
    private String ownerename;

    @JSONField(name = "MODIFIERENAME")
    private String modifierename;
}