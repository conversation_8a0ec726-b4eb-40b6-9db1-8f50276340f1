package com.jackrain.nea.st.model.request;

import com.alibaba.fastjson.annotation.JSONField;
import com.jackrain.nea.st.model.table.*;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 仓库拆单策略
 *
 * <AUTHOR>
 * @Date 2020/06/10
 */
@Data
public class StCWarehouseRequest implements Serializable {

    @J<PERSON><PERSON><PERSON>(name = "ST_C_WAREHOUSE")
    private StCWarehouseDO StCWarehouse;

    @JSONField(name = "ST_C_WAREHOUSE_BRAND")
    private List<StCWarehouseBrandDO> stCWarehouseBrandList;

    @JSONField(name = "ST_C_WAREHOUSE_GOODS")
    private List<StCWarehouseGoodsDO> stCWarehouseGoodsList;

    @JSONField(name = "ST_C_WAREHOUSE_SKU")
    private List<StCWarehouseSkuDO> stCWarehouseSkuList;

    @JSONField(name = "ST_C_WAREHOUSE_GOODS_CLASS")
    private List<StCWarehouseGoodsClassDO> stCWarehouseGoodsClassList;

    private Long objId;
}
