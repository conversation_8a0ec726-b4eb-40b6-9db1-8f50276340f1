package com.jackrain.nea.st.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.*;
import com.jackrain.nea.st.model.base.SubBaseModel;
import lombok.Data;

import java.math.BigDecimal;

@TableName(value = "CP_C_PHY_WAREHOUSE")
@Data
public class CpCPhyWarehouseDO extends SubBaseModel {
    @JSONField(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    @JSONField(name = "ECODE")
    private String ecode;

    @JSONField(name = "ENAME")
    private String ename;

    @J<PERSON><PERSON><PERSON>(name = "WMS_WAREHOUSE_CODE")
    private String wmsWarehouseCode;

    @JSONField(name = "OWNER_CODE")
    private String ownerCode;

    @JSONField(name = "WMS_ACCOUNT")
    private String wmsAccount;

    @JSONField(name = "WMS_URL")
    private String wmsUrl;

    @JSONField(name = "REMARK")
    private String remark;

    @JSONField(name = "CONTACT_NAME")
    private String contactName;

    @JSONField(name = "MOBILEPHONE_NUM")
    private String mobilephoneNum;

    @JSONField(name = "PHONE_NUM")
    private String phoneNum;

    @JSONField(name = "SELLER_PROVINCE_ID")
    private Long sellerProvinceId;

    @JSONField(name = "SELLER_CITY_ID")
    private Long sellerCityId;

    @JSONField(name = "SELLER_AREA_ID")
    private Long sellerAreaId;

    @JSONField(name = "SELLER_ZIP")
    private String sellerZip;

    @JSONField(name = "SEND_ADDRESS")
    private String sendAddress;

    @JSONField(name = "CP_C_CUSTOMER_ID")
    private Long cpCCustomerId;

    @JSONField(name = "CP_C_CUSTOMER_ECODE")
    private String cpCCustomerEcode;

    @JSONField(name = "CP_C_CUSTOMER_ENAME")
    private String cpCCustomerEname;

    @JSONField(name = "CP_C_STORE_ID")
    private Long cpCStoreId;

    @JSONField(name = "CP_C_STORE_ECODE")
    private String cpCStoreEcode;

    @JSONField(name = "CP_C_STORE_ENAME")
    private String cpCStoreEname;

    @JSONField(name = "QTY_PKG_MAX")
    private BigDecimal qtyPkgMax;

    @JSONField(name = "WMS_CONTROL_WAREHOUSE")
    private String wmsControlWarehouse;

    @JSONField(name = "OWNERENAME")
    private String ownerename;

    @JSONField(name = "MODIFIERENAME")
    private String modifierename;
}