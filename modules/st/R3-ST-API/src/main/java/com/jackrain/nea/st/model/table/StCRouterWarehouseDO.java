package com.jackrain.nea.st.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jackrain.nea.es.annotations.Document;
import com.jackrain.nea.es.annotations.Field;
import com.jackrain.nea.es.constans.FieldType;
import com.jackrain.nea.st.model.base.SubBaseModel;
import java.util.Date;
import lombok.Data;

@TableName(value = "st_c_router_warehouse")
@Data
@Document(index = "st_c_router_warehouse",type = "st_c_router_warehouse")
public class StCRouterWarehouseDO extends SubBaseModel {
    @JSONField(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "id", type = IdType.INPUT)
    @Field(type = FieldType.Long)
    private Long id;

    @JSONField(name = "ENAME")
    @Field(type = FieldType.Keyword)
    private String ename;

    @JSONField(name = "CP_C_PHY_WAREHOUSE_ID")
    @Field(type = FieldType.Long)
    private Long cpCPhyWarehouseId;

    @JSONField(name = "CP_C_LOGISTICS_ID")
    @Field(type = FieldType.Long)
    private Long cpCLogisticsId;

    @JSONField(name = "SYS_TYPE")
    @Field(type = FieldType.Keyword)
    private String sysType;

    @JSONField(name = "IS_ALL_LOGISTICS")
    @Field(type = FieldType.Integer)
    private Integer isAllLogistics;

    @JSONField(name = "CUSTOMER_CODE")
    @Field(type = FieldType.Keyword)
    private String customerCode;

    @JSONField(name = "CHECK_CODE")
    @Field(type = FieldType.Keyword)
    private String checkCode;

    @JSONField(name = "CARD_NUMBER")
    @Field(type = FieldType.Keyword)
    private String cardNumber;

    @JSONField(name = "AD_ORG_ID")
    @Field(type = FieldType.Long)
    private Long adOrgId;

    @JSONField(name = "ISACTIVE")
    @Field(type = FieldType.Keyword)
    private String isactive;

    @JSONField(name = "AD_CLIENT_ID")
    @Field(type = FieldType.Long)
    private Long adClientId;

    @JSONField(name = "OWNERID")
    @Field(type = FieldType.Long)
    private Long ownerid;

    @JSONField(name = "OWNERENAME")
    @Field(type = FieldType.Keyword)
    private String ownerename;

    @JSONField(name = "OWNERNAME")
    @Field(type = FieldType.Keyword)
    private String ownername;

    @JSONField(name = "CREATIONDATE")
    @Field(type = FieldType.Date)
    private Date creationdate;

    @JSONField(name = "MODIFIERID")
    @Field(type = FieldType.Long)
    private Long modifierid;

    @JSONField(name = "MODIFIERENAME")
    @Field(type = FieldType.Keyword)
    private String modifierename;

    @JSONField(name = "MODIFIERNAME")
    @Field(type = FieldType.Keyword)
    private String modifiername;

    @JSONField(name = "MODIFIEDDATE")
    @Field(type = FieldType.Date)
    private Date modifieddate;

    @JSONField(name = "DELID")
    @Field(type = FieldType.Long)
    private Long delid;

    @JSONField(name = "DELENAME")
    @Field(type = FieldType.Keyword)
    private String delename;

    @JSONField(name = "DELNAME")
    @Field(type = FieldType.Keyword)
    private String delname;

    @JSONField(name = "DEL_TIME")
    @Field(type = FieldType.Date)
    private Date delTime;
}