package com.jackrain.nea.st.model.result;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;

@Data
public class ExpressAreaItemResult implements Serializable {
    @J<PERSON>NField(name = "ID")
    private Long id;

    @J<PERSON><PERSON><PERSON>(name = "ST_C_EXPRESS_AREA_ID")
    private Long stCExpressAreaId;

    @JSONField(name = "CP_C_REGION_PROVINCE_ID")
    private Long cpCRegionProvinceId;

    @JSONField(name = "CP_C_REGION_PROVINCE_ECODE")
    private String cpCRegionProvinceEcode;

    @J<PERSON><PERSON>ield(name = "CP_C_REGION_PROVINCE_ENAME")
    private String cpCRegionProvinceEname;

    @JSONField(name = "CP_C_REGION_CITY_ID")
    private Long cpCRegionCityId;

    @JSONField(name = "CP_C_REGION_CITY_ECODE")
    private String cpCRegionCityEcode;

    @JSONField(name = "CP_C_REGION_CITY_ENAME")
    private String cpCRegionCityEname;

    @JSO<PERSON>ield(name = "CP_C_REGION_AREA_ID")
    private Long cpCRegionAreaId;

    @JSONField(name = "CP_C_REGION_AREA_ECODE")
    private String cpCRegionAreaEcode;

    @JSONField(name = "CP_C_REGION_AREA_ENAME")
    private String cpCRegionAreaEname;

    @JSONField(name = "IS_ARRIVE")
    private String isArrive;

    @JSONField(name = "EXCLUSION_AREA")
    private String exclusionArea;
}