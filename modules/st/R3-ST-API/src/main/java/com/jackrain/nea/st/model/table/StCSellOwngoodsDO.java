package com.jackrain.nea.st.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.*;
import com.jackrain.nea.st.model.base.SubBaseModel;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @Description 经销商自有商品主表实体类
 * @Date 2019/3/7
 **/
@TableName(value = "st_c_sell_owngoods")
@Data
public class StCSellOwngoodsDO extends SubBaseModel {
    @JSONField(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    @JSONField(name = "BILL_NO")
    private String billNo;

    @JSONField(name = "ECODE")
    private String ecode;

    @JSONField(name = "ENAME")
    private String ename;

    @JSONField(name = "BILL_STATUS")
    private Integer billStatus;

    @JSONField(name = "BEGIN_TIME")
    private Date beginTime;

    @JSONField(name = "END_TIME")
    private Date endTime;

    @JSONField(name = "CP_C_CUSTOMER_ECODE")
    private String cpCCustomerEcode;

    @JSONField(name = "CP_C_CUSTOMER_ENAME")
    private String cpCCustomerEname;

    @JSONField(name = "PLAN_DESC")
    private String planDesc;

    @JSONField(name = "OWNERENAME")
    private String ownerename;

    @JSONField(name = "FINISHID")
    private Long finishid;

    @JSONField(name = "FINISHENAME")
    private String finishename;

    @JSONField(name = "FINISHNAME")
    private String finishname;

    @JSONField(name = "FINISHTIME")
    private Date finishtime;

    @JSONField(name = "MODIFIERENAME")
    private String modifierename;

    @JSONField(name = "DELID")
    private Long delid;

    @JSONField(name = "DELENAME")
    private String delename;

    @JSONField(name = "DELNAME")
    private String delname;

    @JSONField(name = "DEL_TIME")
    private Date delTime;

    @JSONField(name = "CHECKID")
    private Long checkid;

    @JSONField(name = "CHECKENAME")
    private String checkename;

    @JSONField(name = "CHECKNAME")
    private String checkname;

    @JSONField(name = "CHECKTIME")
    private Date checktime;

    @JSONField(name = "CP_C_CUSTOMER_ID")
    private String cpCCustomerId;

    @JSONField(name = "CP_C_SHOP_ID")
    private Long cpCShopId;

    @JSONField(name = "CP_C_SHOP_ECODE")
    private String cpCShopEcode;

    @JSONField(name = "CP_C_SHOP_TITLE")
    private String cpCShopTitle;
}