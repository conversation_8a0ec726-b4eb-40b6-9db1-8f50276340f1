package com.jackrain.nea.st.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.*;
import com.jackrain.nea.st.model.base.SubBaseModel;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 需求变更，即将要废弃删除的表（转移到组织中心的渠道仓明细维护）
 * <AUTHOR>
 * @Date 2019/12/4 11:50
 */
@TableName(value = "ST_C_CHANNEL_STRATEGY_ITEM")
@Data
@Deprecated
public class StCChannelStrategyItemDO extends SubBaseModel {
    @JSONField(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    @JSONField(name = "ST_C_CHANNEL_STRATEGY_ID")
    private Long stCChannelStrategyId;

    @JSONField(name = "ST_C_SYNC_STOCK_STRATEGY_ID")
    @TableField(exist = false)
    private Long stCSyncStockStrategyId;

    @JSONField(name = "CP_C_STORE_ECODE")
    private String cpCStoreEcode;

    @JSONField(name = "CP_C_STORE_ENAME")
    private String cpCStoreEname;

    @JSONField(name = "CP_C_STORE_ID")
    private Long cpCStoreId;

    @JSONField(name = "PRIORITY")
    private Integer priority;

    @JSONField(name = "RATE")
    private BigDecimal rate;

    @JSONField(name = "LOW_STOCK")
    private BigDecimal lowStock;

    @JSONField(name = "IS_SEND")
    private Integer isSend;

    @JSONField(name = "DELID")
    @TableField(exist = false)
    private Long delid;

    @JSONField(name = "DELENAME")
    @TableField(exist = false)
    private String delename;

    @JSONField(name = "DELNAME")
    @TableField(exist = false)
    private String delname;

    @JSONField(name = "DEL_TIME")
    @TableField(exist = false)
    private Date delTime;
}
