package com.jackrain.nea.st.model.request;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * Descroption 物流区域设置
 *
 * <AUTHOR>
 * @Date 2019/3/13 14:23
 */
@Data
public class StCShopLogisticStrategyQueryRequest implements Serializable {

    /**
     * 店铺id
     */
    private Long cpCShopId;
    /**
     * 品项
     */
    private List<Long> psCProdimIdList;

    /**
     * 商品
     */
    private List<Long> psCProIdList;
}
