package com.jackrain.nea.st.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.*;
import com.jackrain.nea.st.model.base.SubBaseModel;
import lombok.Data;

import java.util.Date;

@TableName(value = "st_c_send_rule")
@Data
public class StCSendRuleDO extends SubBaseModel {
    @J<PERSON><PERSON>ield(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    @J<PERSON>NField(name = "ECODE")
    private String ecode;

    @J<PERSON><PERSON>ield(name = "ENAME")
    private String ename;

    @J<PERSON><PERSON>ield(name = "ETYPE")
    private String etype;

    @J<PERSON><PERSON>ield(name = "REMARK")
    private String remark;

    @JSONField(name = "OWNERENAME")
    private String ownerename;

    @J<PERSON>NField(name = "MODIFIERENAME")
    private String modifierename;

    @J<PERSON>NField(name = "DELID")
    private Long delid;

    @J<PERSON><PERSON>ield(name = "DELENAME")
    private String delename;

    @<PERSON><PERSON><PERSON><PERSON>(name = "DELNAME")
    private String delname;

    @J<PERSON><PERSON>ield(name = "DEL_TIME")
    private Date delTime;
}