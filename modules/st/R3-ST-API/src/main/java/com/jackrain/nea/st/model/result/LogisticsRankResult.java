package com.jackrain.nea.st.model.result;

import lombok.Data;

import java.io.Serializable;

/**
 * @Author: huang.zaizai
 * @Date: 2019-08-13
 * @Version 1.0
 */
@Data
public class LogisticsRankResult implements Serializable {

    private Long logisticsId;

    private String logisticsEcode;

    private String logisticsEname;

    private String logisticsIdField;

    private String logisticsEcodeField;

    private String logisticsEnameField;

    private String rank;

    private String rankField;

    private String provDiffRank;
}
