package com.jackrain.nea.st.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.*;
import com.jackrain.nea.st.model.base.SubBaseModel;
import lombok.Data;

import java.math.BigDecimal;

@TableName(value = "st_c_sync_stock_strategy_channel")
@Data
public class StCSyncStockStrategyChannelDO extends SubBaseModel {
    @JSONField(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;
    /**
     * 渠道仓id
     */
    @JSONField(name = "CP_C_ORG_CHANNEL_ID")
    private Long cpCOrgChannelId;
    /**
     * 主表id
     */
    @JSONField(name = "ST_C_SYNC_STOCK_STRATEGY_ID")
    private Long stCSyncStockStrategyId;
    /**
     * 渠道仓名称
     */
    @JSONField(name = "CP_C_ORG_CHANNEL_ENAME")
    private String cpCOrgChannelEname;
    /**
     * 渠道仓编码
     */
    @JSONField(name = "CP_C_ORG_CHANNEL__ECODE")
    private String cpCOrgChannelEcode;
    /**
     * 比例
     */
    @JSONField(name = "STOCK_RATE")
    private BigDecimal stockRate;
    /**
     * 熔断值
     */
    @JSONField(name = "LOW_STOCK")
    private BigDecimal lowStock;

    @JSONField(name = "REMARK")
    private String remark;

    @JSONField(name = "VERSION")
    private Long version;

    @JSONField(name = "OWNERENAME")
    private String ownerename;

    @JSONField(name = "MODIFIERENAME")
    private String modifierename;

    @JSONField(name = "BEFORE_UPDATE_STOCK_RATE")
    private BigDecimal beforeUpdateStockRate;
    /**
     * 主表店铺id
     */
    @JSONField(name = "CP_C_SHOP_ID")
    private Long cpCShopId;
    /**
     * 主表店铺编码
     */
    @JSONField(name = "CP_C_SHOP_ECODE")
    private String cpCShopEcode;
    /**
     * 主表店铺名称
     */
    @JSONField(name = "CP_C_SHOP_TITLE")
    private String cpCShopTitle;
    /**
     * 渠道仓优先级
     */
    @JSONField(name = "CHANNEL_PRIORITY")
    private Integer channelPriority;
}