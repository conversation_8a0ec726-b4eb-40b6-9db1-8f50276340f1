package com.jackrain.nea.st.model.request;

import com.alibaba.fastjson.annotation.JSONField;
import com.jackrain.nea.st.model.base.SubBaseModel;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @Description 店铺商品特殊设置明细
 * @Date 2019/3/12
 **/
@Data
public class ProductStrategyItemRequest extends SubBaseModel {
    /**
     * 主键
     */
    @JSONField(name = "ID")
    private Long id;
    /**
     * 店铺商品特殊设置主表id
     */
    @JSONField(name = "ST_C_PRODUCT_STRATEGY_ID")
    private Long stCProductStrategyId;
    /**
     * 店铺id
     */
    private Long cpCShopId;
    /**
     * 店铺id 多值逗号隔开
     */
    @JSONField(name = "CP_C_SHOP_ID")
    private String cpCShopIds;
    /**
     * 店铺编码
     */
    @JSONField(name = "CP_C_SHOP_ECODE")
    private String cpCShopEcode;
    /**
     * 店铺名称
     */
    @JSONField(name = "CP_C_SHOP_TITLE")
    private String cpCShopTitle;
    /**
     * 平台条码id
     */
    @JSONField(name = "PT_SKU_ID")
    private String ptSkuId;
    /**
     * 平台商品id
     */
    @JSONField(name = "PT_PRO_ID")
    private String ptProId;
    /**
     * 条码id
     */
    @JSONField(name = "PS_C_SKU_ID")
    private Long psCSkuId;
    /**
     * 条码编码
     */
    @JSONField(name = "PS_C_SKU_ECODE")
    private String psCSkuEcode;
    /**
     * 商品id
     */
    @JSONField(name = "PS_C_PRO_ID")
    private Long psCProId;
    /**
     * 商品编码
     */
    @JSONField(name = "PS_C_PRO_ECODE")
    private String psCProEcode;
    /**
     * 商品名称
     */
    @JSONField(name = "PS_C_PRO_ENAME")
    private String psCProEname;
    /**
     * 库存比例
     */
    @JSONField(name = "STOCK_SCALE")
    private BigDecimal stockScale;
    /**
     * 低库存数
     */
    @JSONField(name = "LOW_STOCK")
    private Long lowStock;

    @JSONField(name = "OWNERENAME")
    private String ownerename;

    @JSONField(name = "MODIFIERENAME")
    private String modifierename;
    /**
     * 渠道仓id
     */
    @JSONField(name = "CP_C_ORG_CHANNEL_ID")
    private Long cpCOrgChannelId;
    /**
     * 渠道仓编码
     */
    @JSONField(name = "CP_C_ORG_CHANNEL_ECODE")
    private String cpCOrgChannelEcode;
    /**
     * 渠道仓名称
     */
    @JSONField(name = "CP_C_ORG_CHANNEL_ENAME")
    private String cpCOrgChannelEname;

    /**
     * 当前时间
     */
    @JSONField(name = "EXPIRE_TIME")
    private Date expireTime;
}