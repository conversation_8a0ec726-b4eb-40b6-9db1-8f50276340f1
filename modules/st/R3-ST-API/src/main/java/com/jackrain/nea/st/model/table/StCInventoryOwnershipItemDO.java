package com.jackrain.nea.st.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.*;
import com.jackrain.nea.st.model.base.SubBaseModel;
import lombok.Data;

@TableName(value = "st_c_inventory_ownership_item")
@Data
public class StCInventoryOwnershipItemDO extends SubBaseModel {
    @JSONField(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    @JSONField(name = "ST_C_INVENTORY_OWNERSHIP_ID")
    private Long stCInventoryOwnershipId;

    @J<PERSON><PERSON>ield(name = "CP_C_SHOP_ID")
    private Long cpCShopId;

    @JSONField(name = "CP_C_SHOP_ECODE")
    private String cpCShopEcode;

    @JSONField(name = "CP_C_SHOP_TITLE")
    private String cpCShopTitle;

    @JSONField(name = "MATERIAL_TYPE")
    private Long materialType;

    @J<PERSON>NField(name = "MATERIAL_TYPE_CODE")
    private String materialTypeCode;

    @J<PERSON><PERSON>ield(name = "MATERIAL_TYPE_NAME")
    private String materialTypeName;

    @JSONField(name = "OWNERENAME")
    private String ownerename;

    @JSONField(name = "MODIFIERENAME")
    private String modifierename;
}