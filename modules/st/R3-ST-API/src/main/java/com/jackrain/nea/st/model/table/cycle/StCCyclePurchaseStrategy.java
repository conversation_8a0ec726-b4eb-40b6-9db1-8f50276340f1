package com.jackrain.nea.st.model.table.cycle;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.*;
import com.jackrain.nea.sys.domain.BaseModel;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@TableName(value = "st_c_cycle_purchase_strategy")
@Data
public class StCCyclePurchaseStrategy extends BaseModel implements Serializable {
    @JSONField(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    @J<PERSON><PERSON><PERSON>(name = "REMARK")
    private String remark;

    @JSONField(name = "VERSION")
    private Long version;

    @JSONField(name = "OWNERENAME")
    private String ownerename;

    @J<PERSON><PERSON>ield(name = "MODIFIERENAME")
    private String modifierename;

    @JSONField(name = "PS_C_PRO_ID")
    private Long psCProId;

    @JSONField(name = "PS_C_PRO_ECODE")
    private String psCProEcode;

    @JSONField(name = "PS_C_PRO_ENAME")
    private String psCProEname;

    @JSO<PERSON>ield(name = "START_DATE")
    private Date startDate;

    @JSONField(name = "END_DATE")
    private Date endDate;

    @JSONField(name = "SEND_PATTERN")
    private String sendPattern;

    @JSONField(name = "CYCLE_TYPE")
    private String cycleType;

    @JSONField(name = "TYPE")
    private Integer type;

    @JSONField(name = "QTY_CYCLE")
    private Integer qtyCycle;

    @JSONField(name = "STATUS")
    private Integer status;

    @JSONField(name = "DELER_ID")
    private Long delerId;

    @JSONField(name = "DELER_ENAME")
    private String delerEname;

    @JSONField(name = "DELER_NAME")
    private String delerName;

    @JSONField(name = "DEL_TIME")
    private Date delTime;

    @JSONField(name = "STATUS_ID")
    private Long statusId;

    @JSONField(name = "STATUS_ENAME")
    private String statusEname;

    @JSONField(name = "STATUS_NAME")
    private String statusName;

    @JSONField(name = "STATUS_TIME")
    private Date statusTime;

    @JSONField(name = "CLOSER_STATUS")
    private Integer closerStatus;

    @JSONField(name = "CLOSER_TIME")
    private Date closerTime;

    @JSONField(name = "CLOSER_ID")
    private Long closerId;
}