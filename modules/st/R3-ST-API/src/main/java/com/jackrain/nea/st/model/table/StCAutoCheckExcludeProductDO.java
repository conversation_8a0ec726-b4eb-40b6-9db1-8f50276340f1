package com.jackrain.nea.st.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.*;
import com.jackrain.nea.st.model.base.SubBaseModel;
import lombok.Data;

@TableName(value = "st_c_autocheck_exclude_product")
@Data
public class StCAutoCheckExcludeProductDO extends SubBaseModel {
    @JSONField(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    @JSONField(name = "ST_C_AUTOCHECK_ID")
    private Long stCAutocheckId;

    @JSONField(name = "PRO_LEVEL")
    private Integer proLevel;

    @JSONField(name = "PS_C_PRODIM_ITEM_ID")
    private Long psCProdimItemId;

    @JSONField(name = "PS_C_PRODIM_ITEM_ECODE")
    private String psCProdimItemEcode;

    @JSONField(name = "PS_C_PRO_ID")
    private Long psCProId;

    @J<PERSON><PERSON>ield(name = "PS_C_PRO_ECODE")
    private String psCProEcode;

    @JSONField(name = "PS_C_SKU_ID")
    private Long psCSkuId;

    @JSONField(name = "PS_C_SKU_ECODE")
    private String psCSkuEcode;

    @JSONField(name = "SG_B_CHANNEL_PRODUCT_SKU_ID")
    private Long sgBChannelProductSkuId;

    @JSONField(name = "PLATFORM_SKU_ID")
    private String platformSkuId;

    @JSONField(name = "SG_B_CHANNEL_PRODUCT_PRO_ID")
    private Long sgBChannelProductProId;

    @JSONField(name = "PLATFORM_NUMIID")
    private String platformNumiid;

    @JSONField(name = "REMARK")
    private String remark;

}