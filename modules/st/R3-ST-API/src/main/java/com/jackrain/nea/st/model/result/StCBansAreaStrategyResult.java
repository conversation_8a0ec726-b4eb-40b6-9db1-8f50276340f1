package com.jackrain.nea.st.model.result;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @title $
 * @Description 描述
 * @Date date()$ $
 * @Copyright 2019-2020
 */
@Data
public class StCBansAreaStrategyResult implements Serializable {

    /**
     * 物流公司
     */
    private Long cpCLogisticsId;

    private List<Long> cpCLogisticsIdList;

    public StCBansAreaStrategyResult() {
    }

    public StCBansAreaStrategyResult(Long cpCLogisticsId) {
        this.cpCLogisticsId = cpCLogisticsId;
    }

}
