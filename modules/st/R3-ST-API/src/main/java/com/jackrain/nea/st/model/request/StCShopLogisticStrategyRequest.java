package com.jackrain.nea.st.model.request;

import com.alibaba.fastjson.annotation.JSONField;
import com.jackrain.nea.st.model.table.StCShopLogisticStrategy;
import com.jackrain.nea.st.model.table.StCShopLogisticStrategyItem;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * Created with IntelliJ IDEA.
 * @Author: jg.zhan
 * @Date:  2022/6/20 19:20
 * @Description:  店铺物流设置
 */
@Data
public class StCShopLogisticStrategyRequest implements Serializable {
    private static final long serialVersionUID = -277584093839370537L;
    @JSONField(name = "ST_C_SHOP_LOGISTIC_STRATEGY")
    private StCShopLogisticStrategy stCShopLogisticStrategy;
    @JSONField(name = "ST_C_SHOP_LOGISTIC_STRATEGY_ITEM")
    private List<StCShopLogisticStrategyItem> stCShopLogisticStrategyItemList;
    private Long objid;
}
