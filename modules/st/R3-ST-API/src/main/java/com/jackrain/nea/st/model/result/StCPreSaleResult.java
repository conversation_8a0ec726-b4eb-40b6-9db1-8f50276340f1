package com.jackrain.nea.st.model.result;

import com.jackrain.nea.st.model.table.StCPreSaleDO;
import com.jackrain.nea.st.model.table.StCPreSaleItemDO;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @title $
 * @Description 描述
 * @Date date()$ $
 * @Copyright 2019-2020
 */
@Data
public class StCPreSaleResult implements Serializable {

    // 预售解析策略主档
    private StCPreSaleDO stCPreSaleDO;

    // 预售解析 策略明细
    private List<StCPreSaleItemDO> itemDOList;
}
