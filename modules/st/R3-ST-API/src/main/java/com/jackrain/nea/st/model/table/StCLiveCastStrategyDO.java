package com.jackrain.nea.st.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.*;
import com.jackrain.nea.st.model.base.SubBaseModel;
import lombok.Data;

import java.util.Date;

/**
 * 直播解析策略表
 */
@TableName(value = "st_c_live_cast_strategy")
@Data
public class StCLiveCastStrategyDO extends SubBaseModel {

    @JSONField(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    @JSONField(name = "cp_c_shop_id")
    private Long cpCShopId;
    // 店铺编码
    @JSONField(name = "cp_c_shop_ecode")
    private String cpCShopEcode;
    // 店铺名称
    @JSONField(name = "cp_c_shop_title")
    private String cpCShopTitle;
    // 方案名称
    @JSONField(name = "strategy_name")
    private String strategyName;
    // 方案描述
    @JSONField(name = "strategy_desc")
    private String strategyDesc;
    // 状态
    @JSONField(name = "strategy_status")
    private String strategyStatus;
    // 开始时间
    @JSONField(name = "start_time")
    private Date startTime;
    // 结束时间
    @JSONField(name = "end_time")
    private Date endTime;
    // 订单时间
    @JSONField(name = "bill_time_type")
    private String billTimeType;
    // 直播平台：快手,抖音,蘑菇街,陌陌,淘宝
    @JSONField(name = "live_platform")
    private String livePlatform;

    // 主播ID
    @JSONField(name = "ANCHOR_ARCHIVES_ID")
    private Long anchorArchivesId;

    // 主播ID
    @JSONField(name = "anchor_id")
    private String anchorId;

    // 主播昵称
    @JSONField(name = "anchor_nick_name")
    private String anchorNickName;

    // 直播主体
    @JSONField(name = "AC_F_MANAGE_ID")
    private Long acFManageId;

    // 直播主体 经营主体ecode
    @JSONField(name = "AC_F_MANAGE_ECODE")
    private String acFManageEcode;

    // 直播主体 经营主体ename
    @JSONField(name = "AC_F_MANAGE_ENAME")
    private String acFManageEname;

    // 配合主体
    @JSONField(name = "COOPERATE_ID")
    private Long cooperateId;

    // 配合主体 经营主体ecode
    @JSONField(name = "COOPERATE_ECODE")
    private String cooperateEcode;

    // 配合主体 经营主体ename
    @JSONField(name = "COOPERATE_ENAME")
    private String cooperateEname;

    // 直播场次
    @JSONField(name = "LIVE_EVENTS")
    private Integer liveEvents;

}
