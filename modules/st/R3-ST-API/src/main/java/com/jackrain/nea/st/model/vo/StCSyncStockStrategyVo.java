package com.jackrain.nea.st.model.vo;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class StCSyncStockStrategyVo implements Serializable{

    @J<PERSON><PERSON>ield(name = "ST_C_SYNC_STOCK_STRATEGY_ID")
    private Long stCSyncStockStrategyId;

    @JSONField(name = "CP_C_STORE_ID")
    private Long cpCStoreId;

    @JSONField(name = "CP_C_STORE_ECODE")
    private String cpCStoreEcode;

    @JSONField(name = "CP_C_STORE_ENAME")
    private String cpCStoreEname;

    @J<PERSON><PERSON>ield(name = "PRIORITY")
    private Integer priority;

    @J<PERSON><PERSON>ield(name = "RATE")
    private BigDecimal rate;

    @J<PERSON><PERSON>ield(name = "LOW_STOCK")
    private Long lowStock;

    @JSONField(name = "IS_SEND")
    private Integer isSend;

    @J<PERSON>NField(name = "CP_C_SHOP_ID")
    private Long cpCShopId;

    @J<PERSON><PERSON>ield(name = "CP_C_SHOP_TITLE")
    private String cpCShopTitle;

    @J<PERSON><PERSON><PERSON>(name = "STOCK_RATE")
    private BigDecimal stockRate;

    @JSONField(name = "IS_SYNC_STOCK")
    private Integer isSyncStock;

}
