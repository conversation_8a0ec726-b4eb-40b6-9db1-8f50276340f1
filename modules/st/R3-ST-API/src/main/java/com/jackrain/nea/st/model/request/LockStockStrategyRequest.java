package com.jackrain.nea.st.model.request;

import com.alibaba.fastjson.annotation.JSONField;
import com.jackrain.nea.st.model.table.StCLockStockStrategyDO;
import com.jackrain.nea.st.model.table.StCLockStockStrategyItemDO;
import com.jackrain.nea.st.model.table.StCVipcomProjectDO;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 店铺锁库策略设置 请求实体
 *
 * <AUTHOR>
 * @Date 2019/3/10 21:26
 */
@Data
public class LockStockStrategyRequest  implements Serializable {
    @J<PERSON>NField(name = "ST_C_LOCK_STOCK_STRATEGY")
    private StCLockStockStrategyDO stCLockStockStrategy;
    @JSONField(name = "ST_C_LOCK_STOCK_STRATEGY_ITEM")
    private List<StCLockStockStrategyItemDO> stCLockStockStrategyItemList;
    private Long objId;
}
