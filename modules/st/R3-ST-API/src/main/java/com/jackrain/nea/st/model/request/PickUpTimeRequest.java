package com.jackrain.nea.st.model.request;

import com.alibaba.fastjson.annotation.JSONField;
import com.jackrain.nea.st.model.table.*;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 上门取件时间策略
 *
 * <AUTHOR>
 * @Date 2020/5/12 21:26
 */
@Data
public class PickUpTimeRequest implements Serializable {
    @JSONField(name = "ST_C_PICK_UP_TIME")
    private StCPickUpTimeDO stCPickUpTime;
    @JSONField(name = "ST_C_PICK_UP_TIME_ITEM")
    private List<StCPickUpTimeItemDO> stCPickUpTimeItem;
    private Long objId;
}
