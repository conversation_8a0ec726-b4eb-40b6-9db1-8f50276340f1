package com.jackrain.nea.st.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jackrain.nea.st.model.base.SubBaseModel;
import lombok.Data;

import java.util.Date;

@TableName(value = "st_c_vipcom_project_wh_entry_item")
@Data
public class StCVipcomProjectWhEntryItem extends SubBaseModel {
    @JSONField(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    @JSONField(name = "ST_C_VIPCOM_PROJECT_ID")
    private Long stCVipcomProjectId;

    @JSONField(name = "PEAK_VALUE")
    private Integer peakValue;

    @JSONField(name = "CARRIER_CODE")
    private String carrierCode;

    @JSONField(name = "SEND_INTERVAL")
    private String sendInterval;

    @JSONField(name = "SENDTIME")
    private String sendtime;

    @JSONField(name = "ARRIVAL_INTERVAL")
    private String arrivalInterval;

    @JSONField(name = "ARRIVALTIME")
    private String arrivaltime;

    @JSONField(name = "STATEMENT_TIME")
    private String statementTime;

    @JSONField(name = "DELIVERY_WAY")
    private String deliveryWay;

    @JSONField(name = "IS_AIR_EMBARGO")
    private Integer isAirEmbargo;

    @JSONField(name = "OWNERENAME")
    private String ownerename;

    @JSONField(name = "MODIFIERENAME")
    private String modifierename;

    @JSONField(name = "CP_C_PHY_WAREHOUSE_ID")
    private String cpCPhyWarehouseId;

    @JSONField(name = "CP_C_PHY_WAREHOUSE_ECODE")
    private String cpCPhyWarehouseEcode;

    @JSONField(name = "CP_C_PHY_WAREHOUSE_ENAME")
    private String cpCPhyWarehouseEname;
}