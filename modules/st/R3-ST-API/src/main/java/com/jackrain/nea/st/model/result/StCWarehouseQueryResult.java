package com.jackrain.nea.st.model.result;

import com.jackrain.nea.st.model.table.*;
import lombok.Data;

import java.util.List;

/**
 * @Description: 仓库拆单策略查询结果
 * @author: 江家雷
 * @since: 2020/8/17
 * create at : 2020/8/17 16:00
 */
@Data
public class StCWarehouseQueryResult extends BaseResult {

    private StCWarehouseDO StCWarehouse;

    private List<StCWarehouseBrandDO> stCWarehouseBrandList;

    private List<StCWarehouseGoodsDO> stCWarehouseGoodsList;

    private List<StCWarehouseSkuDO> stCWarehouseSkuList;

    private List<StCWarehouseGoodsClassDO> stCWarehouseGoodsClassList;
}