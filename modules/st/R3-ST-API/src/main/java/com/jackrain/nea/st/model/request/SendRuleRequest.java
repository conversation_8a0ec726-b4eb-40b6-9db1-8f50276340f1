package com.jackrain.nea.st.model.request;

import com.alibaba.fastjson.annotation.JSONField;
import com.jackrain.nea.st.model.result.SendRuleAddressRankResult;
import com.jackrain.nea.st.model.result.SendRuleAddressVipResult;
import com.jackrain.nea.st.model.table.StCSendRuleAddressRentDO;
import com.jackrain.nea.st.model.table.StCSendRuleDO;
import com.jackrain.nea.st.model.table.StCSendRuleWarehouseRateDO;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * Descroption 订单派单规则
 *
 * <AUTHOR>
 * @Date 2019/9/4 15:23
 */
@Data
public class SendRuleRequest implements Serializable {
    /**
     * 订单派单规则
     */
    @JSONField(name = "ST_C_SEND_RULE")
    private StCSendRuleDO sendRule;
    /**
     * 订单派单规则明细
     */
    @JSONField(name = "ST_C_SEND_RULE_ADDRESS_RENT")
    private List<StCSendRuleAddressRentDO> rentList;
    /**
     * 订单派单规则-仓库优先级明细
     */
    @JSONField(name = "ST_C_SEND_RULE_ADDRESS_RANK_RESULT")
    private List<SendRuleAddressRankResult> rankList;
    /**
     * 订单派单规则-分仓比例明细
     */
    @JSONField(name = "ST_C_SEND_RULE_WAREHOUSE_RATE")
    private List<StCSendRuleWarehouseRateDO> rateList;
    /**
     * 订单派单规则-唯品会明细
     */
    @JSONField(name = "ST_C_SEND_RULE_ADDRESS_VIP_RESULT")
    private List<SendRuleAddressVipResult> sendRuleAddressVipResultList;

}
