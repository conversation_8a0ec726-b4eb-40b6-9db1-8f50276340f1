package com.jackrain.nea.st.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.*;
import com.jackrain.nea.st.model.base.SubBaseModel;
import lombok.Data;

@TableName(value = "st_c_pick_up_time_item")
@Data
public class StCPickUpTimeItemDO extends SubBaseModel {
    @J<PERSON><PERSON>ield(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    @JSONField(name = "ST_C_PICK_UP_TIME_ID")
    private Long stCPickUpTimeId;

    @JSONField(name = "CP_C_PHY_WAREHOUSE_ID")
    private Long cpCPhyWarehouseId;

    @JSONField(name = "CP_C_PHY_WAREHOUSE_ECODE")
    private String cpCPhyWarehouseEcode;

    @<PERSON><PERSON><PERSON>ield(name = "CP_C_PHY_WAREHOUSE_ENAME")
    private String cpCPhyWarehouseEname;

    @<PERSON><PERSON><PERSON>ield(name = "OWNERENAME")
    private String ownerename;

    @<PERSON><PERSON><PERSON>ield(name = "MODIFIERENAME")
    private String modifierename;
}