package com.jackrain.nea.st.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.*;
import com.jackrain.nea.es.annotations.Document;
import com.jackrain.nea.es.annotations.Field;
import com.jackrain.nea.es.constans.FieldType;
import com.jackrain.nea.st.model.base.SubBaseModel;
import lombok.Data;

@TableName(value = "ST_C_VIPCOM_PROJECT_ITEM")
@Data
@Document(index = "st_c_vipcom_project_item", type = "st_c_vipcom_project_item")
public class StCVipcomProjectItemDO extends SubBaseModel {
    @JSONField(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "id", type = IdType.INPUT)
    @Field(type = FieldType.Long)
    private Long id;

    @JSONField(name = "ST_C_VIPCOM_PROJECT_ID")
    @Field(type = FieldType.Long)
    private Long stCVipcomProjectId;

    @JSONField(name = "ENAME")
    @Field(type = FieldType.Keyword)
    private String ename;

    @JSONField(name = "DOWNLOAD_TIME")
    @Field(type = FieldType.Date)
    private String downloadTime;

    @JSONField(name = "DELIVERY_METHOD")
    @Field(type = FieldType.Keyword)
    private String deliveryMethod;

    @JSONField(name = "EXP_SENDTIME")
    @Field(type = FieldType.Date)
    private String expSendtime;

    @JSONField(name = "EXP_ARRIVETIME")
    @Field(type = FieldType.Date)
    private String expArrivetime;

    @JSONField(name = "ARRIVAL_INTERVAL")
    @Field(type = FieldType.Long)
    private Long arrivalInterval;

    @JSONField(name = "CP_C_ORIG_ENAME")
    @Field(type = FieldType.Keyword)
    private String cpCOrigEname;

    @JSONField(name = "IS_DOWNLOAD")
    @Field(type = FieldType.Integer)
    private Integer isDownload;

    @JSONField(name = "IS_OUT")
    @Field(type = FieldType.Integer)
    private Integer isOut;

    @JSONField(name = "IS_STATEMENT")
    @Field(type = FieldType.Integer)
    private Integer isStatement;

    /**
     * 是否航空禁运,0-不禁运，1-禁运，默认为0
     */
    @JSONField(name = "IS_AIR_EMBARGO")
    @Field(type = FieldType.Integer)
    private Integer isAirEmbargo;

    /**
     * 承运商编码
     */
    @JSONField(name = "CARRIER_CODE")
    @Field(type = FieldType.Keyword)
    private String carrierCode;

    @JSONField(name = "BILL_TYPE")
    @Field(type = FieldType.Integer)
    private Integer billType;

    @JSONField(name = "OWNERENAME")
    @Field(type = FieldType.Keyword)
    private String ownerename;

    @JSONField(name = "MODIFIERENAME")
    @Field(type = FieldType.Keyword)
    private String modifierename;

    @JSONField(name = "ST_C_VIPCOM_ASCRIPTION_ID")
    @Field(type = FieldType.Long)
    private Long stCVipcomAscriptionId;

    @JSONField(name = "SEND_INTERVAL")
    @Field(type = FieldType.Long)
    private Long sendInterval;

    @JSONField(name = "CP_C_PHY_WAREHOUSE_ID")
    @Field(type = FieldType.Keyword)
    private String cpCPhyWarehouseId;

    @JSONField(name = "CP_C_PHY_WAREHOUSE_ECODE")
    @Field(type = FieldType.Keyword)
    private String cpCPhyWarehouseEcode;

    @JSONField(name = "CP_C_PHY_WAREHOUSE_ENAME")
    @Field(type = FieldType.Keyword)
    private String cpCPhyWarehouseEname;

    @JSONField(name = "CP_C_ORIG_ID")
    @Field(type = FieldType.Keyword)
    private String cpCOrigId;

    @JSONField(name = "CP_C_ORIG_ECODE")
    @Field(type = FieldType.Keyword)
    private String cpCOrigEcode;

    @JSONField(name = "PICK_FLAG")
    @Field(type = FieldType.Integer)
    private Integer pickFlag;
}