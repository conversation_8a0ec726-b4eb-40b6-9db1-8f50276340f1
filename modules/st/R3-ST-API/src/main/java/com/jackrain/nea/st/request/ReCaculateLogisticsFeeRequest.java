package com.jackrain.nea.st.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


/**
 * <AUTHOR>
 * @Date 2022/9/8 09:56
 * @Description
 */
@Data
public class ReCaculateLogisticsFeeRequest {

    @ApiModelProperty(value = "报价主表id")
    private Long id;

    /**
     * 0-零担；1-快递；2-快运
     */
    @ApiModelProperty(value = "报价设置类型")
    private Integer type;

    @ApiModelProperty(value = "开始对账日期")
    private String beginConfirmDate;

    @ApiModelProperty(value = "结束对账日期")
    private String endConfirmDate;
}
