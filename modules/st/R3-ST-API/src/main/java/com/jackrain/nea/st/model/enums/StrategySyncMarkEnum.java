package com.jackrain.nea.st.model.enums;

/**
 * <ul>
 *     <li>
 *         策略是否开始执行标记
 *     </li>
 * </ul>
 */
public enum StrategySyncMarkEnum {

    /**
     * 策略未开始执行
     */
    NONBEGIN(0, "未开始"),

    /**
     * 策略已执行
     */
    BEGINNING(1, "开始"),

    END(2, "结束"),

    COMPLETE(3, "结案");

    private final Integer code;

    private final String desc;

    StrategySyncMarkEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
