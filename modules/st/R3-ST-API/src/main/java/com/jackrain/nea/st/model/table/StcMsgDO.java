package com.jackrain.nea.st.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.*;
import com.jackrain.nea.es.annotations.Field;
import com.jackrain.nea.es.constans.FieldType;
import com.jackrain.nea.st.model.base.SubBaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

@TableName(value = "st_c_msg_strategy")
@Data
@EqualsAndHashCode(callSuper=false)
public class StcMsgDO extends SubBaseModel {

    @JSONField(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    /**
     短信策略名称
     */
    @JSONField(name = "MSG_NAME")
    @Field(type = FieldType.Keyword)
    private String msgName;
    /**
     * 通知类型
     *      1:物流发货提醒
     *      2:退换货提醒
     */
    @JSONField(name = "ADVICE_TYPE")
    @Field(type = FieldType.Keyword)
    private String adviceType;
    /**
     * 任务节点
     *      1:非平台拆分订单完成仓库发货
     *      2:非天猫换货订单完成仓库发货
     *      3:退换货完成入库
     *      4:无名件完成入库
     */
    @JSONField(name = "TASK_NODE")
    @Field(type = FieldType.Keyword)
    private String taskNode;
    /**
     是否发送短信（Y/N）
     */
    @JSONField(name = "IS_SEND")
    @Field(type = FieldType.Keyword)
    private String isSend;
    /**
     店铺ID(多店铺逗号相隔)
     */
    @JSONField(name = "CP_C_SHOP_ID")
    @Field(type = FieldType.Keyword)
    private String cpCShopId;
    /**
     短信模板内容
     */
    @JSONField(name = "TEMPLATE_CONTENT")
    @Field(type = FieldType.Text)
    private String templateContent;
    /**
     备注
     */
    @JSONField(name = "REMARK")
    @Field(type = FieldType.Keyword)
    private String remark;
    /**
     店铺名
     */
    @JSONField(name = "CP_C_SHOP_TITLE")
    @Field(type = FieldType.Text)
    private String cpCShopTitle;
    /**
     扩展字段
     */
    @JSONField(name = "EXTEND_FIELD")
    @Field(type = FieldType.Text)
    private String extendField;

}
