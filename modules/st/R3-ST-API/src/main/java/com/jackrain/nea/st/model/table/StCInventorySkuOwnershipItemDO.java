package com.jackrain.nea.st.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.*;
import com.jackrain.nea.st.model.base.SubBaseModel;
import lombok.Data;

import java.math.BigDecimal;

@TableName(value = "st_c_inventory_sku_ownership_item")
@Data
public class StCInventorySkuOwnershipItemDO extends SubBaseModel {
    @JSONField(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    @J<PERSON>NField(name = "ST_C_INVENTORY_SKU_OWNERSHIP_ID")
    private Long stCInventorySkuOwnershipId;

    @JSONField(name = "PS_C_SKU_ID")
    private Long psCSkuId;

    @JSONField(name = "PS_C_SKU_ECODE")
    private String psCSkuEcode;

    @JSONField(name = "PS_C_PRO_ID")
    private Long psCProId;

    @JSONField(name = "PS_C_PRO_ECODE")
    private String psCProEcode;

    @JSONField(name = "PS_C_PRO_ENAME")
    private String psCProEname;

    @JSONField(name = "GBCODE")
    private String gbcode;

    @JSONField(name = "PS_C_CLR_ID")
    private Long psCClrId;

    @JSONField(name = "PS_C_CLR_ECODE")
    private String psCClrEcode;

    @JSONField(name = "PS_C_CLR_ENAME")
    private String psCClrEname;

    @JSONField(name = "PS_C_SIZE_ID")
    private Long psCSizeId;

    @JSONField(name = "PS_C_SIZE_ECODE")
    private String psCSizeEcode;

    @JSONField(name = "PS_C_SIZE_ENAME")
    private String psCSizeEname;

    @JSONField(name = "OWNERSHIP_NUM")
    private BigDecimal ownershipNum;

    @JSONField(name = "CP_C_SHOP_ID")
    private Long cpCShopId;

    @JSONField(name = "CP_C_SHOP_ECODE")
    private String cpCShopEcode;

    @JSONField(name = "CP_C_SHOP_TITLE")
    private String cpCShopTitle;

    @JSONField(name = "OWNERENAME")
    private String ownerename;

    @JSONField(name = "MODIFIERENAME")
    private String modifierename;
}