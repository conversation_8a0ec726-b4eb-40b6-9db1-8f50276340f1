package com.jackrain.nea.st.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jackrain.nea.es.annotations.Field;
import com.jackrain.nea.es.constans.FieldType;
import com.jackrain.nea.st.model.base.SubBaseModel;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.io.Serializable;

/**
 * @program: r3-st
 * @description: 仓库物流设置主表
 * @author: caomalai
 * @create: 2022-06-23 16:55
 **/
@ApiModel(value="仓库物流设置明细表")
@Data
@TableName(value = "st_c_warehouse_logistic_strategy_item")
public class StCWarehouseLogisticStrategyItem extends SubBaseModel implements Serializable,Comparable<StCWarehouseLogisticStrategyItem> {

    /**
     * id
     */
    @JSONField(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    /**
     * 主表ID
     */
    @JSONField(name = "ST_C_WAREHOUSE_LOGISTIC_STRATEGY_ID")
    @Field(type = FieldType.Long)
    private Long stCWarehouseLogisticStrategyId;

    /**
     * 物流公司ID
     */
    @JSONField(name = "CP_C_LOGISTICS_ID")
    @Field(type = FieldType.Long)
    private Long cpCLogisticsId;

    /**
     * 物流公司code
     */
    @JSONField(name = "CP_C_LOGISTICS_ECODE")
    @Field(type = FieldType.Keyword)
    private String cpCLogisticsEcode;

    /**
     * 物流公司名称
     */
    @JSONField(name = "CP_C_LOGISTICS_ENAME")
    @Field(type = FieldType.Keyword)
    private String cpCLogisticsEname;

    /**
     * 物流类型 1=快递 2=快运 3=大货物流 4=自提
     */
    @JSONField(name = "LOGISTICS_TYPE")
    @Field(type = FieldType.Keyword)
    private Integer logisticsType;

    /**
     * 物流结算公司ID
     */
    @JSONField(name = "LOGISTICS_SUPPLIER_ID")
    @Field(type = FieldType.Long)
    private Long logisticsSupplierId;

    /**
     * 物流结算公司代码
     */
    @JSONField(name = "LOGISTICS_SUPPLIER_ECODE")
    @Field(type = FieldType.Keyword)
    private String logisticsSupplierEcode;

    /**
     * 物流结算公司名称
     */
    @JSONField(name = "LOGISTICS_SUPPLIER_ENAME")
    @Field(type = FieldType.Keyword)
    private String logisticsSupplierEname;

    @JSONField(name = "ENABLE_MAXIMUM_ORDER_VOLUME")
    private String enableMaximumOrderVolume;

    @JSONField(name = "MAXIMUM_ORDER_VOLUME")
    private Integer maximumOrderVolume;

    /**
     * 优先级
     */
    @JSONField(name = "ITEM_PRIORITY")
    private Integer itemPriority;

    @TableField(strategy = FieldStrategy.IGNORED)
    @JSONField(name = "EXPIRE_START_TIME")
    private String expireStartTime;

    @TableField(strategy = FieldStrategy.IGNORED)
    @JSONField(name = "EXPIRE_END_TIME")
    private String expireEndTime;

    @Override
    public int compareTo(StCWarehouseLogisticStrategyItem item) {
        // 从小到大
        return this.itemPriority.compareTo(item.getItemPriority());
    }
}
