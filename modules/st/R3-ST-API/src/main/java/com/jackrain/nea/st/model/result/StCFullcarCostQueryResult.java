package com.jackrain.nea.st.model.result;

import com.jackrain.nea.st.model.table.StCFullcarCost;
import com.jackrain.nea.st.model.table.StCFullcarCostItem;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @ClassName StCFullcarCostQueryResult
 * @Description 整车报价
 * <AUTHOR>
 * @Date 2024/4/8 14:46
 * @Version 1.0
 */
@Data
public class StCFullcarCostQueryResult implements Serializable {
    private static final long serialVersionUID = -6316112624616540789L;

    /**
     * 整车报价
     */
    private List<StCFullcarCost> fullcarCostList;

    /**
     * 整车报价明细
     */
    private List<StCFullcarCostItem> fullcarCostItemList;
}
