package com.jackrain.nea.st.utils;

import com.jackrain.nea.config.Resources;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.util.ValueHolder;
import org.json.JSONArray;
import org.json.JSONObject;

import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;

/**
 * ValueHolder返回类
 *
 * <AUTHOR>
 */
public class ValueHolderUtils {

    /**
     * 异常或者失败
     *
     * @param message 提示信息
     * @return holder
     */
    public static ValueHolder getFailValueHolder(String message) {
        ValueHolder holder = new ValueHolder();
        HashMap map = new HashMap();
        map.put("code", ResultCode.FAIL);
        map.put("message", Resources.getMessage(message, new Locale("zh", "CN")));
        map.put("data", new HashMap<>());
        holder.setData(map);
        return holder;
    }

    /**
     * 成功
     *
     * @return hoder
     */
    public static ValueHolder getSuccessValueHolder(String message) {
        ValueHolder holder = new ValueHolder();
        HashMap map = new HashMap();
        map.put("code", ResultCode.SUCCESS);
        map.put("message", Resources.getMessage(message, new Locale("zh", "CN")));
        map.put("data", new HashMap<>());
        holder.setData(map);
        return holder;
    }

    /**
     * 成功
     *
     * @param objid     当前操作主表ID(单表为本身)
     * @param tableName 当前操作数据库表名
     * @return holder
     */
    public static ValueHolder getSuccessValueHolder(Long objid, String tableName) {
        ValueHolder holder = new ValueHolder();
        Map<String, Object> data = new HashMap();
        data.put("objid", objid);
        data.put("tablename", tableName);
        holder.put("code", ResultCode.SUCCESS);
        holder.put("message", Resources.getMessage("success", new Locale("zh", "CN")));
        holder.put("data", data);
        return holder;
    }
    /**
     * 成功
     * @param message 执行结果提示语
     * @param objid     当前操作主表ID(单表为本身)
     * @param tableName 当前操作数据库表名
     * @return holder
     */
    public static ValueHolder getSuccessValueHolderByMessage(Long objid, String tableName,String message) {
        ValueHolder holder = new ValueHolder();
        Map<String, Object> data = new HashMap();
        data.put("objid", objid);
        data.put("tablename", tableName);
        HashMap map = new HashMap();
        map.put("code", ResultCode.SUCCESS);
        map.put("message", Resources.getMessage(message, new Locale("zh", "CN")));
        map.put("data", data);
        holder.setData(map);
        return holder;
    }

    /**
     * 成功
     *
     * @param objid     当前操作主表ID(单表为本身)
     * @param tableName 当前操作数据库表名
     * @return holder
     */
    public static ValueHolder getSuccessValueHolder(Long objid, String tableName, String message) {
        ValueHolder holder = new ValueHolder();
        Map<String, Object> data = new HashMap();
        data.put("objid", objid);
        data.put("tablename", tableName);
        HashMap map = new HashMap();
        map.put("code", ResultCode.SUCCESS);
        map.put("message", Resources.getMessage(message, new Locale("zh", "CN")));
        map.put("data", data);
        holder.setData(map);
        return holder;
    }

    /**
     * 删除成功
     *
     * @return hoder
     */
    public static ValueHolder getDeleteSuccessValueHolder() {
        ValueHolder holder = new ValueHolder();
        HashMap map = new HashMap();
        map.put("code", ResultCode.SUCCESS);
        map.put("message", Resources.getMessage("删除成功", new Locale("zh", "CN")));
        map.put("data", new HashMap<>());
        holder.setData(map);
        return holder;
    }


    public static ValueHolder custom(Integer code, String message, Object data) {
        ValueHolder valueHolder = new ValueHolder();
        valueHolder.put("code", code);
        valueHolder.put("message", message);
        valueHolder.put("data", data);
        return valueHolder;
    }


}
