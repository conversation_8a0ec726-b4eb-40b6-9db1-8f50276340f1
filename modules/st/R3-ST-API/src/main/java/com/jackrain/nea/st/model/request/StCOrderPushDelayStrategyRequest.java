package com.jackrain.nea.st.model.request;

import com.alibaba.fastjson.annotation.JSONField;
import com.jackrain.nea.st.model.table.StCOrderPushDelayStrategy;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Description 订单推单延时策略
 * @Date 15:12 2020/7/1
 **/
@Data
public class StCOrderPushDelayStrategyRequest implements Serializable {
    @JSONField(name = "ST_C_ORDER_PUSH_DELAY_STRATEGY")
    private StCOrderPushDelayStrategy stCOrderPushDelayStrategy;
}
