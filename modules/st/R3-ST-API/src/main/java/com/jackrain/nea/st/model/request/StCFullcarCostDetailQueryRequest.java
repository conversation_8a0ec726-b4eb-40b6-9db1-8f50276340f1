package com.jackrain.nea.st.model.request;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 整车报价明细查询请求
 */
@Data
public class StCFullcarCostDetailQueryRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 整车报价设置ID
     */
    private Long fullcarCostId;

    /**
     * 重量
     */
    private BigDecimal weight;

    /**
     * 省份ID
     */
    private Long provinceId;

    /**
     * 城市ID
     */
    private Long cityId;
}
