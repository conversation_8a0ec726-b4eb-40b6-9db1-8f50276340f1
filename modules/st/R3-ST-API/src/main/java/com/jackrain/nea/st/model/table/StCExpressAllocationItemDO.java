package com.jackrain.nea.st.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.*;
import com.jackrain.nea.st.model.base.SubBaseModel;
import lombok.Data;

import java.math.BigDecimal;

@TableName(value = "st_c_express_allocation_item")
@Data
public class StCExpressAllocationItemDO extends SubBaseModel {
    @JSONField(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    @JSONField(name = "ST_C_EXPRESS_ALLOCATION_ID")
    private Long stCExpressAllocationId;

    @J<PERSON><PERSON>ield(name = "CP_C_LOGISTICS_ID")
    private Long cpCLogisticsId;

    @JSONField(name = "CP_C_LOGISTICS_ECODE")
    private String cpCLogisticsEcode;

    @J<PERSON>NField(name = "CP_C_LOGISTICS_ENAME")
    private String cpCLogisticsEname;

    @JSONField(name = "CP_C_SHOP_ID")
    private String cpCShopId;

    @J<PERSON><PERSON>ield(name = "CP_C_SHOP_ENAME")
    private String cpCShopEname;

    @JSONField(name = "CP_C_SHOP_ECODE")
    private String cpCShopEcode;

    @JSONField(name = "SCALE")
    private BigDecimal scale;

    @JSONField(name = "LIMIT_NUM")
    private BigDecimal limitNum;

    @JSONField(name = "SEND_NUM")
    private BigDecimal sendNum;

    @JSONField(name = "IS_GET_EWAYBILL")
    private String isGetEwaybill;

    @JSONField(name = "OWNERENAME")
    private String ownerename;

    @JSONField(name = "MODIFIERENAME")
    private String modifierename;
}