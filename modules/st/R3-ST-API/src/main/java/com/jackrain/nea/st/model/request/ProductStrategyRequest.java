package com.jackrain.nea.st.model.request;

import com.alibaba.fastjson.annotation.JSONField;
import com.jackrain.nea.st.model.table.StCProductStrategyDO;
import com.jackrain.nea.st.model.table.StCProductStrategyItemDO;
import lombok.Data;

import java.util.List;

@Data
public class ProductStrategyRequest {
    @JSONField(name = "ST_C_PRODUCT_STRATEGY")
    private StCProductStrategyDO stCProductStrategyDO;
    @JSONField(name = "ST_C_PRODUCT_STRATEGY_ITEM")
    private List<ProductStrategyItemRequest> strategyItemRequestList;
    private Long objId;
}
