package com.jackrain.nea.st.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.*;
import com.jackrain.nea.st.model.base.SubBaseModel;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @create 2020-06-15 16:55
 * @desc 订单派单规则-分仓比例明细
 **/
@TableName(value = "st_c_send_rule_warehouse_rate")
@Data
public class StCSendRuleWarehouseRateDO extends SubBaseModel {
    @JSONField(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    @JSONField(name = "ST_C_SEND_RULE_ID")
    private Long stCSendRuleId;

    @JSONField(name = "CP_C_PHY_WAREHOUSE_ID")
    private Long cpCPhyWarehouseId;

    @JSONField(name = "CP_C_PHY_WAREHOUSE_ECODE")
    private String cpCPhyWarehouseEcode;

    @JSONField(name = "CP_C_PHY_WAREHOUSE_ENAME")
    private String cpCPhyWarehouseEname;

    @JSONField(name = "RANK")
    private Long rank;

    @JSONField(name = "SEND_RATE")
    private BigDecimal sendRate;

    @JSONField(name = "QTY_SEND")
    private BigDecimal qtySend;

    @JSONField(name = "OWNERENAME")
    private String ownerename;

    @JSONField(name = "MODIFIERENAME")
    private String modifierename;
}