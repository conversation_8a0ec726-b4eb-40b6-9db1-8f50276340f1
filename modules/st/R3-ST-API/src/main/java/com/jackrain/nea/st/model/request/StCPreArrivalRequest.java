package com.jackrain.nea.st.model.request;

import com.alibaba.fastjson.annotation.JSONField;
import com.jackrain.nea.st.model.table.*;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 预到仓方案
 *
 * <AUTHOR>
 * @Date 2020/06/10
 */
@Data
public class StCPreArrivalRequest implements Serializable {
    @JSONField(name = "ST_C_PRE_ARRIVAL")
    private StCPreArrivalDO stCPreArrival;
    @JSONField(name = "ST_C_PRE_ARRIVAL_ITEM")
    private List<StCPreArrivalItemDO> stCPreArrivalItemList;
    private Long objId;
}
