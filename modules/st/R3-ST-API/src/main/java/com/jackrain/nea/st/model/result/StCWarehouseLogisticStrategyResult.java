package com.jackrain.nea.st.model.result;

import com.alibaba.fastjson.annotation.JSONField;
import com.jackrain.nea.es.annotations.Field;
import com.jackrain.nea.es.constans.FieldType;
import com.jackrain.nea.st.model.table.StCWarehouseLogisticStrategyItem;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @title $
 * @Description 描述
 * @Date date()$ $
 * @Copyright 2019-2020
 */
@Data
public class StCWarehouseLogisticStrategyResult extends StCWarehouseLogisticStrategyItem implements Serializable {

    /**
     * 仓库ID
     */
    @J<PERSON>NField(name = "CP_C_PHY_WAREHOUSE_ID")
    @Field(type = FieldType.Long)
    private Long cpCPhyWarehouseId;

    @JSONField(name = "IS_EXPIRE")
    @Field(type = FieldType.Keyword)
    private String isExpire;

}
