package com.jackrain.nea.st.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.*;
import com.jackrain.nea.st.model.base.SubBaseModel;
import lombok.Data;

import java.util.Date;

@TableName(value = "st_c_inventory_sku_ownership")
@Data
public class StCInventorySkuOwnershipDO extends SubBaseModel {
    @JSONField(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    @JSONField(name = "ENAME")
    private String ename;

    @J<PERSON><PERSON>ield(name = "ECODE")
    private String ecode;

    @J<PERSON><PERSON>ield(name = "BEGIN_TIME")
    private Date beginTime;

    @JSONField(name = "END_TIME")
    private Date endTime;

    @JSONField(name = "REMARK")
    private String remark;

    @JSONField(name = "DELER_ID")
    private Long delerId;

    @JSONField(name = "DELENAME")
    private String delename;

    @J<PERSON><PERSON>ield(name = "DELNAME")
    private String delname;

    @J<PERSON><PERSON>ield(name = "DEL_TIME")
    private Date delTime;

    @JSONField(name = "CHECKID")
    private Long checkid;

    @JSONField(name = "CHECKENAME")
    private String checkename;

    @JSONField(name = "CHECKNAME")
    private String checkname;

    @JSONField(name = "CHECKTIME")
    private Date checktime;

    @JSONField(name = "REVERSE_ID")
    private Long reverseId;

    @JSONField(name = "REVERSE_ENAME")
    private String reverseEname;

    @JSONField(name = "REVERSE_NAME")
    private String reverseName;

    @JSONField(name = "REVERSE_TIME")
    private Date reverseTime;

    @JSONField(name = "OWNERENAME")
    private String ownerename;

    @JSONField(name = "MODIFIERENAME")
    private String modifierename;

    @JSONField(name = "ESTATUS")
    private Integer estatus;

    @JSONField(name = "FINISHID")
    private Long finishid;

    @JSONField(name = "FINISHENAME")
    private String finishename;

    @JSONField(name = "FINISHNAME")
    private String finishname;

    @JSONField(name = "FINISHTIME")
    private Date finishtime;

    @JSONField(name = "CP_C_SHOP_CHANNEL_TYPE")
    private Long cpCShopChannelType;
}