package com.jackrain.nea.st.model.request.cycle;


import com.jackrain.nea.sys.domain.BaseModel;
import lombok.Data;

import java.io.Serializable;

/**
 * @Auther: chenhao
 * @Date: 2022-09-03 15:28
 * @Description:
 */

@Data
public class StCCyclePurchaseStrategyItemSaveRequest extends BaseModel implements Serializable {

    private Long id;

    private String remark;

    private Long version;

    private String ownerename;

    private String modifierename;

    private Long psCSkuId;

    private String psCSkuEcode;

    private String gbcode;

    private Long psCBrandId;

    private Long psCProId;

    private String psCProEcode;

    private String psCProEname;

    private Long psCSpec1Id;

    private String psCSpec1Ecode;

    private String psCSpec1Ename;

    private Long psCSpec2Id;

    private String psCSpec2Ecode;

    private String psCSpec2Ename;

    private Long psCSpec3Id;

    private String psCSpec3Ecode;

    private String psCSpec3Ename;

    private Integer qty;

    private Long stCCyclePurchaseStrategyId;
}
