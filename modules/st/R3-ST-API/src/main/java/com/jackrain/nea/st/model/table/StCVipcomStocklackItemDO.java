package com.jackrain.nea.st.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.*;
import com.jackrain.nea.st.model.base.SubBaseModel;
import lombok.Data;

import java.math.BigDecimal;

@TableName(value = "st_c_vipcom_stocklack_item")
@Data
public class StCVipcomStocklackItemDO extends SubBaseModel {
    @J<PERSON>NField(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    @J<PERSON><PERSON>ield(name = "ST_C_VIPCOM_STOCKLACK_ID")
    private Long stCVipcomStocklackId;

    @JSONField(name = "GBCODE")
    private BigDecimal gbcode;

    @J<PERSON><PERSON>ield(name = "PS_C_SKU_ID")
    private Long psCSkuId;

    @JSONField(name = "PS_C_SKU_ECODE")
    private String psCSkuEcode;

    @JSONField(name = "LACK_NUM")
    private BigDecimal lackNum;

    @J<PERSON><PERSON>ield(name = "OWNERENAME")
    private String ownerename;

    @JSO<PERSON>ield(name = "MODIFIERENAME")
    private String modifierename;
}