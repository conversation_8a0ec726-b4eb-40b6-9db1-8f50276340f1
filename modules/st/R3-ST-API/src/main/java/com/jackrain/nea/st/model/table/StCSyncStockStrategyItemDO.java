package com.jackrain.nea.st.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.*;
import com.jackrain.nea.st.model.base.SubBaseModel;
import lombok.Data;

import java.math.BigDecimal;

@TableName(value = "ST_C_SYNC_STOCK_STRATEGY_ITEM")
@Data
public class StCSyncStockStrategyItemDO extends SubBaseModel {
    @JSONField(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    @J<PERSON>NField(name = "ST_C_SYNC_STOCK_STRATEGY_ID")
    private Long stCSyncStockStrategyId;

    @JSONField(name = "CP_C_STORE_ID")
    private Long cpCStoreId;

    @JSONField(name = "CP_C_STORE_ECODE")
    private String cpCStoreEcode;

    @JSONField(name = "CP_C_STORE_ENAME")
    private String cpCStoreEname;

    @JSONField(name = "PRIORITY")
    private Integer priority;

    @JSONField(name = "RATE")
    private BigDecimal rate;

    @JSONField(name = "LOW_STOCK")
    private Long lowStock;

    @JSONField(name = "IS_SEND")
    private Integer isSend;

    @JSONField(name = "OWNERENAME")
    private String ownerename;

    @JSONField(name = "MODIFIERENAME")
    private String modifierename;
}