package com.jackrain.nea.st.model.result;

import com.jackrain.nea.st.model.table.StCShopStrategyLogisticsItem;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @Description: 策略查询返回结果基础类
 * @author: 江家雷
 * @since: 2020/8/17
 * create at : 2020/8/17 16:37
 */
@Data
public class StCShopStrategyLogisticsItemResult implements Serializable {

    private Integer logisticsType;
    private List<StCShopStrategyLogisticsItem> logisticsItemList;
}