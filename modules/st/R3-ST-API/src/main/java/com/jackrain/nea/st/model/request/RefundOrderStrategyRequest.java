package com.jackrain.nea.st.model.request;

import com.alibaba.fastjson.annotation.JSONField;
import com.jackrain.nea.st.model.table.StCRefundOrderStrategyDO;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 退单审核策略请求实体
 *
 * <AUTHOR>
 * @Date 2019/3/11 13:35
 */
@Data
public class RefundOrderStrategyRequest  implements Serializable {
    @JSONField(name = "ST_C_REFUND_ORDER_STRATEGY")
    private StCRefundOrderStrategyDO stCRefundOrderStrategy;
    private Long objId;
}
