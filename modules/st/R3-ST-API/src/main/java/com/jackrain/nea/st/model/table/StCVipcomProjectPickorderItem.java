package com.jackrain.nea.st.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jackrain.nea.st.model.base.SubBaseModel;
import lombok.Data;

import java.util.Date;

@TableName(value = "st_c_vipcom_project_pickorder_item")
@Data
public class StCVipcomProjectPickorderItem extends SubBaseModel {
    @JSONField(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    @JSONField(name = "ST_C_VIPCOM_PROJECT_ID")
    private Long stCVipcomProjectId;

    @JSONField(name = "CREATE_TIME")
    private String createTime;

    @JSONField(name = "PICKORDER_CREATE_TYPE")
    private String pickorderCreateType;

    @JSONField(name = "CP_C_ORIG_ENAME")
    private String cpCOrigEname;

    @JSONField(name = "PICKORDER_TYPE")
    private String pickorderType;

    @JSONField(name = "UN_PICK_NUM_DIMENSION")
    private String unPickNumDimension;

    @JSONField(name = "PEAK_VALUE")
    private Integer peakValue;

    @JSONField(name = "AUTO_PICK_INTERVAL_TIME")
    private String autoPickIntervalTime;

    @JSONField(name = "OWNERENAME")
    private String ownerename;

    @JSONField(name = "MODIFIERENAME")
    private String modifierename;

    @JSONField(name = "CP_C_ORIG_ID")
    private String cpCOrigId;

    @JSONField(name = "CP_C_ORIG_ECODE")
    private String cpCOrigEcode;
}