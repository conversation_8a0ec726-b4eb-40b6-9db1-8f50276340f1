package com.jackrain.nea.st.api;

import com.jackrain.nea.ac.model.request.AcLogisticsFeeReCaculationRequest;
import com.jackrain.nea.ac.model.result.AcLogisticsFeeCaculationResult;
import com.jackrain.nea.st.model.request.StCExpressCostPoi;
import com.jackrain.nea.st.model.request.StCExpressPriceStrategyPoi;
import com.jackrain.nea.st.model.result.StImportErrorMsgResult;
import com.jackrain.nea.st.request.ReCaculateLogisticsFeeRequest;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/8/8 16:42
 * @Description
 */
public interface StCExpressCostImportAndExportCmd {

    /**
     * 导入快运报价设置
     * @param importList
     * @param user
     * @return
     */
    ValueHolderV14<List<StImportErrorMsgResult>> importExpressCost(List<StCExpressCostPoi> importList, User user);


    /**
     * 导入错误信息下载
     * @param user
     * @param errMsgList
     * @return
     */
    String downloadImportErrMsg(User user,List<StImportErrorMsgResult> errMsgList);


    ValueHolderV14<String> exportExpressCost(String ids,User user);

    /**
     * 获取重新计算参数
     * @param request
     * @param user
     * @return
     */
    ValueHolderV14<AcLogisticsFeeReCaculationRequest> queryFeeChecklistCount(ReCaculateLogisticsFeeRequest request, User user);

    /**
     * 重新计算
     * @param request
     * @return
     */
    ValueHolderV14<AcLogisticsFeeCaculationResult> reCaculateLogisticsFee(AcLogisticsFeeReCaculationRequest request);
}
