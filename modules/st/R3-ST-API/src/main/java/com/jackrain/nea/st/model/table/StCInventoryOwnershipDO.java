package com.jackrain.nea.st.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.*;
import com.jackrain.nea.st.model.base.SubBaseModel;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@TableName(value = "st_c_inventory_ownership")
@Data
public class StCInventoryOwnershipDO extends SubBaseModel {
    @JSONField(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    @J<PERSON>NField(name = "SCHEMA_NAME")
    private String schemaName;

    @J<PERSON><PERSON>ield(name = "BRAND")
    private Long brand;

    @JSONField(name = "CP_C_SHOP_ID")
    private Long cpCShopId;

    @J<PERSON>NField(name = "CP_C_SHOP_ECODE")
    private String cpCShopEcode;

    @J<PERSON>NField(name = "CP_C_SHOP_TITLE")
    private String cpCShopTitle;

    @JSONField(name = "MATERIAL_TYPE")
    private String materialType;

    @JSONField(name = "INTERNET_SHOP_OWNERSHIP")
    private BigDecimal internetShopOwnership;

    @JSONField(name = "WEITHER_INVENTORY_OWNERSHIP")
    private String weitherInventoryOwnership;

    @JSONField(name = "REMARK")
    private String remark;

    @JSONField(name = "OWNERENAME")
    private String ownerename;

    @JSONField(name = "MODIFIERENAME")
    private String modifierename;

    @JSONField(name = "DELID")
    private Long delid;

    @JSONField(name = "DELENAME")
    private String delename;

    @JSONField(name = "DELNAME")
    private String delname;

    @JSONField(name = "DEL_TIME")
    private Date delTime;

//    @JSONField(name = "CP_C_SHOP_CHANNEL_TYPE")
//    private Long cpCShopChannelType;
}