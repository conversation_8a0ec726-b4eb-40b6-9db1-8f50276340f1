package com.jackrain.nea.st.model.request;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;

/**
 * @description 唯品会日程规划明细及归属部分信息
 * @author:xtt
 * @since: 2020/12/13
 */
@Data
public class VipProjectItemAscriptionRequest implements Serializable {

    @JSONField(name = "ID")
    private Long id;
    /**
     * 日程归属编码
     */
    @JSONField(name = "ECODE")
    private String eCode;
    /**
     * 日程归属名称
     */
    @JSONField(name = "ENAME")
    private String eName;
    /**
     * 配送方式名称
     */
    @JSONField(name = "DELIVERY_METHOD")
    private Integer deliveryMethod;
    /**
     * 配送方式名称
     */
    @JSONField(name = "DELIVERY_METHOD_NAME")
    private String deliveryMethodName;
    /**
     * 下单时间
     */
    @JSONField(name = "DOWNLOAD_TIME")
    private String downloadTime;
    /**
     * 是否选中 0不选中；1选中；
     */
    @JSONField(name = "SELECTED")
    private Integer selected;
}
