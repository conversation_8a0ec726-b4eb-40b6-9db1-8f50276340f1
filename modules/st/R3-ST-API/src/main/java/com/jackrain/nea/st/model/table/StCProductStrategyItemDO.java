package com.jackrain.nea.st.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.*;
import com.jackrain.nea.es.annotations.Document;
import com.jackrain.nea.es.annotations.Field;
import com.jackrain.nea.es.constans.FieldType;
import com.jackrain.nea.st.model.base.SubBaseModel;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@TableName(value = "st_c_product_strategy_item")
@Data
@Document(index = "st_c_product_strategy",type = "st_c_product_strategy_item")
public class StCProductStrategyItemDO extends SubBaseModel {
    @JSONField(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "id", type = IdType.INPUT)
    @Field(type = FieldType.Long)
    private Long id;
    
    @JSONField(name = "ST_C_PRODUCT_STRATEGY_ID")
    @Field(type = FieldType.Long)
    private Long stCProductStrategyId;
    /**
     * 店铺id
     */
    @JSONField(name = "CP_C_SHOP_ID")
    @Field(type = FieldType.Long)
    private Long cpCShopId;
    /**
     * 店铺编码
     */
    @JSONField(name = "CP_C_SHOP_ECODE")
    @Field(type = FieldType.Keyword)
    private String cpCShopEcode;
    /**
     * 店铺名称
     */
    @JSONField(name = "CP_C_SHOP_TITLE")
    @Field(type = FieldType.Keyword)
    private String cpCShopTitle;
    /**
     * 平台条码id
     */
    @JSONField(name = "PT_SKU_ID")
    @Field(type = FieldType.Keyword)
    private String ptSkuId;
    /**
     * 平台商品id
     */
    @JSONField(name = "PT_PRO_ID")
    @Field(type = FieldType.Keyword)
    private String ptProId;
    /**
     * 条码id
     */
    @JSONField(name = "PS_C_SKU_ID")
    @Field(type = FieldType.Long)
    private Long psCSkuId;
    /**
     * 条码编码
     */
    @JSONField(name = "PS_C_SKU_ECODE")
    @Field(type = FieldType.Keyword)
    private String psCSkuEcode;
    /**
     * 商品id
     */
    @JSONField(name = "PS_C_PRO_ID")
    @Field(type = FieldType.Long)
    private Long psCProId;
    /**
     * 商品编码
     */
    @JSONField(name = "PS_C_PRO_ECODE")
    @Field(type = FieldType.Keyword)
    private String psCProEcode;
    /**
     * 商品名称
     */
    @JSONField(name = "PS_C_PRO_ENAME")
    @Field(type = FieldType.Keyword)
    private String psCProEname;
    /**
     * 库存比例
     */
    @JSONField(name = "STOCK_SCALE")
    @Field(type = FieldType.Double)
    private BigDecimal stockScale;
    /**
     * 低库存数
     */
    @JSONField(name = "LOW_STOCK")
    @Field(type = FieldType.Long)
    private Long lowStock;

    @JSONField(name = "OWNERENAME")
    @Field(type = FieldType.Keyword)
    private String ownerename;

    @JSONField(name = "MODIFIERENAME")
    @Field(type = FieldType.Keyword)
    private String modifierename;
    /**
     * 渠道仓id
     */
    @JSONField(name = "CP_C_ORG_CHANNEL_ID")
    @Field(type = FieldType.Long)
    private Long cpCOrgChannelId;
    /**
     * 渠道仓编码
     */
    @JSONField(name = "CP_C_ORG_CHANNEL_ECODE")
    @Field(type = FieldType.Keyword)
    private String cpCOrgChannelEcode;
    /**
     * 渠道仓名称
     */
    @JSONField(name = "CP_C_ORG_CHANNEL_ENAME")
    @Field(type = FieldType.Keyword)
    private String cpCOrgChannelEname;
    /**
     * 修改前比例
     */
    @JSONField(name = "before_update_stock_rate")
    @Field(type = FieldType.Keyword)
    private BigDecimal beforeUpdateStockRate;
    /**
     * 主表状态
     */
    @JSONField(name = "STATUS")
    @Field(type = FieldType.Integer)
    private Integer status;
    /**
     * 主表方案名称
     */
    @JSONField(name = "PLAN_NAME")
    @Field(type = FieldType.Keyword)
    private String planName;
    /**
     * 主表开始时间
     */
    @JSONField(name = "BEGIN_TIME")
    @Field(type = FieldType.Date)
    private Date beginTime;
    /**
     * 主表结束时间
     */
    @JSONField(name = "END_TIME")
    @Field(type = FieldType.Date)
    private Date endTime;
    /**
     * 主表创建时间
     */
    @JSONField(name = "MAIN_CREATIONDATE")
    @Field(type = FieldType.Date)
    private Date mainCreationdate;
}