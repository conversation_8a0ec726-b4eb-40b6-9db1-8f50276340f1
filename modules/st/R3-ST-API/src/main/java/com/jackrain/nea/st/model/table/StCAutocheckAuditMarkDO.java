package com.jackrain.nea.st.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.*;
import com.jackrain.nea.st.model.base.SubBaseModel;
import lombok.Data;

@TableName(value = "ST_C_AUTOCHECK_AUDIT_MARK")
@Data
public class StCAutocheckAuditMarkDO extends SubBaseModel {
    @JSONField(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    @JSONField(name = "ST_C_AUTOCHECK_ID")
    private Long stCAutocheckId;

    @JSO<PERSON>ield(name = "MARK_WAIT_TIME")
    private Long markWaitTime;

    @JSONField(name = "ORDER_TAG_ID")
    private Long orderTagId;

    @J<PERSON>NField(name = "ORDER_TAG_CODE")
    private String orderTagCode;

    @JSONField(name = "ORDER_TAG_ENAME")
    private String orderTagEname;

    @J<PERSON><PERSON>ield(name = "ISACTIVE")
    private String ISACTIVE;
}