package com.jackrain.nea.st.model.request;

import com.alibaba.fastjson.annotation.JSONField;
import com.jackrain.nea.st.model.table.*;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 物流方案
 *
 * <AUTHOR>
 * @Date 2019/3/10 21:26
 */
@Data
public class ExpressRequest  implements Serializable {
    @J<PERSON><PERSON>ield(name = "ST_C_EXPRESS")
    private StCExpressDO stCExpress;
    @J<PERSON><PERSON>ield(name = "ST_C_EXPRESS_PRO_ITEM")
    private List<StCExpressProItemDO> stCExpressProItemList;
    @J<PERSON><PERSON>ield(name = "ST_C_EXPRESS_PLAN_AREA_ITEM")
    private List<StCExpressPlanAreaItemDO> stCExpressAreaItemList;
    @JSONField(name = "ST_C_EXPRESS_PACKAGE")
    private List<StCExpressPackageDO> stCExpressPackageList;
    private Long objId;
}
