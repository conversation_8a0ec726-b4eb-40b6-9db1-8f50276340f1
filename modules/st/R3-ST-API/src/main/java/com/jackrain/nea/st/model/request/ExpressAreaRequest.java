package com.jackrain.nea.st.model.request;

import com.alibaba.fastjson.annotation.JSONField;
import com.jackrain.nea.st.model.table.StCEwaybillDO;
import com.jackrain.nea.st.model.table.StCEwaybillLogisticsDO;
import com.jackrain.nea.st.model.table.StCExpressAreaDO;
import com.jackrain.nea.st.model.table.StCExpressAreaItemDO;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * Descroption 物流区域设置
 *
 * <AUTHOR>
 * @Date 2019/3/13 14:23
 */
@Data
public class ExpressAreaRequest implements Serializable {

    @JSONField(name = "ST_C_EXPRESS_AREA")
    private StCExpressAreaDO stCExpressArea;
    @JSONField(name = "ST_C_EXPRESS_AREA_ITEM")
    private List<StCExpressAreaItemDO> stCExpressAreaItemList;

}
