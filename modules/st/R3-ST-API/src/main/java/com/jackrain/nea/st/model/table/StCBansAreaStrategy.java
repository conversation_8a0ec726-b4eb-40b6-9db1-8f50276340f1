package com.jackrain.nea.st.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.*;
import com.jackrain.nea.st.model.base.SubBaseModel;
import lombok.Data;

/**
 * 物流禁发区域
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-06-10 15:05:00
 */
@TableName(value = "st_c_bans_area_strategy")
@Data
public class StCBansAreaStrategy extends SubBaseModel {
    /**
     * ID
     */
    @JSONField(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;
    /**
     * 物流公司
     */
    @JSONField(name = "CP_C_LOGISTICS_ID")
    private Long cpCLogisticsId;

    /**
     * 仓库
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    @JSONField(name = "CP_C_PHY_WAREHOUSE_ID")
    private Long cpCPhyWarehouseId;

    /**
     * 省份
     */
    @JSONField(name = "CP_C_PROVINCE_ID")
    private Long cpCProvinceId;
    /**
     * 城市
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    @JSONField(name = "CP_C_CITY_ID")
    private Long cpCCityId;
    /**
     * 区/县
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    @JSONField(name = "CP_C_REGION_AREA_ID")
    private Long cpCRegionAreaId;
    /**
     * 备注
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    @JSONField(name = "REMARK")
    private String remark;
    /**
     * 版本号
     */
    @JSONField(name = "VERSION")
    private Long version;
    /**
     * 创建人姓名
     */
    @JSONField(name = "OWNERENAME")
    private String ownerename;
    /**
     * 修改人姓名
     */
    @JSONField(name = "MODIFIERENAME")
    private String modifierename;

}
