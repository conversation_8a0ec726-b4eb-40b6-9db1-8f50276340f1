package com.jackrain.nea.st.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.*;
import com.jackrain.nea.st.model.base.SubBaseModel;
import lombok.Data;

import java.math.BigDecimal;

@TableName(value = "st_c_postfee_item")
@Data
public class StCPostfeeItemDO extends SubBaseModel {
    @JSONField(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    @JSONField(name = "ST_C_POSTFEE_ID")
    private Long stCPostfeeId;

    @J<PERSON><PERSON>ield(name = "CP_C_LOGISTICS_ID")
    private Long cpCLogisticsId;

    @JSONField(name = "CP_C_LOGISTICS_ECODE")
    private String cpCLogisticsEcode;

    @JSONField(name = "CP_C_LOGISTICS_ENAME")
    private String cpCLogisticsEname;

    @JSONField(name = "ETYPE")
    private String etype;

    @J<PERSON><PERSON><PERSON>(name = "CP_C_REGION_PROVINCE_ID")
    private Long cpCRegionProvinceId;

    @JSONField(name = "CP_C_REGION_PROVINCE_ECODE")
    private String cpCRegionProvinceEcode;

    @JSONField(name = "CP_C_REGION_PROVINCE_ENAME")
    private String cpCRegionProvinceEname;

    @JSONField(name = "CP_C_REGION_CITY_ID")
    private Long cpCRegionCityId;

    @JSONField(name = "CP_C_REGION_CITY_ECODE")
    private String cpCRegionCityEcode;

    @JSONField(name = "CP_C_REGION_CITY_ENAME")
    private String cpCRegionCityEname;

    @JSONField(name = "CP_C_REGION_AREA_ID")
    private Long cpCRegionAreaId;

    @JSONField(name = "CP_C_REGION_AREA_ECODE")
    private String cpCRegionAreaEcode;

    @JSONField(name = "CP_C_REGION_AREA_ENAME")
    private String cpCRegionAreaEname;

    @JSONField(name = "AREA_RANK")
    private Long areaRank;

    @JSONField(name = "FIRSTHEAVY")
    private BigDecimal firstheavy;

    @JSONField(name = "FIRSTFEE")
    private BigDecimal firstfee;

    @JSONField(name = "SECONDHEAVY")
    private BigDecimal secondheavy;

    @JSONField(name = "SECONDFEE")
    private BigDecimal secondfee;

    @JSONField(name = "THIRDHEAVY")
    private BigDecimal thirdheavy;

    @JSONField(name = "THIRDFEE")
    private BigDecimal thirdfee;

    @JSONField(name = "CONTINUEDHEAVY")
    private BigDecimal continuedheavy;

    @JSONField(name = "CONTINUEDFEE")
    private BigDecimal continuedfee;

    @JSONField(name = "WEIGHT")
    private BigDecimal weight;

    @JSONField(name = "ADD_WEIGHT")
    private BigDecimal addWeight;

    @JSONField(name = "ADD_FEE")
    private BigDecimal addFee;

    @JSONField(name = "OWNERENAME")
    private String ownerename;

    @JSONField(name = "MODIFIERENAME")
    private String modifierename;
}