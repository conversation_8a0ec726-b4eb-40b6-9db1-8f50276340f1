package com.jackrain.nea.st.model.result;

import com.jackrain.nea.st.model.table.StCWarehouseLogisticStrategy;
import com.jackrain.nea.st.model.table.StCWarehouseLogisticStrategyItem;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> lin yu
 * @date : 2022/8/1 下午5:35
 * @describe :
 */
@Data
public class StCWarehouseLogisticStrategyQueryResult implements Serializable {

    private List<StCWarehouseLogisticStrategy> logisticStrategiesList;

    private Map<Long, List<StCWarehouseLogisticStrategyItem>> logisticStrategiesItemMap;

    private List<Long> logisticsIdList;
}
