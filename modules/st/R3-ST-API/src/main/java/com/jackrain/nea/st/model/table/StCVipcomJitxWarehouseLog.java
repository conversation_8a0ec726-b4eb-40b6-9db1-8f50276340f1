package com.jackrain.nea.st.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jackrain.nea.es.annotations.Document;
import com.jackrain.nea.es.annotations.Field;
import com.jackrain.nea.es.constans.FieldType;
import com.jackrain.nea.sys.domain.BaseModel;
import lombok.Data;

@TableName(value = "st_c_vipcom_jitx_warehouse_log")
@Data
@Document(index = "st_c_vipcom_jitx_warehouse_log", type = "st_c_vipcom_jitx_warehouse_log")
public class StCVipcomJitxWarehouseLog extends BaseModel {
    @JSONField(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "id", type = IdType.INPUT)
    @Field(type = FieldType.Long)
    private Long id;

    @JSONField(name = "MODIFY_CONTENT")
    @Field(type = FieldType.Keyword)
    private String modifyContent;

    @JSONField(name = "MODIFY_BEFORE")
    @Field(type = FieldType.Keyword)
    private String modifyBefore;

    @JSONField(name = "MODIFY_AFTER")
    @Field(type = FieldType.Keyword)
    private String modifyAfter;

    @JSONField(name = "OWNERENAME")
    @Field(type = FieldType.Keyword)
    private String ownerename;

    @JSONField(name = "MODIFIERENAME")
    @Field(type = FieldType.Keyword)
    private String modifierename;

    @JSONField(name = "OUT_ID")
    @Field(type = FieldType.Keyword)
    private Long outId;
}