package com.jackrain.nea.st.model.result;

import com.alibaba.fastjson.annotation.JSONField;
import com.jackrain.nea.st.model.table.StCChannelStrategyItemDO;
import lombok.Data;

import java.io.Serializable;

/**
 * 即将要废弃的功能
 * @author: 汪聿森
 * @Date: Created in 2020-05-21 10:06
 * @Description : ${description}
 */
@Data
@Deprecated
public class StCChannelStrategyResult extends StCChannelStrategyItemDO implements Serializable {
    @JSONField(name = "AREATYPE")
    private Long areatype;
}
