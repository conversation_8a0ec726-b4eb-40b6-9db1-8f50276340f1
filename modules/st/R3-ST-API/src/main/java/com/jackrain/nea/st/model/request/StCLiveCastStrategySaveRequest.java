package com.jackrain.nea.st.model.request;

import com.alibaba.fastjson.annotation.JSONField;
import com.jackrain.nea.st.model.table.StCLiveCastStrategyDO;
import com.jackrain.nea.st.model.table.StCLiveCastStrategyItemDO;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * controller 保存入参
 */
@Data
public class StCLiveCastStrategySaveRequest implements Serializable {

    // 直播解析策略
    @JSONField(name = "ST_C_LIVE_CAST_STRATEGY")
    private StCLiveCastStrategyDO liveCastStrategyDO;

    // 策略明细列表
    @JSONField(name = "ST_C_LIVE_CAST_STRATEGY_ITEM")
    private List<StCLiveCastStrategyItemDO> liveCastStrategyItemDOList;
}
