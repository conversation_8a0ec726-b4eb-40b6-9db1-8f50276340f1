package com.jackrain.nea.st.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.*;
import com.jackrain.nea.es.annotations.Document;
import com.jackrain.nea.es.annotations.Field;
import com.jackrain.nea.es.constans.FieldType;
import com.jackrain.nea.st.model.base.SubBaseModel;
import lombok.Data;

/**
 * <AUTHOR>
 * @title $
 * @Description 描述
 * @Date 2020/06/10
 * @Copyright 2019-2020
 */
@TableName(value = "ST_C_WAREHOUSE_BRAND")
@Data
@Document(index = "st_c_warehouse",type = "st_c_warehouse_brand")
public class StCWarehouseBrandDO extends SubBaseModel {
    @JSONField(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "id", type = IdType.INPUT)
    @Field(type = FieldType.Long)
    private Long id;

    @JSONField(name = "ST_C_WAREHOUSE_ID")
    @Field(type = FieldType.Long)
    private Long stCWarehouseId;

    @JSONField(name = "BRAND_GROUP")
    @Field(type = FieldType.Keyword)
    private String brandGroup;

    @JSONField(name = "BRAND_ID")
    @Field(type = FieldType.Keyword)
    private String brandId;

    @JSONField(name = "BRAND_NAME")
    @Field(type = FieldType.Keyword)
    private String brandName;

}
