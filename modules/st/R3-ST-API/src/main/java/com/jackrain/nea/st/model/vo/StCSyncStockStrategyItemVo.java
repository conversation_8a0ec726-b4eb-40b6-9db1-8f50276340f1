package com.jackrain.nea.st.model.vo;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @author: 陈俊明
 * @since: 2019-07-08
 * @create at : 2019-07-08 9:51
 */
@Data
public class StCSyncStockStrategyItemVo implements Serializable {

    @J<PERSON>NField(name = "PRIORITY")
    private Integer priority;

    @JSONField(name = "RATE")
    private BigDecimal rate;

    @JSONField(name = "ISACTIVE")
    private String isactive;

    @JSONField(name = "CP_C_STORE_ID")
    private String cpCStoreId;

    @JSONField(name = "LOW_STOCK")
    private Long lowStock;
}
