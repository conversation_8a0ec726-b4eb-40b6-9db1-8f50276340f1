package com.jackrain.nea.st.model.common;

import java.util.HashMap;
import java.util.Map;

/**
 * @Descroption
 * <AUTHOR>
 * @Date 2020/9/2
 */
public interface StCMsgConstants {

    /**
     * 是否发送短信
     */
    Map<String,String> SENDMAP = new HashMap(6){{
       put("Y","是");
       put("y","是");
       put("N","否");
       put("n","否");
       put(null,"N/A");
       put("","N/A");
    }};

    /**
     * 通知类型
     */
    Map<String,String> ADVICEMAP = new HashMap(4){{
        put(null,"N/A");
        put("","N/A");
        put("1","物流发货提醒");
        put("2","退换货提醒");
    }};

    /**
     * 任务节点
     */
    Map<String,String> TASKMAP = new HashMap(6){{
        put(null,"N/A");
        put("","N/A");
        put("1","非平台拆分订单完成仓库发货");
        put("2","非天猫换货订单完成仓库发货");
        put("3","退换货完成入库");
        put("4","无名件完成入库");
    }};
}
