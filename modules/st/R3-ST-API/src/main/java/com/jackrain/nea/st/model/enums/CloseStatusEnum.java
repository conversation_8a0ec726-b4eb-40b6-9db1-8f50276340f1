package com.jackrain.nea.st.model.enums;

import lombok.Getter;

import java.util.Objects;

/**
 * <AUTHOR>
 * @Date 2022/6/20 15:07
 */
public enum CloseStatusEnum {

    NO_CLOSE(0, "未结案"),
    CLOSE(1,"已结案");


    @Getter
    private Integer key;
    @Getter
    private String desc;

    CloseStatusEnum(Integer key, String desc) {
        this.key = key;
        this.desc = desc;
    }

    public static CloseStatusEnum getByKey(Integer key) {
        for (CloseStatusEnum current : values()) {
            if (Objects.equals(current.getKey(), key)) {
                return current;
            }
        }
        return null;
    }
}
