package com.jackrain.nea.st.model.request;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 店铺库存同步策略查询请求对象
 *
 * <AUTHOR>
 * @since 2020-08-18
 * create at : 2020-08-18 23:03
 */
@Data
public class SyncStockStrategyQueryRequest implements Serializable {

    /**
     * 策略id集合
     */
    private List<Long> ids;
    /**
     * 店铺编码集合
     */
    private List<String> shopCodes;
    /**
     * 店铺ID集合
     */
    private List<Long> shopIds;
    /**
     * 是否可用
     */
    private String isactive;
}
