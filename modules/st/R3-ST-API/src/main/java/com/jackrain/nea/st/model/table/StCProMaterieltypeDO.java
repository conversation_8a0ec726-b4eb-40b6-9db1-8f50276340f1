package com.jackrain.nea.st.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.*;
import com.jackrain.nea.st.model.base.SubBaseModel;
import lombok.Data;

@TableName(value = "st_c_pro_materieltype")
@Data
public class StCProMaterieltypeDO extends SubBaseModel {
    @JSONField(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    @J<PERSON>NField(name = "ECODE")
    private String ecode;

    @J<PERSON><PERSON>ield(name = "ENAME")
    private String ename;

    @J<PERSON><PERSON>ield(name = "REMAR<PERSON>")
    private String remark;

    @J<PERSON><PERSON>ield(name = "<PERSON>WNERENAME")
    private String ownerename;

    @JSONField(name = "MODIFIERENAME")
    private String modifierename;
}