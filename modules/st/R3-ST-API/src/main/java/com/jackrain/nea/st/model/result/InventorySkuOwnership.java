package com.jackrain.nea.st.model.result;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version :1.0
 * description ： 库存归属策略服务model
 * @date :2019/9/24 10:30
 */
@Data
public class InventorySkuOwnership implements Serializable {

    /**
     * 条码
     */
    private Long psCSkuId;

    /**
     * 品牌
     */
    private Long psCBrandId;


     /**
     * 物料类型
     */
    private String materialType;

    /**
     * 渠道类型
     */
    private Long cpCShopChannelType;

    /**
     * 网店库存归属数量
     */
    private BigDecimal ownershipNum;

    /**
     * 库存归属店铺
     */
    private Long cpCShopId;
}
