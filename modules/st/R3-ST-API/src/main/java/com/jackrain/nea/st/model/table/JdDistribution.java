package com.jackrain.nea.st.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.*;
import com.jackrain.nea.es.annotations.Document;
import com.jackrain.nea.es.annotations.Field;
import com.jackrain.nea.es.constans.FieldType;
import com.jackrain.nea.sys.domain.BaseModel;
import lombok.Data;

/**
 * @ClassName JdDistribution
 * @Description 京东分销
 * <AUTHOR>
 * @Date 2022/12/8 19:37
 * @Version 1.0
 */
@TableName(value = "jd_distribution")
@Data
@Document(index = "jd_distribution", type = "jd_distribution")
public class JdDistribution extends BaseModel {
    private static final long serialVersionUID = 3747631115171661863L;

    @JSONField(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "id", type = IdType.INPUT)
    @Field(type = FieldType.Long)
    private Long id;

    /**
     * 分销商id
     */
    @JSONField(name = "CP_C_DISTRIBUTION_ID")
    @Field(type = FieldType.Keyword)
    private String cpCDistributionId;

    /**
     * 分销商名称
     */
    @JSONField(name = "CP_C_DISTRIBUTION_ENAME")
    @Field(type = FieldType.Keyword)
    private String cpCDistributionEname;

    /**
     * 供应商店铺id
     */
    @JSONField(name = "CP_C_SHOP_ID")
    @Field(type = FieldType.Long)
    private Long cpCShopId;

    /**
     * 供应商店铺编码
     */
    @JSONField(name = "CP_C_SHOP_ECODE")
    @Field(type = FieldType.Keyword)
    private String cpCShopEcode;

    /**
     * 供应商店铺名称
     */
    @JSONField(name = "CP_C_SHOP_ENAME")
    @Field(type = FieldType.Keyword)
    private String cpCShopEname;
}
