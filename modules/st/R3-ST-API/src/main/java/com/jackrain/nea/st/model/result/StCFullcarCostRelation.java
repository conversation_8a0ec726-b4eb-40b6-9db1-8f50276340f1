package com.jackrain.nea.st.model.result;

import com.jackrain.nea.st.model.table.StCFullcarCost;
import com.jackrain.nea.st.model.table.StCFullcarCostItem;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 整车报价关系
 *
 * <AUTHOR>
 * @Date 2025/7/15
 * @Description 整车报价设置关系表
 * @Version 1.0
 */
@Data
public class StCFullcarCostRelation implements Serializable {

    /**
     * 整车报价主表
     */
    private StCFullcarCost stCFullcarCost;

    /**
     * 整车报价明细
     */
    private List<StCFullcarCostItem> stCFullcarCostItemList;
}
