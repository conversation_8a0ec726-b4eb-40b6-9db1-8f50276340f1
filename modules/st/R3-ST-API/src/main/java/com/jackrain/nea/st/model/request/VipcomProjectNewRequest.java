package com.jackrain.nea.st.model.request;

import com.alibaba.fastjson.annotation.JSONField;
import com.jackrain.nea.st.model.table.StCVipcomProjectDO;
import com.jackrain.nea.st.model.table.StCVipcomProjectPickorderItem;
import com.jackrain.nea.st.model.table.StCVipcomProjectWhEntryItem;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 档期日程规划实体类
 *
 * <AUTHOR>
 * @Date 2021/05/27 10:26
 */
@Data
public class VipcomProjectNewRequest implements Serializable {

    @J<PERSON>NField(name = "ST_C_VIPCOM_PROJECT")
    private StCVipcomProjectDO stCVipcomProject;
    @JSONField(name = "ST_C_VIPCOM_PROJECT_PICKORDER_ITEM")
    private List<StCVipcomProjectPickorderItem> stCVipcomProjectPickorderItemList;
    @JSONField(name = "ST_C_VIPCOM_PROJECT_WAREHOUSE_ENTRY_ITEM")
    private List<StCVipcomProjectWhEntryItem> stCVipcomProjectWhEntryItemList;

}
