package com.jackrain.nea.st.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.*;
import com.jackrain.nea.st.model.base.SubBaseModel;
import lombok.Data;

import java.util.Date;

@TableName(value = "st_c_vipcom_stocklack")
@Data
public class StCVipcomStocklackDO extends SubBaseModel {
    @JSONField(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    @JSONField(name = "ETYPE")
    private Integer etype;

    @JSONField(name = "PO_ID")
    private String poId;

    @J<PERSON><PERSON>ield(name = "DISTRIBUTION_ID")
    private String distributionId;

    @JSONField(name = "RESULT")
    private String result;

    @JSONField(name = "REMARK")
    private String remark;

    @J<PERSON>NField(name = "OWNERENAME")
    private String ownerename;

    @J<PERSON>NField(name = "MODIFIERENAME")
    private String modifierename;

    @J<PERSON><PERSON><PERSON>(name = "DELID")
    private Long delid;

    @J<PERSON><PERSON><PERSON>(name = "DE<PERSON>NAME")
    private String delename;

    @JSONField(name = "DELNAME")
    private String delname;

    @JSONField(name = "DEL_TIME")
    private Date delTime;
}