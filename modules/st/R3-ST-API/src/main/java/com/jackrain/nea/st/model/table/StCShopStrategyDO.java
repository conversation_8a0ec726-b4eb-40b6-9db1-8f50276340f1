package com.jackrain.nea.st.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jackrain.nea.st.model.base.SubBaseModel;
import lombok.Data;

import java.math.BigDecimal;

@TableName(value = "st_c_shop_strategy")
@Data
public class StCShopStrategyDO extends SubBaseModel {
    private static final long serialVersionUID = 1187601656566047180L;
    @JSONField(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    @JSONField(name = "CP_C_SHOP_ID")
    private Long cpCShopId;

    @JSONField(name = "CP_C_SHOP_TITLE")
    private String cpCShopTitle;

    @JSONField(name = "POSTAGE_CODE")
    private String postageCode;

    @JSONField(name = "DEFAULT_STORE_ID")
    private Long defaultStoreId;

    @JSONField(name = "IS_AG")
    private String isAg;

    @JSONField(name = "IS_SPLIT")
    private String isSplit;

    @JSONField(name = "IS_AUTO_SPLIT")
    private String isAutoSplit;

    @JSONField(name = "IS_AUTO_MATCH")
    private String isAutoMatch;

    @JSONField(name = "IS_AUTO_AUDIT")
    private String isAutoAudit;

    @JSONField(name = "OWNERENAME")
    private String ownerename;

    @JSONField(name = "MODIFIERENAME")
    private String modifierename;

    @JSONField(name = "IS_WRITEOFF")
    private Integer isWriteoff;
    /**
     * lijp add 是否强制发货
     */
    @JSONField(name = "IS_FORCE_SEND")
    private Integer isForceSend;

    @JSONField(name = "IS_ENABLE_UNLOCK")
    private String isEnableUnlock;

    @JSONField(name = "LOCK_DAYS")
    private Long lockDays;

    @JSONField(name = "IS_PLATFORM_SPLIT")
    private String isPlatformSplit;

    @JSONField(name = "IS_ORDER_LOAD")
    private String isOrderLoad;

    // merge ref ys
    @JSONField(name = "IS_JUDGE_STOCK")
    private String isJudgeStock;

    @JSONField(name = "IS_FULLY_GOODS")
    private String isFullyGoods;

    @JSONField(name = "PRICE_FIX")
    private BigDecimal priceFix;

    @JSONField(name = "IS_FBT_CONTROL")
    private Integer isFbtControl;

    @JSONField(name = "PRICE_ACTUAL_MIN")
    private BigDecimal priceActualMin;

    @JSONField(name = "CP_C_WAREHOUSE_DEF_ID")
    private Long cpCWarehouseDefId;

    @JSONField(name = "CP_C_WAREHOUSE_DEF_NAME")
    private String cpCWarehouseDefName;

    @JSONField(name = "CP_C_WAREHOUSE_DEF_CODE")
    private String cpCWarehouseDefCode;

    @JSONField(name = "IS_MULTI_RETURN_WAREHOUSE")
    private Integer isMultiReturnWarehouse;

    /**
     * pos门店发货实体仓
     */
    @JSONField(name = "POS_SEND_PHY_WAREHOUSE_ID")
    private Long posSendPhyWarehouseId;

    /**
     * 同城购库存同步比例（0-100）
     */
    @JSONField(name = "SAME_CITY_STORAGE_RATE")
    private BigDecimal sameCityStorageRate;


    @JSONField(name = "CP_C_WAREHOUSE_EXCHANGE_ID")
    private Long cpCWarehouseExchangeId;

    @JSONField(name = "CP_C_WAREHOUSE_EXCHANGE_NAME")
    private String cpCWarehouseExchangeName;


    @JSONField(name = "CP_C_WAREHOUSE_EXCHANGE_CODE")
    private String cpCWarehouseExchangeCode;


    @JSONField(name = "IS_DETENTION_SPLIT")
    private String isDetentionSplit;

    @JSONField(name = "IS_ADVANCE_SPLIT")
    private String isAdvanceSplit;

    @JSONField(name = "IS_MANUALLY_CREATE")
    private String isManuallyCreate;

    @JSONField(name = "OCCUPY_TYPE")
    private String occupyType;

    @JSONField(name = "CANCEL_RETURN_ORDER")
    private String cancelReturnOrder;

    @JSONField(name = "IS_ORDER_TMS_TRACK")
    private String isOrderTmsTrack;

    @JSONField(name = "IS_RETURN_ORDER_TMS_TRACK")
    private String isReturnOrderTmsTrack;

    @JSONField(name = "AGREE_REFUND")
    private String agreeRefund;

    @JSONField(name = "REFUND_MONEY_LIMIT")
    private BigDecimal refundMoneyLimit;

    @JSONField(name = "FREEZE_SHOP_CAN_AUDIT")
    private String freezeShopCanAudit;

    @JSONField(name = "CAN_SPLIT")
    private String canSplit;

    @JSONField(name = "logistics_type")
    private Integer logisticsType;

    @JSONField(name = "IS_JD_FLOW_OCCUPY")
    private String isJdFlowOccupy;

    @JSONField(name = "ADDRESS_ID")
    private Long addressId;

    @JSONField(name = "IS_DELAY_TRANSFER")
    private String isDelayTransfer;

    @JSONField(name = "DELAY_TRANSFER_TIME")
    private Long delayTransferTime;

    /**
     * 是否自动拦截
     */
    @JSONField(name = "IS_AUTO_INTERCEPT")
    private String isAutoIntercept;

    /**
     * 订单不创建售后
     */
    @JSONField(name = "REFUND_NOT_FROM_ORDER")
    private String refundNotFromOrder;

    /**
     * 是否箱型拆单
     */
    @JSONField(name = "IS_BOX_SPLIT")
    private String isBoxSplit;

    /**
     * 是否自动退款
     */
    @JSONField(name = "IS_AUTO_REFUND")
    private String isAutoRefund;
}