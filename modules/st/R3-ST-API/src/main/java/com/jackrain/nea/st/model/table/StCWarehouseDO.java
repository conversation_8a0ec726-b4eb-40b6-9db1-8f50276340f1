package com.jackrain.nea.st.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.*;
import com.jackrain.nea.es.annotations.Document;
import com.jackrain.nea.es.annotations.Field;
import com.jackrain.nea.es.constans.FieldType;
import com.jackrain.nea.st.model.base.SubBaseModel;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @title $
 * @Description 描述
 * @Date 2020/06/10
 * @Copyright 2019-2020
 */
@TableName(value = "ST_C_WAREHOUSE")
@Data
@Document(index = "st_c_warehouse",type = "st_c_warehouse")
public class StCWarehouseDO extends SubBaseModel {
    @JSONField(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "id", type = IdType.INPUT)
    @Field(type = FieldType.Long)
    private Long id;

    @JSONField(name = "CP_C_PHY_WAREHOUSE_ID")
    @Field(type = FieldType.Long)
    private Long cpCPhyWarehouseId;

    @JSONField(name = "CP_C_PHY_WAREHOUSE_ECODE")
    @Field(type = FieldType.Keyword)
    private String cpCPhyWarehouseEcode;

    @JSONField(name = "CP_C_PHY_WAREHOUSE_ENAME")
    @Field(type = FieldType.Keyword)
    private String cpCPhyWarehouseEname;

    @JSONField(name = "IS_SEX_SPLIT")
    @Field(type = FieldType.Keyword)
    private String isSexSplit;

    @JSONField(name = "IS_BRAND_SPLIT")
    @Field(type = FieldType.Keyword)
    private String isBrandSplit;

    @JSONField(name = "IS_GOODS_SPLIT")
    @Field(type = FieldType.Keyword)
    private String isGoodsSplit;

    @JSONField(name = "IS_SKU_SPLIT")
    @Field(type = FieldType.Keyword)
    private String isSkuSplit;

    @JSONField(name = "IS_GOODS_CLASS_SPLIT")
    @Field(type = FieldType.Keyword)
    private String isGoodsClassSplit;

    @JSONField(name = "REMARK")
    @Field(type = FieldType.Keyword)
    private String remark;


}
