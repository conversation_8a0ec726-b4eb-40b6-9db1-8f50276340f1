package com.jackrain.nea.st.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jackrain.nea.st.model.base.SubBaseModel;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

@TableName(value = "st_c_distribution")
@Data
public class StCDistributionDO extends SubBaseModel {
    @JSONField(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    @JSONField(name = "BILL_NO")
    private String billNo;

    @JSONField(name = "ENAME")
    private String ename;

    @JSONField(name = "BILL_STATUS")
    private Integer billStatus;

    @JSONField(name = "BEGIN_TIME")
    private Date beginTime;

    @JSONField(name = "END_TIME")
    private Date endTime;

    @JSONField(name = "CP_C_CUSTOMER_ECODE")
    private String cpCCustomerEcode;

    @JSONField(name = "CP_C_CUSTOMER_ENAME")
    private String cpCCustomerEname;

    @JSONField(name = "SETTLEMENTTYPE")
    private Integer settlementtype;

    @JSONField(name = "FEESCALE")
    private BigDecimal feescale;

    @JSONField(name = "PLAN_DESC")
    private String planDesc;

    @JSONField(name = "OWNERENAME")
    private String ownerename;

    @JSONField(name = "FINISHID")
    private Long finishid;

    @JSONField(name = "FINISHENAME")
    private String finishename;

    @JSONField(name = "FINISHNAME")
    private String finishname;

    @JSONField(name = "FINISHTIME")
    private Date finishtime;

    @JSONField(name = "MODIFIERENAME")
    private String modifierename;

    @JSONField(name = "DELID")
    private Long delid;

    @JSONField(name = "DELENAME")
    private String delename;

    @JSONField(name = "DELNAME")
    private String delname;

    @JSONField(name = "DEL_TIME")
    private Date delTime;

    @JSONField(name = "CHECKID")
    private Long checkid;

    @JSONField(name = "CHECKENAME")
    private String checkename;

    @JSONField(name = "CHECKNAME")
    private String checkname;

    @JSONField(name = "CHECKTIME")
    private Date checktime;

    @JSONField(name = "SETTLE_TYPE")
    private Integer settleType;

    @JSONField(name = "AC_F_MANAGE_ID")
    private Long acFManageId;

    @JSONField(name = "AC_F_MANAGE_ECODE")
    private String acFManageEcode;

    @JSONField(name = "AC_F_MANAGE_ENAME")
    private String acFManageEname;

    @JSONField(name = "AC_F_MANAGE_ID_SELLER")
    private Long acFManageIdSeller;

    @JSONField(name = "AC_F_MANAGE_ECODE_SELLER")
    private String acFManageEcodeSeller;

    @JSONField(name = "AC_F_MANAGE_ENAME_SELLER")
    private String acFManageEnameSeller;

    @JSONField(name = "CP_C_CUSTOMER_ID_SET")
    private String cpCCustomerIdSet;
}