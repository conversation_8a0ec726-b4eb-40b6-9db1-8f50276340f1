package com.jackrain.nea.st.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jackrain.nea.es.annotations.Document;
import com.jackrain.nea.st.model.base.SubBaseModel;
import lombok.Data;

/**
 * @ClassName StCPreOccupyProvincePriority
 * @Description 预寻源-省优先
 * <AUTHOR>
 * @Date 2025/2/26 09:15
 * @Version 1.0
 */
@TableName(value = "st_c_pre_occupy_province_priority")
@Data
@Document(index = "st_c_pre_occupy_province_priority", type = "st_c_pre_occupy_province_priority")
public class StCPreOccupyProvincePriority extends SubBaseModel {
    private static final long serialVersionUID = -6686383525388678200L;

    @JSONField(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    @JSONField(name = "CP_C_PROVINCE_ID")
    private Long cpCProvinceId;

    @JSONField(name = "CP_C_PROVINCE_CODE")
    private String cpCProvinceCode;

    @JSONField(name = "CP_C_PROVINCE_NAME")
    private String cpCProvinceName;

    @JSONField(name = "CP_C_PHY_WAREHOUSE_ID")
    private Long cpCPhyWarehouseId;

    @JSONField(name = "CP_C_PHY_WAREHOUSE_ECODE")
    private String cpCPhyWarehouseEcode;

    @JSONField(name = "CP_C_PHY_WAREHOUSE_ENAME")
    private String cpCPhyWarehouseEname;

}
