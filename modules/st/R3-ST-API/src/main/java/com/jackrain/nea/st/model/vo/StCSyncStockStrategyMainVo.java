package com.jackrain.nea.st.model.vo;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @author: 陈俊明
 * @since: 2019-07-08
 * @create at : 2019-07-08 9:51
 */
@Data
public class StCSyncStockStrategyMainVo implements Serializable {

    @JSONField(name = "STOCK_RATE")
    private String stockRate;
    @JSONField(name = "LOW_STOCK")
    private String lowStock;
    @JSONField(name = "IS_SYNC_STOCK")
    private String isSyncStock;

}
