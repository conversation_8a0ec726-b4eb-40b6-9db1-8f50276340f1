package com.jackrain.nea.st.model.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @Date 2023/4/14 16:21
 * @Description
 */
@Getter
public enum StCShopStrategyLogisticsRuleEnum {

    STATUS_0(0, "追加"),
    STATUS_1(1, "删除"),
    STATUS_2(2, "覆盖");

    private Integer code;
    private String desc;

    StCShopStrategyLogisticsRuleEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }
}
