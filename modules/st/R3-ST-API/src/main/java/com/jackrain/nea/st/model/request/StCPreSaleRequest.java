package com.jackrain.nea.st.model.request;

import com.alibaba.fastjson.annotation.JSONField;
import com.jackrain.nea.st.model.table.StCPreArrivalDO;
import com.jackrain.nea.st.model.table.StCPreArrivalItemDO;
import com.jackrain.nea.st.model.table.StCPreSaleDO;
import com.jackrain.nea.st.model.table.StCPreSaleItemDO;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 预售策略方案
 *
 * <AUTHOR>
 * @Date 2020/06/10
 */
@Data
public class StCPreSaleRequest implements Serializable {
    @JSONField(name = "ST_C_PRE_SALE")
    private StCPreSaleDO stCPreSale;
    @JSONField(name = "ST_C_PRE_SALE_ITEM")
    private List<StCPreSaleItemDO> stCPreSaleItemList;
    @JSONField(name = "ST_C_PRE_SALE_ITEM_CUSTOMIZE")
    private List<StCPreSaleItemDO> stCPreSaleItemCustomizeList;
    private Long objId;
}
