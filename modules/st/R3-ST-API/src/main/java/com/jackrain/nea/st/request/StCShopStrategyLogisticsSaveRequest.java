package com.jackrain.nea.st.request;

import com.alibaba.fastjson.annotation.JSONField;
import com.jackrain.nea.web.face.User;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class StCShopStrategyLogisticsSaveRequest implements Serializable {

    private static final long serialVersionUID = -8687719140541524946L;

    @JSONField(name = "OBJIDS")
    private List<Long> objids;

    @JSONField(name = "LOGISTICS_TYPE")
    private String logisticsType;

    @JSONField(name = "LOGISTICS_LIST")
    private List<Long> logisticsList;

    @JSONField(name = "RULE")
    private Integer rule;

    private User user;

}
