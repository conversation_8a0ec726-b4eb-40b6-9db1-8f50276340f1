package com.jackrain.nea.st.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.*;
import com.jackrain.nea.st.model.base.SubBaseModel;
import lombok.Data;

@TableName(value = "st_c_ewaybill_logistics")
@Data
public class StCEwaybillLogisticsDO extends SubBaseModel {
    @JSO<PERSON>ield(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    @J<PERSON>NField(name = "ST_C_EWAYBILL_ID")
    private Long stCEwaybillId;

    @JSONField(name = "CP_C_LOGISTICS_ID")
    private Long cpCLogisticsId;

    @JSONField(name = "CP_C_LOGISTICS_ECODE")
    private String cpCLogisticsEcode;

    @JSONField(name = "CP_C_LOGISTICS_ENAME")
    private String cpCLogisticsEname;

    @JSONField(name = "ETYP<PERSON>")
    private Integer etype;

    @JSONField(name = "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>")
    private String ownerename;

    @J<PERSON>NField(name = "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>")
    private String modifierename;

    @JSONField(name = "AFFILIATED_SHOP")
    private String affiliatedShop;

}