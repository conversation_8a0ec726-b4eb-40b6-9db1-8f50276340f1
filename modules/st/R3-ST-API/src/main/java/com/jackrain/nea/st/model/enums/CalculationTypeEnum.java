package com.jackrain.nea.st.model.enums;

import lombok.Getter;

import java.util.Objects;

/**
 * JITX产能计算类型
 */
public enum CalculationTypeEnum {

    /**
     * 扣减
     */
    INCREMENT(0, "扣减产能"),

    /**
     * 回退
     */
    DECREMENT(1, "回退产能");


    @Getter
    private Integer key;
    @Getter
    private String desc;

    CalculationTypeEnum(Integer key, String desc) {
        this.key = key;
        this.desc = desc;
    }

    public static CalculationTypeEnum getByKey(Integer key) {
        for (CalculationTypeEnum current : values()) {
            if (Objects.equals(current.getKey(), key)) {
                return current;
            }
        }
        return null;
    }


}
