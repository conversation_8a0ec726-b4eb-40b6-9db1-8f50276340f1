package com.jackrain.nea.st.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jackrain.nea.es.annotations.Document;
import com.jackrain.nea.es.annotations.Field;
import com.jackrain.nea.es.constans.FieldType;
import com.jackrain.nea.sys.domain.BaseModel;
import lombok.Data;

/**
 * 得物仓库配置表
 */
@TableName(value = "st_c_dewu_warehouse_config")
@Data
@Document(index = "st_c_dewu_warehouse_config", type = "st_c_dewu_warehouse_config")
public class StCDewuWarehouseConfig extends BaseModel {
    @JSONField(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "id", type = IdType.INPUT)
    @Field(type = FieldType.Long)
    private Long id;

    /**
     * 地址ID
     */
    @JSONField(name = "ADDRESS_ID")
    @Field(type = FieldType.Keyword)
    private String addressId;

    /**
     * 仓库ID
     */
    @JSONField(name = "WAREHOUSE_ID")
    @Field(type = FieldType.Long)
    private Long warehouseId;

    /**
     * 仓库编码
     */
    @JSONField(name = "WAREHOUSE_CODE")
    @Field(type = FieldType.Keyword)
    private String warehouseCode;

    /**
     * 仓库名称
     */
    @JSONField(name = "WAREHOUSE_NAME")
    @Field(type = FieldType.Keyword)
    private String warehouseName;

    /**
     * 创建人姓名
     */
    @JSONField(name = "OWNERENAME")
    @Field(type = FieldType.Keyword)
    private String ownerename;

    /**
     * 修改人姓名
     */
    @JSONField(name = "MODIFIERENAME")
    @Field(type = FieldType.Keyword)
    private String modifierename;
}
