package com.jackrain.nea.st.model.result;

import com.jackrain.nea.st.model.table.StCAllocationCost;
import com.jackrain.nea.st.model.table.StCAllocationCostItem;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @program: r3-st
 * @description:
 * @author: caomalai
 * @create: 2022-11-28 10:17
 **/
@Data
public class StCAllocationCostQueryResult implements Serializable {

    List<StCAllocationCost> stCAllocationCostList;

    List<StCAllocationCostItem> stCAllocationCostItemList;
}
