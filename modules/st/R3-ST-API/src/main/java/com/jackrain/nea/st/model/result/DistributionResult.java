package com.jackrain.nea.st.model.result;

import com.jackrain.nea.st.model.table.StCDistributionDO;
import com.jackrain.nea.st.model.table.StCDistributionItemDO;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @description 分销代销查询结果集
 * @author:洪艺安
 * @since: 2019/7/18
 * @create at : 2019/7/18 16:32
 */
@Data
public class DistributionResult implements Serializable {

    private StCDistributionDO stCDistribution;

    private List<StCDistributionItemDO> stCDistributionItemList;

}
