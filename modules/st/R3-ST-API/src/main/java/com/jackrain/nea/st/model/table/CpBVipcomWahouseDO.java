package com.jackrain.nea.st.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.*;
import com.jackrain.nea.es.annotations.Document;
import com.jackrain.nea.es.annotations.Field;
import com.jackrain.nea.es.constans.FieldType;
import com.jackrain.nea.st.model.base.SubBaseModel;
import lombok.Data;

import java.util.Date;

@TableName(value = "CP_B_VIPCOM_WAHOUSE")
@Data
@Document(index = "cp_b_vipcom_wahouse",type = "cp_b_vipcom_wahouse")
public class CpBVipcomWahouseDO extends SubBaseModel {
    @JSONField(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "id", type = IdType.INPUT)
    @Field(type = FieldType.Long)
    private Long id;

    @JSONField(name = "PS_C_BRAND_ID")
    @Field(type = FieldType.Long)
    private Long psCBrandId;

    @JSONField(name = "PS_C_BRAND_ECODE")
    @Field(type = FieldType.Keyword)
    private String psCBrandEcode;

    @JSONField(name = "PS_C_BRAND_ENAME")
    @Field(type = FieldType.Keyword)
    private String psCBrandEname;

    @JSONField(name = "WAREHOUSE_CODE")
    @Field(type = FieldType.Keyword)
    private String warehouseCode;

    @JSONField(name = "WAREHOUSE_NAME")
    @Field(type = FieldType.Keyword)
    private String warehouseName;

    @JSONField(name = "ADDRESS")
    @Field(type = FieldType.Keyword)
    private String address;

    @JSONField(name = "PROVINCE_ENAME")
    @Field(type = FieldType.Keyword)
    private String provinceEname;

    @JSONField(name = "CITY_ENAME")
    @Field(type = FieldType.Keyword)
    private String cityEname;

    @JSONField(name = "AREA_ENAME")
    @Field(type = FieldType.Keyword)
    private String areaEname;

    @JSONField(name = "POSTCODE")
    @Field(type = FieldType.Keyword)
    private String postcode;

    @JSONField(name = "PHONE")
    @Field(type = FieldType.Keyword)
    private String phone;

    @JSONField(name = "MOBILE")
    @Field(type = FieldType.Keyword)
    private String mobile;

    @JSONField(name = "DELIVERY_METHOD")
    @Field(type = FieldType.Keyword)
    private String deliveryMethod;

    @JSONField(name = "CP_C_LOGISTICS_ID")
    @Field(type = FieldType.Long)
    private Long cpCLogisticsId;

    @JSONField(name = "CP_C_LOGISTICS_ECODE")
    @Field(type = FieldType.Keyword)
    private String cpCLogisticsEcode;

    @JSONField(name = "CP_C_LOGISTICS_ENAME")
    @Field(type = FieldType.Keyword)
    private String cpCLogisticsEname;

    @JSONField(name = "ARRIVAL_TIME")
    @Field(type = FieldType.Date)
    private Date arrivalTime;

    @JSONField(name = "VERSION")
    @Field(type = FieldType.Long)
    private Long version;

    @JSONField(name = "CP_C_REGION_PROVINCE_ID")
    @Field(type = FieldType.Long)
    private Long cpCRegionProvinceId;

    @JSONField(name = "CP_C_REGION_PROVINCE_ECODE")
    @Field(type = FieldType.Keyword)
    private String cpCRegionProvinceEcode;

    @JSONField(name = "CP_C_REGION_PROVINCE_ENAME")
    @Field(type = FieldType.Keyword)
    private String cpCRegionProvinceEname;

    @JSONField(name = "CP_C_REGION_CITY_ID")
    @Field(type = FieldType.Long)
    private Long cpCRegionCityId;

    @JSONField(name = "CP_C_REGION_CITY_ECODE")
    @Field(type = FieldType.Keyword)
    private String cpCRegionCityEcode;

    @JSONField(name = "CP_C_REGION_CITY_ENAME")
    @Field(type = FieldType.Keyword)
    private String cpCRegionCityEname;

    @JSONField(name = "CP_C_REGION_AREA_ID")
    @Field(type = FieldType.Long)
    private Long cpCRegionAreaId;

    @JSONField(name = "CP_C_REGION_AREA_ECODE")
    @Field(type = FieldType.Keyword)
    private String cpCRegionAreaEcode;

    @JSONField(name = "CP_C_REGION_AREA_ENAME")
    @Field(type = FieldType.Keyword)
    private String cpCRegionAreaEname;

    @JSONField(name = "OWNERENAME")
    @Field(type = FieldType.Keyword)
    private String ownerename;

    @JSONField(name = "MODIFIERENAME")
    @Field(type = FieldType.Keyword)
    private String modifierename;
}