package com.jackrain.nea.st.model.request;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 商品特殊比例-多店铺比例导入模板
 *
 * <AUTHOR>
 * @since 2020-08-24
 * create at : 2020-08-24 11:49
 */
@Data
public class ProductStrategyMultiShopImportRequest implements Serializable {

    /**
     * 店铺编码集合，多个使用“,”分隔
     */
    private String shopCodes;
    /**
     * 渠道仓编码
     */
    private String channelCode;
    /**
     * 库存比例集合，多个使用“,”分隔
     */
    private String stockRates;
    /**
     * 安全库存
     */
    private String lowStock;
    /**
     * 商品编码
     */
    private String proCode;
    /**
     * 条码编码
     */
    private String skuCode;


    /**
     * 行号
     */
    private Integer rowNum;
    /**
     * 是否成功
     */
    private Boolean isSuccess;
    /**
     * 结果信息
     */
    private String message;


    /**
     * 用于匹配拆分多线程的key
     *
     * @return
     */
    public String getThreadSplitKey() {
        return channelCode + "_" + proCode + "_" + skuCode;
    }

}