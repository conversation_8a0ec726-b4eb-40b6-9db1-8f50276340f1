package com.jackrain.nea.st.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.*;
import com.jackrain.nea.st.model.base.SubBaseModel;
import lombok.Data;

import java.util.Date;

@TableName(value = "st_c_resolve_rule")
@Data
public class StCResolveRuleDO extends SubBaseModel {
    @JSONField(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    @JSONField(name = "RANK")
    private Long rank;

    @J<PERSON>NField(name = "FILTERCOL")
    private Integer filtercol;

    @JSONField(name = "FILTEREXPRESS")
    private Integer filterexpress;

    @JSONField(name = "FILTERKEY")
    private String filterkey;

    @JSONField(name = "RESOLVECOL")
    private Integer resolvecol;

    @J<PERSON>NField(name = "IS_STATIC")
    private Integer isStatic;

    @JSONField(name = "RESOLVERESULT")
    private String resolveresult;

    @JSONField(name = "BE_RESOLVECOL")
    private Integer beResolvecol;

    @JSONField(name = "BEGIN_VALUE")
    private String beginValue;

    @JSONField(name = "END_VALUE")
    private String endValue;

    @JSONField(name = "CP_C_PLATFORM_ID")
    private Long cpCPlatformId;

    @JSONField(name = "CP_C_PLATFORM_ECODE")
    private String cpCPlatformEcode;

    @JSONField(name = "CP_C_PLATFORM_ENAME")
    private String cpCPlatformEname;

    @JSONField(name = "OWNERENAME")
    private String ownerename;

    @JSONField(name = "MODIFIERENAME")
    private String modifierename;

    @JSONField(name = "DELID")
    private Long delid;

    @JSONField(name = "DELENAME")
    private String delename;

    @JSONField(name = "DELNAME")
    private String delname;

    @JSONField(name = "DEL_TIME")
    private Date delTime;
}