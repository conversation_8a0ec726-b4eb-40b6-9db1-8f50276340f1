package com.jackrain.nea.st.model.enums;

import lombok.Getter;

import java.util.Objects;

/**
 * <AUTHOR>
 * @Date 2023/4/14 16:11
 * @Description 店铺策略可发物流类型枚举
 */
@Getter
public enum StCShopStrategyLogisticsTypeEnum {

    STATUS_0("0", "不做要求"),
    STATUS_1("1", "可发所选物流公司"),
    STATUS_2("2", "排除所选物流公司");

    private String code;
    private String desc;

    StCShopStrategyLogisticsTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static StCShopStrategyLogisticsTypeEnum getByCode(String code) {
        for (StCShopStrategyLogisticsTypeEnum current : values()) {
            if (Objects.equals(current.getCode(), code)) {
                return current;
            }
        }
        return STATUS_0;
    }

}
