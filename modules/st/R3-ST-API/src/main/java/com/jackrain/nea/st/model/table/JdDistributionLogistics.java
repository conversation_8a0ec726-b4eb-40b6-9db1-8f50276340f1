package com.jackrain.nea.st.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.*;
import com.jackrain.nea.es.annotations.Document;
import com.jackrain.nea.es.annotations.Field;
import com.jackrain.nea.es.constans.FieldType;
import com.jackrain.nea.sys.domain.BaseModel;
import lombok.Data;

/**
 * @ClassName JdDistributionLogistics
 * @Description 京东分销商物流公司
 * <AUTHOR>
 * @Date 2022/12/9 11:34
 * @Version 1.0
 */
@TableName(value = "jd_distribution_logistics")
@Data
@Document(index = "jd_distribution_logistics", type = "jd_distribution_logistics")
public class JdDistributionLogistics extends BaseModel {

    private static final long serialVersionUID = 3747631115171661863L;

    @JSONField(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "id", type = IdType.INPUT)
    @Field(type = FieldType.Long)
    private Long id;

    /**
     * 分销商id
     */
    @JSONField(name = "CP_C_DISTRIBUTION_ID")
    @Field(type = FieldType.Keyword)
    private String cpCDistributionId;

    /**
     * 平台物流公司编码
     */
    @JSONField(name = "PLATFORM_LOGISTICS_CODE")
    @Field(type = FieldType.Keyword)
    private String platformLogisticsCode;

    /**
     * 平台物流公司名称
     */
    @JSONField(name = "PLATFORM_LOGISTICS_NAME")
    @Field(type = FieldType.Keyword)
    private String platformLogisticsName;

    /**
     * 中台物流公司编码
     */
    @JSONField(name = "LOGISTICS_CODE")
    @Field(type = FieldType.Keyword)
    private String logisticsCode;

    /**
     * 中台物流公司名称
     */
    @JSONField(name = "LOGISTICS_NAME")
    @Field(type = FieldType.Keyword)
    private String logisticsName;

    /**
     * 中台物流公司ID
     */
    @JSONField(name = "LOGISTICS_ID")
    @Field(type = FieldType.Long)
    private Long logisticsId;

    /**
     * 京东分销表ID
     */
    @JSONField(name = "JD_DISTRIBUTION_ID")
    @Field(type = FieldType.Long)
    private Long jdDistributionId;

}
