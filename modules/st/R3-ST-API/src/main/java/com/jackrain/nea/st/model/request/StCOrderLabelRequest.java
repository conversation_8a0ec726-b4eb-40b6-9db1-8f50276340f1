package com.jackrain.nea.st.model.request;

import com.alibaba.fastjson.annotation.JSONField;
import com.jackrain.nea.st.model.table.*;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @ClassName : StCOrderLabelRequest
 * @Description :
 * <AUTHOR>  YCH
 * @Date: 2021-11-23 10:39
 */
@Data
public class StCOrderLabelRequest implements Serializable {
    @J<PERSON><PERSON><PERSON>(name = "ST_C_ORDER_LABEL")
    private StCOrderLabelDO stCOrderLabelDO;

    @JSONField(name = "ST_C_ORDER_LABEL_ITEM")
    private List<StCOrderLabelItemDO> stCOrderLabelItemDOList;

    @JSONField(name = "ST_C_ORDER_LABEL_STRATEGY_ITEM")
    private List<StCOrderLabelItemDO> stCOrderLabelStrategyItemDOList;

    @JSONField(name = "ST_C_ORDER_LABEL_SHOP_ITEM")
    private List<StCOrderLabelShopItemDO> stCOrderLabelShopItemDOList;
}
