package com.jackrain.nea.st.model.request;

import com.alibaba.fastjson.annotation.JSONField;
import com.jackrain.nea.st.model.result.WarehouseLogisticsRankResult;
import com.jackrain.nea.st.model.table.StCWarehouseLogisticsDO;
import com.jackrain.nea.st.model.table.StCWarehouseLogisticsItemDO;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * Descroption 仓库物流优先级方案
 *
 * <AUTHOR>
 * @Date 2019/8/13 15:23
 */
@Data
public class WarehouseLogisticsRequest implements Serializable {

    @J<PERSON>NField(name = "ST_C_WAREHOUSE_LOGISTICS")
    private StCWarehouseLogisticsDO warehouseLogistics;
    @JSONField(name = "ST_C_WAREHOUSE_LOGISTICS_ITEM")
    private List<StCWarehouseLogisticsItemDO> itemList;
    @J<PERSON>NField(name = "ST_C_WAREHOUSE_LOGISTICS_RANK_RESULT")
    private List<WarehouseLogisticsRankResult> rankResultList;

}
