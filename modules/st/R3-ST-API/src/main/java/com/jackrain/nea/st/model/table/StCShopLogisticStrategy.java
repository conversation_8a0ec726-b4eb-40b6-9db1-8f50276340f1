package com.jackrain.nea.st.model.table;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jackrain.nea.st.model.base.SubBaseModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Created with IntelliJ IDEA.
 * @Author: jg.zhan
 * @Date:  2022/6/17 15:01
 * @Description:  
 */
/**
    * 店铺物流策略主表
    */
@ApiModel(value="店铺物流策略主表")
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "st_c_shop_logistic_strategy")
public class StCShopLogisticStrategy extends SubBaseModel implements Serializable {
    /**
     * ID
     */
    @TableId(value = "id", type = IdType.INPUT)
    @ApiModelProperty(value="ID")
    private Long id;

    /**
     * 平台店铺id
     */
    @TableField(value = "cp_c_shop_id")
    @ApiModelProperty(value="平台店铺id")
    private Long cpCShopId;

    /**
     * 备注
     */
    @TableField(value = "remark")
    @ApiModelProperty(value="备注")
    private String remark;




    private static final long serialVersionUID = 5688383980838766642L;

    public static final String TABLE_NAME = "st_c_shop_logistic_strategy";

    public static final String COL_ID = "id";

    public static final String COL_CP_C_SHOP_ID = "cp_c_shop_id";

    public static final String COL_REMARK = "remark";

    public static final String COL_AD_ORG_ID = "ad_org_id";

    public static final String COL_ISACTIVE = "isactive";

    public static final String COL_AD_CLIENT_ID = "ad_client_id";

    public static final String COL_OWNERID = "ownerid";

    public static final String COL_OWNERENAME = "ownerename";

    public static final String COL_OWNERNAME = "ownername";

    public static final String COL_CREATIONDATE = "creationdate";

    public static final String COL_MODIFIERID = "modifierid";

    public static final String COL_MODIFIERENAME = "modifierename";

    public static final String COL_MODIFIERNAME = "modifiername";

    public static final String COL_MODIFIEDDATE = "modifieddate";
}