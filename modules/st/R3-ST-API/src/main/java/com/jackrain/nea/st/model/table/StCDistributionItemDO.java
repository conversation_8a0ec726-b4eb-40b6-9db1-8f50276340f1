package com.jackrain.nea.st.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.*;
import com.jackrain.nea.st.model.base.SubBaseModel;
import lombok.Data;

import java.math.BigDecimal;

@TableName(value = "ST_C_DISTRIBUTION_ITEM")
@Data
public class StCDistributionItemDO extends SubBaseModel {
    @JSONField(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    @JSONField(name = "ST_C_DISTRIBUTION_ID")
    private Long stCDistributionId;

    @J<PERSON><PERSON><PERSON>(name = "PS_C_SKU_ID")
    private Long psCSkuId;

    @JSONField(name = "PS_C_SKU_ECODE")
    private String psCSkuEcode;

    @JSONField(name = "PS_C_PRO_ID")
    private Long psCProId;

    @JSONField(name = "PS_C_PRO_ECODE")
    private String psCProEcode;

    @JSO<PERSON>ield(name = "PS_C_PRO_ENAME")
    private String psCProEname;

    @J<PERSON><PERSON>ield(name = "STANDARDPRICE")
    private BigDecimal standardprice;

    @JSONField(name = "SETTLEMENTPRICE")
    private BigDecimal settlementprice;

    @JSONField(name = "GBCODE")
    private String gbcode;

    @JSONField(name = "PS_C_CLR_ID")
    private Long psCClrId;

    @JSONField(name = "PS_C_CLR_ECODE")
    private String psCClrEcode;

    @JSONField(name = "PS_C_CLR_ENAME")
    private String psCClrEname;

    @JSONField(name = "PS_C_SIZE_ID")
    private Long psCSizeId;

    @JSONField(name = "PS_C_SIZE_ECODE")
    private String psCSizeEcode;

    @JSONField(name = "PS_C_SIZE_ENAME")
    private String psCSizeEname;

    @JSONField(name = "UNIT")
    private Long unit;

    @JSONField(name = "DISCOUNT")
    private BigDecimal discount;

    @JSONField(name = "OWNERENAME")
    private String ownerename;

    @JSONField(name = "MODIFIERENAME")
    private String modifierename;
}