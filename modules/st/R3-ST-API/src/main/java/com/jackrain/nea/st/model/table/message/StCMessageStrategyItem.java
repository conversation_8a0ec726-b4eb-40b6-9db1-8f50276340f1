package com.jackrain.nea.st.model.table.message;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.*;
import com.jackrain.nea.sys.domain.BaseModel;
import lombok.Data;

@TableName(value = "st_c_message_strategy_item")
@Data
public class StCMessageStrategyItem extends BaseModel {
    @JSO<PERSON>ield(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    @J<PERSON><PERSON>ield(name = "REMARK")
    private String remark;

    @JSONField(name = "VERSION")
    private Long version;

    @J<PERSON><PERSON>ield(name = "OWNERENAME")
    private String ownerename;

    @J<PERSON><PERSON>ield(name = "MODIFIERENAME")
    private String modifierename;

    @JSONField(name = "ST_C_MESSAGE_STRATEGY_ID")
    private Long stCMessageStrategyId;

    @<PERSON><PERSON><PERSON>ield(name = "CP_C_SHOP_ID")
    private Long cpCShopId;

    @JSONField(name = "CP_C_PLATFORM_ID")
    private Long cpCPlatformId;

    @JSONField(name = "BURGEON_PLATFORM_SIGNATURE_NUMBER")
    private String burgeonPlatformSignatureNumber;

    @JSONField(name = "BURGEON_MESSAGE_TEMPLATE_NUMBER")
    private String burgeonMessageTemplateNumber;
}