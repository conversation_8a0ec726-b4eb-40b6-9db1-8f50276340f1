package com.jackrain.nea.st.model.request;

import com.alibaba.fastjson.annotation.JSONField;
import com.jackrain.nea.st.model.table.StCCustomLabelDO;
import com.jackrain.nea.st.model.table.StCOrderLabelDO;
import com.jackrain.nea.st.model.table.StCOrderLabelItemDO;
import lombok.Data;

import java.util.List;

/**
 * @ClassName : StCCustomLabelRequest  
 * @Description : 
 * <AUTHOR>  YCH
 * @Date: 2021-11-30 17:03  
 */
@Data
public class StCCustomLabelRequest {
    @JSONField(name = "ST_C_CUSTOM_LABEL")
    private StCCustomLabelDO stCCustomLabelDO;

}
