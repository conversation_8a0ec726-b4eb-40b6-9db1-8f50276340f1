package com.jackrain.nea.st.model.result;

import com.jackrain.nea.cpext.model.table.CpCPhyWarehouse;
import com.jackrain.nea.st.model.table.StCSendRuleAddressRentDO;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @Author: huang.z<PERSON>
 * @Date: 2019-09-04
 * @Version 1.0
 */

@Data
public class SendRuleWarehouseInfoResult implements Serializable {

    private List<CpCPhyWarehouse> cpCPhyWarehouseList;

    private List<StCSendRuleAddressRentDO> sendRuleAddressRents;
}