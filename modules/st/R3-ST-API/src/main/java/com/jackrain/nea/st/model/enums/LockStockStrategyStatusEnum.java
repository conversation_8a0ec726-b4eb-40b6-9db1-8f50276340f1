package com.jackrain.nea.st.model.enums;

import lombok.Getter;

import java.util.Objects;

/**
 * @description 锁库策略状态
 * @author:秦俊龙
 * @since: 2020/6/13
 * @create at : 2020/6/13 11：11
 */
public enum LockStockStrategyStatusEnum {

    /**
     * 未审核"
     */
    NONE_AUDIT(1, "未审核"),

    /**
     * 已审核"
     */
    AUDITED(2, "已审核"),

    /**
     * 已作废"
     */
    VOIDED(3, "已作废"),

    /**
     * 已结案"
     */
    CLOSED(4, "已结案");


    @Getter
    private Integer key;
    @Getter
    private String desc;

    LockStockStrategyStatusEnum(Integer key, String desc) {
        this.key = key;
        this.desc = desc;
    }

    public static LockStockStrategyStatusEnum getByKey(Integer key) {
        for (LockStockStrategyStatusEnum current : values()) {
            if (Objects.equals(current.getKey(), key)) {
                return current;
            }
        }
        return null;
    }


}
