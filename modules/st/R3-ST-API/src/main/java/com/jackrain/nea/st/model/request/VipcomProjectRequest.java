package com.jackrain.nea.st.model.request;

import com.alibaba.fastjson.annotation.JSONField;
import com.jackrain.nea.st.model.table.*;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 档期日程计划
 *
 * <AUTHOR>
 * @Date 2019/3/10 21:26
 */
@Data
public class VipcomProjectRequest  implements Serializable {
    @JSONField(name = "ST_C_VIPCOM_PROJECT")
    private StCVipcomProjectDO stCVipcomProject;
    @JSONField(name = "ST_C_VIPCOM_PROJECT_ITEM")
    private List<StCVipcomProjectItemDO> stCVipcomProjectItemList;

    private String poNo;//poNo单号
}
