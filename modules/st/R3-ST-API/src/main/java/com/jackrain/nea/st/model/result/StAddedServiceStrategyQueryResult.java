package com.jackrain.nea.st.model.result;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @program: ryytn-st-v3.0
 * @description: 增值服务查询结果返回
 * @author: haiyang
 * @create: 2023-10-25 17:23
 **/
@Data
public class StAddedServiceStrategyQueryResult implements Serializable {


    @J<PERSON><PERSON><PERSON>(name = "ADDED_STRATEGY_ID")
    private Long addedStrategyId;

    @J<PERSON><PERSON><PERSON>(name = "ADDED_STRATEGY_CODE")
    private String addedStrategyCode;

    @JSONField(name = "ADDED_STRATEGY_NAME")
    private String addedStrategyName;

    @JSONField(name = "CP_C_PHY_WAREHOUSE_ID")
    private Long cpCPhyWarehouseId;

    @JSONField(name = "ADDED_TYPE_DOC_ID")
    private Long addedTypeDocId;

    @JSONField(name = "ADDED_TYPE_CODE")
    private String addedTypeCode;

    @J<PERSON><PERSON>ield(name = "ADDED_TYPE_NAME")
    private String addedTypeName;

    @JSONField(name = "UNIT_PRICE")
    private BigDecimal unitPrice;




}
