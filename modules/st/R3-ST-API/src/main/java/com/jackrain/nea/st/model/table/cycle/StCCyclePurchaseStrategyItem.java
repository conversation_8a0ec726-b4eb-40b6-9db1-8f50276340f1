package com.jackrain.nea.st.model.table.cycle;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.*;
import com.jackrain.nea.sys.domain.BaseModel;
import lombok.Data;

import java.io.Serializable;

@TableName(value = "st_c_cycle_purchase_strategy_item")
@Data
public class StCCyclePurchaseStrategyItem extends BaseModel implements Serializable {
    @JSONField(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    @JSONField(name = "REMAR<PERSON>")
    private String remark;

    @JSONField(name = "VERSION")
    private Long version;

    @J<PERSON>NField(name = "OWNERENAME")
    private String ownerename;

    @JSONField(name = "MODIFIERENAME")
    private String modifierename;

    @JSONField(name = "PS_C_SKU_ID")
    private Long psCSkuId;

    @JSONField(name = "PS_C_SKU_ECODE")
    private String psCSkuEcode;

    @JSONField(name = "GBCODE")
    private String gbcode;

    @JSONField(name = "PS_C_BRAND_ID")
    private Long psCBrandId;

    @JSONField(name = "PS_C_PRO_ID")
    private Long psCProId;

    @JSONField(name = "PS_C_PRO_ECODE")
    private String psCProEcode;

    @JSONField(name = "PS_C_PRO_ENAME")
    private String psCProEname;

    @JSONField(name = "PS_C_SPEC1_ID")
    private Long psCSpec1Id;

    @JSONField(name = "PS_C_SPEC1_ECODE")
    private String psCSpec1Ecode;

    @JSONField(name = "PS_C_SPEC1_ENAME")
    private String psCSpec1Ename;

    @JSONField(name = "PS_C_SPEC2_ID")
    private Long psCSpec2Id;

    @JSONField(name = "PS_C_SPEC2_ECODE")
    private String psCSpec2Ecode;

    @JSONField(name = "PS_C_SPEC2_ENAME")
    private String psCSpec2Ename;

    @JSONField(name = "PS_C_SPEC3_ID")
    private Long psCSpec3Id;

    @JSONField(name = "PS_C_SPEC3_ECODE")
    private String psCSpec3Ecode;

    @JSONField(name = "PS_C_SPEC3_ENAME")
    private String psCSpec3Ename;

    @JSONField(name = "QTY")
    private Integer qty;

    @JSONField(name = "ST_C_CYCLE_PURCHASE_STRATEGY_ID")
    private Long stCCyclePurchaseStrategyId;
}