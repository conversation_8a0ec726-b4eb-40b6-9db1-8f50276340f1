package com.jackrain.nea.st.model.request;

import lombok.Data;

import java.io.Serializable;

/**
 * 班牛问题清单查询请求
 */
@Data
public class BnProblemConfigQueryRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    private Long id;

    /**
     * 问题类型
     */
    private String problemText;

    /**
     * 客户要求是否有
     */
    private Integer isCustomerRequest;

    /**
     * 商品编码是否有
     */
    private Integer isSkuCode;

    /**
     * 附件是否必填
     */
    private Integer isUrl;

    /**
     * 批量是否支持
     */
    private Integer isBatch;

    /**
     * 是否取消退换货单
     */
    private Integer isCancelReturn;

    /**
     * 是否激活
     */
    private String isactive;

    /**
     * 卖家备注
     */
    private String sellerRemark;
}
