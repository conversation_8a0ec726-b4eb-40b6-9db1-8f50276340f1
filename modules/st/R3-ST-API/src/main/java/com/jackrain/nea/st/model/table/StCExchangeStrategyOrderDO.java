package com.jackrain.nea.st.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jackrain.nea.st.model.base.SubBaseModel;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@TableName(value = "st_c_exchange_order_strategy")
@Data
public class StCExchangeStrategyOrderDO extends SubBaseModel {
    @JSONField(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    @JSONField(name = "CP_C_SHOP_ID")
    private Long cpCShopId;

    @JSONField(name = "CP_C_SHOP_TITLE")
    private String cpCShopTitle;

    @JSONField(name = "IS_OUT_REFUSE")
    private Integer isOutRefuse;

    @JSONField(name = "IS_PRICE_OTHER_AGREE")
    private Integer isPriceOtherAgree;

    @JSONField(name = "IS_NO_REMARK_AGREE")
    private Integer isNoRemarkAgree;

    @JSONField(name = "REMARK")
    private String remark;

    @JSONField(name = "OWNERENAME")
    private String ownerename;

    @JSONField(name = "MODIFIERENAME")
    private String modifierename;

    @JSONField(name = "DELID")
    private Long delid;

    @JSONField(name = "DELENAME")
    private String delename;

    @JSONField(name = "DELNAME")
    private String delname;

    @JSONField(name = "DEL_TIME")
    private Date delTime;

    @JSONField(name = "IS_OFF_AGREE")
    private Integer isOffAgree;

    @JSONField(name = "IS_OFF_REFUSE")
    private Integer isOffRefuse;

    @JSONField(name = "IS_PRICE_OTHER_REFUSE")
    private Integer isPriceOtherRefuse;

    @JSONField(name = "OUT_REFUSE_REASON_ID")
    private Integer outRefuseReasonId;

    @JSONField(name = "DEVIATION_AMT_REFUSE_REASON_ID")
    private Integer deviationAmtRefuseReasonId;

    @JSONField(name = "DEVIATION_AMT_AGREE")
    private BigDecimal deviationAmtAgree;

    @JSONField(name = "DEVIATION_AMT_REFUSE")
    private BigDecimal deviationAmtRefuse;

    @JSONField(name = "OUT_REFUSE_COPYWRITING")
    private String outRefuseCopywriting;

    @JSONField(name = "DEVIATION_AMT_REFUSE_COPYWRITING")
    private String deviationAmtRefuseCopywriting;

    @JSONField(name = "IS_O2O_DELIVERY")
    private Integer isO2oDelivery ;

}