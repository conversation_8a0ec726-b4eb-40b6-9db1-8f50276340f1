package com.jackrain.nea.st.model.request;

import lombok.Data;

import java.io.Serializable;

/**
 * Descroption 物流区域设置
 *
 * <AUTHOR>
 * @Date 2019/3/13 14:23
 */
@Data
public class StCExpressPriceStrategyQueryRequest implements Serializable {

    /**
     * 实体仓id
     */
    private Long cpCPhyWarehouseId;
    /**
     * 物流公司id
     */
    private Long cpCLogisticsId;
    /**
     * 省
     */
    private Long cpCProvinceId;
}
