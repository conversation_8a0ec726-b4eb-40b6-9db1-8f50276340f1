package com.jackrain.nea.st.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.*;
import com.jackrain.nea.st.model.base.SubBaseModel;
import lombok.Data;

/**
 * 直播解析策略明细表
 */
@TableName(value = "st_c_live_cast_strategy_item")
@Data
public class StCLiveCastStrategyItemDO extends SubBaseModel {

    @JSONField(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    // 直播方案ID
    @JSONField(name = "st_c_live_cast_id")
    private Long stCLiveCastId;
    // 识别规则 1:根据商品标题识别,2:根据平台商品ID识别, 3:根据订单备注识别
    @JSONField(name = "RULE_TYPE")
    private Integer ruleType;
    // 识别内容
    @JSONField(name = "RULE_CONTEXT")
    private String ruleContext;

}
