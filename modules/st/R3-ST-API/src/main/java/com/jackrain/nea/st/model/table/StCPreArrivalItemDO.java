package com.jackrain.nea.st.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.*;
import com.jackrain.nea.es.annotations.Document;
import com.jackrain.nea.es.annotations.Field;
import com.jackrain.nea.es.constans.FieldType;
import com.jackrain.nea.st.model.base.SubBaseModel;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @title $
 * @Description 预到货策略明细
 * @Date 2020/06/10
 * @Copyright 2019-2020
 */
@TableName(value = "ST_C_PRE_ARRIVAL_ITEM")
@Data
@Document(index = "st_c_pre_arrival_item",type = "st_c_pre_arrival_item")
public class StCPreArrivalItemDO extends SubBaseModel {
    @JSONField(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "id", type = IdType.INPUT)
    @Field(type = FieldType.Long)
    private Long id;

    @JSONField(name = "ST_C_PRE_ARRIVAL_ID")
    @Field(type = FieldType.Long)
    private Long stCPreArrivalId;

    @JSONField(name = "PS_C_SKU_ID")
    @Field(type = FieldType.Long)
    private Long psCSkuId;

    @JSONField(name = "PS_C_SKU_ECODE")
    @Field(type = FieldType.Keyword)
    private String psCSkuEcode;

    @JSONField(name = "PS_C_PRO_LINE")
    @Field(type = FieldType.Keyword)
    private Integer psCProLine;

    @JSONField(name = "PS_C_PRO_SEX")
    @Field(type = FieldType.Keyword)
    private String psCProSex;

    @JSONField(name = "PS_C_PRO_ID")
    @Field(type = FieldType.Long)
    private Long psCProId;

    @JSONField(name = "PS_C_PRO_ECODE")
    @Field(type = FieldType.Keyword)
    private String psCProEcode;

    @JSONField(name = "PS_C_PRO_ENAME")
    @Field(type = FieldType.Keyword)
    private String psCProEname;

    @JSONField(name = "PS_C_SPEC_REMARK")
    @Field(type = FieldType.Keyword)
    private String psCSpecRemark;

    @JSONField(name = "NUM")
    @Field(type = FieldType.Long)
    private Long num;

    @JSONField(name = "PRE_ARRIVAL_DATE")
    @Field(type = FieldType.Date)
    private Date preArrivalDate;


}
