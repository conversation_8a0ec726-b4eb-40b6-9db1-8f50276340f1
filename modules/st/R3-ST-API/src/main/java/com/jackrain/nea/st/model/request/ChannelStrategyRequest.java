package com.jackrain.nea.st.model.request;

import com.alibaba.fastjson.annotation.JSONField;
import com.jackrain.nea.st.model.table.StCChannelStrategyDO;
import com.jackrain.nea.st.model.table.StCChannelStrategyItemDO;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * Descroption 渠道策略
 *
 * <AUTHOR>
 * @Date 2019/12/4 13:09
 */
@Data
@Deprecated
public class ChannelStrategyRequest implements Serializable {

    @JSONField(name = "ST_C_CHANNEL_STRATEGY")
    private StCChannelStrategyDO channelStrategy;
    @JSONField(name = "ST_C_CHANNEL_STRATEGY_ITEM")
    private List<StCChannelStrategyItemDO> channelStrategyList;
}
