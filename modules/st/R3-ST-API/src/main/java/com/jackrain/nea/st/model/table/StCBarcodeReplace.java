package com.jackrain.nea.st.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jackrain.nea.es.annotations.Document;
import com.jackrain.nea.es.annotations.Field;
import com.jackrain.nea.es.constans.FieldType;

import java.util.Date;

import com.jackrain.nea.st.model.base.SubBaseModel;
import com.jackrain.nea.sys.domain.BaseModel;
import lombok.Data;

/**
 * 多代条码策略
 *
 * <AUTHOR>
 */
@TableName(value = "st_c_barcode_replace")
@Data
@Document(index = "st_c_barcode_replace", type = "st_c_barcode_replace")
public class StCBarcodeReplace extends BaseModel {
    /**
     * id
     */
    @JSONField(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "id", type = IdType.INPUT)
    @Field(type = FieldType.Long)
    private Long id;

    /**
     * 策略编码
     */
    @JSONField(name = "STRATEGY_NO")
    @Field(type = FieldType.Keyword)
    private String strategyNo;

    /**
     * 可选店铺。1:包含店铺；2:排除店铺
     */
    @JSONField(name = "SELECT_SHOP_TYPE")
    @Field(type = FieldType.Integer)
    private Integer selectShopType;

    /**
     * 组名称
     */
    @JSONField(name = "GROUP_NAME")
    @Field(type = FieldType.Keyword)
    private String groupName;

    /**
     * 备注
     */
    @JSONField(name = "REMARK")
    @Field(type = FieldType.Keyword)
    private String remark;

}