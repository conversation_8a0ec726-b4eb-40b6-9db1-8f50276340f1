package com.jackrain.nea.st.model.request;

import com.alibaba.fastjson.annotation.JSONField;
import com.jackrain.nea.st.model.table.*;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
@Data
public class SellOwnGoodsRequest implements Serializable {

    @J<PERSON><PERSON><PERSON>(name = "ST_C_SELL_OWNGOODS")
    private StCSellOwngoodsDO stCSellOwngoodsDO;

    @J<PERSON>NField(name = "ST_C_SELL_OWNGOODS_ITEM")
    private List<StCSellOwngoodsItemDO> stCSellOwngoodsItemDOList;

    @J<PERSON><PERSON><PERSON>(name = "ST_C_SELL_OWNGOODS_CUSTOMER")
    private List<StCSellOwngoodsCustomerDO> stCSellOwngoodsCustomerDOList;
}
