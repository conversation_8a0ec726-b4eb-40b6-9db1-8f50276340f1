package com.jackrain.nea.st.model.result;

import com.jackrain.nea.st.model.table.StCSendRuleAddressRentDO;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @Author: huang.zaizai
 * @Date: 2019-09-04
 * @Version 1.0
 */
@Data
public class SendRuleTreeResult implements Serializable {

    private List<RegionTreeResult> sendRuleTree;

    private List<StCSendRuleAddressRentDO> sendRuleAddressRents;
}
