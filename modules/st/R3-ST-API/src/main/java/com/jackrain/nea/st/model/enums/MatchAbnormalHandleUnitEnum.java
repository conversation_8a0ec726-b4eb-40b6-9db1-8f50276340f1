package com.jackrain.nea.st.model.enums;

import lombok.Getter;

/**
 * @ClassName MatchAbnormalHandleUnitEnum
 * @Description 单位
 * <AUTHOR>
 * @Date 2022/12/29 19:43
 * @Version 1.0
 */
public enum MatchAbnormalHandleUnitEnum {

    MINUTE(1, "分钟"),
    HOUR(2, "小时"),
    DAY(3, "天");


    @Getter
    private Integer value;

    @Getter
    private String desc;

    MatchAbnormalHandleUnitEnum(Integer value, String desc) {
        this.value = value;
        this.desc = desc;
    }
}
