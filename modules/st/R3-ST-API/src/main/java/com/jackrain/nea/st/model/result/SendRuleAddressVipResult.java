package com.jackrain.nea.st.model.result;

import com.alibaba.fastjson.annotation.JSONField;
import com.jackrain.nea.st.model.table.StCSendRuleAddressVipDo;
import lombok.Data;

/**
 * <AUTHOR>
 * @create 2020-06-15 20:50
 * @desc 订单派单规则-唯品会明细
 **/
@Data
public class SendRuleAddressVipResult extends StCSendRuleAddressVipDo {

    @JSONField(name = "CP_C_PHY_WAREHOUSE_ID1")
    private Long cpCPhyWarehouseId1;
    @J<PERSON><PERSON><PERSON>(name = "CP_C_PHY_WAREHOUSE_ECODE1")
    private String cpCPhyWarehouseEcode1;
    @JSONField(name = "CP_C_PHY_WAREHOUSE_ENAME1")
    private String cpCPhyWarehouseEname1;
    @JSONField(name = "RANK1")
    private String rank1;

    @JSONField(name = "CP_C_PHY_WAREHOUSE_ID2")
    private Long cpCPhyWarehouseId2;
    @J<PERSON><PERSON>ield(name = "CP_C_PHY_WAREHOUSE_ECODE2")
    private String cpCPhyWarehouseEcode2;
    @J<PERSON><PERSON><PERSON>(name = "CP_C_PHY_WAREHOUSE_ENAME2")
    private String cpCPhyWarehouseEname2;
    @JSONField(name = "RANK2")
    private String rank2;

    @JSONField(name = "CP_C_PHY_WAREHOUSE_ID3")
    private Long cpCPhyWarehouseId3;
    @JSONField(name = "CP_C_PHY_WAREHOUSE_ECODE3")
    private String cpCPhyWarehouseEcode3;
    @JSONField(name = "CP_C_PHY_WAREHOUSE_ENAME3")
    private String cpCPhyWarehouseEname3;
    @JSONField(name = "RANK3")
    private String rank3;

    @JSONField(name = "CP_C_PHY_WAREHOUSE_ID4")
    private Long cpCPhyWarehouseId4;
    @JSONField(name = "CP_C_PHY_WAREHOUSE_ECODE4")
    private String cpCPhyWarehouseEcode4;
    @JSONField(name = "CP_C_PHY_WAREHOUSE_ENAME4")
    private String cpCPhyWarehouseEname4;
    @JSONField(name = "RANK4")
    private String rank4;

    @JSONField(name = "CP_C_PHY_WAREHOUSE_ID5")
    private Long cpCPhyWarehouseId5;
    @JSONField(name = "CP_C_PHY_WAREHOUSE_ECODE5")
    private String cpCPhyWarehouseEcode5;
    @JSONField(name = "CP_C_PHY_WAREHOUSE_ENAME5")
    private String cpCPhyWarehouseEname5;
    @JSONField(name = "RANK5")
    private String rank5;

    @JSONField(name = "CP_C_PHY_WAREHOUSE_ID6")
    private Long cpCPhyWarehouseId6;
    @JSONField(name = "CP_C_PHY_WAREHOUSE_ECODE6")
    private String cpCPhyWarehouseEcode6;
    @JSONField(name = "CP_C_PHY_WAREHOUSE_ENAME6")
    private String cpCPhyWarehouseEname6;
    @JSONField(name = "RANK6")
    private String rank6;

    @JSONField(name = "CP_C_PHY_WAREHOUSE_ID7")
    private Long cpCPhyWarehouseId7;
    @JSONField(name = "CP_C_PHY_WAREHOUSE_ECODE7")
    private String cpCPhyWarehouseEcode7;
    @JSONField(name = "CP_C_PHY_WAREHOUSE_ENAME7")
    private String cpCPhyWarehouseEname7;
    @JSONField(name = "RANK7")
    private String rank7;

    @JSONField(name = "CP_C_PHY_WAREHOUSE_ID8")
    private Long cpCPhyWarehouseId8;
    @JSONField(name = "CP_C_PHY_WAREHOUSE_ECODE8")
    private String cpCPhyWarehouseEcode8;
    @JSONField(name = "CP_C_PHY_WAREHOUSE_ENAME8")
    private String cpCPhyWarehouseEname8;
    @JSONField(name = "RANK8")
    private String rank8;

    @JSONField(name = "CP_C_PHY_WAREHOUSE_ID9")
    private Long cpCPhyWarehouseId9;
    @JSONField(name = "CP_C_PHY_WAREHOUSE_ECODE9")
    private String cpCPhyWarehouseEcode9;
    @JSONField(name = "CP_C_PHY_WAREHOUSE_ENAME9")
    private String cpCPhyWarehouseEname9;
    @JSONField(name = "RANK9")
    private String rank9;

    @JSONField(name = "CP_C_PHY_WAREHOUSE_ID10")
    private Long cpCPhyWarehouseId10;
    @JSONField(name = "CP_C_PHY_WAREHOUSE_ECODE10")
    private String cpCPhyWarehouseEcode10;
    @JSONField(name = "CP_C_PHY_WAREHOUSE_ENAME10")
    private String cpCPhyWarehouseEname10;
    @JSONField(name = "RANK10")
    private String rank10;

    @JSONField(name = "CP_C_PHY_WAREHOUSE_ID11")
    private Long cpCPhyWarehouseId11;
    @JSONField(name = "CP_C_PHY_WAREHOUSE_ECODE11")
    private String cpCPhyWarehouseEcode11;
    @JSONField(name = "CP_C_PHY_WAREHOUSE_ENAME11")
    private String cpCPhyWarehouseEname11;
    @JSONField(name = "RANK11")
    private String rank11;

    @JSONField(name = "CP_C_PHY_WAREHOUSE_ID12")
    private Long cpCPhyWarehouseId12;
    @JSONField(name = "CP_C_PHY_WAREHOUSE_ECODE12")
    private String cpCPhyWarehouseEcode12;
    @JSONField(name = "CP_C_PHY_WAREHOUSE_ENAME12")
    private String cpCPhyWarehouseEname12;
    @JSONField(name = "RANK12")
    private String rank12;

    @JSONField(name = "CP_C_PHY_WAREHOUSE_ID13")
    private Long cpCPhyWarehouseId13;
    @JSONField(name = "CP_C_PHY_WAREHOUSE_ECODE13")
    private String cpCPhyWarehouseEcode13;
    @JSONField(name = "CP_C_PHY_WAREHOUSE_ENAME13")
    private String cpCPhyWarehouseEname13;
    @JSONField(name = "RANK13")
    private String rank13;

    @JSONField(name = "CP_C_PHY_WAREHOUSE_ID14")
    private Long cpCPhyWarehouseId14;
    @JSONField(name = "CP_C_PHY_WAREHOUSE_ECODE14")
    private String cpCPhyWarehouseEcode14;
    @JSONField(name = "CP_C_PHY_WAREHOUSE_ENAME14")
    private String cpCPhyWarehouseEname14;
    @JSONField(name = "RANK14")
    private String rank14;

    @JSONField(name = "CP_C_PHY_WAREHOUSE_ID15")
    private Long cpCPhyWarehouseId15;
    @JSONField(name = "CP_C_PHY_WAREHOUSE_ECODE15")
    private String cpCPhyWarehouseEcode15;
    @JSONField(name = "CP_C_PHY_WAREHOUSE_ENAME15")
    private String cpCPhyWarehouseEname15;
    @JSONField(name = "RANK15")
    private String rank15;

    @JSONField(name = "CP_C_PHY_WAREHOUSE_ID16")
    private Long cpCPhyWarehouseId16;
    @JSONField(name = "CP_C_PHY_WAREHOUSE_ECODE16")
    private String cpCPhyWarehouseEcode16;
    @JSONField(name = "CP_C_PHY_WAREHOUSE_ENAME16")
    private String cpCPhyWarehouseEname16;
    @JSONField(name = "RANK16")
    private String rank16;

    @JSONField(name = "CP_C_PHY_WAREHOUSE_ID17")
    private Long cpCPhyWarehouseId17;
    @JSONField(name = "CP_C_PHY_WAREHOUSE_ECODE17")
    private String cpCPhyWarehouseEcode17;
    @JSONField(name = "CP_C_PHY_WAREHOUSE_ENAME17")
    private String cpCPhyWarehouseEname17;
    @JSONField(name = "RANK17")
    private String rank17;

    @JSONField(name = "CP_C_PHY_WAREHOUSE_ID18")
    private Long cpCPhyWarehouseId18;
    @JSONField(name = "CP_C_PHY_WAREHOUSE_ECODE18")
    private String cpCPhyWarehouseEcode18;
    @JSONField(name = "CP_C_PHY_WAREHOUSE_ENAME18")
    private String cpCPhyWarehouseEname18;
    @JSONField(name = "RANK18")
    private String rank18;

    @JSONField(name = "CP_C_PHY_WAREHOUSE_ID19")
    private Long cpCPhyWarehouseId19;
    @JSONField(name = "CP_C_PHY_WAREHOUSE_ECODE19")
    private String cpCPhyWarehouseEcode19;
    @JSONField(name = "CP_C_PHY_WAREHOUSE_ENAME19")
    private String cpCPhyWarehouseEname19;
    @JSONField(name = "RANK19")
    private String rank19;

    @JSONField(name = "CP_C_PHY_WAREHOUSE_ID20")
    private Long cpCPhyWarehouseId20;
    @JSONField(name = "CP_C_PHY_WAREHOUSE_ECODE20")
    private String cpCPhyWarehouseEcode20;
    @JSONField(name = "CP_C_PHY_WAREHOUSE_ENAME20")
    private String cpCPhyWarehouseEname20;
    @JSONField(name = "RANK20")
    private String rank20;

    @JSONField(name = "CP_C_PHY_WAREHOUSE_ID21")
    private Long cpCPhyWarehouseId21;
    @JSONField(name = "CP_C_PHY_WAREHOUSE_ECODE21")
    private String cpCPhyWarehouseEcode21;
    @JSONField(name = "CP_C_PHY_WAREHOUSE_ENAME21")
    private String cpCPhyWarehouseEname21;
    @JSONField(name = "RANK21")
    private String rank21;

    @JSONField(name = "CP_C_PHY_WAREHOUSE_ID22")
    private Long cpCPhyWarehouseId22;
    @JSONField(name = "CP_C_PHY_WAREHOUSE_ECODE22")
    private String cpCPhyWarehouseEcode22;
    @JSONField(name = "CP_C_PHY_WAREHOUSE_ENAME22")
    private String cpCPhyWarehouseEname22;
    @JSONField(name = "RANK22")
    private String rank22;

    @JSONField(name = "CP_C_PHY_WAREHOUSE_ID23")
    private Long cpCPhyWarehouseId23;
    @JSONField(name = "CP_C_PHY_WAREHOUSE_ECODE23")
    private String cpCPhyWarehouseEcode23;
    @JSONField(name = "CP_C_PHY_WAREHOUSE_ENAME23")
    private String cpCPhyWarehouseEname23;
    @JSONField(name = "RANK23")
    private String rank23;

    @JSONField(name = "CP_C_PHY_WAREHOUSE_ID24")
    private Long cpCPhyWarehouseId24;
    @JSONField(name = "CP_C_PHY_WAREHOUSE_ECODE24")
    private String cpCPhyWarehouseEcode24;
    @JSONField(name = "CP_C_PHY_WAREHOUSE_ENAME24")
    private String cpCPhyWarehouseEname24;
    @JSONField(name = "RANK24")
    private String rank24;

    @JSONField(name = "CP_C_PHY_WAREHOUSE_ID25")
    private Long cpCPhyWarehouseId25;
    @JSONField(name = "CP_C_PHY_WAREHOUSE_ECODE25")
    private String cpCPhyWarehouseEcode25;
    @JSONField(name = "CP_C_PHY_WAREHOUSE_ENAME25")
    private String cpCPhyWarehouseEname25;
    @JSONField(name = "RANK25")
    private String rank25;

    @JSONField(name = "CP_C_PHY_WAREHOUSE_ID26")
    private Long cpCPhyWarehouseId26;
    @JSONField(name = "CP_C_PHY_WAREHOUSE_ECODE26")
    private String cpCPhyWarehouseEcode26;
    @JSONField(name = "CP_C_PHY_WAREHOUSE_ENAME26")
    private String cpCPhyWarehouseEname26;
    @JSONField(name = "RANK26")
    private String rank26;

    @JSONField(name = "CP_C_PHY_WAREHOUSE_ID27")
    private Long cpCPhyWarehouseId27;
    @JSONField(name = "CP_C_PHY_WAREHOUSE_ECODE27")
    private String cpCPhyWarehouseEcode27;
    @JSONField(name = "CP_C_PHY_WAREHOUSE_ENAME27")
    private String cpCPhyWarehouseEname27;
    @JSONField(name = "RANK27")
    private String rank27;

    @JSONField(name = "CP_C_PHY_WAREHOUSE_ID28")
    private Long cpCPhyWarehouseId28;
    @JSONField(name = "CP_C_PHY_WAREHOUSE_ECODE28")
    private String cpCPhyWarehouseEcode28;
    @JSONField(name = "CP_C_PHY_WAREHOUSE_ENAME28")
    private String cpCPhyWarehouseEname28;
    @JSONField(name = "RANK28")
    private String rank28;

    @JSONField(name = "CP_C_PHY_WAREHOUSE_ID29")
    private Long cpCPhyWarehouseId29;
    @JSONField(name = "CP_C_PHY_WAREHOUSE_ECODE29")
    private String cpCPhyWarehouseEcode29;
    @JSONField(name = "CP_C_PHY_WAREHOUSE_ENAME29")
    private String cpCPhyWarehouseEname29;
    @JSONField(name = "RANK29")
    private String rank29;

    @JSONField(name = "CP_C_PHY_WAREHOUSE_ID30")
    private Long cpCPhyWarehouseId30;
    @JSONField(name = "CP_C_PHY_WAREHOUSE_ECODE30")
    private String cpCPhyWarehouseEcode30;
    @JSONField(name = "CP_C_PHY_WAREHOUSE_ENAME30")
    private String cpCPhyWarehouseEname30;
    @JSONField(name = "RANK30")
    private String rank30;
}