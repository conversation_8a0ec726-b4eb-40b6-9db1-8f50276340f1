package com.jackrain.nea.st.model.enums;

import lombok.Getter;

import java.util.Objects;

/**
 * <AUTHOR>
 * @Date 2022/6/20 15:05
 */
public enum SubmitStatusEnum {

    NO_SUBMIT(1, "未提交"),
    SUBMIT(2,"已提交");


    @Getter
    private Integer key;
    @Getter
    private String desc;

    SubmitStatusEnum(Integer key, String desc) {
        this.key = key;
        this.desc = desc;
    }

    public static SubmitStatusEnum getByKey(Integer key) {
        for (SubmitStatusEnum current : values()) {
            if (Objects.equals(current.getKey(), key)) {
                return current;
            }
        }
        return null;
    }
}
