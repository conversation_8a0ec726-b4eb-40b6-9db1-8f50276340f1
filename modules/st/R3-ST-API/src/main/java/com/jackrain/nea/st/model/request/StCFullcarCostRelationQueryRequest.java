package com.jackrain.nea.st.model.request;

import lombok.Data;

import java.io.Serializable;

/**
 * 整车报价关系查询请求
 *
 * <AUTHOR>
 * @Date 2025/7/15
 * @Description 整车报价关系查询请求
 * @Version 1.0
 */
@Data
public class StCFullcarCostRelationQueryRequest implements Serializable {

    /**
     * 实体仓id
     */
    private Long cpCPhyWarehouseId;
    
    /**
     * 物流公司id
     */
    private Long cpCLogisticsId;
}
