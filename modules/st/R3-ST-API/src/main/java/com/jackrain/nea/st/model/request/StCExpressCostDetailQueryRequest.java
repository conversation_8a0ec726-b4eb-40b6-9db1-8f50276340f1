package com.jackrain.nea.st.model.request;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 快运报价明细查询请求
 */
@Data
public class StCExpressCostDetailQueryRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 快运报价设置ID
     */
    private Long expressCostId;

    /**
     * 重量
     */
    private BigDecimal weight;

    /**
     * 省份ID
     */
    private Long provinceId;
}
