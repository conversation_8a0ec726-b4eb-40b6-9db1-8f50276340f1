package com.jackrain.nea.st.model.result;

import com.jackrain.nea.st.model.table.StCUnfullcarCost;
import com.jackrain.nea.st.model.table.StCUnfullcarCostItem;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR> lin yu
 * @date : 2022/8/2 下午12:03
 * @describe :
 */
@Data
public class StCUnfullcarCostQueryResult implements Serializable {

    private List<StCUnfullcarCost> unfullcarCostList;

    private List<StCUnfullcarCostItem>  unfullcarCostItemList;
}
