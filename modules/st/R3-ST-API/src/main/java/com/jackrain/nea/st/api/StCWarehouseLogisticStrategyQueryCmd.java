package com.jackrain.nea.st.api;

import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.st.model.request.StCWarehouseLogisticStrategyItemQueryRequest;
import com.jackrain.nea.st.model.request.StCWarehouseLogisticStrategyQueryRequest;
import com.jackrain.nea.st.model.result.StCWarehouseLogisticStrategyQueryResult;
import com.jackrain.nea.st.model.result.StCWarehouseLogisticStrategyResult;
import com.jackrain.nea.st.model.table.StCWarehouseLogisticStrategy;
import com.jackrain.nea.sys.domain.ValueHolderV14;

import java.util.List;
import java.util.Map;

/**
 *
 */
public interface StCWarehouseLogisticStrategyQueryCmd {

    /**
     * @param request
     * @return
     * @throws NDSException
     */
    ValueHolderV14<List<StCWarehouseLogisticStrategy>> queryLogisticStrategyByDetail(
            StCWarehouseLogisticStrategyItemQueryRequest request) throws NDSException;

    /**
     * 根据仓库id、物流公司查询仓库物流设置
     * @param request
     * @return
     * @throws NDSException
     */
    ValueHolderV14<List<StCWarehouseLogisticStrategyResult>> queryLogisticStrategyByWarehouseAndLogistics(
            StCWarehouseLogisticStrategyItemQueryRequest request) throws NDSException;

    ValueHolderV14<StCWarehouseLogisticStrategyQueryResult> queryLogisticStrategyByWarehouseId(
            StCWarehouseLogisticStrategyQueryRequest request) throws NDSException;

}
