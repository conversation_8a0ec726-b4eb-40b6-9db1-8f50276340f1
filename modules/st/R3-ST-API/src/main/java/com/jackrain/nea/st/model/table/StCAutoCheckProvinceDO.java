package com.jackrain.nea.st.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.*;
import com.jackrain.nea.st.model.base.SubBaseModel;
import lombok.Data;

@TableName(value = "st_c_autocheck_province")
@Data
public class StCAutoCheckProvinceDO extends SubBaseModel {
    @JSO<PERSON>ield(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    @J<PERSON>NField(name = "ST_C_AUTOCHECK_ID")
    private Long stCAutocheckId;

    @JSONField(name = "CP_C_REGION_PROVINCE_ID")
    private Long cpCRegionProvinceId;

    @JSONField(name = "CP_C_REGION_PROVINCE_ECODE")
    private String cpCRegionProvinceEcode;

    @<PERSON><PERSON>NField(name = "CP_C_REGION_PROVINCE_ENAME")
    private String cpCRegionProvinceEname;

    @JSONField(name = "<PERSON>W<PERSON>RE<PERSON>ME")
    private String ownerename;

    @JSO<PERSON>ield(name = "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ME")
    private String modifierename;
}