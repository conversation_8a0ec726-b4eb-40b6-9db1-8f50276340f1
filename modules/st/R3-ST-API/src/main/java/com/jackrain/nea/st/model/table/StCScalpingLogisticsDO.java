package com.jackrain.nea.st.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.*;
import com.jackrain.nea.st.model.base.SubBaseModel;
import lombok.Data;

@TableName(value = "st_c_scalping_logistics")
@Data
public class StCScalpingLogisticsDO extends SubBaseModel {
    @J<PERSON>NField(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    @J<PERSON>NField(name = "ST_C_SCALPING_ID")
    private Long stCScalpingId;

    @JSONField(name = "CP_C_LOGISTICS_ID")
    private Long cpCLogisticsId;

    @JSONField(name = "CP_C_LOGISTICS_ECODE")
    private String cpCLogisticsEcode;

    @J<PERSON><PERSON>ield(name = "CP_C_LOGISTICS_ENAME")
    private String cpCLogisticsEname;

    @J<PERSON>NField(name = "<PERSON>N<PERSON>")
    private Long rank;

    @J<PERSON>NField(name = "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>")
    private String ownerename;

    @J<PERSON><PERSON>ield(name = "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>")
    private String modifierename;
}