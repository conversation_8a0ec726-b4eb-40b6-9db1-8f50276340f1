package com.jackrain.nea.st.api;

import com.jackrain.nea.st.model.vo.StCShopLogisticStrategyImpVo;
import com.jackrain.nea.sys.Command;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;

import java.util.List;

/**
 * @program: r3-st
 * @description: 店铺物流设置导入
 * @author: caomalai
 * @create: 2022-06-23 15:34
 **/
public interface StCShopLogisticStrategyImportCmd extends Command {
    ValueHolderV14 queryTemplateDownloadUrl();

    ValueHolderV14 importInvoiceList(List<StCShopLogisticStrategyImpVo> invoiceImpVos, User user);
}
