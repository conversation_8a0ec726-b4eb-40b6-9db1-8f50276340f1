package com.jackrain.nea.st.api;

import com.jackrain.nea.st.model.table.StCPreOccupyWarehousePriority;
import com.jackrain.nea.sys.Command;
import com.jackrain.nea.sys.domain.ValueHolderV14;

/**
 * @ClassName StCPreOccupyWarehousePriorityQueryCmd
 * @Description 仓优先
 * <AUTHOR>
 * @Date 2025/3/4 09:25
 * @Version 1.0
 */
public interface StCPreOccupyWarehousePriorityQueryCmd extends Command {

    ValueHolderV14<StCPreOccupyWarehousePriority> queryByWarehouseEcode(String warehouseEcode);
}
