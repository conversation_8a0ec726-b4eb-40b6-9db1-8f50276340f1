package com.jackrain.nea.st.model.common;

/**
 * <AUTHOR> zhuxing
 * @Date : 2022-07-07 19:45
 * @Description : 策略常量类
 **/
public class StCConstants {

    /**
     * 商品价格策略 商品范围:1-全部商品，2-自定义商品
     */
    public final static Integer ST_C_PRICE_PRO_RANGE_1 = 1;
    public final static Integer ST_C_PRICE_PRO_RANGE_2 = 2;

    /**
     * 商品价格策略 价格取值类型:1-按商品基价下限
     */
    public final static Integer ST_C_PRICE_PRICE_TYPE_1 = 1;


    /**
     * 商品价格策略明细 价格取值类型:1-按商品基价下限，2-自定义商品最低成交价
     */
    public final static Integer ST_C_PRICE_ITEM_PRICE_TYPE_1 = 1;
    public final static Integer ST_C_PRICE_ITEM_PRICE_TYPE_2 = 2;


    /**
     * 商品价格策略排除明细 识别规则:1-商品标题关联字 2-商品编码 3-平台商品ID
     */
    public final static Integer ST_C_PRICE_EXCLUDE_ITEM_1 = 1;
    public final static Integer ST_C_PRICE_EXCLUDE_ITEM_2 = 2;
    public final static Integer ST_C_PRICE_EXCLUDE_ITEM_3 = 3;

    /**
     * 关键字快递拦截策略缓存key
     */
    public static final String ST_C_KEYWORDS_INTERCEPT_STRATEGY = "st:keywords:intercept:strategy:";
}
