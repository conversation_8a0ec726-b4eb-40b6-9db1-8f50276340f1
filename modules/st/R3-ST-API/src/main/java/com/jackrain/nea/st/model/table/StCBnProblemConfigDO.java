package com.jackrain.nea.st.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jackrain.nea.es.annotations.Field;
import com.jackrain.nea.es.constans.FieldType;
import com.jackrain.nea.st.model.base.SubBaseModel;
import lombok.Data;

/**
 * @ClassName StCBnProblemConfigDO
 * @Description 班牛问题清单
 * <AUTHOR>
 * @Date 2024/11/11 14:49
 * @Version 1.0
 */
@TableName(value = "st_c_bn_problem_config")
@Data
public class StCBnProblemConfigDO extends SubBaseModel {
    private static final long serialVersionUID = 2058821340247293345L;

    @JSONField(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    /**
     * 问题类型
     */
    @JSONField(name = "PROBLEM_TEXT")
    private String problemText;

    /**
     * 客户要求是否有
     */
    @JSONField(name = "IS_CUSTOMER_REQUEST")
    private Integer isCustomerRequest;

    /**
     * 商品编码是否有
     */
    @JSONField(name = "IS_SKU_CODE")
    private Integer isSkuCode;

    /**
     * 附件是否必填
     */
    @JSONField(name = "IS_URL")
    private Integer isUrl;

    /**
     * 批量是否支持
     */
    @JSONField(name = "IS_BATCH")
    private Integer isBatch;

    /**
     * 是否取消退换货单
     */
    @JSONField(name = "IS_CANCEL_RETURN")
    private Integer isCancelReturn;

    @JSONField(name = "ISACTIVE")
    @Field(type = FieldType.Keyword)
    private String isactive;

    @JSONField(name = "SELLER_REMARK")
    private String sellerRemark;
}
