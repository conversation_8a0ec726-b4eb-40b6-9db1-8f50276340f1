package com.jackrain.nea.st.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.*;
import com.jackrain.nea.st.model.base.SubBaseModel;
import lombok.Data;

import java.math.BigDecimal;

@TableName(value = "st_c_price_item")
@Data
public class StCPriceItemDO extends SubBaseModel {
    @JSONField(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    @JSONField(name = "ST_C_PRICE_ID")
    private Long stCPriceId;

    @JSONField(name = "PS_C_PRO_ID")
    private Long psCProId;

    @JSONField(name = "PS_C_PRO_ECODE")
    private String psCProEcode;

    @JSONField(name = "PS_C_PRO_ENAME")
    private String psCProEname;

    @J<PERSON>NField(name = "QTY_LOWPRICE")
    private BigDecimal qtyLowprice;

    @JSONField(name = "PRICE_SALE")
    private BigDecimal priceSale;

    @JSONField(name = "ACTIVE_PRICE")
    private BigDecimal activePrice;

    @JSONField(name = "PRICE_LIST")
    private BigDecimal priceList;

    @JSONField(name = "OWNERENAME")
    private String ownerename;

    @JSONField(name = "MODIFIERENAME")
    private String modifierename;

    @JSONField(name = "PRICE_TYPE")
    private Integer priceType;
}