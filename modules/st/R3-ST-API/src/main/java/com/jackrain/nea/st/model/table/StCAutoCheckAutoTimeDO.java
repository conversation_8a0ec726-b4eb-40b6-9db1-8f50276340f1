package com.jackrain.nea.st.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.*;
import com.jackrain.nea.st.model.base.SubBaseModel;
import lombok.Data;

@TableName(value = "st_c_autocheck_auto_time")
@Data
public class StCAutoCheckAutoTimeDO extends SubBaseModel {
    @JSONField(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    @JSONField(name = "ST_C_AUTOCHECK_ID")
    private Long stCAutocheckId;

    @JSONField(name = "START_TIME")
    private String startTime;

    @JSONField(name = "END_TIME")
    private String endTime;



}