package com.jackrain.nea.st.model.result;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;

/**
 * @program: ryytn-st-v3.0
 * @description: 增值服务档案结果返回
 * @author: haiyang
 * @create: 2023-11-02 11:53
 **/
@Data
public class StAddedServiceStrategyDocResult implements Serializable {

    @JSONField(name = "ID")
    private Long id;

    @JSONField(name = "ADDED_TYPE_CODE")
    private String addedTypeCode;

    @JSONField(name = "ADDED_TYPE_NAME")
    private String addedTypeName;
}
