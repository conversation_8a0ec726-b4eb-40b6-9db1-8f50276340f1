package com.jackrain.nea.st.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jackrain.nea.es.annotations.Document;
import com.jackrain.nea.es.annotations.Field;
import com.jackrain.nea.es.constans.FieldType;
import com.jackrain.nea.st.model.base.SubBaseModel;
import com.jackrain.nea.sys.domain.BaseModel;
import lombok.Data;

import java.util.Date;

/**
 * 多代条码策略-规则
 */
@TableName(value = "st_c_barcode_replace_rule")
@Data
@Document(index = "st_c_barcode_replace_rule", type = "st_c_barcode_replace_rule")
public class StCBarcodeReplaceRule extends BaseModel {
    /**
     * id
     */
    @JSONField(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "id", type = IdType.INPUT)
    @Field(type = FieldType.Long)
    private Long id;

    /**
     * 多代条码策略id
     */
    @JSONField(name = "ST_C_BARCODE_REPLACE_ID")
    @Field(type = FieldType.Long)
    private Long stCBarcodeReplaceId;

    /**
     * 多代skuid
     */
    @JSONField(name = "PS_C_SKU_ID")
    @Field(type = FieldType.Long)
    private Long psCSkuId;

    /**
     * 多代sku编码
     */
    @JSONField(name = "PS_C_SKU_CODE")
    @Field(type = FieldType.Keyword)
    private String psCSkuCode;

    /**
     * 多代sku名称
     */
    @JSONField(name = "PS_C_SKU_NAME")
    @Field(type = FieldType.Keyword)
    private String psCSkuName;

    /**
     * 是否卡片款。1:是；0:否
     */
    @JSONField(name = "IS_CARD")
    @Field(type = FieldType.Integer)
    private Integer isCard;

    /**
     * 优先级
     */
    @JSONField(name = "LEVEL")
    @Field(type = FieldType.Integer)
    private Integer level;

}