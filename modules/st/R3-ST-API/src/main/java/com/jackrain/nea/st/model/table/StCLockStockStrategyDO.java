package com.jackrain.nea.st.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.*;
import com.jackrain.nea.es.annotations.Document;
import com.jackrain.nea.es.annotations.Field;
import com.jackrain.nea.es.constans.FieldType;
import com.jackrain.nea.st.model.base.SubBaseModel;
import lombok.Data;

import java.util.Date;

@TableName(value = "st_c_lock_stock_strategy")
@Data
@Document(index = "st_c_lock_stock_strategy",type = "st_c_lock_stock_strategy")
public class StCLockStockStrategyDO extends SubBaseModel {
    @JSONField(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "id", type = IdType.INPUT)
    @Field(type = FieldType.Long)
    private Long id;

    @JSONField(name = "PLAN_NAME")
    @Field(type = FieldType.Keyword)
    private String planName;

    @JSONField(name = "PLAN_ID")
    @Field(type = FieldType.Keyword)
    private String planId;

    @JSONField(name = "RANK")
    @Field(type = FieldType.Integer)
    private Integer rank;

    @JSONField(name = "DELER_ID")
    @Field(type = FieldType.Long)
    private Long delerId;

    @JSONField(name = "DELENAME")
    @Field(type = FieldType.Keyword)
    private String delename;

    @JSONField(name = "DELNAME")
    @Field(type = FieldType.Keyword)
    private String delname;

    @JSONField(name = "DEL_TIME")
    @Field(type = FieldType.Date)
    private Date delTime;

    @JSONField(name = "OWNERENAME")
    @Field(type = FieldType.Keyword)
    private String ownerename;

    @JSONField(name = "MODIFIERENAME")
    @Field(type = FieldType.Keyword)
    private String modifierename;

    @JSONField(name = "CHECKID")
    @Field(type = FieldType.Long)
    private Long checkid;

    @JSONField(name = "CHECKENAME")
    @Field(type = FieldType.Keyword)
    private String checkename;

    @JSONField(name = "CHECKNAME")
    @Field(type = FieldType.Keyword)
    private String checkname;

    @JSONField(name = "CHECKTIME")
    @Field(type = FieldType.Date)
    private Date checktime;

    @JSONField(name = "REVERSE_ID")
    @Field(type = FieldType.Long)
    private Long reverseId;

    @JSONField(name = "REVERSE_ENAME")
    @Field(type = FieldType.Keyword)
    private String reverseEname;

    @JSONField(name = "REVERSE_NAME")
    @Field(type = FieldType.Keyword)
    private String reverseName;

    @JSONField(name = "REVERSE_TIME")
    @Field(type = FieldType.Date)
    private Date reverseTime;

    @JSONField(name = "ESTATUS")
    @Field(type = FieldType.Integer)
    private Integer estatus;

    @JSONField(name = "FINISHID")
    @Field(type = FieldType.Long)
    private Long finishid;

    @JSONField(name = "FINISHENAME")
    @Field(type = FieldType.Keyword)
    private String finishename;

    @JSONField(name = "FINISHNAME")
    @Field(type = FieldType.Keyword)
    private String finishname;

    @JSONField(name = "FINISHTIME")
    @Field(type = FieldType.Date)
    private Date finishtime;

    @JSONField(name = "PLAN_DESC")
    @Field(type = FieldType.Keyword)
    private String planDesc;
}