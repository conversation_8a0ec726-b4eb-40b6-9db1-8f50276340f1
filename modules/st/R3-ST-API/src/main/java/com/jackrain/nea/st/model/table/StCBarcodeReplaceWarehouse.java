package com.jackrain.nea.st.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jackrain.nea.es.annotations.Document;
import com.jackrain.nea.es.annotations.Field;
import com.jackrain.nea.es.constans.FieldType;
import com.jackrain.nea.st.model.base.SubBaseModel;
import com.jackrain.nea.sys.domain.BaseModel;
import lombok.Data;

import java.util.Date;

/**
 * 多代条码策略-条码仓
 */
@TableName(value = "st_c_barcode_replace_warehouse")
@Data
@Document(index = "st_c_barcode_replace_warehouse", type = "st_c_barcode_replace_warehouse")
public class StCBarcodeReplaceWarehouse extends BaseModel {
    /**
     * id
     */
    @JSONField(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "id", type = IdType.INPUT)
    @Field(type = FieldType.Long)
    private Long id;

    /**
     * 多代条码策略id
     */
    @JSONField(name = "ST_C_BARCODE_REPLACE_ID")
    @Field(type = FieldType.Long)
    private Long stCBarcodeReplaceId;

    /**
     * 省id
     */
    @JSONField(name = "CP_C_REGION_PROVINCE_ID")
    @Field(type = FieldType.Long)
    private Long cpCRegionProvinceId;

    /**
     * 省编码
     */
    @JSONField(name = "CP_C_REGION_PROVINCE_ECODE")
    @Field(type = FieldType.Keyword)
    private String cpCRegionProvinceEcode;

    /**
     * 实体仓id
     */
    @JSONField(name = "CP_C_PHY_WAREHOUSE_ID")
    @Field(type = FieldType.Long)
    private Long cpCPhyWarehouseId;

    /**
     * 实体仓编码
     */
    @JSONField(name = "CP_C_PHY_WAREHOUSE_ECODE")
    @Field(type = FieldType.Keyword)
    private String cpCPhyWarehouseEcode;

    /**
     * 实体仓名称
     */
    @JSONField(name = "CP_C_PHY_WAREHOUSE_ENAME")
    @Field(type = FieldType.Keyword)
    private String cpCPhyWarehouseEname;

    /**
     * 优先级
     */
    @JSONField(name = "LEVEL")
    @Field(type = FieldType.Integer)
    private Integer level;

}