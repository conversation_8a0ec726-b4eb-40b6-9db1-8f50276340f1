package com.jackrain.nea.st.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.*;
import com.jackrain.nea.es.annotations.Document;
import com.jackrain.nea.es.annotations.Field;
import com.jackrain.nea.es.constans.FieldType;
import com.jackrain.nea.st.model.base.SubBaseModel;
import lombok.Data;

import java.util.Date;

@TableName(value = "st_c_lock_stock_strategy_item")
@Data
@Document(index = "st_c_lock_stock_strategy",type = "st_c_lock_stock_strategy_item")
public class StCLockStockStrategyItemDO extends SubBaseModel {
    @JSONField(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "id", type = IdType.INPUT)
    @Field(type = FieldType.Long)
    private Long id;

    @JSONField(name = "ST_C_LOCK_STOCK_STRATEGY_ID")
    @Field(type = FieldType.Long)
    private Long stCLockStockStrategyId;

    @JSONField(name = "CP_C_SHOP_ID")
    @Field(type = FieldType.Long)
    private Long cpCShopId;

    @JSONField(name = "CP_C_SHOP_TITLE")
    @Field(type = FieldType.Keyword)
    private String cpCShopTitle;

    @JSONField(name = "CP_C_SHOP_ECODE")
    @Field(type = FieldType.Keyword)
    private String cpCShopEcode;

    @JSONField(name = "LOCK_BTIME")
    @Field(type = FieldType.Date)
    private Date lockBtime;

    @JSONField(name = "LOCK_ETIME")
    @Field(type = FieldType.Date)
    private Date lockEtime;

    @JSONField(name = "OWNERENAME")
    @Field(type = FieldType.Keyword)
    private String ownerename;

    @JSONField(name = "MODIFIERENAME")
    @Field(type = FieldType.Keyword)
    private String modifierename;
    /**
     * 方案名称
     */
    @JSONField(name = "PLAN_NAME")
    @Field(type = FieldType.Keyword)
    private String planName;
    /**
     * 主表状态
     */
    @JSONField(name = "STATUS")
    @Field(type = FieldType.Integer)
    private Integer status;
    /**
     * 优先级
     */
    @JSONField(name = "RANK")
    @Field(type = FieldType.Integer)
    private Integer rank;
    /**
     * 主表创建时间
     */
    @JSONField(name = "MAIN_CREATIONDATE")
    @Field(type = FieldType.Date)
    private Date mainCreationdate;
}