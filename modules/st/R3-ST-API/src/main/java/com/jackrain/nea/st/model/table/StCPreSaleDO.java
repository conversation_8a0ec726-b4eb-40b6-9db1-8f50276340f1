package com.jackrain.nea.st.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.*;
import com.jackrain.nea.es.annotations.Document;
import com.jackrain.nea.es.annotations.Field;
import com.jackrain.nea.es.constans.FieldType;
import com.jackrain.nea.st.model.base.SubBaseModel;
import lombok.Data;

import java.util.Date;

@TableName(value = "ST_C_PRE_SALE")
@Data
@Document(index = "st_c_pre_sale",type = "st_c_pre_sale")
public class StCPreSaleDO extends SubBaseModel {
    @JSONField(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "id", type = IdType.INPUT)
    @Field(type = FieldType.Long)
    private Long id;

    @JSONField(name = "ename")
    @Field(type = FieldType.Keyword)
    private String ename;

    @JSONField(name = "cp_c_shop_id")
    @Field(type = FieldType.Long)
    private Long cpCShopId;

    @JSONField(name = "CP_C_SHOP_TITLE")
    @Field(type = FieldType.Keyword)
    private String cpCShopTitle;

    @JSONField(name = "DAY_TYPE")
    @Field(type = FieldType.Integer)
    private Integer dayType;

    @JSONField(name = "PRE_SALE_WAY")
    @Field(type = FieldType.Keyword)
    private String preSaleWay;
       
    @JSONField(name = "BEGIN_TIME")
    @Field(type = FieldType.Date)
    private Date beginTime;

    @JSONField(name = "END_TIME")
    @Field(type = FieldType.Date)
    private Date endTime;

    @JSONField(name = "PRE_SALE_STATUS")
    @Field(type = FieldType.Integer)
    private Integer preSaleStatus;


}