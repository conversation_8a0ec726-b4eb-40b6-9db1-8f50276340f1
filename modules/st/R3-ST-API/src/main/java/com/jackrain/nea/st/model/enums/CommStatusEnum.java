package com.jackrain.nea.st.model.enums;

/**
 * @Desc :
 * <AUTHOR> xiWen
 * @Date : 2022/5/31
 */
public enum CommStatusEnum {

    YES(1, "Y", "是"),

    NO(0, "N", "否");

    Integer v;

    String c;

    String t;

    public Integer intVal() {
        return this.v;
    }

    public String charVal() {
        return this.c;
    }

    public String desc() {
        return this.t;
    }

    public boolean isTrue(Integer v) {
        return this.v.equals(v);
    }

    public boolean isTrue(String v) {
        return this.c.equals(v);
    }

    CommStatusEnum(Integer v1, String v2, String v3) {
        this.v = v1;
        this.c = v2;
        this.t = v3;
    }
}
