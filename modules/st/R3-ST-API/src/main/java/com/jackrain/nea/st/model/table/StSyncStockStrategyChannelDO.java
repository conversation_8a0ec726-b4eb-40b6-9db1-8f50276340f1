package com.jackrain.nea.st.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.*;
import com.jackrain.nea.st.model.base.SubBaseModel;
import lombok.Data;

/**
 * <AUTHOR>
 * @Date 2019/12/5 14:37
 */
@TableName(value = "ST_C_SYNC_STOCK_STRATEGY_CHANNEL")
@Data
public class StSyncStockStrategyChannelDO extends SubBaseModel {
    @JSONField(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    @JSONField(name = "ST_C_CHANNEL_STRATEGY_ID")
    private Long stCChannelStrategyId;

    @JSONField(name = "ST_C_SYNC_STOCK_STRATEGY_ID")
    private Long stCSyncStockStrategyId;


}
