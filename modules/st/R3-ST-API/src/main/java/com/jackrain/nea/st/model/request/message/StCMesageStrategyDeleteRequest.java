package com.jackrain.nea.st.model.request.message;


import com.alibaba.fastjson.annotation.JSONField;
import com.burgeon.r3.sg.core.model.request.SgR3BaseRequest;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @Auther: chen<PERSON>
 * @Date: 2022-09-05 10:11
 * @Description:
 */

@Data
public class StCMesageStrategyDeleteRequest extends SgR3BaseRequest implements Serializable {

    @JSONField(name = "ST_C_MESSAGE_STRATEGY")
    private List<Long> mainIds;

    @JSONField(name = "ST_C_MESSAGE_STRATEGY_ITEM")
    private List<Long> itemIds;
}
