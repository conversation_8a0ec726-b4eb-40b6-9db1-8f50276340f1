package com.jackrain.nea.st.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.*;
import com.jackrain.nea.st.model.base.SubBaseModel;
import lombok.Data;

@TableName(value = "st_c_send_plan_item")
@Data
public class StCSendPlanItemDO extends SubBaseModel {
    @J<PERSON><PERSON>ield(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    @J<PERSON><PERSON>ield(name = "ST_C_SEND_PLAN_ID")
    private Long stCSendPlanId;

    @JSONField(name = "ST_C_SEND_RULE_ID")
    private Long stCSendRuleId;

    @JSONField(name = "ST_C_SEND_RULE_ECODE")
    private String stCSendRuleEcode;

    @JSONField(name = "ST_C_SEND_RULE_ENAME")
    private String stCSendRuleEname;

    @J<PERSON><PERSON>ield(name = "REMAR<PERSON>")
    private String remark;

    @J<PERSON><PERSON>ield(name = "RAN<PERSON>")
    private Long rank;

    @J<PERSON><PERSON>ield(name = "<PERSON>W<PERSON>RENA<PERSON>")
    private String ownerename;

    @JSONField(name = "MODIFIERENAME")
    private String modifierename;
}