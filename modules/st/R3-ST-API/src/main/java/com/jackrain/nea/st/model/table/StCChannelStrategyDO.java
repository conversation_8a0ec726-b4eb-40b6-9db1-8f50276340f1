package com.jackrain.nea.st.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.*;
import com.jackrain.nea.st.model.base.SubBaseModel;
import lombok.Data;

import java.util.Date;

/**
 * 需求变更，即将要废弃删除的表（转移到组织中心的渠道仓维护）
 * <AUTHOR>
 * @Date 2019/12/4 11:14
 */
@TableName(value = "ST_C_CHANNEL_STRATEGY")
@Data
@Deprecated
public class StCChannelStrategyDO extends SubBaseModel {
    @JSONField(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    @JSONField(name = "CP_C_ORG_CHANNEL_ID")
    private Long cpCOrgChannelId;

    @JSONField(name = "CP_C_ORG_CHANNEL_ECODE")
    private String cpCOrgChannelEcode;

    @JSONField(name = "CP_C_ORG_CHANNEL_ENAME")
    private String cpCOrgChannelEname;

    @JSONField(name = "TYPE")
    private String type;

    @JSONField(name = "DELID")
    @TableField(exist = false)
    private Long delid;

    @JSONField(name = "DELENAME")
    @TableField(exist = false)
    private String delename;

    @JSONField(name = "DELNAME")
    @TableField(exist = false)
    private String delname;

    @JSONField(name = "DEL_TIME")
    @TableField(exist = false)
    private Date delTime;

}
