package com.jackrain.nea.st.model.result;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;

/**
 * @author: 汪聿森
 * @Date: Created in 2020-02-19 15:46
 * @Description : 同步库存归属仓库属性
 */
@Data
public class OwnershipWarehouseSetSyncResult  implements Serializable {
    @JSONField(name = "CP_C_STORE_ID")
    private Long cpCStoreId;

    @JSONField(name = "CP_C_STORE_ECODE")
    private String cpCStoreEcode;

    @J<PERSON>NField(name = "CP_C_STORE_ENAME")
    private String cpCStoreEname;

    @J<PERSON>NField(name = "CHANNEL_TYPE")
    private String channelType;
}
