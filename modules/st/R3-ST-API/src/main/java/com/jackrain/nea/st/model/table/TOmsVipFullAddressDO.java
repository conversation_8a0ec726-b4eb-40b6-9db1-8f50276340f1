package com.jackrain.nea.st.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.*;
import com.jackrain.nea.st.model.base.SubBaseModel;
import lombok.Data;

/**
 * vip地址编码对应省市区表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021-08-24 17:00:19
 */
@TableName(value = "t_omsvipfulladdress")
@Data
public class TOmsVipFullAddressDO extends SubBaseModel {
    /**
     * ID
     */
    @JSONField(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;
    /**
     * 地址编码
     */
    @JSONField(name = "ADDRESS_CODE")
    private String addressCode;
    /**
     * 地址中文名(街道全称)
     */
    @JSONField(name = "ADDRESS_NAME")
    private String addressName;
    /**
     * 省市区街道全称
     */
    @JSONField(name = "FULL_NAME")
    private String fullName;
    /**
     * 父级地址代码
     */
    @JSONField(name = "PARENT_CODE")
    private String parentCode;
    /**
     * 省名称
     */
    @JSONField(name = "PROVINCE_NAME")
    private String provinceName;
    /**
     * 市名称
     */
    @JSONField(name = "CITY_NAME")
    private String cityName;
    /**
     * 区名称
     */
    @JSONField(name = "COUNTY_NAME")
    private String countyName;
    /**
     * 修改人
     */
    @JSONField(name = "MODIFIERENAME")
    private String modifierename;
    /**
     * 创建人
     */
    @JSONField(name = "OWNERENAME")
    private String ownerename;

}
