package com.jackrain.nea.st.model.componentJsonModel;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @Descroption 弹出多选jsonList对象
 * <AUTHOR>
 * @Date 2019/5/8 15:14
 */
@Data
public class PopCheckboxListsJson implements Serializable {
    @JSONField(name = "result")
    private List<PopCheckboxResultJson> popCheckboxResultJson;
}
