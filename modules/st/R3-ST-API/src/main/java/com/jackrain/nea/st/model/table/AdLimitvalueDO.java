package com.jackrain.nea.st.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.*;
import com.jackrain.nea.es.annotations.Document;
import com.jackrain.nea.es.annotations.Field;
import com.jackrain.nea.es.constans.FieldType;
import com.jackrain.nea.st.model.base.SubBaseModel;
import lombok.Data;

@TableName(value = "AD_LIMITVALUE")
@Data
@Document(index = "ad_limitvalue",type = "ad_limitvalue")
public class AdLimitvalueDO extends SubBaseModel {
    @JSONField(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "id", type = IdType.INPUT)
    @Field(type = FieldType.Long)
    private Long id;

    @JSONField(name = "AD_LIMITVALUE_GROUP_ID")
    @Field(type = FieldType.Long)
    private Long adLimitvalueGroupId;

    @JSONField(name = "VALUE")
    @Field(type = FieldType.Keyword)
    private String value;

    @JSONField(name = "DESCRIPTION")
    @Field(type = FieldType.Keyword)
    private String description;

    @JSONField(name = "ORDERNO")
    @Field(type = FieldType.Long)
    private Long orderno;

    @JSONField(name = "COMMENTS")
    @Field(type = FieldType.Keyword)
    private String comments;

    @JSONField(name = "VALUETYPE")
    @Field(type = FieldType.Keyword)
    private String valuetype;

    @JSONField(name = "CSSCLASS")
    @Field(type = FieldType.Keyword)
    private String cssclass;

    @JSONField(name = "WATERMARKIMG")
    @Field(type = FieldType.Keyword)
    private String watermarkimg;

    @JSONField(name = "MODIFIERENAME")
    @Field(type = FieldType.Keyword)
    private String modifierename;

    @JSONField(name = "OWNERENAME")
    @Field(type = FieldType.Keyword)
    private String ownerename;
}