package com.jackrain.nea.st.model.result;

import com.alibaba.fastjson.annotation.JSONField;
import com.jackrain.nea.es.annotations.Field;
import com.jackrain.nea.es.constans.FieldType;
import com.jackrain.nea.st.model.table.StCExpressPriceStrategyItemDO;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @title $
 * @Description 描述
 * @Date date()$ $
 * @Copyright 2019-2020
 */
@Data
public class StCExpressPriceStrategyQueryResult extends StCExpressPriceStrategyItemDO implements Serializable {

    @JSONField(name = "cp_c_phy_warehouse_id")
    private Long cpCPhyWarehouseId;

    @JSONField(name = "cp_c_logistics_id")
    private Long cpCLogisticsId;

}
