package com.jackrain.nea.st.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.*;
import com.jackrain.nea.st.model.base.SubBaseModel;
import lombok.Data;

@TableName(value = "st_c_warehouse_logistics_rank")
@Data
public class StCWarehouseLogisticsRankDO extends SubBaseModel {
    @JSONField(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    @JSONField(name = "ST_C_WAREHOUSE_LOGISTICS_ID")
    private Long stCWarehouseLogisticsId;

    @JSO<PERSON>ield(name = "CP_C_REGION_PROVINCE_ID")
    private Long cpCRegionProvinceId;

    @JSONField(name = "CP_C_REGION_PROVINCE_ECODE")
    private String cpCRegionProvinceEcode;

    @JSONField(name = "CP_C_REGION_PROVINCE_ENAME")
    private String cpCRegionProvinceEname;

    @JSONField(name = "CP_C_REGION_CITY_ID")
    private Long cpCRegionCityId;

    @JSONField(name = "CP_C_REGION_CITY_ECODE")
    private String cpCRegionCityEcode;

    @JSONField(name = "CP_C_REGION_CITY_ENAME")
    private String cpCRegionCityEname;

    @JSONField(name = "CP_C_REGION_AREA_ID")
    private Long cpCRegionAreaId;

    @JSONField(name = "CP_C_REGION_AREA_ECODE")
    private String cpCRegionAreaEcode;

    @JSONField(name = "CP_C_REGION_AREA_ENAME")
    private String cpCRegionAreaEname;

    @JSONField(name = "CP_C_REGION_COUNTRY_ENAME")
    private String cpCRegionCountryEname;

    @JSONField(name = "OWNERENAME")
    private String ownerename;

    @JSONField(name = "MODIFIERENAME")
    private String modifierename;

    @JSONField(name = "LOGISTICS_RANK")
    private String logisticsRank;
}