package com.jackrain.nea.st.model.vo;

import lombok.Data;

import java.util.Map;

/**
 * @program: r3-st
 * @description: 订单自动审核排除商品
 * @author: caomalai
 * @create: 2022-07-01 15:19
 **/
@Data
public class StCAutocheckExcludeProductImpVo {
    public static final String cellStr = "cell_";
    public static final String rowStr = "row_";

    private Integer proLevel;
    private String proLevelName;
    private String psCProdimItemEname;
    private String psCProEcode;
    private String psCSkuEcode;
    private String platformSkuId;
    private String platformNumiid;
    private String remark;
    //行号
    private int rowNum;

    private String desc;

    public static StCAutocheckExcludeProductImpVo importCreate(int index, StCAutocheckExcludeProductImpVo impVo, Map<String, String> columnMap) {
        try {
            //商品级别
            impVo.setProLevelName(columnMap.get(rowStr + index + cellStr + 0));
            //一级分类ID
            impVo.setPsCProdimItemEname(columnMap.get(rowStr + index + cellStr + 1));
            //商品编码
            impVo.setPsCProEcode(columnMap.get(rowStr + index + cellStr + 2));
            //平台条码
            impVo.setPsCSkuEcode(columnMap.get(rowStr + index + cellStr + 3));
            //平台商品ID
            impVo.setPlatformNumiid(columnMap.get(rowStr + index + cellStr + 4));
            //平台条码ID
            impVo.setPlatformSkuId(columnMap.get(rowStr + index + cellStr + 5));
            //平台商品ID
            impVo.setRemark(columnMap.get(rowStr + index + cellStr + 6));

        } catch (Exception e) {

        }
        impVo.setRowNum(index + 1);
        return impVo;
    }
}
