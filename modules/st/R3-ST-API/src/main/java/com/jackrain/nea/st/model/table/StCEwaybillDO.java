package com.jackrain.nea.st.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.*;
import com.jackrain.nea.st.model.base.SubBaseModel;
import lombok.Data;

import java.util.Date;

@TableName(value = "st_c_ewaybill")
@Data
public class StCEwaybillDO extends SubBaseModel {
    @JSONField(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    @JSONField(name = "ENAME")
    private String ename;

    @J<PERSON><PERSON>ield(name = "CP_C_SHOP_TITLE")
    private String cpCShopTitle;

    @J<PERSON>NField(name = "IS_USE")
    private Integer isUse;

    @JSONField(name = "OWNERENAME")
    private String ownerename;

    @JSONField(name = "MODIFIERENAME")
    private String modifierename;

    @JSONField(name = "CP_C_SHOP_ID")
    private String cpCShopId;

    @JSONField(name = "DELID")
    private Long delid;

    @J<PERSON>NField(name = "DELENAME")
    private String delename;

    @JSONField(name = "DELNAME")
    private String delname;

    @JSONField(name = "DEL_TIME")
    private Date delTime;
}