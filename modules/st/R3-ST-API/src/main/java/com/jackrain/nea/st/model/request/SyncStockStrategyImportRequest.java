package com.jackrain.nea.st.model.request;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 库存同步策略导入请求对象
 *
 * <AUTHOR>
 * @since 2020-08-18
 * create at : 2020-08-18 20:56
 */
@Data
public class SyncStockStrategyImportRequest implements Serializable {

    /**
     * 明细id
     */
    private Long id;
    /**
     * 渠道编码
     */
    private String channelCode;
    /**
     * 店铺编码
     */
    private String shopCode;
    /**
     * 库存比例
     */
    private BigDecimal stockRate;
    /**
     * 安全库存
     */
    private BigDecimal lowStock;
    /**
     * 渠道优先级
     */
    private Integer channelPriority;
}
