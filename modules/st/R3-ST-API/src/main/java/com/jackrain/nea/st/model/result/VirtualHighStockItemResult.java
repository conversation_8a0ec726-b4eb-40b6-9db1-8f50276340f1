package com.jackrain.nea.st.model.result;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @Date: 2020/7/7 5:41 下午
 * @Desc:
 */
@Data
public class VirtualHighStockItemResult implements Serializable {
    /**
     * 条码id
     */
    private String skuId;

    /**
     * 平台商品id
     */
    private String proId;

    /**
     * 中台skuid
     */
    private String psCSkuId;

    /**
     * 条码
     */
    private String skuEcode;

    /**
     * 中台商品id
     */
    private Long psCProId;

    /**
     * 商品编码
     */
    private String proEcode;

    /**
     * 商品名称
     */
    private String proEname;

    /**
     * 虚高失效值
     */
    private BigDecimal expireValue;

    /**
     * 虚高货值
     */
    private BigDecimal virtualHighValue;
    /**
     * 创建时间
     */
    private Date creationdate;
    /**
     * 主表创建时间
     */
    private Date mainCreationdate;
}
