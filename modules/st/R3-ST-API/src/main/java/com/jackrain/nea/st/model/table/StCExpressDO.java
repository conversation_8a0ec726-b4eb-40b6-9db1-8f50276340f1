package com.jackrain.nea.st.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.*;
import com.jackrain.nea.es.annotations.Document;
import com.jackrain.nea.es.annotations.Field;
import com.jackrain.nea.es.constans.FieldType;
import com.jackrain.nea.st.model.base.SubBaseModel;
import lombok.Data;

import java.util.Date;

@TableName(value = "ST_C_EXPRESS")
@Data
@Document(index = "st_c_express",type = "st_c_express")
public class StCExpressDO extends SubBaseModel {
    @JSONField(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "id", type = IdType.INPUT)
    @Field(type = FieldType.Long)
    private Long id;

    @JSONField(name = "ECODE")
    @Field(type = FieldType.Keyword)
    private String ecode;

    @JSONField(name = "ENAME")
    @Field(type = FieldType.Keyword)
    private String ename;

    @JSONField(name = "BILL_TYPE")
    @Field(type = FieldType.Integer)
    private Integer billType;

    @JSONField(name = "CP_C_SHOP_ID")
    @Field(type = FieldType.Keyword)
    private String cpCShopId;

    @JSONField(name = "CP_C_SHOP_TITLE")
    @Field(type = FieldType.Keyword)
    private String cpCShopTitle;

    @JSONField(name = "DAY_TYPE")
    @Field(type = FieldType.Integer)
    private Integer dayType;

    @JSONField(name = "BEGIN_TIME")
    @Field(type = FieldType.Date)
    private Date beginTime;

    @JSONField(name = "END_TIME")
    @Field(type = FieldType.Date)
    private Date endTime;

    @JSONField(name = "CP_C_PHY_WAREHOUSE_ECODE")
    @Field(type = FieldType.Keyword)
    private String cpCPhyWarehouseEcode;

    @JSONField(name = "CP_C_PHY_WAREHOUSE_ENAME")
    @Field(type = FieldType.Keyword)
    private String cpCPhyWarehouseEname;

    @JSONField(name = "CP_C_LOGISTICS_ECODE")
    @Field(type = FieldType.Keyword)
    private String cpCLogisticsEcode;

    @JSONField(name = "CP_C_LOGISTICS_ENAME")
    @Field(type = FieldType.Keyword)
    private String cpCLogisticsEname;

    @JSONField(name = "RANK")
    @Field(type = FieldType.Long)
    private Long rank;

    @JSONField(name = "AREA_TYPE")
    @Field(type = FieldType.Integer)
    private Integer areaType;

    @JSONField(name = "CP_C_PLATFORM_ID")
    @Field(type = FieldType.Long)
    private Long cpCPlatformId;

    @JSONField(name = "CP_C_PLATFORM_ECODE")
    @Field(type = FieldType.Keyword)
    private String cpCPlatformEcode;

    @JSONField(name = "CP_C_PLATFORM_ENAME")
    @Field(type = FieldType.Keyword)
    private String cpCPlatformEname;

    @JSONField(name = "BILL_STATUS")
    @Field(type = FieldType.Integer)
    private Integer billStatus;

    @JSONField(name = "REMARK")
    @Field(type = FieldType.Keyword)
    private String remark;

    @JSONField(name = "OWNERENAME")
    @Field(type = FieldType.Keyword)
    private String ownerename;

    @JSONField(name = "MODIFIERENAME")
    @Field(type = FieldType.Keyword)
    private String modifierename;

    @JSONField(name = "DELID")
    @Field(type = FieldType.Long)
    private Long delid;

    @JSONField(name = "DELENAME")
    @Field(type = FieldType.Keyword)
    private String delename;

    @JSONField(name = "DELNAME")
    @Field(type = FieldType.Keyword)
    private String delname;

    @JSONField(name = "DEL_TIME")
    @Field(type = FieldType.Date)
    private Date delTime;

    @JSONField(name = "CHECKID")
    @Field(type = FieldType.Long)
    private Long checkid;

    @JSONField(name = "CHECKENAME")
    @Field(type = FieldType.Keyword)
    private String checkename;

    @JSONField(name = "CHECKNAME")
    @Field(type = FieldType.Keyword)
    private String checkname;

    @JSONField(name = "CHECKTIME")
    @Field(type = FieldType.Date)
    private Date checktime;

    @JSONField(name = "REVERSE_ID")
    @Field(type = FieldType.Long)
    private Long reverseId;

    @JSONField(name = "REVERSE_ENAME")
    @Field(type = FieldType.Keyword)
    private String reverseEname;

    @JSONField(name = "REVERSE_NAME")
    @Field(type = FieldType.Keyword)
    private String reverseName;

    @JSONField(name = "REVERSE_TIME")
    @Field(type = FieldType.Date)
    private Date reverseTime;

    @JSONField(name = "ESTATUS")
    @Field(type = FieldType.Integer)
    private Integer estatus;

    @JSONField(name = "FINISHID")
    @Field(type = FieldType.Long)
    private Long finishid;

    @JSONField(name = "FINISHENAME")
    @Field(type = FieldType.Keyword)
    private String finishename;

    @JSONField(name = "FINISHNAME")
    @Field(type = FieldType.Keyword)
    private String finishname;

    @JSONField(name = "FINISHTIME")
    @Field(type = FieldType.Date)
    private Date finishtime;

    @JSONField(name = "CP_C_PHY_WAREHOUSE_ID_SET")
    @Field(type = FieldType.Keyword)
    private String cpCPhyWarehouseIdSet;

    @JSONField(name = "CP_C_LOGISTICS_ID_SET")
    @Field(type = FieldType.Keyword)
    private String cpCLogisticsIdSet;

    @JSONField(name = "CP_C_PLATFORM_ID_SET")
    @Field(type = FieldType.Keyword)
    private String cpCPlatformIdSet;

    @JSONField(name = "CHANNEL_TYPE")
    @Field(type = FieldType.Keyword)
    private String channelType;
}