package com.jackrain.nea.st.model.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @Author: 黄世新
 * @Date: 2022/6/9 下午3:29
 * @Version 1.0
 */
@Getter
@AllArgsConstructor
public enum AppointDimensionEnum {

    //1.品项,2.商品编码,3.商品标题,4.平台商品ID
    PRODUCT_TERM(1, "品项"),
    GOODS_CODE(2, "商品编码"),
    GOODS_TITLE(3, "商品标题"),
    PT_GOODS_ID(3, "商品标题");
    private Integer code;
    private String message;


    public static String getMessage(Integer code) {
        AppointDimensionEnum[] values = AppointDimensionEnum.values();
        for (AppointDimensionEnum value : values) {
            if (value.getCode().equals(code)) {
                return value.getMessage();
            }
        }
        return "";
    }
}
