package com.jackrain.nea.st.model.base;

import com.alibaba.fastjson.annotation.JSONField;
import com.jackrain.nea.es.annotations.Field;
import com.jackrain.nea.es.constans.FieldType;
import com.jackrain.nea.sys.domain.BaseModel;

import lombok.Data;

@Data
public class SubBaseModel extends BaseModel {

    @JSONField(name = "MOD<PERSON>IERE<PERSON><PERSON>")
    @Field(type = FieldType.Keyword)
    private String modifierename;

    @JSONField(name = "OWNERENAME")
    @Field(type = FieldType.Keyword)
    private String ownerename;
}