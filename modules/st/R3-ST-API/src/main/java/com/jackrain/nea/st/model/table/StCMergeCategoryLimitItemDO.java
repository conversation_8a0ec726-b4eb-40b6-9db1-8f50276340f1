package com.jackrain.nea.st.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jackrain.nea.es.annotations.Field;
import com.jackrain.nea.es.constans.FieldType;
import com.jackrain.nea.st.model.base.SubBaseModel;
import lombok.Data;

@TableName(value = "st_c_merge_category_limit_item")
@Data
public class StCMergeCategoryLimitItemDO extends SubBaseModel {
    @JSONField(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "id", type = IdType.INPUT)
    @Field(type = FieldType.Long)
    private Long id;

    @JSONField(name = "ST_C_MERGE_ORDER_ID")
    @Field(type = FieldType.Long)
    private Long stCMergeOrderId;

    @JSONField(name = "PS_C_PRO_CATEGORY_ID")
    @Field(type = FieldType.Long)
    private Long psCProCategoryId;

    @JSONField(name = "PS_C_PRO_CATEGORY_ECODE")
    @Field(type = FieldType.Keyword)
    private String psCProCategoryEcode;

    @JSONField(name = "PS_C_PRO_CATEGORY_ENAME")
    @Field(type = FieldType.Keyword)
    private String psCProCategoryEname;

    @JSONField(name = "SINGLE_PACKAGE_MAX_NUM")
    @Field(type = FieldType.Integer)
    private Integer singlePackageMaxNum;

    @JSONField(name = "OTHER_CATEGORY_MAX_NUM")
    @Field(type = FieldType.Integer)
    private Integer otherCategoryMaxNum;
}