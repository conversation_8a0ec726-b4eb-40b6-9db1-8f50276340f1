package com.jackrain.nea.st.api;

import com.jackrain.nea.st.model.table.StCOrderPushDelayStrategy;

import java.util.List;

/**
 * <AUTHOR>
 * @Description 订单推单延迟策略查询
 * @Date 16:32 2020/7/1
 **/
public interface StCOrderPushDelayStrategyQueryCmd {

    /**
     * <AUTHOR>
     * @Description 根据店铺id查询可用订单推单延迟策略
     * @Date 16:43 2020/7/1
     * @param shopId
     * @return com.jackrain.nea.st.model.table.StCOrderPushDelayStrategy
     **/
    StCOrderPushDelayStrategy queryOrderPushDelayStrategy(Long shopId);

    /**
     * <AUTHOR>
     * @Description 查询所有可用订单推单延迟策略
     * @Date 16:44 2020/7/1
     * @param
     * @return java.util.List<com.jackrain.nea.st.model.table.StCOrderPushDelayStrategy>
     **/
    List<StCOrderPushDelayStrategy> queryOrderPushDelayStrategyAll();
}
