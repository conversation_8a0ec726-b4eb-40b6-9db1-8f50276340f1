package com.jackrain.nea.st.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.*;
import com.jackrain.nea.es.annotations.Document;
import com.jackrain.nea.es.annotations.Field;
import com.jackrain.nea.es.constans.FieldType;
import com.jackrain.nea.st.model.base.SubBaseModel;
import lombok.Data;

/**
 * <AUTHOR>
 * @title $
 * @Description 描述
 * @Date 2020/06/10
 * @Copyright 2019-2020
 */
@TableName(value = "ST_C_WAREHOUSE_SKU")
@Data
@Document(index = "st_c_warehouse",type = "st_c_warehouse_sku")
public class StCWarehouseSkuDO extends SubBaseModel {
    @JSONField(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "id", type = IdType.INPUT)
    @Field(type = FieldType.Long)
    private Long id;

    @JSONField(name = "ST_C_WAREHOUSE_ID")
    @Field(type = FieldType.Long)
    private Long stCWarehouseId;

    @JSONField(name = "PS_C_SKU_ID")
    @Field(type = FieldType.Long)
    private Long psCSkuId;

    @JSONField(name = "PS_C_SKU_ECODE")
    @Field(type = FieldType.Keyword)
    private String psCSkuEcode;

    @JSONField(name = "NUM")
    @Field(type = FieldType.Integer)
    private Integer num;

}
