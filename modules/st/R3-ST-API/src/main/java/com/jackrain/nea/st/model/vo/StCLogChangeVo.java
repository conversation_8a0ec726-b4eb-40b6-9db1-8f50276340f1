package com.jackrain.nea.st.model.vo;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * @description 日志生成传入VO
 * @author:洪艺安
 * @since: 2020/2/14
 * @create at : 2020/2/14 11:05
 */
@Data
public class StCLogChangeVo implements Serializable {
    /**
     * 主表表明
     */
    @JSONField(name = "TABLE_NAME")
    private String tableName;

    /**
     * 操作类型
     */
    @J<PERSON>NField(name = "OPERATION_TYPE")
    private String operationType;

    /**
     * 明细集合
     */
    @JSONField(name = "ITEMS_TABLE_NAME")
    private List<String> itemsTableName;

    /**
     * 明细新增/删除操作展示键
     */
    @JSONField(name = "ITEM_OPERATION_KEY")
    private Map<String,String> itemOperationKey;

    /**
     * 日志监控字段
     */
    @JSONField(name = "FOCUS_COLOMN_MAP")
    private Map<String,List<String>> focusColomnMap;

    public StCLogChangeVo(String tableName, String operationType, List<String> itemsTableName, Map<String,String> itemOperationKey, Map<String,List<String>> focusColomnMap){
        this.tableName = tableName;
        this.itemsTableName = itemsTableName;
        this.operationType = operationType;
        this.itemOperationKey = itemOperationKey;
        this.focusColomnMap = focusColomnMap;
    }
}
