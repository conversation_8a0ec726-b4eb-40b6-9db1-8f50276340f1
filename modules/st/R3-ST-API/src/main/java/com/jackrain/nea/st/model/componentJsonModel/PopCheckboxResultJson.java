package com.jackrain.nea.st.model.componentJsonModel;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;

/**
 * @Descroption 弹出多选jsonValue对象
 * <AUTHOR>
 * @Date 2019/5/8 15:14
 */
@Data
public class PopCheckboxResultJson implements Serializable {
    @JSONField(name = "screen_string")
    private String screenString;

    @JSONField(name = "screen")
    private long screen;

    @JSONField(name = "exclude")
    private boolean exclude;

    @JSONField(name = "id_list")
    private long[] idList;

}
