package com.jackrain.nea.st.model.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @create 2020-06-17
 * @desc 派单规则类型
 **/
public enum SendRuleEtype {
    /**
     * 按收货地址
     */
    SHIPPING_ADDRESS("1", "收货地址"),
    /**
     * 按分仓比例
     */
    BIN_RATIO("2", "分仓比例"),
    /**
     * 按唯品会
     */
    VIP("3", "唯品会");

    @Getter
    private String etype;

    @Getter
    private String etypeName;

    SendRuleEtype(String etype, String etypeName) {
        this.etype = etype;
        this.etypeName = etypeName;
    }

    public static String getNameByValue(String value) {
        String operationName = "";
        for (SendRuleEtype e : SendRuleEtype.values()) {
            if (e.getEtype().equals(value)) {
                operationName = e.getEtypeName();
                break;
            }
        }
        return operationName;
    }
}