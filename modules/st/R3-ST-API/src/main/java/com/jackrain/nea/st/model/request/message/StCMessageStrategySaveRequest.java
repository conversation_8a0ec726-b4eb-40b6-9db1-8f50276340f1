package com.jackrain.nea.st.model.request.message;


import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * @Auther: chenhao
 * @Date: 2022-09-12 13:33
 * @Description:
 */

@Data
public class StCMessageStrategySaveRequest implements Serializable {

    private Long id;

    private String remark;

    private Long version;

    private String ownerename;

    private String modifierename;

    private String strategyName;

    private String orderBusinessType;

    private String orderBusinessTypeCode;

    private String orderBusinessTypeName;

    private String orderStatus;

    private String orderStatusCode;

    private Date warehouseDeliveryTime;

    private Date lastTime;
}
