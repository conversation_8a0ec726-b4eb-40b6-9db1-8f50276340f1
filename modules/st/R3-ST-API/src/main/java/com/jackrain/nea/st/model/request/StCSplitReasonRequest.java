package com.jackrain.nea.st.model.request;

import com.alibaba.fastjson.annotation.JSONField;
import com.jackrain.nea.st.model.table.StCSplitReasonConfigDO;
import com.jackrain.nea.st.model.table.StCSplitReasonDO;
import com.jackrain.nea.st.model.table.StCSplitReasonItemDO;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @program: r3-st
 * @description: 自定义拆单
 * @author: liuwj
 * @create: 2021-05-31 15:44
 **/
@Data
public class StCSplitReasonRequest implements Serializable {

    @JSONField(name = "ST_C_SPLIT_REASON_CONFIG")
    private StCSplitReasonConfigDO stCSplitReasonConfigDO;

    @JSONField(name = "ST_C_SPLIT_REASON")
    private StCSplitReasonDO stCSplitReasonDO;

    @JSONField(name = "ST_C_SPLIT_REASON_ITEM")
    private List<StCSplitReasonItemDO> stCSplitReasonItemDOList;

    @JSONField(name = "ID")
    private Long id;

    @JSONField(name = "SPLIT_REASON_CONFIG_ID")
    private Long splitReasonConfigId;

    //系统拆单原因
    @JSONField(name = "SYSTEM_SPLIT_REASON")
    private String systemSplitReason;

    //自定义拆单原因
    @JSONField(name = "CUSTOM_REASON")
    private String customReason;

}
