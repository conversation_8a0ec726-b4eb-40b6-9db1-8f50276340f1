package com.jackrain.nea.st.model.request;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;

/**
 * Descroption 物流区域设置
 *
 * <AUTHOR>
 * @Date 2019/3/13 14:23
 */
@Data
public class StCBansAreaStrategyRequest implements Serializable {

    /**
     * 省份
     */
    @JSONField(name = "CP_C_PROVINCE_ID")
    private Long cpCProvinceId;
    /**
     * 城市
     */
    @JSONField(name = "CP_C_CITY_ID")
    private Long cpCCityId;
    /**
     * 区/县
     */
    @JSONField(name = "CP_C_REGION_AREA_ID")
    private Long cpCRegionAreaId;

    /**
     * 实体仓
     */
    @JSONField(name = "CP_C_PHY_WAREHOUSE_ID")
    private Long cpCPhyWarehouseId;

}
