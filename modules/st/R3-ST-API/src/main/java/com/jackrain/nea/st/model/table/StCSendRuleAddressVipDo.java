package com.jackrain.nea.st.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.*;
import com.jackrain.nea.st.model.base.SubBaseModel;
import lombok.Data;

/**
 * <AUTHOR>
 * @create 2020-06-15 17:04
 * @desc 订单派单规则-唯品会明细
 **/
@TableName(value = "st_c_send_rule_address_vip")
@Data
public class StCSendRuleAddressVipDo extends SubBaseModel {
    /**
     * ID
     */
    @JSONField(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    /**
     * 订单派单规则ID
     */
    @JSONField(name = "ST_C_SEND_RULE_ID")
    private Long stCSendRuleId;

    /**
     * 唯品会仓库ID
     */
    @JSONField(name = "CP_C_VIPCOM_WAHOUSE_ID")
    private Long cpCVipcomWahouseId;

    /**
     * 唯品会仓库代码
     */
    @JSONField(name = "CP_C_VIPCOM_WAHOUSE_WAREHOUSE_CODE")
    private String cpCVipcomWahouseWarehouseCode;

    /**
     * 唯品会仓库名称
     */
    @JSONField(name = "CP_C_VIPCOM_WAHOUSE_WAREHOUSE_NAME")
    private String cpCVipcomWahouseWarehouseName;

    /**
     * 仓库优先级
     */
    @JSONField(name = "WAREHOUSE_RANK")
    private String warehouseRank;
}