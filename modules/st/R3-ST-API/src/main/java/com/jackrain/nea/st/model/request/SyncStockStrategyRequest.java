package com.jackrain.nea.st.model.request;

import com.alibaba.fastjson.annotation.JSONField;
import com.jackrain.nea.st.model.table.StCSyncStockStrategyChannelDO;
import com.jackrain.nea.st.model.table.StCSyncStockStrategyDO;
import com.jackrain.nea.st.model.table.StCSyncStockStrategyItemDO;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * Descroption 店铺同步库存策略
 *
 * <AUTHOR>
 * @Date 2019/3/8 15:23
 */
@Data
public class SyncStockStrategyRequest implements Serializable {
    @J<PERSON>NField(name = "ST_C_SYNC_STOCK_STRATEGY")
    private StCSyncStockStrategyDO syncStockStrategy;
    @JSONField(name = "ST_C_SYNC_STOCK_STRATEGY_ITEM")
    private List<StCSyncStockStrategyItemDO> syncStockStrategyList;
    @JSONField(name = "ST_C_SYNC_STOCK_STRATEGY_CHANNEL")
    private List<StCSyncStockStrategyChannelDO> syncStockStrategyChannelList;
}
