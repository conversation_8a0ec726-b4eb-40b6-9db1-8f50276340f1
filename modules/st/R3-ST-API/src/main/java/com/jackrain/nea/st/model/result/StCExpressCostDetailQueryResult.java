package com.jackrain.nea.st.model.result;

import com.jackrain.nea.st.model.table.StCExpressCostItem;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 快运报价明细查询结果
 */
@Data
public class StCExpressCostDetailQueryResult implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 快运报价设置明细列表
     */
    private List<StCExpressCostItem> expressCostItemList;
}
