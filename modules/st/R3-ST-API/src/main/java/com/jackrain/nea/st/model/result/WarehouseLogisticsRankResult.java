package com.jackrain.nea.st.model.result;

import com.alibaba.fastjson.annotation.JSONField;
import com.jackrain.nea.st.model.table.StCWarehouseLogisticsRankDO;
import lombok.Data;

@Data
public class WarehouseLogisticsRankResult extends StCWarehouseLogisticsRankDO {

    @JSONField(name = "CITYLEVEL")
    private String citylevel;

    @JSONField(name = "CP_C_LOGISTICS_ID1")
    private Long cpCLogisticsId1;
    @JSONField(name = "CP_C_LOGISTICS_ECODE1")
    private String cpCLogisticsEcode1;
    @JSONField(name = "CP_C_LOGISTICS_ENAME1")
    private String cpCLogisticsEname1;
    @J<PERSON><PERSON><PERSON>(name = "RANK1")
    private String rank1;

    @JSONField(name = "CP_C_LOGISTICS_ID2")
    private Long cpCLogisticsId2;
    @JSONField(name = "CP_C_LOGISTICS_ECODE2")
    private String cpCLogisticsEcode2;
    @J<PERSON><PERSON>ield(name = "CP_C_LOGISTICS_ENAME2")
    private String cpCLogisticsEname2;
    @J<PERSON><PERSON>ield(name = "RANK2")
    private String rank2;

    @JSONField(name = "CP_C_LOGISTICS_ID3")
    private Long cpCLogisticsId3;
    @JSONField(name = "CP_C_LOGISTICS_ECODE3")
    private String cpCLogisticsEcode3;
    @JSONField(name = "CP_C_LOGISTICS_ENAME3")
    private String cpCLogisticsEname3;
    @JSONField(name = "RANK3")
    private String rank3;

    @JSONField(name = "CP_C_LOGISTICS_ID4")
    private Long cpCLogisticsId4;
    @JSONField(name = "CP_C_LOGISTICS_ECODE4")
    private String cpCLogisticsEcode4;
    @JSONField(name = "CP_C_LOGISTICS_ENAME4")
    private String cpCLogisticsEname4;
    @JSONField(name = "RANK4")
    private String rank4;

    @JSONField(name = "CP_C_LOGISTICS_ID5")
    private Long cpCLogisticsId5;
    @JSONField(name = "CP_C_LOGISTICS_ECODE5")
    private String cpCLogisticsEcode5;
    @JSONField(name = "CP_C_LOGISTICS_ENAME5")
    private String cpCLogisticsEname5;
    @JSONField(name = "RANK5")
    private String rank5;

    @JSONField(name = "CP_C_LOGISTICS_ID6")
    private Long cpCLogisticsId6;
    @JSONField(name = "CP_C_LOGISTICS_ECODE6")
    private String cpCLogisticsEcode6;
    @JSONField(name = "CP_C_LOGISTICS_ENAME6")
    private String cpCLogisticsEname6;
    @JSONField(name = "RANK6")
    private String rank6;

    @JSONField(name = "CP_C_LOGISTICS_ID7")
    private Long cpCLogisticsId7;
    @JSONField(name = "CP_C_LOGISTICS_ECODE7")
    private String cpCLogisticsEcode7;
    @JSONField(name = "CP_C_LOGISTICS_ENAME7")
    private String cpCLogisticsEname7;
    @JSONField(name = "RANK7")
    private String rank7;

    @JSONField(name = "CP_C_LOGISTICS_ID8")
    private Long cpCLogisticsId8;
    @JSONField(name = "CP_C_LOGISTICS_ECODE8")
    private String cpCLogisticsEcode8;
    @JSONField(name = "CP_C_LOGISTICS_ENAME8")
    private String cpCLogisticsEname8;
    @JSONField(name = "RANK8")
    private String rank8;

    @JSONField(name = "CP_C_LOGISTICS_ID9")
    private Long cpCLogisticsId9;
    @JSONField(name = "CP_C_LOGISTICS_ECODE9")
    private String cpCLogisticsEcode9;
    @JSONField(name = "CP_C_LOGISTICS_ENAME9")
    private String cpCLogisticsEname9;
    @JSONField(name = "RANK9")
    private String rank9;

    @JSONField(name = "CP_C_LOGISTICS_ID10")
    private Long cpCLogisticsId10;
    @JSONField(name = "CP_C_LOGISTICS_ECODE10")
    private String cpCLogisticsEcode10;
    @JSONField(name = "CP_C_LOGISTICS_ENAME10")
    private String cpCLogisticsEname10;
    @JSONField(name = "RANK10")
    private String rank10;

    @JSONField(name = "CP_C_LOGISTICS_ID11")
    private Long cpCLogisticsId11;
    @JSONField(name = "CP_C_LOGISTICS_ECODE11")
    private String cpCLogisticsEcode11;
    @JSONField(name = "CP_C_LOGISTICS_ENAME11")
    private String cpCLogisticsEname11;
    @JSONField(name = "RANK11")
    private String rank11;

    @JSONField(name = "CP_C_LOGISTICS_ID12")
    private Long cpCLogisticsId12;
    @JSONField(name = "CP_C_LOGISTICS_ECODE12")
    private String cpCLogisticsEcode12;
    @JSONField(name = "CP_C_LOGISTICS_ENAME12")
    private String cpCLogisticsEname12;
    @JSONField(name = "RANK12")
    private String rank12;

    @JSONField(name = "CP_C_LOGISTICS_ID13")
    private Long cpCLogisticsId13;
    @JSONField(name = "CP_C_LOGISTICS_ECODE13")
    private String cpCLogisticsEcode13;
    @JSONField(name = "CP_C_LOGISTICS_ENAME13")
    private String cpCLogisticsEname13;
    @JSONField(name = "RANK13")
    private String rank13;

    @JSONField(name = "CP_C_LOGISTICS_ID14")
    private Long cpCLogisticsId14;
    @JSONField(name = "CP_C_LOGISTICS_ECODE14")
    private String cpCLogisticsEcode14;
    @JSONField(name = "CP_C_LOGISTICS_ENAME14")
    private String cpCLogisticsEname14;
    @JSONField(name = "RANK14")
    private String rank14;

    @JSONField(name = "CP_C_LOGISTICS_ID15")
    private Long cpCLogisticsId15;
    @JSONField(name = "CP_C_LOGISTICS_ECODE15")
    private String cpCLogisticsEcode15;
    @JSONField(name = "CP_C_LOGISTICS_ENAME15")
    private String cpCLogisticsEname15;
    @JSONField(name = "RANK15")
    private String rank15;

    @JSONField(name = "CP_C_LOGISTICS_ID16")
    private Long cpCLogisticsId16;
    @JSONField(name = "CP_C_LOGISTICS_ECODE16")
    private String cpCLogisticsEcode16;
    @JSONField(name = "CP_C_LOGISTICS_ENAME16")
    private String cpCLogisticsEname16;
    @JSONField(name = "RANK16")
    private String rank16;

    @JSONField(name = "CP_C_LOGISTICS_ID17")
    private Long cpCLogisticsId17;
    @JSONField(name = "CP_C_LOGISTICS_ECODE17")
    private String cpCLogisticsEcode17;
    @JSONField(name = "CP_C_LOGISTICS_ENAME17")
    private String cpCLogisticsEname17;
    @JSONField(name = "RANK17")
    private String rank17;

    @JSONField(name = "CP_C_LOGISTICS_ID18")
    private Long cpCLogisticsId18;
    @JSONField(name = "CP_C_LOGISTICS_ECODE18")
    private String cpCLogisticsEcode18;
    @JSONField(name = "CP_C_LOGISTICS_ENAME18")
    private String cpCLogisticsEname18;
    @JSONField(name = "RANK18")
    private String rank18;

    @JSONField(name = "CP_C_LOGISTICS_ID19")
    private Long cpCLogisticsId19;
    @JSONField(name = "CP_C_LOGISTICS_ECODE19")
    private String cpCLogisticsEcode19;
    @JSONField(name = "CP_C_LOGISTICS_ENAME19")
    private String cpCLogisticsEname19;
    @JSONField(name = "RANK19")
    private String rank19;

    @JSONField(name = "CP_C_LOGISTICS_ID20")
    private Long cpCLogisticsId20;
    @JSONField(name = "CP_C_LOGISTICS_ECODE20")
    private String cpCLogisticsEcode20;
    @JSONField(name = "CP_C_LOGISTICS_ENAME20")
    private String cpCLogisticsEname20;
    @JSONField(name = "RANK20")
    private String rank20;

    @JSONField(name = "CP_C_LOGISTICS_ID21")
    private Long cpCLogisticsId21;
    @JSONField(name = "CP_C_LOGISTICS_ECODE21")
    private String cpCLogisticsEcode21;
    @JSONField(name = "CP_C_LOGISTICS_ENAME21")
    private String cpCLogisticsEname21;
    @JSONField(name = "RANK21")
    private String rank21;

    @JSONField(name = "CP_C_LOGISTICS_ID22")
    private Long cpCLogisticsId22;
    @JSONField(name = "CP_C_LOGISTICS_ECODE22")
    private String cpCLogisticsEcode22;
    @JSONField(name = "CP_C_LOGISTICS_ENAME22")
    private String cpCLogisticsEname22;
    @JSONField(name = "RANK22")
    private String rank22;

    @JSONField(name = "CP_C_LOGISTICS_ID23")
    private Long cpCLogisticsId23;
    @JSONField(name = "CP_C_LOGISTICS_ECODE23")
    private String cpCLogisticsEcode23;
    @JSONField(name = "CP_C_LOGISTICS_ENAME23")
    private String cpCLogisticsEname23;
    @JSONField(name = "RANK23")
    private String rank23;

    @JSONField(name = "CP_C_LOGISTICS_ID24")
    private Long cpCLogisticsId24;
    @JSONField(name = "CP_C_LOGISTICS_ECODE24")
    private String cpCLogisticsEcode24;
    @JSONField(name = "CP_C_LOGISTICS_ENAME24")
    private String cpCLogisticsEname24;
    @JSONField(name = "RANK24")
    private String rank24;

    @JSONField(name = "CP_C_LOGISTICS_ID25")
    private Long cpCLogisticsId25;
    @JSONField(name = "CP_C_LOGISTICS_ECODE25")
    private String cpCLogisticsEcode25;
    @JSONField(name = "CP_C_LOGISTICS_ENAME25")
    private String cpCLogisticsEname25;
    @JSONField(name = "RANK25")
    private String rank25;

    @JSONField(name = "CP_C_LOGISTICS_ID26")
    private Long cpCLogisticsId26;
    @JSONField(name = "CP_C_LOGISTICS_ECODE26")
    private String cpCLogisticsEcode26;
    @JSONField(name = "CP_C_LOGISTICS_ENAME26")
    private String cpCLogisticsEname26;
    @JSONField(name = "RANK26")
    private String rank26;

    @JSONField(name = "CP_C_LOGISTICS_ID27")
    private Long cpCLogisticsId27;
    @JSONField(name = "CP_C_LOGISTICS_ECODE27")
    private String cpCLogisticsEcode27;
    @JSONField(name = "CP_C_LOGISTICS_ENAME27")
    private String cpCLogisticsEname27;
    @JSONField(name = "RANK27")
    private String rank27;

    @JSONField(name = "CP_C_LOGISTICS_ID28")
    private Long cpCLogisticsId28;
    @JSONField(name = "CP_C_LOGISTICS_ECODE28")
    private String cpCLogisticsEcode28;
    @JSONField(name = "CP_C_LOGISTICS_ENAME28")
    private String cpCLogisticsEname28;
    @JSONField(name = "RANK28")
    private String rank28;

    @JSONField(name = "CP_C_LOGISTICS_ID29")
    private Long cpCLogisticsId29;
    @JSONField(name = "CP_C_LOGISTICS_ECODE29")
    private String cpCLogisticsEcode29;
    @JSONField(name = "CP_C_LOGISTICS_ENAME29")
    private String cpCLogisticsEname29;
    @JSONField(name = "RANK29")
    private String rank29;

    @JSONField(name = "CP_C_LOGISTICS_ID30")
    private Long cpCLogisticsId30;
    @JSONField(name = "CP_C_LOGISTICS_ECODE30")
    private String cpCLogisticsEcode30;
    @JSONField(name = "CP_C_LOGISTICS_ENAME30")
    private String cpCLogisticsEname30;
    @JSONField(name = "RANK30")
    private String rank30;
}