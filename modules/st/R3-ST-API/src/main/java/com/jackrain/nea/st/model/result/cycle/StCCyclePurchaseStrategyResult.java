package com.jackrain.nea.st.model.result.cycle;

import com.jackrain.nea.st.model.table.cycle.StCCyclePurchaseStrategy;
import com.jackrain.nea.st.model.table.cycle.StCCyclePurchaseStrategyItem;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @Auther: chenhao
 * @Date: 2022-09-05 14:02
 * @Description:
 */

@Data
public class StCCyclePurchaseStrategyResult implements Serializable {

    private StCCyclePurchaseStrategy main;

    private List<StCCyclePurchaseStrategyItem> itemList;
}
