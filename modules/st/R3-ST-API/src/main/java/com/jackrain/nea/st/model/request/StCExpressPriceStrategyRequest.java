package com.jackrain.nea.st.model.request;

import com.alibaba.fastjson.annotation.JSONField;
import com.jackrain.nea.st.model.table.StCExpressPriceStrategyDO;
import com.jackrain.nea.st.model.table.StCExpressPriceStrategyItemDO;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/6/9 21:00
 * @Description TODO
 * @Version 1.0
 */
@Data
public class StCExpressPriceStrategyRequest implements Serializable {
    @J<PERSON>NField(name = "ST_C_EXPRESS_PRICE_STRATEGY")
    private StCExpressPriceStrategyDO stCExpressPriceStrategyDO;

    @JSO<PERSON>ield(name="ST_C_EXPRESS_PRICE_STRATEGY_ITEM")
    private List<StCExpressPriceStrategyItemDO> stCExpressPriceStrategyItemDOList;
}
