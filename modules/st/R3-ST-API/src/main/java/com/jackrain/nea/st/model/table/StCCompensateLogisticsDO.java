package com.jackrain.nea.st.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.*;
import com.jackrain.nea.st.model.base.SubBaseModel;
import lombok.Data;

import java.math.BigDecimal;

@TableName(value = "st_c_compensate_logistics")
@Data
public class StCCompensateLogisticsDO extends SubBaseModel {
    @JSONField(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    @JSONField(name = "ST_C_LOGISTICS_COMPENSATE_ID")
    private Long stCLogisticsCompensateId;

    @J<PERSON><PERSON>ield(name = "CP_C_LOGISTICS_ID")
    private Long cpCLogisticsId;

    @JSONField(name = "CP_C_LOGISTICS_ECODE")
    private String cpCLogisticsEcode;

    @JSONField(name = "CP_C_LOGISTICS_ENAME")
    private String cpCLogisticsEname;

    @JSONField(name = "COMPENSATE_TYPE")
    private Integer compensateType;

    @J<PERSON><PERSON>ield(name = "COMPENSATE_STANDARD")
    private String compensateStandard;

    @JSONField(name = "MULTIPLE")
    private BigDecimal multiple;

    @JSONField(name = "SETTLEMENTPRICE")
    private BigDecimal settlementprice;

    @JSONField(name = "OWNERENAME")
    private String ownerename;

    @JSONField(name = "MODIFIERENAME")
    private String modifierename;
}