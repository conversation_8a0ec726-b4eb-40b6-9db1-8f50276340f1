package com.jackrain.nea.st.model.table.message;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.*;
import com.jackrain.nea.st.model.base.SubBaseModel;
import lombok.Data;

import java.util.Date;

@TableName(value = "st_c_send_message")
@Data
public class StCSendMessage extends SubBaseModel {
    private static final long serialVersionUID = 1846890344279789321L;
    @JSONField(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    @JSONField(name = "REMARK")
    private String remark;

    @JSONField(name = "VERSION")
    private Long version;

    @JSONField(name = "OWNERENAME")
    private String ownerename;

    @JSONField(name = "MODIFIERENAME")
    private String modifierename;

    @JSONField(name = "CP_C_SHOP_ID")
    private Long cpCShopId;

    @JSO<PERSON>ield(name = "CP_C_SHOP_CODE")
    private String cpCShopCode;

    @JSONField(name = "CP_C_SHOP_NAME")
    private String cpCShopName;

    @JSONField(name = "SELLER_NICK")
    private String sellerNick;

    @JSONField(name = "TID")
    private String tid;

    @JSONField(name = "BILL_NO")
    private String billNo;

    @JSONField(name = "OAID")
    private String oaid;

    @JSONField(name = "ORDER_BUSINESS_TYPE")
    private String orderBusinessType;

    @JSONField(name = "ORDER_STATUS")
    private Integer orderStatus;

    @JSONField(name = "MOBILE")
    private String mobile;

    @JSONField(name = "CP_C_LOGISTICS_ID")
    private Long cpCLogisticsId;

    @JSONField(name = "CP_C_LOGISTICS_ECODE")
    private String cpCLogisticsEcode;

    @JSONField(name = "CP_C_LOGISTICS_ENAME")
    private String cpCLogisticsEname;

    @JSONField(name = "LOGISTICS_NUMBER")
    private String logisticsNumber;

    @JSONField(name = "ST_C_MESSAGE_STRATEGY_ID")
    private Long stCMessageStrategyId;

    @JSONField(name = "BURGEON_PLATFORM_SIGNATURE_NUMBER")
    private String burgeonPlatformSignatureNumber;

    @JSONField(name = "BURGEON_MESSAGE_TEMPLATE_NUMBER")
    private String burgeonMessageTemplateNumber;

    @JSONField(name = "STATUS")
    private Integer status;

    @JSONField(name = "INTERFACE_STATUS")
    private Integer interfaceStatus;

    @JSONField(name = "FAIL_REASON")
    private String failReason;

    @JSONField(name = "SEND_TIME")
    private Date sendTime;

    @JSONField(name = "MESSAGE")
    private String message;

    @JSONField(name = "CP_C_PLATFORM_ID")
    private Long cpCPlatformId;

    @JSONField(name = "FAIL_COUNT")
    private Integer failCount;

    /**
     * 仓库出库时间
     */
    @JSONField(name = "SCAN_TIME")
    private Date scanTime;

    /**
     * 仓库编码
     */
    @JSONField(name = "CP_C_PHY_WAREHOUSE_ENAME")
    private String cpCPhyWarehouseEname;

    /**
     * 仓库名称
     */
    @JSONField(name = "CP_C_PHY_WAREHOUSE_ECODE")
    private String cpCPhyWarehouseEcode;


    /**
     * 仓库ID
     */
    @JSONField(name = "CP_C_PHY_WAREHOUSE_ID")
    private Long cpCPhyWarehouseId;

    /**
     * 省份ID
     */
    @JSONField(name = "PROVINCE_ID")
    private Long provinceId;

    /**
     * 城市ID
     */
    @JSONField(name = "CITY_ID")
    private Long cityId;
}