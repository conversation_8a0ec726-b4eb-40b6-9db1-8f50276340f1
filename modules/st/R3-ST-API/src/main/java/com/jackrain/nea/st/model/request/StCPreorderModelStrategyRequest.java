package com.jackrain.nea.st.model.request;

import com.alibaba.fastjson.annotation.JSONField;
import com.jackrain.nea.st.model.table.StCPreorderFieldStrategyDO;
import com.jackrain.nea.st.model.table.StCPreorderItemStrategyDO;
import com.jackrain.nea.st.model.table.StCPreorderModelStrategyDO;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @ClassName StCPreorderModelStrategyRequest
 * @Description 发货单导入模板
 * <AUTHOR>
 * @Date 2022/12/23 10:20
 * @Version 1.0
 */
@Data
public class StCPreorderModelStrategyRequest implements Serializable {
    private static final long serialVersionUID = -277584093839370537L;

    /**
     * 导入模板
     */
    @JSONField(name = "ST_C_PREORDER_MODEL_STRATEGY")
    private StCPreorderModelStrategyDO modelStrategyDO;

    /**
     * 模板明细
     */
    @JSONField(name = "ST_C_PREORDER_FIELD_STRATEGY")
    private List<StCPreorderFieldStrategyDO> fieldStrategyDOS;

    /**
     * 模板商品明细
     */
    @JSONField(name = "ST_C_PREORDER_ITEM_STRATEGY")
    private List<StCPreorderItemStrategyDO> itemStrategyDOS;

    private Long objid;
}
