package com.jackrain.nea.st.model.request;

import com.alibaba.fastjson.annotation.JSONField;
import com.jackrain.nea.st.model.table.StCCompensateDO;
import com.jackrain.nea.st.model.table.StCCompensateLogisticsDO;
import com.jackrain.nea.st.model.table.StCCompensateWarehouseDO;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2019/3/12 10:19
 */

@Data
public class CompensateRequest implements Serializable {

    @J<PERSON><PERSON>ield(name = "ST_C_COMPENSATE")
    private StCCompensateDO stCCompensateDO;

    @JSONField(name = "ST_C_COMPENSATE_LOGISTICS")
    private List<StCCompensateLogisticsDO> stCCompensateLogisticsDO;

    @J<PERSON>NField(name = "ST_C_COMPENSATE_WAREHOUSE")
    private List<StCCompensateWarehouseDO> stCCompensateWarehouseDO;

}
