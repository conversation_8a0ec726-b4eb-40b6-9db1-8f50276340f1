package com.jackrain.nea.st.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.*;
import com.jackrain.nea.st.model.base.SubBaseModel;
import lombok.Data;

@TableName(value = "st_c_shop_strategy_item")
@Data
public class StCShopStrategyItemDO extends SubBaseModel {
    @JSONField(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    @JSONField(name = "ST_C_SHOP_STRATEGY_ID")
    private Long stCShopStrategyId;

    @JSONField(name = "DIFFPRICESKU")
    private String diffpricesku;

    @JSONField(name = "OWNERENAME")
    private String ownerename;

    @J<PERSON>NField(name = "MODIFIERENAME")
    private String modifierename;
}