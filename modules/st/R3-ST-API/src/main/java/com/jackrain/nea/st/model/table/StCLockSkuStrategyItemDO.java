package com.jackrain.nea.st.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.*;
import com.jackrain.nea.es.annotations.Document;
import com.jackrain.nea.es.annotations.Field;
import com.jackrain.nea.es.constans.FieldType;
import com.jackrain.nea.st.model.base.SubBaseModel;
import lombok.Data;

import java.util.Date;

@TableName(value = "st_c_lock_sku_strategy_item")
@Data
@Document(index = "st_c_lock_sku_strategy",type = "st_c_lock_sku_strategy_item")
public class StCLockSkuStrategyItemDO extends SubBaseModel {
    @JSONField(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "id", type = IdType.INPUT)
    @Field(type = FieldType.Long)
    private Long id;

    @JSONField(name = "ST_C_LOCK_SKU_STRATEGY_ID")
    @Field(type = FieldType.Long)
    private Long stCLockSkuStrategyId;

    @JSONField(name = "PT_PRO_ID")
    @Field(type = FieldType.Keyword)
    private String ptProId;

    @JSONField(name = "PT_PRO_TITLE")
    @Field(type = FieldType.Keyword)
    private String ptProTitle;

    @JSONField(name = "PS_C_PRO_ID")
    @Field(type = FieldType.Long)
    private Long psCProId;

    @JSONField(name = "PS_C_PRO_ECODE")
    @Field(type = FieldType.Keyword)
    private String psCProEcode;

    @JSONField(name = "PS_C_PRO_ENAME")
    @Field(type = FieldType.Keyword)
    private String psCProEname;

    @JSONField(name = "PS_C_SKU_ID")
    @Field(type = FieldType.Long)
    private Long psCSkuId;

    @JSONField(name = "PS_C_SKU_ECODE")
    @Field(type = FieldType.Keyword)
    private String psCSkuEcode;

    @JSONField(name = "PT_SKU_ID")
    @Field(type = FieldType.Keyword)
    private String ptSkuId;

    @JSONField(name = "GBCODE")
    @Field(type = FieldType.Keyword)
    private String gbcode;

    @JSONField(name = "PS_C_CLR_ID")
    @Field(type = FieldType.Long)
    private Long psCClrId;

    @JSONField(name = "PS_C_CLR_ECODE")
    @Field(type = FieldType.Keyword)
    private String psCClrEcode;

    @JSONField(name = "PS_C_CLR_ENAME")
    @Field(type = FieldType.Keyword)
    private String psCClrEname;

    @JSONField(name = "PS_C_SIZE_ID")
    @Field(type = FieldType.Long)
    private Long psCSizeId;

    @JSONField(name = "PS_C_SIZE_ECODE")
    @Field(type = FieldType.Keyword)
    private String psCSizeEcode;

    @JSONField(name = "PS_C_SIZE_ENAME")
    @Field(type = FieldType.Keyword)
    private String psCSizeEname;

    @JSONField(name = "OWNERENAME")
    @Field(type = FieldType.Keyword)
    private String ownerename;

    @JSONField(name = "MODIFIERENAME")
    @Field(type = FieldType.Keyword)
    private String modifierename;
    /**
     * 方案名称
     */
    @JSONField(name = "PLAN_NAME")
    @Field(type = FieldType.Keyword)
    private String planName;
    /**
     * 状态
     */
    @JSONField(name = "STATUS")
    @Field(type = FieldType.Integer)
    private Integer status;
    /**
     * 开始时间
     */
    @JSONField(name = "BEGIN_TIME")
    @Field(type = FieldType.Date)
    private Date beginTime;
    /**
     * 结束时间
     */
    @JSONField(name = "END_TIME")
    @Field(type = FieldType.Date)
    private Date endTime;
    /**
     * 店铺id
     */
    @JSONField(name = "CP_C_SHOP_ID")
    @Field(type = FieldType.Long)
    private Long cpCShopId;
    /**
     * 店铺编码
     */
    @JSONField(name = "CP_C_SHOP_ECODE")
    @Field(type = FieldType.Keyword)
    private String cpCShopEcode;
    /**
     * 店铺名称
     */
    @JSONField(name = "CP_C_SHOP_TITLE")
    @Field(type = FieldType.Keyword)
    private String cpCShopTitle;
    /**
     * 主表创建时间
     */
    @JSONField(name = "MAIN_CREATIONDATE")
    @Field(type = FieldType.Date)
    private Date mainCreationdate;
}