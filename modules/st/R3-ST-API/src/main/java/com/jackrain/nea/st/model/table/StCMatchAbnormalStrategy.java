package com.jackrain.nea.st.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.*;
import com.jackrain.nea.es.annotations.Field;
import com.jackrain.nea.es.constans.FieldType;
import com.jackrain.nea.st.model.base.SubBaseModel;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.io.Serializable;

/**
 * @program: r3-st
 * @description:
 * @author: caomalai
 * @create: 2022-10-16 17:18
 **/
@ApiModel(value="发货单异常类型定义")
@Data
@TableName(value = "st_c_match_abnormal_strategy")
public class StCMatchAbnormalStrategy extends SubBaseModel implements Serializable {

    /**
     * id
     */
    @JSONField(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;


    /**
     * 识别关键字
     */
    @JSONField(name = "MATCH_KEYWORDS")
    @Field(type = FieldType.Keyword)
    private String matchKeywords;

    /**
     * 异常节点类型 1=寻源异常 2=分物流异常 3=传wms异常
     */
    @JSONField(name = "ABNORMAL_TYPE")
    @Field(type = FieldType.Integer)
    private Integer abnormalType;

    /**
     * 异常类型说明
     */
    @JSONField(name = "ABNORMAL_DESC")
    @Field(type = FieldType.Keyword)
    private String abnormalDesc;

    /**
     * 异常处理方式 1 仅打标 2 打标并卡单 3 打标并Hold单
     */
    @JSONField(name = "ABNORMAL_HANDLE_TYPE")
    @Field(type = FieldType.Integer)
    private Integer abnormalHandleType;

    /**
     * 处理时长
     */
    @JSONField(name = "ABNORMAL_HANDLE_TIME")
    @Field(type = FieldType.Long)
    private Long abnormalHandleTime;

    /**
     * 时间单位 1分钟 2小时 3天
     */
    @JSONField(name = "ABNORMAL_HANDLE_UNIT")
    @Field(type = FieldType.Long)
    private Integer abnormalHandleUnit;

}
