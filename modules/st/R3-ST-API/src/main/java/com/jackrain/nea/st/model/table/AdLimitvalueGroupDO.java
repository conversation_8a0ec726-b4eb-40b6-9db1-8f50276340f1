package com.jackrain.nea.st.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.*;
import com.jackrain.nea.es.annotations.Document;
import com.jackrain.nea.es.annotations.Field;
import com.jackrain.nea.es.constans.FieldType;
import com.jackrain.nea.st.model.base.SubBaseModel;
import lombok.Data;

@TableName(value = "AD_LIMITVALUE_GROUP")
@Data
@Document(index = "ad_limitvalue_group",type = "ad_limitvalue_group")
public class AdLimitvalueGroupDO extends SubBaseModel {
    @JSONField(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "id", type = IdType.INPUT)
    @Field(type = FieldType.Long)
    private Long id;

    @JSONField(name = "NAME")
    @Field(type = FieldType.Keyword)
    private String name;

    @JSONField(name = "COMMENTS")
    @Field(type = FieldType.Keyword)
    private String comments;

    @JSONField(name = "VALUETYPE")
    @Field(type = FieldType.Keyword)
    private String valuetype;

    @JSONField(name = "MODIFIERENAME")
    @Field(type = FieldType.Keyword)
    private String modifierename;

    @JSONField(name = "OWNERENAME")
    @Field(type = FieldType.Keyword)
    private String ownerename;
}