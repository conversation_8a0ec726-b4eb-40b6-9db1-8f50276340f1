package com.jackrain.nea.st.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jackrain.nea.st.model.base.SubBaseModel;
import lombok.Data;

import java.util.Date;

/**
 * 订单加急打标策略
 * st_c_order_urgent_strategy
 *
 * <AUTHOR>
@Data
@TableName(value = "st_c_order_urgent_strategy")
public class StCOrderUrgentStrategyDO extends SubBaseModel {
    /**
     * 编号
     */
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "id", type = IdType.INPUT)
    @JSONField(name = "ID")
    private Long id;

    /**
     * 店铺id
     */
    @JSONField(name = "CP_C_SHOP_ID")
    private String cpCShopId;

    /**
     * 店铺编码
     */
    @JSONField(name = "CP_C_SHOP_ECODE")
    private String cpCShopEcode;

    /**
     * 店铺名称
     */
    @JSONField(name = "CP_C_SHOP_ENAME")
    private String cpCShopEname;

    /**
     * 会员等级
     */
    @JSONField(name = "VP_C_VIPTYPE_ID")
    private Long vpCViptypeId;

    /**
     * 是否订单加急打标
     */
    @JSONField(name = "IS_URGENT")
    private String isUrgent;

    /**
     * 备注
     */
    @JSONField(name = "REMARK")
    private String remark;

    /**
     * 版本号
     */
    @JSONField(name = "VERSION")
    private Long version;

    /**
     * 所属组织
     */
    @JSONField(name = "AD_ORG_ID")
    private Long adOrgId;

    /**
     * 是否可用
     */
    @JSONField(name = "ISACTIVE")
    private String isactive;

    /**
     * 所属公司
     */
    @JSONField(name = "AD_CLIENT_ID")
    private Long adClientId;

    /**
     * 创建人id
     */
    @JSONField(name = "OWNERID")
    private Long ownerid;

    /**
     * 创建人姓名
     */
    @JSONField(name = "OWNERENAME")
    private String ownerename;

    /**
     * 创建人用户名
     */
    @JSONField(name = "OWNERNAME")
    private String ownername;

    /**
     * 创建时间
     */
    @JSONField(name = "CREATIONDATE")
    private Date creationdate;

    /**
     * 修改人id
     */
    @JSONField(name = "MODIFIERID")
    private Long modifierid;

    /**
     * 修改人姓名
     */
    @JSONField(name = "MODIFIERENAME")
    private String modifierename;

    /**
     * 修改人用户名
     */
    @JSONField(name = "MODIFIERNAME")
    private String modifiername;

    /**
     * 修改时间
     */
    @JSONField(name = "MODIFIEDDATE")
    private Date modifieddate;
}