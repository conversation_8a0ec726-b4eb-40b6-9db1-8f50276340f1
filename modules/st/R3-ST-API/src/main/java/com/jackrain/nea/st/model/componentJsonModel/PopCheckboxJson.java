package com.jackrain.nea.st.model.componentJsonModel;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;

/**
 * @Descroption 弹出多选json对象
 * <AUTHOR>
 * @Date 2019/5/8 15:02
 */
@Data
public class PopCheckboxJson implements Serializable {
    @JSONField(name = "value")
    private PopCheckboxValueJson popCheckboxValue;

    @JSONField(name = "total")
    private long total;

    @JSONField(name = "lists")
    private PopCheckboxListsJson popCheckboxLists;

    @JSONField(name = "idArray")
    private String[] idArray;

}
