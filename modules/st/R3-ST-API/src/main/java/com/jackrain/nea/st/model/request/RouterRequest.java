package com.jackrain.nea.st.model.request;

import com.alibaba.fastjson.annotation.JSONField;
import com.jackrain.nea.st.model.table.StCRouterCodeDO;
import com.jackrain.nea.st.model.table.StCRouterDO;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2020/5/25 18:12
 */
@Data
public class RouterRequest implements Serializable {

    @J<PERSON>NField(name = "ST_C_ROUTER")
    private StCRouterDO stCRouterDO;

    @JSONField(name = "ST_C_ROUTER_CODE")
    private List<StCRouterCodeDO> stCRouterCodeDOList;
}
