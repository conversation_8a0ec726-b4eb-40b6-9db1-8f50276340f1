package com.jackrain.nea.st.model.request;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 库存同步策略渠道查询请求对象
 *
 * <AUTHOR>
 * @since 2020-08-19
 * create at : 2020-08-19 15:36
 */
@Data
public class SyncStockStrategyChannelQueryRequest implements Serializable {

    /**
     * 同步库存策略ID集合
     */
    private List<Long> syncStockStrategyIds;
    /**
     * 是否可用
     */
    private String isactive;
    /**
     * 渠道仓编码集合
     */
    private List<String> orgChannelCodes;
    /**
     * 渠道仓ID
     */
    private List<Long> orgChannelIds;
}
