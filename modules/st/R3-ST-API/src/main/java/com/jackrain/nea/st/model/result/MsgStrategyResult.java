package com.jackrain.nea.st.model.result;

import com.alibaba.fastjson.annotation.JSONField;
import com.jackrain.nea.st.model.table.StcMsgDO;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @Descroption
 * <AUTHOR>
 * @Date 2020/8/28
 */
@Data
public class MsgStrategyResult implements Serializable {

    @JSONField(name = "ST_C_MSG_STRATEGY")
    private List<StcMsgDO> stcMsgDOList;
}
