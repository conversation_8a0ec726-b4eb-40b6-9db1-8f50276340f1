package com.jackrain.nea.st.model.result;

import com.jackrain.nea.st.model.table.StCBnColumnListDO;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 班牛工单组件列表查询结果
 */
@Data
public class BnColumnListQueryResult implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 班牛工单组件列表
     */
    private List<StCBnColumnListDO> columnListList;
}
