package com.jackrain.nea.st.model.result;

import com.jackrain.nea.st.model.table.StCWarehouseLogisticsDO;
import com.jackrain.nea.st.model.table.StCWarehouseLogisticsItemDO;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @Author: huang.z<PERSON>
 * @Date: 2019-08-13
 * @Version 1.0
 */
@Data
public class WarehouseLogisticsTreeResult implements Serializable {

    private StCWarehouseLogisticsDO warehouseLogistics;

    private List<RegionTreeResult> warehouseLogisticsTree;

    private List<StCWarehouseLogisticsItemDO> warehouseLogisticsItems;
}
