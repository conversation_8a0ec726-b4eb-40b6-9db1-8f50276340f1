package com.jackrain.nea.st.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jackrain.nea.st.model.base.SubBaseModel;
import lombok.Data;

@TableName(value = "st_c_keywords_intercept_strategy")
@Data
public class StCKeywordsInterceptStrategy extends SubBaseModel {
    @JSONField(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    @JSONField(name = "CP_C_PLATFORM_ID")
    private Long cpCPlatformId;

    @JSONField(name = "CP_C_PLATFORM_ECODE")
    private String cpCPlatformEcode;

    @J<PERSON><PERSON>ield(name = "CP_C_PLATFORM_ENAME")
    private String cpCPlatformEname;

    @JSONField(name = "KEYWORDS")
    private String keywords;

    @JSONField(name = "OWNERENAME")
    private String ownerename;

    @JSONField(name = "MODIFIERENAME")
    private String modifierename;
}