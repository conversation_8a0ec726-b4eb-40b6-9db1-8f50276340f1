package com.jackrain.nea.st.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.*;
import com.jackrain.nea.es.annotations.Document;
import com.jackrain.nea.es.annotations.Field;
import com.jackrain.nea.es.constans.FieldType;
import com.jackrain.nea.sys.domain.BaseModel;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @ClassName StCPreorderItemStrategyDO
 * @Description 订单预导入模板商品明细
 * <AUTHOR>
 * @Date 2023/2/1 15:21
 * @Version 1.0
 */
@TableName(value = "st_c_preorder_item_strategy")
@Data
@Document(index = "st_c_preorder_item_strategy", type = "st_c_preorder_item_strategy")
public class StCPreorderItemStrategyDO extends BaseModel {

    @JSONField(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "id", type = IdType.INPUT)
    @Field(type = FieldType.Long)
    private Long id;

    /**
     * 模板ID
     */
    @JSONField(name = "PREORDER_MODEL_STRATEGY_ID")
    @Field(type = FieldType.Long)
    private Long preorderModelStrategyId;

    /**
     * 商品名称
     */
    @JSONField(name = "ITEM_NAME")
    @Field(type = FieldType.Keyword)
    private String itemName;

    /**
     * sku编码
     */
    @JSONField(name = "SKU_ECODE")
    @Field(type = FieldType.Keyword)
    private String skuEcode;

    /**
     * sku id
     */
    @JSONField(name = "SKU_ID")
    @Field(type = FieldType.Long)
    private Long skuId;

    /**
     * 成交单价
     */
    @JSONField(name = "PRICE_ACTUAL")
    @Field(type = FieldType.Double)
    private BigDecimal priceActual;

}
