package com.jackrain.nea.st.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.*;
import com.jackrain.nea.es.annotations.Document;
import com.jackrain.nea.es.annotations.Field;
import com.jackrain.nea.es.constans.FieldType;
import com.jackrain.nea.sys.domain.BaseModel;
import lombok.Data;

/**
 * @ClassName StCPreorderFieldStrategyDO
 * @Description 订单预导入模板明细表
 * <AUTHOR>
 * @Date 2022/12/22 10:31
 * @Version 1.0
 */
@TableName(value = "st_c_preorder_field_strategy")
@Data
@Document(index = "st_c_preorder_field_strategy", type = "st_c_preorder_field_strategy")
public class StCPreorderFieldStrategyDO extends BaseModel {

    @JSONField(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "id", type = IdType.INPUT)
    @Field(type = FieldType.Long)
    private Long id;

    /**
     * 模板表ID
     */
    @JSONField(name = "PREORDER_MODEL_STRATEGY_ID")
    @Field(type = FieldType.Long)
    private Long preorderModelStrategyId;

    /**
     * 标准列名
     */
    @JSONField(name = "STANDARD_FIELD")
    @Field(type = FieldType.Keyword)
    private String standardField;

    /**
     * 自定义列名
     */
    @JSONField(name = "CUSTOMIZE_FIELD")
    @Field(type = FieldType.Keyword)
    private String customizeField;

}
