package com.jackrain.nea.st.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jackrain.nea.st.model.base.SubBaseModel;
import com.jackrain.nea.sys.domain.BaseModel;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

@TableName(value = "st_c_unfullcar_cost")
@Data
public class StCUnfullcarCost extends SubBaseModel {
    @JSONField(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    @JSONField(name = "CP_C_PHY_WAREHOUSE_ID")
    private Long cpCPhyWarehouseId;

    @JSONField(name = "CP_C_LOGISTICS_ID")
    private Long cpCLogisticsId;

    @JSONField(name = "START_DATE")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    private Date startDate;

    @JSONField(name = "END_DATE")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    private Date endDate;

    @JSONField(name = "SUBMIT_USER_ID")
    private Long submitUserId;

    @JSONField(name = "SUBMIT_TIME")
    private LocalDateTime submitTime;

    @JSONField(name = "STATUS")
    private Integer status;

    @JSONField(name = "CLOSE_USER_ID")
    private Long closeUserId;

    @JSONField(name = "CLOSE_TIME")
    private LocalDateTime closeTime;

    @JSONField(name = "CLOSE_STATUS")
    private Integer closeStatus;

    @JSONField(name = "OIL_PRICE_LINKAGE")
    private BigDecimal oilPriceLinkage;

    @JSONField(name = "REMARK")
    private String remark;

    @JSONField(name = "VERSION")
    private Long version;

    @JSONField(name = "OWNERENAME")
    private String ownerename;

    @JSONField(name = "MODIFIERENAME")
    private String modifierename;
}