package com.jackrain.nea.st.model.request;

import com.alibaba.fastjson.annotation.JSONField;
import com.jackrain.nea.st.model.table.StCDistributionCustomerDO;
import com.jackrain.nea.st.model.table.StCDistributionDO;
import com.jackrain.nea.st.model.table.StCDistributionItemDO;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * Descroption 分销代销策略
 *
 * <AUTHOR>
 * @Date 2019/3/7 15:23
 */
@Data
public class DistributionRequest implements Serializable {

    @JSONField(name = "ST_C_DISTRIBUTION")
    private StCDistributionDO stCDistribution;
    @JSONField(name = "ST_C_DISTRIBUTION_ITEM")
    private List<StCDistributionItemDO> stCDistributionItemList;

}
