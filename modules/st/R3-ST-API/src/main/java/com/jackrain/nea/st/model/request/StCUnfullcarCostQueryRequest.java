package com.jackrain.nea.st.model.request;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR> lin yu
 * @date : 2022/8/2 上午10:58
 * @describe :
 */

@Data
public class StCUnfullcarCostQueryRequest implements Serializable {

    private List<Long> warehouseIdList;

    private List<Long> logisticsIdList;

    private BigDecimal totalWeight;

    private Long regionProvinceId;

    private Long regionCityId;
}
