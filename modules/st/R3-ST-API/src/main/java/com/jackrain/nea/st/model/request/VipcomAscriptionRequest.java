package com.jackrain.nea.st.model.request;

import com.alibaba.fastjson.annotation.JSONField;
import com.jackrain.nea.st.model.table.StCVipcomAscriptionDO;
import lombok.Data;

import java.io.Serializable;

/**
 * 日程归属设置请求实体
 *
 * <AUTHOR>
 * @Date 2019/3/11 13:35
 */
@Data
public class VipcomAscriptionRequest implements Serializable {
    @JSONField(name = "ST_C_VIPCOM_ASCRIPTION")
    private StCVipcomAscriptionDO stCVipcomAscription;
}
