package com.jackrain.nea.st.model.request;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;


/**
 * <AUTHOR>
 * @Date 2022/6/13 15:48
 */
@Data
public class SyncUnfullcarCostImportRequest implements Serializable {

    /**
     * 仓库ID
     */
    private Long warehouseId;

    /**
     * 仓库名称
     */
    private String warehouseName;

    /**
     * 物流公司ID
     */
    private Long logisticsId;

    /**
     * 物流公司名称
     */
    private String logisticsName;

    /**
     * 开始日期Date格式
     */
    private Date startDateForDate;

    /**
     * 开始日期
     */
    private String startDate;

    /**
     * 开始日期Date格式
     */
    private Date endDateForDate;

    /**
     * 结束日期
     */
    private String endDate;

    /**
     * 油价联动（%）数值
     */
    private BigDecimal oilPriceLinkageNum;

    /**
     * 油价联动（%）
     */
    private String oilPriceLinkage;

    /**
     * 备注
     */
    private String remark;

    /**
     * 目的省份ID
     */
    private Long provinceId;

    /**
     * 目的省份
     */
    private String province;

    /**
     * 目的城市ID
     */
    private Long cityId;

    /**
     * 目的城市
     */
    private String city;

    /**
     * 到货天数数值
     */
    private Integer arrivalDaysNum;

    /**
     * 到货天数
     */
    private String arrivalDays;

    /**
     * 起始重量数值
     */
    private BigDecimal startWeightNum;

    /**
     * 起始重量
     */
    private String startWeight;

    /**
     * 结束重量数值
     */
    private BigDecimal endWeightNum;

    /**
     * 结束重量
     */
    private String endWeight;

    /**
     * 干线费用数值
     */
    private BigDecimal trunkFreightNum;

    /**
     * 干线费用
     */
    private String trunkFreight;

    /**
     * 提货费数值
     */
    private BigDecimal deliveryFeeNum;

    /**
     * 提货费
     */
    private String deliveryFee;

    /**
     * 送货费
     */
    private BigDecimal freightNum;

    /**
     * 送货费
     */
    private String freight;

    /**
     * 保费数值
     */
    private BigDecimal premiumNum;

    /**
     * 保费
     */
    private String premium;

    /**
     * 卸货费数值
     */
    private BigDecimal unloadingFeeNum;

    /**
     * 卸货费
     */
    private String unloadingFee;

    /**
     * 其他费用数值
     */
    private BigDecimal otherFeeNum;

    /**
     * 其他费用
     */
    private String otherFee;

    /**
     * 行数
     */
    private Integer rowNum;

}
