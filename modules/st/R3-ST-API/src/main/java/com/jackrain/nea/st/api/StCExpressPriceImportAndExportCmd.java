package com.jackrain.nea.st.api;


import com.jackrain.nea.st.model.request.StCExpressPriceStrategyPoi;
import com.jackrain.nea.st.model.result.StImportErrorMsgResult;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @Date 2022/6/13 17:07
 */
public interface StCExpressPriceImportAndExportCmd {


    /**
     * 导入零担费用设置
     * @param importList
     * @param user
     * @return
     */
    ValueHolderV14<List<StImportErrorMsgResult>> importExpressPriceCost(List<StCExpressPriceStrategyPoi> importList, User user);


    /**
     * 导入错误信息下载
     * @param user
     * @param errMsgList
     * @return
     */
    String downloadImportErrMsg(User user,List<StImportErrorMsgResult> errMsgList);


    ValueHolderV14<String> exportExpressPriceCost(String ids,User user);
}
