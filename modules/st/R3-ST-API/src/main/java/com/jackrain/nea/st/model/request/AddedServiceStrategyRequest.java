package com.jackrain.nea.st.model.request;

import com.alibaba.fastjson.annotation.JSONField;
import com.jackrain.nea.st.model.table.StAddedServiceStrategyDO;
import com.jackrain.nea.st.model.table.StAddedServiceStrategyDetailDO;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @program: ryytn-st-v3.0
 * @description: 增值服务策略请求
 * @author: haiyang
 * @create: 2023-10-20 17:49
 **/
@Data
public class AddedServiceStrategyRequest implements Serializable {

    @J<PERSON>NField(name = "ST_ADDED_SERVICE_STRATEGY")
    private StAddedServiceStrategyDO stAddedServiceStrategyDO;

    @JSONField(name = "ST_ADDED_SERVICE_STRATEGY_DETAIL")
    private List<StAddedServiceStrategyDetailDO> stAddedServiceStrategyDetailDOList;
}
