package com.jackrain.nea.st.model.result;

import com.jackrain.nea.st.model.table.StCUnfullcarCost;
import com.jackrain.nea.st.model.table.StCUnfullcarCostItem;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 零担报价关系
 *
 * <AUTHOR>
 * @Date 2025/7/15
 * @Description 零担报价设置关系表
 * @Version 1.0
 */
@Data
public class StCUnfullcarCostRelation implements Serializable {

    /**
     * 零担报价主表
     */
    private StCUnfullcarCost stCUnfullcarCost;

    /**
     * 零担报价明细
     */
    private List<StCUnfullcarCostItem> stCUnfullcarCostItemList;
}
