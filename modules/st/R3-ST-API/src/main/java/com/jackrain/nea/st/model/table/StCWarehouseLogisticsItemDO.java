package com.jackrain.nea.st.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.*;
import com.jackrain.nea.st.model.base.SubBaseModel;
import lombok.Data;

@TableName(value = "st_c_warehouse_logistics_item")
@Data
public class StCWarehouseLogisticsItemDO extends SubBaseModel {
    @JSONField(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    @J<PERSON>NField(name = "ST_C_WAREHOUSE_LOGISTICS_ID")
    private Long stCWarehouseLogisticsId;

    @JSO<PERSON>ield(name = "CP_C_LOGISTICS_ID")
    private Long cpCLogisticsId;

    @JSONField(name = "CP_C_LOGISTICS_ECODE")
    private String cpCLogisticsEcode;

    @JSONField(name = "CP_C_LOGISTICS_ENAME")
    private String cpCLogisticsEname;

    @JSONField(name = "<PERSON>W<PERSON><PERSON><PERSON><PERSON>")
    private String ownerename;

    @J<PERSON><PERSON>ield(name = "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>")
    private String modifierename;
}