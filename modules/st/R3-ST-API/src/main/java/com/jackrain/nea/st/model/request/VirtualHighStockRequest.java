package com.jackrain.nea.st.model.request;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @Date: 2020/7/7 4:23 下午
 * @Desc:
 */
@Data
public class VirtualHighStockRequest implements Serializable {


    private static final long serialVersionUID = 78285423478684424L;
    /**
     * 平台skuid
     */
    private String skuId;
    /**
     * 平台商品id
     */
    private String proId;
    /**
     * 店铺id
     */
    private String shopId;
    /**
     * 当前时间
     */
    private String expireTime;
    /**
     * 中台skuid
     */
    private Long psCSkuId;

}
