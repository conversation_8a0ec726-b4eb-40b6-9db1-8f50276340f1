package com.jackrain.nea.st.model.result;

import com.jackrain.nea.st.model.table.StCFullcarCostItem;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 整车报价明细查询结果
 */
@Data
public class StCFullcarCostDetailQueryResult implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 整车报价设置明细列表
     */
    private List<StCFullcarCostItem> fullcarCostItemList;
}
