package com.jackrain.nea.st.utils;


import org.apache.commons.lang3.StringUtils;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @date 2020/12/10 11:46 下午
 * @description 正则工具类
 **/
public class RegexUtils {
    /**
     * 判断一个数字是不是正整数
     * @param str
     * @return
     */
    public static boolean isPureDigital(String str) {
        if(StringUtils.isBlank(str)){
            return false;
        }
        String regEx = "[1-9]\\d*";
        Pattern p;
        Matcher m;
        p = Pattern.compile(regEx);
        m = p.matcher(str);
        if (m.matches()) {
            return true;
        }
        return false;
    }
}
