package com.jackrain.nea.st.model.componentJsonModel;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;

/**
 * @Descroption 弹出多选jsonValue对象
 * <AUTHOR>
 * @Date 2019/5/8 15:14
 */
@Data
public class PopCheckboxValueJson  implements Serializable {
    @J<PERSON><PERSON>ield(name = "TABLE<PERSON><PERSON>")
    private String tablename;

    @JSONField(name = "CONDITION")
    private String[] condition;

    @JSONField(name = "IN")
    private long[] in;

    @JSONField(name = "NOTIN")
    private String[] notin;

    @J<PERSON>NField(name = "EXCLUDE")
    private String[] exclude;

    @JSONField(name = "<PERSON><PERSON><PERSON><PERSON><PERSON>")
    private String global;

}
