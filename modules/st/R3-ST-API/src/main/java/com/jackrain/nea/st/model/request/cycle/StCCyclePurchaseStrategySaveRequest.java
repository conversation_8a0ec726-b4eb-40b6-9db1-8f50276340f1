package com.jackrain.nea.st.model.request.cycle;


import com.jackrain.nea.sys.domain.BaseModel;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * @Auther: chen<PERSON>
 * @Date: 2022-09-03 15:26
 * @Description:
 */

@Data
public class StCCyclePurchaseStrategySaveRequest extends BaseModel implements Serializable {

    private Long id;

    private String remark;

    private Long version;

    private String ownerename;

    private String modifierename;

    private Long psCProId;

    private String psCProEcode;

    private String psCProEname;

    private Date startDate;

    private Date endDate;

    private String sendPattern;

    private String cycleType;

    private Integer type;

    private Integer qtyCycle;

    private Integer status;

    private Long delerId;

    private String delerEname;

    private String delerName;

    private Date delTime;

    private Long statusId;

    private String statusEname;

    private String statusName;

    private Date statusTime;

    private Integer closerStatus;

    private Date closerTime;

    private Long closerId;
}
