package com.jackrain.nea.st.model.request;

import com.alibaba.fastjson.annotation.JSONField;
import com.jackrain.nea.st.model.base.SubBaseModel;
import lombok.Data;

import java.util.Date;

/**
 * @Author: ganquan
 * @Date Create In 2020/12/18 18:35
 * @Description: 店铺锁库
 */
@Data
public class StCLockStockStrategyItemRequest extends SubBaseModel {
    @JSONField(name = "ID")
    private Long id;

    @JSONField(name = "ST_C_LOCK_STOCK_STRATEGY_ID")
    private Long stCLockStockStrategyId;

    @JSONField(name = "CP_C_SHOP_ID")
    private Long cpCShopId;

    @JSONField(name = "CP_C_SHOP_TITLE")
    private String cpCShopTitle;

    @JSONField(name = "CP_C_SHOP_ECODE")
    private String cpCShopEcode;

    @JSONField(name = "LOCK_BTIME")
    private Date lockBtime;

    @JSONField(name = "LOCK_ETIME")
    private Date lockEtime;

    @JSONField(name = "OWNERENAME")
    private String ownerename;

    @JSONField(name = "MODIF<PERSON>RENAME")
    private String modifierename;
    /**
     * 方案名称
     */
    @JSONField(name = "PLAN_NAME")
    private String planName;
    /**
     * 主表状态
     */
    @JSONField(name = "STATUS")
    private Integer status;
    /**
     * 优先级
     */
    @JSONField(name = "RANK")
    private Integer rank;
    /**
     * 主表创建时间
     */
    @JSONField(name = "MAIN_CREATIONDATE")
    private Date mainCreationdate;

    /**
     * 当前时间
     */
    @JSONField(name = "EXPIRE_TIME")
    private Date expireTime;
}
