package com.jackrain.nea.st.model.result;

import com.jackrain.nea.cp.entity.CpCOrgChannelEntity;
import com.jackrain.nea.cp.entity.CpCOrgChannelItemEntity;
import com.jackrain.nea.st.model.table.*;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * @program: r3-st
 * @author: huang_zaizai
 * @create: 2019-07-03 14:15
 */

@Data
@AllArgsConstructor
@NoArgsConstructor
public class SyncStockStrategyQueryResult implements Serializable {

    private StCSyncStockStrategyDO syncStockStrategy;
    /**
     * 关联渠道仓列表
     */
    private List<StCSyncStockStrategyChannelDO> syncStockStrategyChannelDOList;
    /**
     * 渠道仓列表
     */
    private List<CpCOrgChannelEntity> cpCOrgChannelEntityList;
    /**
     *渠道仓子表列表
     */
    private List<CpCOrgChannelItemEntity> cpCOrgChannelItemEntityList;
}
