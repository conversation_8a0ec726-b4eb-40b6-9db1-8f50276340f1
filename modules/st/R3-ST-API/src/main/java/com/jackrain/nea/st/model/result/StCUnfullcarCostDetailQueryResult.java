package com.jackrain.nea.st.model.result;

import com.jackrain.nea.st.model.table.StCUnfullcarCostItem;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 零担报价明细查询结果
 */
@Data
public class StCUnfullcarCostDetailQueryResult implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 零担报价设置明细列表
     */
    private List<StCUnfullcarCostItem> unfullcarCostItemList;
}
