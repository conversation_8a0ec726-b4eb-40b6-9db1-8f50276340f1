package com.jackrain.nea.st.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jackrain.nea.st.model.base.SubBaseModel;
import lombok.Data;

import java.util.Date;

@TableName(value = "st_c_merge_order")
@Data
public class StCMergeOrderDO extends SubBaseModel {
    @JSONField(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    @JSONField(name = "CP_C_SHOP_ID")
    private Long cpCShopId;

    @JSONField(name = "CP_C_SHOP_TITLE")
    private String cpCShopTitle;

    @JSONField(name = "IS_AUTOMERGE")
    private Integer isAutomerge;

    @J<PERSON><PERSON>ield(name = "IS_AUTOCHANGE")
    private Integer isAutochange;

    @JSONField(name = "REMARK")
    private String remark;

    @JSONField(name = "OWNERENAME")
    private String ownerename;

    @JSONField(name = "MODIFIERENAME")
    private String modifierename;

    @JSONField(name = "DELID")
    private Long delid;

    @JSONField(name = "DELENAME")
    private String delename;

    @JSONField(name = "DELNAME")
    private String delname;

    @JSONField(name = "DEL_TIME")
    private Date delTime;

    @JSONField(name = "IS_MERGER_PRESELL11")
    private Integer isMergerPresell11;

    @JSONField(name = "IS_MERGER_PRESELL")
    private Integer isMergerPresell;

    @JSONField(name = "IS_MERGER_LIVE")
    private Integer isMergerLive;

    @JSONField(name = "MERGE_SPLIT_ORDER_TYPE")
    private String mergeSplitOrderType;

    @JSONField(name = "IS_REVIEWED_MERGE")
    private String isReviewedMerge;

    @JSONField(name = "CATEGORY_LIMIT")
    private String categoryLimit;

    /**
     * 排除业务类型ID
     */
    @JSONField(name = "ST_C_BUSINESS_TYPE_IDS")
    private String stCBusinessTypeIds;

    /**
     * 排除业务类型名称
     */
    @JSONField(name = "ST_C_BUSINESS_TYPE_ENAMES")
    private String stCBusinessTypeEnames;

}