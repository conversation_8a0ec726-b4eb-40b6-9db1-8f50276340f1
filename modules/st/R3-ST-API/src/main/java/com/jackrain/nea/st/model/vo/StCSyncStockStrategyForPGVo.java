package com.jackrain.nea.st.model.vo;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;

/**
 * @author: 陈俊明
 * @since: 2019-07-08
 * @create at : 2019-07-08 21:38
 */
@Data
public class StCSyncStockStrategyForPGVo implements Serializable {

    @JSONField(name = "ID")
    private Long id;

    @JSONField(name = "CP_C_STORE_ID")
    private Long cpCStoreId;

    @JSONField(name = "CP_C_STORE_ENAME")
    private String cpCStoreEname;

    @JSONField(name = "CP_C_SHOP_ID")
    private Long cpCShopId;

    @JSONField(name = "ST_C_SYNC_STOCK_STRATEGY_ID")
    private Long stCSyncStockStrategyId;
}
