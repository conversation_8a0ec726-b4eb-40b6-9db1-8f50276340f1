package com.jackrain.nea.st.model.request;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 快运报价设置查询请求
 */
@Data
public class StCExpressCostQueryRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 仓库ID列表
     */
    private List<Long> warehouseIdList;

    /**
     * 物流公司ID列表
     */
    private List<Long> logisticsIdList;

    /**
     * 总重量
     */
    private BigDecimal totalWeight;

    /**
     * 省份ID
     */
    private Long provinceId;
}
