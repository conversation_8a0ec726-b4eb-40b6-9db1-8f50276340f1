package com.jackrain.nea.st.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jackrain.nea.sys.domain.BaseModel;
import lombok.Data;

/**
 * @ClassName CpCRegion
 * @Description 区域信息
 * <AUTHOR>
 * @Date 2025/2/26 14:53
 * @Version 1.0
 */
@TableName(value = "cp_c_region")
@Data
public class CpCRegion extends BaseModel {
    private static final long serialVersionUID = 9085805288062170658L;
    @JSONField(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "ID", type = IdType.INPUT)
    private Long id;

    @JSONField(name = "REGIONTYPE")
    private String regiontype;

    @JSONField(name = "ECODE")
    private String ecode;

    @JSONField(name = "ENAME")
    private String ename;

    @JSONField(name = "C_UP_ID")
    private Long cUpId;

    @JSONField(name = "MIXNAME")
    private String mixname;

    @JSONField(name = "REMARK")
    private String remark;

    @JSONField(name = "MODIFIERENAME")
    private String modifierename;

    @JSONField(name = "OWNERENAME")
    private String ownerename;

    @JSONField(name = "CITY_LEVEL")
    private String cityLevel;
}
