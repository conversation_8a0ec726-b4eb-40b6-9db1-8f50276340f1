package com.jackrain.nea.st.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jackrain.nea.es.annotations.Document;
import com.jackrain.nea.es.annotations.Field;
import com.jackrain.nea.es.constans.FieldType;
import com.jackrain.nea.st.model.base.SubBaseModel;
import com.jackrain.nea.sys.domain.BaseModel;
import lombok.Data;

import java.util.Date;

/**
 * 多代条码策略-店铺
 */
@TableName(value = "st_c_barcode_replace_shop")
@Data
@Document(index = "st_c_barcode_replace_shop", type = "st_c_barcode_replace_shop")
public class StCBarcodeReplaceShop extends BaseModel {
    /**
     * id
     */
    @JSONField(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "id", type = IdType.INPUT)
    @Field(type = FieldType.Long)
    private Long id;

    /**
     * 多代条码策略id
     */
    @JSONField(name = "ST_C_BARCODE_REPLACE_ID")
    @Field(type = FieldType.Long)
    private Long stCBarcodeReplaceId;

    /**
     * 店铺
     */
    @JSONField(name = "CP_C_SHOP_ID")
    @Field(type = FieldType.Long)
    private Long cpCShopId;

    /**
     * 店铺编码
     */
    @JSONField(name = "CP_C_SHOP_CODE")
    @Field(type = FieldType.Keyword)
    private String cpCShopCode;

}