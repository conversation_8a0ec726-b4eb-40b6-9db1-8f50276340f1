package com.jackrain.nea.st.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jackrain.nea.es.annotations.Document;
import com.jackrain.nea.es.annotations.Field;
import com.jackrain.nea.es.constans.FieldType;
import com.jackrain.nea.sys.domain.BaseModel;
import java.math.BigDecimal;
import lombok.Data;

@TableName(value = "st_c_vipcom_jitx_warehouse")
@Data
@Document(index = "st_c_vipcom_jitx_warehouse", type = "st_c_vipcom_jitx_warehouse")
public class StCVipcomJitxWarehouse extends BaseModel {
    @JSONField(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "id", type = IdType.INPUT)
    @Field(type = FieldType.Long)
    private Long id;

    @JSONField(name = "CP_C_SHOP_ID")
    @Field(type = FieldType.Long)
    private Long cpCShopId;

    @JSONField(name = "CP_C_SHOP_ECODE")
    @Field(type = FieldType.Keyword)
    private String cpCShopEcode;

    @JSONField(name = "CP_C_SHOP_ENAME")
    @Field(type = FieldType.Keyword)
    private String cpCShopEname;

    @JSONField(name = "CP_C_PHY_WAREHOUSE_ID")
    @Field(type = FieldType.Long)
    private Long cpCPhyWarehouseId;

    @JSONField(name = "CP_C_PHY_WAREHOUSE_ECODE")
    @Field(type = FieldType.Keyword)
    private String cpCPhyWarehouseEcode;

    @JSONField(name = "CP_C_PHY_WAREHOUSE_ENAME")
    @Field(type = FieldType.Keyword)
    private String cpCPhyWarehouseEname;

    @JSONField(name = "VIPCOM_WAREHOUSE_ECODE")
    @Field(type = FieldType.Keyword)
    private String vipcomWarehouseEcode;

    @JSONField(name = "VIPCOM_UNSHOP_WAREHOUSE_ECODE")
    @Field(type = FieldType.Keyword)
    private String vipcomUnshopWarehouseEcode;

    @JSONField(name = "VIPCOM_SPECIAL_WAREHOUSE_ECODE")
    @Field(type = FieldType.Keyword)
    private String vipcomSpecialWarehouseEcode;

    @JSONField(name = "JITX_CAPACITY")
    @Field(type = FieldType.Double)
    private BigDecimal jitxCapacity;

    @JSONField(name = "IS_ENABLE_JITX_CAPACITY")
    @Field(type = FieldType.Keyword)
    private String isEnableJitxCapacity;

    @JSONField(name = "QTY_JITX_ORDER_TODAY")
    @Field(type = FieldType.Double)
    private BigDecimal qtyJitxOrderToday;

    @JSONField(name = "OWNERENAME")
    @Field(type = FieldType.Keyword)
    private String ownerename;

    @JSONField(name = "MODIFIERENAME")
    @Field(type = FieldType.Keyword)
    private String modifierename;
}