package com.jackrain.nea.st.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.*;
import com.jackrain.nea.st.model.base.SubBaseModel;
import lombok.Data;

@TableName(value = "st_c_ewaybill_shop")
@Data
public class StCEwaybillShopDO extends SubBaseModel {
    @J<PERSON>NField(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    @J<PERSON>NField(name = "ST_C_EWAYBILL_ID")
    private Long stCEwaybillId;

    @JSONField(name = "CP_C_SHOP_ID")
    private Long cpCShopId;

    @JSONField(name = "CP_C_SHOP_TITLE")
    private String cpCShopTitle;

    @J<PERSON><PERSON>ield(name = "OWNERENAME")
    private String ownerename;

    @JSONField(name = "MODIFIERENA<PERSON>")
    private String modifierename;
}