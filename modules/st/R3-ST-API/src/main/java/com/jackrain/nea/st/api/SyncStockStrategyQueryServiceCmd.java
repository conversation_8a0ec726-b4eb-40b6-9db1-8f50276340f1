package com.jackrain.nea.st.api;

import com.jackrain.nea.cp.entity.CpCOrgChannelItemEntity;
import com.jackrain.nea.st.model.request.StStockPriorityRequest;
import com.jackrain.nea.st.model.result.CpCOrgChannelResult;
import com.jackrain.nea.st.model.result.SyncStockStrategyQueryResult;
import com.jackrain.nea.st.model.vo.StCSyncStockStrategyVo;

import java.util.List;

public interface SyncStockStrategyQueryServiceCmd {
    List<StCSyncStockStrategyVo> querySyncStockStrategyItemByID(Long shopId);

    List<StStockPriorityRequest> selectStockPriorityByShopId(Long shopId);

    Boolean checkByStoreIds(List<Long> storeIds);

    List<Long> queryStoreIdsByShopId(Long shopId);

    List<Long> queryShopStoreList(Long cpCShopId, List<Long> storeList);

    List<StStockPriorityRequest> queryStStockPriority(Long cpCShopId, List<Long> storeList);

    List<CpCOrgChannelItemEntity> querySyncStockStrategy(Long cpCShopId);

    List<CpCOrgChannelResult> querySyncStockStrategyStore(Long cpCShopId);

    List<Long> queryShopStoreNextList(Long cpCShopId);

    List<SyncStockStrategyQueryResult> queryAllSyncStockStrategy();

}
