package com.jackrain.nea.st.model.common;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 19:52 2021/4/6
 * @Description 直播策略字段转化类
 */
public interface StLiveCastStrategyConstants {


    /**
     * 是否可用
     */
    Map<String,String> ISACTIVE = new HashMap(6){{
        put("Y","是");
        put("y","是");
        put("N","否");
        put("n","否");
        put(null,"N/A");
        put("","N/A");
    }};
    /**
     * 方案状态
     */
    Map<String,String> STRATEGYSTATUS = new HashMap(6){{
        put("10","未审核");
        put("20","已审核");
        put("30","已结案");
        put("40","已作废");
        put(null,"N/A");
        put("","N/A");
    }};
    /**
     * 单据时间类型
     */
    Map<String,String> BILLTIMETYPE = new HashMap(6){{
        put("1","下单时间");
        put("2","支付时间");
        put(null,"N/A");
        put("","N/A");
    }};
    /**
     * 直播平台
     */
    Map<String,String> LIVEPLATFORM = new HashMap(6){{
        put("1","抖音");
        put("2","快手");
        put("3","陌陌");
        put("4","蘑菇街");
        put("5","淘宝");
        put(null,"N/A");
        put("","N/A");
    }};
}
