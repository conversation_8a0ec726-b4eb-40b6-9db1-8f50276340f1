package com.jackrain.nea.st.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.*;
import com.jackrain.nea.es.annotations.Document;
import com.jackrain.nea.es.annotations.Field;
import com.jackrain.nea.es.constans.FieldType;
import com.jackrain.nea.st.model.base.SubBaseModel;
import lombok.Data;

import java.util.Date;

@TableName(value = "ST_C_SYNC_SKUSTOCK_STRATEGY")
@Data
@Document(index = "st_c_sync_skustock_strategy", type = "st_c_sync_skustock_strategy")
public class StCSyncSkustockStrategyDO extends SubBaseModel {
    @JSONField(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "ID", type = IdType.INPUT)
    @Field(type = FieldType.Long)
    private Long id;

    @JSONField(name = "CP_C_SHOP_ID")
    @Field(type = FieldType.Long)
    private Long cpCShopId;

    @JSONField(name = "CP_C_SHOP_ECODE")
    @Field(type = FieldType.Keyword)
    private String cpCShopEcode;

    @JSONField(name = "CP_C_SHOP_TITLE")
    @Field(type = FieldType.Keyword)
    private String cpCShopTitle;

    @JSONField(name = "TEMPLATE_NAME")
    @Field(type = FieldType.Keyword)
    private String templateName;

    @JSONField(name = "REMARK")
    @Field(type = FieldType.Keyword)
    private String remark;

    @JSONField(name = "OWNERENAME")
    @Field(type = FieldType.Keyword)
    private String ownerename;

    @JSONField(name = "MODIFIERENAME")
    @Field(type = FieldType.Keyword)
    private String modifierename;

    @JSONField(name = "BEGIN_TIME")
    @Field(type = FieldType.Date)
    private Date beginTime;

    @JSONField(name = "END_TIME")
    @Field(type = FieldType.Date)
    private Date endTime;

    @JSONField(name = "DELER_ID")
    @Field(type = FieldType.Long)
    private Long delerId;

    @JSONField(name = "DELENAME")
    @Field(type = FieldType.Keyword)
    private String delename;

    @JSONField(name = "DELNAME")
    @Field(type = FieldType.Keyword)
    private String delname;

    @JSONField(name = "DEL_TIME")
    @Field(type = FieldType.Date)
    private Date delTime;

    @JSONField(name = "CHECKID")
    @Field(type = FieldType.Long)
    private Long checkid;

    @JSONField(name = "CHECKENAME")
    @Field(type = FieldType.Keyword)
    private String checkename;

    @JSONField(name = "CHECKNAME")
    @Field(type = FieldType.Keyword)
    private String checkname;

    @JSONField(name = "CHECKTIME")
    @Field(type = FieldType.Date)
    private Date checktime;

    @JSONField(name = "BILL_STATUS")
    @Field(type = FieldType.Integer)
    private Integer billStatus;

    @JSONField(name = "FINISHID")
    @Field(type = FieldType.Long)
    private Long finishid;

    @JSONField(name = "FINISHENAME")
    @Field(type = FieldType.Keyword)
    private String finishename;

    @JSONField(name = "FINISHNAME")
    @Field(type = FieldType.Keyword)
    private String finishname;

    @JSONField(name = "FINISHTIME")
    @Field(type = FieldType.Date)
    private Date finishtime;

}