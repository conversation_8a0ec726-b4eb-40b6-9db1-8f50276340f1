package com.jackrain.nea.st.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.*;
import com.jackrain.nea.st.model.base.SubBaseModel;
import lombok.Data;

@TableName(value = "st_c_compensate_warehouse")
@Data
public class StCCompensateWarehouseDO extends SubBaseModel {
    @J<PERSON><PERSON>ield(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    @J<PERSON><PERSON>ield(name = "ST_C_LOGISTICS_COMPENSATE_ID")
    private Long stCLogisticsCompensateId;

    @JSONField(name = "CP_C_PHY_WAREHOUSE_ID")
    private Long cpCPhyWarehouseId;

    @J<PERSON><PERSON>ield(name = "CP_C_PHY_WAREHOUSE_ECODE")
    private String cpCPhyWarehouseEcode;

    @J<PERSON>NField(name = "CP_C_PHY_WAREHOUSE_ENAME")
    private String cpCPhyWarehouseEname;

    @J<PERSON><PERSON>ield(name = "OWNERENAME")
    private String ownerename;

    @J<PERSON><PERSON>ield(name = "MODIFIERENAME")
    private String modifierename;
}