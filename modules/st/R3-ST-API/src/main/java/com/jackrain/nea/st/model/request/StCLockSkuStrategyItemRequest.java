package com.jackrain.nea.st.model.request;

import com.alibaba.fastjson.annotation.JSONField;
import com.jackrain.nea.st.model.base.SubBaseModel;
import lombok.Data;

import java.util.Date;

/**
 * @Author: ganquan
 * @Date Create In 2020/12/18 17:45
 * @Description: 锁库条码
 */
@Data
public class StCLockSkuStrategyItemRequest extends SubBaseModel {
    @JSONField(name = "ID")
    private Long id;

    @JSONField(name = "ST_C_LOCK_SKU_STRATEGY_ID")
    private Long stCLockSkuStrategyId;

    @JSONField(name = "PT_PRO_ID")
    private String ptProId;

    @JSONField(name = "PT_PRO_TITLE")
    private String ptProTitle;

    @JSONField(name = "PS_C_PRO_ID")
    private Long psCProId;

    @J<PERSON>NField(name = "PS_C_PRO_ECODE")
    private String psCProEcode;

    @JSONField(name = "PS_C_PRO_ENAME")
    private String psCProEname;

    @JSONField(name = "PS_C_SKU_ID")
    private Long psCSkuId;

    @JSONField(name = "PS_C_SKU_ECODE")
    private String psCSkuEcode;

    @JSONField(name = "PT_SKU_ID")
    private String ptSkuId;

    @JSONField(name = "GBCODE")
    private String gbcode;

    @JSONField(name = "PS_C_CLR_ID")
    private Long psCClrId;

    @JSONField(name = "PS_C_CLR_ECODE")
    private String psCClrEcode;

    @JSONField(name = "PS_C_CLR_ENAME")
    private String psCClrEname;

    @JSONField(name = "PS_C_SIZE_ID")
    private Long psCSizeId;

    @JSONField(name = "PS_C_SIZE_ECODE")
    private String psCSizeEcode;

    @JSONField(name = "PS_C_SIZE_ENAME")
    private String psCSizeEname;

    @JSONField(name = "OWNERENAME")
    private String ownerename;

    @JSONField(name = "MODIFIERENAME")
    private String modifierename;
    /**
     * 方案名称
     */
    @JSONField(name = "PLAN_NAME")
    private String planName;
    /**
     * 状态
     */
    @JSONField(name = "STATUS")
    private Integer status;
    /**
     * 开始时间
     */
    @JSONField(name = "BEGIN_TIME")
    private Date beginTime;
    /**
     * 结束时间
     */
    @JSONField(name = "END_TIME")
    private Date endTime;
    /**
     * 店铺id
     */
    @JSONField(name = "CP_C_SHOP_ID")
    private Long cpCShopId;
    /**
     * 店铺编码
     */
    @JSONField(name = "CP_C_SHOP_ECODE")
    private String cpCShopEcode;
    /**
     * 店铺名称
     */
    @JSONField(name = "CP_C_SHOP_TITLE")
    private String cpCShopTitle;
    /**
     * 主表创建时间
     */
    @JSONField(name = "MAIN_CREATIONDATE")
    private Date mainCreationdate;

    /**
     * 当前时间
     */
    @JSONField(name = "EXPIRE_TIME")
    private Date expireTime;
}
