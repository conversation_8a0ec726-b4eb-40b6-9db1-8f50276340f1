package com.jackrain.nea.st.model.request;

import com.alibaba.fastjson.annotation.JSONField;
import com.jackrain.nea.st.model.table.*;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * Descroption 电子面单策略
 *
 * <AUTHOR>
 * @Date 2019/3/11 20:23
 */
@Data
public class EwaybillRequest implements Serializable {

    @JSONField(name = "ST_C_EWAYBILL")
    private StCEwaybillDO stCEwaybill;
    @JSONField(name = "ST_C_EWAYBILL_LOGISTICS")
    private List<StCEwaybillLogisticsDO> stCEwaybillLogisticsList;

}
