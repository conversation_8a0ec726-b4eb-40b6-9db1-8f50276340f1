package com.jackrain.nea.st.model.request;

import com.alibaba.fastjson.annotation.JSONField;
import com.jackrain.nea.st.model.table.StCShopStrategyDO;
import com.jackrain.nea.st.model.table.StCShopStrategyItemDO;
import com.jackrain.nea.st.model.table.StCShopStrategyLogisticsItem;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2019/3/8 18:12
 */
@Data
public class ShopStrategyRequest implements Serializable {

    @J<PERSON><PERSON><PERSON>(name = "ST_C_SHOP_STRATEGY")
    private StCShopStrategyDO stCShopStrategyDO;

    @JSONField(name = "ST_C_SHOP_STRATEGY_ITEM")
    private List<StCShopStrategyItemDO> stCShopStrategyItemDOList;

    @<PERSON><PERSON><PERSON><PERSON>(name = "ST_C_SHOP_STRATEGY_LOGISTICS_ITEM")
    private List<StCShopStrategyLogisticsItem> stCShopStrategyLogisticsItemList;
}
