package com.jackrain.nea.st.api;

import com.jackrain.nea.st.model.table.StCAllocationStorageCostStrategy;
import com.jackrain.nea.sys.domain.ValueHolderV14;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2023/8/16 18:27
 * @Description
 */
public interface StCAllocationStorageCostQueryCmd {

    /**
     * 根据仓库id查询调拨预估报价设置
     *
     * @param warehouseIds
     * @return
     */
    ValueHolderV14<List<StCAllocationStorageCostStrategy>> queryByWarehouseIds(List<Long> warehouseIds);
}
