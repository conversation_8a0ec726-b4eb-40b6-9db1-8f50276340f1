package com.jackrain.nea.st.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jackrain.nea.st.model.base.SubBaseModel;
import lombok.Data;

@TableName(value = "ST_C_SPLIT_REASON")
@Data
public class StCSplitReasonDO extends SubBaseModel {
    @J<PERSON>NField(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    @JSONField(name = "ECODE")
    private String ecode;

    @J<PERSON>NField(name = "SPLIT_REASON_CONFIG_ID")
    private Long splitReasonConfigId;

    @JSONField(name = "<PERSON>W<PERSON><PERSON><PERSON><PERSON>")
    private String ownerename;

    @JSO<PERSON>ield(name = "M<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>")
    private String modifierename;
}