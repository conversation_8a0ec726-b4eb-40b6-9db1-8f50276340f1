package com.jackrain.nea.st.model.table;

import java.io.Serializable;
import java.util.Date;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jackrain.nea.st.model.base.SubBaseModel;
import com.jackrain.nea.sys.domain.BaseModel;
import lombok.Data;

/**
 * st_c_appoint_batch_item
 * <AUTHOR>
@TableName
@Data
public class StCAppointBatchItem extends SubBaseModel {
    @JSONField(name = "ID")
    private Long id;

    /**
     * 主表id
     */
    @JSONField(name = "ST_C_APPOINT_BATCH_ID")
    private Long stCAppointBatchId;

    /**
     * 指定维度(1.品项,2.商品编码,3.商品标题,4.平台商品ID)
     */
    @JSONField(name = "APPOINT_DIMENSION")
    private Integer appointDimension;

    /**
     * 指定内容
     */
    @JSONField(name = "APPOINT_CONTENT")
    private String appointContent;

    /**
     * 指定类型(1.生产日期范围,2.)生产天数
     */
    @JSONField(name = "APPOINT_TYPE")
    private Integer appointType;

    /**
     * 开始批次/天数
     */
    @JSONField(name = "START_BATCH_DAY")
    private String startBatchDay;

    /**
     * 结束批次/天数
     */
    @JSONField(name = "END_BATCH_DAY")
    private String endBatchDay;


    private static final long serialVersionUID = 1L;
}