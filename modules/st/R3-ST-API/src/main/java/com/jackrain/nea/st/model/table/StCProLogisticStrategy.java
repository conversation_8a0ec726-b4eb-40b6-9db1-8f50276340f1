package com.jackrain.nea.st.model.table;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jackrain.nea.st.model.base.SubBaseModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * Created with IntelliJ IDEA.
 * @Author: jg.zhan
 * @Date:  2022/6/21 14:48
 * @Description:  
 */
/**
    * 商品物流策略
    */
@ApiModel(value="商品物流策略")
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "st_c_pro_logistic_strategy")
public class StCProLogisticStrategy extends SubBaseModel implements Serializable {
    /**
     * ID
     */
    @TableId(value = "id", type = IdType.INPUT)
    @ApiModelProperty(value = "ID")
    private Long id;

    /**
     * 省份
     */
    @TableField(value = "cp_c_province_id")
    @ApiModelProperty(value = "省份")
    private Long cpCProvinceId;

    /**
     * 市
     */
    @TableField(value = "cp_c_city_id")
    @ApiModelProperty(value = "市")
    private Long cpCCityId;

    /**
     * 区
     */
    @TableField(value = "cp_c_area_id")
    @ApiModelProperty(value = "区")
    private Long cpCAreaId;

    /**
     * 品项
     */
    @TableField(value = "ps_c_prodim_id")
    @ApiModelProperty(value = "品项")
    private Long psCProdimId;

    /**
     * 商品
     */
    @TableField(value = "ps_c_pro_id")
    @ApiModelProperty(value="商品")
    private Long psCProId;

    @TableField(value = "num_iid")
    @ApiModelProperty(value="平台商品id")
    private String numIid;

    /**
     * 商品编码
     */
    @TableField(value = "ps_c_pro_ecode")
    @ApiModelProperty(value="商品编码")
    private String psCProEcode;

    /**
     * 商品名称
     */
    @TableField(value = "ps_c_pro_ename")
    @ApiModelProperty(value="商品名称")
    private String psCProEname;

    /**
     * 物流公司档案
     */
    @TableField(value = "cp_c_logistics_id")
    @ApiModelProperty(value="物流公司档案")
    private Long cpCLogisticsId;

    /**
     * 物流公司档案
     */
    @TableField(value = "cp_c_logistics_ecode")
    @ApiModelProperty(value="物流公司档案")
    private String cpCLogisticsEcode;

    /**
     * 物流公司档案
     */
    @TableField(value = "cp_c_logistics_ename")
    @ApiModelProperty(value="物流公司档案")
    private String cpCLogisticsEname;

    /**
     * 物流类型
     */
    @TableField(value = "logistic_type")
    @ApiModelProperty(value="物流类型")
    private Integer logisticType;

    /**
     * 备注
     */
    @TableField(value = "REMARK")
    @ApiModelProperty(value="备注")
    private String remark;

    @TableField(value = "CP_C_PHY_WAREHOUSE_ID")
    @ApiModelProperty(value="实体仓ID")
    private Long cpCPhyWarehouseId;

    @TableField(value = "CP_C_PHY_WAREHOUSE_ECODE")
    @ApiModelProperty(value="实体仓编码")
    private String cpCPhyWarehouseEcode;

    @TableField(value = "CP_C_PHY_WAREHOUSE_ENAME")
    @ApiModelProperty(value="实体仓名称")
    private String cpCPhyWarehouseEname;

    private static final long serialVersionUID = -4372958769976161336L;

    public static final String COL_ID = "id";

    public static final String COL_PS_C_PRODIM_ID = "ps_c_prodim_id";

    public static final String COL_PS_C_PRO_ID = "ps_c_pro_id";

    public static final String COL_PS_C_PRO_ECODE = "ps_c_pro_ecode";

    public static final String COL_PS_C_PRO_ENAME = "ps_c_pro_ename";

    public static final String COL_CP_C_LOGISTICS_ID = "cp_c_logistics_id";

    public static final String COL_LOGISTIC_TYPE = "logistic_type";

    public static final String COL_AD_ORG_ID = "ad_org_id";

    public static final String COL_ISACTIVE = "isactive";

    public static final String COL_AD_CLIENT_ID = "ad_client_id";

    public static final String COL_OWNERID = "ownerid";

    public static final String COL_OWNERENAME = "ownerename";

    public static final String COL_OWNERNAME = "ownername";

    public static final String COL_CREATIONDATE = "creationdate";

    public static final String COL_MODIFIERID = "modifierid";

    public static final String COL_MODIFIERENAME = "modifierename";

    public static final String COL_MODIFIERNAME = "modifiername";

    public static final String COL_MODIFIEDDATE = "modifieddate";
}