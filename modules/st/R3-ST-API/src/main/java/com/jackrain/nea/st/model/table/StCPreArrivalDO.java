package com.jackrain.nea.st.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.*;
import com.jackrain.nea.es.annotations.Document;
import com.jackrain.nea.es.annotations.Field;
import com.jackrain.nea.es.constans.FieldType;
import com.jackrain.nea.st.model.base.SubBaseModel;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @title $
 * @Description 描述
 * @Date 2020/06/10
 * @Copyright 2019-2020
 */
@TableName(value = "ST_C_PRE_ARRIVAL")
@Data
@Document(index = "st_c_pre_arrival",type = "st_c_pre_arrival")
public class StCPreArrivalDO extends SubBaseModel {
    @JSONField(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "id", type = IdType.INPUT)
    @Field(type = FieldType.Long)
    private Long id;

    @JSONField(name = "ECODE")
    @Field(type = FieldType.Keyword)
    private String ecode;

    @JSONField(name = "ENAME")
    @Field(type = FieldType.Keyword)
    private String ename;

    @JSONField(name = "CP_C_DEST_ID")
    @Field(type = FieldType.Long)
    private Long cpCDestId;

    @JSONField(name = "CP_C_DEST_ECODE")
    @Field(type = FieldType.Keyword)
    private String cpCDestEcode;

    @JSONField(name = "CP_C_DEST_ENAME")
    @Field(type = FieldType.Keyword)
    private String cpCDestEname;

    @JSONField(name = "BEGIN_TIME")
    @Field(type = FieldType.Date)
    private Date beginTime;

    @JSONField(name = "END_TIME")
    @Field(type = FieldType.Date)
    private Date endTime;

    @JSONField(name = "PRE_ARRIVAL_STATUS")
    @Field(type = FieldType.Integer)
    private Integer preArrivalStatus;


}
