package com.jackrain.nea.st.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jackrain.nea.st.model.base.SubBaseModel;
import lombok.Data;

/**
 * @ClassName StCBnColumnListDO
 * @Description 班牛工单组件列表
 * <AUTHOR>
 * @Date 2024/11/13 17:41
 * @Version 1.0
 */
@Data
@TableName(value = "st_c_bn_column_list")
public class StCBnColumnListDO extends SubBaseModel {
    private static final long serialVersionUID = 3529829174807018402L;

    @JSONField(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    @JSONField(name = "PROJECT_ID")
    private Long projectId;

    @JSONField(name = "COLUMN_ID")
    private Long columnId;

    @JSONField(name = "name")
    private String name;

    @JSONField(name = "COLUMN_TYPE")
    private String columnType;

    @JSONField(name = "type")
    private String type;

    @JSONField(name = "BEHAVIOR_TYPE")
    private String behaviorType;

    @JSONField(name = "options")
    private String options;

    @JSONField(name = "RELATION_OPTIONS")
    private String relationOptions;

    @JSONField(name = "SON_COLUMN_BOS")
    private String sonColumnBos;

    @JSONField(name = "IS_INSIDE")
    private Integer isInside;
}
