package com.jackrain.nea.st.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jackrain.nea.st.model.base.SubBaseModel;
import lombok.Data;

/**
 * @ClassName StCBnWarehouseLogisticsConfigDO
 * @Description 班牛仓库物流配置
 * <AUTHOR>
 * @Date 2024/11/12 18:07
 * @Version 1.0
 */
@Data
@TableName(value = "st_c_bn_warehouse_logistics_config")
public class StCBnWarehouseLogisticsConfigDO extends SubBaseModel {
    private static final long serialVersionUID = 5270876861517765715L;

    @JSONField(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    @JSONField(name = "WAREHOUSE_CODE")
    private String warehouseCode;

    @JSONField(name = "WAREHOUSE_NAME")
    private String warehouseName;

    @JSONField(name = "LOGISTICS_NAME")
    private String logisticsName;

    @JSONField(name = "LOGISTICS_CODE")
    private String logisticsCode;

    @JSONField(name = "BN_LOGISTICS")
    private String bnLogistics;

    @JSONField(name = "BN_LOGISTICS_ID")
    private Long bnLogisticsId;
}
