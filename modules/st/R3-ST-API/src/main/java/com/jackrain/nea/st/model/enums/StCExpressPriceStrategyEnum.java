package com.jackrain.nea.st.model.enums;

import lombok.Getter;

import java.util.Objects;

/**
 * <AUTHOR>
 * @Date 2022/6/10 11:24
 * @Description 快递报价设置状态
 * @Version 1.0
 */
@Getter
public enum StCExpressPriceStrategyEnum {

    /**
     * 未提交"
     */
    UN_SUBMITTED(1, "未提交"),

    /**
     * 已提交"
     */
    SUBMITTED(2, "已提交"),

    /**
     * 未结案"
     */
    UN_CLOSED(0, "未结案"),

    /**
     * 已结案"
     */
    CLOSED(1, "已结案");


    @Getter
    private Integer key;
    @Getter
    private String desc;

    StCExpressPriceStrategyEnum(Integer key, String desc) {
        this.key = key;
        this.desc = desc;
    }

    public static StCExpressPriceStrategyEnum getByKey(Integer key) {
        for (StCExpressPriceStrategyEnum current : values()) {
            if (Objects.equals(current.getKey(), key)) {
                return current;
            }
        }
        return null;
    }

}
