package com.jackrain.nea.st.model.request;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * Descroption 物流区域设置
 *
 * <AUTHOR>
 * @Date 2022/8/11 14:23
 */
@Data
public class StCProLogisticStrategyQueryRequest implements Serializable {

    /**
     * 品项
     */
    private List<Long> psCProdimIdList;

    /**
     * 商品
     */
    private List<Long> psCProIdList;

    /**
     * 平台商品id
     */
    private List<String> numIids;

    /**
     * 省
     */
    private Long provinceId;

    /**
     * 市
     */
    private Long cityId;

    /**
     * 区
     */
    private Long areaId;

    /**
     * 实体仓
     */
    private Long warehouseId;

}
