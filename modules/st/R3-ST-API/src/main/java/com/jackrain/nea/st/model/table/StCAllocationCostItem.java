package com.jackrain.nea.st.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.*;
import com.jackrain.nea.st.model.base.SubBaseModel;
import lombok.Data;

import java.math.BigDecimal;

@TableName(value = "st_c_allocation_cost_item")
@Data
public class StCAllocationCostItem extends SubBaseModel {
    @JSONField(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    @JSONField(name = "ST_C_ALLOCATION_COST_ID")
    private Long stCAllocationCostId;

    @JSO<PERSON>ield(name = "CP_C_PHY_WAREHOUSE_ID")
    private Long cpCPhyWarehouseId;

    @JSONField(name = "ARRIVAL_DAYS")
    private Integer arrivalDays;

    @JSONField(name = "START_WEIGHT")
    private BigDecimal startWeight;

    @JSONField(name = "END_WEIGHT")
    private BigDecimal endWeight;

    @J<PERSON><PERSON>ield(name = "TRUNK_FREIGHT")
    private BigDecimal trunkFreight;

    @JSONField(name = "DELIVERY_FEE")
    private BigDecimal deliveryFee;

    @JSONField(name = "FREIGHT")
    private BigDecimal freight;

    @JSONField(name = "PREMIUM")
    private BigDecimal premium;

    @JSONField(name = "UNLOADING_FEE")
    private BigDecimal unloadingFee;

    @JSONField(name = "OTHER_FEE")
    private BigDecimal otherFee;

    @JSONField(name = "OWNERENAME")
    private String ownerename;

    @JSONField(name = "MODIFIERENAME")
    private String modifierename;
}