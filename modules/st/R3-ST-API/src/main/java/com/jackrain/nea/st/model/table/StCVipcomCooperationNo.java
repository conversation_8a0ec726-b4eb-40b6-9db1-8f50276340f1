package com.jackrain.nea.st.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jackrain.nea.st.model.base.SubBaseModel;
import lombok.Data;

import java.util.Date;

@TableName(value = "st_c_vipcom_cooperation_no")
@Data
public class StCVipcomCooperationNo extends SubBaseModel {
    @JSONField(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    @JSONField(name = "VENDOR_ID")
    private Integer vendorId;

    @JSONField(name = "COOPERATION_NO")
    private Integer cooperationNo;

    @JSONField(name = "WAREHOUSE")
    private String warehouse;

    @JSONField(name = "SELL_TIME_FROM")
    private Date sellTimeFrom;

    @JSONField(name = "SELL_TIME_TO")
    private Date sellTimeTo;

    @JSONField(name = "SCHEDULE_ID")
    private Integer scheduleId;

    @JSONField(name = "COOPERATION_NAME")
    private String cooperationName;

    @JSONField(name = "IS_STORE_DELIVERY")
    private Integer isStoreDelivery;

    @JSONField(name = "JITX_SUBTYPE")
    private String jitxSubtype;

    @JSONField(name = "AD_ORG_ID")
    private Long adOrgId;

    @JSONField(name = "ISACTIVE")
    private String isactive;

    @JSONField(name = "AD_CLIENT_ID")
    private Long adClientId;

    @JSONField(name = "OWNERID")
    private Long ownerid;

    @JSONField(name = "OWNERENAME")
    private String ownerename;

    @JSONField(name = "OWNERNAME")
    private String ownername;

    @JSONField(name = "CREATIONDATE")
    private Date creationdate;

    @JSONField(name = "MODIFIERID")
    private Long modifierid;

    @JSONField(name = "MODIFIERENAME")
    private String modifierename;

    @JSONField(name = "MODIFIERNAME")
    private String modifiername;

    @JSONField(name = "MODIFIEDDATE")
    private Date modifieddate;

    @JSONField(name = "DELID")
    private Long delid;

    @JSONField(name = "DELENAME")
    private String delename;

    @JSONField(name = "DELNAME")
    private String delname;

    @JSONField(name = "DEL_TIME")
    private Date delTime;
}