package com.jackrain.nea.st.model.enums;

import lombok.Getter;

import java.util.Objects;

/**
 * @program: r3-st
 * @description: 排除商品级别
 * @author: caomalai
 * @create: 2022-07-29 17:19
 **/
public enum AutoCheckProLevelEnum {
    CATEGORY(1, "品类"),
    PRO_CODE(2,"商品编码"),
    SKU_CODE(3,"商品条码"),
    PLATFORM_PRO(4,"平台商品ID"),
    PLATFORM_SKU(5,"平台条码ID");

    @Getter
    private Integer key;
    @Getter
    private String desc;

    AutoCheckProLevelEnum(Integer key, String desc) {
        this.key = key;
        this.desc = desc;
    }

    public static AutoCheckProLevelEnum getByKey(Integer key) {
        for (AutoCheckProLevelEnum current : values()) {
            if (Objects.equals(current.getKey(), key)) {
                return current;
            }
        }
        return null;
    }

    public static AutoCheckProLevelEnum getByDesc(String desc) {
        for (AutoCheckProLevelEnum current : values()) {
            if (Objects.equals(current.getDesc(), desc)) {
                return current;
            }
        }
        return null;
    }
}
