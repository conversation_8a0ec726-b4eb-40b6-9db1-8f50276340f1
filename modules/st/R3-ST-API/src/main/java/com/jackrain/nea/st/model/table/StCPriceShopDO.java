package com.jackrain.nea.st.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.*;
import com.jackrain.nea.st.model.base.SubBaseModel;
import lombok.Data;

@TableName(value = "st_c_price_shop")
@Data
public class StCPriceShopDO extends SubBaseModel {
    @JSONField(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    @J<PERSON>NField(name = "ST_C_PRICE_ID")
    private Long stCPriceId;

    @J<PERSON>NField(name = "CP_C_SHOP_ID")
    private Long cpCShopId;

    @J<PERSON><PERSON>ield(name = "CP_C_SHOP_TITLE")
    private String cpCShopTitle;

    @JSO<PERSON>ield(name = "OWNERENAME")
    private String ownerename;

    @JSONField(name = "MODIFIERE<PERSON><PERSON>")
    private String modifierename;
}