package com.jackrain.nea.st.model.request;

import com.alibaba.fastjson.annotation.JSONField;
import com.jackrain.nea.st.model.table.*;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR> liang
 * @description 预售卡单请求
 */
@Data
public class StDetentionPolicyRequest implements Serializable {


    @JSONField(name = "ST_C_DETENTION_POLICY")
    private StCDetentionPolicy stCDetentionPolicy;
    @JSONField(name = "ST_C_DETENTION_POLICY_ITEM")
    private List<StCDetentionPolicyItem> stCDetentionPolicyItem;

    private Long objId;

}
