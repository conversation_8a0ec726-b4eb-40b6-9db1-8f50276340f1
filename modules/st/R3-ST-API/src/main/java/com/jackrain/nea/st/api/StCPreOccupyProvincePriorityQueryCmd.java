package com.jackrain.nea.st.api;

import com.jackrain.nea.st.model.table.StCPreOccupyProvincePriority;
import com.jackrain.nea.sys.Command;
import com.jackrain.nea.sys.domain.ValueHolderV14;

/**
 * @ClassName StCPreOccupyProvincePriorityQueryCmd
 * @Description 省优先
 * <AUTHOR>
 * @Date 2025/3/4 10:01
 * @Version 1.0
 */
public interface StCPreOccupyProvincePriorityQueryCmd extends Command {

    ValueHolderV14<StCPreOccupyProvincePriority> queryByProvinceEcode(String provinceEcode);
}
