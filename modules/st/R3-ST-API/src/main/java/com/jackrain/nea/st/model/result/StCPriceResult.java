package com.jackrain.nea.st.model.result;

import com.alibaba.fastjson.JSON;
import com.jackrain.nea.st.model.table.StCPriceDO;
import com.jackrain.nea.st.model.table.StCPriceExcludeItemDO;
import com.jackrain.nea.st.model.table.StCPriceItemDO;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> zhuxing
 * @Date : 2022-07-07 15:31
 * @Description : 商品价格返回对象
 **/
@Data
public class StCPriceResult implements Serializable {

    private Long id;

    /**
     * 店铺
     */
    private Long cpCShopId;

    /**
     * 价格策略主表信息
     */
    private StCPriceDO stCPriceDO;

    /**
     * 商品价格策略明细
     */
    private List<StCPriceItemDO> stCPriceItemDOS;

    /**
     * 排除商品明细
     */
    private List<StCPriceExcludeItemDO> stCPriceExcludeItemDOS;

    public static void main(String[] args) {
        StCPriceResult result = new StCPriceResult();

        StCPriceDO stCPriceDO = new StCPriceDO();
        stCPriceDO.setId(1L);

        List<StCPriceItemDO> stCPriceItemDOS = new ArrayList<>();
        StCPriceItemDO stCPriceItemDO = new StCPriceItemDO();
        stCPriceItemDO.setId(100L);
        stCPriceItemDOS.add(stCPriceItemDO);

        List<StCPriceExcludeItemDO> stCPriceExcludeItemDOS = new ArrayList<>();
        StCPriceExcludeItemDO stCPriceExcludeItemDO = new StCPriceExcludeItemDO();
        stCPriceExcludeItemDO.setId(160L);
        stCPriceExcludeItemDOS.add(stCPriceExcludeItemDO);

        result.setId(1L);
        result.setCpCShopId(2L);
        result.setStCPriceDO(stCPriceDO);
        result.setStCPriceItemDOS(stCPriceItemDOS);
        result.setStCPriceExcludeItemDOS(stCPriceExcludeItemDOS);

        System.out.println("======="+ JSON.toJSONString(result));
    }

}
