package com.jackrain.nea.st.model.request.message;


import com.alibaba.fastjson.annotation.JSONField;
import com.burgeon.r3.sg.core.model.request.SgR3BaseRequest;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @Auther: chen<PERSON>
 * @Date: 2022-09-12 13:36
 * @Description:
 */

@Data
public class StCMessageStrategyBillSaveRequest extends SgR3BaseRequest implements Serializable {
    /**
     * 主表信息
     */
    @JSONField(name = "ST_C_MESSAGE_STRATEGY")
    private StCMessageStrategySaveRequest mainRequest;

    /**
     * 明细信息
     */
    @JSONField(name = "ST_C_MESSAGE_STRATEGY_ITEM")
    private List<StCMessageStrategySaveItemRequest> itemRequest;
}
