package com.jackrain.nea.st.model.request;

import com.alibaba.fastjson.annotation.JSONField;
import com.jackrain.nea.st.model.table.StCExchangeStrategyOrderDO;
import com.jackrain.nea.st.model.table.StCMergeOrderDO;
import lombok.Data;

import java.io.Serializable;

/**
 * 订单换货策略请求参数
 */
@Data
public class ExchangeStrategyOderRequest implements Serializable {
    @JSONField(name = "st_c_exchange_order_strategy")
    private StCExchangeStrategyOrderDO stCExchangeStrategyOrderDO;
    private Long objId;
}
