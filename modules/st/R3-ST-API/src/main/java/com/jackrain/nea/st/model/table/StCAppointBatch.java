package com.jackrain.nea.st.model.table;

import java.io.Serializable;
import java.util.Date;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jackrain.nea.st.model.base.SubBaseModel;
import com.jackrain.nea.sys.domain.BaseModel;
import lombok.Data;

/**
 * st_c_appoint_batch
 * <AUTHOR>
@TableName
@Data
public class StCAppointBatch extends SubBaseModel {
    @JSONField(name = "ID")
    private Long id;

    /**
     * 单据编号
     */
    @JSONField(name = "BILL_NO")
    private String billNo;

    /**
     * 店铺id
     */
    @JSONField(name = "SHOP_ID")
    private Long shopId;

    /**
     * 开始时间
     */
    @JSONField(name = "START_TIME")
    private Date startTime;

    /**
     * 结束时间
     */
    @JSONField(name = "END_TIME")
    private Date endTime;

    /**
     * 备注
     */
    @JSONField(name = "REMARKS")
    private String remarks;

    /**
     * 提交状态(1,未提交,2,已提交,3已作废)
     */
    @JSONField(name = "SUBMIT_STATUS")
    private Integer submitStatus;

    /**
     * 结案状态(0,未结案,1,已结案)
     */
    @JSONField(name = "CASE_STATUS")
    private Integer caseStatus;

    private static final long serialVersionUID = 1L;
}