package com.jackrain.nea.st.model.request;

import lombok.Data;

import java.io.Serializable;

/**
 * 班牛工单组件列表查询请求
 */
@Data
public class BnColumnListQueryRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    private Long id;

    /**
     * 项目ID
     */
    private Long projectId;

    /**
     * 列ID
     */
    private Long columnId;

    /**
     * 名称
     */
    private String name;

    /**
     * 列类型
     */
    private String columnType;

    /**
     * 类型
     */
    private String type;

    /**
     * 行为类型
     */
    private String behaviorType;

    /**
     * 选项
     */
    private String options;

    /**
     * 关联选项
     */
    private String relationOptions;

    /**
     * 子列
     */
    private String sonColumnBos;

    /**
     * 是否内部
     */
    private Integer isInside;

    /**
     * 是否激活
     */
    private String isactive;
}
