package com.jackrain.nea.st.api;

import com.jackrain.nea.st.model.vo.StCWarehouseLogisticStrategyImpVo;
import com.jackrain.nea.sys.Command;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;

import java.util.List;

/**
 * @program: r3-st
 * @description: 仓库物流设置导入
 * @author: caomalai
 * @create: 2022-06-23 15:34
 **/
public interface StCWarehouseLogisticStrategyImportCmd extends Command {
    ValueHolderV14 queryTemplateDownloadUrl();

    ValueHolderV14 importInvoiceList(List<StCWarehouseLogisticStrategyImpVo> invoiceImpVos, User user);
}
