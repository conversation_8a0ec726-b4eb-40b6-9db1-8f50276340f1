package com.jackrain.nea.st.model.enums;

import lombok.Getter;

/**
 * @description 数据操作参数
 * @author:洪艺安
 * @since: 2020/2/13
 * @create at : 2020/2/13 15:59
 */
public enum OperationTypeEnum {
    /**
     * 新增
     */
    ADD("ADD", "新增"),
    /**
     * 修改
     */
    MOD("MOD", "修改"),
    /**
     * 删除
     */
    DEL("DEL", "删除"),
    /**
     * 审核
     */
    AUDIT("AUDIT", "审核"),
    /**
     * 反审核
     */
    RESERVE_AUDIT("RESERVE_AUDIT", "反审核"),
    /**
     * 作废
     */
    VOID("VOID", "作废"),

    /**
     * 启用
     */
    OPEN("OPEN", "启用"),
    /**
     * 结案
     */
    FINISH("FINISH", "结案");
    
    @Getter
    private String operationName;
    @Getter
    private String operationValue;

    OperationTypeEnum(String operationValue, String operationName) {
        this.operationValue = operationValue;
        this.operationName = operationName;
    }

    public static String getNameByValue(String value) {
        String operationName = "";
        for (OperationTypeEnum e : OperationTypeEnum.values()) {
            if (e.getOperationValue().equals(value)) {
                operationName = e.getOperationName();
                break;
            }
        }
        return operationName;
    }


}
