<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.burgeon.r3</groupId>
        <artifactId>r3-st</artifactId>
        <version>3.0.0-SNAPSHOT</version>
        <relativePath>../../../pom.xml</relativePath>
    </parent>

    <modelVersion>4.0.0</modelVersion>
    <artifactId>r3-st-api</artifactId>
    <version>3.0.0-SNAPSHOT</version>
    <packaging>jar</packaging>

    <dependencies>
        <!-- r3-service -->
        <dependency>
            <groupId>com.burgeon.r3</groupId>
            <artifactId>r3-model-query</artifactId>
        </dependency>
        <dependency>
            <groupId>com.burgeon.r3</groupId>
            <artifactId>r3-service-core</artifactId>
        </dependency>

        <!-- r3-project -->
        <dependency>
            <groupId>com.burgeon.r3</groupId>
            <artifactId>r3-cp-ext-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.burgeon.r3</groupId>
            <artifactId>r3-cp-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.burgeon.r3</groupId>
            <artifactId>ac-finance-api</artifactId>
        </dependency>
    </dependencies>

</project>