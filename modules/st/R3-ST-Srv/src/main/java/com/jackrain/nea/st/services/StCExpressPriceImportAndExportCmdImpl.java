package com.jackrain.nea.st.services;

import com.jackrain.nea.st.api.StCExpressPriceImportAndExportCmd;
import com.jackrain.nea.st.model.request.StCExpressPriceStrategyPoi;
import com.jackrain.nea.st.model.result.StImportErrorMsgResult;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/6/13 17:09
 */
@Slf4j
@Component
//@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "st")
public class StCExpressPriceImportAndExportCmdImpl implements StCExpressPriceImportAndExportCmd {

    @Autowired
    private StCExpressPriceImportAndExportService stCExpressPriceImportAndExportService;

    @Override
    public ValueHolderV14<List<StImportErrorMsgResult>> importExpressPriceCost(List<StCExpressPriceStrategyPoi> importList, User user) {
        return stCExpressPriceImportAndExportService.importExpressPriceCost(importList,user);
    }

    @Override
    public String downloadImportErrMsg(User user, List<StImportErrorMsgResult> errMsgList) {
        return stCExpressPriceImportAndExportService.downloadImportErrMsg(user,errMsgList);
    }

    @Override
    public ValueHolderV14<String> exportExpressPriceCost(String ids,User user) {
        return stCExpressPriceImportAndExportService.exportExpressPriceCost(ids,user);
    }
}
