package com.jackrain.nea.st.services;

import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.st.api.StCDetentionPolicyCancelCmd;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @program: r3-st
 * @description: 预售卡单反审
 * @author: liuwj
 * @create: 2021-06-17 18:09
 **/
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "st")
public class StCDetentionPolicyCancelCmdImpl extends CommandAdapter implements StCDetentionPolicyCancelCmd {

    @Autowired
    private StCDetentionPolicyCancelService stCDetentionPolicyCancelService;

    @Override
    public ValueHolder execute(QuerySession querySession) throws NDSException {
        return stCDetentionPolicyCancelService.execute(querySession);
    }
}
