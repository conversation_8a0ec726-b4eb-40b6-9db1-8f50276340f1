package com.jackrain.nea.st.services;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.st.api.StCShopLogisticStrategyImportCmd;
import com.jackrain.nea.st.model.vo.StCShopLogisticStrategyImpVo;
import com.jackrain.nea.st.utils.ExportUtil;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.face.impl.UserImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @program: r3-st
 * @description: 仓库物流设置导入
 * @author: caomalai
 * @create: 2022-07-01 13:57
 **/
@Slf4j
@Component
public class StCShopLogisticStrategyImportCmdImpl extends CommandAdapter implements StCShopLogisticStrategyImportCmd {
    @Autowired
    private ExportUtil exportUtil;
    @Autowired
    private StCShopLogisticStrategySaveService stCShopLogisticStrategySaveService;

    @Override
    public ValueHolderV14 queryTemplateDownloadUrl() {
        ValueHolderV14 holderV14 = new ValueHolderV14(ResultCode.SUCCESS, "店铺物流设置头明细导入模板下载成功！");
        /**
         *  拼接Excel主表sheet表头字段
         * */
        String[] mainNames = {"店铺", "物流公司", "四级分类", "商品", "省", "市", "仓库", "卖家备注"};
        String[] mustNames = {"店铺", "物流公司"};
        String[] orderKeys = {"cpCShopName", "cpCLogisticsName"};

        User user = new UserImpl();
        ((UserImpl) user).setName("");
        List<String> mainList = Lists.newArrayList(mainNames);
        List<String> mustList = Lists.newArrayList(mustNames);
        List<String> orderKeyList = Lists.newArrayList(orderKeys);
        //生成Excel
        XSSFWorkbook hssfWorkbook = new XSSFWorkbook();
        hssfWorkbook = exportUtil.executeSheet(hssfWorkbook, "店铺物流设置头明细", "", mainList, mustList,
                orderKeyList, Lists.newArrayList(), false);
        String putMsg = exportUtil.saveFileAndPutOss(hssfWorkbook, "店铺物流设置头明细导入模板",
                user, "OSS-Bucket/EXPORT/ST_C_SHOP_LOGISTIC_STRATEGY/");
         holderV14.setData(putMsg);
        return holderV14;
    }

    @Override
    public ValueHolderV14 importInvoiceList(List<StCShopLogisticStrategyImpVo> invoiceImpVos, User user) {
        ValueHolderV14<Object> holderV14 = new ValueHolderV14<>(ResultCode.SUCCESS, "导入成功！");
        List<StCShopLogisticStrategyImpVo> checkedIpBInvoiceImpVos = checkValid(invoiceImpVos);
        List<StCShopLogisticStrategyImpVo> errorList =
                checkedIpBInvoiceImpVos.stream().filter(s -> StringUtils.isNotEmpty(s.getDesc())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(errorList)) {
            holderV14 = new ValueHolderV14<>();
            holderV14.setData(exportResut(errorList, user));
            holderV14.setCode(ResultCode.FAIL); //
            holderV14.setMessage("店铺物流设置头明细导入失败条数:[" + checkedIpBInvoiceImpVos.size() + "]，成功条数:[" + 0 + "]，详情见文件内容");
            return holderV14;
        }
        StCShopLogisticStrategySaveService bean = ApplicationContextHandle.getBean(StCShopLogisticStrategySaveService.class);
        int successNum = bean.batchSaveInvoice(checkedIpBInvoiceImpVos, user);
        int failNum = invoiceImpVos.size() - successNum;
        if (failNum > 0) {
            holderV14 = new ValueHolderV14<>();
            holderV14.setData(exportResut(invoiceImpVos, user));
            holderV14.setCode(ResultCode.FAIL); //
            holderV14.setMessage("店铺物流设置头明细导入失败条数:[" + failNum + "]，成功条数:[" + successNum + "]，详情见文件内容");
        } else {
            holderV14.setMessage("店铺物流设置头明细导入成功条数:[" + successNum + "]");
        }
        log.info("店铺物流设置头明细导入结果 {}", JSON.toJSONString(holderV14));
        return holderV14;
    }

    /**
     * 参数有效性校验
     * @param invoiceImpVos
     * @return
     */
    private List<StCShopLogisticStrategyImpVo> checkValid(List<StCShopLogisticStrategyImpVo> invoiceImpVos) {
        List<StCShopLogisticStrategyImpVo> ipBInvoiceImpVos = new ArrayList<>();
        Map<String, StCShopLogisticStrategyImpVo> repeatCheckMap = new HashMap<>();
        // 数据缺失校验
        StringBuilder checkMessage = new StringBuilder();
        for (StCShopLogisticStrategyImpVo vo : invoiceImpVos) {
            if (Objects.isNull(vo)) {
                checkMessage.append("[店铺物流设置入参不能为空]");
            } else {
                if (Objects.isNull(vo.getCpCShopName())) {
                    checkMessage.append("[店铺不能为空]");
                }
                if (Objects.isNull(vo.getCpCLogisticsName())) {
                    checkMessage.append("[物流公司不能为空]");
                }
                if (!checkItemByVo(vo)) {
                    checkMessage.append("[规则不正确，请检查]");
                }
            }
            if (StringUtils.isNotEmpty(checkMessage.toString())) {
                vo.setDesc(checkMessage.toString());
                checkMessage.setLength(0);
                ipBInvoiceImpVos.add(vo);
            }else {
                //通过店铺和物流公司判断重复
                String repeatKey = vo.getCpCShopName() + vo.getCpCLogisticsName() + vo.getProvinceName() + vo.getCityName()
                        + vo.getWarehouseName() + vo.getPsCProCode() + vo.getPsCProdimNanme() + vo.getSellerRemark();
                if (!repeatCheckMap.containsKey(repeatKey)) {
                    repeatCheckMap.put(repeatKey, vo);
                } else {
                    vo.setDesc("[物流公司信息已存在！,与第" + repeatCheckMap.get(repeatKey).getRowNum() + "行重复]");
                }
                ipBInvoiceImpVos.add(vo);
            }
        }
        return ipBInvoiceImpVos;
    }

    /**
     * 数据校验
     *
     * @param item
     * @return
     */
    private boolean checkItemByVo(StCShopLogisticStrategyImpVo item) {
        //卖家备注+物流
        if (StringUtils.isNotEmpty(item.getSellerRemark()) && StringUtils.isEmpty(item.getProvinceName())
                && StringUtils.isEmpty(item.getCityName()) && StringUtils.isEmpty(item.getWarehouseName())
                && StringUtils.isEmpty(item.getPsCProCode()) && StringUtils.isEmpty(item.getPsCProdimNanme())) {
            return true;
        }
        //省+仓+市+商品+物流
        if (StringUtils.isNotEmpty(item.getProvinceName()) && StringUtils.isNotEmpty(item.getCityName())
                && StringUtils.isNotEmpty(item.getWarehouseName()) && StringUtils.isNotEmpty(item.getPsCProCode())
                && StringUtils.isEmpty(item.getPsCProdimNanme()) && StringUtils.isEmpty(item.getSellerRemark())) {
            return true;
        }
        //省+仓+市+四级分类+物流
        if (StringUtils.isNotEmpty(item.getProvinceName()) && StringUtils.isNotEmpty(item.getCityName())
                && StringUtils.isNotEmpty(item.getWarehouseName()) && StringUtils.isEmpty(item.getPsCProCode())
                && StringUtils.isNotEmpty(item.getPsCProdimNanme()) && StringUtils.isEmpty(item.getSellerRemark())) {
            return true;
        }
        //省+仓+商品+物流
        if (StringUtils.isNotEmpty(item.getProvinceName()) && StringUtils.isEmpty(item.getCityName())
                && StringUtils.isNotEmpty(item.getWarehouseName()) && StringUtils.isNotEmpty(item.getPsCProCode())
                && StringUtils.isEmpty(item.getPsCProdimNanme()) && StringUtils.isEmpty(item.getSellerRemark())) {
            return true;
        }
        //仓+商品+物流
        if (StringUtils.isEmpty(item.getProvinceName()) && StringUtils.isEmpty(item.getCityName())
                && StringUtils.isNotEmpty(item.getWarehouseName()) && StringUtils.isNotEmpty(item.getPsCProCode())
                && StringUtils.isEmpty(item.getPsCProdimNanme()) && StringUtils.isEmpty(item.getSellerRemark())) {
            return true;
        }
        //省+仓+物流
        if (StringUtils.isNotEmpty(item.getProvinceName()) && StringUtils.isEmpty(item.getCityName())
                && StringUtils.isNotEmpty(item.getWarehouseName()) && StringUtils.isEmpty(item.getPsCProCode())
                && StringUtils.isEmpty(item.getPsCProdimNanme()) && StringUtils.isEmpty(item.getSellerRemark())) {
            return true;
        }
        //商品+物流
        if (StringUtils.isEmpty(item.getProvinceName()) && StringUtils.isEmpty(item.getCityName())
                && StringUtils.isEmpty(item.getWarehouseName()) && StringUtils.isNotEmpty(item.getPsCProCode())
                && StringUtils.isEmpty(item.getPsCProdimNanme()) && StringUtils.isEmpty(item.getSellerRemark())) {
            return true;
        }
        //四级分类+物流
        if (StringUtils.isEmpty(item.getProvinceName()) && StringUtils.isEmpty(item.getCityName())
                && StringUtils.isEmpty(item.getWarehouseName()) && StringUtils.isEmpty(item.getPsCProCode())
                && StringUtils.isNotEmpty(item.getPsCProdimNanme()) && StringUtils.isEmpty(item.getSellerRemark())) {
            return true;
        }
        //仓+物流
        if (StringUtils.isEmpty(item.getProvinceName()) && StringUtils.isEmpty(item.getCityName())
                && StringUtils.isNotEmpty(item.getWarehouseName()) && StringUtils.isEmpty(item.getPsCProCode())
                && StringUtils.isEmpty(item.getPsCProdimNanme()) && StringUtils.isEmpty(item.getSellerRemark())) {
            return true;
        }
        return false;
    }

    /**
     * @param invoiceImpVos
     * @param user
     * @return
     */
    private String exportResut(List<StCShopLogisticStrategyImpVo> invoiceImpVos, User user) {
        List<StCShopLogisticStrategyImpVo> errorList = invoiceImpVos.parallelStream().filter(x -> x.getDesc() != null).collect(Collectors.toList());
        // 列名
        String[] columnNames = {"主表行号", "错误原因"};
        List<String> c = Lists.newArrayList(columnNames);
        // map中的key
        String[] keys = {"rowNum", "desc"};
        List<String> k = Lists.newArrayList(keys);
        Workbook hssfWorkbook = exportUtil.execute("店铺物流设置", "店铺物流设置", c, k, errorList);
        return exportUtil.saveFileAndPutOss(hssfWorkbook, "店铺物流设置入错误信息", user, "OSS-Bucket/IMPORT/ST_C_SHOP_LOGISTIC_STRATEGY/");
    }
}
