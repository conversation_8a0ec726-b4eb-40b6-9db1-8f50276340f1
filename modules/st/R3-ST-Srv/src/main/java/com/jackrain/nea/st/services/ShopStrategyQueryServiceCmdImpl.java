package com.jackrain.nea.st.services;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.st.api.ShopStrategyQueryServiceCmd;
import com.jackrain.nea.st.model.result.MergeOrderStrategyResult;
import com.jackrain.nea.st.mapper.StCOrderPriceItemMapper;
import com.jackrain.nea.st.model.result.StCPriceResult;
import com.jackrain.nea.st.model.result.StCShopStrategyLogisticsItemResult;
import com.jackrain.nea.st.model.table.*;
import com.jackrain.nea.st.model.result.StCAutoCheckResult;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.utility.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;

/**
 * @description: 店铺策略服务接口
 * @author: 郑小龙
 * @date: 2019-04-03 16:22
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "st")
public class ShopStrategyQueryServiceCmdImpl implements ShopStrategyQueryServiceCmd {
    @Autowired
    private ShopStrategyQueryService service;

    @Autowired
    private StrategyCenterService strategyCenterService;

    @Autowired
    private StCOrderPriceItemMapper stCOrderPriceItemMapper;

    @Override
    public ValueHolderV14<StCShopStrategyDO> queryShopStrategyById(Long id) throws NDSException {
        return service.queryShopStrategyById(id);
    }

    @Override
    public StCShopStrategyDO selectOcStCShopStrategyByCpCshopId(Long shopId) {
        return service.selectOcStCShopStrategyByCpCshopId(shopId);
    }

    @Override
    public List<String> selectDiffpriceskuList(Long shopId, List<String> skuEcodeList) {
        return service.selectDiffpriceskuList(shopId, skuEcodeList);
    }

    @Override
    public List<StCShopStrategyItemDO> queryShopStrategyItem(Long shopId) {
        return service.queryShopStrategyItem(shopId);
    }

    @Override
    public List<Long> queryPriceList(Long shopId) {
        return service.queryPriceList(shopId);
    }

    @Override
    public List<BigDecimal> queryPriceTotal(List<Long> stCPriceList, Long proId) {
        return service.queryPriceTotal(stCPriceList, proId);
    }

    /**
     * 后续接口需要删除
     * @param shopId
     * @return
     */
    @Override
    public StCAutoCheckDO queryOcStCAutocheck(Long shopId) {

        return service.queryOcStCAutocheck(shopId);
    }
    @Override
    public StCAutoCheckResult queryOcStCAutoCheckWithItems(Long shopId){
        StCAutoCheckDO stCAutoCheckDO = service.queryOcStCAutocheck(shopId);
        if(Objects.nonNull(stCAutoCheckDO)){
            Long checkId = stCAutoCheckDO.getId();
            List<StCAutoCheckExcludeProductDO> stCAutoCheckExcludeProductDOS =
                    service.queryOcStCAutoCheckExcludeProduct(checkId);
            List<StCAutoCheckAutoTimeDO> stCAutoCheckAutoTimeDOS =
                    service.queryOcStCAutoCheckAutoTime(checkId);

            StCAutoCheckResult stCAutoCheckResult = new StCAutoCheckResult();
            stCAutoCheckResult.setStCAutoCheckDO(stCAutoCheckDO);
            stCAutoCheckResult.setAutoTimes(stCAutoCheckAutoTimeDOS);
            stCAutoCheckResult.setExcludeProducts(stCAutoCheckExcludeProductDOS);
            return stCAutoCheckResult;
        }
        return null;
    }

    @Override
    public StCAutoInvoiceDO queryStCAutoInvoice(Long shopId) {
        return service.queryStCAutoInvoice(shopId);
    }

    @Override
    public List<StCAutoInvoiceDO> queryAllAutoInvoice() {
        return service.queryAllAutoInvoice();
    }

    @Override
    public boolean isExitsRefundOrderStrategy(Long shopId) {
        return service.isExitsRefundOrderStrategy(shopId);
    }

    @Override
    public boolean isDifferenPriceSku(Long shopId, String skuEcode) {
        return service.isDifferenPriceSku(shopId, skuEcode);
    }

    @Override
    public Long queryDefaultWarehouse(Long shopId) {
        return service.queryDefaultWarehouse(shopId);
    }

    @Override
    public List<StCMergeOrderDO> queryAllMergeOrder() { return service.queryAllMergeOrder(); }

    /**
     * description：斯凯奇项目合单策略-新增品类限制需求 增加品类限制明细查询返回
     *
     * <AUTHOR>
     * @date 2021/5/13
     */
    @Override
    public List<MergeOrderStrategyResult> queryAllMergeOrderInfo() {
        return service.queryAllMergeOrderInfo();
    }

    //@Override
    //public Set<Long> queryMergeShopAllShopIds() {
    //    return service.queryMergeShopAllShopIds();
    //}

    //@Override
    //public Map<Long, StCMergeOrderDO> queryAllMergeShop() {
    //    return service.queryAllMergeShop();
    //}

    //@Override
    //public StCMergeOrderDO queryMergeShopByShopId(Long shopId) {
    //    return service.queryMergeShopByShopId(shopId);
    //}

    @Override
    public List<StCShopStrategyDO> queryAllShopStrategy() { return service.queryAllShopStrategy(); }
    @Override
    public List<StCShopStrategyDO> queryOrderLoadShopStrategy() { return service.queryOrderLoadShopStrategy(); }

    @Override
    public List<StCExchangeStrategyOrderDO> queryAllExchangeShopStrategy() {
        return service.queryAllExchangeShopStrategy();
    }

    @Override
    public StCOrderPriceItemDO queryStCOrderPriceItemByPriceId(Long priceId,String policyType) {
        log.info(LogUtil.format("ShopStrategyQueryServiceCmdImpl.StCOrderPriceItemDO priceId/policyType=", priceId,
                policyType));
        QueryWrapper<StCOrderPriceItemDO> queryWrapper =new QueryWrapper<>();
        queryWrapper.eq("ST_C_PRICE_ID",priceId);
        queryWrapper.eq("POLICY_TYPE",policyType);
        StCOrderPriceItemDO  stCOrderPriceItemDO = stCOrderPriceItemMapper.selectOne(queryWrapper);
        return stCOrderPriceItemDO;
    }

    @Override
    public ValueHolderV14<StCPriceResult> queryPricesByShopId(Long shopId) {
        return service.queryPricesByShopId(shopId);
    }

    @Override
    public StCShopStrategyLogisticsItemResult queryShopStrategyLogisticsList(Long id) {
        return service.queryShopStrategyLogisticsList(id);
    }
}
