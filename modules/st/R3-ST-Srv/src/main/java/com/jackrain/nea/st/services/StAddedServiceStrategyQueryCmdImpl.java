package com.jackrain.nea.st.services;

import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.st.api.StAddedServiceStrategyQueryCmd;
import com.jackrain.nea.st.mapper.StAddedServiceTypeDocMapper;
import com.jackrain.nea.st.model.result.StAddedServiceStrategyDocResult;
import com.jackrain.nea.st.model.result.StAddedServiceStrategyQueryResult;
import com.jackrain.nea.st.model.table.StAddedServiceStrategyDO;
import com.jackrain.nea.st.model.table.StAddedServiceStrategyDetailDO;
import com.jackrain.nea.st.model.table.StAddedServiceTypeDocDO;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * @program: ryytn-st-v3.0
 * @description: 增值服务查询实现
 * @author: haiyang
 * @create: 2023-10-25 17:25
 **/
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "st")
public class StAddedServiceStrategyQueryCmdImpl implements StAddedServiceStrategyQueryCmd {

    @Autowired
    private StAddedServiceStrategyQueryService addedServiceStrategyQueryService;
    @Autowired
    private StAddedServiceTypeDocMapper stAddedServiceTypeDocMapper;

    @Override
    public ValueHolderV14<StAddedServiceStrategyQueryResult> selectByTypeDocNameAndCpCPhyWarehouseId(String typeName, Long cpCPhyWarehouseId) {
        log.info("selectByTypeDocNameAndCpCPhyWarehouseId.param: {}-{}", typeName, cpCPhyWarehouseId);
        ValueHolderV14<StAddedServiceStrategyQueryResult> valueHolderV14 = new ValueHolderV14<>();
        StAddedServiceTypeDocDO addedServiceTypeDocDO = addedServiceStrategyQueryService.selectDocByTypeName(typeName);
        if (null == addedServiceTypeDocDO || addedServiceTypeDocDO.getIsactive().equals("N")) {
            valueHolderV14.setCode(-1);
            valueHolderV14.setMessage("记录为空或档案被停用");
            return valueHolderV14;
        }
        StAddedServiceStrategyDetailDO strategyDetailDO = addedServiceStrategyQueryService.selectDetailByTypeDocIdAndCpCPhyWarehouseId(addedServiceTypeDocDO.getId(), cpCPhyWarehouseId);
        if (null != strategyDetailDO) {
            Long strategyId = strategyDetailDO.getAddedStrategyId();
            StAddedServiceStrategyDO strategyDO = addedServiceStrategyQueryService.selectById(strategyId);
            StAddedServiceStrategyQueryResult result = buildResult(addedServiceTypeDocDO, strategyDO, strategyDetailDO);
            valueHolderV14.setCode(0);
            valueHolderV14.setData(result);
        } else {
            valueHolderV14.setCode(-1);
            valueHolderV14.setMessage("无匹配信息");
        }
        return valueHolderV14;
    }

    @Override
    public ValueHolderV14<StAddedServiceStrategyDocResult> selectDocInfoByTypeDocName(String typeDocName) {
        StAddedServiceTypeDocDO addedServiceTypeDocDO = stAddedServiceTypeDocMapper.selectByTypeName(typeDocName);
        ValueHolderV14<StAddedServiceStrategyDocResult> valueHolderV14 = new ValueHolderV14<>();
        if (null == addedServiceTypeDocDO || addedServiceTypeDocDO.getIsactive().equals("N")) {
            valueHolderV14.setCode(-1);
            valueHolderV14.setMessage("记录为空或档案已被停用");
            return valueHolderV14;
        }
        StAddedServiceStrategyDocResult result = new StAddedServiceStrategyDocResult();
        result.setId(addedServiceTypeDocDO.getId());
        result.setAddedTypeCode(addedServiceTypeDocDO.getAddedTypeCode());
        result.setAddedTypeName(addedServiceTypeDocDO.getAddedTypeName());
        valueHolderV14.setCode(0);
        valueHolderV14.setData(result);
        return valueHolderV14;
    }

    @Override
    public ValueHolderV14<List<StAddedServiceStrategyDocResult>> queryAllDoc() {
        List<StAddedServiceStrategyDocResult> resultList = new ArrayList<>();
        List<StAddedServiceTypeDocDO> docList = stAddedServiceTypeDocMapper.query();
        ValueHolderV14<List<StAddedServiceStrategyDocResult>> valueHolderV14 = new ValueHolderV14<>();
        if (CollectionUtils.isEmpty(docList)) {
            valueHolderV14.setCode(ResultCode.SUCCESS);
            valueHolderV14.setMessage("success");
            valueHolderV14.setData(null);
            return valueHolderV14;
        }
        for (StAddedServiceTypeDocDO doc : docList) {
            StAddedServiceStrategyDocResult result = new StAddedServiceStrategyDocResult();
            result.setId(doc.getId());
            result.setAddedTypeCode(doc.getAddedTypeCode());
            result.setAddedTypeName(doc.getAddedTypeName());
            resultList.add(result);
        }
        valueHolderV14.setCode(ResultCode.SUCCESS);
        valueHolderV14.setMessage("success");
        valueHolderV14.setData(resultList);
        return valueHolderV14;
    }

    private StAddedServiceStrategyQueryResult buildResult(StAddedServiceTypeDocDO addedServiceTypeDocDO,
                                                          StAddedServiceStrategyDO strategyDO,
                                                          StAddedServiceStrategyDetailDO strategyDetailDO) {
        StAddedServiceStrategyQueryResult result = new StAddedServiceStrategyQueryResult();
        result.setAddedStrategyId(strategyDetailDO.getAddedStrategyId());
        result.setAddedStrategyCode(strategyDO.getAddedStrategyCode());
        result.setAddedStrategyName(strategyDO.getAddedStrategyName());
        result.setAddedTypeDocId(strategyDetailDO.getAddedTypeDocId());
        result.setAddedTypeCode(addedServiceTypeDocDO.getAddedTypeCode());
        result.setAddedTypeName(addedServiceTypeDocDO.getAddedTypeName());
        result.setUnitPrice(strategyDetailDO.getUnitPrice());
        result.setCpCPhyWarehouseId(strategyDO.getCpCPhyWarehouseId());
        return result;
    }
}
