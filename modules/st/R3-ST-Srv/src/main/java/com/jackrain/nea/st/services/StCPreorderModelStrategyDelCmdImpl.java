package com.jackrain.nea.st.services;

import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.st.api.StCPreorderModelStrategyDelCmd;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @ClassName StCPreorderModelStrategyDelCmdImpl
 * @Description 预导入模板删除
 * <AUTHOR>
 * @Date 2022/12/23 16:44
 * @Version 1.0
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "st")
public class StCPreorderModelStrategyDelCmdImpl extends CommandAdapter implements StCPreorderModelStrategyDelCmd {

    @Autowired
    private StCPreorderModelStrategyService modelStrategyService;

    @Override
    public ValueHolder execute(QuerySession querySession) throws NDSException {
        return modelStrategyService.delModelStrategy(querySession);
    }
}
