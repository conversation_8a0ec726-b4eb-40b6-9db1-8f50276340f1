package com.jackrain.nea.st.services;

import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.st.api.StCKeywordsInterceptStrategyQueryCmd;
import com.jackrain.nea.st.model.table.StCKeywordsInterceptStrategy;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/3/10
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "st")
public class StCKeywordsInterceptStrategyQueryImpl extends CommandAdapter implements StCKeywordsInterceptStrategyQueryCmd {
    @Autowired
    private StCKeywordsInterceptStrategyService service;

    @Override
    public ValueHolderV14<List<StCKeywordsInterceptStrategy>> queryByPlatformId(Long platformId) {
        ValueHolderV14<List<StCKeywordsInterceptStrategy>> v14 =
                new ValueHolderV14(ResultCode.SUCCESS, "查询关键字快递拦截策略成功！");
        if (platformId == null) {
            v14.setCode(ResultCode.FAIL);
            v14.setMessage("来源平台id不能为空");
            return v14;
        }
        try {
            List<StCKeywordsInterceptStrategy> strategyList = service.queryByPlatformId(platformId);
            v14.setData(strategyList);
        } catch (Exception e) {
            v14.setCode(ResultCode.FAIL);
            v14.setMessage(e.getMessage());
            return v14;
        }
        return v14;
    }
}

