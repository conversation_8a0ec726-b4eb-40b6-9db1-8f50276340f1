package com.jackrain.nea.st.services;

import com.jackrain.nea.st.api.StCOrderPushDelayStrategyQueryCmd;
import com.jackrain.nea.st.model.table.StCOrderPushDelayStrategy;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @Description 订单推单延迟策略查询
 * @Date 16:36 2020/7/1
 **/
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "st")
public class StCOrderPushDelayStrategyStrategyQueryCmdImpl implements StCOrderPushDelayStrategyQueryCmd {

    @Autowired
    private StCOrderPushDelayStrategyQueryService stCOrderPushDelayStrategyQueryService;

    @Override
    public StCOrderPushDelayStrategy queryOrderPushDelayStrategy(Long shopId) {
        return stCOrderPushDelayStrategyQueryService.queryOrderPushDelayStrategy(shopId);
    }

    @Override
    public List<StCOrderPushDelayStrategy> queryOrderPushDelayStrategyAll() {
        return stCOrderPushDelayStrategyQueryService.queryOrderPushDelayStrategyAll();
    }
}
