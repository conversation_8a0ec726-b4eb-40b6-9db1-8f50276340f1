package com.jackrain.nea.st.services;

import com.jackrain.nea.st.api.StCVipUrgentStrategyQueryCmd;
import com.jackrain.nea.st.model.result.StCVipUrgentStrategyResult;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR> zhuxing
 * @Date : 2021-10-25 11:35
 * @Description : 唯品会补货加急策略 - 查询服务
 **/

@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "st")
public class StCVipUrgentStrategyQueryCmdImpl extends CommandAdapter implements StCVipUrgentStrategyQueryCmd {

    @Autowired
    private StCVipUrgentStrategyQueryService stCVipUrgentStrategyQueryService;

    @Override
    public ValueHolderV14<StCVipUrgentStrategyResult> queryVipUrgentStrategyByShopId(Long shopId) {
        return stCVipUrgentStrategyQueryService.queryVipUrgentStrategyByShopId(shopId);
    }
}
