package com.jackrain.nea.st.services;

import org.apache.dubbo.config.annotation.Service;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.st.api.VipcomMailSetColumnSelectCmd;
import com.jackrain.nea.st.model.table.StCVipcomMailDO;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @Descroption 唯品会邮件设置获取字段查询接口
 * <AUTHOR>
 * @Date 2019/4/23 17:09
 */
@Slf4j
@Component
//@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "st")
public class VipcomMailSetColumnSelectCmdImpl extends CommandAdapter implements VipcomMailSetColumnSelectCmd {

    @Autowired
    private VipcomMailSetColumnSelectService vipcomMailSetColumnSelectService;

    @Override
    public ValueHolderV14<StCVipcomMailDO> selectMailDatasByid(Long id) throws NDSException {
        return vipcomMailSetColumnSelectService.selectMailDatasByid(id);
    }
}
