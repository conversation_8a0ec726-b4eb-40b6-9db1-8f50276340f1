package com.jackrain.nea.st.services;

import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.st.api.ExchangeStrategyOrderVoidCmd;
import com.jackrain.nea.st.api.MergeOrderVoidCmd;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR> ruan.gz
 * @Description : 作废换货策略
 * @Date : 2020/6/19
 **/
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "st")
public class ExchangeStrategyOrderVoidCmdImpl extends CommandAdapter implements ExchangeStrategyOrderVoidCmd {

    @Autowired
    private ExchangeStrategyOrderVoidService exchangeStrategyOrderVoidService;

    @Override
    public ValueHolder execute(QuerySession querySession) throws NDSException {

        return exchangeStrategyOrderVoidService.voidOrder(querySession);
    }
}
