package com.jackrain.nea.st.services;

import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.st.api.StCAllocationStorageCostDelCmd;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @Date 2023/8/16 18:05
 * @Description
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "st")
public class StCAllocationStorageCostDelCmdImpl extends CommandAdapter implements StCAllocationStorageCostDelCmd {

    @Resource
    private StCAllocationStorageCostDelService service;

    @Override
    public ValueHolder execute(QuerySession session) throws NDSException {
        return service.execute(session);
    }
}
