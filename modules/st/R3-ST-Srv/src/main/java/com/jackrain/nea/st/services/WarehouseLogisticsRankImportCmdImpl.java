package com.jackrain.nea.st.services;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.st.api.WarehouseLogisticsRankImportCmd;
import com.jackrain.nea.st.model.result.WarehouseLogisticsRankResult;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @author:huang.zizai
 * @since: 2019/8/15
 * @create at : 2019/8/15 14:12
 */
@Slf4j
@Component
//@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "st")
public class WarehouseLogisticsRankImportCmdImpl extends CommandAdapter implements WarehouseLogisticsRankImportCmd {
    @Autowired
    private WarehouseLogisticsRankImportService importService;

    @Override
    public ValueHolderV14 downloadTemp(JSONObject obj, User user) {
        return importService.downloadTemp(obj, user);
    }

    @Override
    public ValueHolderV14 importWarehouseLogisticsRank(Long objid, List<WarehouseLogisticsRankResult> rankResultList, User user) {
        return importService.importWarehouseLogisticsRank(objid, rankResultList, user);
    }
}
