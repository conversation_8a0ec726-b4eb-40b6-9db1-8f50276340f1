package com.jackrain.nea.st.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.jackrain.nea.cpext.utils.ValueHolderUtils;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.st.annotation.StOperationLog;
import com.jackrain.nea.st.api.StCWarehouseLogisticStrategySaveCmd;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @program: r3-st
 * @description: 仓库物流设置保存
 * @author: caomalai
 * @create: 2022-06-23 15:36
 **/
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "st")
public class StCWarehouseLogisticStrategySaveCmdImpl extends CommandAdapter implements StCWarehouseLogisticStrategySaveCmd {
    @Autowired
    private StCWarehouseLogisticStrategyService stCWarehouseLogisticStrategyService;

    @StOperationLog(mainTableName = "ST_C_WAREHOUSE_LOGISTIC_STRATEGY", itemsTableName = "ST_C_WAREHOUSE_LOGISTIC_STRATEGY_ITEM")
    @Override
    public ValueHolder execute(QuerySession querySession) throws NDSException {
        DefaultWebEvent event = querySession.getEvent();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(event.getParameterValue("param"), "yyyy-MM-dd HH:mm:ss", SerializerFeature.WriteMapNullValue), Feature.OrderedField);
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("StCWarehouseLogisticStrategySaveCmdImpl param：{}", "仓库物流设置"), param);
        }
        User user = querySession.getUser();
        Long objid = null;
        String tableName = param.getString("table");
        try{
            objid = stCWarehouseLogisticStrategyService.save(param,user);
        }catch (Exception e){
            if(log.isErrorEnabled()){
                log.error(LogUtil.format("新增异常"),e);
            }
            return ValueHolderUtils.fail(e.getMessage());
        }
        return ValueHolderUtils.success("新增成功！",ValueHolderUtils.createAddErrorData(tableName, objid, null));
    }
}
