package com.jackrain.nea.st.services;

import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.st.api.StCDepositPreSaleSinkCancelCmd;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @ClassName : StCDepositPreSaleSinkCancelCmdImpl  
 * @Description : 预下沉反审核
 * <AUTHOR>  YCH
 * @Date: 2021-09-24 10:16  
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "st")
public class StCDepositPreSaleSinkCancelCmdImpl  extends CommandAdapter implements StCDepositPreSaleSinkCancelCmd {

    @Autowired
    private StCDepositPreSaleSinkCancelService stCDepositPreSaleSinkCancelService;

    @Override
    public ValueHolder execute(QuerySession querySession) throws NDSException {
        return stCDepositPreSaleSinkCancelService.execute(querySession);
    }

}
