package com.jackrain.nea.st.services;

import com.jackrain.nea.st.api.StCUnfullcarCostItemQueryCmd;
import com.jackrain.nea.st.request.StCUnfullcarCostArrivalDaysRequest;
import com.jackrain.nea.st.result.StCUnfullcarCostArrivalDaysResponse;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 零担报价明细查询
 *
 * <AUTHOR>
 * @since 2024-02-27 10:49
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "st")
public class StCUnfullcarCostItemQueryCmdImpl implements StCUnfullcarCostItemQueryCmd {
    @Resource
    private StCUnfullcarCostItemQueryService stCUnfullcarCostItemQueryService;

    @Override
    public ValueHolderV14<StCUnfullcarCostArrivalDaysResponse> queryArrivalDays(StCUnfullcarCostArrivalDaysRequest request) {
        return stCUnfullcarCostItemQueryService.queryArrivalDays(request);
    }
}
