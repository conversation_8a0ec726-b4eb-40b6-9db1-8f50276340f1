package com.jackrain.nea.st.services;

import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.st.api.StCExpressPriceStrategySaveCmd;
import com.jackrain.nea.st.api.StCExpressPriceStrategySubmitCmd;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @Date 2022/6/13 9:58
 * @Description 快递报价设置提交策略
 * @Version 1.0
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "st")
public class StCExpressPriceStrategySubmitCmdImpl extends CommandAdapter implements StCExpressPriceStrategySubmitCmd {
    @Autowired
    private StCExpressPriceStrategySubmitService stCExpressPriceStrategySubmitService;

    @Override
    public ValueHolder execute(QuerySession querySession) throws NDSException {
        return stCExpressPriceStrategySubmitService.execute(querySession);
    }
}
