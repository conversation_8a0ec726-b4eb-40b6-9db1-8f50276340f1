package com.jackrain.nea.st.services;

/**
 * description:预下沉策略删除
 * @Author:  liuwenjin
 * @Date 2021/9/23 8:01 下午
 */

import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.st.api.StCDepositPreSaleSinkDeleteCmd;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * description:定金预售预下沉策略保存实现类
 * @Author:  liuwenjin
 * @Date 2021/9/23 4:12 下午
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "st")
public class StCDepositPreSaleSinkDeleteCmdImpl extends CommandAdapter implements StCDepositPreSaleSinkDeleteCmd {
    @Autowired
    private StCDepositPreSaleSinkDeleteService stCDepositPreSaleSinkDeleteService;

    @Override
    public ValueHolder execute(QuerySession querySession) throws NDSException {
        return stCDepositPreSaleSinkDeleteService.execute(querySession);
    }

}
