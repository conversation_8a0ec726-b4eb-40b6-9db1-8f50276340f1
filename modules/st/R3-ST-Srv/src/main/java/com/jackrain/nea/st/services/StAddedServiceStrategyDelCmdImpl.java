package com.jackrain.nea.st.services;

import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.st.annotation.StOperationLog;
import com.jackrain.nea.st.api.StAddedServiceStrategyDelCmd;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @program: ryytn-st-v3.0
 * @description: 增值策略删除实现
 * @author: haiyang
 * @create: 2023-10-25 14:28
 **/

@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "st")
public class StAddedServiceStrategyDelCmdImpl extends CommandAdapter implements StAddedServiceStrategyDelCmd {
    @Autowired
    private StAddedServiceStrategyDelService strategyDelService;

    @StOperationLog(operationType = "DEL", mainTableName = "ST_ADDED_SERVICE_STRATEGY", itemsTableName = "ST_ADDED_SERVICE_STRATEGY_DETAIL")
    public ValueHolder execute(QuerySession session) throws NDSException {
        return strategyDelService.execute(session);
    }
}
