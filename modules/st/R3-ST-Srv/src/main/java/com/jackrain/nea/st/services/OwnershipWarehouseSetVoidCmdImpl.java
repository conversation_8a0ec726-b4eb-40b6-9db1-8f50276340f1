package com.jackrain.nea.st.services;

import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.st.api.OwnershipWarehouseSetVoidCmd;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @author: 汪聿森
 * @Date: Created in 2020-02-19 10:38
 * @Description : 库存归属仓库属性设置作废
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "st")
public class OwnershipWarehouseSetVoidCmdImpl extends CommandAdapter implements OwnershipWarehouseSetVoidCmd {

    @Autowired
    private OwnershipWarehouseSetVoidService ownershipWarehouseSetVoidService;

    @Override
    public ValueHolder execute(QuerySession session) throws NDSException {
        return ownershipWarehouseSetVoidService.execute(session);
    }
}
