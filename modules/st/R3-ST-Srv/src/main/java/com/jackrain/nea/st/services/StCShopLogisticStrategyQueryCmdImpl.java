package com.jackrain.nea.st.services;

import com.jackrain.nea.st.api.StCShopLogisticStrategyQueryCmd;
import com.jackrain.nea.st.model.request.StCShopLogisticStrategyQueryRequest;
import com.jackrain.nea.st.model.table.StCShopLogisticStrategyItem;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @program: r3-st
 * @description: 仓库物流设置
 * @author: caomalai
 * @create: 2022-07-01 13:57
 **/
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "st")
public class StCShopLogisticStrategyQueryCmdImpl extends CommandAdapter implements StCShopLogisticStrategyQueryCmd {
    @Autowired
    private StCShopLogisticStrategyQueryService service;

    @Override
    public ValueHolderV14<List<StCShopLogisticStrategyItem>> queryStCShopLogisticStrategy(StCShopLogisticStrategyQueryRequest request) {
        return service.queryStCShopLogisticStrategy(request);
    }
}
