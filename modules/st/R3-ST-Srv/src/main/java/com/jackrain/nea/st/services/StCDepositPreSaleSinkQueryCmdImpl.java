package com.jackrain.nea.st.services;

import com.jackrain.nea.st.api.StCDepositPreSaleSinkAuditCmd;
import com.jackrain.nea.st.api.StCDepositPreSaleSinkQueryCmd;
import com.jackrain.nea.st.model.request.StCDepositPreSaleSinkRequest;
import com.jackrain.nea.sys.CommandAdapter;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * description:提供查询的rpc
 * @Author:  liuwenjin
 * @Date 2021/9/24 11:23 上午
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "st")
public class StCDepositPreSaleSinkQueryCmdImpl extends CommandAdapter implements StCDepositPreSaleSinkQueryCmd {
    @Autowired
    private  StCDepositPreSaleSinkQueryService stCDepositPreSaleSinkQueryService;

    @Override
    public List<StCDepositPreSaleSinkRequest> queryDepositPreSaleSink(Long cpCPhyWarehouse) {
        return stCDepositPreSaleSinkQueryService.queryDepositPreSaleSink(cpCPhyWarehouse);
    }
}
