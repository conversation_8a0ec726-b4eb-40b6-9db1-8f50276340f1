package com.jackrain.nea.st.services;

import com.jackrain.nea.st.api.SendPlanRuleQueryServiceCmd;
import com.jackrain.nea.st.model.table.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * @description: 派单策略服务接口
 * @author: 汪聿森
 * @date: 2019-05-21
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "st")
public class SendPlanRuleQueryServiceCmdImpl implements SendPlanRuleQueryServiceCmd {
    @Autowired
    private SendPlanRuleQueryService sendPlanRuleQueryService;

    /**
     * 查找派单方案
     *
     * @param shopId      店鋪ID
     * @param currentDate 当前日期
     * @return
     */
    @Override
    public List<Long> selectSendPlanList(Long shopId, Date currentDate) {
        return sendPlanRuleQueryService.selectSendPlanList(shopId, currentDate);
    }

    /**
     * @param name
     * @return
     */
    @Override
    public AdParam selectSysParamValue(String name) {
        return sendPlanRuleQueryService.selectSysParamValue(name);
    }

    /**
     * @param planId 方案Id
     * @return
     */
    @Override
    public List<Long> selectSendPlanItemList(Long planId) {
        return sendPlanRuleQueryService.selectSendPlanItemList(planId);
    }

    /**
     * @param planId 方案Id
     * @return
     */
    @Override
    public List<StCSendPlanItemDO> selectSendPlanItemListByPlanId(Long planId) {
        return sendPlanRuleQueryService.selectSendPlanItemListByPlanId(planId);
    }

    /**
     * @param warehouseList 实体发货仓库id集合
     * @param sendRuleId    派单规则id
     * @return
     */
    @Override
    public List<StCSendRuleWarehouseRateDO> selectWarehouseRateMapper(List<Long> warehouseList, Long sendRuleId) {
        return sendPlanRuleQueryService.selectWarehouseRateMapper(warehouseList, sendRuleId);
    }

    /**
     * @param warehouseList 实体发货仓库id集合
     * @param sendRuleId    派单规则id
     * @return
     */
    @Override
    public BigDecimal selectQtySendTotal(List<Long> warehouseList, Long sendRuleId) {
        return sendPlanRuleQueryService.selectQtySendTotal(warehouseList, sendRuleId);
    }

    /**
     * @param warehouseId 发货仓库Id
     * @return
     */
    @Override
    public Integer updateRuleWarehouseRate(Long warehouseId) {
        return sendPlanRuleQueryService.updateRuleWarehouseRate(warehouseId);
    }

    /**
     * @param sendPlanList 方案Id集合
     * @return
     */
    @Override
    public List<Long> querySendPlanByActiveList(List<Long> sendPlanList) {
        return sendPlanRuleQueryService.querySendPlanByActiveList(sendPlanList);
    }

    /**
     * @param sendRuleId 规则Id
     * @return
     */
    @Override
    public StCSendRuleDO selectSendRuleType(Long sendRuleId) {
        return sendPlanRuleQueryService.selectSendRuleType(sendRuleId);
    }

    @Override
    public Long selectSendRuleIds(List<Long> sendRuleIds, String type) {
        return sendPlanRuleQueryService.selectSendRuleIds(sendRuleIds, type);
    }

    @Override
    public List<StCSendRuleDO> selectSendRuleByIdList(List<Long> sendRuleIds) {
        return sendPlanRuleQueryService.selectSendRuleByIdList(sendRuleIds);
    }

    /**
     * 查找地址就近的返回仓库 根据rank和创建时间排序
     *
     * @param cpPhyWarehouseList 实体仓list
     * @param cRegionProvinceId  省份Id
     * @param sendRuleId         规则ID
     * @return List<OcStCSendRuleAddressRent>
     */
    @Override
    public List<Long> selectAddressWarehouseList(List<Long> cpPhyWarehouseList, Long cRegionProvinceId, Long sendRuleId) {
        return sendPlanRuleQueryService.selectAddressWarehouseList(cpPhyWarehouseList, cRegionProvinceId, sendRuleId);
    }

    /**
     * 查找地址就近的返回仓库优先级
     *
     * @param cRegionProvinceId 省份Id
     * @param sendRuleId        规则ID
     * @return List<StCSendRuleAddressRankDO>
     */
    @Override
    public List<StCSendRuleAddressRankDO> selectSendRuleAddressRankList(Long cRegionProvinceId, Long sendRuleId) {
        return sendPlanRuleQueryService.selectSendRuleAddressRankList(cRegionProvinceId, sendRuleId);
    }

    /**
     * 查找按唯品会的返回仓库优先级
     *
     * @param sendRuleId         规则ID
     * @param cpCVipcomWahouseId 唯品会仓库ID
     * @return
     */
    @Override
    public List<StCSendRuleAddressVipDo> selectSendRuleAddressVoList(Long sendRuleId, Long cpCVipcomWahouseId) {
        return sendPlanRuleQueryService.selectSendRuleAddressVoList(sendRuleId, cpCVipcomWahouseId);
    }

    /**
     * 查找派单方案
     *
     * @param shopId 店鋪ID
     * @return
     */
    @Override
    public List<StCSendPlanDO> selectSendPlanListByShopId(Long shopId) {
        return sendPlanRuleQueryService.selectSendPlanListByShopId(shopId);
    }


    /**
     * 根据shopId 查询唯品会派单规则
     *
     * @param shopId 店铺id
     * @return list
     */
    @Override
    public List<StCSendRuleAddressVipDo> findRuleAddressVipByShopId(Long shopId, String jitCode) {
        return sendPlanRuleQueryService.findRuleAddressVipByShopId(shopId, jitCode);
    }
}
