package com.jackrain.nea.st.services;

import com.jackrain.nea.st.api.StCAllocationStorageCostQueryCmd;
import com.jackrain.nea.st.model.table.StCAllocationStorageCostStrategy;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2023/8/16 18:28
 * @Description
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "st")
public class StCAllocationStorageCostQueryCmdImpl implements StCAllocationStorageCostQueryCmd {

    @Resource
    private StCAllocationStorageCostQueryService service;

    @Override
    public ValueHolderV14<List<StCAllocationStorageCostStrategy>> queryByWarehouseIds(List<Long> warehouseIds) {
        return service.queryByWarehouseIds(warehouseIds);
    }
}
