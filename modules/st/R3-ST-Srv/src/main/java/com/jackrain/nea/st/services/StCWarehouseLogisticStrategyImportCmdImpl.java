package com.jackrain.nea.st.services;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.redis.config.CusRedisTemplate;
import com.jackrain.nea.redis.util.RedisOpsUtil;
import com.jackrain.nea.st.api.StCWarehouseLogisticStrategyImportCmd;
import com.jackrain.nea.st.model.vo.StCWarehouseLogisticStrategyImpVo;
import com.jackrain.nea.st.utils.ExportUtil;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.face.impl.UserImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * @program: r3-st
 * @description: 仓库物流设置导入
 * @author: caomalai
 * @create: 2022-07-01 13:57
 **/
@Slf4j
@Component
public class StCWarehouseLogisticStrategyImportCmdImpl extends CommandAdapter implements StCWarehouseLogisticStrategyImportCmd {
    @Autowired
    private ExportUtil exportUtil;
    @Autowired
    private StCWarehouseLogisticStrategyService stCWarehouseLogisticStrategyService;

    @Override
    public ValueHolderV14 queryTemplateDownloadUrl() {
        ValueHolderV14 holderV14 = new ValueHolderV14(ResultCode.SUCCESS, "仓库物流设置头明细导入模板下载成功！");
        /**
         *  拼接Excel主表sheet表头字段
         * */
        String[] mainNames = {"仓库", "是否启用失效时间", "物流公司", "物流结算公司", "优先级", "失效开始时间", "失效截止时间", "启用最大接单量", "最大接单量"};
        String[] mustNames = {"仓库", "物流公司", "物流结算公司", "优先级", "启用最大接单量"};
        String[] orderKeys = {"cpCPhyWarehouseEname", "cpCLogisticsEname", "logisticsSupplierEname"};

        User user = new UserImpl();
        ((UserImpl) user).setName("");
        List<String> mainList = Lists.newArrayList(mainNames);
        List<String> mustList = Lists.newArrayList(mustNames);
        List<String> orderKeyList = Lists.newArrayList(orderKeys);
        //生成Excel
        XSSFWorkbook hssfWorkbook = new XSSFWorkbook();
        hssfWorkbook = exportUtil.executeSheet(hssfWorkbook, "仓库物流设置头明细", "", mainList, mustList,
                orderKeyList, Lists.newArrayList(), false);
        String putMsg = exportUtil.saveFileAndPutOss(hssfWorkbook, "仓库物流设置头明细导入模板",
                user, "OSS-Bucket/EXPORT/ST_C_WAREHOUSE_LOGISTIC_STRATEGY/");
         holderV14.setData(putMsg);
        return holderV14;
    }

    @Override
    public ValueHolderV14 importInvoiceList(List<StCWarehouseLogisticStrategyImpVo> invoiceImpVos, User user) {
        ValueHolderV14<Object> holderV14 = new ValueHolderV14<>(ResultCode.SUCCESS, "导入成功！");
        String key =
                invoiceImpVos.stream().map(o -> Optional.ofNullable(o.getCpCLogisticsEname()).orElse(""))
                        .distinct().sorted().collect(Collectors.joining(","));
        String redisKey = "oc:oms:st_c_warehouse_logistic_strategy:import:"+key;
        CusRedisTemplate<String, String> objRedisTemplate = RedisOpsUtil.getStrRedisTemplate();
        if (objRedisTemplate.hasKey(redisKey)) {
            holderV14.setCode(ResultCode.FAIL);
            holderV14.setMessage("请勿重复导入！");
            return holderV14;
        }
        List<StCWarehouseLogisticStrategyImpVo> checkedIpBInvoiceImpVos = checkValid(invoiceImpVos);
        int successNum = stCWarehouseLogisticStrategyService.batchSaveInvoice(checkedIpBInvoiceImpVos, user);
        int failNum = invoiceImpVos.size()-successNum;
        if (failNum>0) {
            holderV14 = new ValueHolderV14<>();
            holderV14.setData(exportResut(invoiceImpVos, user));
            holderV14.setCode(ResultCode.FAIL); //
            holderV14.setMessage("仓库物流设置头明细导入成功条数:["+successNum+"]，失败条数:["+failNum+"]，详情见文件内容");
        }else{
            holderV14.setMessage("仓库物流设置头明细导入成功条数:["+successNum+"]");
        }
        log.info("仓库物流设置头明细导入结果 {}", JSON.toJSONString(holderV14));
        return holderV14;
    }

    /**
     * 参数有效性校验
     * @param invoiceImpVos
     * @return
     */
    private List<StCWarehouseLogisticStrategyImpVo> checkValid(List<StCWarehouseLogisticStrategyImpVo> invoiceImpVos) {
        List<StCWarehouseLogisticStrategyImpVo> ipBInvoiceImpVos = new ArrayList<>();
        List<String> repeatCheck = new ArrayList<>();
        // 数据缺失校验
        StringBuilder checkMessage = new StringBuilder();
        for(StCWarehouseLogisticStrategyImpVo vo:invoiceImpVos) {
            if (Objects.isNull(vo)) {
                checkMessage.append("[仓库物流设置入参不能为空]");
            }else {
                if (Objects.isNull(vo.getCpCPhyWarehouseEname())) {
                    checkMessage.append("[仓库不能为空]");

                }
                if (Objects.isNull(vo.getCpCLogisticsEname())) {
                    checkMessage.append("[物流公司不能为空]");
                }
                if (Objects.isNull(vo.getLogisticsSupplierEname())) {
                    checkMessage.append("[物流结算公司不能为空]");
                }
                if (Objects.isNull(vo.getItemPriority())) {
                    checkMessage.append("[优先级不能为空]");
                }
                if ("Y".equals(vo.getEnableMaximumOrderVolume()) && Objects.isNull(vo.getMaximumOrderVolume())) {
                    checkMessage.append("[最大接单数不能为空]");
                }
                if (!(StringUtils.isEmpty(vo.getExpireStartTime()) == StringUtils.isEmpty(vo.getExpireEndTime()))) {
                    checkMessage.append("[失效开始时间]和[失效截止时间]要么都为空，要么都不为空");
                }
                if (StringUtils.isNotEmpty(vo.getExpireStartTime())) {
                    String formatTime = validateTimeFormat(vo.getExpireStartTime());
                    if (StringUtils.isNotEmpty(formatTime)) {
                        vo.setExpireStartTime(formatTime);
                    } else {
                        checkMessage.append("[失效开始时间]格式错误");
                    }
                }
                if (StringUtils.isNotEmpty(vo.getExpireEndTime())) {
                    String formatTime = validateTimeFormat(vo.getExpireEndTime());
                    if (StringUtils.isNotEmpty(formatTime)) {
                        vo.setExpireEndTime(formatTime);
                    } else {
                        checkMessage.append("[失效截止时间]格式错误");
                    }
                }
            }
            if (StringUtils.isNotEmpty(checkMessage.toString())) {
                vo.setDesc(checkMessage.toString());
                checkMessage.setLength(0);
            } else {
                //通过仓库和物流公司判断重复
                String repeatKey = vo.getCpCPhyWarehouseEname() + vo.getCpCLogisticsEname();
                if (!repeatCheck.contains(repeatKey)) {
                    ipBInvoiceImpVos.add(vo);
                    repeatCheck.add(repeatKey);
                }
            }
        }
        return ipBInvoiceImpVos;
    }

    private String validateTimeFormat(String time) {
        String pattern = "^(0[0-9]|1[0-9]|2[0-3]):([0-5][0-9]):([0-5][0-9])$";
        if (Pattern.matches(pattern, time)) {
            String[] parts = time.split(":");
            int hours = Integer.parseInt(parts[0]);
            int minutes = Integer.parseInt(parts[1]);
            int seconds = Integer.parseInt(parts[2]);
            return String.format("%02d:%02d:%02d", hours, minutes, seconds);
        } else {
            return null;
        }
    }

    /**
     * @param invoiceImpVos
     * @param user
     * @return
     */
    private String exportResut(List<StCWarehouseLogisticStrategyImpVo> invoiceImpVos, User user) {
        List<StCWarehouseLogisticStrategyImpVo> errorList = invoiceImpVos.parallelStream().filter(x -> x.getDesc() != null).collect(Collectors.toList());
        // 列名
        String[] columnNames = {"主表行号", "错误原因"};
        List<String> c = Lists.newArrayList(columnNames);
        // map中的key
        String[] keys = {"rowNum", "desc"};
        List<String> k = Lists.newArrayList(keys);
        Workbook hssfWorkbook = exportUtil.execute("仓库物流设置", "仓库物流设置", c, k, errorList);
        return exportUtil.saveFileAndPutOss(hssfWorkbook, "仓库物流设置入错误信息", user, "OSS-Bucket/IMPORT/ST_C_WAREHOUSE_LOGISTIC_STRATEGY/");
    }
}
