package com.jackrain.nea.st.services;

import com.jackrain.nea.st.api.StCPreorderModelStrategyCmd;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @ClassName StCPreorderModelStrategyCmdImpl
 * @Description 发货单导入模板
 * <AUTHOR>
 * @Date 2022/12/22 17:48
 * @Version 1.0
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "st")
public class StCPreorderModelStrategyCmdImpl extends CommandAdapter implements StCPreorderModelStrategyCmd {

    @Autowired
    private StCPreorderModelStrategyService modelStrategyService;

    @Override
    public ValueHolderV14 getFieldsByModelCode(String modelCode) {
        return modelStrategyService.getFieldStrategyByModelCode(modelCode);
    }

    @Override
    public ValueHolderV14 getItemByModelCode(String modelCode) {
        return modelStrategyService.getItemStrategyByModelCode(modelCode);
    }

    @Override
    public ValueHolderV14 getAllModel() {
        return modelStrategyService.getAllModel();
    }

    @Override
    public ValueHolderV14 getByModelCode(String modelCode) {
        return modelStrategyService.getByModelCode(modelCode);
    }
}
