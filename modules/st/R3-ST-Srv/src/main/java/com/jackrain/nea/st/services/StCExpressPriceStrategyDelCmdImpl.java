package com.jackrain.nea.st.services;

import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.st.api.StCExpressPriceStrategyDelCmd;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @Date 2022/6/12 9:37
 * @Description 快递报价设置删除策略
 * @Version 1.0
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "st")
public class StCExpressPriceStrategyDelCmdImpl extends CommandAdapter implements StCExpressPriceStrategyDelCmd {
    @Autowired
    private StCExpressPriceStrategyDelService stCExpressPriceStrategyDelService;

    @Override
    public ValueHolder execute(QuerySession querySession) throws NDSException {
        return stCExpressPriceStrategyDelService.execute(querySession);
    }
}
