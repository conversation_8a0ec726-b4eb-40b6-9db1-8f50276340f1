package com.jackrain.nea.st.services;

import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.st.annotation.StOperationLog;
import com.jackrain.nea.st.api.StCPreorderModelStrategyUnSubmitCmd;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @ClassName StCPreorderModelStrategyUnSubmitCmdImpl
 * @Description 取消提交
 * <AUTHOR>
 * @Date 2022/12/27 17:27
 * @Version 1.0
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "st")
public class StCPreorderModelStrategyUnSubmitCmdImpl extends CommandAdapter implements StCPreorderModelStrategyUnSubmitCmd {
    @Autowired
    private StCPreorderModelStrategyService strategyService;

    @Override
    @StOperationLog(operationType = "RESERVE_AUDIT", mainTableName = "ST_C_PREORDER_MODEL_STRATEGY", itemsTableName = "ST_C_PREORDER_FIELD_STRATEGY,ST_C_PREORDER_ITEM_STRATEGY")
    public ValueHolder execute(QuerySession querySession) throws NDSException {
        return strategyService.unSubmitModelStrategy(querySession);
    }
}
