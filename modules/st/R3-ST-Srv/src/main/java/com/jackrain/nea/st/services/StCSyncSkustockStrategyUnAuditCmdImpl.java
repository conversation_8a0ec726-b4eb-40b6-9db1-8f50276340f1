package com.jackrain.nea.st.services;

import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.st.api.StCSyncSkustockStrategyUnAuditCmd;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @author: 汪聿森
 * @Date: Created in 2020-05-20 9:05
 * @Description : 反审核
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "st")
public class StCSyncSkustockStrategyUnAuditCmdImpl extends CommandAdapter implements StCSyncSkustockStrategyUnAuditCmd {
    @Autowired
    private StCSyncSkustockStrategyUnAuditService stCSyncSkustockStrategyUnAuditService;

    @Override
    public ValueHolder execute(QuerySession session) throws NDSException {
        return stCSyncSkustockStrategyUnAuditService.execute(session);
    }
}
