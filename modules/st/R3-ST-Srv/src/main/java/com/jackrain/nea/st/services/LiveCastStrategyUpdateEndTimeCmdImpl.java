package com.jackrain.nea.st.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.st.annotation.StOperationLog;
import com.jackrain.nea.st.api.LiveCastStrategyUpdateEndTimeCmd;
import com.jackrain.nea.st.common.StConstant;
import com.jackrain.nea.st.mapper.LiveCastStrategyMapper;
import com.jackrain.nea.st.model.table.StCLiveCastStrategyDO;
import com.jackrain.nea.st.utils.ValueHolderUtils;
import com.jackrain.nea.st.validate.StCLiveConsts;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.HashMap;
import java.util.Objects;

/**
 * Description： 更新结束时间
 * Author: RESET
 * Date: Created in 2020/7/2 19:53
 * Modified By:
 */
@Component
@Slf4j
//@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "st")
public class LiveCastStrategyUpdateEndTimeCmdImpl implements LiveCastStrategyUpdateEndTimeCmd {

    @Autowired
    LiveCastStrategyService liveCastStrategyService;

    @Autowired
    LiveCastStrategyMapper liveCastStrategyMapper;

    @StOperationLog(configurationFlag = false, mainTableName = "ST_C_LIVE_CAST_STRATEGY", itemsTableName = "ST_C_LIVE_CAST_STRATEGY_ITEM", customizeLogAopKey = "stCLiveCastStrategyUpdateTime")
    @Override
    public ValueHolder execute(QuerySession session) throws NDSException {
        /*
        参数格式
        {
          "objid": -1,
          "fixcolumn": {
            "ST_C_LIVE_CAST_STRATEGY": {
              "BILL_TIME_TYPE": "1",
              "END_TIME": "2020-06-18 00:00:00"
            }
          },
          "table": "ST_C_LIVE_CAST_STRATEGY"
        }
         */
        // 解析参数对象
        DefaultWebEvent event = session.getEvent();
        JSONObject param = JSON.parseObject(
                JSON.toJSONStringWithDateFormat(event.getParameterValue("param"), "yyyy-MM-dd HH:mm:ss"),
                Feature.OrderedField);
        Long id = param.getLong("objid");
        JSONObject fixColumn = param.getJSONObject("fixcolumn");

        if (Objects.isNull(id) || Objects.isNull(fixColumn)) {
            return ValueHolderUtils.getFailValueHolder("更新失败，参数为空！");
        }
        StCLiveCastStrategyDO stCLiveCastStrategyDO = liveCastStrategyMapper.selectById(id);
        if (stCLiveCastStrategyDO != null) {
            session.setAttribute("beforeObj", JSON.toJSONString(stCLiveCastStrategyDO));
        }
        // 取参数
        JSONObject strategy = fixColumn.getJSONObject(StConstant.TAB_ST_C_LIVE_CAST_STRATEGY);

        if (Objects.isNull(strategy) || Objects.isNull(strategy.getDate(StCLiveConsts.END_TIME))) {
            return ValueHolderUtils.getFailValueHolder("更新失败，结束时间为空！");
        }
        Date endTime = strategy.getDate(StCLiveConsts.END_TIME);
        try {
            liveCastStrategyService.updateEndTime(session, id, endTime);
        } catch (NDSException e) {
            return ValueHolderUtils.getFailValueHolder(e.getMessage());
        }
        return ValueHolderUtils.getSuccessValueHolder("更新成功！");
    }

    @Override
    public ValueHolder execute(HashMap map) throws NDSException {
        return null;
    }

}
