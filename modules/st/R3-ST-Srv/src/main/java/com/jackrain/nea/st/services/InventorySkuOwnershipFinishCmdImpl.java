package com.jackrain.nea.st.services;

import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.st.api.InventorySkuOwnershipFinishCmd;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @Descroption 库存条码归属
 * <AUTHOR>
 * @Date 2019/9/23 15:00
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "st")
public class InventorySkuOwnershipFinishCmdImpl extends CommandAdapter implements InventorySkuOwnershipFinishCmd {
    @Autowired
    private InventorySkuOwnershipFinishService finishService;

    @Override
    public ValueHolder execute(QuerySession session) throws NDSException {
        return finishService.execute(session);
    }
}
