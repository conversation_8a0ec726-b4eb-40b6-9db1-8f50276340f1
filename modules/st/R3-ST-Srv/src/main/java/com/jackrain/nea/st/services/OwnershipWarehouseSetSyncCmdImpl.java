package com.jackrain.nea.st.services;

import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.st.api.OwnershipWarehouseSetSyncCmd;
import com.jackrain.nea.st.model.table.StCOwnershipWarehouseSetDO;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @author: 汪聿森
 * @Date: Created in 2020-02-19 11:48
 * @Description : 库存归属仓库属性设置同步
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "st")
public class OwnershipWarehouseSetSyncCmdImpl   extends CommandAdapter implements OwnershipWarehouseSetSyncCmd {
    @Autowired
    private OwnershipWarehouseSetSyncService ownershipWarehouseSetSyncService;

    @Override
    public ValueHolder execute(QuerySession session) throws NDSException {
        return ownershipWarehouseSetSyncService.execute(session);
    }

    /**
     * 根据仓库属性查询供货仓信息
     * @param channelType
     * @return
     */
    @Override
    public List<StCOwnershipWarehouseSetDO> selectByChannelType(Long channelType) {
        return ownershipWarehouseSetSyncService.selectByChannelType(channelType);
    }
}
