package com.jackrain.nea.st.services;

import org.apache.dubbo.config.annotation.Service;
import com.jackrain.nea.st.api.ShopStrategyDefaultStoreQueryCmd;
import com.jackrain.nea.st.model.table.StCShopStrategyDO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "st")
public class ShopStrategyDefaultStoreQueryCmdImpl implements ShopStrategyDefaultStoreQueryCmd{

    @Autowired
    private ShopStrategyDefaultStoreQueryService shopStrategyDefaultStoreQueryService;
    @Override
    public List<StCShopStrategyDO> selectDefaultStore(Long shopId) {
        return shopStrategyDefaultStoreQueryService.selectDefaultStore(shopId);
    }
}
