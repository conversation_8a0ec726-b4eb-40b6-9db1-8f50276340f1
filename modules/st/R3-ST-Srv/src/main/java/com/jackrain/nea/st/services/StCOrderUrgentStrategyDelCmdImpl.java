package com.jackrain.nea.st.services;

import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.st.api.StCOrderUrgentStrategyDelCmd;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @Package com.jackrain.nea.st.services
 * @Description: 订单加急打标-del
 * @date 2020/8/29 23:51
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "st")
public class StCOrderUrgentStrategyDelCmdImpl extends CommandAdapter implements StCOrderUrgentStrategyDelCmd {

    @Autowired
    private StCOrderUrgentStrategyDelService service;

    @Override
    public ValueHolder execute(QuerySession querySession) throws NDSException {
        return service.execute(querySession);
    }
}
