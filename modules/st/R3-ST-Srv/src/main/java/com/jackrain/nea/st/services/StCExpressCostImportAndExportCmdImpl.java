package com.jackrain.nea.st.services;

import com.jackrain.nea.ac.model.request.AcLogisticsFeeReCaculationRequest;
import com.jackrain.nea.ac.model.result.AcLogisticsFeeCaculationResult;
import com.jackrain.nea.st.api.StCExpressCostImportAndExportCmd;
import com.jackrain.nea.st.model.request.StCExpressCostPoi;
import com.jackrain.nea.st.model.result.StImportErrorMsgResult;
import com.jackrain.nea.st.request.ReCaculateLogisticsFeeRequest;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/8/8 16:50
 * @Description
 */
@Slf4j
@Component
public class StCExpressCostImportAndExportCmdImpl implements StCExpressCostImportAndExportCmd {

    @Autowired
    private StCExpressCostImportAndExportService stCExpressCostImportAndExportService;

    @Override
    public ValueHolderV14<List<StImportErrorMsgResult>> importExpressCost(List<StCExpressCostPoi> importList, User user) {
        return stCExpressCostImportAndExportService.importExpressPriceCost(importList,user);
    }

    @Override
    public String downloadImportErrMsg(User user, List<StImportErrorMsgResult> errMsgList) {
        return stCExpressCostImportAndExportService.downloadImportErrMsg(user,errMsgList);
    }

    @Override
    public ValueHolderV14<String> exportExpressCost(String ids, User user) {
        return stCExpressCostImportAndExportService.exportExpressPriceCost(ids,user);
    }

    @Override
    public ValueHolderV14<AcLogisticsFeeReCaculationRequest> queryFeeChecklistCount(ReCaculateLogisticsFeeRequest request, User user) {
        return stCExpressCostImportAndExportService.queryFeeChecklistCount(request,user);
    }

    @Override
    public ValueHolderV14<AcLogisticsFeeCaculationResult> reCaculateLogisticsFee(AcLogisticsFeeReCaculationRequest request) {
        return stCExpressCostImportAndExportService.reCaculateLogisticsFee(request);
    }
}
