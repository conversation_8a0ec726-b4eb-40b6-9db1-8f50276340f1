package com.jackrain.nea.st.services;

import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.st.api.LiveCastStrategyQueryListCmd;
import com.jackrain.nea.st.model.table.StCLiveCastStrategyDO;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;

/**
 * @program: r3-st
 * @description:
 * @author: liuwj
 * @create: 2021-04-06 17:01
 **/
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "st")
public class LiveCastStrategyQueryListCmdImpl implements LiveCastStrategyQueryListCmd {

    @Autowired
    LiveCastStrategyService liveCastStrategyService;

    @Override
    public ValueHolder execute(QuerySession session) throws NDSException {
        return liveCastStrategyService.queryList(session);
    }

    @Override
    public ValueHolder execute(HashMap map) throws NDSException {
        return null;
    }

    @Override
    public ValueHolderV14 queryListById(Long objId) {
        return liveCastStrategyService.queryListById(objId);
    }

    @Override
    public List<StCLiveCastStrategyDO> queryLiveCastStrategyByAnchorArchivesId(Long anchorArchivesId) {
        return liveCastStrategyService.queryLiveCastStrategyByAnchorArchivesId(anchorArchivesId);
    }
}
