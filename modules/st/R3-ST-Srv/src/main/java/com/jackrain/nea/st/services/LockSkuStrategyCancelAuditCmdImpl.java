//package com.jackrain.nea.st.services;
//
//import org.apache.dubbo.config.annotation.Service;
//import com.jackrain.nea.exception.NDSException;
//import com.jackrain.nea.st.api.LockSkuStrategyCancelAuditCmd;
//import com.jackrain.nea.sys.CommandAdapter;
//import com.jackrain.nea.util.ValueHolder;
//import com.jackrain.nea.web.query.QuerySession;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.stereotype.Component;
//
///**
// * 调用BLL层
// *
// * <AUTHOR> 陈俊明
// * @since : 2019-03-21
// * create at : 2019-03-21 17:20
// */
//@Slf4j
//@Component
//@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "st")
//public class LockSkuStrategyCancelAuditCmdImpl extends CommandAdapter implements LockSkuStrategyCancelAuditCmd {
//    @Autowired
//    private LockSkuStrategyCancelAuditService lockSkuStrategyCancelAuditService;
//
//    @Override
//    public
//    ValueHolder execute(QuerySession querySession) throws NDSException {
//        return lockSkuStrategyCancelAuditService.execute(querySession);
//    }
//}
