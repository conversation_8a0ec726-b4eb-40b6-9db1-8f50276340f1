package com.jackrain.nea.st.services;

import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.st.api.StCPreOccupyProvincePrioritySaveCmd;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;

/**
 * @ClassName StCPreOccupyProvincePrioritySaveCmdImpl
 * @Description 订单预寻源
 * <AUTHOR>
 * @Date 2025/2/26 10:57
 * @Version 1.0
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "st")
public class StCPreOccupyProvincePrioritySaveCmdImpl extends CommandAdapter implements StCPreOccupyProvincePrioritySaveCmd {

    @Autowired
    private StCPreOccupyProvincePriorityService stCPreOccupyProvincePriorityService;

    @Override
    public ValueHolder execute(QuerySession session) throws NDSException {
        return stCPreOccupyProvincePriorityService.saveOrUpdate(session);
    }

    @Override
    public ValueHolder execute(HashMap map) throws NDSException {
        return null;
    }
}
