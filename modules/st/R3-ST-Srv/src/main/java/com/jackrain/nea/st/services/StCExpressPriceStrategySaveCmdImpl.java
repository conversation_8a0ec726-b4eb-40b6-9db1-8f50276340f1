package com.jackrain.nea.st.services;

import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.st.api.StCExpressPriceStrategySaveCmd;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @Date 2022/6/9 16:55
 * @Description 快递报价设置保存
 * @Version 1.0
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "st")
public class StCExpressPriceStrategySaveCmdImpl extends CommandAdapter implements StCExpressPriceStrategySaveCmd {

    @Resource
    private StCExpressPriceStrategySaveService saveService;

    @Override
    public ValueHolder execute(QuerySession querySession) throws NDSException {
        return saveService.execute(querySession);
    }
}
