package com.jackrain.nea.st.services;

import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.st.annotation.StOperationLog;
import com.jackrain.nea.st.api.StAddedServiceTypeDocSaveCmd;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @program: ryytn-st-v3.0
 * @description: 增值服务策略类型档案保存操作
 * @author: haiyang
 * @create: 2023-10-20 14:38
 **/
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "st")
public class StAddedServiceTypeDocSaveCmdImpl extends CommandAdapter implements StAddedServiceTypeDocSaveCmd {

    @Autowired
    private StAddedServiceTypeDocSaveService serviceTypeDocSaveService;

    public ValueHolder execute(QuerySession querySession) throws NDSException {
        return serviceTypeDocSaveService.execute(querySession);
    }

}
