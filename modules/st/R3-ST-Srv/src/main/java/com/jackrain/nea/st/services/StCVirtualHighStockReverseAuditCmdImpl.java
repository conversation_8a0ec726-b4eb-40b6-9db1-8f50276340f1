package com.jackrain.nea.st.services;

import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.st.api.StCVirtualHighStockCloseCmd;
import com.jackrain.nea.st.api.StCVirtualHighStockReverseAuditCmd;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @Date: 2020/6/23 3:20 下午
 * @Desc: 店铺虚高库存设置反审核
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "st")
public class StCVirtualHighStockReverseAuditCmdImpl  extends CommandAdapter implements StCVirtualHighStockReverseAuditCmd {
    @Autowired
    private StCVirtualHighStockReverseAuditService reverseAuditService;

    @Override
    public ValueHolder execute(QuerySession querySession) throws NDSException {
        return reverseAuditService.execute(querySession);
    }
}
