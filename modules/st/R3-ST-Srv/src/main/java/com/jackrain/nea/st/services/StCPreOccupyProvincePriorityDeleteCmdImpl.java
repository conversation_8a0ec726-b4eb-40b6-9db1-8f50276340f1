package com.jackrain.nea.st.services;

import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.st.api.StCPreOccupyProvincePriorityDeleteCmd;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @ClassName StCPreOccupyProvincePriorityDeleteCmdImpl
 * @Description 订单预寻源-省优先策略-删除
 * <AUTHOR>
 * @Date 2025/2/26 16:08
 * @Version 1.0
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "st")
public class StCPreOccupyProvincePriorityDeleteCmdImpl extends CommandAdapter implements StCPreOccupyProvincePriorityDeleteCmd {

    @Autowired
    private StCPreOccupyProvincePriorityService stCPreOccupyProvincePriorityService;

    @Override
    public ValueHolder execute(QuerySession session) throws NDSException {
        return stCPreOccupyProvincePriorityService.delete(session);
    }
}
