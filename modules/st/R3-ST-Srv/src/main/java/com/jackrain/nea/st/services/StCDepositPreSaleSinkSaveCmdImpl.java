package com.jackrain.nea.st.services;

import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.st.api.StCDepositPreSaleSinkSaveCmd;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * description:定金预售预下沉策略保存实现类
 * @Author:  liuwenjin
 * @Date 2021/9/23 4:12 下午
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "st")
public class StCDepositPreSaleSinkSaveCmdImpl  extends CommandAdapter implements StCDepositPreSaleSinkSaveCmd {

    @Autowired
    private  StCDepositPreSaleSinkSaveService stCDepositPreSaleSinkSaveService;

    @Override
    public ValueHolder execute(QuerySession querySession) throws NDSException {
        return stCDepositPreSaleSinkSaveService.execute(querySession);
    }
}
