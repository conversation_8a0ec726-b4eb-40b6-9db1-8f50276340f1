package com.jackrain.nea.st.services;

import com.jackrain.nea.st.api.StCMatchAbnormalStrategyMatchCmd;
import com.jackrain.nea.st.model.table.StCMatchAbnormalStrategy;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.utils.ValueHolderV14Utils;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @program: r3-st
 * @description:
 * @author: caomalai
 * @create: 2022-10-16 17:36
 **/
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "st")
public class StCMatchAbnormalStrategyMatchCmdImpl implements StCMatchAbnormalStrategyMatchCmd {
    @Autowired
    private StCMatchAbnormalStrategyService stCMatchAbnormalStrategyService;

    @Override
    public ValueHolderV14<List<StCMatchAbnormalStrategy>> match(Integer abnormalType, String abnormalContent) {
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format(" StCMatchAbnormalStrategyMatchCmdImpl param：{}","发货单异常类型定义"),
                    String.format("异常类型：%s,异常内容：%s",abnormalType,abnormalContent));
        }
        try {
            List<StCMatchAbnormalStrategy> match = stCMatchAbnormalStrategyService.match(abnormalType, abnormalContent);
            ValueHolderV14 success = ValueHolderV14Utils.getSuccessValueHolder("success");
            success.setData(match);
            return success;
        }catch (Exception e) {
            if (log.isErrorEnabled()) {
                log.error(LogUtil.format("新增异常"), e);
            }
            return ValueHolderV14Utils.getFailValueHolder(e.getMessage());
        }
    }
}
