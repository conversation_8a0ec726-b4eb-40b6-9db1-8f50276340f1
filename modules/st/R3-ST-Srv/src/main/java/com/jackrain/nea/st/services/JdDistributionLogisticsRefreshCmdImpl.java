package com.jackrain.nea.st.services;

import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.st.api.JdDistributionLogisticsRefreshCmd;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @ClassName JdDistributionLogisticsRefreshCmdImpl
 * @Description 京东分销物流公司刷新
 * <AUTHOR>
 * @Date 2022/12/10 14:49
 * @Version 1.0
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "st")
public class JdDistributionLogisticsRefreshCmdImpl extends CommandAdapter implements JdDistributionLogisticsRefreshCmd {

    @Autowired
    private JdDistributionLogisticsService logisticsService;

    @Override
    public ValueHolder execute(QuerySession session) throws NDSException {
        return logisticsService.refreshLogistics(session);
    }
}
