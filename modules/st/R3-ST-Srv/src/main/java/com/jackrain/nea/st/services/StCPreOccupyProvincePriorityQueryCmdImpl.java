package com.jackrain.nea.st.services;

import com.jackrain.nea.st.api.StCPreOccupyProvincePriorityQueryCmd;
import com.jackrain.nea.st.model.table.StCPreOccupyProvincePriority;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @ClassName StCPreOccupyProvincePriorityQueryCmdImpl
 * @Description 省优先
 * <AUTHOR>
 * @Date 2025/3/4 10:02
 * @Version 1.0
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "st")
public class StCPreOccupyProvincePriorityQueryCmdImpl extends CommandAdapter implements StCPreOccupyProvincePriorityQueryCmd {

    @Autowired
    private StCPreOccupyProvincePriorityService stCPreOccupyProvincePriorityService;

    @Override
    public ValueHolderV14<StCPreOccupyProvincePriority> queryByProvinceEcode(String provinceEcode) {
        return stCPreOccupyProvincePriorityService.queryByProvinceEcode(provinceEcode);
    }
}
