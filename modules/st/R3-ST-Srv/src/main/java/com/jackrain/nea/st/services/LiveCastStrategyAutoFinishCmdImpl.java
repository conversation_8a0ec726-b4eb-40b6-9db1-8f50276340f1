package com.jackrain.nea.st.services;

import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.st.api.LiveCastStrategyAutoFinishCmd;
import com.jackrain.nea.st.utils.ValueHolderUtils;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;

/**
 * Description： 自动结束已审核单据
 * Author: RESET
 * Date: Created in 2020/7/3 11:08
 * Modified By:
 */
@Component
@Slf4j
//@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "st")
public class LiveCastStrategyAutoFinishCmdImpl implements LiveCastStrategyAutoFinishCmd {

    @Autowired
    LiveCastStrategyService liveCastStrategyService;

    @Override
    public ValueHolder execute(QuerySession session) throws NDSException {
        liveCastStrategyService.updateStrategyStatusByAuto(session);
        return ValueHolderUtils.getSuccessValueHolder("自动结案执行成功");
    }

    @Override
    public ValueHolder execute(HashMap map) throws NDSException {
        return null;
    }

}
