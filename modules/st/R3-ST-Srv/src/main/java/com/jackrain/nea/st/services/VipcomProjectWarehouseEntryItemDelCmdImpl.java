package com.jackrain.nea.st.services;

import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.st.api.VipcomProjectWarehouseEntryItemDelCmd;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @Descroption 日程规划入库单明细-删除接口
 * <AUTHOR>
 * @Date 2021/05/25 16:30
 */
@Slf4j
@Component
//@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "st")
public class VipcomProjectWarehouseEntryItemDelCmdImpl extends CommandAdapter implements VipcomProjectWarehouseEntryItemDelCmd {
    @Autowired
    private VipcomProjectWarehouseEntryItemDelService service;
    @Override
    public ValueHolder execute(QuerySession session) throws NDSException {
        return service.execute(session);
    }
}
