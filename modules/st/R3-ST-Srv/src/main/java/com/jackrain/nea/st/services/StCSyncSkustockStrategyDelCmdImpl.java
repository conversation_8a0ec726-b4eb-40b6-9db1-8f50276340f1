package com.jackrain.nea.st.services;

import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.st.api.StCSyncSkustockStrategyDelCmd;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @author: 汪聿森
 * @Date: Created in 2020-05-21 17:02
 * @Description : 店铺条码库存同步策略删除
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "st")
public class StCSyncSkustockStrategyDelCmdImpl extends CommandAdapter implements StCSyncSkustockStrategyDelCmd {

    @Autowired
    private StCSyncSkustockStrategyDelService stCSyncSkustockStrategyDelService;

    @Override
    public ValueHolder execute(QuerySession querySession) throws NDSException {
        return stCSyncSkustockStrategyDelService.execute(querySession);
    }
}
