package com.jackrain.nea.st.services;

import com.jackrain.nea.config.Resources;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.st.api.StAddedServiceTypeDocQueryCmd;
import com.jackrain.nea.st.mapper.StAddedServiceTypeDocMapper;
import com.jackrain.nea.st.model.table.StAddedServiceTypeDocDO;
import com.jackrain.nea.st.utils.ValueHolderUtils;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Locale;

/**
 * @ClassName StAddedServiceTypeDocQueryCmdImpl
 * @Description 增值服务查询
 * <AUTHOR>
 * @Date 2024/6/6 15:40
 * @Version 1.0
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "st")
public class StAddedServiceTypeDocQueryCmdImpl extends CommandAdapter implements StAddedServiceTypeDocQueryCmd {

    @Autowired
    private StAddedServiceTypeDocMapper stAddedServiceTypeDocMapper;

    public ValueHolder execute(QuerySession querySession) throws NDSException {
        ValueHolder valueHolder = new ValueHolder();
        List<StAddedServiceTypeDocDO> addedServiceTypeDocDOList = stAddedServiceTypeDocMapper.query();
        if (CollectionUtils.isEmpty(addedServiceTypeDocDOList)) {
            return ValueHolderUtils.getSuccessValueHolder("未查询到增值服务类型");
        }
        valueHolder.put("code", ResultCode.SUCCESS);
        valueHolder.put("message", Resources.getMessage("success", new Locale("zh", "CN")));
        valueHolder.put("data", addedServiceTypeDocDOList);
        return valueHolder;
    }
}
