package com.jackrain.nea.st.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.st.annotation.StOperationLog;
import com.jackrain.nea.st.api.StCShopStrategyCancelAtuoInterceptCmd;
import com.jackrain.nea.st.mapper.StCShopStrategyMapper;
import com.jackrain.nea.st.utils.ValueHolderUtils;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * @ClassName StCShopStrategyCancelAtuoInterceptCmdImpl
 * @Description 批量取消店铺策略自动拦截
 * <AUTHOR>
 * @Date 2023/11/13 18:43
 * @Version 1.0
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "st")
public class StCShopStrategyCancelAtuoInterceptCmdImpl extends CommandAdapter implements StCShopStrategyCancelAtuoInterceptCmd {

    @Autowired
    private StCShopStrategyMapper stCShopStrategyMapper;

    @Override
    @StOperationLog(operationType = "MOD", mainTableName = "ST_C_SHOP_STRATEGY", itemsTableName = "ST_C_SHOP_STRATEGY_LOGISTICS_ITEM")
    public ValueHolder execute(QuerySession querySession) throws NDSException {
        ValueHolder result = ValueHolderUtils.getSuccessValueHolder("success");
        DefaultWebEvent event = querySession.getEvent();
        User user = querySession.getUser();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(
                event.getParameterValue("param"), "yyyy-MM-dd HH:mm:ss"), Feature.OrderedField);
        log.info(LogUtil.format("店铺策略修改自动拦截Json：") + param.toJSONString());
        JSONArray jsonArray = param.getJSONArray("ids");
        if (CollectionUtils.isEmpty(jsonArray)) {
            result.put("code", ResultCode.FAIL);
            result.put("message", "请选择需要修改的数据");
            return result;
        }
        List<Long> ids = new ArrayList<>();
        for (Object o : jsonArray) {
            ids.add(Long.valueOf(o.toString()));
        }
        stCShopStrategyMapper.batchCancelAutoInterceptById(Long.valueOf(user.getId()), user.getEname(), ids);
        return result;
    }
}
