package com.jackrain.nea.st.services;

import com.jackrain.nea.st.api.VipcomDistributionFilterCmd;
import com.jackrain.nea.st.model.request.VipcomDistributionFilterRequest;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date ：2020/12/14 3:47 下午
 * description ：
 * @ Modified By：
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "st")
public class VipcomDistributionFilterCmdImpl implements VipcomDistributionFilterCmd {
    @Autowired
    private VipcomDistributionFilterService distributionFilterService;

    @Override
    public ValueHolderV14<List<Long>> filterDistribution(List<VipcomDistributionFilterRequest> requests, User operateUser) {
        return distributionFilterService.filterDistribution(requests, operateUser);
    }
}
