package com.jackrain.nea.st.services;

import com.alibaba.fastjson.JSONObject;
import com.burgeon.r3.sg.core.common.SgConstants;
import com.google.common.base.Throwables;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.st.api.StCWarehouseLogisticStrategyQueryCmd;
import com.jackrain.nea.st.model.request.StCWarehouseLogisticStrategyItemQueryRequest;
import com.jackrain.nea.st.model.request.StCWarehouseLogisticStrategyQueryRequest;
import com.jackrain.nea.st.model.result.StCWarehouseLogisticStrategyQueryResult;
import com.jackrain.nea.st.model.result.StCWarehouseLogisticStrategyResult;
import com.jackrain.nea.st.model.table.StCWarehouseLogisticStrategy;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.utility.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * @program: r3-st
 * @description: 仓库物流设置查询
 * @author: chenb
 * @create: 2022-07-21 16:48
 **/
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "st")
public class StCWarehouseLogisticStrategyQueryCmdImpl implements StCWarehouseLogisticStrategyQueryCmd {

    @Autowired
    private StCWarehouseLogisticStrategyService stCWarehouseLogisticStrategyService;

    @Override
    public ValueHolderV14<List<StCWarehouseLogisticStrategy>> queryLogisticStrategyByDetail(
            StCWarehouseLogisticStrategyItemQueryRequest request) throws NDSException {

        ValueHolderV14<List<StCWarehouseLogisticStrategy>> holder = new ValueHolderV14<>(ResultCode.SUCCESS, SgConstants.MESSAGE_STATUS_SUCCESS);
        List<StCWarehouseLogisticStrategy> result = new ArrayList<>();
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("StCWarehouseLogisticStrategyDeleteCmdImpl param：{}", "仓库物流设置查询-根据明细信息获取仓库物流设置信息"),
                    JSONObject.toJSONString(request));
        }

        try {
            result = stCWarehouseLogisticStrategyService.queryLogisticStrategyByDetail(request);
        } catch (Exception e) {
            log.error(LogUtil.format("仓库物流设置查询-根据明细信息获取仓库物流设置信息 发生异常"), Throwables.getStackTraceAsString(e));
            holder.setCode(ResultCode.FAIL);
            holder.setMessage("仓库物流设置查询-根据明细信息获取仓库物流设置信息 发生异常");
        }

        holder.setData(result);
        return holder;
    }

    @Override
    public ValueHolderV14<List<StCWarehouseLogisticStrategyResult>> queryLogisticStrategyByWarehouseAndLogistics(StCWarehouseLogisticStrategyItemQueryRequest request) throws NDSException {
        return stCWarehouseLogisticStrategyService.queryLogisticStrategyByWarehouseAndLogistics(request);
    }

    @Override
    public ValueHolderV14<StCWarehouseLogisticStrategyQueryResult> queryLogisticStrategyByWarehouseId(StCWarehouseLogisticStrategyQueryRequest request) throws NDSException {

        return stCWarehouseLogisticStrategyService.queryLogisticStrategyByWarehouseId(request);
    }

}
