package com.jackrain.nea.st.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.jackrain.nea.cpext.utils.ValueHolderUtils;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.st.api.StCMatchAbnormalStrategyDeleteCmd;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @program: r3-st
 * @description:
 * @author: caomalai
 * @create: 2022-10-16 17:29
 **/
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "st")
public class StCMatchAbnormalStrategyDeleteCmdImpl extends CommandAdapter implements StCMatchAbnormalStrategyDeleteCmd {
    @Autowired
    private StCMatchAbnormalStrategyService stCMatchAbnormalStrategyService;
    @Override
    public ValueHolder execute(QuerySession querySession) throws NDSException {
        DefaultWebEvent event = querySession.getEvent();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(event.getParameterValue("param"), "yyyy-MM-dd HH:mm:ss", SerializerFeature.WriteMapNullValue), Feature.OrderedField);
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("StCMatchAbnormalStrategyDeleteCmdImpl param：{}","发货单异常类型定义") , param);
        }
        try{
            stCMatchAbnormalStrategyService.delete(param);
        }catch (Exception e){
            if(log.isErrorEnabled()){
                log.error(LogUtil.format("删除异常"),e);
            }
            return ValueHolderUtils.fail("删除失败！");
        }
        return ValueHolderUtils.success("删除成功！");
    }
}
