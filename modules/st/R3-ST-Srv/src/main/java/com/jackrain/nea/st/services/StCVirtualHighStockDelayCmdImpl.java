package com.jackrain.nea.st.services;

import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.st.api.StCVirtualHighStockDelayCmd;
import com.jackrain.nea.st.model.request.StCVirtualHighStockRequest;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR> <PERSON>
 * @Date: 2020/6/23 3:20 下午
 * @Desc: 店铺虚高库存设置延期
 */
@Slf4j
@Component
//@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "st")
public class StCVirtualHighStockDelayCmdImpl extends CommandAdapter implements StCVirtualHighStockDelayCmd{
    @Autowired
    private StCVirtualHighStockDelayService delayService;

    @Override
    public ValueHolder execute(QuerySession querySession) throws NDSException {
        return delayService.execute(querySession);
    }

    @Override
    public ValueHolder batchDelayEndTime(QuerySession querySession) throws NDSException {
        return delayService.batchDelayEndTime(querySession);
    }
}
