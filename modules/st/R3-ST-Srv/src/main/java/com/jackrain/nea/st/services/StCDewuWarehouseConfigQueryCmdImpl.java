package com.jackrain.nea.st.services;

import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.st.api.StCDewuWarehouseConfigQueryCmd;
import com.jackrain.nea.st.model.table.StCDewuWarehouseConfig;
import com.jackrain.nea.sys.CommandAdapter;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 得物仓库配置表查询接口实现
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "st")
public class StCDewuWarehouseConfigQueryCmdImpl extends CommandAdapter implements StCDewuWarehouseConfigQueryCmd {

    @Autowired
    private StCDewuWarehouseConfigQueryService stCDewuWarehouseConfigQueryService;

    @Override
    public List<StCDewuWarehouseConfig> queryByWarehouseCode(String warehouseCode) throws NDSException {
        return stCDewuWarehouseConfigQueryService.queryByWarehouseCode(warehouseCode);
    }
}
