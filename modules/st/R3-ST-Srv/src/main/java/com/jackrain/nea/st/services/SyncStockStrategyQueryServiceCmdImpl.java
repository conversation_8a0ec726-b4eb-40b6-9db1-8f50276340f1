package com.jackrain.nea.st.services;

import com.jackrain.nea.cp.entity.CpCOrgChannelItemEntity;
import com.jackrain.nea.st.api.SyncStockStrategyQueryServiceCmd;
import com.jackrain.nea.st.model.request.StStockPriorityRequest;
import com.jackrain.nea.st.model.result.CpCOrgChannelResult;
import com.jackrain.nea.st.model.result.SyncStockStrategyQueryResult;
import com.jackrain.nea.st.model.vo.StCSyncStockStrategyVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @description: 店铺同步策略查询接口服务
 * @author: 郑小龙
 * @date: 2019-04-03 17:19
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "st")
public class SyncStockStrategyQueryServiceCmdImpl implements SyncStockStrategyQueryServiceCmd {
    @Autowired
    private SyncStockStrategyQueryService service;

    @Override
    public List<StCSyncStockStrategyVo> querySyncStockStrategyItemByID(Long shopId) {
        return service.querySyncStockStrategyItemByID(shopId);
    }

    @Override
    public List<StStockPriorityRequest> selectStockPriorityByShopId(Long shopId) {
        return service.selectStockPriorityByShopId(shopId);
    }

    @Override
    public Boolean checkByStoreIds(List<Long> storeIds) {
        return service.checkByStoreIds(storeIds);
    }

    @Override
    public List<Long> queryStoreIdsByShopId(Long shopId) {
        return service.queryStoreIdsByShopId(shopId);
    }

    @Override
    public List<Long> queryShopStoreList(Long cpCShopId, List<Long> storeList) {
        return service.queryShopStoreList(cpCShopId, storeList);
    }

    @Override
    public List<StStockPriorityRequest> queryStStockPriority(Long cpCShopId, List<Long> storeList) {
        return service.queryStStockPriority(cpCShopId, storeList);
    }

    @Override
    public List<CpCOrgChannelItemEntity> querySyncStockStrategy(Long cpCShopId) {
        return service.querySyncStockStrategy(cpCShopId);
    }

    @Override
    public List<CpCOrgChannelResult> querySyncStockStrategyStore(Long cpCShopId) {
        return service.querySyncStockStrategyStore(cpCShopId);
    }

    @Override
    public List<Long> queryShopStoreNextList(Long cpCShopId) {
        return service.queryShopStoreNextList(cpCShopId);
    }

    @Override
    public List<SyncStockStrategyQueryResult> queryAllSyncStockStrategy() {
        return service.queryAllSyncStockStrategy();
    }

}
