package com.jackrain.nea.st.services;

import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.st.api.StCPreOccupyWarehousePrioritySaveCmd;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @ClassName StCPreOccupyWarehousePrioritySaveCmdImpl
 * @Description 预寻源-仓优先-保存
 * <AUTHOR>
 * @Date 2025/2/27 09:11
 * @Version 1.0
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "st")
public class StCPreOccupyWarehousePrioritySaveCmdImpl extends CommandAdapter implements StCPreOccupyWarehousePrioritySaveCmd {

    @Autowired
    private StCPreOccupyWarehousePriorityService stCPreOccupyWarehousePriorityService;

    @Override
    public ValueHolder execute(QuerySession session) throws NDSException {
        return stCPreOccupyWarehousePriorityService.saveOrUpdate(session);
    }
}
