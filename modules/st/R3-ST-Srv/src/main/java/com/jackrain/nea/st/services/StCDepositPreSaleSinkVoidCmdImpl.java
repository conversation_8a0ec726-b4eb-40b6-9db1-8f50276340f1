package com.jackrain.nea.st.services;

import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.st.api.StCDepositPreSaleSinkVoidCmd;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @create 2021/9/24 10:24
 * 预下沉作废
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "st")
public class StCDepositPreSaleSinkVoidCmdImpl extends CommandAdapter implements StCDepositPreSaleSinkVoidCmd {

    @Autowired
    private StCDepositPreSaleSinkVoidService stCDepositPreSaleSinkVoidService;

    @Override
    public ValueHolder execute(QuerySession querySession) throws NDSException {
        return stCDepositPreSaleSinkVoidService.execute(querySession);
    }
}
