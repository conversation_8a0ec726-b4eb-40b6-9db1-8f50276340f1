package com.jackrain.nea.st.services;

import com.jackrain.nea.st.api.StCExpressPriceStrategyQueryCmd;
import com.jackrain.nea.st.model.request.StCExpressPriceStrategyQueryRequest;
import com.jackrain.nea.st.model.request.StCExpressPriceStrategyRelationQueryRequest;
import com.jackrain.nea.st.model.result.StCExpressPriceStrategyQueryResult;
import com.jackrain.nea.st.model.result.StCExpressPriceStrategyRelation;
import com.jackrain.nea.sys.CommandAdapter;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/6/9 16:55
 * @Description 快递报价设置保存
 * @Version 1.0
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "st")
public class StCExpressPriceStrategyQueryCmdImpl extends CommandAdapter implements StCExpressPriceStrategyQueryCmd {

    @Resource
    private StCExpressPriceStrategyQueryService service;

    @Override
    public List<StCExpressPriceStrategyQueryResult> queryStCExpressPriceByParams(StCExpressPriceStrategyQueryRequest request) {
        return service.queryStCExpressPriceByParams(request);
    }

    @Override
    public List<StCExpressPriceStrategyRelation> queryExpressPriceRelation(StCExpressPriceStrategyRelationQueryRequest request) {
        return service.queryExpressPriceRelation(request);
    }

    @Override
    public StCExpressPriceStrategyRelation queryExpressPriceRelationById(Long id) {
        return service.queryExpressPriceRelationById(id);
    }
}
