package com.jackrain.nea.st.services;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.st.api.RouterWarehouseQueryServiceCmd;
import com.jackrain.nea.st.mapper.StCRouterWarehouseMapper;
import com.jackrain.nea.st.model.table.StCRouterWarehouseDO;
import com.jackrain.nea.utility.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @Descroption 路由实体仓策略查询
 * <AUTHOR>
 * @Date 2020/5/7 15:45
 */
@Slf4j
@Component
//@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "st")
public class RouterWarehouseQueryServiceCmdImpl implements RouterWarehouseQueryServiceCmd {

    @Autowired
    private StCRouterWarehouseMapper stCRouterWarehouseMapper;

    @Override
    public List<StCRouterWarehouseDO> selectByCpCPhyWarehouseId(Long cpCPhyWarehouseId) {
        log.debug(LogUtil.format("RouterWarehouseQueryServiceCmd.selectByCpCPhyWarehouseId入参：cpCPhyWarehouseId=",
                cpCPhyWarehouseId));
        List<StCRouterWarehouseDO> itemList = stCRouterWarehouseMapper.selectByCpCPhyWarehouseId(cpCPhyWarehouseId);
        log.debug(LogUtil.format("RouterWarehouseQueryServiceCmd.selectByCpCPhyWarehouseId返回：")
                + JSONObject.toJSONString(itemList));
        return itemList;
    }

    @Override
    public List<StCRouterWarehouseDO> queryAllStCRouterWarehouse() {
        log.debug(LogUtil.format("RouterWarehouseQueryServiceCmd.queryAllStCRouterWarehouse入参："));
        List<StCRouterWarehouseDO> itemList = stCRouterWarehouseMapper.queryAllStCRouterWarehouse();
        log.debug(LogUtil.format("RouterWarehouseQueryServiceCmd.queryAllStCRouterWarehouse返回：")
                + JSONObject.toJSONString(itemList));
        return itemList;
    }


}
