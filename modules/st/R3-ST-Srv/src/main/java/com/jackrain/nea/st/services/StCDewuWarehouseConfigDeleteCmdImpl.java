package com.jackrain.nea.st.services;

import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.st.api.StCDewuWarehouseConfigDeleteCmd;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 得物仓库配置表删除接口实现
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "st")
public class StCDewuWarehouseConfigDeleteCmdImpl extends CommandAdapter implements StCDewuWarehouseConfigDeleteCmd {

    @Autowired
    private StCDewuWarehouseConfigDeleteService stCDewuWarehouseConfigDeleteService;

    @Override
    public ValueHolder execute(QuerySession querySession) throws NDSException {
        return stCDewuWarehouseConfigDeleteService.execute(querySession);
    }
}
