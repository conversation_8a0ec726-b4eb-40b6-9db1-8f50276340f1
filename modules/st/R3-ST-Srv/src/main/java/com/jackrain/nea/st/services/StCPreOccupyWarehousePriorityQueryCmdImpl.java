package com.jackrain.nea.st.services;

import com.jackrain.nea.st.api.StCPreOccupyWarehousePriorityQueryCmd;
import com.jackrain.nea.st.model.table.StCPreOccupyWarehousePriority;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @ClassName StCPreOccupyWarehousePriorityQueryCmdImpl
 * @Description 仓优先
 * <AUTHOR>
 * @Date 2025/3/4 09:25
 * @Version 1.0
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "st")
public class StCPreOccupyWarehousePriorityQueryCmdImpl extends CommandAdapter implements StCPreOccupyWarehousePriorityQueryCmd {

    @Autowired
    private StCPreOccupyWarehousePriorityService warehousePriorityService;

    @Override
    public ValueHolderV14<StCPreOccupyWarehousePriority> queryByWarehouseEcode(String warehouseEcode) {
        return warehousePriorityService.queryByWarehouseEcode(warehouseEcode);
    }
}
