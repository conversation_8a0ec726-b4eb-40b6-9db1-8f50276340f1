package com.jackrain.nea.st.services;

import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.st.api.StCVipUrgentStrategyVoidCmd;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR> zhuxing
 * @Date : 2021-10-24 16:08
 * @Description : 唯品会补货加急策略 - 作废服务
 **/
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "st")
public class StCVipUrgentStrategyVoidCmdImpl extends CommandAdapter implements StCVipUrgentStrategyVoidCmd {

    @Autowired
    private StCVipUrgentStrategyVoidService stCVipUrgentStrategyVoidService;

    @Override
    public ValueHolder execute(QuerySession session) throws NDSException {
        return stCVipUrgentStrategyVoidService.execute(session);
    }
}
