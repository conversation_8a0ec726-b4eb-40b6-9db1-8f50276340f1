package com.jackrain.nea.st.services;

import com.jackrain.nea.st.api.StCProLogisticStrategyQueryCmd;
import com.jackrain.nea.st.model.request.StCProLogisticStrategyQueryRequest;
import com.jackrain.nea.st.model.table.StCProLogisticStrategy;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "st")
public class StCProLogisticStrategyQueryCmdImpl extends CommandAdapter implements StCProLogisticStrategyQueryCmd {
    @Autowired
    private StCProLogisticStrategyQueryService service;

    @Override
    public ValueHolderV14<List<StCProLogisticStrategy>> queryStCProLogisticStrategy(StCProLogisticStrategyQueryRequest request) {
        return service.queryStCProLogisticStrategy(request);
    }

}
