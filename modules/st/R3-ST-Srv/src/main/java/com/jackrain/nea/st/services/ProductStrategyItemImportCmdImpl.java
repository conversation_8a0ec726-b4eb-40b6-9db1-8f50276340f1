//package com.jackrain.nea.st.services;
//
//import com.jackrain.nea.st.api.ProductStrategyItemImportCmd;
//import com.jackrain.nea.st.model.table.StCProductStrategyItemDO;
//import com.jackrain.nea.sys.CommandAdapter;
//import com.jackrain.nea.sys.domain.ValueHolderV14;
//import com.jackrain.nea.util.ValueHolder;
//import com.jackrain.nea.web.face.User;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.dubbo.config.annotation.Service;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.stereotype.Component;
//
//import java.util.List;
//
///**
// * @author:huang.zizai
// * @since: 2019/9/25
// * @create at : 2019/9/25 14:12
// */
//@Slf4j
//@Component
//@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "st")
//public class ProductStrategyItemImportCmdImpl extends CommandAdapter implements ProductStrategyItemImportCmd {
//    @Autowired
//    private ProductStrategyItemImportService importService;
//
//    @Override
//    public ValueHolderV14 downloadTemp() {
//        return importService.downloadTemp();
//    }
//
//    @Override
//    public ValueHolder importProductStrategyItem(Long objid, List<StCProductStrategyItemDO> productStrategyItemList, User user,Integer channelType) {
//        return importService.importProductStrategyItem(objid, productStrategyItemList, user,channelType);
//    }
//}
