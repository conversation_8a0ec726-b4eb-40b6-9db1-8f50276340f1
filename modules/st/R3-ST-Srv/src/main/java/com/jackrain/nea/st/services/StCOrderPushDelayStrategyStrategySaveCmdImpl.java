package com.jackrain.nea.st.services;

import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.st.api.StCOrderPushDelayStrategySaveCmd;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @Description 订单推单延迟策略保存
 * @Date 16:36 2020/7/1
 **/
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "st")
public class StCOrderPushDelayStrategyStrategySaveCmdImpl extends CommandAdapter implements StCOrderPushDelayStrategySaveCmd {

    @Autowired
    private StCOrderPushDelayStrategySaveService orderPushDelayStrategySaveService;

    @Override
    public ValueHolder execute(QuerySession querySession) throws NDSException {
        return orderPushDelayStrategySaveService.execute(querySession);
    }
}
