<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.burgeon.r3</groupId>
        <artifactId>r3-st</artifactId>
        <version>3.0.0-SNAPSHOT</version>
        <relativePath>../../../pom.xml</relativePath>
    </parent>

    <modelVersion>4.0.0</modelVersion>
    <artifactId>r3-st-srv</artifactId>
    <version>3.0.0-SNAPSHOT</version>
    <packaging>jar</packaging>

    <dependencies>
        <dependency>
            <groupId>com.burgeon.r3</groupId>
            <artifactId>r3-st-bll</artifactId>
        </dependency>
        <dependency>
            <groupId>com.burgeon.r3</groupId>
            <artifactId>r3-service-impl-dubbo</artifactId>
        </dependency>
	    <dependency>
            <groupId>com.burgeon.r3</groupId>
            <artifactId>r3-system-shutdown</artifactId>
            <version>3.2.0-SNAPSHOT</version>
        </dependency>
	    <dependency>
		    <groupId>org.mybatis</groupId>
		    <artifactId>mybatis-typehandlers-jsr310</artifactId>
		    <version>1.0.1</version>
	    </dependency>
	    <dependency>
		    <groupId>com.fasterxml.jackson.datatype</groupId>
		    <artifactId>jackson-datatype-jsr310</artifactId>
		    <version>2.9.2</version>
	    </dependency>
    </dependencies>

</project>